/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2024-07-16
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2024-07-16
 * @FilePath: internal/modules/user/domain/contract/PermissionRepository.go
 * @Description: Defines the repository contract for the Permission entity.
 *
 * © 2024 GACMS. All rights reserved.
 */
package contract

import (
	"gacms/internal/modules/user/domain/model"
	"gacms/pkg/database"
	"github.com/gin-gonic/gin"
)

// PermissionRepository defines the persistence operations for the Permission model.
type PermissionRepository interface {
	Create(ctx *gin.Context, permission *model.Permission) error
	GetByID(ctx *gin.Context, id uint) (*model.Permission, error)
	GetBySlug(ctx *gin.Context, slug string) (*model.Permission, error)
	List(ctx *gin.Context, options *database.ListOptions) ([]*model.Permission, int64, error)
	Update(ctx *gin.Context, permission *model.Permission) error
	Delete(ctx *gin.Context, id uint) error
	GetByIDs(ctx *gin.Context, ids []uint) ([]*model.Permission, error)
} 