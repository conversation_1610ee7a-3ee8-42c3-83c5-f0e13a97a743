/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: Clion Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: pkg/contract/EventHandlerRegistry.go
 * @Description: 事件处理器注册表接口定义
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

// EventHandlerRegistry 定义了事件处理器注册表的接口
// 它负责管理事件处理器的注册和查询
type EventHandlerRegistry interface {
	// RegisterHandler 注册一个事件处理器
	RegisterHandler(handler EventHandler) error

	// UnregisterHandler 取消注册一个事件处理器
	UnregisterHandler(handler EventHandler) error

	// GetHandlersForEvent 获取指定事件的所有处理器
	GetHandlersForEvent(eventName EventName) []EventHandler

	// HasHandlersForEvent 检查是否有处理器注册了指定事件
	HasHandlersForEvent(eventName EventName) bool

	// GetAllHandlers 获取所有注册的处理器
	GetAllHandlers() []EventHandler
} 