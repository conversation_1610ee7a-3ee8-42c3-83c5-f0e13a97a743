/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/modules/user/di/UserModule.go
 * @Description: 用户模块的依赖注入
 * 
 * © 2025 GACMS. All rights reserved.
 */

package di

import (
	"gacms/internal/modules/user/application/listener"
	"gacms/internal/modules/user/application/service"
	"gacms/internal/modules/user/domain/contract"
	"gacms/internal/modules/user/infrastructure/persistence"
	"gacms/pkg/contract"

	"go.uber.org/fx"
)

// UserModule 提供用户模块的所有组件
var UserModule = fx.Options(
	// 提供用户仓储
	fx.Provide(
		persistence.NewUserRepository,
		fx.Annotate(
			persistence.NewUserRepository,
			fx.As(new(contract.UserRepository)),
		),
	),

	// 提供用户服务
	fx.Provide(
		service.NewUserService,
		fx.Annotate(
			service.NewUserService,
			fx.As(new(contract.UserService)),
		),
	),

	// 提供事件处理器
	fx.Provide(
		listener.NewUserEventHandlers,
	),

	// 注册事件处理器
	fx.Invoke(listener.RegisterHandlers),
) 