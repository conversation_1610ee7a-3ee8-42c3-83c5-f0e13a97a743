<!--
Author: Cion Nieh
EMAIL: <EMAIL>
Copyright (c) 2025 Cion Nieh
-->
# 用户故事地图 (User Story Map) - GACMS

## 目录

- [1. 用户故事地图概述](#1-用户故事地图概述)
- [2. 用户画像与核心目标](#2-用户画像与核心目标)
- [3. 用户故事地图结构](#3-用户故事地图结构)
  - [3.1 用户活动流 (骨干 Backbone)](#31-用户活动流-骨干-backbone)
  - [3.2 用户任务分解 (行走骨架 Walking Skeleton)](#32-用户任务分解-行走骨架-walking-skeleton)
  - [3.3 故事详情与版本映射](#33-故事详情与版本映射)
- [4. 用户故事地图详解](#4-用户故事地图详解)
  - [4.1 用户画像1: 王经理 (企业网站管理员/运营人员)](#41-用户画像1-王经理-企业网站管理员运营人员)
  - [4.2 用户画像2: 李明 (Web开发者/技术爱好者)](#42-用户画像2-李明-web开发者技术爱好者)
  - [4.3 系统管理与配置 (多站点支持)](#43-系统管理与配置-多站点支持)
  - [4.4 用户活动: 增强安全性与个性化配置](#44-用户活动-增强安全性与个性化配置)
- [5. 版本发布规划 (关联Roadmap)](#5-版本发布规划-关联roadmap)




## 1. 用户故事地图概述

本用户故事地图旨在通过可视化的方式，从用户视角出发，梳理GACMS的核心用户活动、任务和具体的用户故事。它有助于团队更好地理解用户需求，规划产品功能，并确保开发过程始终以用户价值为中心。用户故事地图将与产品路线图紧密关联，指导各版本的迭代开发。




## 2. 用户画像与核心目标

*(引用PRD中的用户画像)*

- **用户画像1: 王经理 (企业网站管理员/运营人员)**
    - **核心目标**: 高效管理企业网站内容，提升在线品牌形象，获取潜在客户，通过数据分析优化运营策略。
- **用户画像2: 李明 (Web开发者/技术爱好者)**
    - **核心目标**: 快速搭建和定制化网站，利用现代框架提升开发效率和应用性能，为客户提供解决方案或打造个人项目。




## 3. 用户故事地图结构

用户故事地图通常采用二维结构：

- **横向 (用户活动流 - Backbone)**: 代表用户为达成其目标所进行的一系列高层次活动，构成了产品的骨干。
- **纵向 (用户任务分解 - Walking Skeleton)**: 在每个用户活动下，分解出用户为完成该活动所执行的具体任务和步骤，这些任务构成了产品的行走骨架。
- **更深层次 (故事详情与版本映射)**: 
    - 每个任务可以进一步细化为一个或多个用户故事，描述具体的功能点和验收标准。
    - **体现版本功能差异的方式：** 在每个用户故事的详细描述中，除了包含标准的"作为用户，我想要做什么，以便于达到什么目的"之外，还需要明确该故事与不同产品版本（个人版、专业版、商业版）的关联关系。这有助于清晰地规划各版本的功能范围，并指导开发和测试。
        1.  **版本标签/字段**: 为每个用户故事添加一个明确的字段或标签，标示其所属的最低版本或适用的所有版本。例如：
            *   `版本: 个人版+` (表示个人版及以上版本均包含此功能)
            *   `版本: 专业版+` (表示专业版及以上版本包含此功能)
            *   `版本: 商业版` (表示仅商业版包含此功能)
            *   `版本: 个人版(基础), 专业版(增强), 商业版(完整)` (表示同一功能在不同版本有不同程度的实现)
        2.  **独立的用户故事**: 对于仅存在于特定高级版本的功能，可以创建独立的用户故事，并明确其版本归属。
        3.  **验收标准差异化**: 对于同一核心功能，不同版本的验收标准可能不同。例如，个人版可能只要求基本的内容发布，而商业版可能要求包含内容审批流程。这些差异应在用户故事的验收标准中明确体现。
        4.  **功能矩阵引用**: 在用户故事地图的概述或相关章节，可以引用 <mcfile name="PRD.md" path="docs/PRD.md"></mcfile> 或 <mcfile name="SADD.md" path="docs/SADD.md"></mcfile> 中的版本功能矩阵，作为详细的功能差异参考。
- **版本切片 (Release Planning)**: 
    - 在地图上根据优先级、依赖关系以及各版本的功能定位，划分出不同产品版本（如个人版MVP、专业版V1.0、商业版V1.0）要实现的故事集合。
    - 通过上述版本映射方式，确保每个版本切片包含的用户故事与其版本定义的功能范围一致。




## 4. 用户故事地图详解

### 4.1 用户画像1: 王经理 (企业网站管理员/运营人员)

| **用户活动 (Activity)** | **用户任务 (Task)**                                  | **用户故事 (Story) - 个人版 (MVP)**                                                                                           | **用户故事 (Story) - 专业版 (V1.1)**                                                                                                                                                                                          | **用户故事 (Story) - 商业版 (V1.2 & V2.0)**                                                                                                                                                                                                                                                      |
| :---------------------- | :--------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------ | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **1. 网站初始化与配置** | 1.1 首次登录与引导                                   | 作为管理员，我希望能首次登录系统并看到一个简洁的引导，以便快速了解后台基本布局。 (个人版)                                           |                                                                                                                                                                                                                         |                                                                                                                                                                                                                                                                                              |
|                         | 1.2 配置站点基本信息                                 | 作为管理员，我希望能设置网站名称、Logo、版权信息，以便定制网站的基本外观和信息。 (个人版)                                               | 作为管理员，我希望能配置网站的默认语言和时区，以便适应不同地区用户。 (专业版)                                                                                                                                         | 作为管理员，我希望能配置网站的备案号等合规信息。 (商业版)                                                                                                                                                                                                                                         |
|                         | 1.3 配置后台访问安全                                 |                                                                                                                           | 作为管理员，我希望能绑定特定域名才能访问后台，以便增强安全性。 (专业版)                                                                                                                                                 | 作为管理员，我希望能设置IP白名单限制后台访问。 (商业版)<br>作为管理员，我希望能开启后台登录验证码。 (商业版)                                                                                                                                                                                             |
| **2. 内容架构管理**     | 2.1 管理内容栏目                                     | 作为管理员，我希望能创建、编辑、删除内容栏目，并能设置栏目的层级关系，以便组织网站内容结构。 (个人版)                                   | 作为管理员，我希望能调整栏目的显示顺序。 (专业版)                                                                                                                                                                       | 作为管理员，我希望能为栏目绑定独立的二级域名。 (商业版)<br>作为管理员，我希望能为栏目设置独立的模板。 (商业版)                                                                                                                                                                                           |
|                         | 2.2 管理内容标签 (专业版开始)                          |                                                                                                                           | 作为管理员，我希望能创建、编辑、删除内容标签，以便更灵活地对内容进行分类和关联。 (专业版)                                                                                                                                   |                                                                                                                                                                                                                                                                                              |
|                         | 2.3 管理专题页面 (专业版开始)                          |                                                                                                                           | 作为管理员，我希望能创建专题页面，聚合不同栏目的特定内容，并自定义专题页的布局元素（如Banner），以便进行营销推广。 (专业版)                                                                                               | 作为内容运营，我希望专题能支持定时发布和定时下线，以便配合特定的营销活动周期。 (商业版)<br>作为网站管理员，我希望能查看专题页面的独立访问数据，以便评估专题的引流效果。 (商业版)                                                                                                         |
| **3. 内容创作与发布**   | 3.1 创建和编辑文章                                   | 作为内容编辑，我希望能使用富文本编辑器创建和编辑文章，包含标题、正文、摘要，并能上传和插入图片，以便发布图文并茂的内容。 (个人版)             | 作为内容编辑，我希望能使用Markdown编辑器创建和编辑文章。 (专业版)<br>作为内容编辑，我希望能为文章设置标签。 (专业版)<br>作为内容编辑，我希望能将文章保存为草稿，稍后再编辑。 (专业版)                                                              | 作为内容编辑，我希望能对文章内容进行版本控制和回溯。 (商业版)<br>作为内容编辑，我希望能预览文章在不同设备上的显示效果。 (商业版)                                                                                                                                                                             |
|                         | 3.2 设置文章属性                                     | 作为内容编辑，我希望能为文章选择所属栏目，并设置发布状态（已发布/草稿），以便控制内容的可见性。 (个人版)                                  | 作为内容编辑，我希望能为文章设置SEO相关的标题、关键词和描述。 (专业版)<br>作为内容编辑，我希望能设置文章的发布时间和作者。 (专业版)                                                                                             | 作为内容编辑，我希望能设置文章的定时发布。 (商业版)<br>作为内容编辑，我希望能控制文章是否允许评论。 (商业版)                                                                                                                                                                                           |
|                         | 3.3 内容发布与管理                                   | 作为内容编辑，我希望能将编辑好的文章发布到前台，并能在后台管理已发布的文章列表（查看、编辑、删除）。 (个人版)                             | 作为内容编辑，我希望能手动触发已发布内容的静态化生成（中英文）。 (专业版)                                                                                                                                               | 作为内容编辑，我希望能看到文章的浏览次数等基本统计。 (商业版)<br>作为内容编辑，我希望能将内容提交给上级审核（内容工作流）。(商业版)                                                                                                                                                                         |
| **4. 用户互动管理**     | 4.1 管理用户评论 (专业版开始)                          |                                                                                                                           | 作为管理员，我希望能查看、审核、回复和删除用户对文章的评论，以便管理网站互动。 (专业版)                                                                                                                                     | 作为管理员，我希望能设置评论的默认审核状态（自动通过/需审核）。(商业版)                                                                                                                                                                                                                           |
| **5. 网站外观与主题**   | 5.1 选择和应用主题                                   | 作为管理员，我希望能看到系统内置的默认前后台主题，并能直接应用。 (个人版)                                                               | 作为管理员，我希望能切换不同的前台主题。 (专业版)                                                                                                                                                                       | 作为管理员，我希望能在线安装和更新主题。 (商业版)<br>作为管理员，我希望能对当前主题进行一些可视化配置（如颜色、字体）。(商业版)                                                                                                                                                                             |
|                         | 5.2 管理模板组件 (专业版开始)                          |                                                                                                                           | 作为管理员，我希望能管理一些可复用的模板组件（如页头、页脚、侧边栏），以便在不同页面中调用。 (专业版)                                                                                                                           |                                                                                                                                                                                                                                                                                              |
| **6. 系统维护与优化**   | 6.1 查看系统信息                                     | 作为管理员，我希望能查看到当前系统的版本号等基本信息。 (个人版)                                                                     |                                                                                                                                                                                                                         | 作为管理员，我希望能查看系统操作日志。 (商业版)                                                                                                                                                                                                                                             |
|                         | 6.2 配置缓存与静态化 (V1.1开始)                      |                                                                                                                           | 作为管理员，我希望能配置是否启用内容静态化，并能手动清理全站静态文件和缓存。 (V1.1)                                                                                                                                         | 作为管理员，我希望能配置Redis作为缓存驱动。 (V2.0)                                                                                                                                                                                                                                           |
|                         | 6.3 邮件服务配置 (V1.1开始)                          |                                                                                                                           | 作为管理员，我希望能配置SMTP服务器信息，以便系统能发送邮件（如密码找回）。 (V1.1)                                                                                                                                           |                                                                                                                                                                                                                                                                                              |
|                         | 6.4 SEO优化 (V1.2开始)                               |                                                                                                                           |                                                                                                                                                                                                                         | 作为管理员，我希望能自动生成Sitemap.xml文件。 (V1.2)<br>作为管理员，我希望能为网站配置结构化数据标记（如站点链接）。(V1.2)                                                                                                                                                                                 |
| **7. 多语言内容管理**   | 7.1 管理后台语言 (V1.1开始)                          |                                                                                                                           | 作为管理员，我希望能切换后台管理界面的显示语言（中/英）。 (V1.1)                                                                                                                                                           |                                                                                                                                                                                                                                                                                              |
|                         | 7.2 管理前台内容语言 (V1.1开始)                      |                                                                                                                           | 作为内容编辑，我希望能为同一篇文章或栏目创建不同语言版本的内容（如中文和英文），并且前台用户可以通过切换按钮查看不同语言的页面。 (V1.1)                                                                                             | 作为管理员，我希望能管理语言包，方便添加新的翻译或修改现有翻译。 (V1.2)                                                                                                                                                                                                                           |
| **8. 数据分析 (V2.0)**  | 8.1 查看网站访问数据                                 |                                                                                                                           |                                                                                                                                                                                                                         | 作为运营人员，我希望能查看网站的访问量、独立访客、热门页面等数据报表，以便了解网站运营状况。 (V2.0)                                                                                                                                                                                                 |
| **9. 网站视觉与推广 (新增)** | 9.1 管理焦点图 (V1.1开始)                          |                                                                                                                           | 作为运营人员，我希望能管理网站首页的焦点图（上传图片、设置标题和链接），以便推广重要活动或内容。 (V1.1)                                                                                                                  | 作为运营人员，我希望能对焦点图进行排序和启用/禁用，以便灵活控制其展示。 (V1.2)                                                                                                                                                             |

### 4.2 用户画像2: 李明 (Web开发者/技术爱好者)

| **用户活动 (Activity)** | **用户任务 (Task)**                                  | **用户故事 (Story) - 个人版 (MVP)**                                                                                           | **用户故事 (Story) - 专业版 (V1.1)**                                                                                                                                                                                          | **用户故事 (Story) - 商业版 (V1.2 & V2.0)**                                                                                                                                                                                                                                                      |
| :---------------------- | :--------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------ | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **1. 系统安装与部署**   | 1.1 下载和安装GACMS                                  | 作为开发者，我希望能通过Composer快速安装GACMS核心系统，或者下载完整的代码包进行手动部署。 (个人版)                                        |                                                                                                                                                                                                                         |                                                                                                                                                                                                                                                                                              |
|                         | 1.2 环境配置与检查                                   | 作为开发者，我希望能有清晰的文档说明系统运行所需的环境（Go版本、依赖库等），并能通过简单的命令检查环境是否满足。 (MVP)                        |                                                                                                                                                                                                                         |                                                                                                                                                                                                                                                                                              |
| **2. 主题开发与定制**   | 2.1 理解主题结构                                     | 作为开发者，我希望能有清晰的文档说明GACMS主题的文件结构和基本开发规范。 (MVP)                                                               | 作为开发者，我希望能了解组件化模板的使用方法，以便复用和定制主题组件。 (V1.1)                                                                                                                                             | 作为开发者，我希望能方便地创建新的主题包，并在后台进行上传和激活。 (V1.2)                                                                                                                                                                                                                           |
|                         | 2.2 开发自定义主题                                   | 作为开发者，我希望能基于系统提供的主题框架或示例，快速开发出符合自己需求的自定义主题。 (个人版)                                               |                                                                                                                                                                                                                         | 作为开发者，我希望能方便地在主题中使用系统提供的标签库和函数。 (商业版)                                                                                                                                                                                                                               |
| **3. 插件与扩展开发** | 3.1 理解插件机制 (专业版开始)                          |                                                                                                                           | 作为开发者，我希望能了解GACMS的插件机制、钩子系统和事件系统，以便开发自定义插件。 (专业版)                                                                                                                                   | 作为开发者，我希望能方便地创建、安装、卸载和管理插件。 (商业版)                                                                                                                                                                                                                                     |
|                         | 3.2 开发简单功能插件 (专业版开始)                        |                                                                                                                           | 作为开发者，我希望能开发一些简单的功能插件（如统计代码插件、分享按钮插件），并将其集成到系统中。 (专业版)                                                                                                                             | 作为开发者，我希望能开发更复杂的插件，例如与其他第三方服务集成的插件。 (商业版)                                                                                                                                                                                                                         |
| **4. API接口使用与开发**| 4.1 调用内容获取API (专业版开始)                       |                                                                                                                           | 作为开发者，我希望能调用系统提供的基础内容API（如获取栏目文章列表、文章详情），以便在其他应用（如App、小程序）中展示内容。 (专业版)                                                                                               | 作为开发者，我希望能有完善的API文档，了解API的请求参数、返回格式和认证方式。 (商业版)<br>作为开发者，我希望能方便地扩展或创建新的API接口以满足特定需求。 (商业版)                                                                                                                                                         |
| **5. 系统二次开发**     | 5.1 理解核心代码与架构                               | 作为开发者，我希望能通过阅读文档和代码，理解GACMS的核心架构、模块划分和主要类的职责。 (个人版)                                                |                                                                                                                                                                                                                         |                                                                                                                                                                                                                                                                                              |
|                         | 5.2 扩展或修改核心功能                               | 作为开发者，我希望能通过覆写核心类或利用事件机制，在不直接修改核心代码的情况下扩展或调整系统功能。 (个人版)                                     |                                                                                                                                                                                                                         | 作为开发者，我希望能方便地添加新的数据模型、控制器和服务，并与现有系统集成。 (商业版)                                                                                                                                                                                                                       |
| **6. 性能与安全调优**   | 6.1 了解性能优化点                                   |                                                                                                                           | 作为开发者，我希望能了解GACMS在缓存使用、数据库查询优化、静态化等方面的实践，以便进行针对性的性能调优。 (专业版)                                                                                                                           | 作为开发者，我希望能方便地集成更高级的缓存方案（如Redis）。 (商业版)                                                                                                                                                                                                                                     |
|                         | 6.2 参与安全加固                                     |                                                                                                                           | 作为开发者，我希望能了解GACMS的安全机制和常见的安全风险点，以便在二次开发中遵循安全规范。 (专业版)                                                                                                                                 |                                                                                                                                                                                                                                                                                              |
| **7. 学习与生态参与 (新增)** | 7.1 查阅开发文档 (V1.1开始)                        |                                                                                                                           | 作为开发者，我希望能有一个集中的开发者文档中心，方便我快速查找API参考、主题和插件开发指南，以便降低学习成本，提升开发效率。 (V1.1)                                                                                              |                                                                                                                                                                                                                                                                                              |

### 4.3 系统管理与配置 (多站点支持)

| 用户任务 (Task)             | 用户故事 (User Story)                                                                                                | 优先级 | 个人版 (MVP) | 专业版 (V1.1) | 商业版 (V1.2) | 备注                                                                 |
| --------------------------- | -------------------------------------------------------------------------------------------------------------------- | ------ | :----------: | :-----------: | :-----------: | -------------------------------------------------------------------- |
| **多站点管理**              | 作为网站管理员，我想要创建和管理多个独立的站点实例，以便统一管理不同业务或品牌的网站。                                       | P1     |              |       ✅       |       ✅       | 专业版核心功能                                                       |
|                             | 作为网站管理员，我想要为每个创建的站点独立配置其基础信息（如站点名称、Logo、描述）。                                         | P1     |              |       ✅       |       ✅       |                                                                      |
|                             | 作为网站管理员，我想要能够快速切换不同站点的后台管理界面。                                                                 | P1     |              |       ✅       |       ✅       |                                                                      |
| **站点独立域名绑定**        | 作为网站管理员，我想要为每个站点独立绑定前台访问域名（支持主域名或子域名），以便用户通过特定域名访问对应站点。                 | P1     |              |       ✅       |       ✅       |                                                                      |
|                             | 作为网站管理员，我想要为每个站点独立绑定后台管理域名（支持主域名或子域名），以便通过特定域名安全访问对应站点的后台。             | P1     |              |       ✅       |       ✅       | 增强安全性                                                           |
|                             | 作为网站管理员，我希望系统能根据访问的域名自动路由到正确的站点进行内容展示和后台管理。                                       | P1     |              |       ✅       |       ✅       |                                                                      |
| **站点独立内容与配置**      | 作为网站管理员，我想要每个站点可以拥有独立的内容（文章、栏目等），以便针对不同站点发布不同信息。                                 | P1     |              |       ✅       |       ✅       |                                                                      |
|                             | 作为网站管理员，我想要每个站点可以独立选择和配置主题模板，以便不同站点拥有独特的外观风格。                                     | P1     |              |       ✅       |       ✅       |                                                                      |
|                             | 作为网站管理员，我想要每个站点可以拥有独立的用户和权限设置（可选，或与主系统用户打通但权限隔离），以便精细化管理各站点访问。 | P2     |              |               |       ✅       | 商业版考虑更复杂的权限隔离需求                                         |
|                             | 作为网站管理员，我想要每个站点可以独立配置其系统设置（如缓存策略、SEO参数等），以便针对不同站点进行优化。                       | P1     |              |       ✅       |       ✅       |                                                                      |



### 4.4 用户活动: 增强安全性与个性化配置

#### 针对用户画像: 王经理 (企业网站管理员/运营人员)

| **用户任务 (Task)**             | **用户故事 (User Story)**                                                                                                                               | **优先级** | **个人版 (MVP)** | **专业版 (V1.1)** | **商业版 (V1.2)** | **备注**                                                                 |
| :------------------------------ | :------------------------------------------------------------------------------------------------------------------------------------------------------ | :--------- | :--------------: | :---------------: | :---------------: | :----------------------------------------------------------------------- |
| **启用与管理2FA双因素认证**     | 作为管理员，我希望能为自己的后台账户启用2FA双因素认证（如TOTP），以便在输入密码后，还需要输入动态验证码，从而增强账户安全性。                                 | P1         |                  |        ✅         |        ✅         | 提升账户安全，防止未授权访问                                               |
|                                 | 作为管理员，我希望能要求特定用户组（如其他管理员、内容编辑）强制启用2FA，以确保敏感操作的安全性。                                                               | P1         |                  |                   |        ✅         | 商业版功能，用于企业级安全策略                                             |
|                                 | 作为管理员，我希望能查看账户的2FA启用状态，并在必要时为用户重置2FA设置（例如用户丢失认证设备）。                                                                   | P2         |                  |        ✅         |        ✅         | 管理员辅助功能                                                           |
| **配置站点级独立主题**          | 作为管理员，我希望能为我管理的每个独立站点分别设置前台主题，以便不同业务或品牌的网站拥有独特且一致的视觉风格。                                                               | P1         |                  |        ✅         |        ✅         | 提升多站点管理的灵活性和品牌形象                                           |
|                                 | 作为管理员，我希望能为我管理的每个独立站点分别设置后台主题，以便在管理不同站点时能通过视觉快速区分，提升管理效率。                                                               | P2         |                  |        ✅         |        ✅         | 辅助管理员区分不同站点后台                                                 |
|                                 | 作为管理员，我希望在切换不同站点后台时，系统能自动应用该站点已配置的后台主题。                                                                                       | P1         |                  |        ✅         |        ✅         |                                                                          |

#### 针对用户画像: 李明 (Web开发者/技术爱好者)

| **用户任务 (Task)**             | **用户故事 (User Story)**                                                                                                                               | **优先级** | **个人版 (MVP)** | **专业版 (V1.1)** | **商业版 (V1.2)** | **备注**                                                                 |
| :------------------------------ | :------------------------------------------------------------------------------------------------------------------------------------------------------ | :--------- | :--------------: | :---------------: | :---------------: | :----------------------------------------------------------------------- |
| **为开发者账户启用2FA**         | 作为开发者，我希望能为我的GACMS开发者账户（如果系统支持）或后台管理员账户启用2FA，以保护我的项目和代码安全。                                                              | P1         |                  |        ✅         |        ✅         | 保护开发者账户安全                                                       |
| **为客户站点配置独立主题**      | 作为开发者，当为客户构建多站点项目时，我希望能方便地为每个客户的子站点配置独立的前后台主题，以满足客户的定制化品牌需求。                                                           | P1         |                  |        ✅         |        ✅         | 满足客户定制化需求，提升项目交付质量                                       |
|                                 | 作为开发者，我希望能通过代码或后台配置的方式，快速复制和应用主题到不同的站点，并进行微调。                                                                               | P2         |                  |                   |        ✅         | 提高主题配置效率                                                         |

 

## 5. 版本发布规划 (关联Roadmap)

*(此部分将用户故事地图中的故事与Roadmap中的版本进行映射，确保每个版本交付明确的用户价值)*

- **MVP 版本**: 
    - 重点实现用户活动"网站初始化与配置"、"内容架构管理 (栏目)"、"内容创作与发布 (基础)"的核心任务。
    - 对应王经理画像的核心需求：快速搭建简单网站并发布内容。
    - 对应李明画像的核心需求：系统能跑起来，可以进行基本的主题修改。
- **V1.1 版本**:
    - 重点实现用户活动"内容架构管理 (标签、专题)"、"内容创作与发布 (增强)"、"用户互动管理 (评论)"、"网站外观与主题 (组件)"、"系统维护与优化 (基础配置)"、"多语言内容管理 (基础)"等。
    - 对应王经理画像的核心需求：更灵活的内容组织，初步的SEO和多语言支持。
    - 对应李明画像的核心需求：更方便的主题开发，了解API基础。
- **V1.2 版本**:
    - 重点实现用户活动"插件开发与扩展 (基础)"、"API接口使用与开发 (文档与扩展)"、"SEO优化 (Sitemap等)"、"内容工作流 (基础)"等。
    - 对应王经理画像的核心需求：规范化内容流程，提升SEO效果。
    - 对应李明画像的核心需求：可以开发插件，API使用更便捷。
- **V2.0 版本**:
    - 重点实现用户活动"数据分析"、"智能化内容功能 (推荐、爬虫)"等。
    - 对应王经理画像的核心需求：数据驱动运营，自动化内容获取。
    - 对应李明画像的核心需求：系统功能更强大，可定制性更高。