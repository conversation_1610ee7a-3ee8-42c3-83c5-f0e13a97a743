/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/di/LicenseModule.go
 * @Description: 许可证模块的fx依赖注入配置，符合fx.Option被动声明原则
 * 
 * © 2025 GACMS. All rights reserved.
 */

package di

import (
	"context"
	"time"

	"gacms/internal/core/service"
	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// LicenseModule 许可证模块的fx配置
// 提供许可证验证、管理、存储等核心功能
var LicenseModule = fx.Options(
	// 许可证存储
	fx.Provide(
		fx.Annotate(
			service.NewDefaultLicenseStore,
			fx.As(new(contract.LicenseStore)),
		),
	),

	// 许可证验证器注册表
	fx.Provide(
		fx.Annotate(
			service.NewDefaultLicenseValidatorRegistry,
			fx.As(new(contract.LicenseValidatorRegistry)),
		),
	),

	// 官方许可证验证器
	fx.Provide(
		fx.Annotate(
			service.NewOfficialLicenseValidator,
			fx.As(new(contract.OfficialLicenseValidator)),
			fx.As(new(contract.LicenseValidator)),
		),
	),

	// 许可证管理器（原有的模块许可证管理器）
	fx.Provide(
		fx.Annotate(
			service.NewDefaultLicenseManager,
			fx.As(new(contract.LicenseManager)),
		),
	),

	// 版本管理器
	fx.Provide(
		fx.Annotate(
			service.NewDefaultEditionManager,
			fx.As(new(service.EditionManager)),
		),
	),

	// 许可证服务层（业务级统一接口）
	fx.Provide(
		fx.Annotate(
			service.NewDefaultLicenseService,
			fx.As(new(service.LicenseService)),
		),
	),

	// 功能守卫
	fx.Provide(
		fx.Annotate(
			service.NewDefaultFeatureGuard,
			fx.As(new(service.FeatureGuard)),
		),
	),

	// 系统许可证配置
	fx.Provide(
		fx.Annotate(
			NewDefaultSystemLicenseConfig,
			fx.As(new(service.SystemLicenseConfig)),
		),
	),

	// 许可证事件处理器
	service.LicenseEventHandlerModule(),

	// 许可证配置
	fx.Provide(
		fx.Annotate(
			NewDefaultLicenseConfig,
			fx.As(new(contract.LicenseConfig)),
		),
	),

	// 启动时初始化许可证系统
	fx.Invoke(InitializeLicenseSystem),
	fx.Invoke(InitializeSystemLicenseSystem),
)

// NewDefaultLicenseConfig 创建默认许可证配置
func NewDefaultLicenseConfig() *contract.LicenseConfig {
	return &contract.LicenseConfig{
		// 官方许可证配置
		OfficialLicenseURL:    "https://license.gacms.com/api/v1",
		OfficialLicenseAPIKey: "", // 从环境变量或配置文件读取

		// 第三方许可证配置
		AllowThirdPartyLicense: true,
		ThirdPartyValidators:   make(map[string]map[string]interface{}),

		// 验证配置
		ValidateOnStartup:    true,
		ValidationInterval:   24 * 60 * 60 * **********, // 24小时（纳秒）
		OfflineGracePeriod:   7 * 24 * 60 * 60 * **********, // 7天（纳秒）

		// 缓存配置
		CacheLicenseInfo: true,
		CacheExpiration:  1 * 60 * 60 * **********, // 1小时（纳秒）
	}
}

// InitializeLicenseSystem 初始化许可证系统
func InitializeLicenseSystem(
	store contract.LicenseStore,
	manager contract.LicenseManager,
	registry contract.LicenseValidatorRegistry,
	eventManager contract.EventManager,
	logger *zap.Logger,
) error {
	logger.Info("Initializing license system")

	// 创建许可证相关数据表
	if licenseStore, ok := store.(*service.DefaultLicenseStore); ok {
		if err := licenseStore.CreateTables(context.Background()); err != nil {
			logger.Error("Failed to create license tables", zap.Error(err))
			return err
		}
	}

	// 注册许可证事件处理器到事件管理器
	// 这里通过fx的group机制自动注册，符合声明式配置原则

	logger.Info("License system initialized successfully")
	return nil
}

// NewDefaultSystemLicenseConfig 创建默认系统许可证配置
func NewDefaultSystemLicenseConfig() *service.SystemLicenseConfig {
	return &service.SystemLicenseConfig{
		// 商业授权开关
		Enabled:         false, // 默认关闭
		DevelopmentMode: true,

		// 许可证文件路径
		SystemLicensePath: "./licenses/system_license.json",
		UsageLicenseDir:   "./licenses/usage/",

		// 验证配置
		ValidationInterval:      24 * time.Hour,
		OfflineMode:             true,
		LenientMode:             true,
		CacheDuration:           1 * time.Hour,

		// 域名验证
		DomainValidationEnabled: false,

		// 公钥文件路径
		PublicKeyPath: "./keys/gacms_public.pem",
	}
}

// InitializeSystemLicenseSystem 初始化系统许可证系统
func InitializeSystemLicenseSystem(
	licenseManager contract.LicenseManager,
	licenseService service.LicenseService,
	editionManager service.EditionManager,
	featureGuard service.FeatureGuard,
	logger *zap.Logger,
) error {
	logger.Info("Initializing system license system")

	// 验证版本一致性
	if err := featureGuard.ValidateEditionConsistency(context.Background()); err != nil {
		logger.Warn("Edition consistency validation failed", zap.Error(err))
		// 不返回错误，因为开发阶段可能会有不一致
	}

	// 获取系统信息
	systemInfo := licenseService.GetSystemInfo()

	// 验证许可证
	licenseInfo, err := licenseManager.ValidateLicense(context.Background())
	if err != nil {
		logger.Error("License validation failed", zap.Error(err))
		// 在开发阶段不返回错误
	} else {
		logger.Info("System license validated successfully",
			zap.String("edition", string(licenseInfo.Edition)),
			zap.Bool("is_valid", licenseInfo.IsValid))
	}

	// 记录系统信息
	if systemInfo != nil {
		logger.Info("System license system enabled",
			zap.String("system_edition", string(systemInfo.Edition)),
			zap.Bool("is_valid", systemInfo.IsValid),
			zap.Int("max_tenants", systemInfo.MaxTenants),
			zap.Int("current_tenants", systemInfo.CurrentTenants))
	} else {
		logger.Info("System license system disabled (development mode)")
	}

	logger.Info("System license system initialized successfully")
	return nil
}

// ThirdPartyLicenseValidatorModule 第三方许可证验证器模块
// 第三方开发者可以使用此模块注册自己的验证器
func ThirdPartyLicenseValidatorModule(validator contract.ThirdPartyLicenseValidator) fx.Option {
	return fx.Options(
		fx.Provide(
			fx.Annotate(
				func() contract.ThirdPartyLicenseValidator {
					return validator
				},
				fx.As(new(contract.ThirdPartyLicenseValidator)),
				fx.As(new(contract.LicenseValidator)),
				fx.ResultTags(`group:"third_party_validators"`),
			),
		),
	)
}

// LicenseEventHandlerModule 许可证事件处理器模块
// 模块可以使用此模块注册自己的许可证事件处理器
func LicenseEventHandlerModule(handler contract.LicenseEventHandler) fx.Option {
	return fx.Options(
		fx.Provide(
			fx.Annotate(
				func() contract.LicenseEventHandler {
					return handler
				},
				fx.As(new(contract.LicenseEventHandler)),
				fx.As(new(contract.EventHandler)),
				fx.ResultTags(`group:"event_handlers"`),
			),
		),
	)
}

// LicenseMiddlewareModule 许可证中间件模块
// 提供路由级别的许可证验证中间件
func LicenseMiddlewareModule() fx.Option {
	return fx.Options(
		fx.Provide(
			fx.Annotate(
				service.NewLicenseMiddleware,
				fx.As(new(contract.Middleware)),
				fx.ResultTags(`group:"middlewares"`),
			),
		),
	)
}

// ModuleLicenseRecipe 模块许可证Recipe
// 为需要许可证的模块提供统一的Recipe创建方法
func ModuleLicenseRecipe(name, version string, requiresLicense bool, options fx.Option) *service.ModuleRecipe {
	var recipe *service.ModuleRecipe
	
	if requiresLicense {
		recipe = service.NewExtensionModuleRecipe(name, version, options)
	} else {
		recipe = service.NewCoreModuleRecipe(name, version, options)
	}
	
	return recipe
}

// ThirdPartyModuleLicenseRecipe 第三方模块许可证Recipe
// 为第三方模块提供许可证支持
func ThirdPartyModuleLicenseRecipe(name, version, validator string, options fx.Option) *service.ModuleRecipe {
	recipe := service.NewThirdPartyModuleRecipe(name, version, options)
	
	if validator != "" {
		recipe.RequiresLicense = true
		recipe.LicenseProvider = "third_party"
		recipe.LicenseValidator = validator
	}
	
	return recipe
}

// PluginLicenseRecipe 插件许可证Recipe
func PluginLicenseRecipe(name, version string, requiresLicense bool, validator string, options fx.Option) *service.ModuleRecipe {
	recipe := service.NewPluginModuleRecipe(name, version, options)
	
	if requiresLicense {
		recipe.RequiresLicense = true
		if validator != "" {
			recipe.LicenseProvider = "third_party"
			recipe.LicenseValidator = validator
		} else {
			recipe.LicenseProvider = "official"
		}
	}
	
	return recipe
}

// ThemeLicenseRecipe 主题许可证Recipe
func ThemeLicenseRecipe(name, version string, requiresLicense bool, validator string, options fx.Option) *service.ModuleRecipe {
	recipe := service.NewThemeModuleRecipe(name, version, options)
	
	if requiresLicense {
		recipe.RequiresLicense = true
		if validator != "" {
			recipe.LicenseProvider = "third_party"
			recipe.LicenseValidator = validator
		} else {
			recipe.LicenseProvider = "official"
		}
	}
	
	return recipe
}
