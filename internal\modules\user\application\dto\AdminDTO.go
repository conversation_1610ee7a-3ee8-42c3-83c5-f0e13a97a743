/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/user/application/dto/AdminDTO.go
 * @Description: DTOs for Admin operations.
 *
 * © 2025 GACMS. All rights reserved.
 */
package dto

// AdminLoginDTO is used for administrator login.
type AdminLoginDTO struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// AdminCreateDTO is used for creating a new administrator.
type AdminCreateDTO struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=8,max=100"`
	RoleIDs  []uint `json:"roleIds" binding:"omitempty"`
}

// AdminUpdateDTO is used for updating an existing administrator.
type AdminUpdateDTO struct {
	Email    *string `json:"email" binding:"omitempty,email"`
	Password *string `json:"password" binding:"omitempty,min=8,max=100"`
	IsActive *bool   `json:"isActive" binding:"omitempty"`
}

// AssignRolesToAdminDTO is used for assigning roles to an administrator.
type AssignRolesToAdminDTO struct {
	RoleIDs []uint `json:"roleIds" binding:"required,gt=0"`
} 