/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/topic/port/http/controller/TopicController.go
 * @Description: HTTP Controller for the Topic module.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package controller

import (
	"gacms/internal/modules/topic/application/service"
	"github.com/gin-gonic/gin"
	"net/http"
)

type TopicController struct {
	topicService *service.TopicService
}

func NewTopicController(topicService *service.TopicService) *TopicController {
	return &TopicController{topicService: topicService}
}

// RegisterRoutes implements the pkgContract.IRoutable interface.
func (c *TopicController) RegisterRoutes(group *gin.RouterGroup) {
	topicRoutes := group.Group("/topics")
	{
		topicRoutes.GET("/:slug", c.GetTopicBySlug)
	}
}

// GetTopicBySlug handles the request to fetch a single topic by its slug.
func (c *TopicController) GetTopicBySlug(ctx *gin.Context) {
	slug := ctx.Param("slug")
	
	topic, err := c.topicService.GetTopicBySlug(ctx, slug)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Topic not found"})
		return
	}

	ctx.JSON(http.StatusOK, topic)
} 