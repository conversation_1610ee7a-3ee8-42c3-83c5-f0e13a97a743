<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 客户字段管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .field-card {
            transition: all 0.3s ease;
        }
        
        .field-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .drag-handle {
            cursor: grab;
        }
        
        .drag-handle:active {
            cursor: grabbing;
        }
        
        .field-type-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
        }
        
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .add-field-btn {
            transition: all 0.3s ease;
        }
        
        .add-field-btn:hover {
            transform: translateY(-2px);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="clients.html" class="hover:text-white">客户管理</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">客户字段</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">客户字段管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="addFieldBtn" class="add-field-btn flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-plus text-white"></i>
                                </span>
                                添加字段
                            </span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 字段分类选项卡 -->
            <div class="mb-6">
                <div class="border-b border-gray-700">
                    <nav class="-mb-px flex space-x-6">
                        <a href="#" class="border-b-2 border-blue-500 text-blue-500 whitespace-nowrap py-3 px-1 font-medium text-sm">
                            基本信息
                        </a>
                        <a href="#" class="border-b-2 border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-400 whitespace-nowrap py-3 px-1 font-medium text-sm">
                            联系方式
                        </a>
                        <a href="#" class="border-b-2 border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-400 whitespace-nowrap py-3 px-1 font-medium text-sm">
                            企业信息
                        </a>
                        <a href="#" class="border-b-2 border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-400 whitespace-nowrap py-3 px-1 font-medium text-sm">
                            自定义字段
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- 字段列表 -->
            <div class="space-y-4">
                <!-- 字段卡片 - 用户名 -->
                <div class="field-card bg-gray-800/10 border border-gray-700 rounded-xl p-4">
                    <div class="flex flex-wrap items-center">
                        <div class="flex items-center w-8 mr-3 text-gray-400">
                            <i class="fas fa-grip-vertical drag-handle"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex flex-wrap items-center gap-3">
                                <h3 class="text-white font-medium">用户名</h3>
                                <span class="field-type-badge bg-blue-500/20 text-blue-400">文本</span>
                                <span class="text-red-400 text-xs">必填</span>
                            </div>
                            <p class="text-gray-400 text-sm mt-1">客户账号的唯一标识符</p>
                        </div>
                        <div class="flex items-center space-x-3 mt-3 sm:mt-0">
                            <button class="text-gray-400 hover:text-white p-2">
                                <i class="fas fa-edit"></i>
                            </button>
                            <div class="h-4 border-r border-gray-700"></div>
                            <label class="inline-flex items-center cursor-pointer">
                                <span class="mr-2 text-gray-400 text-sm">启用</span>
                                <div class="relative">
                                    <input type="checkbox" class="sr-only" checked>
                                    <div class="w-10 h-5 bg-gray-700 rounded-full shadow-inner"></div>
                                    <div class="dot absolute w-4 h-4 bg-blue-500 rounded-full transition left-0.5 top-0.5 transform translate-x-5"></div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- 字段卡片 - 电子邮箱 -->
                <div class="field-card bg-gray-800/10 border border-gray-700 rounded-xl p-4">
                    <div class="flex flex-wrap items-center">
                        <div class="flex items-center w-8 mr-3 text-gray-400">
                            <i class="fas fa-grip-vertical drag-handle"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex flex-wrap items-center gap-3">
                                <h3 class="text-white font-medium">电子邮箱</h3>
                                <span class="field-type-badge bg-green-500/20 text-green-400">邮箱</span>
                                <span class="text-red-400 text-xs">必填</span>
                            </div>
                            <p class="text-gray-400 text-sm mt-1">用于登录和接收通知</p>
                        </div>
                        <div class="flex items-center space-x-3 mt-3 sm:mt-0">
                            <button class="text-gray-400 hover:text-white p-2">
                                <i class="fas fa-edit"></i>
                            </button>
                            <div class="h-4 border-r border-gray-700"></div>
                            <label class="inline-flex items-center cursor-pointer">
                                <span class="mr-2 text-gray-400 text-sm">启用</span>
                                <div class="relative">
                                    <input type="checkbox" class="sr-only" checked>
                                    <div class="w-10 h-5 bg-gray-700 rounded-full shadow-inner"></div>
                                    <div class="dot absolute w-4 h-4 bg-blue-500 rounded-full transition left-0.5 top-0.5 transform translate-x-5"></div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- 字段卡片 - 手机号码 -->
                <div class="field-card bg-gray-800/10 border border-gray-700 rounded-xl p-4">
                    <div class="flex flex-wrap items-center">
                        <div class="flex items-center w-8 mr-3 text-gray-400">
                            <i class="fas fa-grip-vertical drag-handle"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex flex-wrap items-center gap-3">
                                <h3 class="text-white font-medium">手机号码</h3>
                                <span class="field-type-badge bg-purple-500/20 text-purple-400">电话</span>
                                <span class="text-gray-400 text-xs">选填</span>
                            </div>
                            <p class="text-gray-400 text-sm mt-1">用于短信通知和账号验证</p>
                        </div>
                        <div class="flex items-center space-x-3 mt-3 sm:mt-0">
                            <button class="text-gray-400 hover:text-white p-2">
                                <i class="fas fa-edit"></i>
                            </button>
                            <div class="h-4 border-r border-gray-700"></div>
                            <label class="inline-flex items-center cursor-pointer">
                                <span class="mr-2 text-gray-400 text-sm">启用</span>
                                <div class="relative">
                                    <input type="checkbox" class="sr-only" checked>
                                    <div class="w-10 h-5 bg-gray-700 rounded-full shadow-inner"></div>
                                    <div class="dot absolute w-4 h-4 bg-blue-500 rounded-full transition left-0.5 top-0.5 transform translate-x-5"></div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- 字段卡片 - 真实姓名 -->
                <div class="field-card bg-gray-800/10 border border-gray-700 rounded-xl p-4">
                    <div class="flex flex-wrap items-center">
                        <div class="flex items-center w-8 mr-3 text-gray-400">
                            <i class="fas fa-grip-vertical drag-handle"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex flex-wrap items-center gap-3">
                                <h3 class="text-white font-medium">真实姓名</h3>
                                <span class="field-type-badge bg-blue-500/20 text-blue-400">文本</span>
                                <span class="text-gray-400 text-xs">选填</span>
                            </div>
                            <p class="text-gray-400 text-sm mt-1">客户的真实姓名</p>
                        </div>
                        <div class="flex items-center space-x-3 mt-3 sm:mt-0">
                            <button class="text-gray-400 hover:text-white p-2">
                                <i class="fas fa-edit"></i>
                            </button>
                            <div class="h-4 border-r border-gray-700"></div>
                            <label class="inline-flex items-center cursor-pointer">
                                <span class="mr-2 text-gray-400 text-sm">启用</span>
                                <div class="relative">
                                    <input type="checkbox" class="sr-only" checked>
                                    <div class="w-10 h-5 bg-gray-700 rounded-full shadow-inner"></div>
                                    <div class="dot absolute w-4 h-4 bg-blue-500 rounded-full transition left-0.5 top-0.5 transform translate-x-5"></div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- 字段卡片 - 性别 -->
                <div class="field-card bg-gray-800/10 border border-gray-700 rounded-xl p-4">
                    <div class="flex flex-wrap items-center">
                        <div class="flex items-center w-8 mr-3 text-gray-400">
                            <i class="fas fa-grip-vertical drag-handle"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex flex-wrap items-center gap-3">
                                <h3 class="text-white font-medium">性别</h3>
                                <span class="field-type-badge bg-yellow-500/20 text-yellow-400">选择</span>
                                <span class="text-gray-400 text-xs">选填</span>
                            </div>
                            <p class="text-gray-400 text-sm mt-1">客户的性别信息</p>
                        </div>
                        <div class="flex items-center space-x-3 mt-3 sm:mt-0">
                            <button class="text-gray-400 hover:text-white p-2">
                                <i class="fas fa-edit"></i>
                            </button>
                            <div class="h-4 border-r border-gray-700"></div>
                            <label class="inline-flex items-center cursor-pointer">
                                <span class="mr-2 text-gray-400 text-sm">启用</span>
                                <div class="relative">
                                    <input type="checkbox" class="sr-only" checked>
                                    <div class="w-10 h-5 bg-gray-700 rounded-full shadow-inner"></div>
                                    <div class="dot absolute w-4 h-4 bg-blue-500 rounded-full transition left-0.5 top-0.5 transform translate-x-5"></div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- 字段卡片 - 生日 -->
                <div class="field-card bg-gray-800/10 border border-gray-700 rounded-xl p-4">
                    <div class="flex flex-wrap items-center">
                        <div class="flex items-center w-8 mr-3 text-gray-400">
                            <i class="fas fa-grip-vertical drag-handle"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex flex-wrap items-center gap-3">
                                <h3 class="text-white font-medium">生日</h3>
                                <span class="field-type-badge bg-red-500/20 text-red-400">日期</span>
                                <span class="text-gray-400 text-xs">选填</span>
                            </div>
                            <p class="text-gray-400 text-sm mt-1">客户的出生日期</p>
                        </div>
                        <div class="flex items-center space-x-3 mt-3 sm:mt-0">
                            <button class="text-gray-400 hover:text-white p-2">
                                <i class="fas fa-edit"></i>
                            </button>
                            <div class="h-4 border-r border-gray-700"></div>
                            <label class="inline-flex items-center cursor-pointer">
                                <span class="mr-2 text-gray-400 text-sm">启用</span>
                                <div class="relative">
                                    <input type="checkbox" class="sr-only" checked>
                                    <div class="w-10 h-5 bg-gray-700 rounded-full shadow-inner"></div>
                                    <div class="dot absolute w-4 h-4 bg-blue-500 rounded-full transition left-0.5 top-0.5 transform translate-x-5"></div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- 字段卡片 - 头像 -->
                <div class="field-card bg-gray-800/10 border border-gray-700 rounded-xl p-4">
                    <div class="flex flex-wrap items-center">
                        <div class="flex items-center w-8 mr-3 text-gray-400">
                            <i class="fas fa-grip-vertical drag-handle"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex flex-wrap items-center gap-3">
                                <h3 class="text-white font-medium">头像</h3>
                                <span class="field-type-badge bg-pink-500/20 text-pink-400">图片</span>
                                <span class="text-gray-400 text-xs">选填</span>
                            </div>
                            <p class="text-gray-400 text-sm mt-1">客户的个人头像</p>
                        </div>
                        <div class="flex items-center space-x-3 mt-3 sm:mt-0">
                            <button class="text-gray-400 hover:text-white p-2">
                                <i class="fas fa-edit"></i>
                            </button>
                            <div class="h-4 border-r border-gray-700"></div>
                            <label class="inline-flex items-center cursor-pointer">
                                <span class="mr-2 text-gray-400 text-sm">启用</span>
                                <div class="relative">
                                    <input type="checkbox" class="sr-only" checked>
                                    <div class="w-10 h-5 bg-gray-700 rounded-full shadow-inner"></div>
                                    <div class="dot absolute w-4 h-4 bg-blue-500 rounded-full transition left-0.5 top-0.5 transform translate-x-5"></div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 页面底部版权信息 -->
            <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
                © 2025 GACMS 后台管理系统 - 版本 v1.0.0
            </footer>
        </div>
    </main>

    <!-- 添加字段模态框 -->
    <div id="addFieldModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 w-full max-w-lg">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-white">添加客户字段</h3>
                <button id="closeModalBtn" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form>
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">字段名称</label>
                    <input type="text" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="输入字段名称">
                </div>
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">字段类型</label>
                    <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="text">文本</option>
                        <option value="email">邮箱</option>
                        <option value="phone">电话</option>
                        <option value="select">选择</option>
                        <option value="date">日期</option>
                        <option value="image">图片</option>
                        <option value="textarea">多行文本</option>
                        <option value="number">数字</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">字段描述</label>
                    <textarea class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3" placeholder="输入字段描述"></textarea>
                </div>
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">字段分组</label>
                    <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="basic">基本信息</option>
                        <option value="contact">联系方式</option>
                        <option value="company">企业信息</option>
                        <option value="custom">自定义字段</option>
                    </select>
                </div>
                <div class="mb-4 flex items-center">
                    <input type="checkbox" id="requiredField" class="mr-2">
                    <label for="requiredField" class="text-gray-300">必填字段</label>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancelBtn" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg">取消</button>
                    <button type="button" class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 必要的JS脚本 -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 字段开关切换效果
            const toggleSwitches = document.querySelectorAll('.field-card input[type="checkbox"]');
            
            toggleSwitches.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const dot = this.nextElementSibling.nextElementSibling;
                    if (this.checked) {
                        dot.classList.add('bg-blue-500', 'transform', 'translate-x-5');
                        dot.classList.remove('bg-gray-400');
                    } else {
                        dot.classList.remove('bg-blue-500', 'transform', 'translate-x-5');
                        dot.classList.add('bg-gray-400');
                    }
                });
            });
            
            // 模态框控制
            const addFieldBtn = document.getElementById('addFieldBtn');
            const addFieldBtnBottom = document.querySelector('.add-field-btn');
            const addFieldModal = document.getElementById('addFieldModal');
            const closeModalBtn = document.getElementById('closeModalBtn');
            const cancelBtn = document.getElementById('cancelBtn');
            
            function openModal() {
                addFieldModal.classList.remove('hidden');
            }
            
            function closeModal() {
                addFieldModal.classList.add('hidden');
            }
            
            addFieldBtn.addEventListener('click', openModal);
            addFieldBtnBottom.addEventListener('click', openModal);
            closeModalBtn.addEventListener('click', closeModal);
            cancelBtn.addEventListener('click', closeModal);
            
            // 点击模态框外部关闭
            addFieldModal.addEventListener('click', function(e) {
                if (e.target === addFieldModal) {
                    closeModal();
                }
            });
            
            // 模拟拖拽排序功能
            const dragHandles = document.querySelectorAll('.drag-handle');
            
            dragHandles.forEach(handle => {
                handle.addEventListener('mousedown', function() {
                    const card = this.closest('.field-card');
                    card.classList.add('opacity-75', 'border-blue-500');
                });
                
                handle.addEventListener('mouseup', function() {
                    const card = this.closest('.field-card');
                    card.classList.remove('opacity-75', 'border-blue-500');
                });
            });
        });
    </script>
</body>
</html>