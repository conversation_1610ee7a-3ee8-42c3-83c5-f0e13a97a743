/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/port/http/router/Manager.go
 * @Description: Central HTTP router that automatically discovers and registers routes
 *               based on controller method naming conventions, using lazy-loading handlers.
 *
 * © 2025 GACMS. All rights reserved.
 */
package router

import (
	"fmt"
	"gacms/internal/port/http/middleware"
	pkgContract "gacms/pkg/contract"

	"github.com/gin-gonic/gin"
	"go.uber.org/fx"
)

// RoutableRegistration holds a routable component and its target group.
type RoutableRegistration struct {
	Target   pkgContract.RouteTarget
	Routable pkgContract.Routable
}

// Manager is the unified routing center.
type Manager struct {
	engine *gin.Engine
}

// NewManagerParams defines the dependencies for the router manager.
type NewManagerParams struct {
	fx.In
	Config               pkgContract.Config
	SiteResolver         *middleware.SiteResolver
	AdminAuthMiddleware  *middleware.AdminAuthMiddleware // Assuming this exists for now
	Registrations        []RoutableRegistration `group:"routables"`
}

// NewManager creates a new router manager and registers all discovered routes.
func NewManager(p NewManagerParams) (*Manager, error) {
	engine := gin.New()
	engine.Use(gin.Logger())
	engine.Use(gin.Recovery())

	manager := &Manager{
		engine: engine,
	}

	// Register all routes into their respective groups
	manager.registerRoutes(p)

	return manager, nil
}

func (m *Manager) registerRoutes(p NewManagerParams) {
	fmt.Printf("Registering routes for %d routable components...\n", len(p.Registrations))

	// 1. Define Backend Admin Entrypoint
	adminPath := p.Config.GetString("system.admin_path")
	if adminPath == "" {
		adminPath = "/admin" // Default fallback
	}
	adminGroup := m.engine.Group(adminPath)
	adminGroup.Use(p.AdminAuthMiddleware.Handle())
	// TODO: Add optional site resolver for admin group

	// 2. Define Frontend Public Entrypoint
	frontendGroup := m.engine.Group("/")
	frontendGroup.Use(p.SiteResolver.Handle())
	// TODO: Add optional public auth middleware

	// 3. Define Third-Party API Entrypoint
	apiGroup := m.engine.Group("/api")
	// TODO: Add mandatory API Key/OAuth middleware to apiGroup

	// Distribute controllers to their target groups
	for _, reg := range p.Registrations {
		var targetGroup *gin.RouterGroup
		switch reg.Target {
		case pkgContract.AdminRoute:
			targetGroup = adminGroup
			fmt.Printf(" -> Registering admin component: %T\n", reg.Routable)
		case pkgContract.FrontendRoute:
			targetGroup = frontendGroup
			fmt.Printf(" -> Registering frontend component: %T\n", reg.Routable)
		case pkgContract.ThirdPartyAPIRoute:
			targetGroup = apiGroup
			fmt.Printf(" -> Registering 3rd-party API component: %T\n", reg.Routable)
		default:
			fmt.Printf(" ! Skipping component with unknown target: %T\n", reg.Routable)
			continue
		}
		reg.Routable.RegisterRoutes(targetGroup)
	}
}

// GetEngine returns the underlying Gin engine.
func (m *Manager) GetEngine() *gin.Engine {
	return m.engine
}