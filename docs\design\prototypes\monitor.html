<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 系统监控</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/Chart.js/3.7.1/chart.js"></script>
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }

        /* 进度条样式 */
        .progress-bar {
            height: 8px;
            border-radius: 4px;
            background-color: rgba(68, 68, 68, 0.5);
            overflow: hidden;
        }
        .progress-value {
            height: 100%;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        .progress-low {
            background: linear-gradient(135deg, #28a745, #1e7e34);
        }
        .progress-medium {
            background: linear-gradient(135deg, #ffc107, #d39e00);
        }
        .progress-high {
            background: linear-gradient(135deg, #dc3545, #bd2130);
        }

        /* 图表容器样式 */
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }

        /* 状态标签样式 */
        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .status-normal {
            background-color: rgba(40, 167, 69, 0.2);
            color: #28a745;
        }
        .status-warning {
            background-color: rgba(255, 193, 7, 0.2);
            color: #ffc107;
        }
        .status-error {
            background-color: rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">系统监控</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <div class="relative">
                            <select class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 pr-8 text-white appearance-none">
                                <option>实时数据</option>
                                <option>最近1小时</option>
                                <option>最近24小时</option>
                                <option>最近7天</option>
                            </select>
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                        <button class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-download text-white"></i>
                                </span>
                                导出报告
                            </span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 系统状态概览 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- CPU使用率 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">CPU使用率</h3>
                        <span class="text-2xl font-bold">65%</span>
                    </div>
                    <div class="progress-bar mb-2">
                        <div class="progress-value progress-medium" style="width: 65%"></div>
                    </div>
                    <div class="flex justify-between text-sm text-gray-400">
                        <span>8核心</span>
                        <span>3.2GHz</span>
                    </div>
                </div>

                <!-- 内存使用率 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">内存使用率</h3>
                        <span class="text-2xl font-bold">45%</span>
                    </div>
                    <div class="progress-bar mb-2">
                        <div class="progress-value progress-low" style="width: 45%"></div>
                    </div>
                    <div class="flex justify-between text-sm text-gray-400">
                        <span>已用: 7.2GB</span>
                        <span>总计: 16GB</span>
                    </div>
                </div>

                <!-- 磁盘使用率 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">磁盘使用率</h3>
                        <span class="text-2xl font-bold">82%</span>
                    </div>
                    <div class="progress-bar mb-2">
                        <div class="progress-value progress-high" style="width: 82%"></div>
                    </div>
                    <div class="flex justify-between text-sm text-gray-400">
                        <span>已用: 410GB</span>
                        <span>总计: 500GB</span>
                    </div>
                </div>

                <!-- 网络流量 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">网络流量</h3>
                        <span class="text-2xl font-bold">2.5MB/s</span>
                    </div>
                    <div class="progress-bar mb-2">
                        <div class="progress-value progress-low" style="width: 25%"></div>
                    </div>
                    <div class="flex justify-between text-sm text-gray-400">
                        <span>↑ 512KB/s</span>
                        <span>↓ 2.0MB/s</span>
                    </div>
                </div>
            </div>

            <!-- 详细监控数据 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- CPU负载趋势 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                    <h3 class="text-lg font-semibold mb-4">CPU负载趋势</h3>
                    <div class="chart-container">
                        <canvas id="cpuChart"></canvas>
                    </div>
                </div>

                <!-- 内存使用趋势 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                    <h3 class="text-lg font-semibold mb-4">内存使用趋势</h3>
                    <div class="chart-container">
                        <canvas id="memoryChart"></canvas>
                    </div>
                </div>

                <!-- 系统进程 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">系统进程</h3>
                        <span class="text-gray-400">共 24 个进程</span>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="text-left text-gray-400 border-b border-gray-700">
                                    <th class="pb-3">进程名称</th>
                                    <th class="pb-3">PID</th>
                                    <th class="pb-3">CPU</th>
                                    <th class="pb-3">内存</th>
                                    <th class="pb-3">状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                    <td class="py-3">mysql</td>
                                    <td>1258</td>
                                    <td>12.3%</td>
                                    <td>1.2GB</td>
                                    <td><span class="status-badge status-normal">正常</span></td>
                                </tr>
                                <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                    <td class="py-3">nginx</td>
                                    <td>1145</td>
                                    <td>7.8%</td>
                                    <td>420MB</td>
                                    <td><span class="status-badge status-normal">正常</span></td>
                                </tr>
                                <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                    <td class="py-3">php-fpm</td>
                                    <td>1268</td>
                                    <td>15.4%</td>
                                    <td>850MB</td>
                                    <td><span class="status-badge status-normal">正常</span></td>
                                </tr>
                                <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                    <td class="py-3">redis-server</td>
                                    <td>1356</td>
                                    <td>4.2%</td>
                                    <td>380MB</td>
                                    <td><span class="status-badge status-warning">警告</span></td>
                                </tr>
                                <tr class="hover:bg-gray-800/20">
                                    <td class="py-3">memcached</td>
                                    <td>1482</td>
                                    <td>1.5%</td>
                                    <td>210MB</td>
                                    <td><span class="status-badge status-normal">正常</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-4 text-right">
                        <button class="text-blue-400 hover:underline text-sm">查看全部进程</button>
                    </div>
                </div>

                <!-- 系统服务 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">系统服务</h3>
                        <button class="text-blue-400 hover:underline text-sm">刷新</button>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="text-left text-gray-400 border-b border-gray-700">
                                    <th class="pb-3">服务名称</th>
                                    <th class="pb-3">状态</th>
                                    <th class="pb-3">运行时间</th>
                                    <th class="pb-3">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                    <td class="py-3">Web服务器</td>
                                    <td><span class="status-badge status-normal">运行中</span></td>
                                    <td>15天</td>
                                    <td>
                                        <button class="text-gray-400 hover:text-white mr-2"><i class="fas fa-redo-alt"></i></button>
                                        <button class="text-gray-400 hover:text-white"><i class="fas fa-stop"></i></button>
                                    </td>
                                </tr>
                                <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                    <td class="py-3">数据库服务</td>
                                    <td><span class="status-badge status-normal">运行中</span></td>
                                    <td>15天</td>
                                    <td>
                                        <button class="text-gray-400 hover:text-white mr-2"><i class="fas fa-redo-alt"></i></button>
                                        <button class="text-gray-400 hover:text-white"><i class="fas fa-stop"></i></button>
                                    </td>
                                </tr>
                                <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                    <td class="py-3">缓存服务</td>
                                    <td><span class="status-badge status-warning">警告</span></td>
                                    <td>4小时</td>
                                    <td>
                                        <button class="text-gray-400 hover:text-white mr-2"><i class="fas fa-redo-alt"></i></button>
                                        <button class="text-gray-400 hover:text-white"><i class="fas fa-stop"></i></button>
                                    </td>
                                </tr>
                                <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                    <td class="py-3">邮件服务</td>
                                    <td><span class="status-badge status-error">停止</span></td>
                                    <td>-</td>
                                    <td>
                                        <button class="text-gray-400 hover:text-white mr-2"><i class="fas fa-play"></i></button>
                                        <button class="text-gray-400 hover:text-white"><i class="fas fa-cog"></i></button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-800/20">
                                    <td class="py-3">定时任务</td>
                                    <td><span class="status-badge status-normal">运行中</span></td>
                                    <td>15天</td>
                                    <td>
                                        <button class="text-gray-400 hover:text-white mr-2"><i class="fas fa-redo-alt"></i></button>
                                        <button class="text-gray-400 hover:text-white"><i class="fas fa-stop"></i></button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- 系统信息概览 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold mb-4">系统信息</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <ul class="space-y-3">
                            <li class="flex justify-between">
                                <span class="text-gray-400">操作系统:</span>
                                <span class="font-medium">CentOS 8.5 (64位)</span>
                            </li>
                            <li class="flex justify-between">
                                <span class="text-gray-400">Web服务器:</span>
                                <span class="font-medium">Nginx 1.20.2</span>
                            </li>
                            <li class="flex justify-between">
                                <span class="text-gray-400">PHP版本:</span>
                                <span class="font-medium">PHP 8.1.6</span>
                            </li>
                            <li class="flex justify-between">
                                <span class="text-gray-400">MySQL版本:</span>
                                <span class="font-medium">MySQL 8.0.29</span>
                            </li>
                            <li class="flex justify-between">
                                <span class="text-gray-400">Redis版本:</span>
                                <span class="font-medium">Redis 6.2.6</span>
                            </li>
                        </ul>
                    </div>
                    <div>
                        <ul class="space-y-3">
                            <li class="flex justify-between">
                                <span class="text-gray-400">服务器时间:</span>
                                <span class="font-medium">2025-06-15 13:45:22</span>
                            </li>
                            <li class="flex justify-between">
                                <span class="text-gray-400">系统运行时间:</span>
                                <span class="font-medium">15天 7小时 23分钟</span>
                            </li>
                            <li class="flex justify-between">
                                <span class="text-gray-400">服务器IP:</span>
                                <span class="font-medium">*************</span>
                            </li>
                            <li class="flex justify-between">
                                <span class="text-gray-400">GACMS版本:</span>
                                <span class="font-medium">v3.5.2 (最新版本)</span>
                            </li>
                            <li class="flex justify-between">
                                <span class="text-gray-400">最后更新时间:</span>
                                <span class="font-medium">2025-06-10 09:15:30</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 初始化图表
            initCPUChart();
            initMemoryChart();
        });
        
        function initCPUChart() {
            const ctx = document.getElementById('cpuChart').getContext('2d');
            const cpuChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['10:00', '10:05', '10:10', '10:15', '10:20', '10:25', '10:30', '10:35', '10:40', '10:45', '10:50', '10:55', '11:00'],
                    datasets: [{
                        label: 'CPU使用率',
                        data: [45, 52, 49, 60, 55, 68, 65, 75, 68, 60, 55, 50, 65],
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        borderColor: '#007bff',
                        borderWidth: 2,
                        tension: 0.3,
                        pointBackgroundColor: '#007bff',
                        pointBorderColor: '#fff',
                        pointRadius: 4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            backgroundColor: 'rgba(0, 0, 0, 0.7)'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                },
                                color: 'rgba(255, 255, 255, 0.6)'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                color: 'rgba(255, 255, 255, 0.6)'
                            },
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }
        
        function initMemoryChart() {
            const ctx = document.getElementById('memoryChart').getContext('2d');
            const memoryChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['10:00', '10:05', '10:10', '10:15', '10:20', '10:25', '10:30', '10:35', '10:40', '10:45', '10:50', '10:55', '11:00'],
                    datasets: [{
                        label: '内存使用率',
                        data: [35, 38, 40, 42, 45, 44, 46, 45, 48, 50, 47, 45, 45],
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        borderColor: '#28a745',
                        borderWidth: 2,
                        tension: 0.3,
                        pointBackgroundColor: '#28a745',
                        pointBorderColor: '#fff',
                        pointRadius: 4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            backgroundColor: 'rgba(0, 0, 0, 0.7)'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                },
                                color: 'rgba(255, 255, 255, 0.6)'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                color: 'rgba(255, 255, 255, 0.6)'
                            },
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }
    </script>
</body>
</html> 