<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 主题市场</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .card {
            transition: all 0.3s ease;
        }
        
        .btn {
            transition: all 0.3s ease;
        }
        
        .tag {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
        }
        
        .theme-card {
            transition: all 0.3s ease;
        }
        
        .theme-preview {
            position: relative;
            overflow: hidden;
            border-radius: 0.5rem 0.5rem 0 0;
        }
        
        .rating {
            color: #FFD700;
        }
        
        .star {
            margin-right: 1px;
        }
        
        .empty {
            color: #4B5563;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm text-gray-400 mb-4">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <span class="mx-2">/</span>
                <a href="themes.html" class="hover:text-white">主题管理</a>
                <span class="mx-2">/</span>
                <span class="text-white">主题市场</span>
            </div>
            
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">主题市场</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <a href="themes.html" class="flex items-center justify-center bg-gray-700 hover:bg-gray-600 text-white px-5 py-3 rounded-lg font-medium transition-all">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-palette text-white"></i>
                                </span>
                                已安装主题
                            </span>
                        </a>
                        <a href="theme_dev.html" class="flex items-center justify-center bg-gradient-to-r from-purple-500 to-purple-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-purple-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-code text-white"></i>
                                </span>
                                主题开发
                            </span>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 统计概览 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="flex items-center p-4 bg-gray-800/30 rounded-xl border border-gray-700">
                        <div class="p-3 rounded-full bg-blue-500/20 mr-4">
                            <i class="fas fa-palette text-blue-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">主题总数</h3>
                            <p class="text-2xl font-bold">86</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center p-4 bg-gray-800/30 rounded-xl border border-gray-700">
                        <div class="p-3 rounded-full bg-green-500/20 mr-4">
                            <i class="fas fa-check-circle text-green-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">已安装主题</h3>
                            <p class="text-2xl font-bold">4</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center p-4 bg-gray-800/30 rounded-xl border border-gray-700">
                        <div class="p-3 rounded-full bg-yellow-500/20 mr-4">
                            <i class="fas fa-sync-alt text-yellow-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">可更新主题</h3>
                            <p class="text-2xl font-bold">2</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center p-4 bg-gray-800/30 rounded-xl border border-gray-700">
                        <div class="p-3 rounded-full bg-purple-500/20 mr-4">
                            <i class="fas fa-download text-purple-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">下载总量</h3>
                            <p class="text-2xl font-bold">18.3K</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 主要内容区 -->
            <div class="flex flex-col lg:flex-row gap-6">
                <!-- 左侧筛选区 -->
                <div class="lg:w-1/4">
                    <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5 mb-6">
                        <h3 class="text-lg font-semibold mb-4">搜索</h3>
                        <div class="relative">
                            <input type="text" placeholder="搜索主题..." class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 pl-10 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                    </div>
                    
                    <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5 mb-6">
                        <h3 class="text-lg font-semibold mb-4">主题分类</h3>
                        <ul class="space-y-2">
                            <li>
                                <a href="#" class="flex items-center justify-between text-blue-400 hover:text-blue-300">
                                    <span>全部主题</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">86</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>企业官网</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">32</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>博客杂志</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">18</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>电子商务</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">14</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>作品展示</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">11</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>社区论坛</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">7</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>教育培训</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">4</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5 mb-6">
                        <h3 class="text-lg font-semibold mb-4">价格</h3>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                <span class="ml-2 text-gray-400">免费</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                <span class="ml-2 text-gray-400">付费</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                        <h3 class="text-lg font-semibold mb-4">特性</h3>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                <span class="ml-2 text-gray-400">响应式设计</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                <span class="ml-2 text-gray-400">深色模式</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600">
                                <span class="ml-2 text-gray-400">RTL支持</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                <span class="ml-2 text-gray-400">多语言支持</span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧主题列表 -->
                <div class="lg:w-3/4">
                    <!-- 排序工具栏 -->
                    <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-4 mb-6 flex flex-wrap items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="text-gray-400">排序:</span>
                            <button class="bg-blue-600 text-white px-3 py-1 rounded-lg text-sm">最新发布</button>
                            <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-1 rounded-lg text-sm">下载最多</button>
                            <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-1 rounded-lg text-sm">评分最高</button>
                        </div>
                        <div class="flex items-center space-x-2 mt-2 sm:mt-0">
                            <span class="text-gray-400">视图:</span>
                            <button class="bg-blue-600 text-white p-1 rounded-lg">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 p-1 rounded-lg">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 主题列表 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                        <!-- 主题卡片 1 -->
                        <div class="card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="theme-card">
                                <div class="theme-preview">
                                    <img src="https://images.unsplash.com/photo-1517292987719-0369a794ec0f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="现代企业主题">
                                    <!-- 图片来源: https://unsplash.com/photos/black-and-white-website-page-on-macbook-pro-_t-l5FFH8VA -->
                                </div>
                                <div class="p-5">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-white">现代企业主题</h3>
                                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">免费</span>
                                    </div>
                                    <div class="flex items-center mb-2">
                                        <div class="rating mr-2">
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star-half-alt star"></i>
                                        </div>
                                        <span class="text-gray-400 text-sm">4.5 (98)</span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-3">专为现代企业设计的响应式主题，提供多种布局选项和丰富的自定义功能。</p>
                                    <div class="flex flex-wrap mb-4">
                                        <span class="tag bg-blue-500/10 text-blue-400">企业</span>
                                        <span class="tag bg-green-500/10 text-green-400">响应式</span>
                                        <span class="tag bg-purple-500/10 text-purple-400">多语言</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400 text-sm">v2.1.0 | 8.5K 下载</span>
                                        <button class="bg-gradient-to-r from-blue-500 to-blue-700 text-white px-4 py-1 rounded-lg text-sm hover:shadow-lg hover:shadow-blue-500/30 transition-all btn">
                                            安装
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 主题卡片 2 -->
                        <div class="card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="theme-card">
                                <div class="theme-preview">
                                    <img src="https://images.unsplash.com/photo-1507721999472-8ed4421c4af2?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="高级博客主题">
                                    <!-- 图片来源: https://unsplash.com/photos/silver-macbook-pro-beside-white-ceramic-mug-on-table-TkEPQPWr2sY -->
                                </div>
                                <div class="p-5">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-white">高级博客主题</h3>
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">¥299</span>
                                    </div>
                                    <div class="flex items-center mb-2">
                                        <div class="rating mr-2">
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                        </div>
                                        <span class="text-gray-400 text-sm">5.0 (76)</span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-3">专业博客和杂志主题，提供多种文章布局、高级排版和内容展示选项。</p>
                                    <div class="flex flex-wrap mb-4">
                                        <span class="tag bg-blue-500/10 text-blue-400">博客</span>
                                        <span class="tag bg-red-500/10 text-red-400">杂志</span>
                                        <span class="tag bg-yellow-500/10 text-yellow-400">排版</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400 text-sm">v3.2.1 | 6.2K 下载</span>
                                        <button class="bg-gradient-to-r from-green-500 to-green-700 text-white px-4 py-1 rounded-lg text-sm hover:shadow-lg hover:shadow-green-500/30 transition-all btn">
                                            购买
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 主题卡片 3 -->
                        <div class="card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="theme-card">
                                <div class="theme-preview">
                                    <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="电商专业版">
                                    <!-- 图片来源: https://unsplash.com/photos/business-chart-on-laptop-screen-15Vb4B_ma_s -->
                                </div>
                                <div class="p-5">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-white">电商专业版</h3>
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">¥499</span>
                                    </div>
                                    <div class="flex items-center mb-2">
                                        <div class="rating mr-2">
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star empty"></i>
                                        </div>
                                        <span class="text-gray-400 text-sm">4.0 (45)</span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-3">专为电子商务网站设计的高级主题，包含产品展示、购物车和结账流程等功能。</p>
                                    <div class="flex flex-wrap mb-4">
                                        <span class="tag bg-red-500/10 text-red-400">电商</span>
                                        <span class="tag bg-yellow-500/10 text-yellow-400">购物</span>
                                        <span class="tag bg-blue-500/10 text-blue-400">产品</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400 text-sm">v2.5.0 | 4.8K 下载</span>
                                        <button class="bg-gradient-to-r from-green-500 to-green-700 text-white px-4 py-1 rounded-lg text-sm hover:shadow-lg hover:shadow-green-500/30 transition-all btn">
                                            购买
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 主题卡片 4 -->
                        <div class="card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="theme-card">
                                <div class="theme-preview">
                                    <img src="https://images.unsplash.com/photo-1517292987719-0369a794ec0f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="作品集展示">
                                    <!-- 图片来源: https://unsplash.com/photos/black-and-white-website-page-on-macbook-pro-_t-l5FFH8VA -->
                                </div>
                                <div class="p-5">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-white">作品集展示</h3>
                                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">免费</span>
                                    </div>
                                    <div class="flex items-center mb-2">
                                        <div class="rating mr-2">
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                        </div>
                                        <span class="text-gray-400 text-sm">4.9 (112)</span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-3">简洁优雅的作品集展示主题，适合设计师、摄影师和创意人士展示作品。</p>
                                    <div class="flex flex-wrap mb-4">
                                        <span class="tag bg-purple-500/10 text-purple-400">作品集</span>
                                        <span class="tag bg-blue-500/10 text-blue-400">创意</span>
                                        <span class="tag bg-green-500/10 text-green-400">展示</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400 text-sm">v1.8.3 | 9.4K 下载</span>
                                        <button class="bg-gradient-to-r from-blue-500 to-blue-700 text-white px-4 py-1 rounded-lg text-sm hover:shadow-lg hover:shadow-blue-500/30 transition-all btn">
                                            安装
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 主题卡片 5 -->
                        <div class="card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="theme-card">
                                <div class="theme-preview">
                                    <img src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="社区论坛主题">
                                    <!-- 图片来源: https://unsplash.com/photos/person-using-macbook-pro-on-person-s-lap-gcsNOsPEXfs -->
                                </div>
                                <div class="p-5">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-white">社区论坛主题</h3>
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">¥399</span>
                                    </div>
                                    <div class="flex items-center mb-2">
                                        <div class="rating mr-2">
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star-half-alt star"></i>
                                        </div>
                                        <span class="text-gray-400 text-sm">4.6 (58)</span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-3">专为社区和论坛设计的主题，提供会员管理、讨论区和通知系统等功能。</p>
                                    <div class="flex flex-wrap mb-4">
                                        <span class="tag bg-blue-500/10 text-blue-400">社区</span>
                                        <span class="tag bg-yellow-500/10 text-yellow-400">论坛</span>
                                        <span class="tag bg-purple-500/10 text-purple-400">会员</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400 text-sm">v2.3.1 | 3.7K 下载</span>
                                        <button class="bg-gradient-to-r from-green-500 to-green-700 text-white px-4 py-1 rounded-lg text-sm hover:shadow-lg hover:shadow-green-500/30 transition-all btn">
                                            购买
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 主题卡片 6 -->
                        <div class="card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="theme-card">
                                <div class="theme-preview">
                                    <img src="https://images.unsplash.com/photo-1563986768609-322da13575f3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="教育培训主题">
                                    <!-- 图片来源: https://unsplash.com/photos/blue-and-white-light-digital-wallpaper-8bghKxNU1j0 -->
                                </div>
                                <div class="p-5">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-white">教育培训主题</h3>
                                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">免费</span>
                                    </div>
                                    <div class="flex items-center mb-2">
                                        <div class="rating mr-2">
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star empty"></i>
                                        </div>
                                        <span class="text-gray-400 text-sm">4.2 (87)</span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-3">专为教育机构和在线课程设计的主题，包含课程展示、学习进度和师资介绍等功能。</p>
                                    <div class="flex flex-wrap mb-4">
                                        <span class="tag bg-green-500/10 text-green-400">教育</span>
                                        <span class="tag bg-blue-500/10 text-blue-400">课程</span>
                                        <span class="tag bg-red-500/10 text-red-400">培训</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400 text-sm">v1.5.2 | 5.3K 下载</span>
                                        <button class="bg-gradient-to-r from-blue-500 to-blue-700 text-white px-4 py-1 rounded-lg text-sm hover:shadow-lg hover:shadow-blue-500/30 transition-all btn">
                                            安装
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 分页控制 -->
                    <div class="flex justify-between items-center">
                        <div class="text-sm text-gray-400">
                            显示 1-6 / 共 86 个主题
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="px-3 py-1 bg-blue-600 text-white rounded-md">1</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">2</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">3</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">4</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 卡片悬停效果
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.classList.add('transform', 'scale-[1.02]');
                });
                card.addEventListener('mouseleave', function() {
                    this.classList.remove('transform', 'scale-[1.02]');
                });
            });
            
            // 按钮悬停效果
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.classList.add('transform', 'scale-[1.05]');
                });
                button.addEventListener('mouseleave', function() {
                    this.classList.remove('transform', 'scale-[1.05]');
                });
            });
        });
    </script>
</body>
</html>