/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON> <<EMAIL>>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/theme/domain/model/SiteTheme.go
 * @Description: Defines the relationship between a site and an installed theme, representing the 'site_themes' table.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package model

import "gorm.io/gorm"

type SiteTheme struct {
	gorm.Model
	SiteID    uint   `gorm:"not null;uniqueIndex:idx_site_theme"`
	ThemeName string `gorm:"type:varchar(100);not null;uniqueIndex:idx_site_theme"`
} 