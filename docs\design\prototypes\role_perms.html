<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 角色权限管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .permissions-group {
            position: relative;
            transition: all 0.3s ease;
        }
        
        .permissions-group:hover {
            background-color: rgba(75, 85, 99, 0.1);
        }
        
        .permissions-group-title {
            position: relative;
            cursor: pointer;
            user-select: none;
        }
        
        .permissions-group-title::after {
            content: '\f0d7';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            right: 0;
            transition: transform 0.3s ease;
        }
        
        .permissions-group.collapsed .permissions-group-title::after {
            transform: rotate(-90deg);
        }
        
        .permissions-group.collapsed .permissions-list {
            max-height: 0;
            opacity: 0;
            overflow: hidden;
        }
        
        .permissions-list {
            max-height: 1000px;
            opacity: 1;
            transition: all 0.3s ease;
            overflow: hidden;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="roles.html" class="hover:text-white">角色管理</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">角色权限</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">角色权限管理: 编辑部主管</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="savePermissionsBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-save text-white"></i>
                                </span>
                                保存权限设置
                            </span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 角色信息卡片 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap items-center gap-6">
                    <div class="flex-shrink-0 w-16 h-16 rounded-full bg-blue-500/20 flex items-center justify-center">
                        <i class="fas fa-user-tie text-blue-500 text-2xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-white">编辑部主管</h3>
                        <p class="text-gray-400">管理内容发布、审核文章、分配任务</p>
                        <div class="flex items-center gap-4 mt-2 text-sm">
                            <span class="text-gray-400">创建时间: <span class="text-white">2025-03-15</span></span>
                            <span class="text-gray-400">用户数量: <span class="text-white">8</span> 名</span>
                            <span class="text-gray-400">角色级别: <span class="text-blue-400">中级管理员</span></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 操作控制区 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap gap-4">
                    <div class="flex items-center">
                        <span class="text-gray-400 mr-2">继承权限:</span>
                        <div class="relative">
                            <select class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="none">不继承</option>
                                <option value="content-editor">内容编辑</option>
                                <option value="reviewer" selected>内容审核员</option>
                                <option value="admin">管理员</option>
                            </select>
                            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                                <i class="fas fa-chevron-down text-xs"></i>
                            </div>
                        </div>
                    </div>
                    
                    <button class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors flex items-center">
                        <i class="fas fa-copy mr-2"></i> 从模板复制
                    </button>
                    
                    <div class="flex-1"></div>
                    
                    <label class="inline-flex items-center cursor-pointer">
                        <span class="mr-2 text-gray-400">全部选择</span>
                        <div class="relative">
                            <input type="checkbox" id="selectAll" class="sr-only">
                            <div class="w-10 h-5 bg-gray-700 rounded-full shadow-inner"></div>
                            <div class="dot absolute w-4 h-4 bg-gray-400 rounded-full transition left-0.5 top-0.5"></div>
                        </div>
                    </label>
                </div>
            </div>
            
            <!-- 权限管理主区域 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 左侧: 内容管理权限 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <h3 class="text-lg font-semibold mb-4 text-white">内容管理权限</h3>
                    
                    <!-- 文章管理权限组 -->
                    <div class="permissions-group mb-4 border-b border-gray-700 pb-2">
                        <div class="permissions-group-title font-medium text-white py-2">
                            文章管理
                        </div>
                        <div class="permissions-list pl-2">
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">查看文章</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-blue-500 w-4 h-4 rounded-full transition-transform transform translate-x-5"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">添加文章</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-blue-500 w-4 h-4 rounded-full transition-transform transform translate-x-5"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">编辑文章</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-blue-500 w-4 h-4 rounded-full transition-transform transform translate-x-5"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">删除文章</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-gray-400 w-4 h-4 rounded-full transition-transform"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">发布文章</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-blue-500 w-4 h-4 rounded-full transition-transform transform translate-x-5"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">审核他人文章</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-blue-500 w-4 h-4 rounded-full transition-transform transform translate-x-5"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 分类管理权限组 -->
                    <div class="permissions-group mb-4 border-b border-gray-700 pb-2">
                        <div class="permissions-group-title font-medium text-white py-2">
                            分类管理
                        </div>
                        <div class="permissions-list pl-2">
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">查看分类</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-blue-500 w-4 h-4 rounded-full transition-transform transform translate-x-5"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">添加分类</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-blue-500 w-4 h-4 rounded-full transition-transform transform translate-x-5"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">编辑分类</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-gray-400 w-4 h-4 rounded-full transition-transform"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">删除分类</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-gray-400 w-4 h-4 rounded-full transition-transform"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 媒体管理权限组 -->
                    <div class="permissions-group mb-4 border-b border-gray-700 pb-2">
                        <div class="permissions-group-title font-medium text-white py-2">
                            媒体管理
                        </div>
                        <div class="permissions-list pl-2">
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">查看媒体库</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-blue-500 w-4 h-4 rounded-full transition-transform transform translate-x-5"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">上传媒体文件</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-blue-500 w-4 h-4 rounded-full transition-transform transform translate-x-5"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">编辑媒体信息</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-blue-500 w-4 h-4 rounded-full transition-transform transform translate-x-5"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">删除媒体文件</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-gray-400 w-4 h-4 rounded-full transition-transform"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧: 系统管理权限 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <h3 class="text-lg font-semibold mb-4 text-white">系统管理权限</h3>
                    
                    <!-- 用户管理权限组 -->
                    <div class="permissions-group mb-4 border-b border-gray-700 pb-2">
                        <div class="permissions-group-title font-medium text-white py-2">
                            用户管理
                        </div>
                        <div class="permissions-list pl-2">
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">查看用户</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-blue-500 w-4 h-4 rounded-full transition-transform transform translate-x-5"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">添加用户</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-gray-400 w-4 h-4 rounded-full transition-transform"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">编辑用户</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-gray-400 w-4 h-4 rounded-full transition-transform"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">删除用户</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-gray-400 w-4 h-4 rounded-full transition-transform"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 权限管理权限组 -->
                    <div class="permissions-group mb-4 border-b border-gray-700 pb-2">
                        <div class="permissions-group-title font-medium text-white py-2">
                            权限管理
                        </div>
                        <div class="permissions-list pl-2">
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">查看角色</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-blue-500 w-4 h-4 rounded-full transition-transform transform translate-x-5"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">创建角色</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-gray-400 w-4 h-4 rounded-full transition-transform"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">编辑角色</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-gray-400 w-4 h-4 rounded-full transition-transform"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">管理权限</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-gray-400 w-4 h-4 rounded-full transition-transform"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 系统设置权限组 -->
                    <div class="permissions-group mb-4 border-b border-gray-700 pb-2">
                        <div class="permissions-group-title font-medium text-white py-2">
                            系统设置
                        </div>
                        <div class="permissions-list pl-2">
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">查看系统设置</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-gray-400 w-4 h-4 rounded-full transition-transform"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">修改系统设置</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-gray-400 w-4 h-4 rounded-full transition-transform"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between py-2 border-t border-gray-700/50">
                                <span class="text-gray-300">备份与恢复</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only">
                                    <div class="w-11 h-6 bg-gray-700 rounded-full"></div>
                                    <div class="absolute left-1 top-1 bg-gray-400 w-4 h-4 rounded-full transition-transform"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 底部按钮区域 -->
            <div class="mt-6 flex justify-end">
                <button class="px-5 py-2.5 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg mr-3 transition-colors">
                    取消
                </button>
                <button id="savePermissionsBtn2" class="px-5 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:shadow-lg hover:shadow-blue-500/30 transition-all">
                    保存更改
                </button>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 权限组折叠/展开功能
            const permissionsGroups = document.querySelectorAll('.permissions-group-title');
            
            permissionsGroups.forEach(group => {
                group.addEventListener('click', function() {
                    const parentGroup = this.closest('.permissions-group');
                    parentGroup.classList.toggle('collapsed');
                });
            });
            
            // 全选/全不选功能
            const selectAllCheckbox = document.getElementById('selectAll');
            const allCheckboxes = document.querySelectorAll('.permissions-list input[type="checkbox"]');
            
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    const isChecked = this.checked;
                    
                    allCheckboxes.forEach(checkbox => {
                        checkbox.checked = isChecked;
                        
                        // 更新开关按钮的视觉效果
                        const dot = checkbox.nextElementSibling.nextElementSibling;
                        if (isChecked) {
                            dot.classList.add('bg-blue-500', 'transform', 'translate-x-5');
                            dot.classList.remove('bg-gray-400');
                        } else {
                            dot.classList.remove('bg-blue-500', 'transform', 'translate-x-5');
                            dot.classList.add('bg-gray-400');
                        }
                    });
                    
                    // 更新全选按钮的视觉效果
                    const selectAllDot = document.querySelector('#selectAll + div + div');
                    if (isChecked) {
                        selectAllDot.classList.add('bg-blue-500', 'transform', 'translate-x-5');
                        selectAllDot.classList.remove('bg-gray-400');
                    } else {
                        selectAllDot.classList.remove('bg-blue-500', 'transform', 'translate-x-5');
                        selectAllDot.classList.add('bg-gray-400');
                    }
                });
            }
            
            // 个别开关切换效果
            allCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const dot = this.nextElementSibling.nextElementSibling;
                    if (this.checked) {
                        dot.classList.add('bg-blue-500', 'transform', 'translate-x-5');
                        dot.classList.remove('bg-gray-400');
                    } else {
                        dot.classList.remove('bg-blue-500', 'transform', 'translate-x-5');
                        dot.classList.add('bg-gray-400');
                    }
                });
            });
            
            // 保存按钮事件
            const saveButtons = document.querySelectorAll('#savePermissionsBtn, #savePermissionsBtn2');
            saveButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 模拟保存成功
                    alert('角色权限保存成功！');
                    // 实际情况会发送 AJAX 请求
                });
            });
        });
    </script>
</body>
</html> 