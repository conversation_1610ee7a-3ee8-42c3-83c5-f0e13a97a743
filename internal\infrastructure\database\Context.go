/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/infrastructure/database/Context.go
 * @Description: Context helpers for database operations.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package database

import "context"

// contextKey is a private type to prevent collisions with other packages' context keys.
type contextKey string

const siteIDKey = contextKey("siteID")

// WithSiteID returns a new context with the provided site ID.
func WithSiteID(ctx context.Context, siteID uint) context.Context {
	return context.WithValue(ctx, siteIDKey, siteID)
}

// SiteIDFrom returns the site ID from the context, if one exists.
func SiteIDFrom(ctx context.Context) (uint, bool) {
	siteID, ok := ctx.Value(siteIDKey).(uint)
	return siteID, ok
} 