<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 页面编辑</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .editor-container {
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .editor-toolbar {
            background-color: rgba(31, 41, 55, 0.5);
            border-bottom: 1px solid rgba(75, 85, 99, 0.5);
        }
        
        .editor-toolbar button {
            margin: 0;
            padding: 8px 12px;
            background: transparent;
            border: none;
            color: #e0e0e0;
            cursor: pointer;
            border-right: 1px solid rgba(75, 85, 99, 0.3);
        }
        
        .editor-toolbar button:hover {
            background-color: rgba(75, 85, 99, 0.3);
        }
        
        .editor-content {
            min-height: 300px;
            padding: 16px;
            background-color: rgba(31, 41, 55, 0.3);
        }
        
        .active-tab {
            background-color: rgba(59, 130, 246, 0.2);
            border-bottom: 2px solid #3b82f6;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="content.html" class="hover:text-white">内容管理</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="pages.html" class="hover:text-white">页面管理</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">编辑页面</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">编辑页面</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <a href="page_preview.html" target="_blank" class="flex items-center justify-center bg-gradient-to-r from-purple-500 to-purple-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-purple-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-eye text-white"></i>
                                </span>
                                预览
                            </span>
                        </a>
                        <button id="saveBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-save text-white"></i>
                                </span>
                                保存
                            </span>
                        </button>
                        <a href="pages.html" class="flex items-center justify-center bg-gradient-to-r from-gray-600 to-gray-700 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-gray-600/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-times text-white"></i>
                                </span>
                                取消
                            </span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 页面编辑区域 -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                <!-- 左侧：页面内容 -->
                <div class="lg:col-span-2">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">页面标题 <span class="text-red-500">*</span></label>
                            <input type="text" id="pageTitle" value="关于我们 - 公司简介" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">页面别名（URL）</label>
                            <div class="flex">
                                <span class="inline-flex items-center px-3 bg-gray-800 border border-r-0 border-gray-600 rounded-l-lg text-gray-400">
                                    /page/
                                </span>
                                <input type="text" id="pageSlug" value="about-us" class="flex-1 bg-gray-700 border border-gray-600 rounded-r-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <p class="text-gray-400 text-sm mt-1">生成的URL: https://www.example.com/page/about-us</p>
                        </div>
                        
                        <!-- 内容编辑器标签 -->
                        <div class="mb-4 border-b border-gray-700">
                            <ul class="flex flex-wrap -mb-px">
                                <li class="mr-2">
                                    <button class="tab-btn active-tab inline-block p-4 rounded-t-lg border-b-2 border-blue-500" data-tab="visual">
                                        可视化编辑器
                                    </button>
                                </li>
                                <li class="mr-2">
                                    <button class="tab-btn inline-block p-4 rounded-t-lg" data-tab="code">
                                        HTML代码
                                    </button>
                                </li>
                                <li class="mr-2">
                                    <button class="tab-btn inline-block p-4 rounded-t-lg" data-tab="blocks">
                                        区块编辑
                                    </button>
                                </li>
                            </ul>
                        </div>
                        
                        <!-- 编辑器 -->
                        <div class="editor-container mb-4">
                            <div class="editor-toolbar flex flex-wrap">
                                <button><i class="fas fa-paragraph"></i></button>
                                <button><i class="fas fa-heading"></i></button>
                                <button><i class="fas fa-bold"></i></button>
                                <button><i class="fas fa-italic"></i></button>
                                <button><i class="fas fa-underline"></i></button>
                                <button><i class="fas fa-list-ul"></i></button>
                                <button><i class="fas fa-list-ol"></i></button>
                                <button><i class="fas fa-link"></i></button>
                                <button><i class="fas fa-image"></i></button>
                                <button><i class="fas fa-video"></i></button>
                                <button><i class="fas fa-table"></i></button>
                                <button><i class="fas fa-code"></i></button>
                                <button><i class="fas fa-undo"></i></button>
                                <button><i class="fas fa-redo"></i></button>
                            </div>
                            <div class="editor-content" contenteditable="true">
                                <h2 style="margin-bottom: 16px; font-size: 20px; font-weight: bold;">公司简介</h2>
                                <p style="margin-bottom: 16px;">GACMS科技有限公司成立于2020年，是一家专注于内容管理系统(CMS)研发的科技公司。我们的使命是为全球客户提供最灵活、高效、安全的内容管理解决方案。</p>
                                <p style="margin-bottom: 16px;">公司总部位于北京，在上海、深圳和成都设有分支机构，拥有一支由50多名经验丰富的开发人员、设计师和内容策略专家组成的精英团队。</p>
                                <h3 style="margin-top: 24px; margin-bottom: 16px; font-size: 18px; font-weight: bold;">我们的核心价值观</h3>
                                <ul style="margin-bottom: 16px; padding-left: 24px;">
                                    <li>创新：不断探索新技术和新方法</li>
                                    <li>品质：追求卓越的产品质量和用户体验</li>
                                    <li>诚信：诚实正直地对待每一位客户和合作伙伴</li>
                                    <li>协作：相信团队合作的力量，共同成长</li>
                                </ul>
                                <h3 style="margin-top: 24px; margin-bottom: 16px; font-size: 18px; font-weight: bold;">发展历程</h3>
                                <p style="margin-bottom: 16px;">从2020年成立至今，我们的产品已经为超过1000家企业和组织提供了内容管理支持，其中包括多家世界500强企业和政府机构。</p>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">摘要</label>
                            <textarea id="pageExcerpt" rows="3" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">GACMS科技有限公司成立于2020年，是一家专注于内容管理系统(CMS)研发的科技公司。我们致力于为各行业客户提供最优质的内容管理解决方案。</textarea>
                            <p class="text-gray-400 text-sm mt-1">显示在列表页、搜索结果和社交媒体分享时使用</p>
                        </div>
                    </div>
                    
                    <!-- 页面设置 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">页面设置</h3>
                        
                        <!-- 模板选择 -->
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">页面模板</label>
                            <select id="pageTemplate" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="default">默认模板</option>
                                <option value="full-width" selected>全宽模板</option>
                                <option value="landing-page">着陆页模板</option>
                                <option value="sidebar-right">右侧边栏模板</option>
                                <option value="contact">联系页模板</option>
                            </select>
                        </div>
                        
                        <!-- 父级页面 -->
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">父级页面</label>
                            <select id="parentPage" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="0">无（顶级页面）</option>
                                <option value="42" selected>关于我们</option>
                                <option value="43">服务</option>
                                <option value="45">支持</option>
                            </select>
                        </div>
                        
                        <!-- 排序 -->
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">排序</label>
                            <input type="number" id="pageOrder" value="10" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <p class="text-gray-400 text-sm mt-1">数字越小排序越靠前</p>
                        </div>
                        
                        <!-- 页面属性 -->
                        <h4 class="font-medium text-white mb-2">页面属性</h4>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 mb-4">
                            <label class="flex items-center">
                                <input type="checkbox" id="pageShowInMenu" checked class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-900 w-5 h-5">
                                <span class="ml-2 text-gray-300">在导航菜单中显示</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="pageShowInFooter" class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-900 w-5 h-5">
                                <span class="ml-2 text-gray-300">在页脚中显示</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="pageAllowComments" class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-900 w-5 h-5">
                                <span class="ml-2 text-gray-300">允许评论</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="pageSticky" class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-900 w-5 h-5">
                                <span class="ml-2 text-gray-300">置顶页面</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 自定义CSS和JavaScript -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">自定义脚本和样式</h3>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">自定义CSS</label>
                            <textarea id="customCSS" rows="4" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">.about-section {
    padding: 40px 0;
    background-color: #f8f9fa;
}
.about-section h2 {
    color: #0d6efd;
    margin-bottom: 20px;
}</textarea>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">自定义JavaScript</label>
                            <textarea id="customJS" rows="4" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">document.addEventListener('DOMContentLoaded', function() {
    console.log('关于我们页面已加载');
});</textarea>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：发布设置 -->
                <div class="lg:col-span-1">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">发布设置</h3>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">状态</label>
                            <select id="pageStatus" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="published" selected>已发布</option>
                                <option value="draft">草稿</option>
                                <option value="pending">待审核</option>
                                <option value="private">私密</option>
                            </select>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">可见性</label>
                            <select id="pageVisibility" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="public" selected>公开</option>
                                <option value="private">私密</option>
                                <option value="password">密码保护</option>
                            </select>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">发布时间</label>
                            <input type="datetime-local" id="publishDate" value="2025-04-15T14:30" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">作者</label>
                            <select id="pageAuthor" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="1" selected>管理员</option>
                                <option value="2">内容编辑</option>
                                <option value="3">市场专员</option>
                            </select>
                        </div>
                        
                        <div class="flex justify-between mt-6">
                            <button class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded">保存草稿</button>
                            <button class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded">立即发布</button>
                        </div>
                    </div>
                    
                    <!-- 特色图片 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">特色图片</h3>
                        
                        <div class="mb-4">
                            <div class="border-2 border-dashed border-gray-600 rounded-lg p-6">
                                <div class="aspect-video bg-cover bg-center rounded-lg flex items-center justify-center" style="background-image: url('https://images.unsplash.com/photo-1497366754035-f200968a6e72?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80')">
                                    <div class="bg-black/50 px-4 py-2 rounded-lg">
                                        <button class="text-white hover:text-gray-200">
                                            <i class="fas fa-pencil-alt mr-2"></i> 更换图片
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-400 text-sm mt-2">建议尺寸：1200×675像素 (16:9比例)</p>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">图片替代文本 (Alt)</label>
                            <input type="text" value="GACMS公司办公室" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">图片说明</label>
                            <input type="text" value="GACMS总部现代化办公环境" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>
                    
                    <!-- SEO设置 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">SEO设置</h3>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">SEO标题</label>
                            <input type="text" value="关于GACMS - 专业的内容管理系统提供商" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <p class="text-gray-400 text-sm mt-1">建议不超过60个字符</p>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">META描述</label>
                            <textarea rows="3" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">了解GACMS科技有限公司的历史、使命和价值观。我们是一家专注于开发高性能内容管理系统的创新型科技公司，致力于为企业提供最佳内容管理解决方案。</textarea>
                            <p class="text-gray-400 text-sm mt-1">建议不超过160个字符</p>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">焦点关键词</label>
                            <input type="text" value="GACMS, 内容管理系统, CMS公司, 企业简介" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <p class="text-gray-400 text-sm mt-1">多个关键词用逗号分隔</p>
                        </div>
                    </div>
                    
                    <!-- 修订历史 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">修订历史</h3>
                        
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <div>
                                    <p class="text-white">当前版本</p>
                                    <p class="text-gray-400 text-xs">由 管理员 于 2025-05-12 14:30 更新</p>
                                </div>
                            </div>
                            
                            <hr class="border-gray-700">
                            
                            <div>
                                <p class="text-gray-300 mb-2">历史版本</p>
                                <ul class="space-y-2">
                                    <li>
                                        <div class="flex justify-between">
                                            <span class="text-blue-400 hover:text-blue-300 cursor-pointer">版本 3</span>
                                            <span class="text-gray-400 text-sm">2025-05-01</span>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="flex justify-between">
                                            <span class="text-blue-400 hover:text-blue-300 cursor-pointer">版本 2</span>
                                            <span class="text-gray-400 text-sm">2025-04-15</span>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="flex justify-between">
                                            <span class="text-blue-400 hover:text-blue-300 cursor-pointer">版本 1</span>
                                            <span class="text-gray-400 text-sm">2025-03-22</span>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 选项卡功能
            const tabButtons = document.querySelectorAll('.tab-btn');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有选项卡的活动状态
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active-tab');
                    });
                    
                    // 为当前点击的选项卡添加活动状态
                    this.classList.add('active-tab');
                    
                    // 在实际应用中，这里会切换编辑器模式
                    console.log('切换到：' + this.getAttribute('data-tab') + '模式');
                });
            });
            
            // 表单保存功能
            document.getElementById('saveBtn').addEventListener('click', function() {
                alert('页面保存成功！');
                // 在实际场景中，这里会有AJAX请求保存表单数据
            });
        });
    </script>
</body>
</html> 