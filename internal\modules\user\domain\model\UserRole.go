/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: {current_date}
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: {current_date}
 * @FilePath: internal/modules/user/domain/model/UserRole.go
 * @Description: Defines the join model for the many-to-many relationship between users and roles.
 *
 * © {current_year} GACMS. All rights reserved.
 */
package model

// UserRole is the explicit join table between a user (Admin or Member) and a Role.
// It includes a UserType field to distinguish which type of user the role is for,
// enabling a polymorphic-like association.
type UserRole struct {
	UserID   uint     `gorm:"primaryKey"`
	RoleID   uint     `gorm:"primaryKey"`
	UserType UserType `gorm:"primaryKey;type:enum('admin','member')"`

	// Relationships
	Role Role `gorm:"foreignKey:RoleID"`
} 