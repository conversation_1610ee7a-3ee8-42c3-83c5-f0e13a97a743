/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: {current_date}
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: {current_date}
 * @FilePath: internal/modules/user/domain/model/Admin.go
 * @Description: Defines the data model for the backend user (Admin),
 *               corresponding to the 'admins' database table.
 *
 * © {current_year} GACMS. All rights reserved.
 */
package model

import "time"

// Admin represents a backend user of the system.
type Admin struct {
	ID            uint      `gorm:"primaryKey"`
	SiteID        uint      `gorm:"not null;uniqueIndex:idx_site_email"`
	Email         string    `gorm:"not null;uniqueIndex:idx_site_email"`
	PasswordHash  string    `gorm:"not null"`
	TfaSecret     *string   // Pointer to allow NULL values
	IsSuperAdmin  bool      `gorm:"not null;default:false"`
	CreatedAt     time.Time
	UpdatedAt     time.Time

	// Relationships
	Roles []*UserRole `gorm:"foreignKey:UserID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}

// IsSuperAdmin checks if the user has a role that is not tied to a specific site (global role).
func (admin *Admin) IsSuperAdmin() bool {
	return admin.IsSuperAdmin
}

// GetSiteID returns the primary SiteID for a site-specific admin.
// It returns the SiteID of the first site-specific role found. Returns 0 if no site-specific role is found.
func (admin *Admin) GetSiteID() uint {
	return admin.SiteID
}

// HasPermission checks if the user has a specific permission through their roles.
func (admin *Admin) HasPermission(permissionName string) bool {
	return admin.IsSuperAdmin
}