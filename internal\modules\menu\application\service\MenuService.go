/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/menu/application/service/MenuService.go
 * @Description: 
 * 
 * © 2025 GACMS. All rights reserved.
 */
package service

import (
	"encoding/json"
	"fmt"
	"gacms/internal/modules/contenttype/application/service"
	"gacms/internal/modules/contenttype/domain/model"
)

// MenuItemNode represents a single node in the final menu tree structure.
type MenuItemNode struct {
	ID       uint            `json:"id"`
	Data     json.RawMessage `json:"data"`
	Children []*MenuItemNode `json:"children"`
	ParentID *uint           `json:"-"` // Internal use for tree building
}

type MenuService struct {
	itemSvc *service.ContentItemService
}

func NewMenuService(itemSvc *service.ContentItemService) *MenuService {
	return &MenuService{itemSvc: itemSvc}
}

// GetFullMenuByGroupSlug fetches all items for a specific menu group and returns them as a tree.
func (s *MenuService) GetFullMenuByGroupSlug(siteID uint, groupSlug string) ([]*MenuItemNode, error) {
	// 1. Find the menu group content item first.
	menuGroups, err := s.itemSvc.GetItemsByContentTypeSlug(siteID, "menu_group")
	if err != nil {
		return nil, fmt.Errorf("could not fetch menu groups: %w", err)
	}

	var menuGroupID uint
	for _, group := range menuGroups {
		var data struct {
			Slug string `json:"slug"`
		}
		if err := json.Unmarshal(group.Data, &data); err == nil {
			if data.Slug == groupSlug {
				menuGroupID = group.ID
				break
			}
		}
	}

	if menuGroupID == 0 {
		return nil, fmt.Errorf("menu group with slug '%s' not found for site %d", groupSlug, siteID)
	}

	// 2. Fetch all menu items for the site.
	// In a real-world scenario, we would filter by the menu_group relation directly in the query.
	// As a simplification for now, we fetch all and filter in memory.
	allItems, err := s.itemSvc.GetItemsByContentTypeSlug(siteID, "menu_item")
	if err != nil {
		return nil, fmt.Errorf("could not fetch menu items: %w", err)
	}

	// 3. Filter items belonging to the correct group and build node map.
	nodeMap := make(map[uint]*MenuItemNode)
	for _, item := range allItems {
		var data struct {
			Parent    *uint `json:"parent"`
			MenuGroup *uint `json:"menu_group"`
		}
		if err := json.Unmarshal(item.Data, &data); err != nil {
			continue // Skip malformed items
		}

		if data.MenuGroup != nil && *data.MenuGroup == menuGroupID {
			nodeMap[item.ID] = &MenuItemNode{
				ID:       item.ID,
				Data:     item.Data,
				Children: []*MenuItemNode{},
				ParentID: data.Parent,
			}
		}
	}

	// 4. Assemble the tree.
	var tree []*MenuItemNode
	for _, node := range nodeMap {
		if node.ParentID != nil && *node.ParentID != 0 {
			if parentNode, ok := nodeMap[*node.ParentID]; ok {
				parentNode.Children = append(parentNode.Children, node)
			} else {
				tree = append(tree, node) // Parent not in this group, treat as root.
			}
		} else {
			tree = append(tree, node) // A root item.
		}
	}

	// Optional: Sort tree nodes by 'order' field. This would require more json unmarshalling.

	return tree, nil
} 