/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-13
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-13
 * @FilePath: pkg/contract/router.go
 * @Description: Defines the public contract for any component that wishes to register HTTP routes.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import "github.com/gin-gonic/gin"

// Routable is the interface that any routable component (like a controller) must implement.
// This allows the core routing system to discover and register routes without depending on
// the internal implementation of any module.
type Routable interface {
	// RegisterRoutes is called by the core router to allow the component
	// to register its specific HTTP routes.
	RegisterRoutes(rg *gin.RouterGroup)
}

// RouteTarget defines the top-level group a routable component should be registered to.
type RouteTarget string

const (
	// AdminRoute targets the backend administrative API group (e.g., /admin).
	AdminRoute RouteTarget = "admin"
	// FrontendRoute targets the public-facing root group (/).
	FrontendRoute RouteTarget = "frontend"
	// ThirdPartyAPIRoute targets the third-party developer API group (/api).
	ThirdPartyAPIRoute RouteTarget = "api"
)

// RoutableRegistration holds a routable component and its target group metadata.
// This struct is used to pass routable components through the DI container.
type RoutableRegistration struct {
	Target   RouteTarget
	Routable Routable
}

// Router defines the enhanced routing interface for three-entry-point system
type Router interface {
	// RegisterRoute registers a single route for a specific entry point
	RegisterRoute(entryPoint RouteTarget, method, pattern string, handler interface{}) error

	// RegisterController registers a controller for a specific entry point
	RegisterController(entryPoint RouteTarget, controller interface{}) error

	// RegisterRoutable registers a routable component (legacy support)
	RegisterRoutable(registration *RoutableRegistration) error

	// Entry point specific registration methods
	RegisterAdminRoute(method, pattern string, handler interface{}) error
	RegisterPublicRoute(method, pattern string, handler interface{}) error
	RegisterAPIRoute(method, pattern string, handler interface{}) error

	// Batch registration methods
	RegisterAdminController(controller interface{}) error
	RegisterPublicController(controller interface{}) error
	RegisterAPIController(controller interface{}) error

	// Route discovery and introspection
	GetRoutes(entryPoint RouteTarget) []RouteInfo
	GetAllRoutes() map[RouteTarget][]RouteInfo

	// Entry point configuration
	GetEntryPointConfig() map[string]string
	SetEntryPointPath(entryPoint RouteTarget, path string) error
}

// RouteInfo provides information about a registered route
type RouteInfo struct {
	EntryPoint  RouteTarget `json:"entry_point"`
	Method      string      `json:"method"`
	Pattern     string      `json:"pattern"`
	Handler     string      `json:"handler"`
	Controller  string      `json:"controller,omitempty"`
	Action      string      `json:"action,omitempty"`
	Middleware  []string    `json:"middleware,omitempty"`
	Module      string      `json:"module,omitempty"`
}

// RouteRegistrationOptions provides options for route registration
type RouteRegistrationOptions struct {
	Middleware []string
	Module     string
	Priority   int
}