/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/LicenseValidatorRegistry.go
 * @Description: 许可证验证器注册表实现，符合公共库接口规范
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"fmt"
	"sync"

	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// DefaultLicenseValidatorRegistry 默认许可证验证器注册表
type DefaultLicenseValidatorRegistry struct {
	validators         map[string]contract.LicenseValidator
	officialValidator  contract.OfficialLicenseValidator
	thirdPartyValidators map[string]contract.ThirdPartyLicenseValidator
	mu                 sync.RWMutex
	logger             *zap.Logger
}

// DefaultLicenseValidatorRegistryParams 注册表参数
type DefaultLicenseValidatorRegistryParams struct {
	fx.In

	Logger             *zap.Logger
	OfficialValidator  contract.OfficialLicenseValidator  `optional:"true"`
	ThirdPartyValidators []contract.ThirdPartyLicenseValidator `group:"third_party_validators" optional:"true"`
}

// NewDefaultLicenseValidatorRegistry 创建默认许可证验证器注册表
func NewDefaultLicenseValidatorRegistry(params DefaultLicenseValidatorRegistryParams) contract.LicenseValidatorRegistry {
	registry := &DefaultLicenseValidatorRegistry{
		validators:           make(map[string]contract.LicenseValidator),
		thirdPartyValidators: make(map[string]contract.ThirdPartyLicenseValidator),
		logger:               params.Logger,
	}

	// 注册官方验证器
	if params.OfficialValidator != nil {
		registry.officialValidator = params.OfficialValidator
		registry.validators["official"] = params.OfficialValidator
		registry.logger.Info("Official license validator registered")
	}

	// 注册第三方验证器
	for _, validator := range params.ThirdPartyValidators {
		name := validator.GetValidatorName()
		registry.thirdPartyValidators[name] = validator
		registry.validators[name] = validator
		registry.logger.Info("Third party license validator registered", zap.String("name", name))
	}

	return registry
}

// RegisterValidator 注册验证器
func (r *DefaultLicenseValidatorRegistry) RegisterValidator(validator contract.LicenseValidator) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	name := validator.GetValidatorName()
	if _, exists := r.validators[name]; exists {
		return fmt.Errorf("validator %s already registered", name)
	}

	r.validators[name] = validator

	// 根据类型分类存储
	if officialValidator, ok := validator.(contract.OfficialLicenseValidator); ok {
		r.officialValidator = officialValidator
		r.logger.Info("Official license validator registered", zap.String("name", name))
	} else if thirdPartyValidator, ok := validator.(contract.ThirdPartyLicenseValidator); ok {
		r.thirdPartyValidators[name] = thirdPartyValidator
		r.logger.Info("Third party license validator registered", zap.String("name", name))
	}

	return nil
}

// GetValidator 获取验证器
func (r *DefaultLicenseValidatorRegistry) GetValidator(name string) (contract.LicenseValidator, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	validator, exists := r.validators[name]
	if !exists {
		return nil, fmt.Errorf("validator %s not found", name)
	}

	return validator, nil
}

// GetOfficialValidator 获取官方验证器
func (r *DefaultLicenseValidatorRegistry) GetOfficialValidator() (contract.OfficialLicenseValidator, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if r.officialValidator == nil {
		return nil, fmt.Errorf("official validator not registered")
	}

	return r.officialValidator, nil
}

// GetThirdPartyValidator 获取第三方验证器
func (r *DefaultLicenseValidatorRegistry) GetThirdPartyValidator(name string) (contract.ThirdPartyLicenseValidator, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	validator, exists := r.thirdPartyValidators[name]
	if !exists {
		return nil, fmt.Errorf("third party validator %s not found", name)
	}

	return validator, nil
}

// ListValidators 列出所有验证器
func (r *DefaultLicenseValidatorRegistry) ListValidators() []string {
	r.mu.RLock()
	defer r.mu.RUnlock()

	names := make([]string, 0, len(r.validators))
	for name := range r.validators {
		names = append(names, name)
	}

	return names
}

// ValidateModuleLicense 验证模块许可证
func (r *DefaultLicenseValidatorRegistry) ValidateModuleLicense(ctx context.Context, moduleName, licenseKey, validatorName string) (*contract.LicenseInfo, error) {
	validator, err := r.GetValidator(validatorName)
	if err != nil {
		return nil, fmt.Errorf("failed to get validator: %w", err)
	}

	// 检查验证器是否支持该模块
	supportedModules := validator.SupportedModules()
	if len(supportedModules) > 0 {
		supported := false
		for _, module := range supportedModules {
			if module == moduleName || module == "*" {
				supported = true
				break
			}
		}
		if !supported {
			return nil, fmt.Errorf("validator %s does not support module %s", validatorName, moduleName)
		}
	}

	// 执行验证
	licenseInfo, err := validator.ValidateLicense(ctx, licenseKey, moduleName)
	if err != nil {
		r.logger.Error("License validation failed",
			zap.String("module", moduleName),
			zap.String("validator", validatorName),
			zap.Error(err),
		)
		return nil, fmt.Errorf("license validation failed: %w", err)
	}

	r.logger.Debug("License validation completed",
		zap.String("module", moduleName),
		zap.String("validator", validatorName),
		zap.Bool("valid", licenseInfo.IsValid),
	)

	return licenseInfo, nil
}

// OfficialLicenseValidator 官方许可证验证器实现
type OfficialLicenseValidator struct {
	logger     *zap.Logger
	apiURL     string
	apiKey     string
	httpClient contract.HTTPClient
}

// OfficialLicenseValidatorParams 官方验证器参数
type OfficialLicenseValidatorParams struct {
	fx.In

	Logger     *zap.Logger
	HTTPClient contract.HTTPClient
	Config     *contract.LicenseConfig
}

// NewOfficialLicenseValidator 创建官方许可证验证器
func NewOfficialLicenseValidator(params OfficialLicenseValidatorParams) contract.OfficialLicenseValidator {
	return &OfficialLicenseValidator{
		logger:     params.Logger,
		apiURL:     params.Config.OfficialLicenseURL,
		apiKey:     params.Config.OfficialLicenseAPIKey,
		httpClient: params.HTTPClient,
	}
}

// ValidateLicense 验证许可证
func (v *OfficialLicenseValidator) ValidateLicense(ctx context.Context, licenseKey, moduleName string) (*contract.LicenseInfo, error) {
	// TODO: 实现官方许可证验证逻辑
	// 这里应该调用官方许可证服务API
	
	v.logger.Debug("Validating official license",
		zap.String("module", moduleName),
		zap.String("license_key", licenseKey[:min(8, len(licenseKey))]+"..."),
	)

	// 模拟验证结果
	licenseInfo := &contract.LicenseInfo{
		Key:        licenseKey,
		Type:       "official",
		ModuleName: moduleName,
		IsValid:    true, // TODO: 实际验证逻辑
	}

	return licenseInfo, nil
}

// GetValidatorName 获取验证器名称
func (v *OfficialLicenseValidator) GetValidatorName() string {
	return "official"
}

// SupportedModules 获取支持的模块列表
func (v *OfficialLicenseValidator) SupportedModules() []string {
	return []string{"*"} // 支持所有官方模块
}

// IsOnlineValidator 是否需要在线验证
func (v *OfficialLicenseValidator) IsOnlineValidator() bool {
	return true
}

// ActivateLicense 激活官方许可证
func (v *OfficialLicenseValidator) ActivateLicense(ctx context.Context, licenseKey, moduleName string) error {
	// TODO: 实现许可证激活逻辑
	v.logger.Info("Activating official license",
		zap.String("module", moduleName),
		zap.String("license_key", licenseKey[:min(8, len(licenseKey))]+"..."),
	)
	return nil
}

// DeactivateLicense 停用官方许可证
func (v *OfficialLicenseValidator) DeactivateLicense(ctx context.Context, licenseKey, moduleName string) error {
	// TODO: 实现许可证停用逻辑
	v.logger.Info("Deactivating official license",
		zap.String("module", moduleName),
		zap.String("license_key", licenseKey[:min(8, len(licenseKey))]+"..."),
	)
	return nil
}

// GetLicenseUsage 获取许可证使用情况
func (v *OfficialLicenseValidator) GetLicenseUsage(ctx context.Context, licenseKey string) (*contract.LicenseUsage, error) {
	// TODO: 实现许可证使用情况查询
	return nil, fmt.Errorf("not implemented")
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
