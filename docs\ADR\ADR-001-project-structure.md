<!--
Author: <PERSON><PERSON>eh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
-->
# ADR-001: 统一项目架构设计 (v5 - 模块化核心)

## 状态
已被 ADR-004 取代

## 上下文
在v4版本的基础上，我们进一步明确了GACMS的核心理念：**GACMS本身不是一个单一应用，而是一个强大的、由模块组成的平台。** 这意味着，所有功能，无论是系统自带的（如用户、文章管理）还是第三方开发的，都应被平等地视为"模块"。本v5版本旨在将这一理念彻底贯彻到架构的每一个角落，并明确区分"模块"与"插件"这两个不同的扩展维度。

## 决策
我们决定将v4的"微核心+模块化"架构思想进行终极提纯。平台核心（Microkernel）只负责提供最基础的服务（如模块管理、路由、数据库连接）和扩展契约。所有业务功能，无一例外，都必须在独立的模块中实现。

---

### **第一部分：后端核心架构 (Go/Gin)**

#### **核心思想**
1.  **平台即框架 (Platform as Framework)**: `internal`下的`domain`, `application`, `infrastructure`, `port`目录共同构成了平台微核心，它们不包含任何具体业务逻辑，只提供模块运行所需的环境和标准。
2.  **万物皆模块 (Everything is a Module)**: 所有业务功能，包括用户管理、内容管理等，都以模块的形式存在于`internal/modules/`目录下。官方模块和第三方模块在架构上地位平等。
3.  **模块内聚 (Module Cohesion)**: 每个模块自身都是一个完整、独立的"迷你应用"，其内部也遵循`domain`, `application`, `infrastructure`, `port`的整洁架构分层，实现了高度的内聚和自治。
4.  **物理隔离**: 模块之间默认是隔离的，只能通过平台核心提供的契约进行有限、安全的交互，或通过显式API调用。

#### **后端目录结构**

```
GACMS/
├── cmd/gacms/Main.go          // 应用入口, 模块加载器和依赖注入中心
├── configs/
├── docs/ADR/ADR-001-UnifiedArchitecture.md
├── internal/
│   ├── domain/                  // 核心平台领域 (只包含模块化和系统级契约)
│   │   └── contract/            // e.g., Module.go, CoreServices.go, Plugin.go
│   ├── application/             // 核心平台应用
│   │   └── service/             // e.g., ModuleManager.go, PluginManager.go
│   ├── infrastructure/          // 共享基础设施 (DB, Cache, Log)
│   ├── port/                    // 核心平台端口
│   │   └── http/
│   │       └── Router.go        // 模块路由注册中心
│   └── modules/                 // 所有官方核心功能模块
│       └── user/                // "用户"模块 (官方标准模块示例)
│           ├── port/            // 模块的API端口
│           │   └── http/
│           │       └── controller/ // UserController.go
│           ├── application/     // 模块的应用层
│           │   ├── service/     // UserService.go
│           │   └── dto/
│           ├── domain/          // 模块的领域层
│           │   ├── model/       // User.go
│           │   └── contract/    // UserRepository.go
│           ├── infrastructure/  // 模块的基础设施实现
│           │   └── persistence/ // UserRepositoryImpl.go
│           └── module.go        // 实现 Module 核心契约
├── pkg/                         // 公共库
├── vendors/                     // 第三方扩展根目录 (按供应商组织)
│   └── gacms-community/         // 示例：GACMS社区供应商
│       ├── modules/             // 第三方模块
│       │   └── forum/
│       └── plugins/             // 第三方插件
│           └── seo-optimizer/
// ... (scripts, static, uploads) ...
```

---

### **第二部分：前端核心架构 (React)**

#### **核心思想**
采用**功能优先 (Feature-First)** 的目录结构，与后端领域划分（如Admin, Member, Post）保持一致，实现高度内聚。同时借鉴后端分层思想，使架构清晰。

#### **前端目录结构 (`web/admin/` 或 `web/frontend/`)**
```
src/
├── application/       // 应用级 (路由, 全局状态, 布局)
│   ├── router/
│   ├── store/         // 全局状态 (Redux)
│   └── ...
├── domain/            // 领域/功能模块
│   ├── admin/         // 后台管理员模块
│   │   ├── pages/       // 页面 (e.g., AdminListPage)
│   │   ├── components/  // 模块内可复用组件
│   │   └── service.js
│   ├── member/        // 前台会员模块
│   └── ...
├── infrastructure/    // 基础设施 (通用能力)
│   ├── api/           // API客户端
│   ├── components/    // 通用UI组件 (e.g., Button)
│   └── hooks/         // 通用Hooks
└── index.js           // 应用入口
```

---

### **第三部分：扩展与模块化**

#### **1. 模块化开发指南 (以"用户User"模块为例)**
开发任何功能都等同于开发一个新模块。以系统自带的`user`模块为例，其开发流程如下：
1.  **创建模块目录**: 在`internal/modules/`下创建`user/`目录。
2.  **定义模块内部各层**: 在`user/`内部分别创建`domain`, `application`, `infrastructure`, `port`目录。
    *   `domain`: 定义`model/User.go`和`contract/UserRepository.go`。
    *   `infrastructure`: 在`persistence/`中实现`UserRepository.go`接口。
    *   `application`: 创建`service/UserService.go`来编排业务逻辑。
    *   `port`: 创建`http/controller/UserController.go`来暴露API。
3.  **实现模块契约**: 创建`user/module.go`文件，在其中实现平台核心定义的`domain/contract/Module.go`接口。这个文件是模块的"心脏"，负责：
    *   **依赖注入**: 组装模块内部的`repository`, `service`, `controller`。
    *   **路由注册**: 调用`CoreServices`提供的`Router`，将`UserController`中的API路由注册到系统中。
4.  **注册模块**: 在`cmd/gacms/Main.go`中，将新建的`user`模块实例添加到`ModuleManager`中。之后，平台的启动过程会自动完成该模块的初始化和路由注册。

#### **2. 平台化扩展系统：模块 vs. 插件**
我们严格区分两种扩展机制，以应对不同的场景：

*   **模块 (Module)**:
    *   **定义**: 独立的、自包含的功能单元，用于向GACMS添加**全新的业务能力**（如论坛、商城、投票等）。
    *   **特征**: 重量级、业务完整、拥有自己的数据模型和API。
    *   **管理**: 由`ModuleManager`在系统启动时加载、初始化和集成。
    *   **存放路径**:
        *   **官方模块**: `internal/modules/`
        *   **第三方模块**: `vendors/{vendor-name}/modules/`

*   **插件 (Plugin/Hook)**:
    *   **定义**: 轻量级的代码片段，用于在**不修改模块源码**的情况下，对现有模块的**行为进行微调或扩展**（如在用户注册后发送一封邮件、修改文章保存前的标题）。
    *   **特征**: 轻量级、无独立业务、依附于特定模块或核心事件的"钩子"（Hook）。
    *   **管理**: 由`PluginManager`动态管理，可以在运行时启用或禁用。
    *   **存放路径**:
        *   **官方插件**: (待定，可放在 `internal/plugins/`)
        *   **第三方插件**: `vendors/{vendor-name}/plugins/`
    *   **状态**: 插件系统的具体设计将在独立的`ADR-002-Plugin-System.md`中详细阐述。

这种双轨制扩展体系，使得GACMS既能通过模块实现强大的功能扩展，又能通过插件保持灵活性和可定制性。

---

## 后果
*   **积极**: 架构模型在逻辑上完全自洽，真正实现了"万物皆模块"。为官方功能和第三方功能的开发提供了统一、平等的范式，并通过`vendors`目录实现了清晰的物理隔离，极大提升了项目的概念完整性和长期可维护性。
*   **消极**: 对模块内聚性的要求极高，模块间通信必须通过规范的API或核心事件进行，禁止直接跨模块调用内部代码。
*   **中性**: 前期架构搭建和模块化拆分工作量较大，但一旦完成，后续业务迭代和功能扩展的效率将呈指数级提升。

## 实施注意事项
*   **模块间通信**: 严禁模块之间直接`import`对方的内部包。模块间通信应通过平台核心提供的事件总线（未来规划）或显式的API调用。
*   **核心API**: `internal/port/http/Router.go`是所有模块路由的根注册点。
*   **版权头部**: 所有`.go`和`.js/.ts`文件都必须添加规则中指定的版权和文件信息注释头。
*   **ADR-002**: 尽快启动`ADR-002-Plugin-System.md`的编写工作。

## 参考资料
- Go项目标准布局: https://github.com/golang-standards/project-layout
- RESTful API最佳实践
- Gin框架文档: https://gin-gonic.com/docs/
- React应用架构最佳实践 