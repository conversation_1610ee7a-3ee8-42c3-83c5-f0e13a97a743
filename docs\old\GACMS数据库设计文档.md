本文档详细描述GACMS系统的数据库结构、表间关系、索引设计及优化策略。

### 1.1 核心数据表结构

以下是GACMS核心功能模块所依赖的数据表结构详情。

#### 1.1.1 用户与权限相关表

##### 1.1.1.1 `users` 表 (用户主表)

存储系统用户的基本认证信息和状态。

| 字段名        | 数据类型          | 约束/默认值                      | 索引        | 注释                                     |
|---------------|-------------------|----------------------------------|-------------|------------------------------------------|
| `id`          | `INT UNSIGNED`    | `PRIMARY KEY`, `AUTO_INCREMENT`  | 主键        | 用户唯一标识符                             |
| `username`    | `VARCHAR(100)`    | `NOT NULL`, `UNIQUE`             | 唯一索引    | 用户名，用于登录，全局唯一                 |
| `email`       | `VARCHAR(255)`    | `NOT NULL`, `UNIQUE`             | 唯一索引    | 电子邮箱，用于登录或找回密码，全局唯一     |
| `password_hash`| `VARCHAR(255)`   | `NOT NULL`                       |             | 加密后的用户密码                           |
| `status`      | `TINYINT UNSIGNED`| `NOT NULL`, `DEFAULT 1`          | 普通索引    | 用户状态 (例如: 0=禁用, 1=激活, 2=待验证) |
| `email_verified_at` | `TIMESTAMP` | `NULLABLE`                       |             | 邮箱验证时间，NULL表示未验证             |
| `remember_token` | `VARCHAR(100)` | `NULLABLE`                       |             | "记住我"功能的令牌                       |
| `totp_secret` | `VARCHAR(255)`    | `NULLABLE`                       |             | TOTP密钥 (加密存储)                      |
| `totp_enabled_at`| `TIMESTAMP`    | `NULLABLE`                       |             | TOTP启用时间，NULL表示未启用             |
| `totp_backup_codes`| `TEXT`        | `NULLABLE`                       |             | TOTP备用码 (哈希或加密存储)              |
| `created_at`  | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` |             | 记录创建时间                             |
| `updated_at`  | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                             |
| `deleted_at`  | `TIMESTAMP`       | `NULLABLE`                       | 普通索引    | 软删除时间，NULL表示未删除               |

**说明:**
- `status` 字段的具体值含义应在代码或枚举类中统一定义。
- 密码存储应使用强哈希算法（如 Argon2, bcrypt）。

##### 1.1.1.2 `user_profiles` 表 (用户资料扩展表)

存储用户的详细个人资料信息，与 `users` 表一对一关联。

| 字段名        | 数据类型          | 约束/默认值                      | 索引        | 注释                                     |
|---------------|-------------------|----------------------------------|-------------|------------------------------------------|
| `user_id`     | `INT UNSIGNED`    | `PRIMARY KEY`                    | 主键/外键   | 关联 `users` 表的 `id`，确保一对一关系   |
| `full_name`   | `VARCHAR(150)`    | `NULLABLE`                       |             | 用户真实姓名或昵称                         |
| `avatar_url`  | `VARCHAR(255)`    | `NULLABLE`                       |             | 用户头像图片的URL或存储路径                |
| `bio`         | `TEXT`            | `NULLABLE`                       |             | 个人简介                                 |
| `gender`      | `TINYINT UNSIGNED`| `NULLABLE`                       |             | 性别 (例如: 0=未知, 1=男, 2=女)          |
| `birth_date`  | `DATE`            | `NULLABLE`                       |             | 出生日期                                 |
| `phone_number`| `VARCHAR(20)`     | `NULLABLE`, `UNIQUE`             | 唯一索引    | 手机号码，可选唯一                       |
| `address`     | `VARCHAR(255)`    | `NULLABLE`                       |             | 详细地址                                 |
| `unit`        | `VARCHAR(255)`    | `NULLABLE`                       |             | 所属单位/公司                            |
| `city`        | `VARCHAR(100)`    | `NULLABLE`                       |             | 所在城市                                 |
| `country`     | `VARCHAR(100)`    | `NULLABLE`                       |             | 所在国家                                 |
| `preferences` | `JSON`            | `NULLABLE`                       |             | 用户个性化偏好设置 (例如: 主题, 通知设置) |
| `created_at`  | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` |             | 记录创建时间                             |
| `updated_at`  | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                             |

**外键约束:**
- `user_profiles.user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

##### 1.1.1.3 `roles` 表 (角色表)

定义系统中的用户角色。

| 字段名        | 数据类型          | 约束/默认值                      | 索引        | 注释                                     |
|---------------|-------------------|----------------------------------|-------------|------------------------------------------|
| `id`          | `INT UNSIGNED`    | `PRIMARY KEY`, `AUTO_INCREMENT`  | 主键        | 角色唯一标识符                             |
| `name`        | `VARCHAR(100)`    | `NOT NULL`, `UNIQUE`             | 唯一索引    | 角色名称 (例如: "administrator", "editor", "viewer") |
| `slug`        | `VARCHAR(120)`    | `NOT NULL`, `UNIQUE`             | 唯一索引    | 角色的唯一标识符 (程序中使用，例如 "admin", "content-manager") |
| `description` | `VARCHAR(255)`    | `NULLABLE`                       |             | 角色描述                                 |
| `level`       | `INT UNSIGNED`    | `NOT NULL`, `DEFAULT 0`          | 普通索引    | 角色级别，用于权限比较或排序，数字越大级别越高 |
| `is_system`   | `BOOLEAN`         | `NOT NULL`, `DEFAULT FALSE`      |             | 是否为系统内置角色 (通常不允许删除)        |
| `created_at`  | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` |             | 记录创建时间                             |
| `updated_at`  | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                             |

**示例角色:**
- `name`: "超级管理员", `slug`: "super-admin", `level`: 999, `is_system`: true
- `name`: "内容编辑", `slug`: "editor", `level`: 100
- `name`: "注册用户", `slug`: "member", `level`: 10

##### ******* `permissions` 表 (权限表)

定义系统中的具体操作权限。

| 字段名        | 数据类型          | 约束/默认值                      | 索引        | 注释                                     |
|---------------|-------------------|----------------------------------|-------------|------------------------------------------|
| `id`          | `INT UNSIGNED`    | `PRIMARY KEY`, `AUTO_INCREMENT`  | 主键        | 权限唯一标识符                             |
| `name`        | `VARCHAR(100)`    | `NOT NULL`, `UNIQUE`             | 唯一索引    | 权限名称 (例如: "创建文章", "编辑用户", "访问后台") |
| `slug`        | `VARCHAR(120)`    | `NOT NULL`, `UNIQUE`             | 唯一索引    | 权限的唯一标识符 (程序中使用，例如 "content.create", "user.edit", "admin.access") |
| `description` | `VARCHAR(255)`    | `NULLABLE`                       |             | 权限描述                                 |
| `group_name`  | `VARCHAR(100)`    | `NULLABLE`                       | 普通索引    | 权限分组名 (例如: "内容管理", "用户管理")，便于后台展示和管理 |
| `created_at`  | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` |             | 记录创建时间                             |
| `updated_at`  | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                             |

**示例权限:**
- `name`: "创建内容", `slug`: "content.create", `group_name`: "内容管理"
- `name`: "编辑任意内容", `slug`: "content.edit.all", `group_name`: "内容管理"
- `name`: "删除用户", `slug`: "user.delete", `group_name`: "用户管理"

##### ******* `user_roles` 表 (用户角色关联表)

存储用户和角色的多对多关系。

| 字段名        | 数据类型          | 约束/默认值                      | 索引        | 注释                                     |
|---------------|-------------------|----------------------------------|-------------|------------------------------------------|
| `user_id`     | `INT UNSIGNED`    | `NOT NULL`                       | 主键/外键   | 关联 `users` 表的 `id`                   |
| `role_id`     | `INT UNSIGNED`    | `NOT NULL`                       | 主键/外键   | 关联 `roles` 表的 `id`                   |
| `created_at`  | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` |             | 记录创建时间                             |

**主键约束:**
- PRIMARY KEY (`user_id`, `role_id`)

**外键约束:**
- `user_roles.user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
- `user_roles.role_id` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

##### 1.1.1.6 `role_permissions` 表 (角色权限关联表)

存储角色和权限的多对多关系。

| 字段名        | 数据类型          | 约束/默认值                      | 索引        | 注释                                     |
|---------------|-------------------|----------------------------------|-------------|------------------------------------------|
| `role_id`     | `INT UNSIGNED`    | `NOT NULL`                       | 主键/外键   | 关联 `roles` 表的 `id`                   |
| `permission_id`| `INT UNSIGNED`   | `NOT NULL`                       | 主键/外键   | 关联 `permissions` 表的 `id`             |
| `created_at`  | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` |             | 记录创建时间                             |

**主键约束:**
- PRIMARY KEY (`role_id`, `permission_id`)

**外键约束:**
- `role_permissions.role_id` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
- `role_permissions.permission_id` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

#### 1.1.2 内容管理相关表

##### 1.1.2.1 `categories` 表 (栏目/分类表)

存储内容的分类信息，支持无限级分类。

| 字段名          | 数据类型          | 约束/默认值                      | 索引        | 注释                                                           |
|-----------------|-------------------|----------------------------------|-------------|----------------------------------------------------------------|
| `id`            | `INT UNSIGNED`    | `PRIMARY KEY`, `AUTO_INCREMENT`  | 主键        | 栏目唯一标识符                                                   |
| `parent_id`     | `INT UNSIGNED`    | `NULLABLE`, `DEFAULT NULL`       | 普通索引    | 父栏目ID，NULL表示顶级栏目                                       |
| `name`          | `VARCHAR(150)`    | `NOT NULL`                       |             | 栏目名称 (例如: "新闻中心", "产品介绍")                          |
| `slug`          | `VARCHAR(180)`    | `NOT NULL`, `UNIQUE`             | 唯一索引    | 栏目的唯一URL友好标识符 (例如: "news", "products")             |
| `description`   | `TEXT`            | `NULLABLE`                       |             | 栏目描述                                                       |
| `image_url`     | `VARCHAR(255)`    | `NULLABLE`                       |             | 栏目特色图片URL或存储路径                                        |
| `type`          | `VARCHAR(50)`     | `NOT NULL`, `DEFAULT 'category'` | 普通索引    | 栏目类型 (例如: 'category' 普通栏目, 'topic' 专题聚合)           |
| `sort_order`    | `INT UNSIGNED`    | `NOT NULL`, `DEFAULT 0`          | 普通索引    | 显示排序，数字越小越靠前                                         |
| `is_nav`        | `BOOLEAN`         | `NOT NULL`, `DEFAULT TRUE`       |             | 是否在导航中显示                                                 |
| `is_visible`    | `BOOLEAN`         | `NOT NULL`, `DEFAULT TRUE`       |             | 是否在前台可见                                                   |
| `meta_title`    | `VARCHAR(255)`    | `NULLABLE`                       |             | SEO: Meta标题                                                  |
| `meta_keywords` | `VARCHAR(255)`    | `NULLABLE`                       |             | SEO: Meta关键词                                                |
| `meta_description`| `TEXT`          | `NULLABLE`                       |             | SEO: Meta描述                                                  |
| `subdomain`     | `VARCHAR(100)`    | `NULLABLE`, `UNIQUE`             | 唯一索引    | 绑定的二级域名 (例如: "news" 对应 news.yourdomain.com)         |
| `template_list` | `VARCHAR(100)`    | `NULLABLE`                       |             | 列表页模板文件 (例如: "category_news.php")                     |
| `template_show` | `VARCHAR(100)`    | `NULLABLE`                       |             | 内容详情页模板文件 (例如: "article_news.php")                  |
| `created_at`    | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` |             | 记录创建时间                                                   |
| `updated_at`    | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                                                   |
| `deleted_at`    | `TIMESTAMP`       | `NULLABLE`                       | 普通索引    | 软删除时间                                                     |

**外键约束:**
- `categories.parent_id` FOREIGN KEY (`parent_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE; (允许父栏目被删除时，子栏目变为顶级)

**说明:**
- `slug` 应确保在同一父栏目下唯一，或者全局唯一，具体取决于业务需求。当前设计为全局唯一。
- `type` 字段可以用于区分普通分类和专题入口等。
- `subdomain` 字段用于实现栏目/专题绑定二级域名的功能。

##### 1.1.2.2 `contents` 表 (内容主表)

存储网站发布的主要内容，如文章、产品、新闻等。

| 字段名             | 数据类型          | 约束/默认值                      | 索引        | 注释                                                                 |
|--------------------|-------------------|----------------------------------|-------------|----------------------------------------------------------------------|
| `id`               | `INT UNSIGNED`    | `PRIMARY KEY`, `AUTO_INCREMENT`  | 主键        | 内容唯一标识符                                                         |
| `category_id`      | `INT UNSIGNED`    | `NOT NULL`                       | 外键/普通索引| 主分类ID，关联 `categories` 表的 `id`                                  |
| `user_id`          | `INT UNSIGNED`    | `NOT NULL`                       | 外键/普通索引| 作者ID，关联 `users` 表的 `id`                                         |
| `title`            | `VARCHAR(255)`    | `NOT NULL`                       |             | 内容标题                                                               |
| `slug`             | `VARCHAR(255)`    | `NOT NULL`, `UNIQUE`             | 唯一索引    | 内容的唯一URL友好标识符 (例如: "my-first-article")                     |
| `summary`          | `TEXT`            | `NULLABLE`                       |             | 内容摘要或简介                                                         |
| `body`             | `LONGTEXT`        | `NULLABLE`                       |             | 内容主体，可以是HTML、Markdown或其他格式                               |
| `body_type`        | `VARCHAR(20)`     | `NOT NULL`, `DEFAULT 'html'`     |             | 内容主体类型 (例如: 'html', 'markdown', 'json')                        |
| `thumbnail_url`    | `VARCHAR(255)`    | `NULLABLE`                       |             | 缩略图/特色图片URL或存储路径                                           |
| `status`           | `TINYINT UNSIGNED`     | `NOT NULL`, `DEFAULT '0'`    | 普通索引    | 内容状态 (例如: 0： 草稿, 1：待审核，2：已发布, 3：已驳回, 4：定时发布) |
| `visibility`       | `VARCHAR(20)`     | `NOT NULL`, `DEFAULT 'public'`   | 普通索引    | 可见性 (例如: 'public' 公开, 'private' 私有, 'password' 密码保护)      |
| `password`         | `VARCHAR(255)`    | `NULLABLE`                       |             | 当 `visibility` 为 'password' 时设置的密码 (应加密存储)                |
| `allow_comments`   | `BOOLEAN`         | `NOT NULL`, `DEFAULT TRUE`       |             | 是否允许评论                                                           |
| `views_count`      | `INT UNSIGNED`    | `NOT NULL`, `DEFAULT 0`          |             | 查看次数                                                               |
| `likes_count`      | `INT UNSIGNED`    | `NOT NULL`, `DEFAULT 0`          |             | 点赞次数                                                               |
| `published_at`     | `TIMESTAMP`       | `NULLABLE`                       | 普通索引    | 发布时间，NULL表示未发布或草稿                                         |
| `scheduled_at`     | `TIMESTAMP`       | `NULLABLE`                       | 普通索引    | 定时发布时间                                                           |
| `meta_title`       | `VARCHAR(255)`    | `NULLABLE`                       |             | SEO: Meta标题                                                          |
| `meta_keywords`    | `VARCHAR(255)`    | `NULLABLE`                       |             | SEO: Meta关键词                                                        |
| `meta_description` | `TEXT`            | `NULLABLE`                       |             | SEO: Meta描述                                                          |
| `is_sticky`        | `BOOLEAN`         | `NOT NULL`, `DEFAULT FALSE`      |             | 是否置顶                                                               |
| `is_featured`      | `BOOLEAN`         | `NOT NULL`, `DEFAULT FALSE`      |             | 是否推荐/精选                                                          |
| `sort_order`       | `INT UNSIGNED`    | `NOT NULL`, `DEFAULT 0`          | 普通索引    | 自定义排序值，数字越小越靠前 (在特定列表或置顶时使用)                  |
| `source_url`       | `VARCHAR(255)`    | `NULLABLE`                       |             | 内容来源URL (如果是转载或爬取)                                         |
| `custom_fields`    | `JSON`            | `NULLABLE`                       |             | 自定义字段，用于存储特定内容类型的额外数据                             |
| `created_at`       | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` |             | 记录创建时间                                                           |
| `updated_at`       | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                                                           |
| `deleted_at`       | `TIMESTAMP`       | `NULLABLE`                       | 普通索引    | 软删除时间                                                             |

**外键约束:**
- `contents.category_id` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE; (不允许删除仍有内容的分类)
- `contents.user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE; (不允许删除仍有内容的用户，或考虑设置为 SET NULL)

**说明:**
- `slug` 应确保全局唯一，以保证URL的唯一性。
- `status` 和 `visibility` 字段共同控制内容的访问和展示。
- `body_type` 用于指示 `body` 字段内容的格式，方便前端渲染。
- `custom_fields` 可以灵活扩展不同内容模型所需的特定字段。

##### 1.1.2.3 `tags` 表 (标签表)

存储内容的标签信息，用于内容的分类和关联。

| 字段名        | 数据类型          | 约束/默认值                      | 索引        | 注释                                     |
|---------------|-------------------|----------------------------------|-------------|------------------------------------------|
| `id`          | `INT UNSIGNED`    | `PRIMARY KEY`, `AUTO_INCREMENT`  | 主键        | 标签唯一标识符                             |
| `name`        | `VARCHAR(100)`    | `NOT NULL`, `UNIQUE`             | 唯一索引    | 标签名称 (例如: "CodeIgniter 4", "CMS", "PHP") |
| `slug`        | `VARCHAR(120)`    | `NOT NULL`, `UNIQUE`             | 唯一索引    | 标签的唯一URL友好标识符 (例如: "codeigniter-4", "cms") |
| `description` | `VARCHAR(255)`    | `NULLABLE`                       |             | 标签描述 (可选)                            |
| `usage_count` | `INT UNSIGNED`    | `NOT NULL`, `DEFAULT 0`          | 普通索引    | 该标签被使用的次数，便于统计和排序         |
| `created_at`  | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` |             | 记录创建时间                             |
| `updated_at`  | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                             |

**说明:**
- `slug` 应确保全局唯一。
- `usage_count` 可以在添加或移除内容与标签的关联时通过触发器或应用逻辑更新。

##### ******* `content_tags` 表 (内容标签关联表)

存储内容和标签的多对多关系。

| 字段名        | 数据类型          | 约束/默认值                      | 索引        | 注释                                     |
|---------------|-------------------|----------------------------------|-------------|------------------------------------------|
| `content_id`  | `INT UNSIGNED`    | `NOT NULL`                       | 主键/外键   | 关联 `contents` 表的 `id`                |
| `tag_id`      | `INT UNSIGNED`    | `NOT NULL`                       | 主键/外键   | 关联 `tags` 表的 `id`                    |
| `content_type` | `VARCHAR(50)`     | `NOT NULL`, `DEFAULT 'content'`  | 普通索引    | 关联内容类型 (例如: 'article', 'product') |
| `created_at`  | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` |             | 记录创建时间                             |

**主键约束:**
- PRIMARY KEY (`content_id`, `tag_id`)

**外键约束:**
- `content_tags.content_id` FOREIGN KEY (`content_id`) REFERENCES `contents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
- `content_tags.tag_id` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

##### 1.1.2.5 `topics` 表 (专题表)

存储专题页面的信息，专题可以聚合多个栏目或特定内容。

| 字段名             | 数据类型          | 约束/默认值                      | 索引        | 注释                                                                 |
|--------------------|-------------------|----------------------------------|-------------|----------------------------------------------------------------------|
| `id`               | `INT UNSIGNED`    | `PRIMARY KEY`, `AUTO_INCREMENT`  | 主键        | 专题唯一标识符                                                         |
| `title`            | `VARCHAR(255)`    | `NOT NULL`                       |             | 专题标题                                                               |
| `slug`             | `VARCHAR(255)`    | `NOT NULL`, `UNIQUE`             | 唯一索引    | 专题的唯一URL友好标识符 (例如: "annual-report-2024")                 |
| `description`      | `TEXT`            | `NULLABLE`                       |             | 专题描述                                                               |
| `cover_image_url`  | `VARCHAR(255)`    | `NULLABLE`                       |             | 专题封面图片URL或存储路径                                              |
| `layout_template`  | `VARCHAR(100)`    | `NULLABLE`                       |             | 专题页面布局模板文件 (例如: "topic_default.php")                     |
| `status`           | `VARCHAR(20)`     | `NOT NULL`, `DEFAULT 'draft'`    | 普通索引    | 专题状态 (例如: 'draft' 草稿, 'published' 已发布, 'archived' 归档)   |
| `is_featured`      | `BOOLEAN`         | `NOT NULL`, `DEFAULT FALSE`      |             | 是否推荐专题                                                           |
| `subdomain`        | `VARCHAR(100)`    | `NULLABLE`, `UNIQUE`             | 唯一索引    | 绑定的二级域名 (例如: "report2024" 对应 report2024.yourdomain.com) |
| `meta_title`       | `VARCHAR(255)`    | `NULLABLE`                       |             | SEO: Meta标题                                                          |
| `meta_keywords`    | `VARCHAR(255)`    | `NULLABLE`                       |             | SEO: Meta关键词                                                        |
| `meta_description` | `TEXT`            | `NULLABLE`                       |             | SEO: Meta描述                                                          |
| `published_at`     | `TIMESTAMP`       | `NULLABLE`                       | 普通索引    | 发布时间                                                               |
| `created_at`       | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` |             | 记录创建时间                                                           |
| `updated_at`       | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                                                           |
| `deleted_at`       | `TIMESTAMP`       | `NULLABLE`                       | 普通索引    | 软删除时间                                                             |

**说明:**
- `slug` 应确保全局唯一。
- `subdomain` 字段用于实现专题绑定二级域名的功能。
- `layout_template` 允许为不同专题指定不同的展示模板。

##### 1.1.2.6 `content_topics` 表 (内容专题关联表)

存储内容和专题的多对多关系。一个内容可以属于多个专题，一个专题可以包含多个内容。

| 字段名        | 数据类型          | 约束/默认值                      | 索引        | 注释                                     |
|---------------|-------------------|----------------------------------|-------------|------------------------------------------|
| `content_id`  | `INT UNSIGNED`    | `NOT NULL`                       | 主键/外键   | 关联 `contents` 表的 `id`                |
| `topic_id`    | `INT UNSIGNED`    | `NOT NULL`                       | 主键/外键   | 关联 `topics` 表的 `id`                  |
| `sort_order`  | `INT UNSIGNED`    | `NOT NULL`, `DEFAULT 0`          |             | 内容在专题内的排序，数字越小越靠前         |
| `created_at`  | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` |             | 记录创建时间                             |

**主键约束:**
- PRIMARY KEY (`content_id`, `topic_id`)

**外键约束:**
- `content_topics.content_id` FOREIGN KEY (`content_id`) REFERENCES `contents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
- `content_topics.topic_id` FOREIGN KEY (`topic_id`) REFERENCES `topics` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

##### 1.1.2.7 `uploads` 表 (上传文件表)

存储所有上传文件的元数据信息。

| 字段名             | 数据类型          | 约束/默认值                      | 索引        | 注释                                                                 |
|--------------------|-------------------|----------------------------------|-------------|----------------------------------------------------------------------|
| `id`               | `INT UNSIGNED`    | `PRIMARY KEY`, `AUTO_INCREMENT`  | 主键        | 上传文件唯一标识符                                                     |
| `user_id`          | `INT UNSIGNED`    | `NULLABLE`                       | 外键        | 上传用户ID (关联 `users.id`), NULL表示系统或匿名上传                   |
| `original_filename`| `VARCHAR(255)`    | `NOT NULL`                       |             | 用户上传时的原始文件名                                                 |
| `stored_filename`  | `VARCHAR(255)`    | `NOT NULL`                       |             | 服务器上存储的文件名 (在 `relative_path` 目录内应唯一)                 |
| `relative_path`    | `VARCHAR(512)`    | `NOT NULL`                       |             | 文件相对于主上传目录的路径 (例如: `images/avatars/2024/05/`, 必须以 `/` 结尾) |
| `mime_type`        | `VARCHAR(100)`    | `NOT NULL`                       | 普通索引    | 文件的MIME类型 (例如: `image/jpeg`, `application/pdf`)               |
| `extension`        | `VARCHAR(20)`     | `NOT NULL`                       | 普通索引    | 文件扩展名 (例如: `jpg`, `pdf`)                                      |
| `size`             | `BIGINT UNSIGNED` | `NOT NULL`                       |             | 文件大小 (字节)                                                        |
| `disk`             | `VARCHAR(50)`     | `NOT NULL`, `DEFAULT 'local'`    | 普通索引    | 存储驱动/磁盘 (例如: 'local', 's3', 'oss')                           |
| `is_image`         | `BOOLEAN`         | `NOT NULL`, `DEFAULT FALSE`      |             | 是否为图片文件                                                         |
| `image_width`      | `INT UNSIGNED`    | `NULLABLE`                       |             | 图片宽度 (像素), 仅当 `is_image` 为 true 时有效                      |
| `image_height`     | `INT UNSIGNED`    | `NULLABLE`                       |             | 图片高度 (像素), 仅当 `is_image` 为 true 时有效                      |
| `alt_text`         | `VARCHAR(255)`    | `NULLABLE`                       |             | 图片的ALT文本 (用于SEO和可访问性)                                    |
| `title`            | `VARCHAR(255)`    | `NULLABLE`                       |             | 文件标题 (可用于显示或搜索)                                          |
| `description`      | `TEXT`            | `NULLABLE`                       |             | 文件描述                                                               |
| `uploader_ip`      | `VARCHAR(45)`     | `NULLABLE`                       |             | 上传者IP地址                                                           |
| `created_at`       | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` |             | 记录创建时间                                                           |
| `updated_at`       | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                                                           |
| `deleted_at`       | `TIMESTAMP`       | `NULLABLE`                       | 普通索引    | 软删除时间戳                                                           |

**唯一约束:**
- UNIQUE KEY `uk_relative_path_stored_filename` (`relative_path`, `stored_filename`) - 确保在同一目录下文件名唯一。

**外键约束:**
- CONSTRAINT `fk_uploads_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

**说明:**
- `relative_path` 结合 `stored_filename` 构成了文件在服务器上的相对存储位置。例如，如果基础上传目录是 `public/uploads/`，`relative_path` 是 `images/articles/`，`stored_filename` 是 `my-image.jpg`，则文件实际位于 `public/uploads/images/articles/my-image.jpg`。
- `disk` 字段为未来支持多种存储后端（如云存储）预留。
- 软删除 (`deleted_at`) 允许恢复文件记录，实际文件可能需要额外的清理机制。

##### 1.1.2.8 `comments` 表 (评论表)

存储用户对内容的评论信息，支持嵌套评论和审核机制。

| 字段名             | 数据类型          | 约束/默认值                      | 索引        | 注释                                                                 |
|--------------------|-------------------|----------------------------------|-------------|----------------------------------------------------------------------|
| `id`               | `INT UNSIGNED`    | `PRIMARY KEY`, `AUTO_INCREMENT`  | 主键        | 评论唯一标识符                                                         |
| `content_id`       | `INT UNSIGNED`    | `NOT NULL`                       | 外键        | 关联的内容ID (例如: `articles.id`, `pages.id`)                       |
| `content_type`     | `VARCHAR(50)`     | `NOT NULL`                       | 普通索引    | 关联的内容类型 (例如: 'article', 'page', 'product')                  |
| `parent_id`        | `INT UNSIGNED`    | `NULLABLE`, `DEFAULT NULL`       | 外键        | 父评论ID (用于嵌套评论, 关联 `comments.id`)                          |
| `user_id`          | `INT UNSIGNED`    | `NULLABLE`                       | 外键        | 发表评论的用户ID (关联 `users.id`), NULL表示游客评论                 |
| `author_name`      | `VARCHAR(100)`    | `NULLABLE`                       |             | 评论者名称 (游客评论时填写)                                          |
| `author_email`     | `VARCHAR(100)`    | `NULLABLE`                       |             | 评论者邮箱 (游客评论时填写, 可考虑不公开显示)                        |
| `author_website`   | `VARCHAR(255)`    | `NULLABLE`                       |             | 评论者网址 (游客评论时填写)                                          |
| `comment_content`  | `TEXT`            | `NOT NULL`                       |             | 评论内容                                                               |
| `status`           | `VARCHAR(20)`     | `NOT NULL`, `DEFAULT 'pending'`  | 普通索引    | 评论状态 (例如: 'pending', 'approved', 'spam', 'trash')              |
| `ip_address`       | `VARCHAR(45)`     | `NULLABLE`                       |             | 评论者IP地址                                                           |
| `user_agent`       | `TEXT`            | `NULLABLE`                       |             | 评论者浏览器User Agent                                                 |
| `likes_count`      | `INT UNSIGNED`    | `NOT NULL`, `DEFAULT 0`          |             | 点赞数                                                                 |
| `dislikes_count`   | `INT UNSIGNED`    | `NOT NULL`, `DEFAULT 0`          |             | 反对数                                                                 |
| `created_at`       | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` |             | 记录创建时间                                                           |
| `updated_at`       | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                                                           |
| `approved_at`      | `TIMESTAMP`       | `NULLABLE`                       |             | 评论审核通过时间                                                       |
| `approved_by`      | `INT UNSIGNED`    | `NULLABLE`                       | 外键        | 审核评论的管理员ID (关联 `users.id`)                                 |

**索引:**
- KEY `idx_content_type_content_id` (`content_type`, `content_id`)
- KEY `idx_status` (`status`)

**外键约束:**
- CONSTRAINT `fk_comments_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `comments` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
- CONSTRAINT `fk_comments_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
- CONSTRAINT `fk_comments_approved_by` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
- 注意: `content_id` 无法直接设置外键，因为它依赖于 `content_type` 来确定关联的表。这需要在应用层面进行逻辑关联和数据完整性校验。

**说明:**
- `content_id` 和 `content_type` 结合使用来确定评论所属的具体内容项。
- `parent_id` 用于实现评论的层级关系（回复）。
- 游客评论时，`user_id` 为 `NULL`，并使用 `author_name`, `author_email`, `author_website` 字段。
- `status` 字段用于评论审核流程。

##### 1.1.2.9 `activity_logs` 表 (活动日志表)

记录系统中用户操作、系统事件等重要活动，用于审计、追踪和分析。

| 字段名          | 数据类型          | 约束/默认值                      | 索引        | 注释                                                                 |
|-----------------|-------------------|----------------------------------|-------------|----------------------------------------------------------------------|
| `id`            | `BIGINT UNSIGNED` | `PRIMARY KEY`, `AUTO_INCREMENT`  | 主键        | 日志唯一标识符                                                         |
| `user_id`       | `INT UNSIGNED`    | `NULLABLE`                       | 外键        | 执行操作的用户ID (关联 `users.id`), NULL表示系统或匿名操作             |
| `action_type`   | `VARCHAR(100)`    | `NOT NULL`                       | 普通索引    | 操作类型 (例如: 'user_login', 'content_create', 'setting_update')      |
| `target_type`   | `VARCHAR(100)`    | `NULLABLE`                       | 普通索引    | 操作对象的类型 (例如: 'user', 'article', 'page', 'setting')            |
| `target_id`     | `INT UNSIGNED`    | `NULLABLE`                       | 普通索引    | 操作对象的ID (配合 `target_type` 使用)                               |
| `description`   | `TEXT`            | `NULLABLE`                       |             | 对操作的简短描述 (例如: "用户 'admin' 登录成功", "创建了文章 #123")     |
| `details`       | `JSON`            | `NULLABLE`                       |             | 操作的详细信息 (例如: 更改前后的数据对比，请求参数等)                  |
| `ip_address`    | `VARCHAR(45)`     | `NULLABLE`                       |             | 操作者IP地址                                                           |
| `user_agent`    | `TEXT`            | `NULLABLE`                       |             | 操作者浏览器User Agent                                                 |
| `status`        | `VARCHAR(50)`     | `NOT NULL`, `DEFAULT 'success'`  | 普通索引    | 操作结果状态 (例如: 'success', 'failure', 'pending')                 |
| `created_at`    | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` | 普通索引    | 日志记录时间                                                           |

**索引:**
- KEY `idx_user_id` (`user_id`)
- KEY `idx_action_type` (`action_type`)
- KEY `idx_target` (`target_type`, `target_id`)
- KEY `idx_created_at` (`created_at`)

**外键约束:**
- CONSTRAINT `fk_activity_logs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

**说明:**
- `action_type` 应使用统一的、有意义的字符串来标识操作，方便查询和分类。
- `target_type` 和 `target_id` 结合使用，指明操作的具体对象。
- `details` 字段使用 `JSON` 类型，可以灵活存储各种结构化的附加信息。
- `status` 字段可以记录操作是否成功，对于失败的操作，`details` 中可以记录失败原因。

##### 1.1.2.10 `menus` 表 (菜单组表)

用于定义不同的菜单区域或菜单组，例如主导航、底部导航等。

| 字段名        | 数据类型        | 约束/默认值                      | 索引        | 注释                                         |
|---------------|-----------------|----------------------------------|-------------|----------------------------------------------|
| `id`          | `INT UNSIGNED`  | `PRIMARY KEY`, `AUTO_INCREMENT`  | 主键        | 菜单组唯一标识符                             |
| `name`        | `VARCHAR(100)`  | `NOT NULL`                       |             | 菜单组名称 (例如: "主导航", "页脚链接")      |
| `slug`        | `VARCHAR(50)`   | `NOT NULL`, `UNIQUE`             | 唯一索引    | 菜单组的唯一标识符 (用于代码调用, 例如: "main-nav") |
| `description` | `VARCHAR(255)`  | `NULLABLE`                       |             | 菜单组描述 (可选)                            |
| `location`    | `VARCHAR(50)`   | `NULLABLE`                       | 普通索引    | (可选) 主题中预定义的菜单位置 (例如: "primary") |
| `status`      | `TINYINT(1)`    | `NOT NULL`, `DEFAULT 1`          | 普通索引    | 状态 (1:启用, 0:禁用)                        |
| `created_at`  | `TIMESTAMP`     | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` |             | 记录创建时间                                 |
| `updated_at`  | `TIMESTAMP`     | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                                 |

**索引:**
- KEY `idx_slug` (`slug`)
- KEY `idx_status` (`status`)

**说明:**
- `slug` 字段用于在模板或代码中唯一地引用某个菜单组。
- `location` 字段可以与主题系统配合，指定菜单在主题中的特定显示位置。

##### 1.1.2.11 `menu_items` 表 (菜单项表)

存储每个菜单组中的具体菜单链接项，支持层级结构。

| 字段名         | 数据类型         | 约束/默认值                      | 索引        | 注释                                                                 |
|----------------|------------------|----------------------------------|-------------|----------------------------------------------------------------------|
| `id`           | `INT UNSIGNED`   | `PRIMARY KEY`, `AUTO_INCREMENT`  | 主键        | 菜单项唯一标识符                                                       |
| `menu_id`      | `INT UNSIGNED`   | `NOT NULL`                       | 外键        | 关联 `menus.id`, 表示所属菜单组                                      |
| `parent_id`    | `INT UNSIGNED`   | `NULLABLE`, `DEFAULT NULL`       | 外键        | 父菜单项ID (关联 `menu_items.id`), 用于实现层级菜单                    |
| `title`        | `VARCHAR(100)`   | `NOT NULL`                       |             | 菜单项显示的文本                                                       |
| `url`          | `VARCHAR(255)`   | `NOT NULL`                       |             | 菜单项链接地址                                                         |
| `target`       | `VARCHAR(20)`    | `NULLABLE`, `DEFAULT '_self'`    |             | 链接打开方式 (例如: `_self`, `_blank`)                               |
| `icon_class`   | `VARCHAR(50)`    | `NULLABLE`                       |             | (可选) 菜单项图标的CSS类名                                           |
| `item_order`   | `INT UNSIGNED`   | `NOT NULL`, `DEFAULT 0`          | 普通索引    | 菜单项在其同级中的显示顺序                                             |
| `type`         | `VARCHAR(50)`    | `NOT NULL`, `DEFAULT 'custom'`   | 普通索引    | 菜单项类型 (例如: 'custom_link', 'page', 'category', 'article')      |
| `type_id`      | `INT UNSIGNED`   | `NULLABLE`                       | 普通索引    | 关联类型ID (例如: 页面ID, 分类ID, 文章ID), 配合 `type` 字段使用       |
| `permissions`  | `VARCHAR(255)`   | `NULLABLE`                       |             | (可选) 查看此菜单项所需的权限标识                                      |
| `status`       | `TINYINT(1)`     | `NOT NULL`, `DEFAULT 1`          | 普通索引    | 状态 (1:启用, 0:禁用)                                                |
| `created_at`   | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` |             | 记录创建时间                                                           |
| `updated_at`   | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                                                           |

**索引:**
- KEY `idx_menu_id_status_order` (`menu_id`, `status`, `item_order`)
- KEY `idx_parent_id` (`parent_id`)
- KEY `idx_type` (`type`, `type_id`)

**外键约束:**
- CONSTRAINT `fk_menu_items_menu_id` FOREIGN KEY (`menu_id`) REFERENCES `menus` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
- CONSTRAINT `fk_menu_items_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `menu_items` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

**说明:**
- `parent_id` 用于构建多级菜单（下拉菜单）。
- `item_order` 决定了同一级别下菜单项的显示顺序。
- `type` 和 `type_id` 字段可以用来关联到系统内的具体内容实体（如页面、分类、文章），使得菜单项可以动态生成链接，而不仅仅是固定的URL。例如，当 `type` 为 'page' 且 `type_id` 为 5 时，表示此菜单项链接到ID为5的页面。

##### 1.1.2.12 `links` 表 (友情链接/外部链接表)

用于管理网站的友情链接、合作伙伴链接或其他需要展示的外部或内部链接。

| 字段名          | 数据类型         | 约束/默认值                                   | 索引        | 注释                                                                 |
|-----------------|------------------|-----------------------------------------------|-------------|----------------------------------------------------------------------|
| `id`            | `INT UNSIGNED`   | `PRIMARY KEY`, `AUTO_INCREMENT`               | 主键        | 链接唯一标识符                                                         |
| `name`          | `VARCHAR(100)`   | `NOT NULL`                                    |             | 链接名称 (例如: "CodeIgniter官网")                                   |
| `url`           | `VARCHAR(255)`   | `NOT NULL`                                    |             | 链接URL地址                                                            |
| `logo`          | `VARCHAR(255)`   | `NULLABLE`                                    |             | (可选) 链接的LOGO图片URL                                               |
| `target`        | `VARCHAR(20)`    | `NOT NULL`, `DEFAULT '_blank'`                |             | 链接打开方式 (例如: `_self`, `_blank`)                               |
| `description`   | `VARCHAR(255)`   | `NULLABLE`                                    |             | (可选) 链接的简短描述                                                  |
| `link_group`    | `VARCHAR(50)`    | `NULLABLE`                                    | 普通索引    | (可选) 链接分组 (例如: '友情链接', '合作伙伴', '推荐站点')             |
| `rel_attribute` | `VARCHAR(100)`   | `NULLABLE`                                    |             | (可选) 链接的 `rel` 属性 (例如: 'nofollow', 'noopener noreferrer')   |
| `status`        | `TINYINT(1)`     | `NOT NULL`, `DEFAULT 1`                       | 普通索引    | 状态 (1:启用, 0:禁用)                                                |
| `sort_order`    | `INT UNSIGNED`   | `NOT NULL`, `DEFAULT 0`                       | 普通索引    | 显示排序，数字越小越靠前                                               |
| `view_count`    | `INT UNSIGNED`   | `NOT NULL`, `DEFAULT 0`                       |             | (可选) 链接点击次数统计                                                |
| `created_by`    | `INT UNSIGNED`   | `NULLABLE`                                    | 外键        | 创建人ID (关联 `users.id`)                                           |
| `updated_by`    | `INT UNSIGNED`   | `NULLABLE`                                    | 外键        | 最后更新人ID (关联 `users.id`)                                       |
| `created_at`    | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP`       |             | 记录创建时间                                                           |
| `updated_at`    | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                                                           |

**索引:**
- KEY `idx_link_group` (`link_group`)
- KEY `idx_status_sort_order` (`status`, `sort_order`)

**外键约束:**
- CONSTRAINT `fk_links_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
- CONSTRAINT `fk_links_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

**说明:**
- `link_group` 字段提供了一种简单的链接分类方式。如果未来需要更复杂的分类管理，可以考虑引入单独的 `link_categories` 表。
- `rel_attribute` 字段有助于SEO优化和安全。
- `view_count` 可以用于分析链接的受欢迎程度。

##### 1.1.2.13 `upload_paths` 表 (上传路径配置表)

用于定义和管理系统中不同文件类型或模块的上传目标路径规则。

| 字段名                | 数据类型         | 约束/默认值                                   | 索引        | 注释                                                                 |
|-----------------------|------------------|-----------------------------------------------|-------------|----------------------------------------------------------------------|
| `id`                  | `INT UNSIGNED`   | `PRIMARY KEY`, `AUTO_INCREMENT`               | 主键        | 路径配置唯一标识符                                                     |
| `key`                 | `VARCHAR(50)`    | `NOT NULL`, `UNIQUE`                          | 唯一索引    | 路径配置的编程键 (例如: "user_avatars", "article_images")              |
| `name`                | `VARCHAR(100)`   | `NOT NULL`                                    |             | 路径配置的显示名称 (例如: "用户头像", "文章图片")                      |
| `path_template`       | `VARCHAR(255)`   | `NOT NULL`                                    |             | 相对于公共目录的路径模板 (例如: `uploads/avatars/{Y}/{m}/`)          |
| `allowed_mime_types`  | `TEXT`           | `NULLABLE`                                    |             | 允许的MIME类型 (逗号分隔, 例如: "image/jpeg,image/png,application/pdf") |
| `max_file_size_kb`    | `INT UNSIGNED`   | `NULLABLE`, `DEFAULT NULL`                    |             | 最大文件大小 (KB), NULL表示使用系统默认或无限制                      |
| `is_public`           | `BOOLEAN`        | `NOT NULL`, `DEFAULT TRUE`                    | 普通索引    | 文件是否公开访问 (TRUE: 是, FALSE: 否, 例如内部文件)                 |
| `description`         | `VARCHAR(255)`   | `NULLABLE`                                    |             | 路径配置描述                                                           |
| `status`              | `TINYINT(1)`     | `NOT NULL`, `DEFAULT 1`                       | 普通索引    | 状态 (1:启用, 0:禁用)                                                |
| `created_at`          | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP`       |             | 记录创建时间                                                           |
| `updated_at`          | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                                                           |

**索引:**
- KEY `idx_key` (`key`)
- KEY `idx_status` (`status`)
- KEY `idx_is_public` (`is_public`)

**说明:**
- `key` 字段用于程序中唯一地引用此上传路径配置。
- `path_template` 定义了文件存储的相对路径。可以使用占位符，例如 `{Y}` 代表四位数年份, `{m}` 代表两位数月份, `{d}` 代表两位数日期, `{user_id}` 代表用户ID等。这些占位符在实际上传时会被替换。路径应以 `/` 结尾。
- `allowed_mime_types` 和 `max_file_size_kb` 提供了对此路径上传文件的基本校验规则。
- `is_public` 字段指明通过此路径上传的文件是否可以通过URL直接公开访问。
- 此表的数据可以由 `database/seeds/UploadPathSeeder.php` 进行初始化填充。

##### 1.1.2.14 `themes` 表 (主题管理表)

用于管理系统中可用的主题及其配置信息。

| 字段名          | 数据类型         | 约束/默认值                                   | 索引        | 注释                                                                 |
|-----------------|------------------|-----------------------------------------------|-------------|----------------------------------------------------------------------|
| `id`            | `INT UNSIGNED`   | `PRIMARY KEY`, `AUTO_INCREMENT`               | 主键        | 主题唯一标识符                                                         |
| `name`          | `VARCHAR(100)`   | `NOT NULL`                                    |             | 主题名称 (例如: "默认主题", "企业蓝")                                |
| `folder_name`   | `VARCHAR(50)`    | `NOT NULL`, `UNIQUE`                          | 唯一索引    | 主题文件夹名称 (对应 `public/themes/` 或 `app/Themes/` 下的目录名)     |
| `version`       | `VARCHAR(20)`    | `NOT NULL`                                    |             | 主题版本号 (例如: "1.0.0")                                           |
| `author`        | `VARCHAR(100)`   | `NULLABLE`                                    |             | 主题作者                                                               |
| `author_url`    | `VARCHAR(255)`   | `NULLABLE`                                    |             | 主题作者链接                                                           |
| `description`   | `TEXT`           | `NULLABLE`                                    |             | 主题描述                                                               |
| `preview_image` | `VARCHAR(255)`   | `NULLABLE`                                    |             | 主题预览图路径                                                         |
| `settings`      | `TEXT`           | `NULLABLE`                                    |             | 主题特定配置 (JSON格式存储)                                          |
| `is_active`     | `BOOLEAN`        | `NOT NULL`, `DEFAULT FALSE`                   | 普通索引    | 是否为当前激活主题 (通常系统中只有一个主题是激活状态)                  |
| `is_core_theme` | `BOOLEAN`        | `NOT NULL`, `DEFAULT FALSE`                   |             | 是否为核心主题 (核心主题通常不可删除)                                  |
| `status`        | `TINYINT(1)`     | `NOT NULL`, `DEFAULT 1`                       | 普通索引    | 状态 (1:已安装/启用, 0:已卸载/禁用)                                  |
| `installed_at`  | `TIMESTAMP`      | `NULLABLE`                                    |             | 主题安装时间                                                           |
| `created_at`    | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP`       |             | 记录创建时间                                                           |
| `updated_at`    | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                                                           |

**索引:**
- KEY `idx_folder_name` (`folder_name`)
- KEY `idx_is_active` (`is_active`)
- KEY `idx_status` (`status`)

**说明:**
- `folder_name` 是主题在文件系统中的唯一标识。
- `is_active` 字段确保同一时间只有一个主题处于激活状态，系统加载时会读取此激活主题的配置和视图。
- `settings` 字段可以存储主题特有的配置项，例如颜色方案、布局选项等。

##### 1.1.2.15 `plugins` 表 (插件管理表)

用于管理系统中已安装和可用的插件及其状态。

| 字段名          | 数据类型         | 约束/默认值                                   | 索引        | 注释                                                                 |
|-----------------|------------------|-----------------------------------------------|-------------|----------------------------------------------------------------------|
| `id`            | `INT UNSIGNED`   | `PRIMARY KEY`, `AUTO_INCREMENT`               | 主键        | 插件唯一标识符                                                         |
| `name`          | `VARCHAR(100)`   | `NOT NULL`                                    |             | 插件名称 (例如: "SEO优化插件", "社交分享插件")                       |
| `plugin_key`    | `VARCHAR(50)`    | `NOT NULL`, `UNIQUE`                          | 唯一索引    | 插件唯一键 (通常是插件目录名或主类名，用于程序调用)                  |
| `version`       | `VARCHAR(20)`    | `NOT NULL`                                    |             | 插件版本号                                                             |
| `author`        | `VARCHAR(100)`   | `NULLABLE`                                    |             | 插件作者                                                               |
| `author_url`    | `VARCHAR(255)`   | `NULLABLE`                                    |             | 插件作者链接                                                           |
| `description`   | `TEXT`           | `NULLABLE`                                    |             | 插件描述                                                               |
| `settings_url`  | `VARCHAR(255)`   | `NULLABLE`                                    |             | (可选) 插件后台配置页面的URL路径                                     |
| `config_data`   | `TEXT`           | `NULLABLE`                                    |             | 插件的配置数据 (JSON格式存储)                                        |
| `is_active`     | `BOOLEAN`        | `NOT NULL`, `DEFAULT FALSE`                   | 普通索引    | 插件是否已激活启用                                                     |
| `load_order`    | `INT UNSIGNED`   | `NOT NULL`, `DEFAULT 100`                     | 普通索引    | 插件加载顺序 (数字越小越先加载)                                      |
| `status`        | `TINYINT(1)`     | `NOT NULL`, `DEFAULT 1`                       | 普通索引    | 状态 (1:已安装, 2:已激活, 0:已卸载/禁用)                             |
| `installed_at`  | `TIMESTAMP`      | `NULLABLE`                                    |             | 插件安装时间                                                           |
| `activated_at`  | `TIMESTAMP`      | `NULLABLE`                                    |             | 插件激活时间                                                           |
| `created_at`    | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP`       |             | 记录创建时间                                                           |
| `updated_at`    | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                                                           |

**索引:**
- KEY `idx_plugin_key` (`plugin_key`)
- KEY `idx_is_active_load_order` (`is_active`, `load_order`)
- KEY `idx_status` (`status`)

**说明:**
- `plugin_key` 是插件的唯一标识符，用于系统内部识别和调用插件。
- `is_active` 控制插件是否加载和执行其功能。
- `load_order` 用于控制多个活动插件的加载和初始化顺序。
- `config_data` 存储插件自身的配置信息。
- `settings_url` 可以指向插件在后台管理界面中的配置页面。

##### 1.1.2.16 `api_keys` 表 (API密钥表)

用于管理和认证访问系统API接口的密钥。

| 字段名            | 数据类型         | 约束/默认值                                   | 索引        | 注释                                                                 |
|-------------------|------------------|-----------------------------------------------|-------------|----------------------------------------------------------------------|
| `id`              | `INT UNSIGNED`   | `PRIMARY KEY`, `AUTO_INCREMENT`               | 主键        | API密钥唯一标识符                                                      |
| `user_id`         | `INT UNSIGNED`   | `NULLABLE`                                    | 外键        | 关联的 `users` 表 `id`，表示密钥所有者 (NULL表示系统级密钥)          |
| `name`            | `VARCHAR(100)`   | `NOT NULL`                                    |             | 密钥的描述性名称 (例如: "移动应用接口", "合作伙伴API")                 |
| `key_prefix`      | `VARCHAR(10)`    | `NOT NULL`                                    |             | 密钥前缀 (例如 "gpk_")，用于快速识别，不参与校验                     |
| `hashed_key`      | `VARCHAR(255)`   | `NOT NULL`, `UNIQUE`                          | 唯一索引    | 经过哈希处理的API密钥 (实际密钥只在创建时显示一次)                     |
| `permissions`     | `TEXT`           | `NULLABLE`                                    |             | 密钥权限范围 (JSON格式, 例如: `["content:read", "category:list"]`)   |
| `allowed_ips`     | `TEXT`           | `NULLABLE`                                    |             | 允许访问的IP地址列表 (JSON格式或逗号分隔, 支持CIDR)                  |
| `request_count`   | `BIGINT UNSIGNED`| `NOT NULL`, `DEFAULT 0`                       |             | 此密钥的总请求次数                                                       |
| `last_used_at`    | `TIMESTAMP`      | `NULLABLE`                                    |             | 最后使用时间                                                             |
| `expires_at`      | `TIMESTAMP`      | `NULLABLE`                                    |             | 密钥过期时间 (NULL表示永不过期)                                      |
| `status`          | `TINYINT(1)`     | `NOT NULL`, `DEFAULT 1`                       | 普通索引    | 状态 (1:激活, 0:禁用/吊销)                                           |
| `created_at`      | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP`       |             | 记录创建时间                                                           |
| `updated_at`      | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                                                           |

**外键约束 (建议):**
- `api_keys.user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

**索引:**
- KEY `idx_hashed_key` (`hashed_key`)
- KEY `idx_user_id` (`user_id`)
- KEY `idx_status` (`status`)
- KEY `idx_expires_at` (`expires_at`)

**说明:**
- `hashed_key` 存储的是API密钥的安全哈希值，原始密钥仅在生成时向用户显示一次，之后不再存储明文。
- `permissions` 字段用于实现细粒度的权限控制，定义此API密钥可以执行的操作。
- `allowed_ips` 字段可以限制API密钥只能从特定的IP地址或IP段发起请求，增强安全性。
- `request_count` 和 `last_used_at` 用于审计和监控API密钥的使用情况。

##### ******** `crawl_sources` 表 (爬取源表)

存储内容爬取的来源信息，例如目标网站。

| 字段名             | 数据类型         | 约束/默认值                                   | 索引        | 注释                                                                 |
|--------------------|------------------|-----------------------------------------------|-------------|----------------------------------------------------------------------|
| `id`               | `INT UNSIGNED`   | `PRIMARY KEY`, `AUTO_INCREMENT`               | 主键        | 爬取源唯一标识符                                                       |
| `name`             | `VARCHAR(150)`   | `NOT NULL`, `UNIQUE`                          | 唯一索引    | 爬取源名称 (例如: "XX技术博客", "YY新闻门户")                         |
| `base_url`         | `VARCHAR(255)`   | `NOT NULL`                                    |             | 爬取源的基础URL                                                      |
| `description`      | `TEXT`           | `NULLABLE`                                    |             | 爬取源描述信息                                                         |
| `source_type`      | `VARCHAR(50)`    | `NOT NULL`, `DEFAULT 'website'`               | 普通索引    | 来源类型 (例如: 'website', 'rss_feed', 'api')                        |
| `request_headers`  | `JSON`           | `NULLABLE`                                    |             | 爬取时附加的HTTP请求头 (例如: User-Agent, Cookies)                   |
| `rate_limit_delay` | `INT UNSIGNED`   | `NOT NULL`, `DEFAULT 1000`                    |             | 对此源的请求间隔时间 (毫秒)，防止过于频繁访问                         |
| `is_active`        | `TINYINT(1)`     | `NOT NULL`, `DEFAULT 1`                       | 普通索引    | 是否激活此爬取源 (1:激活, 0:禁用)                                    |
| `created_at`       | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP`       |             | 记录创建时间                                                           |
| `updated_at`       | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                                                           |

**说明:**
- `base_url` 是爬取目标网站的主要域名或基础路径。
- `rate_limit_delay` 用于控制爬取频率，避免对目标服务器造成过大压力。

##### 1.1.2.18 `crawl_rules` 表 (爬取规则表)

定义从特定爬取源提取内容的规则。

| 字段名                | 数据类型         | 约束/默认值                                   | 索引        | 注释                                                                 |
|-----------------------|------------------|-----------------------------------------------|-------------|----------------------------------------------------------------------|
| `id`                  | `INT UNSIGNED`   | `PRIMARY KEY`, `AUTO_INCREMENT`               | 主键        | 规则唯一标识符                                                         |
| `source_id`           | `INT UNSIGNED`   | `NOT NULL`                                    | 外键/索引   | 关联 `crawl_sources` 表的 `id`                                       |
| `name`                | `VARCHAR(150)`   | `NOT NULL`                                    |             | 规则名称 (例如: "XX博客文章页规则")                                  |
| `list_url_pattern`    | `VARCHAR(255)`   | `NULLABLE`                                    |             | 列表页URL模式 (正则表达式或通配符, 用于发现内容条目)                 |
| `item_selector`       | `VARCHAR(255)`   | `NOT NULL`                                    |             | 在列表页中定位单个内容条目的CSS选择器                                  |
| `item_link_selector`  | `VARCHAR(255)`   | `NOT NULL`                                    |             | 在内容条目中定位详情页链接的CSS选择器                                  |
| `fields_selectors`    | `JSON`           | `NOT NULL`                                    |             | 定义提取各字段内容的CSS选择器 (例如: `{"title": ".title", "body": ".content"}`)|
| `target_content_type_id`| `INT UNSIGNED`   | `NOT NULL`                                    | 外键/索引   | 关联 `content_types` 表 `id`, 抓取内容默认归属的内容类型             |
| `target_category_id`  | `INT UNSIGNED`   | `NULLABLE`                                    | 外键/索引   | 关联 `categories` 表 `id`, 抓取内容默认归属的栏目                    |
| `target_user_id`      | `INT UNSIGNED`   | `NULLABLE`                                    | 外键/索引   | 关联 `users` 表 `id`, 抓取内容默认的作者                             |
| `default_import_status`| `VARCHAR(50)`   | `NOT NULL`, `DEFAULT 'draft'`                 |             | 抓取内容导入后的默认状态 (例如: 'draft', 'pending_review')           |
| `remove_selectors`    | `JSON`           | `NULLABLE`                                    |             | 从抓取到的内容中移除指定元素的选择器列表 (例如广告、导航)              |
| `is_active`           | `TINYINT(1)`     | `NOT NULL`, `DEFAULT 1`                       | 普通索引    | 是否激活此规则 (1:激活, 0:禁用)                                      |
| `created_at`          | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP`       |             | 记录创建时间                                                           |
| `updated_at`          | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                                                           |

**外键约束:**
- `crawl_rules.source_id` FOREIGN KEY (`source_id`) REFERENCES `crawl_sources` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
- `crawl_rules.target_content_type_id` FOREIGN KEY (`target_content_type_id`) REFERENCES `content_types` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
- `crawl_rules.target_category_id` FOREIGN KEY (`target_category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
- `crawl_rules.target_user_id` FOREIGN KEY (`target_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

**说明:**
- `fields_selectors` 是核心字段，定义了如何从详情页提取标题、正文、作者、发布日期等信息。
- `remove_selectors` 用于清洗抓取到的HTML内容。

##### 1.1.2.19 `crawl_tasks` 表 (爬取任务表)

定义具体的爬取任务，包括任务的调度、目标等。

| 字段名                | 数据类型         | 约束/默认值                                   | 索引        | 注释                                                                 |
|-----------------------|------------------|-----------------------------------------------|-------------|----------------------------------------------------------------------|
| `id`                  | `INT UNSIGNED`   | `PRIMARY KEY`, `AUTO_INCREMENT`               | 主键        | 任务唯一标识符                                                         |
| `name`                | `VARCHAR(150)`   | `NOT NULL`, `UNIQUE`                          | 唯一索引    | 任务名称 (例如: "每日抓取XX博客最新文章")                            |
| `source_id`           | `INT UNSIGNED`   | `NOT NULL`                                    | 外键/索引   | 关联 `crawl_sources` 表的 `id`                                       |
| `rule_id`             | `INT UNSIGNED`   | `NOT NULL`                                    | 外键/索引   | 关联 `crawl_rules` 表的 `id`                                         |
| `schedule_type`       | `VARCHAR(50)`    | `NOT NULL`, `DEFAULT 'manual'`                | 普通索引    | 调度类型 ('manual', 'cron', 'interval')                              |
| `schedule_expression` | `VARCHAR(100)`   | `NULLABLE`                                    |             | 调度表达式 (CRON表达式或间隔秒数)                                    |
| `max_items_per_run`   | `INT UNSIGNED`   | `NOT NULL`, `DEFAULT 10`                      |             | 每次任务执行最多抓取的条目数                                           |
| `import_to_category_id`| `INT UNSIGNED`  | `NULLABLE`                                    | 外键/索引   | 覆盖规则中的目标栏目，关联 `categories` 表 `id`                      |
| `import_as_user_id`   | `INT UNSIGNED`   | `NULLABLE`                                    | 外键/索引   | 覆盖规则中的目标用户，关联 `users` 表 `id`                           |
| `import_status_override`| `VARCHAR(50)`  | `NULLABLE`                                    |             | 覆盖规则中的导入状态                                                   |
| `last_run_at`         | `TIMESTAMP`      | `NULLABLE`                                    |             | 上次执行时间                                                           |
| `next_run_at`         | `TIMESTAMP`      | `NULLABLE`                                    | 普通索引    | 下次计划执行时间                                                       |
| `is_active`           | `TINYINT(1)`     | `NOT NULL`, `DEFAULT 0`                       | 普通索引    | 是否激活此任务 (1:激活, 0:禁用)                                      |
| `created_at`          | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP`       |             | 记录创建时间                                                           |
| `updated_at`          | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                                                           |

**外键约束:**
- `crawl_tasks.source_id` FOREIGN KEY (`source_id`) REFERENCES `crawl_sources` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
- `crawl_tasks.rule_id` FOREIGN KEY (`rule_id`) REFERENCES `crawl_rules` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
- `crawl_tasks.import_to_category_id` FOREIGN KEY (`import_to_category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
- `crawl_tasks.import_as_user_id` FOREIGN KEY (`import_as_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

**说明:**
- 此表用于管理和调度爬取作业。
- `schedule_type` 和 `schedule_expression` 定义了任务如何自动执行。

##### 1.1.2.20 `crawl_logs` 表 (爬取日志表)

记录爬取任务的执行历史和结果。

| 字段名             | 数据类型         | 约束/默认值                                   | 索引        | 注释                                                                 |
|--------------------|------------------|-----------------------------------------------|-------------|----------------------------------------------------------------------|
| `id`               | `BIGINT UNSIGNED`| `PRIMARY KEY`, `AUTO_INCREMENT`               | 主键        | 日志唯一标识符                                                         |
| `task_id`          | `INT UNSIGNED`   | `NOT NULL`                                    | 外键/索引   | 关联 `crawl_tasks` 表的 `id`                                         |
| `start_time`       | `TIMESTAMP`      | `NOT NULL`                                    |             | 任务开始执行时间                                                       |
| `end_time`         | `TIMESTAMP`      | `NULLABLE`                                    |             | 任务结束执行时间                                                       |
| `status`           | `VARCHAR(50)`    | `NOT NULL`                                    | 普通索引    | 执行状态 ('running', 'success', 'failed', 'partial_success')         |
| `items_discovered` | `INT UNSIGNED`   | `NOT NULL`, `DEFAULT 0`                       |             | 本次任务发现的潜在内容条目数                                           |
| `items_processed`  | `INT UNSIGNED`   | `NOT NULL`, `DEFAULT 0`                       |             | 本次任务尝试处理的内容条目数                                           |
| `items_imported`   | `INT UNSIGNED`   | `NOT NULL`, `DEFAULT 0`                       |             | 成功导入的内容条目数                                                   |
| `items_failed`     | `INT UNSIGNED`   | `NOT NULL`, `DEFAULT 0`                       |             | 导入失败的内容条目数                                                   |
| `message`          | `TEXT`           | `NULLABLE`                                    |             | 总体日志信息或摘要                                                     |
| `log_details`      | `LONGTEXT`       | `NULLABLE`                                    |             | 详细日志，可存储JSON格式的每个条目处理结果                           |
| `created_at`       | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP`       |             | 日志记录创建时间                                                       |

**外键约束:**
- `crawl_logs.task_id` FOREIGN KEY (`task_id`) REFERENCES `crawl_tasks` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

**索引:**
- KEY `idx_task_status_time` (`task_id`, `status`, `start_time`)

**说明:**
- 此表用于追踪每次爬取任务的执行情况，便于排查问题和监控。
- `log_details` 可以存储更详细的错误信息或处理过程。

##### 1.1.2.21 `domain_bindings` 表 (域名绑定表)

存储子域名或完整域名与系统内实体（用户、栏目、专题、模块）或自定义路由的绑定关系。

| 字段名                  | 数据类型                                      | 约束/默认值                                   | 索引             | 注释                                                                 |
|-------------------------|-----------------------------------------------|-----------------------------------------------|------------------|----------------------------------------------------------------------|
| `id`                    | `INT UNSIGNED`                                | `PRIMARY KEY`, `AUTO_INCREMENT`               | 主键             | 绑定记录唯一标识符                                                     |
| `domain_name`           | `VARCHAR(255)`                                | `NOT NULL`, `UNIQUE`                          | 唯一索引         | 绑定的域名或子域名 (例如: `user1.example.com`, `blog.example.com`)     |
| `binding_type`          | `ENUM('user', 'category', 'topic', 'module', 'custom_route')` | `NOT NULL`                                    | 普通索引         | 绑定类型                                                               |
| `target_id`             | `INT UNSIGNED`                                | `NULLABLE`                                    | 普通索引         | 目标实体ID (用户ID, 栏目ID, 专题ID)。当 `binding_type` 为 'module' 或 'custom_route' 时可为NULL |
| `target_slug_or_module` | `VARCHAR(100)`                                | `NULLABLE`                                    | 普通索引         | 目标实体的别名 (用于栏目/专题) 或模块名称 (用于模块类型)                 |
| `custom_route_uri`      | `VARCHAR(255)`                                | `NULLABLE`                                    |                  | 自定义路由URI (当 `binding_type` 为 'custom_route' 时使用)             |
| `language_code`         | `VARCHAR(10)`                                 | `NULLABLE`                                    | 普通索引         | 绑定的语言代码 (例如 'en', 'zh'), 若此绑定特定于语言                   |
| `is_active`             | `TINYINT(1)`                                  | `NOT NULL`, `DEFAULT 1`                       | 普通索引         | 是否激活此绑定 (1:激活, 0:禁用)                                      |
| `notes`                 | `TEXT`                                        | `NULLABLE`                                    |                  | 备注信息                                                               |
| `created_at`            | `TIMESTAMP`                                   | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP`       |                  | 记录创建时间                                                           |
| `updated_at`            | `TIMESTAMP`                                   | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |                  | 记录更新时间                                                           |

**索引建议:**
- KEY `idx_binding_type_target` (`binding_type`, `target_id`)
- KEY `idx_binding_type_slug_module` (`binding_type`, `target_slug_or_module`)

**说明:**
- `domain_name` 必须是唯一的，确保每个域名只绑定到一个目标。
- `binding_type` 决定了如何解释 `target_id` 和 `target_slug_or_module`。
    - 'user': `target_id` 对应 `users.id`。
    - 'category': `target_id` 对应 `categories.id`，`target_slug_or_module` 可存栏目别名。
    - 'topic': `target_id` 对应 `topics.id`，`target_slug_or_module` 可存专题别名。
    - 'module': `target_slug_or_module` 存储模块的标识符/名称。
    - 'custom_route': `custom_route_uri` 存储具体的路由路径，例如 `controller/method`。
- `language_code` 允许为不同语言版本的内容设置不同的子域名。

##### 1.1.2.22 `content_relations` 表 (内容关联表)

存储不同内容条目之间的关联关系，用于实现相关内容推荐等功能。

| 字段名                | 数据类型                                            | 约束/默认值                                   | 索引                          | 注释                                                                 |
|-----------------------|-----------------------------------------------------|-----------------------------------------------|-------------------------------|----------------------------------------------------------------------|
| `id`                  | `INT UNSIGNED`                                      | `PRIMARY KEY`, `AUTO_INCREMENT`               | 主键                          | 关联记录唯一标识符                                                     |
| `source_content_id`   | `INT UNSIGNED`                                      | `NOT NULL`                                    | 外键/索引                     | 源内容ID, 关联 `contents` 表的 `id` (为此内容推荐其他内容)             |
| `related_content_id`  | `INT UNSIGNED`                                      | `NOT NULL`                                    | 外键/索引                     | 被关联内容ID, 关联 `contents` 表的 `id` (被推荐的内容)                 |
| `relation_type`       | `ENUM('manual', 'tag', 'keyword', 'system_generated')` | `NOT NULL`, `DEFAULT 'manual'`                | 普通索引                      | 关联类型 (手动, 基于标签, 基于关键词, 系统生成)                      |
| `relevance_score`     | `DECIMAL(5,2)`                                      | `NULLABLE`                                    |                               | 相关度评分 (例如 0.00 - 1.00), 用于排序                               |
| `display_order`       | `INT UNSIGNED`                                      | `NULLABLE`, `DEFAULT 0`                       |                               | 显示顺序 (主要用于手动关联时的排序)                                    |
| `created_at`          | `TIMESTAMP`                                         | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP`       |                               | 记录创建时间                                                           |
| `updated_at`          | `TIMESTAMP`                                         | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |                               | 记录更新时间                                                           |

**外键约束:**
- `content_relations.source_content_id` FOREIGN KEY (`source_content_id`) REFERENCES `contents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
- `content_relations.related_content_id` FOREIGN KEY (`related_content_id`) REFERENCES `contents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

**唯一约束:**
- UNIQUE KEY `uk_source_related_type` (`source_content_id`, `related_content_id`, `relation_type`)

**索引建议:**
- KEY `idx_source_content` (`source_content_id`, `relation_type`, `display_order`, `relevance_score`)

**说明:**
- `source_content_id` 和 `related_content_id` 共同定义了一对关联内容。
- `relation_type` 指明了此关联是如何产生的。
    - `manual`: 编辑手动指定。
    - `tag`: 系统根据共同的标签自动生成。
    - `keyword`: 系统根据共同的关键词自动生成。
    - `system_generated`: 其他由系统算法生成的关联。
- `relevance_score` 可以由系统算法计算，或手动调整，用于排序相关内容。
- `display_order` 允许对特定源内容的手动关联进行排序。
- `uk_source_related_type` 唯一约束确保同一对内容之间同一种类型的关联不重复。

##### 1.1.2.23 `search_index` 表 (全文检索引擎索引表)

存储用于全文检索的内容索引数据，支持多语言和特定字段的索引。

| 字段名            | 数据类型         | 约束/默认值                                   | 索引                          | 注释                                                                 |
|-------------------|------------------|-----------------------------------------------|-------------------------------|----------------------------------------------------------------------|
| `id`              | `BIGINT UNSIGNED`| `PRIMARY KEY`, `AUTO_INCREMENT`               | 主键                          | 索引记录唯一标识符                                                     |
| `content_id`      | `INT UNSIGNED`   | `NOT NULL`                                    | 外键/索引                     | 关联 `contents` 表的 `id`, 表示被索引的内容条目                      |
| `language_code`   | `VARCHAR(10)`    | `NOT NULL`                                    | 普通索引                      | 语言代码 (例如 'en', 'zh'), 用于区分不同语言的索引                   |
| `field_name`      | `VARCHAR(50)`    | `NOT NULL`                                    | 普通索引                      | 被索引的字段名称 (例如: 'title', 'body', 'meta_description')         |
| `indexed_content` | `MEDIUMTEXT`     | `NOT NULL`                                    | 全文索引 (推荐)               | 经过处理 (如分词、去除停用词) 的索引内容                             |
| `created_at`      | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP`       |                               | 记录创建时间                                                           |
| `updated_at`      | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |                               | 记录更新时间                                                           |

**外键约束:**
- `search_index.content_id` FOREIGN KEY (`content_id`) REFERENCES `contents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

**唯一约束:**
- UNIQUE KEY `uk_content_lang_field` (`content_id`, `language_code`, `field_name`)

**索引建议:**
- KEY `idx_language_field` (`language_code`, `field_name`)
- **推荐**: 在 `indexed_content` 字段上创建数据库层面的 `FULLTEXT` 索引 (例如 MySQL的 `FULLTEXT` 类型索引)，以提高搜索效率，特别是对于中文内容，需要配合支持中文分词的解析器。

**说明:**
- 此表用于存储从 `contents` 表及其他可能的内容源提取并处理后的文本数据。
- `content_id` 指向原始内容。
- `language_code` 确保多语言内容的搜索隔离和准确性。
- `field_name` 允许对内容的不同部分（如标题、正文）分别建立索引，可以实现更精确的字段内搜索。
- `indexed_content` 存储的是实际用于搜索的文本。对于中文，这里应该存储分词后的结果。
- `uk_content_lang_field` 唯一约束确保对于同一内容的同一语言的同一字段，只有一条索引记录。
- 关键词高亮通常在检索出结果后，由应用程序在展示时动态处理，而不是存储在索引表中。

### 1.1.3 系统配置与维护相关表

#### 1.1.3.1 `settings` 表 (系统配置表)

存储系统级别的配置信息，这些配置通常可以在后台管理界面进行修改。

| 字段名             | 数据类型          | 约束/默认值                      | 索引        | 注释                                                                 |
|--------------------|-------------------|----------------------------------|-------------|----------------------------------------------------------------------|
| `id`               | `INT UNSIGNED`    | `PRIMARY KEY`, `AUTO_INCREMENT`  | 主键        | 设置项唯一标识符                                                       |
| `group_key`        | `VARCHAR(100)`    | `NOT NULL`, `DEFAULT 'general'`  | 普通索引    | 设置项分组键 (例如: 'general', 'seo', 'email', 'theme_main', 'plugin_xyz') |
| `setting_key`      | `VARCHAR(100)`    | `NOT NULL`                       | 唯一索引 (`group_key`, `setting_key`) | 设置项的键 (例如: 'site_name', 'admin_email', 'posts_per_page')        |
| `setting_value`    | `TEXT`            | `NULLABLE`                       |             | 设置项的值 (可以是字符串、数字、布尔值，或序列化的JSON)                  |
| `label`            | `VARCHAR(255)`    | `NULLABLE`                       |             | 设置项的显示名称 (用于后台UI)                                          |
| `description`      | `TEXT`            | `NULLABLE`                       |             | 设置项的描述 (用于后台UI)                                              |
| `input_type`       | `VARCHAR(50)`     | `NOT NULL`, `DEFAULT 'text'`     |             | 输入类型 (用于后台UI渲染, 例如: 'text', 'textarea', 'boolean', 'select', 'number', 'file') |
| `options`          | `TEXT`            | `NULLABLE`                       |             | 选项值 (例如: 'select' 类型的选项, JSON格式: `{"key1":"Value1", "key2":"Value2"}`) |
| `is_serialized`    | `BOOLEAN`         | `NOT NULL`, `DEFAULT FALSE`      |             | `setting_value` 是否为序列化数据 (例如 JSON)                           |
| `is_core`          | `BOOLEAN`         | `NOT NULL`, `DEFAULT FALSE`      |             | 是否为核心设置 (核心设置通常不允许用户删除)                            |
| `validation_rules` | `VARCHAR(255)`    | `NULLABLE`                       |             | 验证规则 (例如: 'required\|min_length[5]')                             |
| `sort_order`       | `INT UNSIGNED`    | `NOT NULL`, `DEFAULT 0`          |             | 设置项在组内的排序，数字越小越靠前                                     |
| `created_at`       | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` |             | 记录创建时间                                                           |
| `updated_at`       | `TIMESTAMP`       | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |             | 记录更新时间                                                           |

**唯一索引:**
- UNIQUE KEY `uk_group_setting_key` (`group_key`, `setting_key`)

**说明:**
- `group_key` 和 `setting_key` 组合起来确保每个设置项的唯一性。
- `setting_value` 使用 `TEXT` 类型以存储较长的配置值或序列化数据。
- `input_type` 和 `options` 字段有助于在后台动态生成设置表单。
- `is_serialized` 字段指明 `setting_value` 是否需要反序列化处理。
- `validation_rules` 可以用来在保存设置时进行数据校验。

#### 1.1.3.2 `scheduled_tasks` 表 (定时任务表)

管理系统中需要定期或在特定时间执行的任务，如内容发布、缓存清理等。

| 字段名             | 数据类型         | 约束/默认值                                   | 索引                                  | 注释                                                                 |
|--------------------|------------------|-----------------------------------------------|---------------------------------------|----------------------------------------------------------------------|
| `id`               | `INT UNSIGNED`   | `PRIMARY KEY`, `AUTO_INCREMENT`               | 主键                                  | 任务唯一ID                                                             |
| `name`             | `VARCHAR(255)`   | `NOT NULL`, `UNIQUE`                          | 唯一索引                              | 任务名称，方便识别                                                       |
| `description`      | `TEXT`           | `NULLABLE`                                    |                                       | 任务详细描述                                                             |
| `command`          | `VARCHAR(255)`   | `NOT NULL`                                    | 普通索引                              | 要执行的CLI命令 (例如: `content:publish`, `cache:clear`)                 |
| `arguments`        | `JSON`           | `NULLABLE`                                    |                                       | 命令参数 (JSON格式)                                                    |
| `cron_expression`  | `VARCHAR(100)`   | `NOT NULL`                                    |                                       | CRON表达式，定义执行周期                                                 |
| `timezone`         | `VARCHAR(100)`   | `NULLABLE`                                    |                                       | CRON表达式时区 (例如: `Asia/Shanghai`)，NULL则使用系统默认时区           |
| `next_run_at`      | `DATETIME`       | `NULLABLE`                                    | 普通索引                              | 下一次计划执行时间                                                       |
| `last_run_at`      | `DATETIME`       | `NULLABLE`                                    |                                       | 上一次实际执行时间                                                       |
| `last_run_status`  | `TINYINT`        | `NOT NULL`, `DEFAULT 0`                       | 普通索引                              | 上一次执行状态 (详见说明)                                                |
| `is_active`        | `TINYINT(1)`     | `NOT NULL`, `DEFAULT 1`                       | 普通索引                              | 任务是否激活 (1:激活, 0:禁用)                                          |
| `run_once`         | `TINYINT(1)`     | `NOT NULL`, `DEFAULT 0`                       |                                       | 是否为一次性任务 (1:是, 0:否)                                          |
| `overlap_strategy` | `TINYINT`        | `NOT NULL`, `DEFAULT 1`                       |                                       | 执行重叠策略 (详见说明)                                                  |
| `created_by`       | `INT UNSIGNED`   | `NULLABLE`                                    | 外键 (users.id)                       | 创建用户ID                                                             |
| `updated_by`       | `INT UNSIGNED`   | `NULLABLE`                                    | 外键 (users.id)                       | 更新用户ID                                                             |
| `created_at`       | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP`       |                                       | 创建时间                                                               |
| `updated_at`       | `TIMESTAMP`      | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` |                                       | 更新时间                                                               |

**说明:**

-   **`last_run_status` (上一次执行状态) 常量定义:**
    *   `0`: `STATUS_PENDING` (待执行/新任务)
    *   `1`: `STATUS_RUNNING` (执行中)
    *   `2`: `STATUS_SUCCESS` (成功)
    *   `3`: `STATUS_FAILED` (失败)
    *   `4`: `STATUS_SKIPPED` (跳过)
-   **`overlap_strategy` (执行重叠策略) 常量定义:**
    *   `0`: `OVERLAP_ALLOW` (允许并发执行新的实例)
    *   `1`: `OVERLAP_SKIP` (如果前一个实例仍在运行，则跳过本次执行)
    *   `2`: `OVERLAP_QUEUE` (如果前一个实例在运行，则将本次任务加入队列等待，需额外实现队列逻辑，不推荐用于长时间任务)
-   `command` 字段指定 CodeIgniter 的 CLI 命令，例如 `php spark your:command`.
-   `arguments` 字段存储传递给命令的参数，格式为 JSON，例如 `{"content_id": 123}`.
-   `next_run_at` 由调度器根据 `cron_expression` 计算，用于确定任务的下一次运行时间。

### 1.2 表关系设计

本节详细定义了GACMS核心数据表之间的关联关系，包括一对一、一对多和多对多关系，并指明了外键约束，以保证数据的完整性和一致性。

#### 1.2.1 用户与权限相关表的关系

1.  **`users` 与 `user_roles` (一对多)**
    *   `user_roles.user_id` 外键关联 `users.id`
    *   描述：一个用户可以拥有多个角色。

2.  **`roles` 与 `user_roles` (一对多)**
    *   `user_roles.role_id` 外键关联 `roles.id`
    *   描述：一个角色可以分配给多个用户。

3.  **`roles` 与 `role_permissions` (一对多)**
    *   `role_permissions.role_id` 外键关联 `roles.id`
    *   描述：一个角色可以拥有多个权限。

4.  **`permissions` 与 `role_permissions` (一对多)**
    *   `role_permissions.permission_id` 外键关联 `permissions.id`
    *   描述：一个权限可以分配给多个角色。

5.  **`users` 与 `user_activity_logs` (一对多)**
    *   `user_activity_logs.user_id` 外键关联 `users.id` (可为NULL，表示系统级活动)
    *   描述：一个用户可以产生多条活动日志。

6.  **`users` 与 `login_attempts` (一对多)**
    *   `login_attempts.user_id` 外键关联 `users.id` (可为NULL，如果尝试登录的用户名不存在)
    *   描述：一个用户（或尝试登录的身份）可以有多条登录尝试记录。

7.  **`users` 与 `password_resets` (一对多)**
    *   `password_resets.user_id` 外键关联 `users.id`
    *   描述：一个用户可以有多条密码重置请求。

8.  **`users` 与 `user_totp_secrets` (一对一)**
    *   `user_totp_secrets.user_id` 外键关联 `users.id`，且应有唯一约束。
    *   描述：一个用户对应一个TOTP密钥配置。

#### 1.2.2 内容管理相关表的关系

1.  **`categories` 与 `articles` (一对多)**
    *   `articles.category_id` 外键关联 `categories.id`
    *   描述：一个栏目下可以有多篇文章。
    *   `categories.parent_id` 自关联 `categories.id` (用于实现层级栏目)

2.  **`users` 与 `articles` (一对多，作者关系)**
    *   `articles.author_id` 外键关联 `users.id`
    *   描述：一个用户可以撰写多篇文章。

3.  **`articles` 与 `article_tags` (一对多)**
    *   `article_tags.article_id` 外键关联 `articles.id`
    *   描述：一篇文章可以有多个标签。

4.  **`tags` 与 `article_tags` (一对多)**
    *   `article_tags.tag_id` 外键关联 `tags.id`
    *   描述：一个标签可以关联多篇文章。

5.  **`articles` 与 `comments` (一对多)**
    *   `comments.article_id` 外键关联 `articles.id`
    *   描述：一篇文章可以有多条评论。

6.  **`users` 与 `comments` (一对多，评论者关系)**
    *   `comments.user_id` 外键关联 `users.id` (可为NULL，允许匿名评论)
    *   描述：一个用户可以发表多条评论。
    *   `comments.parent_id` 自关联 `comments.id` (用于实现评论回复层级)

7.  **`articles` 与 `attachments` (一对多，或通过中间表实现多对多)**
    *   如果一个附件仅属于一篇文章：`attachments.article_id` 外键关联 `articles.id` (可为NULL，如果附件可独立存在)
    *   如果一个附件可被多篇文章共享（更灵活）：则需要 `article_attachments` 中间表。当前设计 `attachments` 表中已有 `related_id` 和 `related_type`，可以灵活关联到文章或其他实体。
    *   `attachments.uploaded_by_user_id` 外键关联 `users.id`

8.  **`articles` 与 `content_views` (一对多)**
    *   `content_views.article_id` 外键关联 `articles.id`
    *   描述：一篇文章可以被多次浏览。
    *   `content_views.user_id` 外键关联 `users.id` (可为NULL，记录匿名用户浏览)

9.  **`articles` 与 `related_articles` (多对多，通过自身实现)**
    *   `related_articles.article_id` 外键关联 `articles.id`
    *   `related_articles.related_article_id` 外键关联 `articles.id`
    *   描述：定义文章之间的相关性。

10. **`topics` 与 `topic_articles` (一对多)**
    *   `topic_articles.topic_id` 外键关联 `topics.id`
    *   描述：一个专题可以包含多篇文章。

11. **`articles` 与 `topic_articles` (一对多)**
    *   `topic_articles.article_id` 外键关联 `articles.id`
    *   描述：一篇文章可以属于多个专题（通过 `topic_articles` 表实现）。

12. **`articles` 与 `article_revisions` (一对多)**
    *   `article_revisions.article_id` 外键关联 `articles.id`
    *   描述：一篇文章可以有多个修订历史版本。
    *   `article_revisions.user_id` 外键关联 `users.id` (记录修订者)

13. **`articles` 与 `content_workflow` (一对一 或 一对多，取决于工作流设计)**
    *   `content_workflow.article_id` 外键关联 `articles.id`
    *   描述：一篇文章对应其当前的工作流状态。
    *   `content_workflow.assigned_to_user_id` 外键关联 `users.id` (指派给审核的用户)
    *   `content_workflow.action_by_user_id` 外键关联 `users.id` (执行操作的用户)

14. **`crawl_sources` 与 `crawl_rules` (一对多)**
    *   `crawl_rules.source_id` 外键关联 `crawl_sources.id`
    *   描述：一个爬取源可以有多条爬取规则。

15. **`crawl_sources` 与 `crawl_logs` (一对多)**
    *   `crawl_logs.source_id` 外键关联 `crawl_sources.id`
    *   描述：一个爬取源可以产生多条爬取日志。

16. **`crawl_rules` 与 `crawl_logs` (一对多)**
    *   `crawl_logs.rule_id` 外键关联 `crawl_rules.id` (可为NULL，如果日志是源级别的)
    *   描述：一条爬取规则可以产生多条爬取日志。

17. **`articles` 与 `seo_metadata` (一对一，通过 `related_id` 和 `related_type`)**
    *   `seo_metadata.related_id` 存储 `articles.id`，`seo_metadata.related_type` 存储 'article'
    *   描述：一篇文章对应一组SEO元数据。`seo_metadata` 也可以用于 `categories` 或 `topics`。

18. **`sitemaps` 与 `users` (创建者)**
    *   `sitemaps.created_by` 外键关联 `users.id`

19. **`structured_data` 与 `articles` (一对一 或 一对多，通过 `related_id` 和 `related_type`)**
    *   `structured_data.related_id` 存储 `articles.id`，`structured_data.related_type` 存储 'article'
    *   描述：一篇文章可以关联一个或多个结构化数据条目。

#### 1.2.3 系统配置与维护相关表的关系

1.  **`users` 与 `system_logs` (操作者)**
    *   `system_logs.user_id` 外键关联 `users.id` (可为NULL，表示系统自动产生的日志)
    *   描述：记录执行操作的用户。

2.  **`users` 与 `scheduled_tasks` (创建/更新者)**
    *   `scheduled_tasks.created_by` 外键关联 `users.id`
    *   `scheduled_tasks.updated_by` 外键关联 `users.id`
    *   描述：记录任务的创建和最后更新用户。

3.  **`upload_paths` 与 `users` (创建/更新者)**
    *   `upload_paths.created_by` 外键关联 `users.id`
    *   `upload_paths.updated_by` 外键关联 `users.id`
    *   描述：记录上传路径配置的创建和最后更新用户。

**注意：**
*   上述关系主要基于已定义的表结构。在实际实现时，应根据具体业务需求调整，并确保在数据库层面设置正确的外键约束（例如，ON DELETE, ON UPDATE 的行为）。
*   对于多态关联（如 `attachments`, `seo_metadata`, `structured_data` 中的 `related_id` 和 `related_type`），外键约束通常在应用层面保证，数据库层面不直接设置跨多个表的外键。

### 1.3 索引设计

为了优化数据库查询性能，需要为表中的特定列创建索引。本节将列出建议的索引策略和具体表的索引设计。

#### 1.3.1 索引设计原则

1.  **选择性高的列**：优先为具有高选择性（列中不同值的数量较多）的列创建索引。
2.  **WHERE 子句中的列**：经常在 `WHERE` 子句中用作查询条件的列。
3.  **JOIN 操作中的列**：用于表连接的列，特别是外键列。
4.  **ORDER BY 和 GROUP BY 子句中的列**：用于排序和分组的列。
5.  **主键和唯一约束**：数据库通常会自动为主键和唯一约束创建索引。
6.  **外键列**：为所有外键列创建索引，以提高连接查询的性能并避免全表扫描。
7.  **复合索引**：当多个列经常一起出现在 `WHERE` 子句中时，可以考虑创建复合索引。索引列的顺序很重要，应将选择性最高或最常用于等值查询的列放在前面。
8.  **避免过度索引**：索引会占用存储空间，并可能降低写操作（INSERT, UPDATE, DELETE）的性能。只创建必要的索引。
9.  **覆盖索引**：如果一个索引包含了查询所需的所有列，数据库可以直接从索引中获取数据，而无需访问表本身，从而提高性能。
10. **使用合适的索引类型**：根据数据类型和查询模式选择合适的索引类型（如 B-Tree, Hash, Full-Text, Spatial）。对于GACMS，B-Tree 索引是主要使用的类型，特定场景（如文章内容搜索）可考虑 Full-Text 索引。

#### 1.3.2 用户与权限相关表的索引建议

1.  **`users` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `UNIQUE INDEX idx_users_username (username)`
    *   `UNIQUE INDEX idx_users_email (email)`
    *   `INDEX idx_users_status (status)`
    *   `INDEX idx_users_created_at (created_at)`

2.  **`roles` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `UNIQUE INDEX idx_roles_name (name)`

3.  **`permissions` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `UNIQUE INDEX idx_permissions_name (name)`
    *   `INDEX idx_permissions_group_name (group_name)`

4.  **`user_roles` (中间表)**
    *   `PRIMARY KEY (user_id, role_id)` (或 `id` 如果有自增主键)
    *   `INDEX idx_user_roles_user_id (user_id)` (外键，通常已包含在主键或单独创建)
    *   `INDEX idx_user_roles_role_id (role_id)` (外键，通常已包含在主键或单独创建)
    *   如果 `id` 是主键，则 `UNIQUE INDEX idx_user_roles_user_role (user_id, role_id)`

5.  **`role_permissions` (中间表)**
    *   `PRIMARY KEY (role_id, permission_id)` (或 `id` 如果有自增主键)
    *   `INDEX idx_role_permissions_role_id (role_id)`
    *   `INDEX idx_role_permissions_permission_id (permission_id)`
    *   如果 `id` 是主键，则 `UNIQUE INDEX idx_role_permissions_role_permission (role_id, permission_id)`

6.  **`user_activity_logs` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `INDEX idx_user_activity_logs_user_id (user_id)`
    *   `INDEX idx_user_activity_logs_activity_type (activity_type)`
    *   `INDEX idx_user_activity_logs_created_at (created_at)`
    *   `INDEX idx_user_activity_logs_ip_address (ip_address)`

7.  **`login_attempts` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `INDEX idx_login_attempts_user_id (user_id)`
    *   `INDEX idx_login_attempts_ip_address (ip_address)`
    *   `INDEX idx_login_attempts_timestamp (timestamp)`

8.  **`password_resets` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `INDEX idx_password_resets_user_id (user_id)`
    *   `UNIQUE INDEX idx_password_resets_token (token)`
    *   `INDEX idx_password_resets_created_at (created_at)`

9.  **`user_totp_secrets` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `UNIQUE INDEX idx_user_totp_secrets_user_id (user_id)`

#### 1.3.3 内容管理相关表的索引建议

1.  **`articles` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `UNIQUE INDEX idx_articles_slug (slug)`
    *   `INDEX idx_articles_category_id (category_id)`
    *   `INDEX idx_articles_author_id (author_id)`
    *   `INDEX idx_articles_status (status)`
    *   `INDEX idx_articles_published_at (published_at)`
    *   `INDEX idx_articles_created_at (created_at)`
    *   `INDEX idx_articles_title (title)` (考虑 FULLTEXT 索引用于内容搜索: `FULLTEXT INDEX ft_articles_title_content (title, content)`)
    *   `INDEX idx_articles_is_featured (is_featured)`
    *   `INDEX idx_articles_is_sticky (is_sticky)`

2.  **`categories` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `UNIQUE INDEX idx_categories_slug (slug)`
    *   `INDEX idx_categories_parent_id (parent_id)`
    *   `INDEX idx_categories_name (name)`
    *   `INDEX idx_categories_sort_order (sort_order)`

3.  **`tags` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `UNIQUE INDEX idx_tags_slug (slug)`
    *   `UNIQUE INDEX idx_tags_name (name)`

4.  **`article_tags` (中间表)**
    *   `PRIMARY KEY (article_id, tag_id)` (或 `id` 如果有自增主键)
    *   `INDEX idx_article_tags_article_id (article_id)`
    *   `INDEX idx_article_tags_tag_id (tag_id)`
    *   如果 `id` 是主键，则 `UNIQUE INDEX idx_article_tags_article_tag (article_id, tag_id)`

5.  **`comments` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `INDEX idx_comments_article_id (article_id)`
    *   `INDEX idx_comments_user_id (user_id)`
    *   `INDEX idx_comments_parent_id (parent_id)`
    *   `INDEX idx_comments_status (status)`
    *   `INDEX idx_comments_created_at (created_at)`

6.  **`attachments` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `INDEX idx_attachments_related (related_id, related_type)`
    *   `INDEX idx_attachments_uploaded_by_user_id (uploaded_by_user_id)`
    *   `INDEX idx_attachments_file_type (file_type)`
    *   `INDEX idx_attachments_created_at (created_at)`

7.  **`content_views` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `INDEX idx_content_views_article_id (article_id)`
    *   `INDEX idx_content_views_user_id (user_id)`
    *   `INDEX idx_content_views_view_date (view_date)`
    *   `INDEX idx_content_views_ip_address (ip_address)`

8.  **`related_articles` (中间表)**
    *   `PRIMARY KEY (article_id, related_article_id)`
    *   `INDEX idx_related_articles_article_id (article_id)`
    *   `INDEX idx_related_articles_related_article_id (related_article_id)`

9.  **`topics` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `UNIQUE INDEX idx_topics_slug (slug)`
    *   `INDEX idx_topics_name (name)`
    *   `INDEX idx_topics_created_at (created_at)`

10. **`topic_articles` (中间表)**
    *   `PRIMARY KEY (topic_id, article_id)` (或 `id` 如果有自增主键)
    *   `INDEX idx_topic_articles_topic_id (topic_id)`
    *   `INDEX idx_topic_articles_article_id (article_id)`
    *   如果 `id` 是主键，则 `UNIQUE INDEX idx_topic_articles_topic_article (topic_id, article_id)`

11. **`article_revisions` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `INDEX idx_article_revisions_article_id (article_id)`
    *   `INDEX idx_article_revisions_user_id (user_id)`
    *   `INDEX idx_article_revisions_created_at (created_at)`

12. **`content_workflow` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `UNIQUE INDEX idx_content_workflow_article_id (article_id)`
    *   `INDEX idx_content_workflow_status (status)`
    *   `INDEX idx_content_workflow_assigned_to_user_id (assigned_to_user_id)`
    *   `INDEX idx_content_workflow_action_by_user_id (action_by_user_id)`
    *   `INDEX idx_content_workflow_updated_at (updated_at)`

13. **`crawl_sources` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `UNIQUE INDEX idx_crawl_sources_name (name)`
    *   `INDEX idx_crawl_sources_status (status)`
    *   `INDEX idx_crawl_sources_last_crawled_at (last_crawled_at)`

14. **`crawl_rules` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `INDEX idx_crawl_rules_source_id (source_id)`
    *   `INDEX idx_crawl_rules_name (name)`
    *   `INDEX idx_crawl_rules_is_active (is_active)`

15. **`crawl_logs` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `INDEX idx_crawl_logs_source_id (source_id)`
    *   `INDEX idx_crawl_logs_rule_id (rule_id)`
    *   `INDEX idx_crawl_logs_status (status)`
    *   `INDEX idx_crawl_logs_created_at (created_at)`

16. **`seo_metadata` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `UNIQUE INDEX idx_seo_metadata_related (related_id, related_type)`
    *   `INDEX idx_seo_metadata_meta_title (meta_title)`

17. **`sitemaps` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `UNIQUE INDEX idx_sitemaps_file_path (file_path)`
    *   `INDEX idx_sitemaps_last_modified (last_modified)`
    *   `INDEX idx_sitemaps_created_by (created_by)`

18. **`structured_data` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `INDEX idx_structured_data_related (related_id, related_type)`
    *   `INDEX idx_structured_data_type (type)`

#### 1.3.4 系统配置与维护相关表的索引建议

1.  **`settings` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `UNIQUE INDEX idx_settings_key (key)`
    *   `INDEX idx_settings_group (group)`

2.  **`system_logs` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `INDEX idx_system_logs_level (level)`
    *   `INDEX idx_system_logs_channel (channel)`
    *   `INDEX idx_system_logs_user_id (user_id)`
    *   `INDEX idx_system_logs_created_at (created_at)`

3.  **`scheduled_tasks` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `UNIQUE INDEX idx_scheduled_tasks_name (name)`
    *   `INDEX idx_scheduled_tasks_status (status)`
    *   `INDEX idx_scheduled_tasks_next_run_at (next_run_at)`
    *   `INDEX idx_scheduled_tasks_last_run_at (last_run_at)`

4.  **`upload_paths` 表**
    *   `PRIMARY KEY (id)` (自动创建)
    *   `UNIQUE INDEX idx_upload_paths_path_name (path_name)`
    *   `INDEX idx_upload_paths_is_active (is_active)`

**注意：**
*   上述索引是基于通用场景的建议。在系统实际运行过程中，应根据查询分析器 (如 MySQL 的 `EXPLAIN`) 的输出和实际的查询负载，对索引进行调整和优化。
*   对于 `TEXT` 或 `BLOB` 类型的列，如果需要索引，通常只能索引其前缀长度，或者使用 FULLTEXT 索引。
*   创建索引的时机可以在数据库迁移脚本中定义。

### 1.4 数据库优化策略

数据库性能是影响GACMS系统整体性能的关键因素。本节将探讨一系列数据库优化策略，以确保系统高效、稳定运行。

#### 1.4.1 优化查询语句

1.  **避免 `SELECT *`**：
    *   只选择需要的列，减少数据传输量和内存消耗。
    *   利用覆盖索引的可能性。

2.  **优化 `WHERE` 子句**：
    *   确保查询条件中的列已建立合适的索引。
    *   避免在索引列上使用函数或进行运算，这可能导致索引失效。例如，使用 `WHERE date_column >= '2024-01-01'` 而不是 `WHERE YEAR(date_column) = 2024`。
    *   使用 `EXPLAIN` 分析查询计划，找出性能瓶颈。

3.  **优化 `JOIN` 操作**：
    *   确保连接条件中的列已建立索引。
    *   尽量使用 `INNER JOIN`，避免不必要的 `OUTER JOIN`。
    *   对于小表驱动大表的场景，确保连接顺序合理。

4.  **合理使用 `GROUP BY` 和 `ORDER BY`**：
    *   确保分组和排序的列已建立索引。
    *   如果不需要排序，避免使用 `ORDER BY`。

5.  **减少子查询**：
    *   尽可能将子查询改写为 `JOIN` 操作，通常 `JOIN` 的性能更好。

6.  **使用批量操作**：
    *   对于批量插入、更新或删除，使用数据库提供的批量操作接口，减少与数据库的交互次数。

7.  **分页查询优化**：
    *   对于大数据量的分页，避免使用 `OFFSET`，尤其是在 `OFFSET` 值很大时。可以考虑使用基于游标或上一页最后一条记录ID的方式进行分页（"seek method" 或 "keyset pagination"）。
    *   例如：`WHERE id > last_seen_id ORDER BY id ASC LIMIT page_size`。

#### 1.4.2 数据库服务器配置优化

1.  **内存分配**：
    *   根据服务器硬件资源，合理配置数据库的内存参数，如 InnoDB buffer pool size (MySQL), shared_buffers (PostgreSQL)。
    *   确保缓冲池足够大，以缓存常用数据和索引，减少磁盘I/O。

2.  **连接池配置**：
    *   合理配置最大连接数、最小空闲连接数等参数，以应对并发请求。
    *   使用持久连接或应用层连接池减少连接建立的开销。

3.  **日志配置**：
    *   合理配置慢查询日志，定期分析并优化慢查询。
    *   根据需求配置二进制日志 (binlog) 或预写日志 (WAL) 的策略，平衡性能和数据恢复能力。

4.  **存储引擎选择**：
    *   选择合适的存储引擎（如 MySQL 的 InnoDB），它支持事务、行级锁和外键，适合大多数应用场景。

#### 1.4.3 架构层面优化

1.  **读写分离**：
    *   对于读多写少的应用，可以考虑使用主从复制，将读请求分发到从库，减轻主库压力。

2.  **数据分片 (Sharding)**：
    *   当单一数据库无法承载极高的数据量和并发时，可以考虑水平分片或垂直分片。这是一个复杂的策略，应在其他优化手段都已尝试后再考虑。

3.  **缓存策略**：
    *   在应用层或使用独立的缓存服务（如 Redis, Memcached）缓存热点数据和查询结果，减少对数据库的直接访问。
    *   例如，缓存栏目信息、热门文章、系统配置等。

#### 1.4.4 定期维护

1.  **分析和优化表**：
    *   定期执行 `ANALYZE TABLE` (或数据库等效命令) 更新统计信息，帮助查询优化器生成更优的执行计划。
    *   对于有大量删除或更新操作的表，定期执行 `OPTIMIZE TABLE` (或等效命令如 `VACUUM` in PostgreSQL) 回收空间，减少碎片。

2.  **监控数据库性能**：
    *   使用数据库监控工具，实时监控数据库的各项性能指标（CPU、内存、I/O、连接数、慢查询等）。
    *   根据监控结果及时调整优化策略。

3.  **备份与恢复策略**：
    *   制定完善的数据库备份和恢复计划，确保数据安全。

#### 1.4.5 应用层优化配合

1.  **避免N+1查询**：
    *   在ORM或数据访问层，注意避免N+1查询问题。使用预加载 (eager loading) 或批量加载相关数据。

2.  **数据校验**：
    *   在数据写入数据库前，在应用层进行严格的数据校验，避免无效数据或格式错误的数据进入数据库。

3.  **异步处理**：
    *   对于非核心、耗时的数据库操作（如发送邮件通知、生成复杂报表），可以考虑使用消息队列进行异步处理，避免阻塞主线程。

**注意：**
*   数据库优化是一个持续的过程，需要根据应用的具体负载和数据特点不断调整。
*   在进行任何重大优化前，务必在测试环境中进行充分测试，并做好数据备份。

### 1.5 数据保护设计

数据保护是GACMS系统安全性的重要组成部分，旨在防止数据丢失、泄露、篡改，并确保在发生灾难时能够及时恢复。本节将阐述关键的数据保护策略。

#### 1.5.1 数据备份与恢复

1.  **备份策略**：
    *   **完整备份**：定期（如每周或每月）对整个数据库进行完整备份。
    *   **增量备份/差异备份**：更频繁地（如每日）进行增量备份（备份自上次任意备份以来更改的数据）或差异备份（备份自上次完整备份以来更改的数据）。
    *   **事务日志备份**：对于支持时间点恢复的数据库（如MySQL的binlog，PostgreSQL的WAL），应定期备份事务日志，以便能够恢复到故障发生前的特定时间点。
    *   **备份存储**：备份数据应存储在与主数据库物理隔离的安全位置，最好是异地存储，以防本地灾难。
    *   **备份保留策略**：根据业务需求和法规要求，制定备份数据的保留期限。

2.  **恢复策略**：
    *   **恢复测试**：定期进行恢复演练，验证备份的有效性和恢复流程的可行性，确保在真实灾难发生时能够快速、准确地恢复数据。
    *   **恢复时间目标 (RTO)**：定义系统从故障中恢复并重新提供服务所需的最长时间。
    *   **恢复点目标 (RPO)**：定义可容忍的最大数据丢失量（以时间衡量）。
    *   **文档化恢复流程**：详细记录数据恢复的步骤和负责人。

#### 1.5.2 数据加密

1.  **传输中加密 (Encryption in Transit)**：
    *   客户端与数据库服务器之间的连接应使用SSL/TLS加密，防止数据在传输过程中被窃听。
    *   应用服务器与数据库服务器之间的连接也应考虑加密。

2.  **静态加密 (Encryption at Rest)**：
    *   **数据库级加密**：许多数据库系统提供透明数据加密 (TDE) 功能，对存储在磁盘上的数据文件进行加密。
    *   **列级加密**：对于特别敏感的数据（如用户密码、支付信息等），可以在应用层面或数据库层面进行列级加密。用户密码必须使用强哈希算法（如 bcrypt, Argon2）加盐存储，而非可逆加密。
    *   **备份加密**：备份数据在存储前也应进行加密，确保即使备份介质泄露，数据也不会被轻易读取。
    *   **密钥管理**：加密密钥需要妥善管理，使用安全的密钥管理系统 (KMS) 或硬件安全模块 (HSM) 存储和管理密钥。密钥的访问权限应严格控制。

#### 1.5.3 访问控制与权限管理

1.  **最小权限原则**：
    *   数据库用户账户应遵循最小权限原则，只授予其完成工作所必需的最小权限集。
    *   应用程序连接数据库使用的账户，其权限应严格限制在必要的DML（SELECT, INSERT, UPDATE, DELETE）操作，并限制其对特定数据库和表的操作。
    *   避免使用具有超级管理员权限的账户进行日常应用操作。

2.  **角色管理**：
    *   使用数据库的角色功能来组织和管理权限，将权限分配给角色，再将角色分配给用户。

3.  **强认证机制**：
    *   数据库用户账户应使用强密码策略。
    *   考虑为数据库管理员账户启用多因素认证（如果数据库支持）。

4.  **审计与监控**：
    *   启用数据库审计功能，记录关键操作（如登录尝试、DDL更改、敏感数据访问等）。
    *   定期审查审计日志，检测异常活动和潜在的安全威胁。

#### 1.5.4 数据脱敏与匿名化

1.  **开发与测试环境**：
    *   在开发和测试环境中使用生产数据的副本时，应对敏感数据进行脱敏或匿名化处理，以防止敏感信息泄露给非授权人员。
    *   例如，将真实的姓名替换为虚构姓名，将身份证号、电话号码等替换为随机生成或部分遮蔽的数据。

2.  **数据分析与共享**：
    *   当需要对外提供数据进行分析或共享时，如果数据包含个人可识别信息 (PII)，应进行匿名化处理，以符合隐私保护法规的要求。

#### 1.5.5 物理安全

*   数据库服务器应放置在物理安全的机房中，具有严格的访问控制措施，防止未经授权的物理接触。

#### 1.5.6 灾难恢复计划 (DRP)

*   制定详细的灾难恢复计划，涵盖不同类型的灾难场景（如硬件故障、自然灾害、网络攻击等）。
*   DRP应包括数据恢复、系统重建、网络恢复等步骤，并明确各阶段的负责人和时间表。

**注意：**
*   数据保护是一个多层面、持续性的工作，需要技术、流程和人员的共同配合。
*   应定期评估和更新数据保护策略，以应对新的威胁和业务变化。
*   遵守相关的法律法规要求，如GDPR、CCPA等数据隐私保护法规。

### 1.6 数据库部署与维护

数据库的稳定运行是GACMS系统正常服务的基础。本节将概述数据库部署的关键步骤和持续维护的最佳实践。

#### 1.6.1 部署规划

1.  **环境准备**：
    *   **操作系统选择**：根据团队熟悉度和数据库兼容性选择合适的操作系统（如 Linux 发行版 CentOS, Ubuntu, Debian，或 Windows Server）。
    *   **硬件资源评估**：根据预期的数据量、并发用户数和性能要求，评估所需的 CPU、内存、磁盘空间和I/O性能。
    *   **网络规划**：规划数据库服务器的网络配置，包括IP地址、端口、防火墙规则等。确保应用服务器与数据库服务器之间的网络通畅且安全。

2.  **数据库版本选择**：
    *   选择稳定且经过广泛测试的数据库版本（如 MySQL 8.x, PostgreSQL 13+）。
    *   考虑与CodeIgniter 4及所用PHP版本的兼容性。
    *   了解所选版本的特性、已知问题和生命周期。

3.  **部署模式**：
    *   **单机部署**：适用于小型应用或开发测试环境。
    *   **主从复制 (Master-Slave Replication)**：提高读取性能和可用性。
    *   **集群部署 (Cluster)**：如 MySQL InnoDB Cluster, Galera Cluster, PostgreSQL Cluster，提供高可用性和可伸缩性。根据业务需求和预算选择合适的模式。

#### 1.6.2 安装与配置

1.  **数据库软件安装**：
    *   遵循官方文档或可靠教程进行数据库软件的安装。
    *   确保安装源的安全性。

2.  **初始化配置**：
    *   **配置文件**：根据服务器硬件和应用负载调整关键配置参数（如 `my.cnf` for MySQL, `postgresql.conf` for PostgreSQL）。例如，内存分配、连接数、日志设置等。
    *   **字符集与排序规则**：统一设置为 `UTF-8` (或 `utf8mb4` for MySQL) 以支持多语言和特殊字符。
    *   **时区设置**：确保数据库服务器时区与应用服务器时区一致或正确配置。

3.  **安全加固**：
    *   **修改默认密码**：修改数据库管理员账户（如 `root`@`localhost`）的默认密码，并设置强密码。
    *   **创建专用账户**：为GACMS应用创建专用的数据库用户，并授予最小必要权限。
    *   **限制远程访问**：如果不需要，禁止不必要的远程访问，或限制特定IP地址的访问。
    *   **移除不必要的数据库和用户**：删除测试数据库和默认创建的不必要用户。
    *   **定期更新和打补丁**：及时应用数据库厂商发布的安全补丁。

#### 1.6.3 数据迁移 (如适用)

*   如果从旧系统迁移数据，制定详细的数据迁移计划。
*   包括数据清洗、格式转换、迁移测试和数据校验步骤。
*   在迁移过程中尽量减少对现有服务的影响。

#### 1.6.4 日常维护

1.  **性能监控**：
    *   使用数据库自带的监控工具或第三方监控系统（如 Prometheus + Grafana, Zabbix, Percona Monitoring and Management (PMM)）监控关键性能指标：CPU使用率、内存使用率、磁盘I/O、网络流量、连接数、查询延迟、慢查询等。
    *   设置告警阈值，及时发现和处理性能问题。

2.  **备份与恢复验证**：
    *   严格执行 1.5.1 节中制定的备份策略。
    *   定期检查备份文件的完整性和可恢复性，进行恢复演练。

3.  **日志管理**：
    *   **错误日志**：定期检查数据库错误日志，排查潜在问题。
    *   **慢查询日志**：启用并定期分析慢查询日志，找出需要优化的SQL语句。
    *   **审计日志**：根据安全策略配置和审查审计日志。
    *   **日志轮转与归档**：配置日志文件的自动轮转和归档，防止磁盘空间耗尽。

4.  **补丁与升级**：
    *   关注数据库厂商发布的安全公告和版本更新。
    *   在测试环境中充分测试补丁和新版本后，再计划在生产环境中进行升级。
    *   制定详细的升级回滚计划。

5.  **表维护**：
    *   定期执行 `ANALYZE TABLE` (或等效命令) 更新表的统计信息。
    *   对于频繁DML操作的表，根据需要执行 `OPTIMIZE TABLE` (或等效命令如 `VACUUM FULL` in PostgreSQL) 来整理碎片，回收空间。注意这些操作可能会锁定表，应在低峰期进行。

6.  **容量规划**：
    *   监控磁盘空间使用情况，预测增长趋势，及时进行扩容。

#### 1.6.5 故障排除与应急响应

1.  **故障排除流程**：
    *   建立标准的故障排除流程，从日志分析、性能指标检查、连接测试等方面入手。
    *   利用数据库诊断工具。

2.  **应急响应计划**：
    *   针对常见的数据库故障（如服务宕机、数据损坏、性能急剧下降等）制定应急响应预案。
    *   明确响应团队、沟通渠道和恢复步骤。

**注意：**
*   数据库的部署和维护是一个专业且细致的工作，建议由经验丰富的数据库管理员 (DBA) 负责或提供指导。
*   所有重要的配置更改和维护操作都应在测试环境中验证，并在生产环境中谨慎执行，最好在业务低峰期进行。
*   保持相关文档的更新，记录部署配置、维护历史和故障处理过程。