/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/topic/module.go
 * @Description: DI bindings for the topic module.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package topic

import (
	"gacms/internal/modules/topic/application/service"
	"gacms/internal/modules/topic/domain/contract"
	"gacms/internal/modules/topic/infrastructure/persistence"
	"gacms/internal/modules/topic/port/http/controller"
	pkgContract "gacms/pkg/contract"
	"go.uber.org/fx"
)

var Module = fx.Options(
	// Provide repositories from infrastructure to domain interfaces
	fx.Provide(
		fx.Annotate(
			persistence.NewTopicGormRepository,
			fx.As(new(contract.TopicRepository)),
		),
	),

	// Provide application services
	fx.Provide(service.NewTopicService),

	// Provide controller and tag it as IRoutable
	fx.Provide(
		fx.Annotate(
			controller.NewTopicController,
			fx.As(new(pkgContract.IRoutable)),
			fx.ResultTags(`group:"routables"`),
		),
	),
) 