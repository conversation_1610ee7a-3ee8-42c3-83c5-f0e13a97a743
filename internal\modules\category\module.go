/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/category/module.go
 * @Description: Defines the category module, its components, and routes for DI.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package category

import (
	coreSvc "gacms/internal/core/service"
	"gacms/internal/modules/category/application/service"
	"gacms/internal/modules/category/domain/contract"
	"gacms/internal/modules/category/infrastructure/persistence"
	"gacms/internal/modules/category/port/http/controller"
	"go.uber.org/fx"
)

const ModuleName = "category"

// Recipe is the DI recipe for the category module.
var Recipe = coreSvc.ModuleRecipe{
	Name: ModuleName,
	Options: fx.Options(
		// Persistence
		fx.Provide(
			fx.Annotate(
				persistence.NewCategoryGormRepository,
				fx.As(new(contract.CategoryRepository)),
			),
		),
		// Application Services
		fx.Provide(
			service.NewCategoryService,
		),
		// HTTP Controller (provided for routing)
		fx.Provide(
			fx.Annotate(
				controller.NewCategoryController,
				fx.ResultTags(`name:"routable_controller"`),
			),
		),
	),
}