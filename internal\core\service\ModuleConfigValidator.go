/*
Author: <PERSON><PERSON>eh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package service

import (
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/hex"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io/ioutil"
	"path/filepath"
)

// ModuleConfigValidator 模块配置验证器
type ModuleConfigValidator struct {
	publicKeys map[string]*rsa.PublicKey // 信任的公钥列表
}

// ModuleConfigDeclaration 模块配置声明
type ModuleConfigDeclaration struct {
	ModuleName      string   `json:"module_name"`
	Version         string   `json:"version"`
	Author          string   `json:"author"`
	RequiresLicense bool     `json:"requires_license"`
	LicenseType     string   `json:"license_type"`     // "none", "official", "third_party"
	MinimumEdition  string   `json:"minimum_edition"`  // "community", "personal", "professional", "enterprise"
	Features        []string `json:"features"`
	Dependencies    []string `json:"dependencies"`
	
	// 签名信息
	Signature string `json:"signature"` // 配置的数字签名
	SignedBy  string `json:"signed_by"` // 签名者标识
}

// NewModuleConfigValidator 创建模块配置验证器
func NewModuleConfigValidator() *ModuleConfigValidator {
	validator := &ModuleConfigValidator{
		publicKeys: make(map[string]*rsa.PublicKey),
	}
	
	// 加载信任的公钥
	validator.loadTrustedPublicKeys()
	
	return validator
}

// loadTrustedPublicKeys 加载信任的公钥
func (v *ModuleConfigValidator) loadTrustedPublicKeys() {
	// 加载官方公钥
	if officialKey, err := v.loadPublicKeyFromPEM(getOfficialPublicKeyPEM()); err == nil {
		v.publicKeys["official"] = officialKey
	}
	
	// 加载社区公钥
	if communityKey, err := v.loadPublicKeyFromPEM(getCommunityPublicKeyPEM()); err == nil {
		v.publicKeys["community"] = communityKey
	}
	
	// TODO: 可以从配置文件或远程服务器加载更多公钥
}

// ValidateModuleConfig 验证模块配置
func (v *ModuleConfigValidator) ValidateModuleConfig(modulePath string) (*ModuleConfigDeclaration, error) {
	// 1. 读取模块配置文件
	configPath := filepath.Join(modulePath, "module.json")
	configData, err := ioutil.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read module config: %w", err)
	}
	
	// 2. 解析配置
	var config ModuleConfigDeclaration
	if err := json.Unmarshal(configData, &config); err != nil {
		return nil, fmt.Errorf("failed to parse module config: %w", err)
	}
	
	// 3. 验证签名
	if config.Signature != "" {
		if err := v.verifyConfigSignature(&config, configData); err != nil {
			return nil, fmt.Errorf("config signature verification failed: %w", err)
		}
	}
	
	return &config, nil
}

// verifyConfigSignature 验证配置签名
func (v *ModuleConfigValidator) verifyConfigSignature(config *ModuleConfigDeclaration, configData []byte) error {
	// 获取对应的公钥
	publicKey, exists := v.publicKeys[config.SignedBy]
	if !exists {
		return fmt.Errorf("unknown signer: %s", config.SignedBy)
	}
	
	// 计算配置数据的哈希（排除签名字段）
	configCopy := *config
	configCopy.Signature = "" // 排除签名字段
	
	configBytes, err := json.Marshal(configCopy)
	if err != nil {
		return fmt.Errorf("failed to marshal config for verification: %w", err)
	}
	
	hash := sha256.Sum256(configBytes)
	
	// 解码签名
	signature, err := hex.DecodeString(config.Signature)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %w", err)
	}
	
	// 验证签名
	if err := rsa.VerifyPKCS1v15(publicKey, 0, hash[:], signature); err != nil {
		return fmt.Errorf("signature verification failed: %w", err)
	}
	
	return nil
}

// loadPublicKeyFromPEM 从PEM格式加载公钥
func (v *ModuleConfigValidator) loadPublicKeyFromPEM(pemData string) (*rsa.PublicKey, error) {
	block, _ := pem.Decode([]byte(pemData))
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}
	
	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse public key: %w", err)
	}
	
	rsaPub, ok := pub.(*rsa.PublicKey)
	if !ok {
		return nil, fmt.Errorf("not an RSA public key")
	}
	
	return rsaPub, nil
}

// IsModuleFree 检查模块是否免费（基于签名验证的配置）
func (v *ModuleConfigValidator) IsModuleFree(modulePath string) (bool, error) {
	config, err := v.ValidateModuleConfig(modulePath)
	if err != nil {
		// 如果没有配置文件或验证失败，默认需要许可证
		return false, err
	}
	
	// 只有经过签名验证的配置才能声明免费
	return !config.RequiresLicense, nil
}

// GetModuleLicenseRequirement 获取模块的许可证要求
func (v *ModuleConfigValidator) GetModuleLicenseRequirement(modulePath string) (*ModuleConfigDeclaration, error) {
	return v.ValidateModuleConfig(modulePath)
}

// getOfficialPublicKeyPEM 获取官方公钥PEM
func getOfficialPublicKeyPEM() string {
	// 这里应该包含官方的RSA公钥
	// 在实际部署中，这个公钥应该被混淆或加密存储
	return `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdef...
-----END PUBLIC KEY-----`
}

// getCommunityPublicKeyPEM 获取社区公钥PEM
func getCommunityPublicKeyPEM() string {
	// 社区免费模块的验证公钥
	return `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAabcdef1234567890...
-----END PUBLIC KEY-----`
}

// AddTrustedPublicKey 添加信任的公钥
func (v *ModuleConfigValidator) AddTrustedPublicKey(name string, publicKey *rsa.PublicKey) {
	v.publicKeys[name] = publicKey
}

// RemoveTrustedPublicKey 移除信任的公钥
func (v *ModuleConfigValidator) RemoveTrustedPublicKey(name string) {
	delete(v.publicKeys, name)
}

// GetTrustedSigners 获取信任的签名者列表
func (v *ModuleConfigValidator) GetTrustedSigners() []string {
	var signers []string
	for name := range v.publicKeys {
		signers = append(signers, name)
	}
	return signers
}

// ValidateModuleIntegrity 验证模块完整性
func (v *ModuleConfigValidator) ValidateModuleIntegrity(modulePath string) error {
	// 1. 验证配置文件
	config, err := v.ValidateModuleConfig(modulePath)
	if err != nil {
		return fmt.Errorf("config validation failed: %w", err)
	}
	
	// 2. 验证模块文件完整性
	if err := v.validateModuleFiles(modulePath, config); err != nil {
		return fmt.Errorf("file integrity validation failed: %w", err)
	}
	
	return nil
}

// validateModuleFiles 验证模块文件完整性
func (v *ModuleConfigValidator) validateModuleFiles(modulePath string, config *ModuleConfigDeclaration) error {
	// TODO: 实现文件完整性检查
	// 1. 计算关键文件的哈希
	// 2. 与配置中的预期哈希比较
	// 3. 检查是否有未授权的文件修改
	
	return nil
}

// CreateModuleConfigTemplate 创建模块配置模板
func (v *ModuleConfigValidator) CreateModuleConfigTemplate(moduleName, author string, requiresLicense bool) *ModuleConfigDeclaration {
	return &ModuleConfigDeclaration{
		ModuleName:      moduleName,
		Version:         "1.0.0",
		Author:          author,
		RequiresLicense: requiresLicense,
		LicenseType:     "none",
		MinimumEdition:  "community",
		Features:        []string{},
		Dependencies:    []string{},
		Signature:       "", // 需要开发者签名
		SignedBy:        "", // 需要指定签名者
	}
}
