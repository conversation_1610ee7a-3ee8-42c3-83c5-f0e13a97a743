<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 评论审核</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .content-section-title::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 5rem;
            height: 3px;
            background: linear-gradient(to right, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            background: linear-gradient(145deg, #1e1e1e, #2a2a2a);
            border-left: 4px solid #007bff;
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            backdrop-filter: blur(20px);
            z-index: 1000;
            transform: translateX(120%);
            opacity: 0;
            transition: all 0.2s ease;
            max-width: 350px;
        }
        
        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="comments.html" class="hover:text-white">评论管理</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">评论审核</span>
            </div>

            <!-- 评论审核主内容 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">评论审核</h2>
                    <div class="flex space-x-2">
                        <span class="text-sm text-gray-400">待审核评论：<span class="text-blue-400 font-medium">32</span> 条</span>
                    </div>
                </div>
                
                <!-- 筛选工具栏 -->
                <div class="flex flex-wrap gap-4 mb-6">
                    <div class="flex items-center space-x-2">
                        <label for="filter-status" class="text-gray-400 text-sm">状态:</label>
                        <select id="filter-status" class="bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-sm text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="pending" selected>待审核</option>
                            <option value="approved">已通过</option>
                            <option value="rejected">已拒绝</option>
                            <option value="all">全部</option>
                        </select>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <label for="filter-article" class="text-gray-400 text-sm">文章:</label>
                        <select id="filter-article" class="bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-sm text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all" selected>全部文章</option>
                            <option value="1">2025年内容营销趋势</option>
                            <option value="2">如何优化网站SEO</option>
                            <option value="3">最新前端框架对比</option>
                        </select>
                    </div>
                    
                    <div class="flex-1"></div>
                    
                    <div class="relative">
                        <input type="text" placeholder="搜索评论内容、用户名..." class="bg-gray-700 border border-gray-600 rounded-md pl-10 pr-4 py-2 w-64 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>
                
                <!-- 评论列表 -->
                <div class="space-y-6">
                    <!-- 评论项 1 (待审核) -->
                    <div class="bg-gray-800/20 border border-l-4 border-yellow-500/70 rounded-lg p-5">
                        <div class="flex flex-wrap lg:flex-nowrap">
                            <!-- 评论用户信息 -->
                            <div class="w-full lg:w-52 flex-shrink-0 mb-4 lg:mb-0 lg:mr-6">
                                <div class="flex items-center">
                                    <img src="./assets/images/avatar-1.jpg" alt="User Avatar" class="w-10 h-10 rounded-full object-cover">
                                    <div class="ml-3">
                                        <h3 class="text-white font-medium">李华</h3>
                                        <p class="text-gray-400 text-sm"><EMAIL></p>
                                    </div>
                                </div>
                                <div class="mt-3 text-xs text-gray-500">
                                    <p><i class="far fa-clock mr-1"></i> 2025-06-05 13:45</p>
                                    <p><i class="fas fa-map-marker-alt mr-1"></i> 192.168.1.1</p>
                                </div>
                            </div>
                            
                            <!-- 评论内容 -->
                            <div class="flex-1">
                                <div class="border-b border-gray-700 pb-3 mb-3">
                                    <a href="article_edit.html?id=3" class="text-blue-400 hover:text-blue-300 font-medium text-sm">
                                        <i class="fas fa-file-alt mr-1"></i> 文章: 《2025年内容营销趋势》
                                    </a>
                                    <div class="mt-3">
                                        <p class="text-gray-200">这篇文章的观点非常有见地，对于内容营销的趋势分析很到位。我特别认同关于视频内容将成为主流的预测，但是我觉得文中对播客的潜力估计不足。根据最近的数据，播客的增长率远超预期。</p>
                                    </div>
                                </div>
                                
                                <!-- 回复表单 -->
                                <div class="flex gap-3">
                                    <div class="flex-1">
                                        <input type="text" placeholder="添加回复..." class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                    <button class="action-button bg-gradient-to-r from-green-500 to-green-600 text-white py-2 px-4 rounded-lg hover:from-green-600 hover:to-green-700 transition-all relative overflow-hidden">
                                        通过并回复
                                    </button>
                                </div>
                                
                                <!-- 操作按钮 -->
                                <div class="flex flex-wrap gap-2 mt-3">
                                    <button class="bg-green-500/20 hover:bg-green-500/30 text-green-400 rounded-md px-3 py-1.5 text-sm flex items-center transition-colors">
                                        <i class="fas fa-check mr-1.5"></i> 通过
                                    </button>
                                    <button class="bg-red-500/20 hover:bg-red-500/30 text-red-400 rounded-md px-3 py-1.5 text-sm flex items-center transition-colors">
                                        <i class="fas fa-times mr-1.5"></i> 拒绝
                                    </button>
                                    <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-md px-3 py-1.5 text-sm flex items-center transition-colors">
                                        <i class="fas fa-envelope mr-1.5"></i> 邮件通知
                                    </button>
                                    <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-md px-3 py-1.5 text-sm flex items-center transition-colors">
                                        <i class="fas fa-pen mr-1.5"></i> 编辑
                                    </button>
                                    <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-md px-3 py-1.5 text-sm flex items-center transition-colors" id="toggleReasonBtn1">
                                        <i class="fas fa-plus mr-1.5"></i> 添加拒绝理由
                                    </button>
                                </div>
                                
                                <!-- 拒绝理由表单 (默认隐藏) -->
                                <div class="mt-3 hidden" id="rejectReasonForm1">
                                    <textarea placeholder="请输入拒绝理由..." rows="2" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                                    <div class="mt-2 flex justify-end gap-2">
                                        <button class="bg-red-500/20 hover:bg-red-500/30 text-red-400 rounded-md px-3 py-1.5 text-sm">确认拒绝</button>
                                        <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-md px-3 py-1.5 text-sm cancel-reason-btn">取消</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 评论项 2 (待审核) -->
                    <div class="bg-gray-800/20 border border-l-4 border-yellow-500/70 rounded-lg p-5">
                        <div class="flex flex-wrap lg:flex-nowrap">
                            <!-- 评论用户信息 -->
                            <div class="w-full lg:w-52 flex-shrink-0 mb-4 lg:mb-0 lg:mr-6">
                                <div class="flex items-center">
                                    <img src="./assets/images/avatar-2.jpg" alt="User Avatar" class="w-10 h-10 rounded-full object-cover">
                                    <div class="ml-3">
                                        <h3 class="text-white font-medium">张小明</h3>
                                        <p class="text-gray-400 text-sm"><EMAIL></p>
                                    </div>
                                </div>
                                <div class="mt-3 text-xs text-gray-500">
                                    <p><i class="far fa-clock mr-1"></i> 2025-06-05 09:23</p>
                                    <p><i class="fas fa-map-marker-alt mr-1"></i> 192.168.1.105</p>
                                </div>
                            </div>
                            
                            <!-- 评论内容 -->
                            <div class="flex-1">
                                <div class="border-b border-gray-700 pb-3 mb-3">
                                    <a href="article_edit.html?id=2" class="text-blue-400 hover:text-blue-300 font-medium text-sm">
                                        <i class="fas fa-file-alt mr-1"></i> 文章: 《如何优化网站SEO》
                                    </a>
                                    <div class="mt-3">
                                        <p class="text-gray-200">这些SEO技巧我已经尝试了很久，但效果并不明显。你们的文章没有提到Google最新的算法更新对这些策略的影响。建议更新一下内容，增加一些关于Core Web Vitals的讨论。</p>
                                    </div>
                                </div>
                                
                                <!-- 操作按钮 -->
                                <div class="flex flex-wrap gap-2">
                                    <button class="bg-green-500/20 hover:bg-green-500/30 text-green-400 rounded-md px-3 py-1.5 text-sm flex items-center transition-colors">
                                        <i class="fas fa-check mr-1.5"></i> 通过
                                    </button>
                                    <button class="bg-red-500/20 hover:bg-red-500/30 text-red-400 rounded-md px-3 py-1.5 text-sm flex items-center transition-colors">
                                        <i class="fas fa-times mr-1.5"></i> 拒绝
                                    </button>
                                    <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-md px-3 py-1.5 text-sm flex items-center transition-colors">
                                        <i class="fas fa-reply mr-1.5"></i> 回复
                                    </button>
                                    <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-md px-3 py-1.5 text-sm flex items-center transition-colors">
                                        <i class="fas fa-pen mr-1.5"></i> 编辑
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 分页控件 -->
                <div class="mt-6 flex justify-between items-center">
                    <div class="text-sm text-gray-400">
                        显示 <span class="text-white">1-10</span> 条，共 <span class="text-white">32</span> 条
                    </div>
                    <div class="flex space-x-1">
                        <a href="#" class="px-3 py-1 rounded border border-gray-700 bg-gray-800/20 text-gray-400 hover:bg-gray-800/40 hover:text-white transition-colors">
                            <i class="fas fa-chevron-left text-xs"></i>
                        </a>
                        <a href="#" class="px-3 py-1 rounded border border-blue-500 bg-blue-500/20 text-white">1</a>
                        <a href="#" class="px-3 py-1 rounded border border-gray-700 bg-gray-800/20 text-gray-400 hover:bg-gray-800/40 hover:text-white transition-colors">2</a>
                        <a href="#" class="px-3 py-1 rounded border border-gray-700 bg-gray-800/20 text-gray-400 hover:bg-gray-800/40 hover:text-white transition-colors">3</a>
                        <a href="#" class="px-3 py-1 rounded border border-gray-700 bg-gray-800/20 text-gray-400 hover:bg-gray-800/40 hover:text-white transition-colors">4</a>
                        <a href="#" class="px-3 py-1 rounded border border-gray-700 bg-gray-800/20 text-gray-400 hover:bg-gray-800/40 hover:text-white transition-colors">
                            <i class="fas fa-chevron-right text-xs"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <!-- 通知组件 -->
    <div id="notification" class="notification">
        <div class="mr-3">
            <i class="fas fa-check-circle text-xl text-green-500"></i>
        </div>
        <div class="flex-1">
            <h4 class="font-semibold text-white">操作成功</h4>
            <p class="text-sm text-gray-300">评论审核状态已更新</p>
        </div>
    </div>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <script>
        // 切换拒绝理由表单显示/隐藏
        document.addEventListener('DOMContentLoaded', function() {
            const toggleReasonBtn = document.getElementById('toggleReasonBtn1');
            const rejectReasonForm = document.getElementById('rejectReasonForm1');
            
            if (toggleReasonBtn && rejectReasonForm) {
                toggleReasonBtn.addEventListener('click', function() {
                    rejectReasonForm.classList.toggle('hidden');
                });
            }
            
            const cancelReasonBtns = document.querySelectorAll('.cancel-reason-btn');
            cancelReasonBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    this.closest('div[id^="rejectReasonForm"]').classList.add('hidden');
                });
            });
            
            // 示例：显示通知
            const showNotificationButtons = document.querySelectorAll('.bg-green-500\\/20');
            const notification = document.getElementById('notification');
            
            showNotificationButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    notification.classList.add('show');
                    setTimeout(() => {
                        notification.classList.remove('show');
                    }, 3000);
                });
            });
        });
    </script>
</body>
</html> 