/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/modules/user/application/service/UserService.go
 * @Description: 用户服务实现
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"errors"
	"time"

	"gacms/internal/modules/user/domain/contract"
	"gacms/internal/modules/user/domain/model"
	"gacms/internal/modules/user/events"
	eventContract "gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// UserService 用户服务实现
type UserService struct {
	userRepository contract.UserRepository
	eventManager   eventContract.EventManager
	logger         *zap.Logger
}

// UserServiceParams 定义了创建 UserService 所需的参数
type UserServiceParams struct {
	fx.In

	UserRepository contract.UserRepository
	EventManager   eventContract.EventManager
	Logger         *zap.Logger
}

// NewUserService 创建一个新的 UserService 实例
func NewUserService(params UserServiceParams) contract.UserService {
	return &UserService{
		userRepository: params.UserRepository,
		eventManager:   params.EventManager,
		logger:         params.Logger,
	}
}

// Register 注册新用户
func (s *UserService) Register(ctx context.Context, input contract.RegisterUserInput) (*model.User, error) {
	// 检查用户名是否已存在
	exists, err := s.userRepository.ExistsByUsername(ctx, input.Username)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, errors.New("username already exists")
	}

	// 检查邮箱是否已存在
	exists, err = s.userRepository.ExistsByEmail(ctx, input.Email)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, errors.New("email already exists")
	}

	// 创建用户
	user := &model.User{
		Username:  input.Username,
		Email:     input.Email,
		Password:  input.Password, // 实际应用中应该先哈希处理
		Status:    model.UserStatusPending,
		CreatedAt: time.Now(),
		SiteID:    input.SiteID,
	}

	// 保存用户
	err = s.userRepository.Create(ctx, user)
	if err != nil {
		return nil, err
	}

	// 发布用户注册事件
	s.publishUserRegisteredEvent(ctx, user)

	return user, nil
}

// Login 用户登录
func (s *UserService) Login(ctx context.Context, input contract.LoginUserInput) (*model.User, error) {
	// 根据用户名查找用户
	user, err := s.userRepository.FindByUsername(ctx, input.Username)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, errors.New("user not found")
	}

	// 检查用户状态
	if user.Status != model.UserStatusActive {
		return nil, errors.New("user is not active")
	}

	// 验证密码（实际应用中应该比较哈希值）
	if user.Password != input.Password {
		return nil, errors.New("invalid password")
	}

	// 更新最后登录时间
	user.LastLoginAt = time.Now()
	err = s.userRepository.Update(ctx, user)
	if err != nil {
		return nil, err
	}

	// 发布用户登录事件
	s.publishUserLoggedInEvent(ctx, user, input.IP, input.UserAgent)

	return user, nil
}

// UpdateProfile 更新用户资料
func (s *UserService) UpdateProfile(ctx context.Context, input contract.UpdateUserProfileInput) (*model.User, error) {
	// 查找用户
	user, err := s.userRepository.FindByID(ctx, input.UserID)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, errors.New("user not found")
	}

	// 记录变更字段
	changedFields := make(map[string]interface{})

	// 更新用户资料
	if input.Nickname != "" && input.Nickname != user.Nickname {
		changedFields["nickname"] = input.Nickname
		user.Nickname = input.Nickname
	}

	if input.Avatar != "" && input.Avatar != user.Avatar {
		changedFields["avatar"] = input.Avatar
		user.Avatar = input.Avatar
	}

	if input.Bio != "" && input.Bio != user.Bio {
		changedFields["bio"] = input.Bio
		user.Bio = input.Bio
	}

	// 如果没有变更，直接返回
	if len(changedFields) == 0 {
		return user, nil
	}

	// 更新用户
	user.UpdatedAt = time.Now()
	err = s.userRepository.Update(ctx, user)
	if err != nil {
		return nil, err
	}

	// 发布用户资料更新事件
	s.publishUserProfileUpdatedEvent(ctx, user, changedFields, input.UpdatedBy)

	return user, nil
}

// 发布用户注册事件
func (s *UserService) publishUserRegisteredEvent(ctx context.Context, user *model.User) {
	payload := &events.UserRegisteredPayload{
		UserID:    user.ID,
		Username:  user.Username,
		Email:     user.Email,
		CreatedAt: user.CreatedAt,
		SiteID:    user.SiteID,
	}

	event := s.eventManager.CreateEvent(ctx, events.UserRegistered, payload)
	err := s.eventManager.PublishEvent(event)
	if err != nil {
		s.logger.Error("Failed to publish user registered event",
			zap.String("userId", user.ID),
			zap.Error(err),
		)
	}
}

// 发布用户登录事件
func (s *UserService) publishUserLoggedInEvent(ctx context.Context, user *model.User, ip, userAgent string) {
	payload := &events.UserLoggedInPayload{
		UserID:    user.ID,
		Username:  user.Username,
		LoginAt:   time.Now(),
		IP:        ip,
		UserAgent: userAgent,
		SiteID:    user.SiteID,
	}

	event := s.eventManager.CreateEvent(ctx, events.UserLoggedIn, payload)
	err := s.eventManager.PublishEvent(event)
	if err != nil {
		s.logger.Error("Failed to publish user logged in event",
			zap.String("userId", user.ID),
			zap.Error(err),
		)
	}
}

// 发布用户资料更新事件
func (s *UserService) publishUserProfileUpdatedEvent(ctx context.Context, user *model.User, changedFields map[string]interface{}, updatedBy string) {
	payload := &events.UserProfileUpdatedPayload{
		UserID:        user.ID,
		Username:      user.Username,
		UpdatedAt:     user.UpdatedAt,
		UpdatedBy:     updatedBy,
		ChangedFields: changedFields,
		SiteID:        user.SiteID,
	}

	event := s.eventManager.CreateEvent(ctx, events.UserProfileUpdated, payload)
	err := s.eventManager.PublishEvent(event)
	if err != nil {
		s.logger.Error("Failed to publish user profile updated event",
			zap.String("userId", user.ID),
			zap.Error(err),
		)
	}
} 