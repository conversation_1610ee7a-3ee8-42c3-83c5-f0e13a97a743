/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON> Nieh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/admin/application/service/AdminService.go
 * @Description: Implements the application logic for the Admin module by calling other modules' APIs.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

// AdminService provides application-level services for the admin backend.
// It acts as an API client to other modules to aggregate data.
type AdminService struct {
	httpClient         *http.Client
	userServiceBaseURL string
	postServiceBaseURL string
	menuServiceBaseURL string
	themeServiceBaseURL string
}

// NewAdminService creates a new instance of AdminService.
func NewAdminService() *AdminService {
	return &AdminService{
		httpClient: &http.Client{
			Timeout: 5 * time.Second,
		},
		// These URLs are placeholders and should be configurable.
		userServiceBaseURL:  "http://localhost:8080/api/v1/user",
		postServiceBaseURL:  "http://localhost:8080/api/v1/post",
		menuServiceBaseURL:  "http://localhost:8080/api/v1/menu",
		themeServiceBaseURL: "http://localhost:8080/api/v1/theme",
	}
}

// GetDashboardStats fetches data from various other modules and aggregates it.
func (s *AdminService) GetDashboardStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	userStats, err := s.getUserStats(ctx)
	if err != nil {
		return nil, err
	}
	stats["totalUsers"] = userStats["total"]

	stats["totalPosts"] = 0 // Placeholder

	return stats, nil
}

// getUserStats performs the actual API call to the user service.
func (s *AdminService) getUserStats(ctx context.Context) (map[string]interface{}, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", s.userServiceBaseURL+"/admins/stats", nil)
	if err != nil {
		return nil, err
	}

	if authHeader := ctx.Value("Authorization"); authHeader != nil {
		if headerStr, ok := authHeader.(string); ok {
			req.Header.Set("Authorization", headerStr)
		}
	}

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, err
	}

	return result, nil
}

// GetAdminMenus fetches the menus for the admin panel.
func (s *AdminService) GetAdminMenus(ctx context.Context, siteID uint) ([]interface{}, error) {
	url := fmt.Sprintf("%s/menus?site_id=%d", s.menuServiceBaseURL, siteID)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, err
	}
	
	if authHeader := ctx.Value("Authorization"); authHeader != nil {
		if headerStr, ok := authHeader.(string); ok {
			req.Header.Set("Authorization", headerStr)
		}
	}

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	var result struct {
		Data []interface{} `json:"data"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, err
	}

	return result.Data, nil
}

// GetAppearanceSettings fetches the appearance settings for the admin panel.
func (s *AdminService) GetAppearanceSettings(ctx context.Context, siteID uint, themeName string) (map[string]interface{}, error) {
	url := fmt.Sprintf("%s/settings?siteId=%d&themeName=%s", s.themeServiceBaseURL, siteID, themeName)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, err
	}
	
	if authHeader := ctx.Value("Authorization"); authHeader != nil {
		if headerStr, ok := authHeader.(string); ok {
			req.Header.Set("Authorization", headerStr)
		}
	}
	
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var result struct {
		Data map[string]interface{} `json:"data"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, err
	}

	return result.Data, nil
} 