/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-09
 * @LastEditors: <PERSON><PERSON> Nieh
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/contenttype/application/service/ContentItemService.go
 * @Description: Service for managing content item data.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"encoding/json"
	"errors"
	"gacms/internal/modules/contenttype/domain/contract"
	"gacms/internal/modules/contenttype/domain/model"
)

// ContentItemService provides business logic for managing content items.
type ContentItemService struct {
	repo       contract.ContentItemRepository
	typeSvc    *ContentTypeService
	// siteRepo   contract.SiteRepository // Needed for site validation
}

// NewContentItemService creates a new ContentItemService.
func NewContentItemService(repo contract.ContentItemRepository, typeSvc *ContentTypeService) *ContentItemService {
	return &ContentItemService{
		repo:    repo,
		typeSvc: typeSvc,
	}
}

// CreateContentItem creates a new content item for a specific site and content type.
// It validates the incoming data against the site-specific content type definition.
func (s *ContentItemService) CreateContentItem(siteID uint, contentTypeSlug string, data map[string]interface{}) (*model.ContentItem, error) {
	// 1. Get the site-specific content type definition
	contentType, err := s.typeSvc.GetBySlugForSite(contentTypeSlug, siteID)
	if err != nil {
		return nil, err
	}

	// 2. Validate data against the schema (placeholder for actual validation logic)
	if err := s.validateData(contentType.Fields, data); err != nil {
		return nil, err
	}

	// 3. Marshal data to JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	// 4. Create and save the content item
	item := &model.ContentItem{
		ContentTypeID: contentType.ID,
		SiteID:        siteID,
		Data:          jsonData,
	}

	if err := s.repo.Create(item); err != nil {
		return nil, err
	}

	return item, nil
}

func (s *ContentItemService) GetItemByID(id uint) (*model.ContentItem, error) {
	return s.repo.FindByID(id)
}

func (s *ContentItemService) GetItemsByContentTypeSlug(siteID uint, contentTypeSlug string) ([]*model.ContentItem, error) {
	contentType, err := s.typeSvc.GetBySlug(contentTypeSlug) // For getting ID, base is fine
	if err != nil {
		return nil, err
	}
	return s.repo.FindAll(siteID, contentType.ID)
}

// validateData checks if the provided data conforms to the field definitions.
// This is a placeholder and should be expanded into a full validation engine.
func (s *ContentItemService) validateData(fields []model.Field, data map[string]interface{}) error {
	for _, field := range fields {
		if field.IsRequired {
			if _, ok := data[field.Slug]; !ok {
				return errors.New("missing required field: " + field.Slug)
			}
		}
		// Add more validation logic here (type checks, length, regex, etc.)
	}
	return nil
}

// ... other methods to wrap repository functions for finding/deleting items ...
func (s *ContentItemService) GetItem(id uint) (*model.ContentItem, error) {
    return s.repo.FindByID(id)
}

func (s *ContentItemService) GetItems(contentTypeID, siteID uint, options contract.QueryOptions) ([]*model.ContentItem, int64, error) {
    return s.repo.FindAll(contentTypeID, siteID, options)
}

func (s *ContentItemService) DeleteItem(id uint) error {
    return s.repo.Delete(id)
}

func (s *ContentItemService) GetItemsByIDs(ids []uint) ([]*model.ContentItem, error) {
	return s.repo.FindBatchByIDs(ids)
} 