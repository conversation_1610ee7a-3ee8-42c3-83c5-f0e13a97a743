/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/system/port/http/controller/SettingController.go
 * @Description: Controller for system settings.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package controller

import (
	"gacms/internal/modules/system/application/dto"
	"gacms/internal/modules/system/domain/contract"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type SettingController struct {
	svc contract.SettingService
}

func NewSettingController(svc contract.SettingService) *SettingController {
	return &SettingController{svc: svc}
}

// RegisterRoutes registers the routes for system and site settings.
// Note: These routes are now site-aware.
func (c *SettingController) RegisterRoutes(rg *gin.RouterGroup) {
	// Global settings (no site context)
	globalSettings := rg.Group("/system/settings")
	{
		globalSettings.GET("/:group", c.getGlobalSettingsByGroup)
		globalSettings.PUT("/:group", c.updateGlobalSettingsByGroup)
	}

	// Site-specific settings
	siteSettings := rg.Group("/sites/:siteId/settings")
	{
		siteSettings.GET("/:group", c.getSiteSettingsByGroup)
		siteSettings.PUT("/:group", c.updateSiteSettingsByGroup)
	}
}

func (c *SettingController) getGlobalSettingsByGroup(ctx *gin.Context) {
	group := ctx.Param("group")
	settings, err := c.svc.GetSettingsByGroup(nil, group) // siteID = nil for global
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"code": 0, "message": "Success", "data": settings})
}

func (c *SettingController) updateGlobalSettingsByGroup(ctx *gin.Context) {
	group := ctx.Param("group")
	var settingsToUpdate []*dto.SettingDTO
	if err := ctx.ShouldBindJSON(&settingsToUpdate); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	err := c.svc.UpdateSettingsByGroup(nil, group, settingsToUpdate) // siteID = nil for global
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"code": 0, "message": "Global settings updated successfully"})
}

func (c *SettingController) getSiteSettingsByGroup(ctx *gin.Context) {
	group := ctx.Param("group")
	siteID, err := strconv.ParseUint(ctx.Param("siteId"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid site ID"})
		return
	}
	siteIDUint := uint(siteID)
	settings, err := c.svc.GetSettingsByGroup(&siteIDUint, group)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"code": 0, "message": "Success", "data": settings})
}

func (c *SettingController) updateSiteSettingsByGroup(ctx *gin.Context) {
	group := ctx.Param("group")
	siteID, err := strconv.ParseUint(ctx.Param("siteId"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid site ID"})
		return
	}
	siteIDUint := uint(siteID)
	var settingsToUpdate []*dto.SettingDTO
	if err := ctx.ShouldBindJSON(&settingsToUpdate); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	err = c.svc.UpdateSettingsByGroup(&siteIDUint, group, settingsToUpdate)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"code": 0, "message": "Site settings updated successfully"})
} 