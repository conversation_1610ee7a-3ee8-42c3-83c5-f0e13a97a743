# GACMS Global Security Configuration
# All security-related settings should be defined here.
# It is strongly recommended to override sensitive values using environment variables.

auth:
  jwt:
    # A strong, secret key for signing JWT tokens.
    # WARNING: This is a default key. CHANGE IT for production environments.
    # Recommended to be set via GACMS_AUTH_JWT_SECRET environment variable.
    secret: "gacms-default-secret-key-please-change-me-in-production"
    # Token expiry time in hours.
    expiry_hours: 72

cors:
  # List of allowed origins. Use "*" for development only.
  allowed_origins:
    - "http://localhost:3000"
    - "http://127.0.0.1:3000" 