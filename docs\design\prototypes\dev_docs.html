<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 开发文档</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <!-- highlight.js CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/monokai.min.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        .docs-content h2 { @apply text-2xl font-bold text-white mt-8 mb-4 border-b border-gray-700 pb-2; }
        .docs-content h3 { @apply text-xl font-semibold text-white mt-6 mb-3; }
        .docs-content p { @apply text-gray-300 mb-4 leading-relaxed; }
        .docs-content a { @apply text-blue-400 hover:underline; }
        .docs-content code { @apply bg-gray-900 text-sm rounded-md px-2 py-1; }
        .docs-content pre { @apply bg-gray-900 rounded-lg p-4 overflow-x-auto; }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: { 500: '#007bff', 600: '#0069d9' },
                        secondary: { 500: '#00c6ff' },
                        dark: { 800: '#1F2937', 900: '#1A1A1A' }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>

        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <div class="grid grid-cols-12 gap-8">
                <!-- Left Nav -->
                <aside class="col-span-2">
                    <div class="sticky top-20">
                        <h3 class="text-lg font-semibold text-white mb-4">文档导航</h3>
                        <ul class="space-y-2">
                            <li><a href="#getting-started" class="text-gray-300 hover:text-white">快速入门</a></li>
                            <li>
                                <a href="#api-reference" class="text-gray-300 hover:text-white">API 参考</a>
                                <ul class="pl-4 mt-2 space-y-1 text-sm">
                                    <li><a href="#api-auth" class="text-gray-400 hover:text-white">身份认证</a></li>
                                    <li><a href="#api-posts" class="text-gray-400 hover:text-white">文章接口</a></li>
                                    <li><a href="#api-users" class="text-gray-400 hover:text-white">用户接口</a></li>
                                </ul>
                            </li>
                            <li><a href="#theme-development" class="text-gray-300 hover:text-white">主题开发</a></li>
                            <li><a href="#plugin-development" class="text-gray-300 hover:text-white">插件开发</a></li>
                        </ul>
                    </div>
                </aside>

                <!-- Main Content -->
                <article class="col-span-8 docs-content">
                    <section id="getting-started">
                        <h2>快速入门</h2>
                        <p>欢迎来到 GACMS 开发文档。这里将引导您完成从安装、配置到进行二次开发的整个过程。</p>
                    </section>
                    
                    <section id="api-reference">
                        <h2>API 参考</h2>
                        <p>GACMS 提供了一套完整的 RESTful API，方便您进行前后端分离开发或与其他系统集成。</p>
                        <h3 id="api-auth">身份认证</h3>
                        <p>所有API请求都需要通过 Token 进行认证。您需要在请求头中加入 <code>Authorization: Bearer YOUR_API_TOKEN</code>。</p>
                        <pre><code class="language-bash">curl -H "Authorization: Bearer your_token_here" https://yourdomain.com/api/v1/posts</code></pre>
                        
                        <h3 id="api-posts">文章接口</h3>
                        <p>用于管理文章资源。</p>
                        <h4>GET /api/v1/posts</h4>
                        <p>获取文章列表，支持分页和筛选。</p>
                        <pre><code class="language-json">{
  "data": [
    {
      "id": 1,
      "title": "Hello World",
      "slug": "hello-world",
      "content": "Welcome to GACMS."
    }
  ],
  "meta": {
    "total": 1,
    "per_page": 15,
    "current_page": 1
  }
}</code></pre>
                    </section>
                    
                     <section id="theme-development">
                        <h2>主题开发</h2>
                        <p>GACMS 的主题系统非常灵活，您可以轻松创建自定义主题...</p>
                    </section>
                </article>

                <!-- Right Nav -->
                <aside class="col-span-2">
                    <div class="sticky top-20">
                        <h3 class="text-lg font-semibold text-white mb-4">本页导航</h3>
                        <ul class="space-y-2 text-sm">
                           <li><a href="#api-auth" class="text-gray-400 hover:text-white">身份认证</a></li>
                           <li><a href="#api-posts" class="text-gray-400 hover:text-white">文章接口</a></li>
                        </ul>
                    </div>
                </aside>
            </div>
        </div>

        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <!-- highlight.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <script>hljs.highlightAll();</script>
</body>
</html> 