/*
Author: <PERSON><PERSON> Nieh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package constants

import (
	"gacms/pkg/contract"
)

// ProfessionalEditionProvider 专业版配置提供者接口
type ProfessionalEditionProvider interface {
	GetFeatures() []string
	IsFeature(featureName string) bool
	GetLimit(limitType string) int
	GetEdition() contract.Edition
}

// professionalEditionConfig 专业版配置实现（私有）
type professionalEditionConfig struct {
	features []string
	limits   map[string]int
}

// 专业版配置（编译时从配置文件固化）
var professionalConfig = &professionalEditionConfig{
	features: []string{
		// 基础功能
		"basic_content", "basic_theme", "basic_seo", "basic_user",
		// 高级功能
		"advanced_theme", "advanced_seo", "workflow", "api_access",
	},
	limits: map[string]int{
		"sites":     5,     // 5个站点
		"users":     20,    // 20个用户
		"api_calls": 10000, // 10000次API调用/天
	},
}

// GetFeatures 获取专业版功能列表
func (p *professionalEditionConfig) GetFeatures() []string {
	return p.features
}

// IsFeature 检查是否是专业版功能
func (p *professionalEditionConfig) IsFeature(featureName string) bool {
	for _, feature := range p.features {
		if featureName == feature {
			return true
		}
	}
	return false
}

// GetLimit 获取专业版限制值
func (p *professionalEditionConfig) GetLimit(limitType string) int {
	switch limitType {
	case "sites", "max_sites":
		return p.limits["sites"]
	case "users", "max_users", "admin_users", "max_admin_users":
		return p.limits["users"]
	case "api_calls", "api_calls_per_day":
		return p.limits["api_calls"]
	default:
		return 0 // 其他限制不存在
	}
}

// GetEdition 获取版本
func (p *professionalEditionConfig) GetEdition() contract.Edition {
	return contract.EditionProfessional
}

// GetProfessionalEditionProvider 获取专业版配置提供者
func GetProfessionalEditionProvider() ProfessionalEditionProvider {
	return professionalConfig
}

// TODO: 后续需要实现从configs/editions.yaml读取配置并编译时固化的机制
// 目前先使用硬编码的配置，后续可以通过build时的代码生成来实现
