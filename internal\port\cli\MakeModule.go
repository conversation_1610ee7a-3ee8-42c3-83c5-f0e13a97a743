/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/port/cli/MakeModule.go
 * @Description: Defines the 'make:module' command and acts as an adapter to the ModuleService.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package cli

import (
	"fmt"
	"gacms/internal/modules/system/application/service"

	"github.com/spf13/cobra"
)

// NewMakeModuleCmd creates the 'make:module' command.
func NewMakeModuleCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "make:module [moduleName]",
		Short: "Create a new module with a standard directory structure.",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			// In a DI-driven app, this service would be injected.
			// For now, we instantiate it directly.
			moduleService := service.NewModuleService()
			
			err := moduleService.CreateStructure(args[0])
			if err != nil {
				return fmt.Errorf("failed to create module: %w", err)
			}
			return nil
		},
	}
	return cmd
} 