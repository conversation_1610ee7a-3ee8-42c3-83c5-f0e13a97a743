/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-13
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-13
 * @FilePath: internal/modules/user/application/service/PermissionService.go
 * @Description: Implements the permission checking logic for RBAC.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"errors"
	"gacms/internal/modules/user/domain/contract"
	"gacms/internal/modules/user/domain/model"
	"gacms/internal/modules/user/events"
	pkgContract "gacms/pkg/contract"
	"gacms/pkg/database"
	"gacms/pkg/logger"

	"context"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
)

var (
	ErrPermissionExists   = errors.New("permission with this slug already exists")
	ErrPermissionNotFound = errors.New("permission not found")
)

// ensure PermissionService implements the PermissionChecker interface.
var _ pkgContract.PermissionChecker = (*PermissionService)(nil)

// PermissionService provides methods for checking user permissions based on their roles.
type PermissionService struct {
	roleRepo     contract.RoleRepository
	adminRepo    contract.AdminRepository // Needed to check for super admin
	permRepo     contract.PermissionRepository
	appCtx       pkgContract.AppContext
}

// NewPermissionService creates a new instance of PermissionService.
func NewPermissionService(
	roleRepo contract.RoleRepository, 
	adminRepo contract.AdminRepository, 
	permRepo contract.PermissionRepository, 
	appCtx pkgContract.AppContext,
) *PermissionService {
	return &PermissionService{
		roleRepo:     roleRepo,
		adminRepo:    adminRepo,
		permRepo:     permRepo,
		appCtx:       appCtx,
	}
}

// Wrap takes a permission string and a handler, and returns a new handler
// that performs the permission check before executing the original handler.
func (s *PermissionService) Wrap(permission string, handler gin.HandlerFunc) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. Get user/role information from context (injected by AdminAuth middleware).
		userID, userExists := c.Get("userID")
		// roleIDs, rolesExist := c.Get("roleIDs") // Example

		if !userExists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		// 2. Perform the actual permission check.
		// TODO: This is where the core RBAC logic goes.
		// It should check if the user's roles have the required 'permission'.
		// It should also consider the siteID from the context if applicable.
		hasPermission := s.checkPermission(userID, permission, c)
		
		if !hasPermission {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
			return
		}

		// 3. If permission check passes, execute the original handler.
		handler(c)
	}
}

// checkPermission is the internal logic for checking permissions.
// This is a placeholder for the actual RBAC check.
func (s *PermissionService) checkPermission(userID interface{}, permission string, c *gin.Context) bool {
	// 1. Cast userID to its proper type (uint).
	id, ok := userID.(uint)
	if !ok {
		s.appCtx.Logger().Warn(c, "userID in context is not of type uint")
		return false
	}

	// 2. Determine the user type. For now, we assume 'AdminUser' for any permission check
    //    happening within this service's Wrap method, as it's intended for admin routes.
    //    A more sophisticated system might get user type from the context as well.
	userType := model.AdminUser

	// 3. Use the existing Can method to perform the check.
	hasPermission, err := s.Can(c.Request.Context(), id, userType, permission)
	if err != nil {
		s.appCtx.Logger().Error(c, "Error checking permission", "error", err, "userID", id, "permission", permission)
		return false
	}
	
	return hasPermission
}

// Can checks if a user has a specific permission.
func (s *PermissionService) Can(ctx context.Context, userID uint, userType model.UserType, permissionSlug string) (bool, error) {
	// 1. Super admin check (only for admin users)
	if userType == model.AdminUser {
		admin, err := s.adminRepo.FindByID(ctx, userID)
		if err != nil {
			return false, err
		}
		if admin.IsSuperAdmin {
			return true, nil
		}
	}

	// 2. Get all roles for the user
	roles, err := s.roleRepo.FindRolesByUserID(ctx, userID, userType)
	if err != nil {
		return false, err
	}
	if len(roles) == 0 {
		return false, nil
	}

	// 3. Check permissions for each role
	// This can be optimized by fetching all permissions for all roles at once
	for _, role := range roles {
		permissions, err := s.roleRepo.FindPermissionsByRoleID(ctx, role.ID)
		if err != nil {
			// Log the error but continue checking other roles
			continue
		}
		for _, perm := range permissions {
			if perm.Slug == permissionSlug {
				return true, nil
			}
		}
	}

	return false, nil
}

type CreatePermissionPayload struct {
	Slug        string `json:"slug" binding:"required"`
	Description string `json:"description"`
	SiteID      uint   `json:"site_id"`
	CreatedBy   uint   `json:"created_by"`
}

func (s *PermissionService) CreatePermission(ctx *gin.Context, payload *CreatePermissionPayload) (*model.Permission, error) {
	if _, err := s.permRepo.GetBySlug(ctx, payload.Slug); !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, ErrPermissionExists
	}

	permission := &model.Permission{
		Slug:        payload.Slug,
		Description: payload.Description,
	}

	if err := s.permRepo.Create(ctx, permission); err != nil {
		s.appCtx.Logger().Error(ctx, "Failed to create permission", "error", err)
		return nil, err
	}

	// Publish permission created event
	createdEvent := events.NewPermissionCreatedEvent(
		ctx.Request.Context(),
		permission,
		payload.CreatedBy,
		payload.SiteID,
	)
	s.appCtx.EventManager().Dispatch(createdEvent)

	return permission, nil
}

type UpdatePermissionPayload struct {
	Slug        string `json:"slug"`
	Description string `json:"description"`
	SiteID      uint   `json:"site_id"`
	UpdatedBy   uint   `json:"updated_by"`
}

func (s *PermissionService) UpdatePermission(ctx *gin.Context, permissionID uint, payload *UpdatePermissionPayload) (*model.Permission, error) {
	// 获取原始权限信息
	permission, err := s.permRepo.GetByID(ctx, permissionID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrPermissionNotFound
		}
		return nil, err
	}

	oldSlug := permission.Slug
	
	// 如果更新slug，检查是否与已有权限冲突
	if payload.Slug != "" && payload.Slug != permission.Slug {
		if existingPerm, err := s.permRepo.GetBySlug(ctx, payload.Slug); err == nil && existingPerm.ID != permissionID {
			return nil, ErrPermissionExists
		}
		permission.Slug = payload.Slug
	}
	
	// 如果更新描述
	if payload.Description != "" {
		permission.Description = payload.Description
	}
	
	// 保存更新
	if err := s.permRepo.Update(ctx, permission); err != nil {
		s.appCtx.Logger().Error(ctx, "Failed to update permission", "error", err)
		return nil, err
	}
	
	// Dispatch update event if there are changes
	if oldSlug != permission.Slug {
		updatedEvent := events.NewPermissionUpdatedEvent(
			ctx.Request.Context(),
			permission,
			payload.UpdatedBy,
			payload.SiteID,
			oldSlug,
		)
		s.appCtx.EventManager().Dispatch(updatedEvent)
	}
	
	return permission, nil
}

func (s *PermissionService) DeletePermission(ctx *gin.Context, permissionID uint, deletedBy uint, siteID uint) error {
	// 获取权限信息用于事件
	permission, err := s.permRepo.GetByID(ctx, permissionID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrPermissionNotFound
		}
		return err
	}
	
	// 保存权限标识符，以便在删除后仍能在事件中使用
	permissionSlug := permission.Slug
	
	// 执行删除
	if err := s.permRepo.Delete(ctx, permissionID); err != nil {
		s.appCtx.Logger().Error(ctx, "Failed to delete permission", "error", err)
		return err
	}
	
	// 发布删除事件
	deletedEvent := events.NewPermissionDeletedEvent(
		ctx.Request.Context(),
		permissionID,
		deletedBy,
		siteID,
		permissionSlug,
	)
	s.appCtx.EventManager().Dispatch(deletedEvent)
	
	return nil
}

func (s *PermissionService) ListPermissions(ctx *gin.Context, options *database.ListOptions) ([]*model.Permission, int64, error) {
	return s.permRepo.List(ctx, options)
}