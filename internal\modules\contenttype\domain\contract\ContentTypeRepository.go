/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-09
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/contenttype/domain/contract/ContentTypeRepository.go
 * @Description: Defines the repository interface for content types.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"context"
	"gacms/internal/modules/contenttype/domain/model"
)

// ContentTypeRepository defines the persistence operations for ContentType entities.
type ContentTypeRepository interface {
	Create(ctx context.Context, contentType *model.ContentType) error
	Update(ctx context.Context, contentType *model.ContentType) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*model.ContentType, error)
	GetBySlug(ctx context.Context, slug string) (*model.ContentType, error)
	GetAll(ctx context.Context) ([]*model.ContentType, error)

	// CRUD for Fields
	CreateField(field *model.Field) error
	UpdateField(field *model.Field) error
	DeleteField(id uint) error
	FindFieldByID(id uint) (*model.Field, error)
} 