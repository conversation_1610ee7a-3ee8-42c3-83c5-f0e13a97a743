/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/modules/admin/port/http/controller/MenuController.go
 * @Description: 后台管理菜单控制器（通过上下文获取权限信息）
 * 
 * © 2025 GACMS. All rights reserved.
 */

package controller

import (
	"net/http"

	"gacms/pkg/contract"
	"github.com/gin-gonic/gin"
)

// MenuItem 菜单项结构
type MenuItem struct {
	Title    string     `json:"title"`
	Path     string     `json:"path"`
	Icon     string     `json:"icon,omitempty"`
	Children []MenuItem `json:"children,omitempty"`
}

// MenuController 菜单控制器（负责后台管理界面的菜单聚合）
type MenuController struct {
	permissionChecker contract.PermissionChecker
}

// NewMenuController 创建菜单控制器
func NewMenuController(permissionChecker contract.PermissionChecker) *MenuController {
	return &MenuController{
		permissionChecker: permissionChecker,
	}
}

// RegisterRoutes 注册菜单路由
func (c *MenuController) RegisterRoutes(group *gin.RouterGroup) {
	menuGroup := group.Group("/menu")
	{
		// 获取当前用户的后台管理菜单
		menuGroup.GET("", c.GetUserMenu)
	}
}

// GetUserMenu 获取当前用户的后台管理菜单
func (c *MenuController) GetUserMenu(ctx *gin.Context) {
	// 构建基于权限的菜单（通过上下文获取权限信息）
	menu := c.buildMenuByUserPermissions(ctx)
	
	ctx.JSON(http.StatusOK, gin.H{
		"data": menu,
	})
}

// buildMenuByUserPermissions 根据用户权限构建菜单（通过上下文获取权限）
func (c *MenuController) buildMenuByUserPermissions(ctx *gin.Context) []MenuItem {
	var menu []MenuItem
	
	// 仪表板（所有登录用户都可以访问）
	menu = append(menu, MenuItem{
		Title: "仪表板",
		Path:  "/admin/dashboard",
		Icon:  "dashboard",
	})
	
	// 站点管理（通过上下文检查权限）
	if c.hasPermissionFromContext(ctx, "system:sites:manage") {
		menu = append(menu, MenuItem{
			Title: "站点管理",
			Path:  "/sites",
			Icon:  "site",
			Children: []MenuItem{
				{Title: "站点列表", Path: "/sites"},
				{Title: "域名绑定", Path: "/sites/domain-bindings"},
			},
		})
	}
	
	// 用户管理（通过上下文检查权限）
	if c.hasPermissionFromContext(ctx, "user:manage") {
		menu = append(menu, MenuItem{
			Title: "用户管理",
			Path:  "/users",
			Icon:  "user",
			Children: []MenuItem{
				{Title: "用户列表", Path: "/users"},
				{Title: "角色管理", Path: "/roles"},
			},
		})
	}
	
	// 内容管理（通过上下文检查权限）
	if c.hasPermissionFromContext(ctx, "content:manage") {
		menu = append(menu, MenuItem{
			Title: "内容管理",
			Path:  "/content",
			Icon:  "content",
			Children: []MenuItem{
				{Title: "文章管理", Path: "/content/articles"},
				{Title: "页面管理", Path: "/content/pages"},
				{Title: "媒体管理", Path: "/content/media"},
			},
		})
	}
	
	// 系统管理（通过上下文检查权限）
	if c.hasPermissionFromContext(ctx, "system:manage") {
		menu = append(menu, MenuItem{
			Title: "系统管理",
			Path:  "/system",
			Icon:  "system",
			Children: []MenuItem{
				{Title: "系统配置", Path: "/system/config"},
				{Title: "系统日志", Path: "/system/logs"},
			},
		})
	}
	
	return menu
}

// hasPermissionFromContext 通过核心权限检查接口检查权限
func (c *MenuController) hasPermissionFromContext(ctx *gin.Context, permission string) bool {
	// 通过核心权限检查接口的Wrap方法检查权限
	// 这里创建一个临时的handler来测试权限
	hasPermission := true

	testHandler := c.permissionChecker.Wrap(permission, func(ctx *gin.Context) {
		// 如果能执行到这里，说明有权限
		hasPermission = true
	})

	// 创建一个临时的响应写入器来捕获权限检查结果
	originalWriter := ctx.Writer
	ctx.Writer = &permissionTestWriter{hasPermission: &hasPermission}

	// 执行权限检查
	testHandler(ctx)

	// 恢复原始的响应写入器
	ctx.Writer = originalWriter

	return hasPermission
}

// permissionTestWriter 用于测试权限的临时响应写入器
type permissionTestWriter struct {
	gin.ResponseWriter
	hasPermission *bool
}

func (w *permissionTestWriter) WriteHeader(statusCode int) {
	if statusCode == 403 || statusCode == 401 {
		*w.hasPermission = false
	}
}

func (w *permissionTestWriter) Write(data []byte) (int, error) {
	return len(data), nil
}

// GetBreadcrumb 获取面包屑导航
func (c *MenuController) GetBreadcrumb(ctx *gin.Context) {
	path := ctx.Query("path")
	if path == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Path parameter is required"})
		return
	}
	
	breadcrumb := c.buildBreadcrumb(path)
	
	ctx.JSON(http.StatusOK, gin.H{
		"data": breadcrumb,
	})
}

// buildBreadcrumb 构建面包屑导航
func (c *MenuController) buildBreadcrumb(path string) []MenuItem {
	// 根据路径构建面包屑
	breadcrumbMap := map[string][]MenuItem{
		"/admin/dashboard": {
			{Title: "首页", Path: "/admin/dashboard"},
		},
		"/sites": {
			{Title: "首页", Path: "/admin/dashboard"},
			{Title: "站点管理", Path: "/sites"},
		},
		"/sites/domain-bindings": {
			{Title: "首页", Path: "/admin/dashboard"},
			{Title: "站点管理", Path: "/sites"},
			{Title: "域名绑定", Path: "/sites/domain-bindings"},
		},
		"/users": {
			{Title: "首页", Path: "/admin/dashboard"},
			{Title: "用户管理", Path: "/users"},
		},
		"/roles": {
			{Title: "首页", Path: "/admin/dashboard"},
			{Title: "用户管理", Path: "/users"},
			{Title: "角色管理", Path: "/roles"},
		},
	}
	
	if breadcrumb, exists := breadcrumbMap[path]; exists {
		return breadcrumb
	}
	
	// 默认面包屑
	return []MenuItem{
		{Title: "首页", Path: "/admin/dashboard"},
	}
}
