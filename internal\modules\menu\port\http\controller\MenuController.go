/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/menu/port/http/controller/MenuController.go
 * @Description: HTTP Controller for managing frontend menus.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package controller

import (
	"gacms/internal/modules/menu/application/dto"
	"gacms/internal/modules/menu/application/service"
	"gacms/internal/modules/menu/domain/model"
	"gacms/pkg/response"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type MenuController struct {
	menuService *service.MenuService
}

func NewMenuController(menuService *service.MenuService) *MenuController {
	return &MenuController{menuService: menuService}
}

func (c *MenuController) RegisterAdminRoutes(rg *gin.RouterGroup) {
	menuGroup := rg.Group("/menus")
	{
		menuGroup.POST("", c.createMenu)
		menuGroup.GET("", c.listMenus)
		menuGroup.GET("/:id", c.getMenu)
		menuGroup.PUT("/:id", c.updateMenu)
		menuGroup.DELETE("/:id", c.deleteMenu)
		menuGroup.PUT("/:id/structure", c.updateMenuStructure)
	}
}

func (c *MenuController) RegisterPublicRoutes(rg *gin.RouterGroup) {
	rg.GET("/menu/:slug", c.getPublicMenu)
}

func (c *MenuController) RegisterRoutes(rg *gin.RouterGroup) {
	// The full path will be like /admin/sites/:siteId/menus
	menuRoutes := rg.Group("/menus")
	{
		menuRoutes.GET("/by-position/:position", c.getMenusByPosition)
	}
}

func (c *MenuController) createMenu(ctx *gin.Context) {
	var input dto.MenuCreateDTO
	if err := ctx.ShouldBindJSON(&input); err != nil {
		response.Error(ctx, 400, err.Error())
		return
	}

	menu, err := c.menuService.CreateMenu(&input)
	if err != nil {
		response.Error(ctx, 500, "Failed to create menu")
		return
	}

	response.Success(ctx, menu)
}

func (c *MenuController) listMenus(ctx *gin.Context) {
	siteIDStr := ctx.Query("site_id")
	if siteIDStr == "" {
		response.Error(ctx, 400, "site_id is required")
		return
	}
	siteID, err := strconv.ParseUint(siteIDStr, 10, 32)
	if err != nil {
		response.Error(ctx, 400, "Invalid site_id")
		return
	}

	menus, err := c.menuService.GetAllMenusBySite(uint(siteID))
	if err != nil {
		response.Error(ctx, 500, "Failed to fetch menus")
		return
	}

	res := make([]dto.MenuResponseDTO, 0, len(menus))
	for _, menu := range menus {
		res = append(res, toMenuResponseDTO(menu))
	}
	response.Success(ctx, res)
}

func (c *MenuController) getMenu(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Error(ctx, 400, "Invalid menu ID")
		return
	}

	menu, err := c.menuService.GetFullMenuByID(uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.Error(ctx, 404, "Menu not found")
		} else {
			response.Error(ctx, 500, "Failed to fetch menu")
		}
		return
	}

	response.Success(ctx, toFullMenuResponseDTO(menu))
}

func (c *MenuController) updateMenu(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Error(ctx, 400, "Invalid menu ID")
		return
	}
	var input dto.MenuUpdateDTO
	if err := ctx.ShouldBindJSON(&input); err != nil {
		response.Error(ctx, 400, err.Error())
		return
	}

	menu, err := c.menuService.UpdateMenu(uint(id), &input)
	if err != nil {
		response.Error(ctx, 500, "Failed to update menu")
		return
	}
	response.Success(ctx, toMenuResponseDTO(menu))
}

func (c *MenuController) deleteMenu(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Error(ctx, 400, "Invalid menu ID")
		return
	}

	if err := c.menuService.DeleteMenu(uint(id)); err != nil {
		response.Error(ctx, 500, "Failed to delete menu")
		return
	}
	response.Success(ctx, nil)
}

func (c *MenuController) updateMenuStructure(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Error(ctx, 400, "Invalid menu ID")
		return
	}
	var input dto.MenuStructureUpdateDTO
	if err := ctx.ShouldBindJSON(&input); err != nil {
		response.Error(ctx, 400, err.Error())
		return
	}
	if err := c.menuService.UpdateMenuStructure(uint(id), input.Items); err != nil {
		response.Error(ctx, 500, "Failed to update menu structure")
		return
	}
	response.Success(ctx, nil)
}

func (c *MenuController) getPublicMenu(ctx *gin.Context) {
	slug := ctx.Param("slug")
	siteIDStr := ctx.Query("site_id")
	if siteIDStr == "" {
		response.Error(ctx, 400, "site_id is required")
		return
	}
	siteID, err := strconv.ParseUint(siteIDStr, 10, 32)
	if err != nil {
		response.Error(ctx, 400, "Invalid site_id")
		return
	}

	menu, err := c.menuService.GetFullMenuBySlug(uint(siteID), slug)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.Error(ctx, 404, "Menu not found")
		} else {
			response.Error(ctx, 500, "Failed to fetch menu")
		}
		return
	}
	response.Success(ctx, toFullMenuResponseDTO(menu))
}

func (c *MenuController) getMenuTreeByGroup(ctx *gin.Context) {
	siteID, err := strconv.ParseUint(ctx.Param("siteId"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid site ID"})
		return
	}
	groupSlug := ctx.Param("groupSlug")

	tree, err := c.menuService.GetFullMenuByGroupSlug(uint(siteID), groupSlug)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if tree == nil {
		tree = []*service.MenuItemNode{}
	}

	ctx.JSON(http.StatusOK, tree)
}

func (c *MenuController) getMenusByPosition(ctx *gin.Context) {
	// ... existing code ...
}

// --- DTO Converters ---

func toMenuResponseDTO(menu *model.Menu) dto.MenuResponseDTO {
	return dto.MenuResponseDTO{
		ID:          menu.ID,
		SiteID:      menu.SiteID,
		Name:        menu.Name,
		Slug:        menu.Slug,
		Description: menu.Description,
	}
}

func toFullMenuResponseDTO(menu *model.Menu) dto.FullMenuResponseDTO {
	return dto.FullMenuResponseDTO{
		ID:          menu.ID,
		SiteID:      menu.SiteID,
		Name:        menu.Name,
		Slug:        menu.Slug,
		Description: menu.Description,
		Items:       toMenuItemDTOs(menu.MenuItems),
	}
}

func toMenuItemDTOs(items []*model.MenuItem) []*dto.MenuItemDTO {
	res := make([]*dto.MenuItemDTO, 0, len(items))
	for _, item := range items {
		res = append(res, &dto.MenuItemDTO{
			ID:        item.ID,
			Title:     item.Title,
			URL:       item.URL,
			Target:    item.Target,
			SortOrder: item.SortOrder,
			Children:  toMenuItemDTOs(item.Children),
		})
	}
	return res
} 