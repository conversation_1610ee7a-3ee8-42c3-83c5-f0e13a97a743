/*
Author: <PERSON><PERSON> Nieh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package model

import (
	"encoding/json"
	"time"

	"gacms/pkg/contract"
)

// ModuleType 模块类型
type ModuleType string

const (
	ModuleTypeCore     ModuleType = "core"     // 核心模块，永远启用
	ModuleTypeOptional ModuleType = "optional" // 可选模块，可启用/停用
	ModuleTypeVendors  ModuleType = "vendors"  // 第三方供应商模块
)

// ModuleConfig 模块配置模型
type ModuleConfig struct {
	ID         uint       `gorm:"primaryKey" json:"id"`
	ModuleName string     `gorm:"uniqueIndex;size:50;not null" json:"module_name"`
	ModuleType ModuleType `gorm:"type:enum('core','optional','third_party');default:'optional';not null" json:"module_type"`

	// 状态管理
	Enabled  bool `gorm:"default:true;not null" json:"enabled"`
	AutoLoad bool `gorm:"default:true;not null" json:"auto_load"`

	// 版本信息
	Version           string `gorm:"size:20;default:'1.0.0'" json:"version"`
	MinGacmsVersion   string `gorm:"size:20;default:'1.0.0'" json:"min_gacms_version"`

	// 依赖关系
	Dependencies DependencyList `gorm:"type:json" json:"dependencies"`
	Conflicts    DependencyList `gorm:"type:json" json:"conflicts"`

	// 配置信息
	Config   ConfigData `gorm:"type:json" json:"config"`
	Settings ConfigData `gorm:"type:json" json:"settings"`

	// 权限和路由
	Permissions PermissionList `gorm:"type:json" json:"permissions"`
	Routes      RouteList      `gorm:"type:json" json:"routes"`
	Events      EventConfig    `gorm:"type:json" json:"events"`

	// 元数据
	DisplayName string `gorm:"size:100" json:"display_name"`
	Description string `gorm:"type:text" json:"description"`
	Author      string `gorm:"size:100" json:"author"`
	Homepage    string `gorm:"size:255" json:"homepage"`
	Repository  string `gorm:"size:255" json:"repository"`

	// 安装信息
	InstallPath string     `gorm:"size:255" json:"install_path"`
	InstalledAt *time.Time `json:"installed_at"`
	UpdatedAt   time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	CreatedAt   time.Time  `gorm:"autoCreateTime" json:"created_at"`
}

// DependencyList 依赖列表
type DependencyList []string

// ConfigData 配置数据
type ConfigData map[string]interface{}

// PermissionInfo 权限信息
type PermissionInfo struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Category    string `json:"category,omitempty"`
}

// PermissionList 权限列表
type PermissionList []PermissionInfo

// RouteInfo 路由信息
type RouteInfo struct {
	Path        string            `json:"path"`
	Method      string            `json:"method"`
	Controller  string            `json:"controller"`
	Action      string            `json:"action"`
	Permission  string            `json:"permission,omitempty"`
	Middlewares []string          `json:"middlewares,omitempty"`
	Params      map[string]string `json:"params,omitempty"`
}

// RouteList 路由列表
type RouteList []RouteInfo

// EventConfig 事件配置
type EventConfig struct {
	Publishes []string              `json:"publishes"`
	Listens   []EventListenerConfig `json:"listens"`
}

// EventListenerConfig 事件监听器配置
type EventListenerConfig struct {
	Event    string `json:"event"`
	Handler  string `json:"handler"`
	Priority int    `json:"priority"`
}

// JSON序列化支持
func (d *DependencyList) Scan(value interface{}) error {
	if value == nil {
		*d = DependencyList{}
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		*d = DependencyList{}
		return nil
	}
	
	return json.Unmarshal(bytes, d)
}

func (d DependencyList) Value() (interface{}, error) {
	if len(d) == 0 {
		return nil, nil
	}
	return json.Marshal(d)
}

func (c *ConfigData) Scan(value interface{}) error {
	if value == nil {
		*c = ConfigData{}
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		*c = ConfigData{}
		return nil
	}
	
	return json.Unmarshal(bytes, c)
}

func (c ConfigData) Value() (interface{}, error) {
	if len(c) == 0 {
		return nil, nil
	}
	return json.Marshal(c)
}

func (p *PermissionList) Scan(value interface{}) error {
	if value == nil {
		*p = PermissionList{}
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		*p = PermissionList{}
		return nil
	}
	
	return json.Unmarshal(bytes, p)
}

func (p PermissionList) Value() (interface{}, error) {
	if len(p) == 0 {
		return nil, nil
	}
	return json.Marshal(p)
}

func (r *RouteList) Scan(value interface{}) error {
	if value == nil {
		*r = RouteList{}
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		*r = RouteList{}
		return nil
	}
	
	return json.Unmarshal(bytes, r)
}

func (r RouteList) Value() (interface{}, error) {
	if len(r) == 0 {
		return nil, nil
	}
	return json.Marshal(r)
}

func (e *EventConfig) Scan(value interface{}) error {
	if value == nil {
		*e = EventConfig{}
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		*e = EventConfig{}
		return nil
	}
	
	return json.Unmarshal(bytes, e)
}

func (e EventConfig) Value() (interface{}, error) {
	return json.Marshal(e)
}

// 业务方法

// IsCore 判断是否为核心模块
func (m *ModuleConfig) IsCore() bool {
	return m.ModuleType == ModuleTypeCore
}

// IsOptional 判断是否为可选模块
func (m *ModuleConfig) IsOptional() bool {
	return m.ModuleType == ModuleTypeOptional
}

// IsVendors 判断是否为第三方供应商模块
func (m *ModuleConfig) IsVendors() bool {
	return m.ModuleType == ModuleTypeVendors
}

// CanDisable 判断是否可以禁用
func (m *ModuleConfig) CanDisable() bool {
	return !m.IsCore()
}

// GetPermissionNames 获取权限名称列表
func (m *ModuleConfig) GetPermissionNames() []string {
	names := make([]string, len(m.Permissions))
	for i, perm := range m.Permissions {
		names[i] = perm.Name
	}
	return names
}

// GetPublishedEvents 获取发布的事件列表
func (m *ModuleConfig) GetPublishedEvents() []contract.EventName {
	events := make([]contract.EventName, len(m.Events.Publishes))
	for i, event := range m.Events.Publishes {
		events[i] = contract.EventName(event)
	}
	return events
}

// GetListenedEvents 获取监听的事件列表
func (m *ModuleConfig) GetListenedEvents() []contract.EventName {
	events := make([]contract.EventName, len(m.Events.Listens))
	for i, listener := range m.Events.Listens {
		events[i] = contract.EventName(listener.Event)
	}
	return events
}

// TableName 指定表名
func (ModuleConfig) TableName() string {
	return "module_configs"
}
