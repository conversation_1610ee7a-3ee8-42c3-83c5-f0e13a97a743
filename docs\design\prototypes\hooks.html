<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 钩子系统</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .hook-card {
            transition: all 0.3s ease;
            border: 1px solid #374151;
        }
        
        .hook-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
            border-color: #4B5563;
        }
        
        .code-block {
            background-color: #1e1e1e;
            border-radius: 6px;
            padding: 1rem;
            font-family: 'Courier New', Courier, monospace;
            font-size: 0.85rem;
            white-space: pre-wrap;
            word-break: break-all;
            margin: 1rem 0;
        }
        
        .code-block.php { color: #c586c0; }
        
        .hook-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .tag-action { background-color: rgba(0, 123, 255, 0.15); color: #0d6efd; }
        .tag-filter { background-color: rgba(40, 167, 69, 0.15); color: #28a745; }
        .tag-core { background-color: rgba(108, 117, 125, 0.15); color: #6c757d; }
        .tag-content { background-color: rgba(255, 193, 7, 0.15); color: #ffc107; }
        .tag-user { background-color: rgba(111, 66, 193, 0.15); color: #6f42c1; }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-4">
                <a href="dashboard.html" class="text-gray-400 hover:text-white">首页</a>
                <span class="mx-2 text-gray-600">/</span>
                <a href="#" class="text-gray-400 hover:text-white">功能扩展</a>
                <span class="mx-2 text-gray-600">/</span>
                <span class="text-white">钩子系统</span>
            </div>
            
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h2 class="text-xl font-bold text-white relative pl-3 section-title">钩子系统</h2>
                <div class="mt-4 flex justify-between items-center">
                    <p class="text-gray-400">钩子系统允许您在特定事件发生时执行自定义代码，实现系统功能的扩展和定制。</p>
                    <div class="flex items-center">
                        <button class="bg-gradient-to-r from-blue-500 to-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center hover:shadow-lg hover:shadow-blue-500/30 transition-all">
                            <i class="fas fa-plus mr-2"></i> 创建自定义钩子
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 钩子系统内容 -->
            <div class="mb-8">
                <!-- 标签页导航 -->
                <div class="border-b border-gray-700 mb-6">
                    <ul class="flex flex-wrap -mb-px" role="tablist">
                        <li class="mr-2" role="presentation">
                            <button class="inline-block px-4 py-2 border-b-2 border-blue-500 text-blue-400 font-medium" role="tab" aria-selected="true" data-target="availableHooks">可用钩子</button>
                        </li>
                        <li class="mr-2" role="presentation">
                            <button class="inline-block px-4 py-2 border-b-2 border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-400 font-medium" role="tab" aria-selected="false" data-target="activeHooks">已激活钩子</button>
                        </li>
                        <li class="mr-2" role="presentation">
                            <button class="inline-block px-4 py-2 border-b-2 border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-400 font-medium" role="tab" aria-selected="false" data-target="customHooks">自定义钩子</button>
                        </li>
                        <li class="mr-2" role="presentation">
                            <button class="inline-block px-4 py-2 border-b-2 border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-400 font-medium" role="tab" aria-selected="false" data-target="hookDocs">开发文档</button>
                        </li>
                    </ul>
                </div>
                
                <!-- 可用钩子内容 -->
                <div id="availableHooks" class="tab-content active">
                    <!-- 筛选工具栏 -->
                    <div class="flex flex-wrap gap-4 mb-6">
                        <div class="relative">
                            <input type="text" placeholder="搜索钩子..." class="w-64 bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 pl-10 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            <span class="text-gray-400">分类:</span>
                            <select class="bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option>全部</option>
                                <option>核心系统</option>
                                <option>内容管理</option>
                                <option>用户管理</option>
                                <option>媒体管理</option>
                            </select>
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            <span class="text-gray-400">类型:</span>
                            <select class="bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option>全部</option>
                                <option>动作钩子</option>
                                <option>过滤钩子</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- 钩子列表 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- 钩子卡片 1 -->
                        <div class="hook-card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="p-5">
                                <div class="flex justify-between items-start mb-3">
                                    <h3 class="text-lg font-semibold text-white">content_before_save</h3>
                                    <span class="hook-tag tag-action">动作钩子</span>
                                </div>
                                <p class="text-gray-400 text-sm mb-4">在内容保存到数据库之前触发，可用于内容预处理或验证。</p>
                                <div class="flex items-center text-xs text-gray-500 mb-4">
                                    <span class="flex items-center mr-4">
                                        <i class="fas fa-layer-group mr-1"></i> 内容管理
                                    </span>
                                    <span class="flex items-center">
                                        <i class="fas fa-code-branch mr-1"></i> 3 个回调
                                    </span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="hook-tag tag-content">内容</span>
                                    <button class="text-blue-400 hover:text-blue-300 text-sm flex items-center">
                                        <i class="fas fa-info-circle mr-1"></i> 查看详情
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 钩子卡片 2 -->
                        <div class="hook-card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="p-5">
                                <div class="flex justify-between items-start mb-3">
                                    <h3 class="text-lg font-semibold text-white">user_login</h3>
                                    <span class="hook-tag tag-action">动作钩子</span>
                                </div>
                                <p class="text-gray-400 text-sm mb-4">用户成功登录后触发，可用于记录登录历史、发送通知等。</p>
                                <div class="flex items-center text-xs text-gray-500 mb-4">
                                    <span class="flex items-center mr-4">
                                        <i class="fas fa-layer-group mr-1"></i> 用户管理
                                    </span>
                                    <span class="flex items-center">
                                        <i class="fas fa-code-branch mr-1"></i> 5 个回调
                                    </span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="hook-tag tag-user">用户</span>
                                    <button class="text-blue-400 hover:text-blue-300 text-sm flex items-center">
                                        <i class="fas fa-info-circle mr-1"></i> 查看详情
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 钩子卡片 3 -->
                        <div class="hook-card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="p-5">
                                <div class="flex justify-between items-start mb-3">
                                    <h3 class="text-lg font-semibold text-white">filter_content_output</h3>
                                    <span class="hook-tag tag-filter">过滤钩子</span>
                                </div>
                                <p class="text-gray-400 text-sm mb-4">在内容输出到前端前过滤，可用于内容格式化、敏感词过滤等。</p>
                                <div class="flex items-center text-xs text-gray-500 mb-4">
                                    <span class="flex items-center mr-4">
                                        <i class="fas fa-layer-group mr-1"></i> 内容管理
                                    </span>
                                    <span class="flex items-center">
                                        <i class="fas fa-code-branch mr-1"></i> 4 个回调
                                    </span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="hook-tag tag-content">内容</span>
                                    <button class="text-blue-400 hover:text-blue-300 text-sm flex items-center">
                                        <i class="fas fa-info-circle mr-1"></i> 查看详情
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 钩子卡片 4 -->
                        <div class="hook-card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="p-5">
                                <div class="flex justify-between items-start mb-3">
                                    <h3 class="text-lg font-semibold text-white">system_init</h3>
                                    <span class="hook-tag tag-action">动作钩子</span>
                                </div>
                                <p class="text-gray-400 text-sm mb-4">系统初始化时触发，可用于加载自定义配置、初始化第三方服务等。</p>
                                <div class="flex items-center text-xs text-gray-500 mb-4">
                                    <span class="flex items-center mr-4">
                                        <i class="fas fa-layer-group mr-1"></i> 核心系统
                                    </span>
                                    <span class="flex items-center">
                                        <i class="fas fa-code-branch mr-1"></i> 7 个回调
                                    </span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="hook-tag tag-core">核心</span>
                                    <button class="text-blue-400 hover:text-blue-300 text-sm flex items-center">
                                        <i class="fas fa-info-circle mr-1"></i> 查看详情
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 钩子卡片 5 -->
                        <div class="hook-card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="p-5">
                                <div class="flex justify-between items-start mb-3">
                                    <h3 class="text-lg font-semibold text-white">media_upload</h3>
                                    <span class="hook-tag tag-action">动作钩子</span>
                                </div>
                                <p class="text-gray-400 text-sm mb-4">媒体文件上传后触发，可用于图像处理、水印添加、云存储同步等。</p>
                                <div class="flex items-center text-xs text-gray-500 mb-4">
                                    <span class="flex items-center mr-4">
                                        <i class="fas fa-layer-group mr-1"></i> 媒体管理
                                    </span>
                                    <span class="flex items-center">
                                        <i class="fas fa-code-branch mr-1"></i> 2 个回调
                                    </span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="hook-tag tag-content">内容</span>
                                    <button class="text-blue-400 hover:text-blue-300 text-sm flex items-center">
                                        <i class="fas fa-info-circle mr-1"></i> 查看详情
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 钩子卡片 6 -->
                        <div class="hook-card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="p-5">
                                <div class="flex justify-between items-start mb-3">
                                    <h3 class="text-lg font-semibold text-white">filter_search_results</h3>
                                    <span class="hook-tag tag-filter">过滤钩子</span>
                                </div>
                                <p class="text-gray-400 text-sm mb-4">过滤搜索结果，可用于自定义搜索结果排序、过滤或增强。</p>
                                <div class="flex items-center text-xs text-gray-500 mb-4">
                                    <span class="flex items-center mr-4">
                                        <i class="fas fa-layer-group mr-1"></i> 内容管理
                                    </span>
                                    <span class="flex items-center">
                                        <i class="fas fa-code-branch mr-1"></i> 1 个回调
                                    </span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="hook-tag tag-content">内容</span>
                                    <button class="text-blue-400 hover:text-blue-300 text-sm flex items-center">
                                        <i class="fas fa-info-circle mr-1"></i> 查看详情
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 分页控制 -->
                    <div class="flex justify-between items-center mt-8">
                        <div class="text-sm text-gray-400">
                            显示 1-6 / 共 24 个钩子
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="px-3 py-1 bg-blue-600 text-white rounded-md">1</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">2</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">3</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">4</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 已激活钩子内容 -->
                <div id="activeHooks" class="tab-content hidden">
                    <div class="bg-gray-800/20 rounded-xl p-6 mb-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold">已激活钩子列表</h3>
                            <button class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded-lg flex items-center text-sm">
                                <i class="fas fa-sync-alt mr-2"></i> 刷新列表
                            </button>
                        </div>
                        
                        <div class="overflow-x-auto">
                            <table class="w-full text-left border-collapse">
                                <thead>
                                    <tr class="bg-gray-800">
                                        <th class="p-3 border-b border-gray-700">钩子名称</th>
                                        <th class="p-3 border-b border-gray-700">类型</th>
                                        <th class="p-3 border-b border-gray-700">回调函数</th>
                                        <th class="p-3 border-b border-gray-700">优先级</th>
                                        <th class="p-3 border-b border-gray-700">来源</th>
                                        <th class="p-3 border-b border-gray-700">状态</th>
                                        <th class="p-3 border-b border-gray-700">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="hover:bg-gray-800/20">
                                        <td class="p-3 border-b border-gray-700">content_before_save</td>
                                        <td class="p-3 border-b border-gray-700">
                                            <span class="hook-tag tag-action">动作钩子</span>
                                        </td>
                                        <td class="p-3 border-b border-gray-700">validate_content</td>
                                        <td class="p-3 border-b border-gray-700">10</td>
                                        <td class="p-3 border-b border-gray-700">内容验证插件</td>
                                        <td class="p-3 border-b border-gray-700">
                                            <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">已激活</span>
                                        </td>
                                        <td class="p-3 border-b border-gray-700">
                                            <button class="text-blue-400 hover:text-blue-300 mr-2">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-red-400 hover:text-red-300">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-800/20">
                                        <td class="p-3 border-b border-gray-700">user_login</td>
                                        <td class="p-3 border-b border-gray-700">
                                            <span class="hook-tag tag-action">动作钩子</span>
                                        </td>
                                        <td class="p-3 border-b border-gray-700">log_user_login</td>
                                        <td class="p-3 border-b border-gray-700">5</td>
                                        <td class="p-3 border-b border-gray-700">安全审计插件</td>
                                        <td class="p-3 border-b border-gray-700">
                                            <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">已激活</span>
                                        </td>
                                        <td class="p-3 border-b border-gray-700">
                                            <button class="text-blue-400 hover:text-blue-300 mr-2">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-red-400 hover:text-red-300">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-800/20">
                                        <td class="p-3 border-b border-gray-700">filter_content_output</td>
                                        <td class="p-3 border-b border-gray-700">
                                            <span class="hook-tag tag-filter">过滤钩子</span>
                                        </td>
                                        <td class="p-3 border-b border-gray-700">format_content_html</td>
                                        <td class="p-3 border-b border-gray-700">20</td>
                                        <td class="p-3 border-b border-gray-700">内容格式化器</td>
                                        <td class="p-3 border-b border-gray-700">
                                            <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">已激活</span>
                                        </td>
                                        <td class="p-3 border-b border-gray-700">
                                            <button class="text-blue-400 hover:text-blue-300 mr-2">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-red-400 hover:text-red-300">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-800/20">
                                        <td class="p-3 border-b border-gray-700">system_init</td>
                                        <td class="p-3 border-b border-gray-700">
                                            <span class="hook-tag tag-action">动作钩子</span>
                                        </td>
                                        <td class="p-3 border-b border-gray-700">load_custom_config</td>
                                        <td class="p-3 border-b border-gray-700">5</td>
                                        <td class="p-3 border-b border-gray-700">系统核心</td>
                                        <td class="p-3 border-b border-gray-700">
                                            <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">已激活</span>
                                        </td>
                                        <td class="p-3 border-b border-gray-700">
                                            <button class="text-blue-400 hover:text-blue-300 mr-2">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-red-400 hover:text-red-300">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- 自定义钩子内容 -->
                <div id="customHooks" class="tab-content hidden">
                    <div class="bg-gray-800/20 rounded-xl p-6 mb-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold">创建自定义钩子</h3>
                        </div>
                        
                        <form class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-400 mb-2">钩子名称</label>
                                    <input type="text" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="例如: custom_content_process">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-400 mb-2">钩子类型</label>
                                    <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option>动作钩子 (Action)</option>
                                        <option>过滤钩子 (Filter)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-400 mb-2">描述</label>
                                <textarea class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent h-24" placeholder="描述此钩子的用途和触发时机..."></textarea>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-400 mb-2">分类</label>
                                    <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option>核心系统</option>
                                        <option>内容管理</option>
                                        <option>用户管理</option>
                                        <option>媒体管理</option>
                                        <option>自定义</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-400 mb-2">触发位置</label>
                                    <input type="text" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="例如: content/process.php:120">
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-400 mb-2">参数</label>
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-4">
                                        <input type="text" class="flex-1 bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="参数名称">
                                        <select class="w-40 bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                            <option>string</option>
                                            <option>integer</option>
                                            <option>array</option>
                                            <option>object</option>
                                            <option>boolean</option>
                                        </select>
                                        <button type="button" class="bg-red-500 hover:bg-red-600 text-white p-2 rounded-lg">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                    <button type="button" class="text-blue-400 hover:text-blue-300 text-sm flex items-center">
                                        <i class="fas fa-plus mr-1"></i> 添加参数
                                    </button>
                                </div>
                            </div>
                            
                            <div class="flex justify-end space-x-4">
                                <button type="button" class="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600">取消</button>
                                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">创建钩子</button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="bg-gray-800/20 rounded-xl p-6">
                        <h3 class="text-lg font-semibold mb-4">我的自定义钩子</h3>
                        <div class="text-center py-8 text-gray-400">
                            <i class="fas fa-code-branch text-5xl mb-4"></i>
                            <p>您还没有创建任何自定义钩子</p>
                            <p class="text-sm mt-2">使用上方表单创建您的第一个自定义钩子</p>
                        </div>
                    </div>
                </div>
                
                <!-- 开发文档内容 -->
                <div id="hookDocs" class="tab-content hidden">
                    <div class="bg-gray-800/20 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4">钩子系统开发文档</h3>
                        
                        <div class="space-y-6">
                            <div>
                                <h4 class="text-md font-semibold text-blue-400 mb-2">什么是钩子系统？</h4>
                                <p class="text-gray-400">钩子系统是 GACMS 的核心扩展机制，允许开发者在不修改核心代码的情况下，在特定事件发生时执行自定义代码。钩子系统包含两种类型的钩子：动作钩子（Action）和过滤钩子（Filter）。</p>
                            </div>
                            
                            <div>
                                <h4 class="text-md font-semibold text-blue-400 mb-2">动作钩子 vs 过滤钩子</h4>
                                <p class="text-gray-400 mb-3">动作钩子允许您在特定事件发生时执行代码，但不修改任何数据。过滤钩子允许您修改通过钩子传递的数据。</p>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="bg-gray-800/30 p-4 rounded-lg">
                                        <h5 class="font-semibold mb-2">动作钩子示例</h5>
                                        <div class="code-block php">
// 注册回调函数
addAction('user_login', 'logUserLogin');

// 回调函数实现
function logUserLogin($user_id) {
    // 记录用户登录日志
    writeLog('User ' . $user_id . ' logged in');
}</div>
                                    </div>
                                    <div class="bg-gray-800/30 p-4 rounded-lg">
                                        <h5 class="font-semibold mb-2">过滤钩子示例</h5>
                                        <div class="code-block php">
// 注册回调函数
addFilter('content_title', 'formatContentTitle');

// 回调函数实现
function formatContentTitle($title) {
    // 将标题转换为大写
    return strtoupper($title);
}</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="text-md font-semibold text-blue-400 mb-2">如何使用钩子系统</h4>
                                <div class="space-y-4">
                                    <div>
                                        <h5 class="font-semibold mb-1">1. 注册钩子回调</h5>
                                        <p class="text-gray-400">使用 <code class="bg-gray-800 px-1 py-0.5 rounded">addAction()</code> 或 <code class="bg-gray-800 px-1 py-0.5 rounded">addFilter()</code> 函数注册您的回调函数：</p>
                                        <div class="code-block php">
// 动作钩子
addAction('hook_name', 'callback_function', priority);

// 过滤钩子
addFilter('hook_name', 'callback_function', priority);</div>
                                    </div>
                                    
                                    <div>
                                        <h5 class="font-semibold mb-1">2. 实现回调函数</h5>
                                        <p class="text-gray-400">创建您的回调函数，接收钩子传递的参数：</p>
                                        <div class="code-block php">
// 动作钩子回调
function my_action_callback($arg1, $arg2) {
    // 执行操作，但不返回值
}

// 过滤钩子回调
function my_filter_callback($value) {
    // 修改 $value
    return $modified_value;
}</div>
                                    </div>
                                    
                                    <div>
                                        <h5 class="font-semibold mb-1">3. 移除钩子回调</h5>
                                        <p class="text-gray-400">如果需要，您可以使用以下函数移除钩子回调：</p>
                                        <div class="code-block php">
// 移除动作钩子
removeAction('hook_name', 'callback_function');

// 移除过滤钩子
removeFilter('hook_name', 'callback_function');</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="text-md font-semibold text-blue-400 mb-2">最佳实践</h4>
                                <ul class="list-disc pl-5 space-y-2 text-gray-400">
                                    <li>始终为您的钩子回调设置适当的优先级（默认为 10）</li>
                                    <li>确保过滤钩子回调始终返回修改后的值</li>
                                    <li>避免在钩子回调中执行耗时操作，以免影响系统性能</li>
                                    <li>使用有意义的钩子和回调函数名称，遵循命名约定</li>
                                    <li>在插件或主题的初始化阶段注册所有钩子回调</li>
                                </ul>
                            </div>
                            
                            <div>
                                <h4 class="text-md font-semibold text-blue-400 mb-2">完整示例：创建自定义内容过滤器</h4>
                                <div class="code-block php">
<?php
/**
 * 插件名称: 内容过滤器
 * 描述: 一个简单的内容过滤插件示例
 * 版本: 1.0.0
 * 作者: GACMS 开发团队
 */

// 在插件初始化时注册钩子
function content_filter_plugin_init() {
    // 注册内容过滤钩子
    addFilter('filter_content_output', 'filter_bad_words', 20);
    addFilter('filter_comment_text', 'filter_bad_words', 10);
    
    // 注册用户评论提交动作
    addAction('comment_posted', 'notify_admin_on_comment', 10);
}

// 初始化插件
addAction('plugin_init', 'content_filter_plugin_init');

// 过滤不良词汇
function filter_bad_words($content) {
    $bad_words = array('坏词1', '坏词2', '坏词3');
    $replacement = '***';
    
    foreach ($bad_words as $word) {
        $content = str_replace($word, $replacement, $content);
    }
    
    return $content;
}

// 在有新评论时通知管理员
function notify_admin_on_comment($comment_id) {
    $comment = get_comment($comment_id);
    $admin_email = get_option('admin_email');
    
    $subject = '新评论通知';
    $message = "有新评论发布:\n";
    $message .= "作者: {$comment->author}\n";
    $message .= "内容: {$comment->content}\n";
    $message .= "查看链接: " . admin_url("comments.php?action=edit&id={$comment_id}");
    
    send_email($admin_email, $subject, $message);
}
?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 标签页切换功能
            const tabButtons = document.querySelectorAll('[role="tab"]');
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有标签页的active类
                    tabButtons.forEach(btn => {
                        btn.classList.remove('border-blue-500', 'text-blue-400');
                        btn.classList.add('border-transparent', 'text-gray-400', 'hover:text-gray-300', 'hover:border-gray-400');
                        btn.setAttribute('aria-selected', 'false');
                    });
                    
                    // 添加当前标签页的active类
                    this.classList.remove('border-transparent', 'text-gray-400', 'hover:text-gray-300', 'hover:border-gray-400');
                    this.classList.add('border-blue-500', 'text-blue-400');
                    this.setAttribute('aria-selected', 'true');
                    
                    // 隐藏所有内容
                    const tabContents = document.querySelectorAll('.tab-content');
                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                        content.classList.remove('active');
                    });
                    
                    // 显示对应的内容
                    const targetId = this.getAttribute('data-target');
                    const targetContent = document.getElementById(targetId);
                    if(targetContent) {
                        targetContent.classList.remove('hidden');
                        targetContent.classList.add('active');
                    }
                });
            });
            
            // 钩子卡片悬停效果
            const hookCards = document.querySelectorAll('.hook-card');
            hookCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.classList.add('transform', 'scale-[1.02]');
                });
                card.addEventListener('mouseleave', function() {
                    this.classList.remove('transform', 'scale-[1.02]');
                });
            });
        });
    </script>
</body>
</html>