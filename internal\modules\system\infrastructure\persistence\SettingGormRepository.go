/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/system/infrastructure/persistence/SettingGormRepository.go
 * @Description: GORM implementation of the setting repository.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package persistence

import (
	"gacms/internal/modules/system/domain/contract"
	"gacms/internal/modules/system/domain/model"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type settingRepository struct {
	db *gorm.DB
}

func NewSettingRepository(db *gorm.DB) contract.SettingRepository {
	return &settingRepository{db: db}
}

func (r *settingRepository) Get(siteID *uint, group, key string) (*model.Setting, error) {
	var setting model.Setting
	query := r.db.Where("`group` = ? AND `key` = ?", group, key)
	if siteID == nil {
		query = query.Where("site_id IS NULL")
	} else {
		query = query.Where("site_id = ?", *siteID)
	}
	err := query.First(&setting).Error
	return &setting, err
}

func (r *settingRepository) GetByGroup(siteID *uint, group string) ([]*model.Setting, error) {
	var settings []model.Setting
	query := r.db.Where("`group` = ?", group)
	if siteID == nil {
		query = query.Where("site_id IS NULL")
	} else {
		query = query.Where("site_id = ?", *siteID)
	}
	err := query.Find(&settings).Error
	return settings, err
}

func (r *settingRepository) Create(setting *model.Setting) error {
	return r.db.Create(setting).Error
}

func (r *settingRepository) Update(setting *model.Setting) error {
	return r.db.Save(setting).Error
}

func (r *settingRepository) Upsert(setting *model.Setting) error {
	return r.db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "site_id"}, {Name: "group"}, {Name: "key"}},
		DoUpdates: clause.AssignmentColumns([]string{"value"}),
	}).Create(setting).Error
} 