/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/recommendation/domain/contract/RecommendationStrategy.go
 * @Description: Defines the interface for recommendation strategies.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package contract

import "gacms/internal/modules/contenttype/domain/model"

// RecommendationStrategy defines the interface for different content recommendation algorithms.
type RecommendationStrategy interface {
	// GetRecommendations takes a content item (e.g., a post) and returns a list of recommended items.
	GetRecommendations(item *model.ContentItem, siteID uint, limit int) ([]*model.ContentItem, error)
} 