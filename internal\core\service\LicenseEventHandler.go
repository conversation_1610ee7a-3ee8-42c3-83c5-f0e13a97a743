/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/LicenseEventHandler.go
 * @Description: 许可证事件处理器，通过事件观察者模式处理许可证相关事件
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"fmt"

	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// DefaultLicenseEventHandler 默认许可证事件处理器
type DefaultLicenseEventHandler struct {
	store         contract.LicenseStore
	moduleManager *ModuleManager
	logger        *zap.Logger
}

// DefaultLicenseEventHandlerParams 许可证事件处理器参数
type DefaultLicenseEventHandlerParams struct {
	fx.In

	Store         contract.LicenseStore
	ModuleManager *ModuleManager
	Logger        *zap.Logger
}

// NewDefaultLicenseEventHandler 创建默认许可证事件处理器
func NewDefaultLicenseEventHandler(params DefaultLicenseEventHandlerParams) contract.LicenseEventHandler {
	return &DefaultLicenseEventHandler{
		store:         params.Store,
		moduleManager: params.ModuleManager,
		logger:        params.Logger,
	}
}

// Handle 处理事件
func (h *DefaultLicenseEventHandler) Handle(event contract.Event) error {
	// 检查是否是许可证事件
	if !h.isLicenseEvent(event) {
		return nil
	}

	// 解析许可证事件
	licenseEvent, err := h.parseLicenseEvent(event)
	if err != nil {
		h.logger.Error("Failed to parse license event", zap.Error(err))
		return err
	}

	// 处理许可证事件
	return h.HandleLicenseEvent(*licenseEvent)
}

// SupportedEvents 返回支持的事件类型
func (h *DefaultLicenseEventHandler) SupportedEvents() []contract.EventName {
	return []contract.EventName{
		"license.activated",
		"license.deactivated",
		"license.validated",
		"license.expired",
		"license.invalid",
	}
}

// HandlerName 返回处理器名称
func (h *DefaultLicenseEventHandler) HandlerName() string {
	return "license-event-handler"
}

// HandleLicenseEvent 处理许可证事件
func (h *DefaultLicenseEventHandler) HandleLicenseEvent(event contract.LicenseEvent) error {
	h.logger.Info("Handling license event",
		zap.String("type", string(event.Type)),
		zap.String("module", event.ModuleName),
		zap.String("validator", event.Validator),
		zap.Bool("success", event.Success),
	)

	switch event.Type {
	case contract.LicenseActivated:
		return h.handleLicenseActivated(event)
	case contract.LicenseDeactivated:
		return h.handleLicenseDeactivated(event)
	case contract.LicenseValidated:
		return h.handleLicenseValidated(event)
	case contract.LicenseExpired:
		return h.handleLicenseExpired(event)
	case contract.LicenseInvalid:
		return h.handleLicenseInvalid(event)
	default:
		h.logger.Warn("Unknown license event type", zap.String("type", string(event.Type)))
		return nil
	}
}

// handleLicenseActivated 处理许可证激活事件
func (h *DefaultLicenseEventHandler) handleLicenseActivated(event contract.LicenseEvent) error {
	h.logger.Info("License activated",
		zap.String("module", event.ModuleName),
		zap.String("validator", event.Validator),
	)

	// 记录许可证使用
	ctx := context.Background()
	usage := &contract.LicenseUsage{
		LicenseKey:  event.LicenseKey,
		ModuleName:  event.ModuleName,
		ActivatedAt: event.Timestamp,
		LastUsedAt:  event.Timestamp,
		UsageCount:  1,
		IsActive:    true,
	}

	if err := h.store.SaveLicenseUsage(ctx, usage); err != nil {
		h.logger.Error("Failed to save license usage", zap.Error(err))
		// 不返回错误，因为这不是关键操作
	}

	// 尝试激活模块（如果模块支持）
	if err := h.moduleManager.ActivateModule(event.ModuleName); err != nil {
		h.logger.Warn("Failed to activate module after license activation",
			zap.String("module", event.ModuleName),
			zap.Error(err),
		)
		// 不返回错误，因为许可证激活已经成功
	}

	return nil
}

// handleLicenseDeactivated 处理许可证停用事件
func (h *DefaultLicenseEventHandler) handleLicenseDeactivated(event contract.LicenseEvent) error {
	h.logger.Info("License deactivated",
		zap.String("module", event.ModuleName),
		zap.String("validator", event.Validator),
	)

	// 更新许可证使用记录
	ctx := context.Background()
	usage, err := h.store.GetLicenseUsage(ctx, event.LicenseKey, event.ModuleName)
	if err == nil && usage != nil {
		usage.IsActive = false
		usage.LastUsedAt = event.Timestamp

		if err := h.store.SaveLicenseUsage(ctx, usage); err != nil {
			h.logger.Error("Failed to update license usage", zap.Error(err))
		}
	}

	// 停用模块（如果模块正在运行）
	if err := h.moduleManager.DeactivateModule(event.ModuleName); err != nil {
		h.logger.Warn("Failed to deactivate module after license deactivation",
			zap.String("module", event.ModuleName),
			zap.Error(err),
		)
	}

	return nil
}

// handleLicenseValidated 处理许可证验证事件
func (h *DefaultLicenseEventHandler) handleLicenseValidated(event contract.LicenseEvent) error {
	h.logger.Debug("License validated",
		zap.String("module", event.ModuleName),
		zap.String("validator", event.Validator),
		zap.Bool("success", event.Success),
	)

	// 更新许可证使用记录
	ctx := context.Background()
	usage, err := h.store.GetLicenseUsage(ctx, event.LicenseKey, event.ModuleName)
	if err == nil && usage != nil {
		usage.LastUsedAt = event.Timestamp
		usage.UsageCount++

		if err := h.store.SaveLicenseUsage(ctx, usage); err != nil {
			h.logger.Error("Failed to update license usage", zap.Error(err))
		}
	}

	return nil
}

// handleLicenseExpired 处理许可证过期事件
func (h *DefaultLicenseEventHandler) handleLicenseExpired(event contract.LicenseEvent) error {
	h.logger.Warn("License expired",
		zap.String("module", event.ModuleName),
		zap.String("validator", event.Validator),
	)

	// 停用模块
	if err := h.moduleManager.DeactivateModule(event.ModuleName); err != nil {
		h.logger.Error("Failed to deactivate module after license expiration",
			zap.String("module", event.ModuleName),
			zap.Error(err),
		)
	}

	// 更新许可证使用记录
	ctx := context.Background()
	usage, err := h.store.GetLicenseUsage(ctx, event.LicenseKey, event.ModuleName)
	if err == nil && usage != nil {
		usage.IsActive = false
		usage.LastUsedAt = event.Timestamp

		if err := h.store.SaveLicenseUsage(ctx, usage); err != nil {
			h.logger.Error("Failed to update license usage", zap.Error(err))
		}
	}

	return nil
}

// handleLicenseInvalid 处理许可证无效事件
func (h *DefaultLicenseEventHandler) handleLicenseInvalid(event contract.LicenseEvent) error {
	h.logger.Error("License invalid",
		zap.String("module", event.ModuleName),
		zap.String("validator", event.Validator),
		zap.String("error", event.ErrorMsg),
	)

	// 停用模块
	if err := h.moduleManager.DeactivateModule(event.ModuleName); err != nil {
		h.logger.Error("Failed to deactivate module after license invalidation",
			zap.String("module", event.ModuleName),
			zap.Error(err),
		)
	}

	// 禁用模块（如果许可证验证失败）
	if err := h.moduleManager.DisableModule(event.ModuleName); err != nil {
		h.logger.Error("Failed to disable module after license invalidation",
			zap.String("module", event.ModuleName),
			zap.Error(err),
		)
	}

	return nil
}

// isLicenseEvent 检查是否是许可证事件
func (h *DefaultLicenseEventHandler) isLicenseEvent(event contract.Event) bool {
	eventName := string(event.Name())
	return len(eventName) > 8 && eventName[:8] == "license."
}

// parseLicenseEvent 解析许可证事件
func (h *DefaultLicenseEventHandler) parseLicenseEvent(event contract.Event) (*contract.LicenseEvent, error) {
	payload := event.Payload()
	
	licenseEvent, ok := payload.(contract.LicenseEvent)
	if !ok {
		return nil, fmt.Errorf("invalid license event payload type: %T", payload)
	}

	return &licenseEvent, nil
}

// LicenseEventHandlerModule 许可证事件处理器模块
func LicenseEventHandlerModule() fx.Option {
	return fx.Options(
		fx.Provide(
			fx.Annotate(
				NewDefaultLicenseEventHandler,
				fx.As(new(contract.LicenseEventHandler)),
				fx.As(new(contract.EventHandler)),
				fx.ResultTags(`group:"event_handlers"`),
			),
		),
	)
}
