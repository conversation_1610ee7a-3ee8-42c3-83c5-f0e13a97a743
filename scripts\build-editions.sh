#!/bin/bash

# GACMS 版本分级编译脚本
# Author: <PERSON><PERSON>eh
# Email: <EMAIL>

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 版本定义
EDITIONS=("personal" "professional" "business")
VERSION=${VERSION:-"1.0.0"}
BUILD_DIR=${BUILD_DIR:-"./dist"}
SOURCE_DIR=${SOURCE_DIR:-"."}

# 编译标志
LDFLAGS="-s -w -X main.Version=${VERSION} -X main.BuildTime=$(date -u +%Y-%m-%dT%H:%M:%SZ)"

# 打印信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 清理构建目录
clean_build_dir() {
    print_info "Cleaning build directory: ${BUILD_DIR}"
    rm -rf "${BUILD_DIR}"
    mkdir -p "${BUILD_DIR}"
}

# 编译指定版本
build_edition() {
    local edition=$1
    local output_name="gacms-${edition}-${VERSION}"
    local build_tags="${edition}"
    
    print_info "Building ${edition} edition..."
    
    # 设置输出路径
    local output_path="${BUILD_DIR}/${output_name}"
    
    # 根据操作系统设置可执行文件扩展名
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        output_path="${output_path}.exe"
    fi
    
    # 编译命令
    go build \
        -tags="${build_tags}" \
        -ldflags="${LDFLAGS}" \
        -o "${output_path}" \
        "${SOURCE_DIR}/cmd/gacms"
    
    if [ $? -eq 0 ]; then
        print_success "Built ${edition} edition: ${output_path}"
        
        # 显示文件大小
        local file_size=$(du -h "${output_path}" | cut -f1)
        print_info "File size: ${file_size}"
        
        # 创建版本信息文件
        create_version_info "${edition}" "${output_path}"
    else
        print_error "Failed to build ${edition} edition"
        return 1
    fi
}

# 创建版本信息文件
create_version_info() {
    local edition=$1
    local binary_path=$2
    local info_file="${BUILD_DIR}/gacms-${edition}-${VERSION}.info"
    
    cat > "${info_file}" << EOF
GACMS ${edition^} Edition
Version: ${VERSION}
Build Time: $(date -u +%Y-%m-%dT%H:%M:%SZ)
Build Tags: ${edition}
Binary: $(basename "${binary_path}")
File Size: $(du -h "${binary_path}" | cut -f1)
Go Version: $(go version)
Platform: $(go env GOOS)/$(go env GOARCH)

Features Included:
EOF

    # 根据版本添加功能列表
    case "${edition}" in
        "community")
            cat >> "${info_file}" << EOF
- Basic Content Management
- Basic Theme Support
- Basic User Management
- Community Support

Limits:
- Max Sites: 1
- Max Users: 5
- Max Storage: 1GB
- Max Bandwidth: 10GB
- Max Pages: 50
- Max Posts: 100
EOF
            ;;
        "personal")
            cat >> "${info_file}" << EOF
- All Community Features
- Advanced Theme Support
- Basic SEO Tools
- Email Support
- Remove Copyright

Limits:
- Max Sites: 3
- Max Users: 20
- Max Storage: 5GB
- Max Bandwidth: 50GB
- Max Pages: 200
- Max Posts: 500
EOF
            ;;
        "professional")
            cat >> "${info_file}" << EOF
- All Personal Features
- Advanced SEO Tools
- Workflow Management
- Advanced User Permissions
- API Access
- Priority Support

Limits:
- Max Sites: 10
- Max Users: 100
- Max Storage: 20GB
- Max Bandwidth: 200GB
- Max Pages: 1000
- Max Posts: 2000
EOF
            ;;
        "business")
            cat >> "${info_file}" << EOF
- All Professional Features
- Unlimited Usage
- Advanced Workflow
- Business Security
- Custom Development Support
- Dedicated Account Manager
- SLA Guarantee

Limits:
- Unlimited
EOF
            ;;
    esac
    
    print_info "Created version info: ${info_file}"
}

# 验证编译结果
verify_build() {
    local edition=$1
    local binary_path="${BUILD_DIR}/gacms-${edition}-${VERSION}"
    
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        binary_path="${binary_path}.exe"
    fi
    
    if [ ! -f "${binary_path}" ]; then
        print_error "Binary not found: ${binary_path}"
        return 1
    fi
    
    # 检查二进制文件是否可执行
    if [ ! -x "${binary_path}" ]; then
        print_error "Binary is not executable: ${binary_path}"
        return 1
    fi
    
    # 尝试运行版本检查
    print_info "Verifying ${edition} edition..."
    if "${binary_path}" --version > /dev/null 2>&1; then
        print_success "Verification passed for ${edition} edition"
    else
        print_warning "Version check failed for ${edition} edition (may be normal if --version not implemented)"
    fi
}

# 创建发布包
create_release_package() {
    local edition=$1
    local package_dir="${BUILD_DIR}/gacms-${edition}-${VERSION}"
    local binary_name="gacms-${edition}-${VERSION}"
    
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        binary_name="${binary_name}.exe"
    fi
    
    print_info "Creating release package for ${edition} edition..."
    
    # 创建包目录
    mkdir -p "${package_dir}"
    
    # 复制二进制文件
    cp "${BUILD_DIR}/${binary_name}" "${package_dir}/"
    
    # 复制版本信息
    cp "${BUILD_DIR}/gacms-${edition}-${VERSION}.info" "${package_dir}/"
    
    # 复制文档
    if [ -f "README.md" ]; then
        cp "README.md" "${package_dir}/"
    fi
    
    if [ -f "LICENSE" ]; then
        cp "LICENSE" "${package_dir}/"
    fi
    
    # 创建安装脚本
    cat > "${package_dir}/install.sh" << 'EOF'
#!/bin/bash
echo "Installing GACMS..."
chmod +x gacms-*
echo "Installation completed!"
echo "Run './gacms-*' to start the application"
EOF
    
    chmod +x "${package_dir}/install.sh"
    
    # 创建压缩包
    cd "${BUILD_DIR}"
    tar -czf "gacms-${edition}-${VERSION}.tar.gz" "gacms-${edition}-${VERSION}/"
    cd - > /dev/null
    
    print_success "Created release package: ${BUILD_DIR}/gacms-${edition}-${VERSION}.tar.gz"
}

# 显示帮助信息
show_help() {
    cat << EOF
GACMS Edition Build Script

Usage: $0 [OPTIONS] [EDITIONS...]

OPTIONS:
    -h, --help          Show this help message
    -c, --clean         Clean build directory before building
    -v, --version       Set version (default: ${VERSION})
    -o, --output        Set output directory (default: ${BUILD_DIR})
    -p, --package       Create release packages
    --verify            Verify built binaries

EDITIONS:
    community           Build community edition (default if no edition specified)
    personal            Build personal edition
    professional        Build professional edition
    business            Build business edition
    all                 Build all editions

EXAMPLES:
    $0                          # Build community edition
    $0 all                      # Build all editions
    $0 professional business    # Build professional and business editions
    $0 -c -p all               # Clean, build all editions, and create packages
    $0 -v 2.0.0 community      # Build community edition with version 2.0.0

EOF
}

# 主函数
main() {
    local editions_to_build=()
    local clean_build=false
    local create_packages=false
    local verify_builds=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--clean)
                clean_build=true
                shift
                ;;
            -v|--version)
                VERSION="$2"
                shift 2
                ;;
            -o|--output)
                BUILD_DIR="$2"
                shift 2
                ;;
            -p|--package)
                create_packages=true
                shift
                ;;
            --verify)
                verify_builds=true
                shift
                ;;
            all)
                editions_to_build=("${EDITIONS[@]}")
                shift
                ;;
            community|personal|professional|business)
                editions_to_build+=("$1")
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定版本，默认构建社区版
    if [ ${#editions_to_build[@]} -eq 0 ]; then
        editions_to_build=("community")
    fi
    
    print_info "GACMS Edition Build Script"
    print_info "Version: ${VERSION}"
    print_info "Build Directory: ${BUILD_DIR}"
    print_info "Editions to build: ${editions_to_build[*]}"
    
    # 清理构建目录
    if [ "$clean_build" = true ]; then
        clean_build_dir
    else
        mkdir -p "${BUILD_DIR}"
    fi
    
    # 构建各个版本
    local build_success=true
    for edition in "${editions_to_build[@]}"; do
        if ! build_edition "${edition}"; then
            build_success=false
        fi
    done
    
    # 验证构建结果
    if [ "$verify_builds" = true ] && [ "$build_success" = true ]; then
        for edition in "${editions_to_build[@]}"; do
            verify_build "${edition}"
        done
    fi
    
    # 创建发布包
    if [ "$create_packages" = true ] && [ "$build_success" = true ]; then
        for edition in "${editions_to_build[@]}"; do
            create_release_package "${edition}"
        done
    fi
    
    if [ "$build_success" = true ]; then
        print_success "All builds completed successfully!"
        print_info "Build artifacts are in: ${BUILD_DIR}"
    else
        print_error "Some builds failed!"
        exit 1
    fi
}

# 运行主函数
main "$@"
