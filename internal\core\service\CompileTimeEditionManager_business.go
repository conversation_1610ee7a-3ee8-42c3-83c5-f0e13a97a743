//go:build business
// +build business

/*
Author: <PERSON><PERSON> Nieh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package service

import (
	"fmt"
	"gacms/pkg/contract"
)

// BusinessEditionCompiler 商业版编译时管理器
type BusinessEditionCompiler struct {
	BaseEditionCompiler
}

func init() {
	// 覆盖默认的编译时版本管理器
	compiledEditionManager = &BusinessEditionCompiler{
		BaseEditionCompiler: BaseEditionCompiler{
			edition: contract.EditionBusiness,
			features: map[string]bool{
				// 基础功能模块（商业版包含）
				"basic_content": true,
				"basic_theme":   true,
				"basic_seo":     true,
				"basic_user":    true,
				
				// 高级功能模块（商业版包含）
				"advanced_theme": true,
				"advanced_seo":   true,
				"workflow":       true,
				"api_access":     true,
				"advanced_user":  true,
				
				// 企业功能模块（商业版包含）
				"enterprise_api":         true,
				"enterprise_security":    true,
				"enterprise_integration": true,
				"enterprise_analytics":   true,
			},
			limits: map[string]bool{
				"max_sites":                   true,
				"max_admin_users":             true,
				"max_pages":                   true,
				"max_posts":                   true,
				"api_calls_per_day":           true,
				"max_file_size":               true,
				"max_concurrent_connections":  true,
			},
		},
	}
	
	// 更新编译时功能开关
	updateBusinessFeatureFlags()
}

// updateBusinessFeatureFlags 更新商业版功能开关
func updateBusinessFeatureFlags() {
	// 基础功能（所有版本都包含）
	BasicContentEnabled = true
	BasicThemeEnabled   = true
	BasicSEOEnabled     = true
	BasicUserEnabled    = true
	
	// 高级功能（商业版包含）
	AdvancedThemeEnabled = true
	AdvancedSEOEnabled   = true
	WorkflowEnabled      = true
	APIAccessEnabled     = true
	AdvancedUserEnabled  = true
	
	// 企业功能（商业版包含）
	EnterpriseAPIEnabled         = true
	EnterpriseSecurityEnabled    = true
	EnterpriseIntegrationEnabled = true
	EnterpriseAnalyticsEnabled   = true
}

// 商业版编译时常量
const (
	CompiledEditionName = "business"
)

// 商业版特有的编译时检查函数
func IsBusinessFeatureEnabled(featureName string) bool {
	businessFeatures := map[string]bool{
		"basic_content":              true,
		"basic_theme":                true,
		"basic_seo":                  true,
		"basic_user":                 true,
		"advanced_theme":             true,
		"advanced_seo":               true,
		"workflow":                   true,
		"api_access":                 true,
		"advanced_user":              true,
		"enterprise_api":             true,
		"enterprise_security":        true,
		"enterprise_integration":     true,
		"enterprise_analytics":       true,
	}
	
	return businessFeatures[featureName]
}

// 商业版是全能版，无需限制配置和功能列表
// 检测到商业版许可证后直接允许所有功能和跳过所有限制

// 商业版编译时验证（简化）
func ValidateBusinessCompilation() error {
	manager := GetCompiledEditionManager()

	// 只验证编译版本
	if manager.GetCompiledEdition() != contract.EditionBusiness {
		return fmt.Errorf("expected business edition, got %s", manager.GetCompiledEdition())
	}

	// 商业版是全能版，不需要功能枚举验证
	return nil
}

// 商业版是全能版，不需要功能检查和限制检查
// 检测到商业版许可证后直接跳过所有检查

// 商业版编译时优化提示
func GetBusinessOptimizationHints() []string {
	return []string{
		"All features are enabled in business edition",
		"All limits are unlimited in business edition",
		"Enterprise features are available",
		"Advanced user management is enabled",
		"Full API access is granted",
		"Enterprise security features are active",
		"Enterprise integration capabilities are available",
		"Enterprise analytics features are enabled",
	}
}

// 商业版许可证要求（简化）
func GetBusinessLicenseRequirements() map[string]interface{} {
	return map[string]interface{}{
		"edition":                  "business",
		"unlimited_access":         true,
		"all_features_enabled":     true,
		"no_limits":               true,
		"priority_support":        true,
		"sla_guarantee":           true,
	}
}
