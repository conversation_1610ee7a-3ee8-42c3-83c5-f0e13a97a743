/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-13
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-13
 * @FilePath: internal/modules/user/events/AdminUserEvents.go
 * @Description: Defines events related to administrator user management.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package events

import (
	"context"

	"github.com/niecl/GACMS/internal/modules/user/application/dto"
	"github.com/niecl/GACMS/internal/modules/user/domain/model"
)

// --- Create Admin User Events ---

// CreateAdminUserEvent is the "Action Event" dispatched to create a new admin user.
// It carries the necessary data for user creation.
type CreateAdminUserEvent struct {
	ctx context.Context
	DTO *dto.CreateUserRequest
}

// NewCreateAdminUserEvent creates a new CreateAdminUserEvent.
func NewCreateAdminUserEvent(ctx context.Context, userDTO *dto.CreateUserRequest) *CreateAdminUserEvent {
	return &CreateAdminUserEvent{
		ctx: ctx,
		DTO: userDTO,
	}
}

// Context returns the event's context.
func (e *CreateAdminUserEvent) Context() context.Context {
	return e.ctx
}

// AdminUserCreatedEvent is the "Fact Event" dispatched after an admin user has been successfully created.
// It carries the newly created user's data.
type AdminUserCreatedEvent struct {
	ctx  context.Context
	User *model.User
}

// NewAdminUserCreatedEvent creates a new AdminUserCreatedEvent.
func NewAdminUserCreatedEvent(ctx context.Context, user *model.User) *AdminUserCreatedEvent {
	return &AdminUserCreatedEvent{
		ctx:  ctx,
		User: user,
	}
}

// Context returns the event's context.
func (e *AdminUserCreatedEvent) Context() context.Context {
	return e.ctx
} 