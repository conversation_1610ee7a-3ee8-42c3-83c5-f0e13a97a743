<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

SEO分析页面 - 用于分析网站SEO数据和搜索引擎表现
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - SEO分析</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <!-- 引入图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment"></script>
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .stat-card {
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-3px);
        }

        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }

        .progress-bar {
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="data_stats.html" class="hover:text-white">数据报告</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">SEO分析</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">SEO分析</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="exportBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <i class="fas fa-download mr-2"></i>
                            导出报告
                        </button>
                        <button class="flex items-center justify-center bg-gradient-to-r from-gray-600 to-gray-700 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg relative overflow-hidden action-button">
                            <i class="fas fa-cog mr-2"></i>
                            设置
                        </button>
                    </div>
                </div>
            </div>

            <!-- 日期和站点筛选 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap gap-4 items-center">
                    <div class="flex items-center">
                        <span class="text-gray-400 mr-2">时间范围:</span>
                        <div class="relative">
                            <select id="dateRange" class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white appearance-none pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="today">今天</option>
                                <option value="yesterday">昨天</option>
                                <option value="7days" selected>最近7天</option>
                                <option value="30days">最近30天</option>
                                <option value="90days">最近90天</option>
                                <option value="custom">自定义</option>
                            </select>
                            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                                <i class="fas fa-chevron-down text-xs"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <span class="text-gray-400 mr-2">站点选择:</span>
                        <div class="relative">
                            <select id="siteSelection" class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white appearance-none pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="all" selected>所有站点</option>
                                <option value="main">主站</option>
                                <option value="blog">博客</option>
                                <option value="store">商城</option>
                            </select>
                            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                                <i class="fas fa-chevron-down text-xs"></i>
                            </div>
                        </div>
                    </div>

                    <div id="customDateRange" class="flex items-center space-x-2 hidden">
                        <input type="date" class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="text-gray-400">至</span>
                        <input type="date" class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div class="ml-auto">
                        <button id="refreshBtn" class="flex items-center justify-center bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-all">
                            <i class="fas fa-sync-alt mr-2"></i>
                            刷新数据
                        </button>
                    </div>
                </div>
            </div>

            <!-- SEO概览卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-6">
                <!-- 总收录量 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 stat-card">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-database text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">总收录量</div>
                            <div class="text-xl font-semibold text-white">12,856</div>
                            <div class="text-xs text-green-400 mt-0.5">
                                <i class="fas fa-arrow-up mr-1"></i>
                                345 较上月
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 关键词排名 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 stat-card">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-sort-amount-up text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">关键词排名</div>
                            <div class="text-xl font-semibold text-white">854</div>
                            <div class="text-xs text-green-400 mt-0.5">
                                <i class="fas fa-arrow-up mr-1"></i>
                                52 较上月
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 外部链接 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 stat-card">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-link text-purple-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">外部链接</div>
                            <div class="text-xl font-semibold text-white">1,435</div>
                            <div class="text-xs text-green-400 mt-0.5">
                                <i class="fas fa-arrow-up mr-1"></i>
                                86 较上月
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 平均搜索排名 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 stat-card">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-trophy text-yellow-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">平均排名</div>
                            <div class="text-xl font-semibold text-white">12.4</div>
                            <div class="text-xs text-green-400 mt-0.5">
                                <i class="fas fa-arrow-up mr-1"></i>
                                2.3 较上月
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- SEO趋势图表 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold text-white mb-4">SEO趋势分析</h3>
                <div class="chart-container">
                    <canvas id="seoTrendChart"></canvas>
                </div>
            </div>

            <!-- 搜索引擎流量和关键词分析 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- 搜索引擎流量 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">搜索引擎流量分布</h3>
                    <div class="chart-container" style="height: 250px;">
                        <canvas id="searchEngineChart"></canvas>
                    </div>
                    <div class="grid grid-cols-2 gap-4 mt-4">
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                            <span class="text-sm text-gray-400">百度 (56%)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-purple-500 mr-2"></span>
                            <span class="text-sm text-gray-400">Google (28%)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                            <span class="text-sm text-gray-400">必应 (12%)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></span>
                            <span class="text-sm text-gray-400">其他 (4%)</span>
                        </div>
                    </div>
                </div>
                
                <!-- 关键词表现 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">关键词排名分布</h3>
                    <div class="chart-container" style="height: 250px;">
                        <canvas id="keywordRankChart"></canvas>
                    </div>
                    <div class="grid grid-cols-3 gap-4 mt-4">
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                            <span class="text-sm text-gray-400">前3名 (18%)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                            <span class="text-sm text-gray-400">4-10名 (35%)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-purple-500 mr-2"></span>
                            <span class="text-sm text-gray-400">11-20名 (28%)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></span>
                            <span class="text-sm text-gray-400">21-30名 (12%)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-red-500 mr-2"></span>
                            <span class="text-sm text-gray-400">30名以后 (7%)</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 页面SEO得分分析 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold text-white mb-4">页面SEO得分分析</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-6">
                    <!-- 页面标题优化 -->
                    <div class="bg-gray-800/20 p-5 rounded-xl">
                        <div class="flex justify-between items-center mb-2">
                            <h4 class="text-white">页面标题</h4>
                            <span class="bg-green-900/30 text-green-400 text-xs px-2 py-1 rounded-md border border-green-900">优秀</span>
                        </div>
                        <div class="mb-2">
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-400">得分</span>
                                <span class="text-white">92/100</span>
                            </div>
                            <div class="progress-bar bg-gray-700">
                                <div class="bg-gradient-to-r from-green-500 to-green-600 h-full" style="width: 92%"></div>
                            </div>
                        </div>
                        <ul class="text-xs text-gray-400 mt-3">
                            <li class="mb-1 flex">
                                <i class="fas fa-check-circle text-green-500 mr-1 mt-0.5"></i>
                                <span>98%的页面标题符合长度要求</span>
                            </li>
                            <li class="flex">
                                <i class="fas fa-check-circle text-green-500 mr-1 mt-0.5"></i>
                                <span>95%的页面包含主要关键词</span>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- Meta描述 -->
                    <div class="bg-gray-800/20 p-5 rounded-xl">
                        <div class="flex justify-between items-center mb-2">
                            <h4 class="text-white">Meta描述</h4>
                            <span class="bg-blue-900/30 text-blue-400 text-xs px-2 py-1 rounded-md border border-blue-900">良好</span>
                        </div>
                        <div class="mb-2">
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-400">得分</span>
                                <span class="text-white">78/100</span>
                            </div>
                            <div class="progress-bar bg-gray-700">
                                <div class="bg-gradient-to-r from-blue-500 to-blue-600 h-full" style="width: 78%"></div>
                            </div>
                        </div>
                        <ul class="text-xs text-gray-400 mt-3">
                            <li class="mb-1 flex">
                                <i class="fas fa-check-circle text-green-500 mr-1 mt-0.5"></i>
                                <span>85%的页面有完整描述</span>
                            </li>
                            <li class="flex">
                                <i class="fas fa-exclamation-circle text-yellow-500 mr-1 mt-0.5"></i>
                                <span>12%的描述需要优化</span>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 移动适配 -->
                    <div class="bg-gray-800/20 p-5 rounded-xl">
                        <div class="flex justify-between items-center mb-2">
                            <h4 class="text-white">移动适配</h4>
                            <span class="bg-green-900/30 text-green-400 text-xs px-2 py-1 rounded-md border border-green-900">优秀</span>
                        </div>
                        <div class="mb-2">
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-400">得分</span>
                                <span class="text-white">96/100</span>
                            </div>
                            <div class="progress-bar bg-gray-700">
                                <div class="bg-gradient-to-r from-green-500 to-green-600 h-full" style="width: 96%"></div>
                            </div>
                        </div>
                        <ul class="text-xs text-gray-400 mt-3">
                            <li class="mb-1 flex">
                                <i class="fas fa-check-circle text-green-500 mr-1 mt-0.5"></i>
                                <span>所有页面均支持移动设备</span>
                            </li>
                            <li class="flex">
                                <i class="fas fa-check-circle text-green-500 mr-1 mt-0.5"></i>
                                <span>点击区域适合移动操作</span>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 页面速度 -->
                    <div class="bg-gray-800/20 p-5 rounded-xl">
                        <div class="flex justify-between items-center mb-2">
                            <h4 class="text-white">页面速度</h4>
                            <span class="bg-yellow-900/30 text-yellow-400 text-xs px-2 py-1 rounded-md border border-yellow-900">一般</span>
                        </div>
                        <div class="mb-2">
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-400">得分</span>
                                <span class="text-white">68/100</span>
                            </div>
                            <div class="progress-bar bg-gray-700">
                                <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 h-full" style="width: 68%"></div>
                            </div>
                        </div>
                        <ul class="text-xs text-gray-400 mt-3">
                            <li class="mb-1 flex">
                                <i class="fas fa-exclamation-circle text-yellow-500 mr-1 mt-0.5"></i>
                                <span>32%的页面加载速度较慢</span>
                            </li>
                            <li class="flex">
                                <i class="fas fa-exclamation-circle text-yellow-500 mr-1 mt-0.5"></i>
                                <span>建议优化图片和脚本文件</span>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
                    <!-- 内容质量 -->
                    <div class="bg-gray-800/20 p-5 rounded-xl">
                        <div class="flex justify-between items-center mb-2">
                            <h4 class="text-white">内容质量</h4>
                            <span class="bg-blue-900/30 text-blue-400 text-xs px-2 py-1 rounded-md border border-blue-900">良好</span>
                        </div>
                        <div class="mb-2">
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-400">得分</span>
                                <span class="text-white">82/100</span>
                            </div>
                            <div class="progress-bar bg-gray-700">
                                <div class="bg-gradient-to-r from-blue-500 to-blue-600 h-full" style="width: 82%"></div>
                            </div>
                        </div>
                        <ul class="text-xs text-gray-400 mt-3">
                            <li class="mb-1 flex">
                                <i class="fas fa-check-circle text-green-500 mr-1 mt-0.5"></i>
                                <span>内容丰富且原创</span>
                            </li>
                            <li class="flex">
                                <i class="fas fa-exclamation-circle text-yellow-500 mr-1 mt-0.5"></i>
                                <span>部分页面内容可再丰富</span>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 内部链接 -->
                    <div class="bg-gray-800/20 p-5 rounded-xl">
                        <div class="flex justify-between items-center mb-2">
                            <h4 class="text-white">内部链接</h4>
                            <span class="bg-green-900/30 text-green-400 text-xs px-2 py-1 rounded-md border border-green-900">优秀</span>
                        </div>
                        <div class="mb-2">
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-400">得分</span>
                                <span class="text-white">94/100</span>
                            </div>
                            <div class="progress-bar bg-gray-700">
                                <div class="bg-gradient-to-r from-green-500 to-green-600 h-full" style="width: 94%"></div>
                            </div>
                        </div>
                        <ul class="text-xs text-gray-400 mt-3">
                            <li class="mb-1 flex">
                                <i class="fas fa-check-circle text-green-500 mr-1 mt-0.5"></i>
                                <span>链接结构清晰合理</span>
                            </li>
                            <li class="flex">
                                <i class="fas fa-check-circle text-green-500 mr-1 mt-0.5"></i>
                                <span>所有重要页面可在3次点击内到达</span>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 图片优化 -->
                    <div class="bg-gray-800/20 p-5 rounded-xl">
                        <div class="flex justify-between items-center mb-2">
                            <h4 class="text-white">图片优化</h4>
                            <span class="bg-yellow-900/30 text-yellow-400 text-xs px-2 py-1 rounded-md border border-yellow-900">一般</span>
                        </div>
                        <div class="mb-2">
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-400">得分</span>
                                <span class="text-white">72/100</span>
                            </div>
                            <div class="progress-bar bg-gray-700">
                                <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 h-full" style="width: 72%"></div>
                            </div>
                        </div>
                        <ul class="text-xs text-gray-400 mt-3">
                            <li class="mb-1 flex">
                                <i class="fas fa-check-circle text-green-500 mr-1 mt-0.5"></i>
                                <span>85%的图片有alt属性</span>
                            </li>
                            <li class="flex">
                                <i class="fas fa-exclamation-circle text-yellow-500 mr-1 mt-0.5"></i>
                                <span>28%的图片需要压缩优化</span>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 网站安全 -->
                    <div class="bg-gray-800/20 p-5 rounded-xl">
                        <div class="flex justify-between items-center mb-2">
                            <h4 class="text-white">网站安全</h4>
                            <span class="bg-green-900/30 text-green-400 text-xs px-2 py-1 rounded-md border border-green-900">优秀</span>
                        </div>
                        <div class="mb-2">
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-400">得分</span>
                                <span class="text-white">98/100</span>
                            </div>
                            <div class="progress-bar bg-gray-700">
                                <div class="bg-gradient-to-r from-green-500 to-green-600 h-full" style="width: 98%"></div>
                            </div>
                        </div>
                        <ul class="text-xs text-gray-400 mt-3">
                            <li class="mb-1 flex">
                                <i class="fas fa-check-circle text-green-500 mr-1 mt-0.5"></i>
                                <span>全站HTTPS已启用</span>
                            </li>
                            <li class="flex">
                                <i class="fas fa-check-circle text-green-500 mr-1 mt-0.5"></i>
                                <span>无已知安全漏洞</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 热门关键词排名 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold text-white mb-4">热门关键词排名</h3>
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead class="text-gray-400 text-left">
                            <tr>
                                <th class="pb-4 px-2">关键词</th>
                                <th class="pb-4 px-2">搜索量</th>
                                <th class="pb-4 px-2">当前排名</th>
                                <th class="pb-4 px-2">排名变化</th>
                                <th class="pb-4 px-2">点击率</th>
                                <th class="pb-4 px-2">转化率</th>
                                <th class="pb-4 px-2">竞争度</th>
                            </tr>
                        </thead>
                        <tbody class="text-gray-300">
                            <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <span>内容管理系统</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">42,500</td>
                                <td class="py-4 px-2">
                                    <span class="px-2 py-1 rounded-md text-xs bg-green-900/30 text-green-400 border border-green-900">2</span>
                                </td>
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <i class="fas fa-arrow-up text-green-500 mr-1"></i>
                                        <span>3</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">18.4%</td>
                                <td class="py-4 px-2">6.7%</td>
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <div class="w-16 h-1.5 bg-gray-700 rounded-full overflow-hidden">
                                            <div class="h-full bg-red-500 rounded-full" style="width: 85%"></div>
                                        </div>
                                        <span class="ml-2">高</span>
                                    </div>
                                </td>
                            </tr>
                            <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <span>企业网站搭建</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">38,200</td>
                                <td class="py-4 px-2">
                                    <span class="px-2 py-1 rounded-md text-xs bg-green-900/30 text-green-400 border border-green-900">1</span>
                                </td>
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <i class="fas fa-minus text-gray-500 mr-1"></i>
                                        <span>0</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">22.6%</td>
                                <td class="py-4 px-2">8.3%</td>
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <div class="w-16 h-1.5 bg-gray-700 rounded-full overflow-hidden">
                                            <div class="h-full bg-red-500 rounded-full" style="width: 78%"></div>
                                        </div>
                                        <span class="ml-2">高</span>
                                    </div>
                                </td>
                            </tr>
                            <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <span>开源CMS系统</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">26,400</td>
                                <td class="py-4 px-2">
                                    <span class="px-2 py-1 rounded-md text-xs bg-blue-900/30 text-blue-400 border border-blue-900">4</span>
                                </td>
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <i class="fas fa-arrow-up text-green-500 mr-1"></i>
                                        <span>2</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">14.2%</td>
                                <td class="py-4 px-2">5.1%</td>
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <div class="w-16 h-1.5 bg-gray-700 rounded-full overflow-hidden">
                                            <div class="h-full bg-yellow-500 rounded-full" style="width: 65%"></div>
                                        </div>
                                        <span class="ml-2">中</span>
                                    </div>
                                </td>
                            </tr>
                            <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <span>网站模板下载</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">22,800</td>
                                <td class="py-4 px-2">
                                    <span class="px-2 py-1 rounded-md text-xs bg-purple-900/30 text-purple-400 border border-purple-900">8</span>
                                </td>
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <i class="fas fa-arrow-down text-red-500 mr-1"></i>
                                        <span>2</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">10.5%</td>
                                <td class="py-4 px-2">3.2%</td>
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <div class="w-16 h-1.5 bg-gray-700 rounded-full overflow-hidden">
                                            <div class="h-full bg-yellow-500 rounded-full" style="width: 55%"></div>
                                        </div>
                                        <span class="ml-2">中</span>
                                    </div>
                                </td>
                            </tr>
                            <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <span>响应式网站设计</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">18,500</td>
                                <td class="py-4 px-2">
                                    <span class="px-2 py-1 rounded-md text-xs bg-blue-900/30 text-blue-400 border border-blue-900">6</span>
                                </td>
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <i class="fas fa-arrow-up text-green-500 mr-1"></i>
                                        <span>5</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">12.8%</td>
                                <td class="py-4 px-2">4.5%</td>
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <div class="w-16 h-1.5 bg-gray-700 rounded-full overflow-hidden">
                                            <div class="h-full bg-green-500 rounded-full" style="width: 42%"></div>
                                        </div>
                                        <span class="ml-2">低</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 页面底部版权信息 -->
            <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
                © 2025 GACMS 后台管理系统 - 版本 v1.0.0
            </footer>
        </div>
    </main>

    <!-- JavaScript 导入区域 -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <script>
        /**
         * @function initCharts
         * @description 初始化所有图表
         */
        function initCharts() {
            // SEO趋势图表
            const seoTrendCtx = document.getElementById('seoTrendChart').getContext('2d');
            const seoTrendChart = new Chart(seoTrendCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
                    datasets: [
                        {
                            label: '总收录量',
                            data: [8560, 9240, 10120, 10580, 11200, 12100, 12856],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.3,
                            fill: true
                        },
                        {
                            label: '关键词排名数',
                            data: [620, 680, 710, 750, 790, 825, 854],
                            borderColor: '#a855f7',
                            backgroundColor: 'rgba(168, 85, 247, 0.1)',
                            tension: 0.3,
                            fill: true
                        },
                        {
                            label: '外部链接数',
                            data: [850, 950, 1050, 1150, 1250, 1350, 1435],
                            borderColor: '#22c55e',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            tension: 0.3,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                color: '#e0e0e0'
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#9CA3AF'
                            }
                        },
                        y: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#9CA3AF'
                            }
                        }
                    }
                }
            });

            // 搜索引擎流量饼图
            const searchEngineCtx = document.getElementById('searchEngineChart').getContext('2d');
            const searchEngineChart = new Chart(searchEngineCtx, {
                type: 'doughnut',
                data: {
                    labels: ['百度', 'Google', '必应', '其他'],
                    datasets: [{
                        data: [56, 28, 12, 4],
                        backgroundColor: [
                            '#3b82f6',
                            '#a855f7',
                            '#22c55e',
                            '#eab308'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    cutout: '65%'
                }
            });

            // 关键词排名分布饼图
            const keywordRankCtx = document.getElementById('keywordRankChart').getContext('2d');
            const keywordRankChart = new Chart(keywordRankCtx, {
                type: 'doughnut',
                data: {
                    labels: ['前3名', '4-10名', '11-20名', '21-30名', '30名以后'],
                    datasets: [{
                        data: [18, 35, 28, 12, 7],
                        backgroundColor: [
                            '#22c55e',
                            '#3b82f6',
                            '#a855f7',
                            '#eab308',
                            '#ef4444'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    cutout: '65%'
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initCharts();

            // 日期范围选择器逻辑
            const dateRangeSelect = document.getElementById('dateRange');
            const customDateRange = document.getElementById('customDateRange');
            
            if (dateRangeSelect && customDateRange) {
                dateRangeSelect.addEventListener('change', function() {
                    if (this.value === 'custom') {
                        customDateRange.classList.remove('hidden');
                    } else {
                        customDateRange.classList.add('hidden');
                    }
                });
            }

            // 刷新按钮事件
            const refreshBtn = document.getElementById('refreshBtn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function() {
                    // 模拟刷新数据
                    const btn = this;
                    btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> 刷新中...';
                    btn.disabled = true;
                    
                    setTimeout(() => {
                        btn.innerHTML = '<i class="fas fa-sync-alt mr-2"></i> 刷新数据';
                        btn.disabled = false;
                        
                        // 可以在这里重新加载图表数据
                        initCharts();
                    }, 1500);
                });
            }

            // 导出按钮事件
            const exportBtn = document.getElementById('exportBtn');
            if (exportBtn) {
                exportBtn.addEventListener('click', function() {
                    alert('报告导出功能将在此实现');
                });
            }
        });
    </script>
</body>
</html> 