/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: {current_date}
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: {current_date}
 * @FilePath: internal/modules/user/port/http/controller/AdminRoleController.go
 * @Description: HTTP controller for managing admin roles and permissions.
 *
 * © {current_year} GACMS. All rights reserved.
 */
package controller

import (
	"gacms/internal/modules/user/application/service"
	"gacms/internal/modules/user/domain/model"
	"github.com/gin-gonic/gin"
	"net/http"
)

// AdminRoleController handles HTTP requests related to role and permission management.
type AdminRoleController struct {
	roleMgmtSvc *service.RoleManagementService
}

// NewAdminRoleController creates a new instance of AdminRoleController.
func NewAdminRoleController(roleMgmtSvc *service.RoleManagementService) *AdminRoleController {
	return &AdminRoleController{roleMgmtSvc: roleMgmtSvc}
}

// RegisterRoutes registers the routes for admin role management.
func (c *AdminRoleController) RegisterRoutes(group *gin.RouterGroup) {
	roles := group.Group("/roles")
	{
		roles.POST("", c.CreateRole)
		roles.POST("/assign-permission", c.AssignPermissionToRole)
		roles.POST("/assign-user", c.AssignRoleToUser)
	}
}

// CreateRole handles the request to create a new admin role.
// @Summary Create Admin Role
// @Description Creates a new role for administrative users.
// @Tags Admin
// @Accept json
// @Produce json
// @Param role body service.CreateAdminRoleDTO true "Role Creation Data"
// @Success 201 {object} model.Role
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /admin/roles [post]
func (c *AdminRoleController) CreateRole(ctx *gin.Context) {
	var dto service.CreateAdminRoleDTO
	if err := ctx.ShouldBindJSON(&dto); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	role, err := c.roleMgmtSvc.CreateAdminRole(ctx.Request.Context(), dto)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, role)
}

// AssignPermissionToRole handles the request to assign a permission to a role.
// @Summary Assign Permission to Role
// @Description Assigns an existing permission to an existing role.
// @Tags Admin
// @Accept json
// @Produce json
// @Param assignment body service.AssignPermissionToRoleDTO true "Assignment Data"
// @Success 204
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /admin/roles/assign-permission [post]
func (c *AdminRoleController) AssignPermissionToRole(ctx *gin.Context) {
	var dto service.AssignPermissionToRoleDTO
	if err := ctx.ShouldBindJSON(&dto); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := c.roleMgmtSvc.AssignPermissionToRole(ctx.Request.Context(), dto)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusNoContent)
}

// AssignRoleToUser handles the request to assign a role to an admin user.
// @Summary Assign Role to Admin User
// @Description Assigns an existing role to an existing admin user.
// @Tags Admin
// @Accept json
// @Produce json
// @Param assignment body service.AssignRoleToAdminDTO true "Assignment Data"
// @Success 204
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /admin/roles/assign-user [post]
func (c *AdminRoleController) AssignRoleToUser(ctx *gin.Context) {
	var dto service.AssignRoleToAdminDTO
	if err := ctx.ShouldBindJSON(&dto); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := c.roleMgmtSvc.AssignRoleToAdmin(ctx.Request.Context(), dto)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusNoContent)
} 