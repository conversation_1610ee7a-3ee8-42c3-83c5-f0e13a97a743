<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - API 文档</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .api-doc-container {
            display: flex;
            height: calc(100vh - 200px);
            overflow: hidden;
        }
        
        .api-sidebar {
            width: 280px;
            overflow-y: auto;
            border-right: 1px solid #374151;
            padding-right: 1rem;
        }
        
        .api-content {
            flex: 1;
            overflow-y: auto;
            padding-left: 1rem;
        }
        
        .method-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .method-get { background-color: rgba(0, 123, 255, 0.15); color: #0d6efd; }
        .method-post { background-color: rgba(40, 167, 69, 0.15); color: #28a745; }
        .method-put { background-color: rgba(255, 193, 7, 0.15); color: #ffc107; }
        .method-delete { background-color: rgba(220, 53, 69, 0.15); color: #dc3545; }
        
        .code-block {
            background-color: #1e1e1e;
            border-radius: 6px;
            padding: 1rem;
            font-family: 'Courier New', Courier, monospace;
            font-size: 0.85rem;
            white-space: pre-wrap;
            word-break: break-all;
            margin: 1rem 0;
        }
        
        .code-block.json { color: #9cdcfe; }
        .code-block.curl { color: #ce9178; }
        .code-block.javascript { color: #dcdcaa; }
        .code-block.php { color: #c586c0; }
        .code-block.python { color: #4ec9b0; }
        
        .api-sidebar-item {
            cursor: pointer;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            margin-bottom: 0.25rem;
            transition: all 0.2s;
        }
        
        .api-sidebar-item:hover {
            background-color: rgba(75, 85, 99, 0.3);
        }
        
        .api-sidebar-item.active {
            background-color: rgba(59, 130, 246, 0.2);
            color: #60a5fa;
        }
        
        .api-sidebar-category {
            font-weight: 600;
            color: #d1d5db;
            margin-top: 1rem;
            margin-bottom: 0.5rem;
            padding-left: 0.75rem;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-4">
                <a href="dashboard.html" class="text-gray-400 hover:text-white">首页</a>
                <span class="mx-2 text-gray-600">/</span>
                <a href="#" class="text-gray-400 hover:text-white">功能扩展</a>
                <span class="mx-2 text-gray-600">/</span>
                <span class="text-white">API 文档</span>
            </div>
            
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h2 class="text-xl font-bold text-white relative pl-3 section-title">API 文档</h2>
                <div class="mt-4 flex justify-between items-center">
                    <p class="text-gray-400">GACMS API 提供了完整的接口，允许开发者通过 RESTful API 访问和管理系统资源。</p>
                    <div class="flex items-center">
                        <span class="text-gray-400 mr-2">API 版本:</span>
                        <select class="bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-1 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option>v1.0</option>
                            <option>v2.0 (Beta)</option>
                        </select>
                        <button class="ml-4 bg-gradient-to-r from-blue-500 to-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center hover:shadow-lg hover:shadow-blue-500/30 transition-all">
                            <i class="fas fa-key mr-2"></i> 获取 API 密钥
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- API 文档内容 -->
            <div class="api-doc-container">
                <!-- 左侧 API 导航 -->
                <div class="api-sidebar">
                    <div class="sticky top-0">
                        <div class="relative">
                            <input type="text" placeholder="搜索 API..." class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 pl-10 mb-4 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        
                        <!-- 认证与授权 -->
                        <div class="api-sidebar-category">认证与授权</div>
                        <div class="api-sidebar-item active">
                            <div class="flex items-center">
                                <span class="method-tag method-post mr-2">POST</span>
                                <span>获取访问令牌</span>
                            </div>
                        </div>
                        <div class="api-sidebar-item">
                            <div class="flex items-center">
                                <span class="method-tag method-post mr-2">POST</span>
                                <span>刷新访问令牌</span>
                            </div>
                        </div>
                        <div class="api-sidebar-item">
                            <div class="flex items-center">
                                <span class="method-tag method-post mr-2">POST</span>
                                <span>撤销访问令牌</span>
                            </div>
                        </div>
                        
                        <!-- 内容管理 -->
                        <div class="api-sidebar-category">内容管理</div>
                        <div class="api-sidebar-item">
                            <div class="flex items-center">
                                <span class="method-tag method-get mr-2">GET</span>
                                <span>获取文章列表</span>
                            </div>
                        </div>
                        <div class="api-sidebar-item">
                            <div class="flex items-center">
                                <span class="method-tag method-get mr-2">GET</span>
                                <span>获取文章详情</span>
                            </div>
                        </div>
                        <div class="api-sidebar-item">
                            <div class="flex items-center">
                                <span class="method-tag method-post mr-2">POST</span>
                                <span>创建文章</span>
                            </div>
                        </div>
                        <div class="api-sidebar-item">
                            <div class="flex items-center">
                                <span class="method-tag method-put mr-2">PUT</span>
                                <span>更新文章</span>
                            </div>
                        </div>
                        <div class="api-sidebar-item">
                            <div class="flex items-center">
                                <span class="method-tag method-delete mr-2">DEL</span>
                                <span>删除文章</span>
                            </div>
                        </div>
                        
                        <!-- 用户管理 -->
                        <div class="api-sidebar-category">用户管理</div>
                        <div class="api-sidebar-item">
                            <div class="flex items-center">
                                <span class="method-tag method-get mr-2">GET</span>
                                <span>获取用户列表</span>
                            </div>
                        </div>
                        <div class="api-sidebar-item">
                            <div class="flex items-center">
                                <span class="method-tag method-get mr-2">GET</span>
                                <span>获取用户详情</span>
                            </div>
                        </div>
                        <div class="api-sidebar-item">
                            <div class="flex items-center">
                                <span class="method-tag method-post mr-2">POST</span>
                                <span>创建用户</span>
                            </div>
                        </div>
                        
                        <!-- 媒体管理 -->
                        <div class="api-sidebar-category">媒体管理</div>
                        <div class="api-sidebar-item">
                            <div class="flex items-center">
                                <span class="method-tag method-get mr-2">GET</span>
                                <span>获取媒体列表</span>
                            </div>
                        </div>
                        <div class="api-sidebar-item">
                            <div class="flex items-center">
                                <span class="method-tag method-post mr-2">POST</span>
                                <span>上传媒体文件</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧 API 详情 -->
                <div class="api-content">
                    <div class="mb-8">
                        <div class="flex items-center mb-4">
                            <span class="method-tag method-post mr-2">POST</span>
                            <h3 class="text-xl font-bold">/api/v1/auth/token</h3>
                        </div>
                        <p class="text-gray-400 mb-4">获取访问令牌 (Access Token)，用于后续 API 请求的身份验证。</p>
                        
                        <!-- 请求参数 -->
                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-2">请求参数</h4>
                            <div class="overflow-x-auto">
                                <table class="w-full text-left border-collapse">
                                    <thead>
                                        <tr class="bg-gray-800">
                                            <th class="p-3 border-b border-gray-700">参数名</th>
                                            <th class="p-3 border-b border-gray-700">类型</th>
                                            <th class="p-3 border-b border-gray-700">必填</th>
                                            <th class="p-3 border-b border-gray-700">描述</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="hover:bg-gray-800/20">
                                            <td class="p-3 border-b border-gray-700">client_id</td>
                                            <td class="p-3 border-b border-gray-700">string</td>
                                            <td class="p-3 border-b border-gray-700">是</td>
                                            <td class="p-3 border-b border-gray-700">API 客户端 ID</td>
                                        </tr>
                                        <tr class="hover:bg-gray-800/20">
                                            <td class="p-3 border-b border-gray-700">client_secret</td>
                                            <td class="p-3 border-b border-gray-700">string</td>
                                            <td class="p-3 border-b border-gray-700">是</td>
                                            <td class="p-3 border-b border-gray-700">API 客户端密钥</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <!-- 请求示例 -->
                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-2">请求示例</h4>
                            <div class="code-block json">
{
  "client_id": "your_client_id",
  "client_secret": "your_client_secret"
}</div>
                        </div>
                        
                        <!-- 响应参数 -->
                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-2">响应参数</h4>
                            <div class="overflow-x-auto">
                                <table class="w-full text-left border-collapse">
                                    <thead>
                                        <tr class="bg-gray-800">
                                            <th class="p-3 border-b border-gray-700">参数名</th>
                                            <th class="p-3 border-b border-gray-700">类型</th>
                                            <th class="p-3 border-b border-gray-700">描述</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="hover:bg-gray-800/20">
                                            <td class="p-3 border-b border-gray-700">access_token</td>
                                            <td class="p-3 border-b border-gray-700">string</td>
                                            <td class="p-3 border-b border-gray-700">访问令牌，用于后续请求的身份验证</td>
                                        </tr>
                                        <tr class="hover:bg-gray-800/20">
                                            <td class="p-3 border-b border-gray-700">refresh_token</td>
                                            <td class="p-3 border-b border-gray-700">string</td>
                                            <td class="p-3 border-b border-gray-700">刷新令牌，用于获取新的访问令牌</td>
                                        </tr>
                                        <tr class="hover:bg-gray-800/20">
                                            <td class="p-3 border-b border-gray-700">expires_in</td>
                                            <td class="p-3 border-b border-gray-700">integer</td>
                                            <td class="p-3 border-b border-gray-700">访问令牌的有效期（秒）</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <!-- 响应示例 -->
                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-2">成功响应示例</h4>
                            <div class="code-block json">
{
  "success": true,
  "code": 200,
  "message": "令牌获取成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ",
    "expires_in": 3600,
    "token_type": "Bearer"
  }
}</div>
                            
                            <h4 class="text-lg font-semibold mb-2 mt-4">失败响应示例</h4>
                            <div class="code-block json">
{
  "success": false,
  "code": 401,
  "message": "无效的客户端凭据",
  "errors": [
    "提供的 client_id 或 client_secret 无效"
  ]
}</div>
                        </div>
                        
                        <!-- 代码示例 -->
                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-2">代码示例</h4>
                            
                            <!-- 代码示例标签页 -->
                            <div class="border-b border-gray-700 mb-4">
                                <ul class="flex flex-wrap -mb-px" role="tablist">
                                    <li class="mr-2" role="presentation">
                                        <button class="inline-block px-4 py-2 border-b-2 border-blue-500 text-blue-400 font-medium" role="tab" aria-selected="true" data-target="curlExample">cURL</button>
                                    </li>
                                    <li class="mr-2" role="presentation">
                                        <button class="inline-block px-4 py-2 border-b-2 border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-400 font-medium" role="tab" aria-selected="false" data-target="jsExample">JavaScript</button>
                                    </li>
                                    <li class="mr-2" role="presentation">
                                        <button class="inline-block px-4 py-2 border-b-2 border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-400 font-medium" role="tab" aria-selected="false" data-target="phpExample">PHP</button>
                                    </li>
                                    <li class="mr-2" role="presentation">
                                        <button class="inline-block px-4 py-2 border-b-2 border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-400 font-medium" role="tab" aria-selected="false" data-target="pythonExample">Python</button>
                                    </li>
                                </ul>
                            </div>
                            
                            <!-- 代码示例内容 -->
                            <div id="curlExample" class="tab-content active">
                                <div class="code-block curl">
curl -X POST https://api.gacms.com/v1/auth/token \
  -H "Content-Type: application/json" \
  -d '{
    "client_id": "your_client_id",
    "client_secret": "your_client_secret"
  }'</div>
                            </div>
                            
                            <div id="jsExample" class="tab-content hidden">
                                <div class="code-block javascript">
const getToken = async () => {
  try {
    const response = await fetch('https://api.gacms.com/v1/auth/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        client_id: 'your_client_id',
        client_secret: 'your_client_secret'
      })
    });
    
    const data = await response.json();
    console.log('Token:', data);
    return data;
  } catch (error) {
    console.error('Error:', error);
  }
};

getToken();</div>
                            </div>
                            
                            <div id="phpExample" class="tab-content hidden">
                                <div class="code-block php">
<?php
$url = 'https://api.gacms.com/v1/auth/token';
$data = array(
    'client_id' => 'your_client_id',
    'client_secret' => 'your_client_secret'
);

$options = array(
    'http' => array(
        'header'  => "Content-type: application/json\r\n",
        'method'  => 'POST',
        'content' => json_encode($data)
    )
);

$context  = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "Error getting token";
} else {
    $response = json_decode($result, true);
    echo "Access Token: " . $response['data']['access_token'];
}
?></div>
                            </div>
                            
                            <div id="pythonExample" class="tab-content hidden">
                                <div class="code-block python">
import requests
import json

url = "https://api.gacms.com/v1/auth/token"

payload = json.dumps({
    "client_id": "your_client_id",
    "client_secret": "your_client_secret"
})

headers = {
    'Content-Type': 'application/json'
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)</div>
                            </div>
                        </div>
                        
                        <!-- 注意事项 -->
                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-2">注意事项</h4>
                            <ul class="list-disc pl-5 space-y-2 text-gray-400">
                                <li>访问令牌有效期为 1 小时，过期后需要使用刷新令牌获取新的访问令牌</li>
                                <li>请妥善保管您的客户端 ID 和客户端密钥，不要泄露给第三方</li>
                                <li>在生产环境中，请始终使用 HTTPS 进行 API 调用</li>
                                <li>如果频繁请求令牌，可能会触发速率限制</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 标签页切换功能
            const tabButtons = document.querySelectorAll('[role="tab"]');
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有标签页的active类
                    tabButtons.forEach(btn => {
                        btn.classList.remove('border-blue-500', 'text-blue-400');
                        btn.classList.add('border-transparent', 'text-gray-400', 'hover:text-gray-300', 'hover:border-gray-400');
                        btn.setAttribute('aria-selected', 'false');
                    });
                    
                    // 添加当前标签页的active类
                    this.classList.remove('border-transparent', 'text-gray-400', 'hover:text-gray-300', 'hover:border-gray-400');
                    this.classList.add('border-blue-500', 'text-blue-400');
                    this.setAttribute('aria-selected', 'true');
                    
                    // 隐藏所有内容
                    const tabContents = document.querySelectorAll('.tab-content');
                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                        content.classList.remove('active');
                    });
                    
                    // 显示对应的内容
                    const targetId = this.getAttribute('data-target');
                    const targetContent = document.getElementById(targetId);
                    if(targetContent) {
                        targetContent.classList.remove('hidden');
                        targetContent.classList.add('active');
                    }
                });
            });
            
            // API 文档侧边栏项点击效果
            const apiSidebarItems = document.querySelectorAll('.api-sidebar-item');
            apiSidebarItems.forEach(item => {
                item.addEventListener('click', function() {
                    apiSidebarItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html>