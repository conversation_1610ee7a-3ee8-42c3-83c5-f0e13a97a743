/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/contenttype/domain/model/Field.go
 * @Description: Defines the Field model for content types.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

// ValidationMap represents the JSON validation rules for a field.
type ValidationMap map[string]interface{}

// Value implements the driver.Valuer interface for database serialization.
func (vm ValidationMap) Value() (driver.Value, error) {
	return json.Marshal(vm)
}

// Scan implements the sql.Scanner interface for database deserialization.
func (vm *ValidationMap) Scan(value interface{}) error {
	b, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, &vm)
}

// Field represents a single field definition within a ContentType.
type Field struct {
	ID            uint          `gorm:"primaryKey"`
	ContentTypeID uint          `gorm:"index;not null"`
	Name          string        `gorm:"type:varchar(255);not null"`
	Slug          string        `gorm:"type:varchar(100);not null"`
	Type          string        `gorm:"type:varchar(50);not null"`
	IsRequired    bool          `gorm:"default:false"`
	Validations   ValidationMap `gorm:"type:json"`
	CreatedAt     time.Time
	UpdatedAt     time.Time
}

func (Field) TableName() string {
	return "content_fields"
} 