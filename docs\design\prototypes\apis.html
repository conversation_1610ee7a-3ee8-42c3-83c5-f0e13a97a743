<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - API管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome CDN -->
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <!-- 核心CSS文件 -->
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <!-- highlight.js CSS for Modal -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/monokai.min.css">
    <!-- Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
    <!-- 页面特定样式 -->
    <style>
        /* 通用样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        /* API状态样式 */
        .status-active {
            @apply text-green-400 font-medium;
        }
        
        .status-inactive {
            @apply text-gray-400 font-medium;
        }
        
        .status-limited {
            @apply text-yellow-400 font-medium;
        }
        
        /* 表格样式 */
        .api-table th {
            @apply py-3 px-4 text-left font-medium text-gray-300 border-b border-gray-700;
        }
        
        .api-table td {
            @apply py-3 px-4 border-b border-gray-700;
        }
        
        .api-table tr:hover {
            @apply bg-gray-800/20;
        }
        
        /* 方法标签样式 */
        .method-get {
            @apply bg-green-900/30 text-green-400;
        }
        
        .method-post {
            @apply bg-blue-900/30 text-blue-400;
        }
        
        .method-put {
            @apply bg-yellow-900/30 text-yellow-400;
        }
        
        .method-delete {
            @apply bg-red-900/30 text-red-400;
        }
    </style>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10">
        <!-- 侧边栏内容将通过JavaScript从模板加载 -->
    </aside>
    
    <!-- 侧边栏遮罩层 -->
    <div id="sidebarOverlay" class="sidebar-overlay"></div>
    
    <!-- 主内容区 -->
    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <!-- 顶部导航栏 -->
        <header class="header sticky top-0 z-30">
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        
        <!-- 页面内容区 -->
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">API管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="createApiBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-plus text-white"></i>
                                </span>
                                创建API
                            </span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- API筛选区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap gap-4">
                    <div class="flex-1 min-w-[200px]">
                        <label class="block text-sm font-medium text-gray-400 mb-2">分组</label>
                        <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2.5 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">全部分组</option>
                            <option value="content">内容管理</option>
                            <option value="user">用户管理</option>
                            <option value="system">系统管理</option>
                            <option value="stats">数据统计</option>
                        </select>
                    </div>
                    <div class="flex-1 min-w-[200px]">
                        <label class="block text-sm font-medium text-gray-400 mb-2">状态</label>
                        <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2.5 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">全部状态</option>
                            <option value="active">已启用</option>
                            <option value="inactive">已禁用</option>
                            <option value="limited">限流中</option>
                        </select>
                    </div>
                    <div class="flex-1 min-w-[200px]">
                        <label class="block text-sm font-medium text-gray-400 mb-2">搜索</label>
                        <div class="relative">
                            <input type="text" placeholder="搜索API名称或路径..." class="w-full bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2.5 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                    </div>
                    <div class="flex items-end">
                        <button class="bg-gray-700 hover:bg-gray-600 text-white px-5 py-2.5 rounded-lg font-medium transition-all">
                            筛选
                        </button>
                    </div>
                </div>
            </div>

            <!-- API列表 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6 overflow-x-auto">
                <table class="w-full api-table">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" class="w-4 h-4 rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                            </th>
                            <th>API名称</th>
                            <th>请求方法</th>
                            <th>路径</th>
                            <th>分组</th>
                            <th>访问限制</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <input type="checkbox" class="w-4 h-4 rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                            </td>
                            <td>
                                <div class="font-medium text-white">获取文章列表</div>
                                <div class="text-xs text-gray-400">获取所有文章或按条件筛选</div>
                            </td>
                            <td>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium method-get">
                                    GET
                                </span>
                            </td>
                            <td class="font-mono text-sm">/api/v1/posts</td>
                            <td>内容管理</td>
                            <td>100次/分钟</td>
                            <td><span class="status-active">已启用</span></td>
                            <td>
                                <div class="flex space-x-3">
                                    <button class="text-gray-400 hover:text-white" title="快速查看" onclick="showApiDetails(this)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-yellow-400 hover:text-yellow-300" title="禁用">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="w-4 h-4 rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                            </td>
                            <td>
                                <div class="font-medium text-white">创建新文章</div>
                                <div class="text-xs text-gray-400">添加新的文章内容</div>
                            </td>
                            <td>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium method-post">
                                    POST
                                </span>
                            </td>
                            <td class="font-mono text-sm">/api/v1/posts</td>
                            <td>内容管理</td>
                            <td>50次/分钟</td>
                            <td><span class="status-active">已启用</span></td>
                            <td>
                                <div class="flex space-x-2">
                                    <button class="text-blue-400 hover:text-blue-300" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300" title="测试">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                    <button class="text-yellow-400 hover:text-yellow-300" title="禁用">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="w-4 h-4 rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                            </td>
                            <td>
                                <div class="font-medium text-white">更新文章</div>
                                <div class="text-xs text-gray-400">更新现有文章内容</div>
                            </td>
                            <td>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium method-put">
                                    PUT
                                </span>
                            </td>
                            <td class="font-mono text-sm">/api/v1/posts/{id}</td>
                            <td>内容管理</td>
                            <td>50次/分钟</td>
                            <td><span class="status-active">已启用</span></td>
                            <td>
                                <div class="flex space-x-2">
                                    <button class="text-blue-400 hover:text-blue-300" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300" title="测试">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                    <button class="text-yellow-400 hover:text-yellow-300" title="禁用">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="w-4 h-4 rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                            </td>
                            <td>
                                <div class="font-medium text-white">删除文章</div>
                                <div class="text-xs text-gray-400">删除指定文章</div>
                            </td>
                            <td>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium method-delete">
                                    DELETE
                                </span>
                            </td>
                            <td class="font-mono text-sm">/api/v1/posts/{id}</td>
                            <td>内容管理</td>
                            <td>30次/分钟</td>
                            <td><span class="status-active">已启用</span></td>
                            <td>
                                <div class="flex space-x-2">
                                    <button class="text-blue-400 hover:text-blue-300" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300" title="测试">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                    <button class="text-yellow-400 hover:text-yellow-300" title="禁用">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="w-4 h-4 rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                            </td>
                            <td>
                                <div class="font-medium text-white">用户登录</div>
                                <div class="text-xs text-gray-400">用户身份验证</div>
                            </td>
                            <td>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium method-post">
                                    POST
                                </span>
                            </td>
                            <td class="font-mono text-sm">/api/v1/auth/login</td>
                            <td>用户管理</td>
                            <td>10次/分钟</td>
                            <td><span class="status-limited">限流中</span></td>
                            <td>
                                <div class="flex space-x-2">
                                    <button class="text-blue-400 hover:text-blue-300" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300" title="测试">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                    <button class="text-green-400 hover:text-green-300" title="解除限流">
                                        <i class="fas fa-unlock"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="w-4 h-4 rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                            </td>
                            <td>
                                <div class="font-medium text-white">系统状态</div>
                                <div class="text-xs text-gray-400">获取系统运行状态</div>
                            </td>
                            <td>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium method-get">
                                    GET
                                </span>
                            </td>
                            <td class="font-mono text-sm">/api/v1/system/status</td>
                            <td>系统管理</td>
                            <td>200次/分钟</td>
                            <td><span class="status-inactive">已禁用</span></td>
                            <td>
                                <div class="flex space-x-2">
                                    <button class="text-blue-400 hover:text-blue-300" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300" title="测试">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                    <button class="text-green-400 hover:text-green-300" title="启用">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- 分页 -->
                <div class="flex justify-between items-center mt-6">
                    <div class="text-sm text-gray-400">
                        显示 1 到 6，共 24 条记录
                    </div>
                    <div class="flex space-x-1">
                        <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="px-3 py-1 rounded bg-blue-600 text-white">
                            1
                        </button>
                        <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">
                            2
                        </button>
                        <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">
                            3
                        </button>
                        <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">
                            4
                        </button>
                        <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>
    
    <!-- 创建API模态框 -->
    <div id="createApiModal" class="fixed inset-0 z-50 hidden">
        <div class="absolute inset-0 bg-black/50"></div>
        <div class="relative z-10 max-w-2xl w-full mx-auto mt-20 bg-gray-800 rounded-xl shadow-xl">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-white">创建API</h3>
                    <button id="closeCreateApiModal" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-400 mb-2">API名称</label>
                            <input type="text" placeholder="例如：获取文章列表" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2.5 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-400 mb-2">分组</label>
                            <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2.5 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="content">内容管理</option>
                                <option value="user">用户管理</option>
                                <option value="system">系统管理</option>
                                <option value="stats">数据统计</option>
                            </select>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-400 mb-2">请求方法</label>
                            <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2.5 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="GET">GET</option>
                                <option value="POST">POST</option>
                                <option value="PUT">PUT</option>
                                <option value="DELETE">DELETE</option>
                                <option value="PATCH">PATCH</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-400 mb-2">路径</label>
                            <div class="flex">
                                <span class="inline-flex items-center px-3 text-sm text-gray-300 bg-gray-600 border border-r-0 border-gray-600 rounded-l-lg">
                                    /api/v1/
                                </span>
                                <input type="text" placeholder="例如：posts" class="flex-1 bg-gray-700 border border-gray-600 rounded-r-lg px-4 py-2.5 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-400 mb-2">描述</label>
                        <textarea rows="2" placeholder="API功能简要描述" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2.5 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"></textarea>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-400 mb-2">访问限制</label>
                            <div class="flex">
                                <input type="number" min="1" value="100" class="w-20 bg-gray-700 border border-gray-600 rounded-l-lg px-4 py-2.5 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <span class="inline-flex items-center px-3 text-sm text-gray-300 bg-gray-600 border border-l-0 border-gray-600 rounded-r-lg">
                                    次/分钟
                                </span>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-400 mb-2">状态</label>
                            <div class="flex items-center space-x-4">
                                <label class="inline-flex items-center">
                                    <input type="radio" name="status" value="active" checked class="w-4 h-4 bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                                    <span class="ml-2 text-gray-300">启用</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="status" value="inactive" class="w-4 h-4 bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                                    <span class="ml-2 text-gray-300">禁用</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-400 mb-2">权限要求</label>
                        <div class="flex flex-wrap gap-3">
                            <label class="inline-flex items-center">
                                <input type="checkbox" class="w-4 h-4 rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                                <span class="ml-2 text-gray-300">需要认证</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="checkbox" class="w-4 h-4 rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                                <span class="ml-2 text-gray-300">管理员权限</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="checkbox" class="w-4 h-4 rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                                <span class="ml-2 text-gray-300">需要API密钥</span>
                            </label>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" id="cancelCreateApi" class="px-5 py-2.5 bg-gray-700 hover:bg-gray-600 text-white rounded-lg">
                            取消
                        </button>
                        <button type="submit" class="px-5 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:shadow-lg hover:shadow-blue-500/30">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- API详情 Modal -->
    <div id="api-details-modal" class="fixed inset-0 bg-black bg-opacity-70 z-50 hidden items-center justify-center">
        <div class="bg-gray-800 rounded-xl shadow-2xl w-full max-w-2xl border border-gray-700 max-h-[90vh] flex flex-col">
            <header class="flex justify-between items-center p-4 border-b border-gray-700">
                <h3 class="text-lg font-bold text-white">API 调用说明</h3>
                <button onclick="closeApiDetails()" class="text-gray-400 hover:text-white">&times;</button>
            </header>
            <div class="p-6 overflow-y-auto">
                 <div class="mb-4">
                    <label class="text-sm text-gray-400">请求方法</label>
                    <p id="modal-method" class="font-mono text-white bg-gray-700 px-2 py-1 rounded-md inline-block"></p>
                </div>
                <div class="mb-4">
                    <label class="text-sm text-gray-400">请求路径</label>
                    <p id="modal-path" class="font-mono text-white"></p>
                </div>
                 <div class="mb-4">
                    <label class="text-sm text-gray-400">Curl 示例</label>
                    <pre><code id="modal-curl-example" class="language-bash"></code></pre>
                </div>
            </div>
        </div>
    </div>

    <!-- 核心JS文件 -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <!-- highlight.js for Modal -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面
            const createApiBtn = document.getElementById('createApiBtn');
            const createApiModal = document.getElementById('createApiModal');
            const closeCreateApiModal = document.getElementById('closeCreateApiModal');
            const cancelCreateApi = document.getElementById('cancelCreateApi');
            
            // 打开创建API模态框
            if (createApiBtn && createApiModal) {
                createApiBtn.addEventListener('click', function() {
                    createApiModal.classList.remove('hidden');
                });
            }
            
            // 关闭创建API模态框
            if (closeCreateApiModal && createApiModal) {
                closeCreateApiModal.addEventListener('click', function() {
                    createApiModal.classList.add('hidden');
                });
            }
            
            // 取消创建API
            if (cancelCreateApi && createApiModal) {
                cancelCreateApi.addEventListener('click', function() {
                    createApiModal.classList.add('hidden');
                });
            }
            
            // 表格行复选框
            const mainCheckbox = document.querySelector('thead input[type="checkbox"]');
            const rowCheckboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            
            if (mainCheckbox && rowCheckboxes.length > 0) {
                // 全选/取消全选
                mainCheckbox.addEventListener('change', function() {
                    rowCheckboxes.forEach(checkbox => {
                        checkbox.checked = mainCheckbox.checked;
                    });
                });
                
                // 更新主复选框状态
                rowCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        const allChecked = Array.from(rowCheckboxes).every(cb => cb.checked);
                        const someChecked = Array.from(rowCheckboxes).some(cb => cb.checked);
                        
                        mainCheckbox.checked = allChecked;
                        mainCheckbox.indeterminate = someChecked && !allChecked;
                    });
                });
            }
        });

        function showApiDetails(element) {
            // Mock data - in a real app, you'd get this from the row or an API
            const method = "GET";
            const path = "/api/v1/posts";
            
            document.getElementById('modal-method').textContent = method;
            document.getElementById('modal-path').textContent = path;
            
            const curlExample = `curl -X ${method} "https://your-domain.com${path}" \\\n-H "Authorization: Bearer YOUR_API_TOKEN"`;
            const codeElement = document.getElementById('modal-curl-example');
            codeElement.textContent = curlExample;
            
            // Highlight the code
            hljs.highlightElement(codeElement);
            
            // Show modal
            document.getElementById('api-details-modal').classList.remove('hidden');
            document.getElementById('api-details-modal').classList.add('flex');
        }

        function closeApiDetails() {
            const modal = document.getElementById('api-details-modal');
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }

        // Close modal on escape key
        document.addEventListener('keydown', function (event) {
            if (event.key === "Escape") {
                closeApiDetails();
            }
        });
    </script>
</body>
</html>