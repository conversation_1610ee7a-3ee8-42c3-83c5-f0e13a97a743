/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/theme/port/http/controller/ThemeController.go
 * @Description: Controller for theme management, with authorization handled by middleware.
 *
 * © 2025 GACMS. All rights reserved.
 */

package controller

import (
	"fmt"
	extService "gacms/internal/modules/extension/application/service"
	"gacms/internal/modules/theme/application/service"
	"gacms/pkg/response"
	"net/http"
	"strconv"

	"github.comcom/gin-gonic/gin"
)

type ThemeController struct {
	themeSvc *service.ThemeService
	extSvc   *extService.ExtensionService
}

func NewThemeController(themeSvc *service.ThemeService, extSvc *extService.ExtensionService) *ThemeController {
	return &ThemeController{
		themeSvc: themeSvc,
		extSvc:   extSvc,
	}
}

// RegisterGeneralRoutes registers routes that do not require specific permissions beyond basic auth.
func (c *ThemeController) RegisterGeneralRoutes(rg *gin.RouterGroup) {
	rg.GET("/", c.ListThemes)
}

// RegisterAdminRoutes registers routes that require specific admin permissions.
func (c *ThemeController) RegisterAdminRoutes(rg *gin.RouterGroup) {
	rg.POST("/activate", c.Activate)
	rg.POST("/deactivate", c.Deactivate)
	rg.POST("/create-child", c.CreateChildTheme)
	rg.GET("/settings", c.GetThemeSettings)
	rg.POST("/settings", c.UpdateThemeSettings)
	rg.GET("/files", c.GetThemeFile)
	rg.POST("/files", c.UpdateThemeFile)
}

func (c *ThemeController) ListThemes(ctx *gin.Context) {
	themes, err := c.extSvc.ListInstalled("theme")
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "Failed to list themes: "+err.Error())
		return
	}
	response.Success(ctx, themes, "Themes listed successfully.")
}

type ActivateThemeRequest struct {
	SiteID       uint   `json:"siteId" binding:"required"`
	ThemeDirName string `json:"themeDirName" binding:"required"`
	ThemeType    string `json:"themeType" binding:"required"`
}

func (c *ThemeController) Activate(ctx *gin.Context) {
	var req ActivateThemeRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "Invalid request: "+err.Error())
		return
	}

	if err := c.extSvc.Activate(req.ThemeDirName, req.SiteID, req.ThemeType); err != nil {
		response.Error(ctx, http.StatusInternalServerError, "Failed to activate theme: "+err.Error())
		return
	}

	ctx.Set("action_log_description", fmt.Sprintf("Activated %s theme '%s' for site ID %d", req.ThemeType, req.ThemeDirName, req.SiteID))
	response.Success(ctx, nil, "Theme activated successfully for site.")
}

type DeactivateThemeRequest struct {
	SiteID    uint   `json:"siteId" binding:"required"`
	ThemeType string `json:"themeType" binding:"required"`
}

func (c *ThemeController) Deactivate(ctx *gin.Context) {
	var req DeactivateThemeRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "Invalid request: "+err.Error())
		return
	}

	if err := c.extSvc.Deactivate(req.SiteID, req.ThemeType); err != nil {
		response.Error(ctx, http.StatusInternalServerError, "Failed to deactivate theme: "+err.Error())
		return
	}

	ctx.Set("action_log_description", fmt.Sprintf("Deactivated %s theme for site ID %d", req.ThemeType, req.SiteID))
	response.Success(ctx, nil, "Theme deactivated successfully for site.")
}

type CreateChildThemeRequest struct {
	SiteID           uint   `json:"siteId" binding:"required"`
	BaseThemeDirName string `json:"baseThemeDirName" binding:"required"`
}

func (c *ThemeController) CreateChildTheme(ctx *gin.Context) {
	var req CreateChildThemeRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "Invalid request: "+err.Error())
		return
	}

	userIDVal, _ := ctx.Get("userID")
	userID, _ := userIDVal.(uint)

	newThemeManifest, err := c.themeSvc.CreateChildTheme(req.BaseThemeDirName, req.SiteID, userID)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "Failed to create child theme: "+err.Error())
		return
	}

	ctx.Set("action_log_description", fmt.Sprintf("Created child theme '%s' for site ID %d", newThemeManifest.Name, req.SiteID))
	response.Success(ctx, newThemeManifest, "Child theme created successfully.")
}

func (c *ThemeController) GetThemeSettings(ctx *gin.Context) {
	siteID, err := strconv.ParseUint(ctx.Query("siteId"), 10, 32)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "Invalid or missing siteId query parameter")
		return
	}
	themeName := ctx.Query("themeName")
	if themeName == "" {
		response.Error(ctx, http.StatusBadRequest, "Missing themeName query parameter")
		return
	}

	settings, err := c.themeSvc.GetThemeSettings(uint(siteID), themeName)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "Failed to get theme settings: "+err.Error())
		return
	}

	response.Success(ctx, settings, "Theme settings retrieved successfully.")
}

type UpdateThemeSettingsRequest struct {
	SiteID   uint                   `json:"siteId" binding:"required"`
	ThemeName string                 `json:"themeName" binding:"required"`
	Settings map[string]interface{} `json:"settings" binding:"required"`
}

func (c *ThemeController) UpdateThemeSettings(ctx *gin.Context) {
	var req UpdateThemeSettingsRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "Invalid request body: "+err.Error())
		return
	}

	userIDVal, _ := ctx.Get("userID")
	userID, _ := userIDVal.(uint)

	if err := c.themeSvc.UpdateThemeSettings(req.SiteID, req.ThemeName, req.Settings, userID); err != nil {
		response.Error(ctx, http.StatusInternalServerError, "Failed to update theme settings: "+err.Error())
		return
	}

	ctx.Set("action_log_description", fmt.Sprintf("Updated theme settings for '%s' on site ID %d", req.ThemeName, req.SiteID))
	response.Success(ctx, nil, "Theme settings updated successfully.")
}

func (c *ThemeController) GetThemeFile(ctx *gin.Context) {
	themeName := ctx.Query("themeName")
	filePath := ctx.Query("filePath")
	if themeName == "" || filePath == "" {
		response.Error(ctx, http.StatusBadRequest, "Missing themeName or filePath query parameter")
		return
	}

	content, err := c.themeSvc.GetThemeFileContent(themeName, filePath)
	if err != nil {
		response.Error(ctx, http.StatusNotFound, "Failed to get theme file: "+err.Error())
		return
	}

	contentType := http.DetectContentType(content)
	ctx.Data(http.StatusOK, contentType, content)
}

type UpdateThemeFileRequest struct {
	ThemeName string `json:"themeName" binding:"required"`
	FilePath  string `json:"filePath" binding:"required"`
	Content   string `json:"content" binding:"required"`
}

func (c *ThemeController) UpdateThemeFile(ctx *gin.Context) {
	var req UpdateThemeFileRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "Invalid request body: "+err.Error())
		return
	}

	// This is disabled for security. A proper implementation would go in ThemeService.
	response.Error(ctx, http.StatusNotImplemented, "File editing via API is a sensitive operation and is not fully implemented for security reasons.")
} 