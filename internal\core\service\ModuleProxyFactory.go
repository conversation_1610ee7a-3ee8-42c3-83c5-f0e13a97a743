/*
 * @Author: <PERSON><PERSON>eh <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/ModuleProxyFactory.go
 * @Description: 模块代理工厂，实现真正的懒加载机制
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"gacms/internal/infrastructure/database"
	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// ModuleProxyFactory 模块代理工厂
// 核心职责：懒加载模块实例，支持多租户隔离
type ModuleProxyFactory struct {
	// 全局依赖（从fx注入）
	globalDeps *GlobalDependencies
	logger     *zap.Logger
	eventMgr   contract.EventManager // 延迟注入，避免循环依赖

	// 模块配置注册表（元信息，不是实例）
	moduleConfigs map[string]*ModuleConfig

	// 多租户模块实例缓存
	globalInstances map[string]*ModuleInstance           // 全局模块实例
	tenantInstances map[uint]map[string]*ModuleInstance  // 租户模块实例

	// 性能管理
	perfManager ModulePerformanceManager

	// 许可证管理
	licenseManager contract.LicenseManager

	// 授权规则管理（安全）
	authRules *ModuleAuthorizationRules

	// 并发控制
	mu sync.RWMutex

	// 统计信息
	loadCount    int64
	cacheHits    int64
	cacheMisses  int64
}

// ModuleProxyFactoryParams fx依赖注入参数
type ModuleProxyFactoryParams struct {
	fx.In

	GlobalDeps     *GlobalDependencies
	PerfManager    ModulePerformanceManager
	LicenseManager contract.LicenseManager
	Logger         *zap.Logger
}

// NewModuleProxyFactory 创建模块代理工厂
func NewModuleProxyFactory(params ModuleProxyFactoryParams) *ModuleProxyFactory {
	factory := &ModuleProxyFactory{
		globalDeps:      params.GlobalDeps,
		logger:          params.Logger,
		perfManager:     params.PerfManager,
		licenseManager:  params.LicenseManager,
		authRules:       NewModuleAuthorizationRules(), // 安全的授权规则
		eventMgr:        nil, // 延迟注入，避免循环依赖
		moduleConfigs:   make(map[string]*ModuleConfig),
		globalInstances: make(map[string]*ModuleInstance),
		tenantInstances: make(map[uint]map[string]*ModuleInstance),
	}

	// 注册模块工厂
	factory.registerModuleFactories()

	return factory
}

// SetEventManager 设置事件管理器（延迟注入，避免循环依赖）
func (f *ModuleProxyFactory) SetEventManager(eventMgr contract.EventManager) {
	f.mu.Lock()
	defer f.mu.Unlock()
	f.eventMgr = eventMgr
}

// GetModule 获取模块实例（多租户感知）
func (f *ModuleProxyFactory) GetModule(ctx context.Context, moduleName string) (*ModuleInstance, error) {
	// 开始性能监控
	timer := f.perfManager.StartLoadTimer(moduleName)
	defer timer.Stop()

	// 检查模块授权
	if err := f.checkModuleAuthorization(ctx, moduleName); err != nil {
		return nil, fmt.Errorf("module authorization failed: %w", err)
	}

	// 从上下文提取租户信息
	if siteID, hasSiteID := database.SiteIDFrom(ctx); hasSiteID && siteID > 0 {
		return f.GetModuleForSite(siteID, moduleName)
	}

	return f.getGlobalModule(moduleName)
}

// GetModuleForSite 获取指定站点的模块实例
func (f *ModuleProxyFactory) GetModuleForSite(siteID uint, moduleName string) (*ModuleInstance, error) {
	f.logger.Debug("Getting module for site",
		zap.Uint("site_id", siteID),
		zap.String("module", moduleName),
	)

	// 1. 检查租户实例缓存
	f.mu.RLock()
	if tenantModules, exists := f.tenantInstances[siteID]; exists {
		if instance, exists := tenantModules[moduleName]; exists {
			f.mu.RUnlock()
			f.cacheHits++
			f.perfManager.RecordCacheHit(moduleName)
			return instance, nil
		}
	}
	f.mu.RUnlock()

	// 2. 检查全局模块缓存
	if instance, err := f.getGlobalModule(moduleName); err == nil {
		return instance, nil
	}

	// 3. 懒加载租户特定模块
	return f.createTenantModule(siteID, moduleName)
}

// getGlobalModule 获取全局模块实例
func (f *ModuleProxyFactory) getGlobalModule(moduleName string) (*ModuleInstance, error) {
	// 1. 检查全局实例缓存
	f.mu.RLock()
	if instance, exists := f.globalInstances[moduleName]; exists {
		f.mu.RUnlock()
		f.cacheHits++
		f.perfManager.RecordCacheHit(moduleName)
		return instance, nil
	}
	f.mu.RUnlock()

	// 2. 懒加载全局模块
	return f.createGlobalModule(moduleName)
}

// createGlobalModule 创建全局模块实例
func (f *ModuleProxyFactory) createGlobalModule(moduleName string) (*ModuleInstance, error) {
	f.mu.Lock()
	defer f.mu.Unlock()

	// 双重检查
	if instance, exists := f.globalInstances[moduleName]; exists {
		return instance, nil
	}

	f.logger.Info("Creating global module instance", zap.String("module", moduleName))
	f.cacheMisses++
	f.perfManager.RecordCacheMiss(moduleName)

	// 1. 获取模块配置
	config, exists := f.moduleConfigs[moduleName]
	if !exists {
		return nil, fmt.Errorf("module config not found: %s", moduleName)
	}

	// 2. 检查是否支持全局实例
	if !config.IsGlobal && config.GlobalFactory == nil {
		return nil, fmt.Errorf("module %s does not support global instances", moduleName)
	}

	// 3. 调用全局工厂创建实例
	instance, err := config.GlobalFactory(f.globalDeps)
	if err != nil {
		f.publishModuleEvent("module.load.failed", moduleName, 0, err)
		return nil, fmt.Errorf("failed to create global module %s: %w", moduleName, err)
	}

	// 4. 设置实例信息
	instance.Name = moduleName
	instance.SiteID = 0
	instance.IsGlobal = true
	instance.LoadedAt = time.Now().Unix()

	// 5. 启动模块
	if err := instance.Start(); err != nil {
		return nil, fmt.Errorf("failed to start global module %s: %w", moduleName, err)
	}

	// 6. 缓存实例
	f.globalInstances[moduleName] = instance
	f.loadCount++

	// 7. 发布模块加载事件
	f.publishModuleEvent("module.loaded", moduleName, 0, nil)

	f.logger.Info("Global module instance created successfully",
		zap.String("module", moduleName),
		zap.Int("services", len(instance.Services)),
		zap.Int("controllers", len(instance.Controllers)),
		zap.Int("routes", len(instance.Routes)),
	)

	return instance, nil
}

// createTenantModule 创建租户特定模块实例
func (f *ModuleProxyFactory) createTenantModule(siteID uint, moduleName string) (*ModuleInstance, error) {
	f.mu.Lock()
	defer f.mu.Unlock()

	// 双重检查
	if tenantModules, exists := f.tenantInstances[siteID]; exists {
		if instance, exists := tenantModules[moduleName]; exists {
			return instance, nil
		}
	}

	f.logger.Info("Creating tenant module instance",
		zap.Uint("site_id", siteID),
		zap.String("module", moduleName),
	)
	f.cacheMisses++
	f.perfManager.RecordCacheMiss(moduleName)

	// 1. 获取模块配置
	config, exists := f.moduleConfigs[moduleName]
	if !exists {
		return nil, fmt.Errorf("module config not found: %s", moduleName)
	}

	// 2. 检查是否支持租户实例
	if !config.CanCreateForTenant(siteID) {
		return nil, fmt.Errorf("module %s does not support tenant instances for site %d", moduleName, siteID)
	}

	// 3. 创建租户依赖
	tenantDeps := NewTenantDependencies(f.globalDeps, siteID)

	// 4. 调用租户工厂创建实例
	var instance *ModuleInstance
	var err error

	if config.TenantFactory != nil {
		instance, err = config.TenantFactory(tenantDeps)
	} else {
		// 使用全局工厂但注入租户依赖
		instance, err = config.GlobalFactory(f.globalDeps)
	}

	if err != nil {
		f.publishModuleEvent("module.load.failed", moduleName, siteID, err)
		return nil, fmt.Errorf("failed to create tenant module %s for site %d: %w", moduleName, siteID, err)
	}

	// 5. 设置实例信息
	instance.Name = moduleName
	instance.SiteID = siteID
	instance.IsGlobal = false
	instance.LoadedAt = time.Now().Unix()

	// 6. 启动模块
	if err := instance.Start(); err != nil {
		return nil, fmt.Errorf("failed to start tenant module %s for site %d: %w", moduleName, siteID, err)
	}

	// 7. 缓存实例
	if f.tenantInstances[siteID] == nil {
		f.tenantInstances[siteID] = make(map[string]*ModuleInstance)
	}
	f.tenantInstances[siteID][moduleName] = instance
	f.loadCount++

	// 8. 发布模块加载事件
	f.publishModuleEvent("module.loaded", moduleName, siteID, nil)

	f.logger.Info("Tenant module instance created successfully",
		zap.Uint("site_id", siteID),
		zap.String("module", moduleName),
		zap.Int("services", len(instance.Services)),
		zap.Int("controllers", len(instance.Controllers)),
		zap.Int("routes", len(instance.Routes)),
	)

	return instance, nil
}

// publishModuleEvent 发布模块事件
func (f *ModuleProxyFactory) publishModuleEvent(eventName, moduleName string, siteID uint, err error) {
	// 检查事件管理器是否已注入
	f.mu.RLock()
	eventMgr := f.eventMgr
	f.mu.RUnlock()

	if eventMgr == nil {
		f.logger.Debug("EventManager not yet injected, skipping module event",
			zap.String("event", eventName),
			zap.String("module", moduleName),
		)
		return
	}

	eventData := map[string]interface{}{
		"module_name": moduleName,
		"site_id":     siteID,
		"timestamp":   time.Now().Unix(),
	}

	if err != nil {
		eventData["error"] = err.Error()
	}

	// 创建带租户上下文的事件
	var ctx context.Context
	if siteID > 0 {
		ctx = database.WithSiteID(context.Background(), siteID)
	} else {
		ctx = context.Background()
	}

	event := eventMgr.CreateEvent(ctx, contract.EventName(eventName), eventData)
	if publishErr := eventMgr.PublishEvent(event); publishErr != nil {
		f.logger.Error("Failed to publish module event",
			zap.String("event", eventName),
			zap.String("module", moduleName),
			zap.Uint("site_id", siteID),
			zap.Error(publishErr),
		)
	}
}

// checkModuleAuthorization 检查模块授权（安全版本）
func (f *ModuleProxyFactory) checkModuleAuthorization(ctx context.Context, moduleName string) error {
	// 获取模块配置
	f.mu.RLock()
	config, exists := f.moduleConfigs[moduleName]
	f.mu.RUnlock()

	if !exists {
		return fmt.Errorf("module %s not registered", moduleName)
	}

	// 使用安全的授权规则检查（基于模块类型，防止配置文件绕过）
	requiresLicense := f.authRules.RequiresLicense(moduleName, config.Type)

	if !requiresLicense {
		// 模块不需要许可证，直接允许
		f.logger.Debug("Module does not require license",
			zap.String("module", moduleName),
		)
		return nil
	}

	// 需要许可证的模块，进行验证
	licenseInfo, err := f.licenseManager.ValidateModuleLicense(ctx, moduleName)
	if err != nil {
		f.logger.Warn("Module license validation failed",
			zap.String("module", moduleName),
			zap.String("type", string(config.Type)),
			zap.Bool("requires_license", requiresLicense),
			zap.Error(err),
		)

		return fmt.Errorf("module %s requires a valid license: %w", moduleName, err)
	}

	// 检查许可证是否有效
	if !licenseInfo.IsValid {
		f.logger.Warn("Module license is invalid",
			zap.String("module", moduleName),
			zap.String("error", licenseInfo.ErrorMsg),
		)

		return fmt.Errorf("module %s license is invalid: %s", moduleName, licenseInfo.ErrorMsg)
	}

	// 检查版本要求（如果有全局许可证信息）
	globalLicenseInfo := f.licenseManager.GetLicenseInfo()
	if globalLicenseInfo != nil {
		currentEdition := string(globalLicenseInfo.Edition)
		if !f.authRules.IsModuleAuthorized(moduleName, currentEdition, config.Type) {
			minimumEdition := f.authRules.GetMinimumEdition(moduleName)
			return fmt.Errorf("module %s requires %s edition or higher, current: %s",
				moduleName, minimumEdition, currentEdition)
		}
	}

	return nil
}

// GetAuthorizedModules 获取授权的模块列表
func (f *ModuleProxyFactory) GetAuthorizedModules(ctx context.Context) []string {
	var authorizedModules []string

	f.mu.RLock()
	defer f.mu.RUnlock()

	for moduleName, config := range f.moduleConfigs {
		// 核心模块总是授权的
		if config.IsCore() {
			authorizedModules = append(authorizedModules, moduleName)
			continue
		}

		// 检查其他模块的授权（使用ValidateModuleLicense）
		if licenseInfo, err := f.licenseManager.ValidateModuleLicense(ctx, moduleName); err == nil && licenseInfo.IsValid {
			authorizedModules = append(authorizedModules, moduleName)
		}
	}

	return authorizedModules
}

// GetUnauthorizedModules 获取未授权的模块列表
func (f *ModuleProxyFactory) GetUnauthorizedModules(ctx context.Context) []string {
	var unauthorizedModules []string

	f.mu.RLock()
	defer f.mu.RUnlock()

	for moduleName, config := range f.moduleConfigs {
		// 跳过核心模块
		if config.IsCore() {
			continue
		}

		// 检查授权（使用ValidateModuleLicense）
		if licenseInfo, err := f.licenseManager.ValidateModuleLicense(ctx, moduleName); err != nil || !licenseInfo.IsValid {
			// 但是要排除不需要许可证的模块
			if licenseInfo != nil && licenseInfo.Type == "none" && licenseInfo.IsValid {
				continue
			}
			unauthorizedModules = append(unauthorizedModules, moduleName)
		}
	}

	return unauthorizedModules
}

// registerModuleFactories 注册模块工厂
func (f *ModuleProxyFactory) registerModuleFactories() {
	f.logger.Info("Registering module factories")

	// 注册用户模块工厂
	f.registerUserModuleFactory()

	// TODO: 注册其他模块工厂
	// f.registerContentModuleFactory()
	// f.registerThemeModuleFactory()

	f.logger.Info("Module factories registered",
		zap.Int("total_configs", len(f.moduleConfigs)),
	)
}

// registerUserModuleFactory 注册用户模块工厂
func (f *ModuleProxyFactory) registerUserModuleFactory() {
	f.moduleConfigs["user"] = &ModuleConfig{
		Name:           "user",
		Version:        "1.0.0",
		Description:    "User management module with authentication and authorization",
		Author:         "GACMS Team",
		Dependencies:   []string{"database", "logger", "event"},
		IsGlobal:       false,
		SupportsTenant: true,
		Path:           "internal/modules/user",

		// 全局工厂（所有租户共享的用户服务）
		GlobalFactory: f.createGlobalUserModule,

		// 租户工厂（租户特定的用户服务）
		TenantFactory: f.createTenantUserModule,
	}

	f.logger.Debug("User module factory registered")
}

// createGlobalUserModule 创建全局用户模块实例
func (f *ModuleProxyFactory) createGlobalUserModule(deps *GlobalDependencies) (*ModuleInstance, error) {
	f.logger.Debug("Creating global user module instance")

	// 创建用户模块实例
	instance := &ModuleInstance{
		Name:        "user",
		Version:     "1.0.0",
		SiteID:      0,
		IsGlobal:    true,
		Services:    make(map[string]interface{}),
		Controllers: make(map[string]interface{}),
		Routes:      make([]RouteInfo, 0),
	}

	// 这里暂时返回空实例，因为用户模块主要是租户特定的
	// 全局用户模块可能只包含一些共享的工具服务

	f.logger.Debug("Global user module instance created")
	return instance, nil
}

// createTenantUserModule 创建租户特定用户模块实例
func (f *ModuleProxyFactory) createTenantUserModule(deps *TenantDependencies) (*ModuleInstance, error) {
	f.logger.Debug("Creating tenant user module instance",
		zap.Uint("site_id", deps.SiteID),
	)

	// 创建用户模块实例
	instance := &ModuleInstance{
		Name:        "user",
		Version:     "1.0.0",
		SiteID:      deps.SiteID,
		IsGlobal:    false,
		Services:    make(map[string]interface{}),
		Controllers: make(map[string]interface{}),
		Routes:      make([]RouteInfo, 0),
	}

	// TODO: 这里需要实际创建用户模块的服务和控制器
	// 由于用户模块使用了复杂的fx依赖注入，我们需要重构它们
	// 暂时返回空实例，在下一步中实现具体的服务创建

	f.logger.Debug("Tenant user module instance created",
		zap.Uint("site_id", deps.SiteID),
	)
	return instance, nil
}

// GetStats 获取工厂统计信息
func (f *ModuleProxyFactory) GetStats() map[string]interface{} {
	f.mu.RLock()
	defer f.mu.RUnlock()

	return map[string]interface{}{
		"load_count":        f.loadCount,
		"cache_hits":        f.cacheHits,
		"cache_misses":      f.cacheMisses,
		"global_instances":  len(f.globalInstances),
		"tenant_instances":  len(f.tenantInstances),
		"registered_configs": len(f.moduleConfigs),
	}
}
