/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/media/domain/contract/Storage.go
 * @Description: Defines the contract for file storage drivers.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"io"
	"mime/multipart"
)

// UploadCredentials contains the necessary information for a client to directly upload a file.
type UploadCredentials struct {
	URL       string            `json:"uploadUrl"`
	Method    string            `json:"method"`
	Headers   map[string]string `json:"headers"`
	FileKey   string            `json:"fileKey"`
	AccessURL string            `json:"accessUrl"`
}

// Storage defines the interface for a file storage driver (e.g., local, S3).
type Storage interface {
	// GetUploadCredentials generates temporary credentials for a client to upload a file directly.
	GetUploadCredentials(siteID uint, filename string, mimeType string) (*UploadCredentials, error)

	// Save uploads a file from a reader to the storage.
	// Note: This is used by the backend for processing (e.g., thumbnails), not for direct client uploads.
	Save(reader io.Reader, siteID uint, path string) (string, error)

	// GetURL returns the public access URL for a given file path.
	GetURL(path string) string
	
	// GetSignedURL returns a temporary, signed URL for accessing a private file.
	GetSignedURL(path string, expires int64) (string, error)

	// Delete removes a file from the storage.
	Delete(path string) error
} 