/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/post/domain/model/Post.go
 * @Description: Defines the Post domain model.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package model

import (
	"time"
	"gorm.io/gorm"
)

// Post represents a single content entry.
type Post struct {
	gorm.Model
	SiteID        uint   `gorm:"index"`
	Title         string `gorm:"type:varchar(255);not null"`
	Slug          string `gorm:"type:varchar(255);uniqueIndex;not null"`
	Content       string `gorm:"type:longtext"`
	AuthorID      uint   `gorm:"index"`
	ContentTypeID uint   `gorm:"index"`
	Status        int    `gorm:"default:1"` // 1: published, 0: draft
	PublishedAt   time.Time
} 