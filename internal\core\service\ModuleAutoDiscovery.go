/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/ModuleAutoDiscovery.go
 * @Description: 模块自发现服务，符合规范8：基于自发现路由的懒加载
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"sync"

	"gacms/internal/infrastructure/database"
	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// ModuleAutoDiscovery 模块自发现服务
// 实现规范8：基于自发现路由的懒加载，在首次请求时自动加载
type ModuleAutoDiscovery struct {
	discovery     *ModuleDiscovery
	manager       *ModuleManager
	router        contract.Router
	eventManager  contract.EventManager
	logger        *zap.Logger
	mu            sync.RWMutex
	loadedModules map[string]bool // 已加载的模块
	routeCache    map[string]string // 路由到模块的映射缓存
}

// ModuleAutoDiscoveryParams 自发现服务参数
type ModuleAutoDiscoveryParams struct {
	fx.In

	Discovery    *ModuleDiscovery
	Manager      *ModuleManager
	Router       contract.Router       `optional:"true"`
	EventManager contract.EventManager
	Logger       *zap.Logger
}

// NewModuleAutoDiscovery 创建模块自发现服务
func NewModuleAutoDiscovery(params ModuleAutoDiscoveryParams) *ModuleAutoDiscovery {
	return &ModuleAutoDiscovery{
		discovery:     params.Discovery,
		manager:       params.Manager,
		router:        params.Router,
		eventManager:  params.EventManager,
		logger:        params.Logger,
		loadedModules: make(map[string]bool),
		routeCache:    make(map[string]string),
	}
}

// DiscoverAndRegisterModules 发现并注册所有模块
// 符合规范9：领域驱动的分层设计，模块目录内的业务代码
func (d *ModuleAutoDiscovery) DiscoverAndRegisterModules(ctx context.Context) error {
	d.logger.Info("Starting module auto-discovery")

	// 扫描模块目录
	moduleDir := "internal/modules"
	modules, err := d.scanModuleDirectory(moduleDir)
	if err != nil {
		return fmt.Errorf("failed to scan module directory: %w", err)
	}

	// 获取站点ID（多租户支持）
	siteID, hasSiteID := database.SiteIDFrom(ctx)

	// 注册发现的模块
	for _, moduleInfo := range modules {
		recipe := d.createModuleRecipe(moduleInfo)
		
		if hasSiteID && !recipe.IsGlobalModule() {
			// 租户特定模块
			d.manager.RegisterTenantModuleForSite(siteID, recipe)
		} else {
			// 全局模块
			d.manager.RegisterGlobalModule(recipe)
		}

		// 发现并缓存路由
		d.discoverModuleRoutes(moduleInfo)
	}

	d.logger.Info("Module auto-discovery completed", 
		zap.Int("modules_found", len(modules)),
		zap.Uint("site_id", siteID),
	)

	return nil
}

// LazyLoadModuleForRoute 为路由懒加载模块
// 符合规范8：在首次请求时自动加载
func (d *ModuleAutoDiscovery) LazyLoadModuleForRoute(ctx context.Context, routePath string) error {
	d.mu.RLock()
	moduleName, exists := d.routeCache[routePath]
	d.mu.RUnlock()

	if !exists {
		// 尝试从路径推断模块名
		moduleName = d.inferModuleFromRoute(routePath)
		if moduleName == "" {
			return fmt.Errorf("no module found for route: %s", routePath)
		}
	}

	// 检查模块是否已加载
	d.mu.RLock()
	isLoaded := d.loadedModules[moduleName]
	d.mu.RUnlock()

	if isLoaded {
		return nil // 已经加载
	}

	// 懒加载模块
	return d.loadModuleLazily(ctx, moduleName)
}

// loadModuleLazily 懒加载模块
func (d *ModuleAutoDiscovery) loadModuleLazily(ctx context.Context, moduleName string) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	// 双重检查
	if d.loadedModules[moduleName] {
		return nil
	}

	d.logger.Info("Lazy loading module", zap.String("name", moduleName))

	// 获取站点ID
	siteID, _ := database.SiteIDFrom(ctx)

	// 加载模块
	var err error
	if siteID > 0 {
		_, err = d.discovery.LoadModuleForSite(siteID, moduleName)
	} else {
		_, err = d.discovery.LoadModule(moduleName)
	}

	if err != nil {
		return fmt.Errorf("failed to load module %s: %w", moduleName, err)
	}

	// 标记为已加载
	d.loadedModules[moduleName] = true

	// 发布模块加载事件
	event := d.eventManager.CreateEvent(ctx, "module.loaded", map[string]interface{}{
		"module_name": moduleName,
		"site_id":     siteID,
		"lazy_load":   true,
	})
	d.eventManager.PublishEvent(event)

	d.logger.Info("Module lazy loaded successfully", 
		zap.String("name", moduleName),
		zap.Uint("site_id", siteID),
	)

	return nil
}

// ModuleInfo 模块信息
type ModuleInfo struct {
	Name         string
	Path         string
	Version      string
	Type         ModuleType
	Dependencies []string
	Routes       []RouteInfo
	Controllers  []ControllerInfo
	HasRecipe    bool
	RecipePath   string
}

// RouteInfo 路由信息
type RouteInfo struct {
	Pattern string
	Method  string
	Handler string
}

// ControllerInfo 控制器信息
type ControllerInfo struct {
	Name    string
	Package string
	Methods []string
}

// scanModuleDirectory 扫描模块目录
func (d *ModuleAutoDiscovery) scanModuleDirectory(dir string) ([]*ModuleInfo, error) {
	var modules []*ModuleInfo

	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过非目录
		if !info.IsDir() {
			return nil
		}

		// 跳过根目录
		if path == dir {
			return nil
		}

		// 检查是否是模块目录
		if d.isModuleDirectory(path) {
			moduleInfo, err := d.analyzeModule(path)
			if err != nil {
				d.logger.Warn("Failed to analyze module", 
					zap.String("path", path),
					zap.Error(err),
				)
				return nil
			}
			modules = append(modules, moduleInfo)
		}

		return nil
	})

	return modules, err
}

// isModuleDirectory 检查是否是模块目录
func (d *ModuleAutoDiscovery) isModuleDirectory(path string) bool {
	// 检查是否有module.go或recipe.go文件
	moduleFile := filepath.Join(path, "module.go")
	recipeFile := filepath.Join(path, "recipe.go")
	
	return d.fileExists(moduleFile) || d.fileExists(recipeFile)
}

// analyzeModule 分析模块
func (d *ModuleAutoDiscovery) analyzeModule(path string) (*ModuleInfo, error) {
	moduleName := filepath.Base(path)
	
	moduleInfo := &ModuleInfo{
		Name:         moduleName,
		Path:         path,
		Version:      "1.0.0", // 默认版本
		Type:         ExtensionModule,
		Dependencies: make([]string, 0),
		Routes:       make([]RouteInfo, 0),
		Controllers:  make([]ControllerInfo, 0),
	}

	// 分析Go文件
	err := d.analyzeGoFiles(path, moduleInfo)
	if err != nil {
		return nil, err
	}

	return moduleInfo, nil
}

// analyzeGoFiles 分析Go文件
func (d *ModuleAutoDiscovery) analyzeGoFiles(dir string, moduleInfo *ModuleInfo) error {
	return filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 只处理Go文件
		if !strings.HasSuffix(path, ".go") {
			return nil
		}

		// 解析Go文件
		fset := token.NewFileSet()
		node, err := parser.ParseFile(fset, path, nil, parser.ParseComments)
		if err != nil {
			return err
		}

		// 分析AST
		d.analyzeAST(node, moduleInfo)

		return nil
	})
}

// analyzeAST 分析AST
func (d *ModuleAutoDiscovery) analyzeAST(node *ast.File, moduleInfo *ModuleInfo) {
	ast.Inspect(node, func(n ast.Node) bool {
		switch x := n.(type) {
		case *ast.TypeSpec:
			// 检查是否是控制器
			if strings.HasSuffix(x.Name.Name, "Controller") {
				controller := ControllerInfo{
					Name:    x.Name.Name,
					Package: node.Name.Name,
					Methods: make([]string, 0),
				}
				moduleInfo.Controllers = append(moduleInfo.Controllers, controller)
			}
		case *ast.FuncDecl:
			// 检查是否是Recipe函数
			if x.Name.Name == "Recipe" || x.Name.Name == "Module" {
				moduleInfo.HasRecipe = true
			}
		}
		return true
	})
}

// createModuleRecipe 创建模块Recipe
func (d *ModuleAutoDiscovery) createModuleRecipe(moduleInfo *ModuleInfo) *ModuleRecipe {
	// 创建基础Recipe
	var recipe *ModuleRecipe
	
	switch moduleInfo.Type {
	case CoreModule:
		recipe = NewCoreModuleRecipe(moduleInfo.Name, moduleInfo.Version, fx.Options())
	case ExtensionModule:
		recipe = NewExtensionModuleRecipe(moduleInfo.Name, moduleInfo.Version, fx.Options())
	case ThirdPartyModule:
		recipe = NewThirdPartyModuleRecipe(moduleInfo.Name, moduleInfo.Version, fx.Options())
	default:
		recipe = NewExtensionModuleRecipe(moduleInfo.Name, moduleInfo.Version, fx.Options())
	}

	// 设置路径和依赖
	recipe.Path = moduleInfo.Path
	recipe.Dependencies = moduleInfo.Dependencies

	return recipe
}

// discoverModuleRoutes 发现模块路由
func (d *ModuleAutoDiscovery) discoverModuleRoutes(moduleInfo *ModuleInfo) {
	d.mu.Lock()
	defer d.mu.Unlock()

	// 基于约定的路由发现
	for _, controller := range moduleInfo.Controllers {
		baseRoute := fmt.Sprintf("/%s/%s", 
			strings.ToLower(moduleInfo.Name),
			strings.ToLower(strings.TrimSuffix(controller.Name, "Controller")),
		)
		
		// 缓存路由到模块的映射
		d.routeCache[baseRoute] = moduleInfo.Name
		d.routeCache[baseRoute+"/*"] = moduleInfo.Name
	}
}

// inferModuleFromRoute 从路由推断模块名
func (d *ModuleAutoDiscovery) inferModuleFromRoute(routePath string) string {
	parts := strings.Split(strings.Trim(routePath, "/"), "/")
	if len(parts) > 0 {
		return parts[0]
	}
	return ""
}

// fileExists 检查文件是否存在
func (d *ModuleAutoDiscovery) fileExists(path string) bool {
	// TODO: 实现真实的文件存在检查
	return true // 临时返回true
}
