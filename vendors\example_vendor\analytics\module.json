{"name": "analytics", "version": "1.0.0", "description": "Advanced analytics and reporting module for GACMS", "author": "GACMS Team", "vendor": "gacms", "homepage": "https://gacms.io/extensions/analytics", "repository": "https://github.com/gacms/gacms-analytics", "license": "MIT", "dependencies": ["content", "user"], "conflicts": [], "min_gacms_version": "1.0.0", "max_gacms_version": "2.0.0", "permissions": [{"name": "analytics:view", "description": "View analytics reports", "category": "analytics"}, {"name": "analytics:manage", "description": "Manage analytics settings", "category": "analytics"}, {"name": "analytics:export", "description": "Export analytics data", "category": "analytics"}], "routes": [{"path": "/admin/analytics", "method": "GET", "controller": "AnalyticsController", "action": "dashboard", "permission": "analytics:view", "middlewares": ["auth", "permission"]}, {"path": "/admin/analytics/reports", "method": "GET", "controller": "AnalyticsController", "action": "reports", "permission": "analytics:view", "middlewares": ["auth", "permission"]}, {"path": "/admin/analytics/settings", "method": "GET", "controller": "AnalyticsController", "action": "settings", "permission": "analytics:manage", "middlewares": ["auth", "permission"]}, {"path": "/admin/analytics/settings", "method": "POST", "controller": "AnalyticsController", "action": "updateSettings", "permission": "analytics:manage", "middlewares": ["auth", "permission"]}, {"path": "/api/analytics/track", "method": "POST", "controller": "AnalyticsAPIController", "action": "track", "middlewares": ["cors"]}, {"path": "/api/analytics/export", "method": "GET", "controller": "AnalyticsAPIController", "action": "export", "permission": "analytics:export", "middlewares": ["auth", "permission"]}], "events": {"publishes": ["analytics.tracked", "analytics.report.generated", "analytics.settings.updated"], "listens": [{"event": "content.created", "handler": "ContentAnalyticsHandler", "priority": 100}, {"event": "content.updated", "handler": "ContentAnalyticsHandler", "priority": 100}, {"event": "user.login", "handler": "UserAnalyticsHandler", "priority": 100}, {"event": "user.logout", "handler": "UserAnalyticsHandler", "priority": 100}]}, "config": {"tracking_enabled": true, "data_retention_days": 365, "real_time_tracking": false, "anonymize_ip": true}, "settings": [{"key": "tracking_enabled", "type": "boolean", "default": true, "required": false, "description": "Enable or disable analytics tracking"}, {"key": "data_retention_days", "type": "integer", "default": 365, "required": true, "description": "Number of days to retain analytics data"}, {"key": "tracking_code", "type": "string", "default": "", "required": false, "description": "Google Analytics tracking code"}, {"key": "report_frequency", "type": "select", "default": "weekly", "required": true, "description": "Frequency of automated reports", "options": ["daily", "weekly", "monthly"]}], "entry_point": "main.go", "assets": ["assets/css/analytics.css", "assets/js/analytics.js", "assets/js/charts.js"], "templates": ["templates/dashboard.html", "templates/reports.html", "templates/settings.html"], "enabled": true}