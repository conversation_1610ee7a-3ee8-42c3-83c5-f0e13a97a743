# GACMS商业授权体系设计决策

## 决策背景

基于2025年6月21日的深入讨论，重新设计GACMS的商业授权体系，解决原设计中的问题：
1. 租户授权绕过官方的问题
2. 版本分级逻辑不合理的问题
3. 硬件资源限制不当的问题
4. 开发优先级和实施策略的问题

## 实施状态

**✅ 已完成实施** (2025年6月22日)
- 双重许可证机制已完整实现
- 降级逻辑已实现并测试
- 三层功能检查机制已部署
- 版本逻辑已优化并简化
- 许可证服务层已完善

## 核心决策

### 1. 双重授权体系

#### A. 系统授权 (System License)
- **授权对象**: 部署方（企业IT部门/SaaS服务商）
- **授权内容**: GACMS软件的部署和运行权限
- **默认权限**: 1个租户（域名）的使用权限
- **版本控制**: 决定系统最高可用版本
- **费用模式**: 按年订阅
- **扩展方式**: 购买额外的租户使用授权

#### B. 使用授权 (Usage License)
- **授权对象**: 最终用户（租户/域名）
- **授权内容**: 特定版本功能的使用权限
- **验证方式**: 每个租户独立的许可证
- **费用模式**: 按年周期续费
- **免费版本**: 个人版无需官方许可证
- **付费版本**: 专业版/商业版需要官方许可证

### 2. 版本分级重新设计

#### A. 个人版 (Personal) - 个人博客、小型网站
```yaml
实现方式: 系统固化（无需许可证）
站点数量: 1个
管理用户: 3个
功能模块:
  - basic_content: 基础内容管理
  - basic_theme: 基础主题支持
  - basic_seo: 基础SEO工具
  - basic_user: 基础用户管理
  - api_access: API访问权限
业务限制:
  - API调用: 1000次/天
  - 存储空间: 无限制
  - 带宽: 无限制
特点:
  - 系统固化，始终可用
  - 无需任何许可证
  - 作为降级保底功能
```

#### B. 专业版 (Professional) - 中小企业、专业网站
```yaml
实现方式: 编译时固化配置
站点数量: 5个
管理用户: 20个
功能模块:
  - 个人版所有功能
  - advanced_theme: 高级主题管理
  - advanced_seo: 高级SEO工具
  - workflow: 工作流管理
业务限制:
  - API调用: 10000次/天
  - 存储空间: 无限制
  - 带宽: 无限制
特点:
  - 编译时固化配置
  - 需要使用许可证
  - 配置从configs/editions.yaml读取
```

#### C. 商业版 (Business) - 大型企业、高流量网站
```yaml
实现方式: 全能版，跳过所有检查
站点数量: 无限制
管理用户: 无限制
功能模块:
  - 编译时包含的所有功能
  - 无需功能枚举
  - 自动拥有所有权限
业务限制:
  - 所有限制: 无限制
  - 跳过所有限制检查
特点:
  - 检测到商业版许可证后跳过所有检查
  - 全能版，拥有系统所有功能
  - 无需维护功能列表
```

### 3. 域名授权策略

#### A. 核心原则
- **根域名授权**: 购买example.com的授权
- **子域名共享**: 自动覆盖所有*.example.com
- **无数量限制**: 不限制子域名数量和用途
- **所有权验证**: 通过DNS TXT记录验证域名所有权

#### B. 支持场景
- 用户个性化子域名: user1.example.com
- 商户子域名: shop1.example.com
- 功能模块子域名: blog.example.com, api.example.com
- 任何合理的子域名使用

#### C. 安全措施
- DNS TXT记录验证域名所有权
- 定期验证域名所有权
- 防止许可证跨域名使用
- 数字签名验证许可证完整性

### 4. 开发优先级和实施策略

#### A. 第一优先级: 完善GACMS核心功能
- 专注于CMS的核心价值
- 模块系统、内容管理、主题系统
- 确保产品稳定性和可用性
- 为后续商业化打好基础

#### B. 第二优先级: 基础授权验证
- 在GACMS中实现许可证验证逻辑
- 支持离线许可证文件验证
- 基础的版本分级功能
- 简单的功能限制机制

#### C. 第三优先级: 许可证管理系统
- 开发独立的许可证管理系统
- 早期使用线下授权方式
- 根据市场反馈决定投入程度
- 逐步发展为自动化平台

### 5. 商业授权开关设计

#### A. 配置开关
```yaml
commercial_authorization:
  enabled: false  # 默认关闭
  license_file_path: "./license.json"
  validation_interval: 24h
  offline_mode: true
```

#### B. 环境变量
```bash
GACMS_COMMERCIAL_AUTH_ENABLED=false
GACMS_LICENSE_FILE_PATH=./license.json
GACMS_LICENSE_VALIDATION_INTERVAL=24h
```

#### C. 开关控制逻辑
- 关闭时: 跳过所有商业授权验证，所有功能可用
- 开启时: 执行完整的授权验证流程
- 开发阶段: 默认关闭，避免影响开发测试
- 商业化阶段: 启用授权验证

## 技术实现决策

### 1. 授权验证架构（已实现）
```
用户请求 → 三层功能检查 → 降级机制 → 功能访问控制
```

#### 三层功能检查机制
```
1. 编译边界检查: 功能是否编译到系统中
   ↓ (失败时尝试降级)
2. 安装配置检查: 功能是否在安装时启用
   ↓ (失败时尝试降级)
3. 许可证验证: 功能是否有许可证授权
   ↓ (失败时尝试降级)
4. 个人版功能检查: 是否为个人版基础功能
```

#### 双重许可证验证
```
系统许可证验证:
- 控制部署权限和租户配额
- 决定系统最高可用版本
- 过期后整个系统降级到个人版

使用许可证验证:
- 控制租户功能权限
- 必须关联到有效的系统许可证
- 过期后对应租户降级到个人版
```

### 2. 降级机制（已实现）
```
许可证过期/无效处理:
1. 系统许可证过期 → 整个系统降级到个人版
2. 使用许可证过期 → 对应租户降级到个人版
3. 个人版功能始终可用 → 系统永不完全不可用
4. 降级状态信息 → 提供升级建议和联系方式
```

### 3. 版本检查逻辑优化（已实现）
```
个人版: 系统固化
- 5个基础功能硬编码在系统中
- 无需配置文件和许可证验证
- 作为降级保底功能

专业版: 编译时固化
- 配置从configs/editions.yaml读取
- 编译时固化为常量
- 通过ProfessionalEditionProvider接口访问

商业版: 全能版跳过检查
- 检测到商业版许可证后跳过所有限制检查
- 无需功能枚举，自动拥有所有权限
- 性能最优，逻辑最简
```

### 4. 架构集成（遵循14条核心原则）
```
React+Gin无头架构:
- 前端React独立部署
- 后端Gin提供API服务
- 许可证验证集成到Gin中间件

微核心+模块架构:
- 核心提供许可证验证工具
- 模块通过公共接口使用许可证服务
- 模块间通过事件观察者通信

fx依赖注入:
- 许可证管理器通过fx.Option声明
- 服务代理工厂实现懒加载
- 事件驱动的模块通信

多租户支持:
- 系统许可证控制租户配额
- 使用许可证按租户分配
- 租户隔离的许可证验证
```

## 商业模式设计

### 1. 企业内部部署
```
系统授权: 商业版系统许可证 ¥50,000/年
默认包含: 1个租户的商业版使用权限
扩展费用: 每增加1个租户 ¥15,000/年
```

### 2. SaaS服务商模式
```
系统授权: 商业版系统许可证 ¥50,000/年
租户授权: 
  - 专业版使用授权 ¥5,000/年/租户
  - 商业版使用授权 ¥15,000/年/租户
  - 个人版使用: 免费
```

## 风险控制

### 1. 技术风险
- 许可证破解: 使用RSA 4096位加密
- 域名滥用: 域名所有权验证
- 系统绕过: 编译时+运行时双重控制

### 2. 商业风险
- 市场接受度: 个人版免费降低门槛
- 竞争压力: 灵活的定价策略
- 技术支持: 分级的技术支持服务

## 实施时间线

### ✅ 阶段1: 基础实现 (已完成 - 2025年6月22日)
- ✅ 双重许可证机制完整实现
- ✅ 三层功能检查机制部署
- ✅ 智能降级逻辑实现
- ✅ 版本检查逻辑优化
- ✅ 许可证服务层完善
- ✅ 代码质量优化（删除约300行冗余代码）

### 🔄 阶段2: 文档完善 (进行中 - 2025年6月22日)
- 🔄 更新系统设计文档
- ⏳ 更新API文档
- ⏳ 更新配置文档
- ⏳ 创建部署指南
- ⏳ 更新用户手册
- ⏳ 创建开发者文档

### 📋 阶段3: 测试和部署 (待定)
- 创建完整的测试用例
- 性能测试和优化
- 生产环境部署验证
- 用户反馈收集

### 🚀 阶段4: 商业化准备 (待定)
- 许可证管理系统开发
- 商业化流程完善
- 合作伙伴计划
- 市场推广准备

## 决策理由

1. **双重授权体系**: 确保官方收益，防止绕过授权 ✅
2. **域名策略简化**: 平衡安全性和用户体验 ✅
3. **开发优先级**: 先做好产品，再考虑商业化 ✅
4. **三层功能检查**: 编译时+安装时+运行时的完整验证 ✅
5. **智能降级机制**: 确保系统在任何情况下都可用 ✅
6. **版本逻辑优化**: 个人版固化、专业版编译时固化、商业版全能 ✅
7. **代码质量优先**: 删除冗余代码，简化复杂逻辑 ✅
8. **架构原则遵循**: 严格遵循14条核心架构原则 ✅

## 技术成果总结

### 🎯 核心功能实现
- **双重许可证架构**: 系统许可证 + 使用许可证的完整层级管理
- **智能降级机制**: 许可证过期后自动降级到个人版基础功能
- **三层功能检查**: 编译边界 → 安装配置 → 许可证验证的完整流程
- **版本逻辑优化**: 三个版本的检查逻辑明确分离和优化

### 📊 代码质量提升
- **删除重复代码**: 约300行重复定义和无意义检查
- **简化复杂逻辑**: 商业版从复杂枚举简化为跳过检查
- **统一接口设计**: 个人版、专业版都使用接口访问
- **最小代码原则**: 只保留必要的功能，删除冗余逻辑

### 🏗️ 架构原则体现
- **React+Gin无头架构**: 许可证验证集成到后端API服务
- **微核心+模块**: 核心提供许可证工具，模块通过接口使用
- **fx依赖注入**: 许可证管理器通过fx.Option声明和懒加载
- **事件观察者模式**: 模块间通过事件通信，声明式配置
- **多租户支持**: 系统和使用许可证的分层租户管理

## 相关文档

### 核心实现文件
- [许可证管理器](../../internal/core/service/LicenseManager.go) - 双重许可证核心逻辑
- [功能守卫](../../internal/core/service/FeatureGuard.go) - 三层功能检查机制
- [许可证服务](../../internal/core/service/LicenseService.go) - 统一业务接口
- [编译时版本管理](../../internal/core/service/CompileTimeEditionManager.go) - 版本固化逻辑

### 配置文件
- [商业授权配置](../../configs/commercial.yaml) - 商业授权开关和配置
- [版本分级配置](../../configs/editions.yaml) - 专业版编译时配置

### 架构文档
- [GACMS核心架构原则](.cursor/rules/architecture.md) - 14条核心架构原则
- [模块设计规范](.cursor/rules/module-design.md) - 模块开发规范
- [依赖注入规范](.cursor/rules/dependency-injection.md) - fx依赖注入规范

---
**决策日期**: 2025-06-21
**实施完成**: 2025-06-22
**决策人**: 项目团队
**状态**: ✅ 已实施完成
**下次评审**: 根据市场反馈和用户需求确定
