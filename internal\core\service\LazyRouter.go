/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/LazyRouter.go
 * @Description: 懒加载路由系统
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"fmt"
	"net/http"
	"reflect"
	"strings"
	"sync"

	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// RouteEntry 路由条目
type RouteEntry struct {
	Pattern     string
	Method      string
	Handler     http.HandlerFunc
	ModuleName  string
	Loaded      bool
	LoadFunc    func() (http.HandlerFunc, error)
}

// LazyRouter 懒加载路由器（集成ModuleProxyFactory）
type LazyRouter struct {
	routes        map[string]*RouteEntry
	mu            sync.RWMutex
	logger        *zap.Logger
	discovery     *ModuleDiscovery
	routables     []contract.IRoutable

	// 新增：模块代理工厂集成
	moduleFactory *ModuleProxyFactory
	eventMgr      contract.EventManager
	config        contract.Config

	// 缓存的入口点配置
	adminEntryPoint  string
	apiEntryPoint    string
	publicEntryPoint string
}

// LazyRouterParams 懒加载路由器参数
type LazyRouterParams struct {
	fx.In

	Logger        *zap.Logger
	Discovery     *ModuleDiscovery
	Routables     []contract.IRoutable `group:"routables"`
	ModuleFactory *ModuleProxyFactory
	EventMgr      contract.EventManager
	Config        contract.Config
}

// NewLazyRouter 创建懒加载路由器
func NewLazyRouter(params LazyRouterParams) *LazyRouter {
	router := &LazyRouter{
		routes:        make(map[string]*RouteEntry),
		logger:        params.Logger,
		discovery:     params.Discovery,
		routables:     params.Routables,
		moduleFactory: params.ModuleFactory,
		eventMgr:      params.EventMgr,
		config:        params.Config,
	}

	// 加载入口点配置
	router.loadEntryPointConfig()

	// 注册所有可路由组件
	router.registerRoutables()

	// 监听模块加载事件
	router.setupEventListeners()

	return router
}

// loadEntryPointConfig 加载入口点配置
func (r *LazyRouter) loadEntryPointConfig() {
	// 从配置文件加载入口点配置
	r.adminEntryPoint = r.config.GetString("routing.entry_points.admin")
	r.apiEntryPoint = r.config.GetString("routing.entry_points.api")
	r.publicEntryPoint = r.config.GetString("routing.entry_points.public")

	// 设置默认值
	if r.adminEntryPoint == "" {
		r.adminEntryPoint = "admin"
	}
	if r.apiEntryPoint == "" {
		r.apiEntryPoint = "api"
	}
	if r.publicEntryPoint == "" {
		r.publicEntryPoint = ""
	}

	r.logger.Info("Entry points configured",
		zap.String("admin_entry", r.adminEntryPoint),
		zap.String("api_entry", r.apiEntryPoint),
		zap.String("public_entry", r.publicEntryPoint),
	)
}

// registerRoutables 注册所有可路由组件
func (r *LazyRouter) registerRoutables() {
	for _, routable := range r.routables {
		r.logger.Debug("Registering routable", zap.String("type", fmt.Sprintf("%T", routable)))
		// TODO: 从routable中提取路由信息并注册
	}
}

// RegisterRoute 注册路由
func (r *LazyRouter) RegisterRoute(pattern, method, moduleName string, loadFunc func() (http.HandlerFunc, error)) {
	r.mu.Lock()
	defer r.mu.Unlock()

	key := r.routeKey(method, pattern)
	r.routes[key] = &RouteEntry{
		Pattern:    pattern,
		Method:     method,
		ModuleName: moduleName,
		Loaded:     false,
		LoadFunc:   loadFunc,
	}

	r.logger.Debug("Route registered",
		zap.String("pattern", pattern),
		zap.String("method", method),
		zap.String("module", moduleName),
	)
}

// ServeHTTP 实现http.Handler接口（集成约定路由和模块懒加载）
func (r *LazyRouter) ServeHTTP(w http.ResponseWriter, req *http.Request) {
	r.logger.Debug("Processing request",
		zap.String("method", req.Method),
		zap.String("path", req.URL.Path),
	)

	// 1. 尝试查找已注册的路由
	route, err := r.findRoute(req.Method, req.URL.Path)
	if err == nil {
		// 找到已注册的路由，使用传统的懒加载处理器
		handler, err := r.loadHandler(route)
		if err != nil {
			r.logger.Error("Failed to load handler",
				zap.String("pattern", route.Pattern),
				zap.String("module", route.ModuleName),
				zap.Error(err),
			)
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			return
		}
		handler(w, req)
		return
	}

	// 2. 尝试约定路由处理
	if r.handleConventionRoute(w, req) {
		return
	}

	// 3. 都没找到，返回404
	r.logger.Debug("No route found",
		zap.String("method", req.Method),
		zap.String("path", req.URL.Path),
	)
	http.NotFound(w, req)
}

// findRoute 查找匹配的路由
func (r *LazyRouter) findRoute(method, path string) (*RouteEntry, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	// 精确匹配
	key := r.routeKey(method, path)
	if route, exists := r.routes[key]; exists {
		return route, nil
	}

	// 模式匹配
	for _, route := range r.routes {
		if route.Method == method && r.matchPattern(route.Pattern, path) {
			return route, nil
		}
	}

	return nil, fmt.Errorf("no route found for %s %s", method, path)
}

// loadHandler 懒加载处理器
func (r *LazyRouter) loadHandler(route *RouteEntry) (http.HandlerFunc, error) {
	r.mu.Lock()
	defer r.mu.Unlock()

	// 如果已经加载，直接返回
	if route.Loaded && route.Handler != nil {
		return route.Handler, nil
	}

	// 懒加载处理器
	if route.LoadFunc != nil {
		handler, err := route.LoadFunc()
		if err != nil {
			return nil, fmt.Errorf("failed to load handler: %w", err)
		}

		route.Handler = handler
		route.Loaded = true

		r.logger.Info("Handler loaded",
			zap.String("pattern", route.Pattern),
			zap.String("module", route.ModuleName),
		)

		return handler, nil
	}

	return nil, fmt.Errorf("no load function for route %s", route.Pattern)
}

// matchPattern 匹配路由模式
func (r *LazyRouter) matchPattern(pattern, path string) bool {
	// 简单的模式匹配实现
	// TODO: 实现更复杂的路由匹配逻辑，支持参数、通配符等

	// 精确匹配
	if pattern == path {
		return true
	}

	// 前缀匹配（以/*结尾的模式）
	if strings.HasSuffix(pattern, "/*") {
		prefix := strings.TrimSuffix(pattern, "/*")
		return strings.HasPrefix(path, prefix)
	}

	return false
}

// routeKey 生成路由键
func (r *LazyRouter) routeKey(method, pattern string) string {
	return method + ":" + pattern
}

// GetRoutes 获取所有路由
func (r *LazyRouter) GetRoutes() map[string]*RouteEntry {
	r.mu.RLock()
	defer r.mu.RUnlock()

	routes := make(map[string]*RouteEntry, len(r.routes))
	for k, v := range r.routes {
		routes[k] = v
	}
	return routes
}

// GetLoadedRoutes 获取已加载的路由
func (r *LazyRouter) GetLoadedRoutes() []*RouteEntry {
	r.mu.RLock()
	defer r.mu.RUnlock()

	loaded := make([]*RouteEntry, 0)
	for _, route := range r.routes {
		if route.Loaded {
			loaded = append(loaded, route)
		}
	}
	return loaded
}

// PreloadModule 预加载模块的所有路由
func (r *LazyRouter) PreloadModule(moduleName string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	count := 0
	for _, route := range r.routes {
		if route.ModuleName == moduleName && !route.Loaded {
			if route.LoadFunc != nil {
				handler, err := route.LoadFunc()
				if err != nil {
					return fmt.Errorf("failed to preload route %s: %w", route.Pattern, err)
				}
				route.Handler = handler
				route.Loaded = true
				count++
			}
		}
	}

	r.logger.Info("Module routes preloaded",
		zap.String("module", moduleName),
		zap.Int("count", count),
	)

	return nil
}

// RegisterConventionRoutes 注册约定式路由
func (r *LazyRouter) RegisterConventionRoutes(moduleName string, controller interface{}) error {
	// TODO: 使用反射分析控制器，按约定生成路由
	// 例如：
	// - Index() -> GET /module
	// - Show(id) -> GET /module/{id}
	// - Create() -> POST /module
	// - Update(id) -> PUT /module/{id}
	// - Delete(id) -> DELETE /module/{id}

	r.logger.Debug("Registering convention routes",
		zap.String("module", moduleName),
		zap.String("controller", fmt.Sprintf("%T", controller)),
	)

	return nil
}

// handleConventionRoute 处理约定路由（支持三入口）
func (r *LazyRouter) handleConventionRoute(w http.ResponseWriter, req *http.Request) bool {
	// 解析约定路径
	routeEntry := r.parseConventionPath(req.URL.Path, req.Method)
	if routeEntry == nil || routeEntry.Module == "" {
		return false
	}

	r.logger.Debug("Handling convention route",
		zap.String("entry_point", routeEntry.EntryPoint),
		zap.String("module", routeEntry.Module),
		zap.String("controller", routeEntry.Controller),
		zap.String("method", routeEntry.Method),
		zap.Strings("params", routeEntry.Params),
	)

	// 根据入口点进行权限检查
	if !r.checkEntryPointAccess(routeEntry.EntryPoint, req) {
		r.logger.Debug("Access denied for entry point",
			zap.String("entry_point", routeEntry.EntryPoint),
			zap.String("path", r.sanitizePathForLog(req.URL.Path, routeEntry.EntryPoint)),
		)

		// 对于管理员入口，根据配置决定是否隐藏路径信息
		if routeEntry.EntryPoint == "admin" && r.config.GetBool("routing.security.hide_admin_path") {
			// 返回通用的404错误，不暴露管理员路径
			http.NotFound(w, req)
		} else {
			http.Error(w, "Forbidden", http.StatusForbidden)
		}
		return true
	}

	// 懒加载模块
	module, err := r.moduleFactory.GetModule(req.Context(), routeEntry.Module)
	if err != nil {
		r.logger.Debug("Failed to load module for convention route",
			zap.String("module", routeEntry.Module),
			zap.String("entry_point", routeEntry.EntryPoint),
			zap.Error(err),
		)
		return false
	}

	// 获取控制器
	controller, exists := module.GetController(routeEntry.Controller)
	if !exists {
		r.logger.Debug("Controller not found in module",
			zap.String("module", routeEntry.Module),
			zap.String("controller", routeEntry.Controller),
			zap.String("entry_point", routeEntry.EntryPoint),
		)
		return false
	}

	// 调用控制器方法
	err = r.invokeControllerMethod(controller, routeEntry.Method, w, req, routeEntry.Params)
	if err != nil {
		r.logger.Error("Failed to invoke controller method",
			zap.String("entry_point", routeEntry.EntryPoint),
			zap.String("module", routeEntry.Module),
			zap.String("controller", routeEntry.Controller),
			zap.String("method", routeEntry.Method),
			zap.Error(err),
		)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return true
	}

	r.logger.Debug("Convention route handled successfully",
		zap.String("entry_point", routeEntry.EntryPoint),
		zap.String("module", routeEntry.Module),
		zap.String("controller", routeEntry.Controller),
		zap.String("method", routeEntry.Method),
	)

	return true
}

// RouteEntry 路由入口信息
type RouteEntry struct {
	EntryPoint string   // "admin", "public", "api"
	Module     string   // 模块名
	Controller string   // 控制器名
	Method     string   // 方法名
	Params     []string // 参数列表
}

// parseConventionPath 解析约定路径（支持三入口）
func (r *LazyRouter) parseConventionPath(path string, httpMethod string) *RouteEntry {
	// 移除前后的斜杠并分割路径
	parts := strings.Split(strings.Trim(path, "/"), "/")

	if len(parts) < 1 {
		return nil
	}

	entry := &RouteEntry{}

	// 检查入口点（使用配置的入口点）
	firstPart := parts[0]
	switch firstPart {
	case r.adminEntryPoint:
		// 后台管理入口：/{admin_entry}/模块名/控制器名/方法名
		entry.EntryPoint = "admin"
		if len(parts) < 2 {
			return nil
		}
		entry.Module = parts[1]
		parts = parts[1:] // 移除admin前缀，后续处理与公共入口相同
	case r.apiEntryPoint:
		// API接口入口：/{api_entry}/模块名/控制器名/方法名
		entry.EntryPoint = "api"
		if len(parts) < 2 {
			return nil
		}
		entry.Module = parts[1]
		parts = parts[1:] // 移除api前缀，后续处理与公共入口相同
	default:
		// 公共入口：/模块名/控制器名/方法名
		entry.EntryPoint = "public"
		entry.Module = parts[0]
	}

	// 解析控制器名（从模块后开始）
	if len(parts) >= 2 {
		entry.Controller = parts[1]
	} else {
		entry.Controller = entry.Module // 默认使用模块名作为控制器名
	}

	// 解析方法名
	if len(parts) >= 3 {
		entry.Method = parts[2]
	} else {
		// 根据HTTP方法和入口点确定默认方法名
		entry.Method = r.getDefaultMethodName(httpMethod, entry.EntryPoint)
	}

	// 提取参数
	if len(parts) > 3 {
		entry.Params = parts[3:]
	}

	// 标准化名称
	entry.Controller = r.normalizeControllerName(entry.Controller)
	entry.Method = r.normalizeMethodName(entry.Method)

	return entry
}

// getDefaultMethodName 根据HTTP方法和入口点获取默认方法名
func (r *LazyRouter) getDefaultMethodName(httpMethod, entryPoint string) string {
	switch entryPoint {
	case "admin":
		// 后台管理默认方法
		switch strings.ToUpper(httpMethod) {
		case "GET":
			return "AdminIndex"
		case "POST":
			return "AdminStore"
		case "PUT", "PATCH":
			return "AdminUpdate"
		case "DELETE":
			return "AdminDelete"
		default:
			return "AdminIndex"
		}
	case "api":
		// API接口默认方法
		switch strings.ToUpper(httpMethod) {
		case "GET":
			return "ApiIndex"
		case "POST":
			return "ApiStore"
		case "PUT", "PATCH":
			return "ApiUpdate"
		case "DELETE":
			return "ApiDelete"
		default:
			return "ApiIndex"
		}
	default: // public
		// 公共入口默认方法
		switch strings.ToUpper(httpMethod) {
		case "GET":
			return "Index"
		case "POST":
			return "Store"
		case "PUT", "PATCH":
			return "Update"
		case "DELETE":
			return "Delete"
		default:
			return "Index"
		}
	}
}

// normalizeControllerName 标准化控制器名称
func (r *LazyRouter) normalizeControllerName(name string) string {
	if name == "" {
		return ""
	}

	// 首字母大写，并添加Controller后缀（如果没有的话）
	normalized := strings.Title(strings.ToLower(name))
	if !strings.HasSuffix(normalized, "Controller") {
		normalized += "Controller"
	}

	return normalized
}

// normalizeMethodName 标准化方法名称
func (r *LazyRouter) normalizeMethodName(name string) string {
	if name == "" {
		return ""
	}

	// 首字母大写
	return strings.Title(strings.ToLower(name))
}

// checkEntryPointAccess 检查入口点访问权限
func (r *LazyRouter) checkEntryPointAccess(entryPoint string, req *http.Request) bool {
	switch entryPoint {
	case "admin":
		// 后台管理入口需要管理员权限
		return r.checkAdminAccess(req)
	case "api":
		// API入口需要API认证
		return r.checkAPIAccess(req)
	case "public":
		// 公共入口允许所有访问
		return true
	default:
		return false
	}
}

// checkAdminAccess 检查管理员访问权限
func (r *LazyRouter) checkAdminAccess(req *http.Request) bool {
	// TODO: 实现真正的管理员权限检查
	// 这里可以检查：
	// 1. 管理员会话
	// 2. 管理员令牌
	// 3. IP白名单
	// 4. 其他安全策略

	r.logger.Debug("Checking admin access",
		zap.String("path", req.URL.Path),
		zap.String("remote_addr", req.RemoteAddr),
	)

	// 暂时允许所有访问，实际应用中需要实现真正的权限检查
	return true
}

// checkAPIAccess 检查API访问权限
func (r *LazyRouter) checkAPIAccess(req *http.Request) bool {
	// TODO: 实现真正的API认证检查
	// 这里可以检查：
	// 1. API密钥
	// 2. JWT令牌
	// 3. OAuth认证
	// 4. 请求频率限制

	r.logger.Debug("Checking API access",
		zap.String("path", req.URL.Path),
		zap.String("user_agent", req.UserAgent()),
	)

	// 暂时允许所有访问，实际应用中需要实现真正的API认证
	return true
}

// sanitizePathForLog 清理日志中的路径信息（安全考虑）
func (r *LazyRouter) sanitizePathForLog(path, entryPoint string) string {
	// 对于管理员入口，如果配置了隐藏路径，则在日志中也隐藏
	if entryPoint == "admin" && r.config.GetBool("routing.security.hide_admin_path") {
		// 将管理员路径替换为通用标识
		adminPath := "/" + r.adminEntryPoint
		if strings.HasPrefix(path, adminPath) {
			return "[ADMIN_PATH]" + strings.TrimPrefix(path, adminPath)
		}
	}
	return path
}

// GetEntryPointConfig 获取入口点配置（用于其他组件）
func (r *LazyRouter) GetEntryPointConfig() map[string]string {
	return map[string]string{
		"admin":  r.adminEntryPoint,
		"api":    r.apiEntryPoint,
		"public": r.publicEntryPoint,
	}
}

// IsAdminPath 检查是否为管理员路径
func (r *LazyRouter) IsAdminPath(path string) bool {
	adminPath := "/" + r.adminEntryPoint
	return strings.HasPrefix(path, adminPath+"/") || path == adminPath
}

// IsAPIPath 检查是否为API路径
func (r *LazyRouter) IsAPIPath(path string) bool {
	apiPath := "/" + r.apiEntryPoint
	return strings.HasPrefix(path, apiPath+"/") || path == apiPath
}

// 实现contract.Router接口

// RegisterRoute 注册单个路由到指定入口点
func (r *LazyRouter) RegisterRoute(entryPoint contract.RouteTarget, method, pattern string, handler interface{}) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	// 根据入口点添加前缀
	fullPattern := r.buildFullPattern(entryPoint, pattern)

	routeKey := fmt.Sprintf("%s:%s", method, fullPattern)
	r.routes[routeKey] = &RouteEntry{
		Pattern:    fullPattern,
		Method:     method,
		Handler:    handler,
		ModuleName: "manual", // 手动注册的路由
	}

	r.logger.Debug("Route registered",
		zap.String("entry_point", string(entryPoint)),
		zap.String("method", method),
		zap.String("pattern", fullPattern),
	)

	return nil
}

// RegisterController 注册控制器到指定入口点
func (r *LazyRouter) RegisterController(entryPoint contract.RouteTarget, controller interface{}) error {
	// TODO: 实现控制器注册逻辑
	// 这里需要反射分析控制器的方法并自动注册路由
	r.logger.Debug("Controller registration requested",
		zap.String("entry_point", string(entryPoint)),
		zap.String("controller_type", reflect.TypeOf(controller).String()),
	)
	return nil
}

// RegisterRoutable 注册可路由组件（向后兼容）
func (r *LazyRouter) RegisterRoutable(registration *contract.RoutableRegistration) error {
	// TODO: 实现可路由组件注册
	r.logger.Debug("Routable registration requested",
		zap.String("target", string(registration.Target)),
	)
	return nil
}

// 入口点特定的路由注册方法

// RegisterAdminRoute 注册管理员路由
func (r *LazyRouter) RegisterAdminRoute(method, pattern string, handler interface{}) error {
	return r.RegisterRoute(contract.AdminRoute, method, pattern, handler)
}

// RegisterPublicRoute 注册公共路由
func (r *LazyRouter) RegisterPublicRoute(method, pattern string, handler interface{}) error {
	return r.RegisterRoute(contract.FrontendRoute, method, pattern, handler)
}

// RegisterAPIRoute 注册API路由
func (r *LazyRouter) RegisterAPIRoute(method, pattern string, handler interface{}) error {
	return r.RegisterRoute(contract.ThirdPartyAPIRoute, method, pattern, handler)
}

// 批量控制器注册方法

// RegisterAdminController 注册管理员控制器
func (r *LazyRouter) RegisterAdminController(controller interface{}) error {
	return r.RegisterController(contract.AdminRoute, controller)
}

// RegisterPublicController 注册公共控制器
func (r *LazyRouter) RegisterPublicController(controller interface{}) error {
	return r.RegisterController(contract.FrontendRoute, controller)
}

// RegisterAPIController 注册API控制器
func (r *LazyRouter) RegisterAPIController(controller interface{}) error {
	return r.RegisterController(contract.ThirdPartyAPIRoute, controller)
}

// 路由发现和内省方法

// GetRoutes 获取指定入口点的所有路由
func (r *LazyRouter) GetRoutes(entryPoint contract.RouteTarget) []contract.RouteInfo {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var routes []contract.RouteInfo
	entryPrefix := r.getEntryPrefix(entryPoint)

	for _, route := range r.routes {
		if strings.HasPrefix(route.Pattern, entryPrefix) {
			routeInfo := contract.RouteInfo{
				EntryPoint: entryPoint,
				Method:     route.Method,
				Pattern:    route.Pattern,
				Handler:    fmt.Sprintf("%T", route.Handler),
				Module:     route.ModuleName,
			}
			routes = append(routes, routeInfo)
		}
	}

	return routes
}

// GetAllRoutes 获取所有入口点的路由
func (r *LazyRouter) GetAllRoutes() map[contract.RouteTarget][]contract.RouteInfo {
	allRoutes := make(map[contract.RouteTarget][]contract.RouteInfo)

	allRoutes[contract.AdminRoute] = r.GetRoutes(contract.AdminRoute)
	allRoutes[contract.FrontendRoute] = r.GetRoutes(contract.FrontendRoute)
	allRoutes[contract.ThirdPartyAPIRoute] = r.GetRoutes(contract.ThirdPartyAPIRoute)

	return allRoutes
}

// SetEntryPointPath 设置入口点路径
func (r *LazyRouter) SetEntryPointPath(entryPoint contract.RouteTarget, path string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	switch entryPoint {
	case contract.AdminRoute:
		r.adminEntryPoint = path
	case contract.ThirdPartyAPIRoute:
		r.apiEntryPoint = path
	case contract.FrontendRoute:
		r.publicEntryPoint = path
	default:
		return fmt.Errorf("unknown entry point: %s", entryPoint)
	}

	r.logger.Info("Entry point path updated",
		zap.String("entry_point", string(entryPoint)),
		zap.String("path", path),
	)

	return nil
}

// 辅助方法

// buildFullPattern 构建完整的路由模式
func (r *LazyRouter) buildFullPattern(entryPoint contract.RouteTarget, pattern string) string {
	prefix := r.getEntryPrefix(entryPoint)

	// 确保pattern以/开头
	if !strings.HasPrefix(pattern, "/") {
		pattern = "/" + pattern
	}

	// 如果是公共入口且前缀为空，直接返回pattern
	if prefix == "" {
		return pattern
	}

	return prefix + pattern
}

// getEntryPrefix 获取入口点前缀
func (r *LazyRouter) getEntryPrefix(entryPoint contract.RouteTarget) string {
	switch entryPoint {
	case contract.AdminRoute:
		if r.adminEntryPoint == "" {
			return ""
		}
		return "/" + r.adminEntryPoint
	case contract.ThirdPartyAPIRoute:
		if r.apiEntryPoint == "" {
			return ""
		}
		return "/" + r.apiEntryPoint
	case contract.FrontendRoute:
		if r.publicEntryPoint == "" {
			return ""
		}
		return "/" + r.publicEntryPoint
	default:
		return ""
	}
}

// invokeControllerMethod 调用控制器方法
func (r *LazyRouter) invokeControllerMethod(controller interface{}, methodName string, w http.ResponseWriter, req *http.Request, params []string) error {
	// 使用反射调用控制器方法
	controllerValue := reflect.ValueOf(controller)
	controllerType := controllerValue.Type()

	// 查找方法
	method := controllerValue.MethodByName(methodName)
	if !method.IsValid() {
		return fmt.Errorf("method %s not found in controller %s", methodName, controllerType.Name())
	}

	// 准备方法参数
	args, err := r.prepareMethodArguments(method.Type(), w, req, params)
	if err != nil {
		return fmt.Errorf("failed to prepare method arguments: %w", err)
	}

	// 调用方法
	results := method.Call(args)

	// 处理返回值
	return r.handleMethodResults(results)
}

// prepareMethodArguments 准备方法参数
func (r *LazyRouter) prepareMethodArguments(methodType reflect.Type, w http.ResponseWriter, req *http.Request, params []string) ([]reflect.Value, error) {
	numArgs := methodType.NumIn()
	args := make([]reflect.Value, numArgs)

	for i := 0; i < numArgs; i++ {
		argType := methodType.In(i)

		switch argType {
		case reflect.TypeOf((*http.ResponseWriter)(nil)).Elem():
			args[i] = reflect.ValueOf(w)
		case reflect.TypeOf((*http.Request)(nil)):
			args[i] = reflect.ValueOf(req)
		default:
			// 对于其他类型的参数，尝试从params中获取
			if i-2 < len(params) { // 减去w和req两个参数
				// 简单的字符串参数处理
				if argType.Kind() == reflect.String {
					args[i] = reflect.ValueOf(params[i-2])
				} else {
					// 对于其他类型，使用零值
					args[i] = reflect.Zero(argType)
				}
			} else {
				// 没有足够的参数，使用零值
				args[i] = reflect.Zero(argType)
			}
		}
	}

	return args, nil
}

// handleMethodResults 处理方法返回值
func (r *LazyRouter) handleMethodResults(results []reflect.Value) error {
	// 检查是否有错误返回值
	for _, result := range results {
		if result.Type().Implements(reflect.TypeOf((*error)(nil)).Elem()) {
			if !result.IsNil() {
				return result.Interface().(error)
			}
		}
	}

	return nil
}

// setupEventListeners 设置事件监听器
func (r *LazyRouter) setupEventListeners() {
	if r.eventMgr == nil {
		return
	}

	// 监听模块加载事件
	r.eventMgr.Subscribe("module.loaded", r.onModuleLoaded)

	r.logger.Debug("Event listeners setup for LazyRouter")
}

// onModuleLoaded 处理模块加载事件
func (r *LazyRouter) onModuleLoaded(event contract.Event) {
	eventData := event.GetData()
	moduleName, ok := eventData["module_name"].(string)
	if !ok {
		return
	}

	siteID, _ := eventData["site_id"].(uint)

	r.logger.Debug("Module loaded event received",
		zap.String("module", moduleName),
		zap.Uint("site_id", siteID),
	)

	// 这里可以实现自动路由注册逻辑
	// 例如：扫描模块的路由定义并注册到路由表
}

// RegisterAdminRoutes 注册后台管理路由
func (r *LazyRouter) RegisterAdminRoutes(moduleName string, routes map[string]http.HandlerFunc) {
	for pattern, handler := range routes {
		adminPattern := "/admin" + pattern
		r.RegisterRoute(adminPattern, "GET", moduleName, func() (http.HandlerFunc, error) {
			return handler, nil
		})
	}
}

// RegisterAPIRoutes 注册API路由
func (r *LazyRouter) RegisterAPIRoutes(moduleName string, routes map[string]http.HandlerFunc) {
	for pattern, handler := range routes {
		apiPattern := "/api" + pattern
		r.RegisterRoute(apiPattern, "GET", moduleName, func() (http.HandlerFunc, error) {
			return handler, nil
		})
	}
}

// RegisterPublicRoutes 注册公共路由
func (r *LazyRouter) RegisterPublicRoutes(moduleName string, routes map[string]http.HandlerFunc) {
	for pattern, handler := range routes {
		r.RegisterRoute(pattern, "GET", moduleName, func() (http.HandlerFunc, error) {
			return handler, nil
		})
	}
}
