<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 用户编辑</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .image-preview-container {
            margin-top: 10px;
            max-width: 200px;
        }
        .image-preview {
            width: 100%;
            height: auto;
            border-radius: 50%;
            border: 1px solid #444;
        }
        .role-badge {
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: capitalize;
        }
        .role-admin {
            background-color: #dc3545;
            color: white;
        }
        .role-editor {
            background-color: #ffc107;
            color: #333;
        }
        .role-author {
            background-color: #007bff;
            color: white;
        }
        .role-subscriber {
            background-color: #6c757d;
            color: white;
        }
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #555;
            transition: .4s;
            border-radius: 24px;
        }
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .toggle-slider {
            background-color: #007bff;
        }
        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10">
        <!-- 侧边栏内容将通过JavaScript从模板加载 -->
    </aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">用户管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <a href="users.html" class="flex items-center justify-center bg-gray-700 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-600 transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i> 返回用户列表
                        </a>
                    </div>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <form action="#" method="post">
                    <!-- 表单网格 -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- 左侧：主要信息表单 -->
                        <div class="lg:col-span-2 space-y-5">
                            <h3 class="text-lg font-semibold mb-4 text-white">基本信息</h3>
                            
                            <!-- 用户名 -->
                            <div class="mb-4">
                                <label class="block text-gray-400 text-sm font-medium mb-2">用户名</label>
                                <input type="text" value="admin_zhang" 
                                       class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            
                            <!-- 显示名称 -->
                            <div class="mb-4">
                                <label class="block text-gray-400 text-sm font-medium mb-2">显示名称</label>
                                <input type="text" value="张小明" 
                                       class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            
                            <!-- 电子邮箱 -->
                            <div class="mb-4">
                                <label class="block text-gray-400 text-sm font-medium mb-2">电子邮箱</label>
                                <input type="email" value="<EMAIL>" 
                                       class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            
                            <!-- 个人网站 -->
                            <div class="mb-4">
                                <label class="block text-gray-400 text-sm font-medium mb-2">个人网站</label>
                                <input type="url" value="https://zhang.example.com" 
                                       class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            
                            <!-- 密码设置 -->
                            <h3 class="text-lg font-semibold mb-4 text-white mt-8">密码设置</h3>
                            <div class="mb-4">
                                <label class="block text-gray-400 text-sm font-medium mb-2">新密码</label>
                                <input type="password" 
                                       class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <p class="text-gray-400 text-xs mt-1">留空表示不修改密码</p>
                            </div>
                            
                            <!-- 确认密码 -->
                            <div class="mb-4">
                                <label class="block text-gray-400 text-sm font-medium mb-2">确认密码</label>
                                <input type="password" 
                                       class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            
                            <!-- 密码强度 -->
                            <div class="mb-4">
                                <label class="block text-gray-400 text-sm font-medium mb-2">密码强度</label>
                                <div class="h-2 w-full bg-gray-700 rounded-full overflow-hidden">
                                    <div class="h-full bg-yellow-500" style="width: 60%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400 mt-1">
                                    <span>弱</span>
                                    <span>中</span>
                                    <span>强</span>
                                </div>
                            </div>
                            
                            <!-- 个人简介 -->
                            <h3 class="text-lg font-semibold mb-4 text-white mt-8">个人简介</h3>
                            <div class="mb-4">
                                <label class="block text-gray-400 text-sm font-medium mb-2">简介</label>
                                <textarea rows="4" 
                                          class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">资深内容创作者，专注于技术文章撰写，GACMS平台管理员。</textarea>
                            </div>
                        </div>
                        
                        <!-- 右侧：头像和角色设置 -->
                        <div class="lg:col-span-1 space-y-5">
                            <h3 class="text-lg font-semibold mb-4 text-white">头像</h3>
                            
                            <!-- 头像预览 -->
                            <div class="image-preview-container mx-auto">
                                <div class="image-preview overflow-hidden">
                                    <img src="./assets/images/avatar.jpg" alt="用户头像" class="w-full h-full object-cover">
                                </div>
                            </div>
                            
                            <!-- 上传按钮 -->
                            <div class="flex justify-center mt-4">
                                <label for="avatar-upload" class="cursor-pointer bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-upload mr-2"></i> 上传头像
                                    <input id="avatar-upload" type="file" accept="image/*" class="hidden">
                                </label>
                            </div>
                            
                            <!-- 角色设置 -->
                            <h3 class="text-lg font-semibold mb-4 text-white mt-8">角色设置</h3>
                            <div class="mb-4">
                                <label class="block text-gray-400 text-sm font-medium mb-2">用户角色</label>
                                <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="admin">管理员</option>
                                    <option value="editor" selected>编辑</option>
                                    <option value="author">作者</option>
                                    <option value="subscriber">订阅者</option>
                                </select>
                            </div>
                            
                            <!-- 当前角色显示 -->
                            <div class="mb-4">
                                <span class="role-badge role-editor">编辑</span>
                                <p class="text-gray-400 text-xs mt-2">加入时间：2025-04-15 10:30</p>
                            </div>
                            
                            <!-- 账户状态 -->
                            <h3 class="text-lg font-semibold mb-4 text-white mt-8">账户状态</h3>
                            <div class="flex items-center justify-between mb-4">
                                <span class="text-gray-400">账户激活</span>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <div class="flex items-center justify-between mb-4">
                                <span class="text-gray-400">邮件验证</span>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <div class="flex items-center justify-between mb-4">
                                <span class="text-gray-400">两步验证</span>
                                <label class="toggle-switch">
                                    <input type="checkbox">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 保存按钮 -->
                    <div class="flex justify-end space-x-4 mt-8">
                        <button type="button" class="py-2 px-6 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors">
                            取消
                        </button>
                        <button type="submit" class="py-2 px-6 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                            保存更改
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
</body>
</html>