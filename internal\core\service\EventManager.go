/*
 * @Author: <PERSON><PERSON>eh <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/EventManager.go
 * @Description: 事件管理器实现，作为事件系统的主要入口点
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"fmt"
	"strings"

	"gacms/internal/core/bus"
	"gacms/internal/infrastructure/database"
	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// DefaultEventManager 是 contract.EventManager 接口的默认实现
// 它封装了事件总线的功能，并提供了额外的管理功能
type DefaultEventManager struct {
	eventBus        contract.EventBus
	handlerRegistry contract.EventHandlerRegistry
	moduleFactory   *ModuleProxyFactory
	eventMapper     ModuleEventMapper
	logger          *zap.Logger
}

// DefaultEventManagerParams 定义了创建 DefaultEventManager 所需的参数
type DefaultEventManagerParams struct {
	fx.In

	EventBus        contract.EventBus
	HandlerRegistry contract.EventHandlerRegistry
	ModuleFactory   *ModuleProxyFactory
	EventMapper     ModuleEventMapper
	Logger          *zap.Logger
}

// NewDefaultEventManager 创建一个新的 DefaultEventManager 实例
func NewDefaultEventManager(params DefaultEventManagerParams) contract.EventManager {
	return &DefaultEventManager{
		eventBus:        params.EventBus,
		handlerRegistry: params.HandlerRegistry,
		moduleFactory:   params.ModuleFactory,
		eventMapper:     params.EventMapper,
		logger:          params.Logger,
	}
}

// RegisterHandler 注册一个事件处理器
func (m *DefaultEventManager) RegisterHandler(handler contract.EventHandler) error {
	// 先注册到处理器注册表
	if err := m.handlerRegistry.RegisterHandler(handler); err != nil {
		return err
	}

	// 然后注册到事件总线
	return m.eventBus.Register(handler)
}

// UnregisterHandler 取消注册一个事件处理器
func (m *DefaultEventManager) UnregisterHandler(handler contract.EventHandler) error {
	// 先从事件总线取消注册
	if err := m.eventBus.Unregister(handler); err != nil {
		return err
	}

	// 然后从处理器注册表取消注册
	return m.handlerRegistry.UnregisterHandler(handler)
}

// PublishEvent 发布一个事件（多租户支持）
func (m *DefaultEventManager) PublishEvent(event contract.Event) error {
	// 确保事件包含租户信息
	event = m.ensureTenantContext(event)

	m.logger.Debug("Publishing event",
		zap.String("event", string(event.Name())),
		zap.Any("tenant_id", m.getTenantIDFromEvent(event)),
	)

	// 检查是否有处理器，如果没有则尝试懒加载相关模块
	if !m.eventBus.HasHandlers(event.Name()) {
		m.logger.Debug("No handlers found for event, attempting lazy load",
			zap.String("event", string(event.Name())),
		)

		if err := m.lazyLoadHandlersForEvent(event); err != nil {
			m.logger.Warn("Failed to lazy load handlers for event",
				zap.String("event", string(event.Name())),
				zap.Error(err),
			)
			// 不返回错误，继续发布事件（可能有其他处理器）
		}
	}

	return m.eventBus.Publish(event)
}

// PublishEventAsync 异步发布一个事件（多租户支持）
func (m *DefaultEventManager) PublishEventAsync(ctx context.Context, event contract.Event) <-chan error {
	// 确保事件包含租户信息
	event = m.ensureTenantContext(event)

	m.logger.Debug("Async publishing event",
		zap.String("event", string(event.Name())),
		zap.Any("tenant_id", m.getTenantIDFromEvent(event)),
	)
	return m.eventBus.PublishAsync(ctx, event)
}

// CreateEvent 创建一个新的事件实例（多租户支持）
func (m *DefaultEventManager) CreateEvent(ctx context.Context, name contract.EventName, payload interface{}) contract.Event {
	event := bus.NewBaseEvent(ctx, name, payload)

	// 自动添加租户信息到事件元数据
	if siteID, ok := database.SiteIDFrom(ctx); ok {
		event.SetMetadata("tenant_id", siteID)
		event.SetMetadata("site_id", siteID)
	}

	return event
}

// GetEventBus 获取底层的事件总线实例
func (m *DefaultEventManager) GetEventBus() contract.EventBus {
	return m.eventBus
}

// GetHandlerRegistry 获取处理器注册表
func (m *DefaultEventManager) GetHandlerRegistry() contract.EventHandlerRegistry {
	return m.handlerRegistry
}

// ensureTenantContext 确保事件包含租户上下文
func (m *DefaultEventManager) ensureTenantContext(event contract.Event) contract.Event {
	// 如果事件已经有租户信息，直接返回
	if _, exists := event.GetMetadata("tenant_id"); exists {
		return event
	}

	// 从事件上下文获取租户ID
	if siteID, ok := database.SiteIDFrom(event.Context()); ok {
		event.SetMetadata("tenant_id", siteID)
		event.SetMetadata("site_id", siteID)
	}

	return event
}

// getTenantIDFromEvent 从事件获取租户ID
func (m *DefaultEventManager) getTenantIDFromEvent(event contract.Event) interface{} {
	if tenantID, exists := event.GetMetadata("tenant_id"); exists {
		return tenantID
	}

	// 从上下文获取
	if siteID, ok := database.SiteIDFrom(event.Context()); ok {
		return siteID
	}

	return nil
}

// PublishEventForSite 为指定站点发布事件
func (m *DefaultEventManager) PublishEventForSite(siteID uint, name contract.EventName, payload interface{}) error {
	// 创建带租户上下文的事件
	ctx := database.WithSiteID(context.Background(), siteID)
	event := m.CreateEvent(ctx, name, payload)

	return m.PublishEvent(event)
}

// PublishEventAsyncForSite 为指定站点异步发布事件
func (m *DefaultEventManager) PublishEventAsyncForSite(siteID uint, name contract.EventName, payload interface{}) <-chan error {
	// 创建带租户上下文的事件
	ctx := database.WithSiteID(context.Background(), siteID)
	event := m.CreateEvent(ctx, name, payload)

	return m.PublishEventAsync(ctx, event)
}

// DefaultEventHandlerRegistry 是 contract.EventHandlerRegistry 接口的默认实现
type DefaultEventHandlerRegistry struct {
	handlers map[string]contract.EventHandler
	eventMap map[contract.EventName][]string // 事件名称到处理器名称的映射
	logger   *zap.Logger
}

// DefaultEventHandlerRegistryParams 定义了创建 DefaultEventHandlerRegistry 所需的参数
type DefaultEventHandlerRegistryParams struct {
	fx.In

	Logger *zap.Logger
}

// NewDefaultEventHandlerRegistry 创建一个新的 DefaultEventHandlerRegistry 实例
func NewDefaultEventHandlerRegistry(params DefaultEventHandlerRegistryParams) contract.EventHandlerRegistry {
	return &DefaultEventHandlerRegistry{
		handlers: make(map[string]contract.EventHandler),
		eventMap: make(map[contract.EventName][]string),
		logger:   params.Logger,
	}
}

// RegisterHandler 注册一个事件处理器
func (r *DefaultEventHandlerRegistry) RegisterHandler(handler contract.EventHandler) error {
	if handler == nil {
		return nil
	}

	handlerName := handler.HandlerName()
	r.handlers[handlerName] = handler

	// 更新事件映射
	for _, eventName := range handler.SupportedEvents() {
		r.eventMap[eventName] = append(r.eventMap[eventName], handlerName)
	}

	r.logger.Debug("Registered handler",
		zap.String("handler", handlerName),
	)

	return nil
}

// UnregisterHandler 取消注册一个事件处理器
func (r *DefaultEventHandlerRegistry) UnregisterHandler(handler contract.EventHandler) error {
	if handler == nil {
		return nil
	}

	handlerName := handler.HandlerName()
	if _, exists := r.handlers[handlerName]; !exists {
		return nil
	}

	// 从事件映射中移除
	for _, eventName := range handler.SupportedEvents() {
		handlers := r.eventMap[eventName]
		for i, h := range handlers {
			if h == handlerName {
				// 移除处理器
				r.eventMap[eventName] = append(handlers[:i], handlers[i+1:]...)
				break
			}
		}

		// 如果没有处理器，删除事件条目
		if len(r.eventMap[eventName]) == 0 {
			delete(r.eventMap, eventName)
		}
	}

	// 删除处理器
	delete(r.handlers, handlerName)

	r.logger.Debug("Unregistered handler",
		zap.String("handler", handlerName),
	)

	return nil
}

// GetHandlersForEvent 获取指定事件的所有处理器
func (r *DefaultEventHandlerRegistry) GetHandlersForEvent(eventName contract.EventName) []contract.EventHandler {
	handlerNames, exists := r.eventMap[eventName]
	if !exists {
		return []contract.EventHandler{}
	}

	result := make([]contract.EventHandler, 0, len(handlerNames))
	for _, name := range handlerNames {
		if handler, ok := r.handlers[name]; ok {
			result = append(result, handler)
		}
	}

	return result
}

// HasHandlersForEvent 检查是否有处理器注册了指定事件
func (r *DefaultEventHandlerRegistry) HasHandlersForEvent(eventName contract.EventName) bool {
	handlers, exists := r.eventMap[eventName]
	return exists && len(handlers) > 0
}

// GetAllHandlers 获取所有注册的处理器
func (r *DefaultEventHandlerRegistry) GetAllHandlers() []contract.EventHandler {
	result := make([]contract.EventHandler, 0, len(r.handlers))
	for _, handler := range r.handlers {
		result = append(result, handler)
	}
	return result
}



// lazyLoadHandlersForEvent 为指定事件懒加载处理器
func (m *DefaultEventManager) lazyLoadHandlersForEvent(event contract.Event) error {
	eventName := event.Name()

	// 使用事件映射器查找事件对应的模块
	moduleName, exists := m.eventMapper.GetModuleForEvent(eventName)
	if !exists {
		return fmt.Errorf("cannot determine module for event: %s", eventName)
	}

	m.logger.Info("Attempting to lazy load module for event",
		zap.String("event", string(eventName)),
		zap.String("module", moduleName),
	)

	// 懒加载模块
	ctx := event.Context()
	if ctx == nil {
		ctx = context.Background()
	}

	_, err := m.moduleFactory.GetModule(ctx, moduleName)
	if err != nil {
		return fmt.Errorf("failed to lazy load module %s for event %s: %w", moduleName, eventName, err)
	}

	m.logger.Info("Successfully lazy loaded module for event",
		zap.String("event", string(eventName)),
		zap.String("module", moduleName),
	)

	return nil
}

