/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/contenttype/domain/model/ContentType.go
 * @Description: Defines the ContentType model for the content modeling engine.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package model

import "time"

// ContentType represents the structure and metadata of a custom content model.
type ContentType struct {
	ID          uint      `gorm:"primaryKey"`
	Name        string    `gorm:"type:varchar(255);not null"`
	Slug        string    `gorm:"type:varchar(100);uniqueIndex;not null"`
	Description string    `gorm:"type:text"`
	IsSystem    bool      `gorm:"default:false"`
	CreatedAt   time.Time
	UpdatedAt   time.Time

	// A ContentType has many Fields
	Fields []Field `gorm:"foreignKey:ContentTypeID"`
}

func (ContentType) TableName() string {
	return "content_types"
} 