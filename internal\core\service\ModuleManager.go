/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/ModuleManager.go
 * @Description: 模块管理服务
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"fmt"
	"sync"

	"gacms/internal/infrastructure/database"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// ModuleManager 模块管理器
type ModuleManager struct {
	discovery *ModuleDiscovery
	logger    *zap.Logger
	mu        sync.RWMutex
}

// ModuleManagerParams 模块管理器参数
type ModuleManagerParams struct {
	fx.In

	Discovery *ModuleDiscovery
	Logger    *zap.Logger
}

// NewModuleManager 创建模块管理器
func NewModuleManager(params ModuleManagerParams) *ModuleManager {
	return &ModuleManager{
		discovery: params.Discovery,
		logger:    params.Logger,
	}
}

// EnableModule 启用模块（向后兼容，操作全局模块）
func (m *ModuleManager) EnableModule(name string) error {
	m.logger.Info("Enabling global module", zap.String("name", name))
	return m.discovery.EnableModule(name)
}

// EnableModuleForSite 为指定站点启用模块
func (m *ModuleManager) EnableModuleForSite(ctx context.Context, name string) error {
	siteID, ok := database.SiteIDFrom(ctx)
	if !ok {
		return fmt.Errorf("site ID not found in context")
	}

	m.logger.Info("Enabling module for site",
		zap.Uint("site_id", siteID),
		zap.String("name", name),
	)
	return m.discovery.EnableModuleForSite(siteID, name)
}

// DisableModule 禁用模块（向后兼容，操作全局模块）
func (m *ModuleManager) DisableModule(name string) error {
	m.logger.Info("Disabling global module", zap.String("name", name))
	return m.discovery.DisableModule(name)
}

// DisableModuleForSite 为指定站点禁用模块
func (m *ModuleManager) DisableModuleForSite(ctx context.Context, name string) error {
	siteID, ok := database.SiteIDFrom(ctx)
	if !ok {
		return fmt.Errorf("site ID not found in context")
	}

	m.logger.Info("Disabling module for site",
		zap.Uint("site_id", siteID),
		zap.String("name", name),
	)
	return m.discovery.DisableModuleForSite(siteID, name)
}

// ActivateModule 激活模块（向后兼容，操作全局模块）
func (m *ModuleManager) ActivateModule(name string) error {
	m.logger.Info("Activating global module", zap.String("name", name))
	return m.discovery.ActivateModule(name)
}

// ActivateModuleForSite 为指定站点激活模块
func (m *ModuleManager) ActivateModuleForSite(ctx context.Context, name string) error {
	siteID, ok := database.SiteIDFrom(ctx)
	if !ok {
		return fmt.Errorf("site ID not found in context")
	}

	m.logger.Info("Activating module for site",
		zap.Uint("site_id", siteID),
		zap.String("name", name),
	)
	return m.discovery.ActivateModule(name) // 激活逻辑暂时保持全局
}

// DeactivateModule 停用模块（向后兼容，操作全局模块）
func (m *ModuleManager) DeactivateModule(name string) error {
	m.logger.Info("Deactivating global module", zap.String("name", name))
	return m.discovery.DeactivateModule(name)
}

// DeactivateModuleForSite 为指定站点停用模块
func (m *ModuleManager) DeactivateModuleForSite(ctx context.Context, name string) error {
	siteID, ok := database.SiteIDFrom(ctx)
	if !ok {
		return fmt.Errorf("site ID not found in context")
	}

	m.logger.Info("Deactivating module for site",
		zap.Uint("site_id", siteID),
		zap.String("name", name),
	)
	return m.discovery.DeactivateModule(name) // 停用逻辑暂时保持全局
}

// RestartModule 重启模块
func (m *ModuleManager) RestartModule(name string) error {
	m.logger.Info("Restarting module", zap.String("name", name))
	
	// 先停用
	if err := m.discovery.DeactivateModule(name); err != nil {
		return fmt.Errorf("failed to deactivate module: %w", err)
	}
	
	// 再激活
	if err := m.discovery.ActivateModule(name); err != nil {
		return fmt.Errorf("failed to activate module: %w", err)
	}
	
	return nil
}

// GetModuleStatus 获取模块状态
func (m *ModuleManager) GetModuleStatus(name string) (ModuleStatus, error) {
	module, exists := m.discovery.GetModule(name)
	if !exists {
		return ModuleStatusDisabled, fmt.Errorf("module not found: %s", name)
	}
	return module.Status, nil
}

// ListModules 列出所有模块及其状态（向后兼容，只返回全局模块）
func (m *ModuleManager) ListModules() map[string]ModuleStatus {
	modules := m.discovery.GetAllModules()
	result := make(map[string]ModuleStatus, len(modules))

	for name, module := range modules {
		result[name] = module.Status
	}

	return result
}

// ListModulesForSite 列出指定站点的所有模块及其状态
func (m *ModuleManager) ListModulesForSite(ctx context.Context) map[string]ModuleStatus {
	siteID, ok := database.SiteIDFrom(ctx)
	if !ok {
		m.logger.Error("Site ID not found in context")
		return make(map[string]ModuleStatus)
	}

	modules := m.discovery.GetAllModulesForSite(siteID)
	result := make(map[string]ModuleStatus, len(modules))

	for name, module := range modules {
		result[name] = module.Status
	}

	return result
}

// GetModuleInfo 获取模块详细信息（向后兼容，查找全局模块）
func (m *ModuleManager) GetModuleInfo(name string) (*ModuleRecipe, error) {
	module, exists := m.discovery.GetModule(name)
	if !exists {
		return nil, fmt.Errorf("module not found: %s", name)
	}
	return module, nil
}

// GetModuleInfoForSite 获取指定站点的模块详细信息
func (m *ModuleManager) GetModuleInfoForSite(ctx context.Context, name string) (*ModuleRecipe, error) {
	siteID, ok := database.SiteIDFrom(ctx)
	if !ok {
		return nil, fmt.Errorf("site ID not found in context")
	}

	module, exists := m.discovery.GetModuleForSite(siteID, name)
	if !exists {
		return nil, fmt.Errorf("module not found: %s for site %d", name, siteID)
	}
	return module, nil
}

// BatchEnable 批量启用模块
func (m *ModuleManager) BatchEnable(names []string) map[string]error {
	results := make(map[string]error, len(names))
	
	for _, name := range names {
		results[name] = m.EnableModule(name)
	}
	
	return results
}

// BatchDisable 批量禁用模块
func (m *ModuleManager) BatchDisable(names []string) map[string]error {
	results := make(map[string]error, len(names))
	
	for _, name := range names {
		results[name] = m.DisableModule(name)
	}
	
	return results
}

// BatchActivate 批量激活模块
func (m *ModuleManager) BatchActivate(names []string) map[string]error {
	results := make(map[string]error, len(names))

	// 按依赖顺序激活
	modules := make([]*ModuleRecipe, 0, len(names))
	for _, name := range names {
		if module, exists := m.discovery.GetModule(name); exists {
			modules = append(modules, module)
		} else {
			results[name] = fmt.Errorf("module not found: %s", name)
		}
	}

	// 排序模块
	sorted, err := m.discovery.SortModulesByDependencies(modules)
	if err != nil {
		// 如果排序失败，按原顺序激活
		for _, name := range names {
			if results[name] == nil {
				results[name] = m.ActivateModule(name)
			}
		}
		return results
	}

	// 按依赖顺序激活
	for _, module := range sorted {
		results[module.Name] = m.ActivateModule(module.Name)
	}

	return results
}

// BatchDeactivate 批量停用模块
func (m *ModuleManager) BatchDeactivate(names []string) map[string]error {
	results := make(map[string]error, len(names))

	// 按依赖关系逆序停用
	modules := make([]*ModuleRecipe, 0, len(names))
	for _, name := range names {
		if module, exists := m.discovery.GetModule(name); exists {
			modules = append(modules, module)
		} else {
			results[name] = fmt.Errorf("module not found: %s", name)
		}
	}

	// 排序模块并逆序
	sorted, err := m.discovery.SortModulesByDependencies(modules)
	if err != nil {
		// 如果排序失败，按原顺序停用
		for _, name := range names {
			if results[name] == nil {
				results[name] = m.DeactivateModule(name)
			}
		}
		return results
	}

	// 按依赖关系逆序停用
	for i := len(sorted) - 1; i >= 0; i-- {
		module := sorted[i]
		results[module.Name] = m.DeactivateModule(module.Name)
	}

	return results
}

// GetActiveModules 获取所有激活的模块（向后兼容，只返回全局模块）
func (m *ModuleManager) GetActiveModules() []string {
	modules := m.discovery.GetActiveModules()
	names := make([]string, len(modules))

	for i, module := range modules {
		names[i] = module.Name
	}

	return names
}

// GetActiveModulesForSite 获取指定站点的所有激活模块
func (m *ModuleManager) GetActiveModulesForSite(ctx context.Context) []string {
	siteID, ok := database.SiteIDFrom(ctx)
	if !ok {
		m.logger.Error("Site ID not found in context")
		return []string{}
	}

	modules := m.discovery.GetActiveModulesForSite(siteID)
	names := make([]string, len(modules))

	for i, module := range modules {
		names[i] = module.Name
	}

	return names
}

// GetEnabledModules 获取所有启用的模块（向后兼容，只返回全局模块）
func (m *ModuleManager) GetEnabledModules() []string {
	modules := m.discovery.GetEnabledModules()
	names := make([]string, len(modules))

	for i, module := range modules {
		names[i] = module.Name
	}

	return names
}

// GetEnabledModulesForSite 获取指定站点的所有启用模块
func (m *ModuleManager) GetEnabledModulesForSite(ctx context.Context) []string {
	siteID, ok := database.SiteIDFrom(ctx)
	if !ok {
		m.logger.Error("Site ID not found in context")
		return []string{}
	}

	modules := m.discovery.GetEnabledModulesForSite(siteID)
	names := make([]string, len(modules))

	for i, module := range modules {
		names[i] = module.Name
	}

	return names
}

// ValidateModuleDependencies 验证模块依赖关系
func (m *ModuleManager) ValidateModuleDependencies() error {
	return m.discovery.ValidateModuleDependencies()
}

// AddChangeCallback 添加模块变化回调
func (m *ModuleManager) AddChangeCallback(callback ModuleChangeCallback) {
	m.discovery.AddChangeCallback(callback)
}

// RefreshModules 刷新模块列表
func (m *ModuleManager) RefreshModules() error {
	_, err := m.discovery.DiscoverModules()
	return err
}

// RegisterGlobalModule 注册全局模块（所有租户共享）
func (m *ModuleManager) RegisterGlobalModule(recipe *ModuleRecipe) {
	m.logger.Info("Registering global module",
		zap.String("name", recipe.Name),
		zap.String("version", recipe.Version),
	)
	m.discovery.RegisterGlobalModule(recipe)
}

// RegisterTenantModule 为指定站点注册租户模块
func (m *ModuleManager) RegisterTenantModule(ctx context.Context, recipe *ModuleRecipe) error {
	siteID, ok := database.SiteIDFrom(ctx)
	if !ok {
		return fmt.Errorf("site ID not found in context")
	}

	m.logger.Info("Registering tenant module",
		zap.Uint("site_id", siteID),
		zap.String("name", recipe.Name),
		zap.String("version", recipe.Version),
	)
	m.discovery.RegisterTenantModule(siteID, recipe)
	return nil
}

// RegisterTenantModuleForSite 为指定站点ID注册租户模块
func (m *ModuleManager) RegisterTenantModuleForSite(siteID uint, recipe *ModuleRecipe) {
	m.logger.Info("Registering tenant module for site",
		zap.Uint("site_id", siteID),
		zap.String("name", recipe.Name),
		zap.String("version", recipe.Version),
	)
	m.discovery.RegisterTenantModule(siteID, recipe)
}

// ActivateModuleLicense 激活模块许可证
func (m *ModuleManager) ActivateModuleLicense(name, licenseKey string) error {
	m.logger.Info("Activating module license",
		zap.String("name", name),
		zap.String("license_key", licenseKey[:min(8, len(licenseKey))]+"..."),
	)
	return m.discovery.ActivateModuleLicense(name, licenseKey)
}

// DeactivateModuleLicense 停用模块许可证
func (m *ModuleManager) DeactivateModuleLicense(name string) error {
	m.logger.Info("Deactivating module license", zap.String("name", name))
	return m.discovery.DeactivateModuleLicense(name)
}

// GetLicensedModules 获取需要许可证的模块
func (m *ModuleManager) GetLicensedModules() []string {
	modules := m.discovery.GetLicensedModules()
	names := make([]string, len(modules))

	for i, module := range modules {
		names[i] = module.Name
	}

	return names
}

// GetActivatedModules 获取已激活许可证的模块
func (m *ModuleManager) GetActivatedModules() []string {
	modules := m.discovery.GetActivatedModules()
	names := make([]string, len(modules))

	for i, module := range modules {
		names[i] = module.Name
	}

	return names
}

// GetRunnableModules 获取可运行的模块（向后兼容，只返回全局模块）
func (m *ModuleManager) GetRunnableModules() []string {
	modules := m.discovery.GetRunnableModules()
	names := make([]string, len(modules))

	for i, module := range modules {
		names[i] = module.Name
	}

	return names
}

// GetRunnableModulesForSite 获取指定站点的可运行模块
func (m *ModuleManager) GetRunnableModulesForSite(ctx context.Context) []string {
	siteID, ok := database.SiteIDFrom(ctx)
	if !ok {
		m.logger.Error("Site ID not found in context")
		return []string{}
	}

	modules := m.discovery.GetRunnableModulesForSite(siteID)
	names := make([]string, len(modules))

	for i, module := range modules {
		names[i] = module.Name
	}

	return names
}

// IsModuleLicenseRequired 检查模块是否需要许可证
func (m *ModuleManager) IsModuleLicenseRequired(name string) (bool, error) {
	module, exists := m.discovery.GetModule(name)
	if !exists {
		return false, fmt.Errorf("module not found: %s", name)
	}
	return module.RequiresActivation(), nil
}

// IsModuleLicenseActivated 检查模块许可证是否已激活
func (m *ModuleManager) IsModuleLicenseActivated(name string) (bool, error) {
	module, exists := m.discovery.GetModule(name)
	if !exists {
		return false, fmt.Errorf("module not found: %s", name)
	}
	return module.IsLicenseActivated(), nil
}

// CanModuleRun 检查模块是否可以运行
func (m *ModuleManager) CanModuleRun(name string) (bool, error) {
	module, exists := m.discovery.GetModule(name)
	if !exists {
		return false, fmt.Errorf("module not found: %s", name)
	}
	return module.CanRun(), nil
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
