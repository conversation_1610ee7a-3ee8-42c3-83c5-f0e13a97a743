/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/port/http/middleware/SiteAccess.go
 * @Description: Middleware to enforce site access control for administrators.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package middleware

import (
	"gacms/internal/infrastructure/auth"
	"gacms/internal/infrastructure/database"
	"gacms/internal/modules/admin/domain/contract"
	"gacms/internal/port/http/response"
	"net/http"

	"github.com/gin-gonic/gin"
)

// NewSiteAccess creates a middleware that checks if an authenticated admin
// has the permission to access the site specified in the context.
func NewSiteAccess(checker contract.SiteAccessChecker) gin.HandlerFunc {
	return func(c *gin.Context) {
		// This middleware should run AFTER the SiteResolver and Auth middlewares.

		// 1. Get SiteID from context (set by SiteResolver).
		siteID, exists := database.SiteIDFrom(c.Request.Context())
		if !exists {
			// This can happen if the request is for a global resource (no domain match)
			// or if SiteResolver is not used on this route. We allow it to proceed.
			c.Next()
			return
		}

		// 2. Get AdminID from context (set by Auth middleware).
		adminID, exists := auth.UserIDFrom(c.Request.Context())
		if !exists {
			response.Fail(c, http.StatusUnauthorized, "Authentication required to access this site.")
			c.Abort()
			return
		}

		// 3. Check authorization.
		isAuthorized, err := checker.IsAdminAuthorizedForSite(c.Request.Context(), adminID, siteID)
		if err != nil {
			response.Fail(c, http.StatusInternalServerError, "Failed to check site authorization.")
			c.Abort()
			return
		}

		if !isAuthorized {
			response.Fail(c, http.StatusForbidden, "You do not have permission to manage this site.")
			c.Abort()
			return
		}

		// 4. If authorized, proceed.
		c.Next()
	}
} 