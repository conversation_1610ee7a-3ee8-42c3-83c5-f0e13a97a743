/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/infrastructure/log/ZapAdapter.go
 * @Description: Zap implementation of the Logger contract.
 *
 * © 2025 GACMS. All rights reserved.
 */

package log

import (
	"fmt"
	"gacms/pkg/contract"
	"os"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// ZapAdapter is a logger that uses the Zap logging library.
// It implements the contract.Logger interface.
type ZapAdapter struct {
	logger *zap.Logger
}

// NewZapAdapter creates a new ZapAdapter.
func NewZapAdapter(config contract.Config) (contract.Logger, error) {
	logLevel := config.GetString("log.level")
	if logLevel == "" {
		logLevel = "info" // Default
	}

	var level zapcore.Level
	if err := level.UnmarshalText([]byte(logLevel)); err != nil {
		return nil, fmt.Errorf("invalid log level: %s", logLevel)
	}

	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "T",
		LevelKey:       "L",
		NameKey:        "N",
		CallerKey:      "C",
		MessageKey:     "M",
		StacktraceKey:  "S",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.StringDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	encoder := zapcore.NewJSONEncoder(encoderConfig)
	writer := zapcore.AddSync(os.Stdout)
	core := zapcore.NewCore(encoder, writer, level)
	logger := zap.New(core, zap.AddCaller())

	logger.Info("Zap logger initialized", zap.String("level", logLevel))

	return &ZapAdapter{logger: logger}, nil
}

// Info logs a message at Info level.
func (za *ZapAdapter) Info(msg string, fields ...map[string]interface{}) {
	zapFields := za.mapsToZapFields(fields...)
	za.logger.Info(msg, zapFields...)
}

// Debug logs a message at Debug level.
func (za *ZapAdapter) Debug(msg string, fields ...map[string]interface{}) {
	zapFields := za.mapsToZapFields(fields...)
	za.logger.Debug(msg, zapFields...)
}

// Warn logs a message at Warn level.
func (za *ZapAdapter) Warn(msg string, fields ...map[string]interface{}) {
	zapFields := za.mapsToZapFields(fields...)
	za.logger.Warn(msg, zapFields...)
}

// Error logs a message at Error level.
func (za *ZapAdapter) Error(msg string, err error, fields ...map[string]interface{}) {
	zapFields := za.mapsToZapFields(fields...)
	zapFields = append(zapFields, zap.Error(err))
	za.logger.Error(msg, zapFields...)
}

// Fatal logs a message at Fatal level and then exits.
func (za *ZapAdapter) Fatal(msg string, err error, fields ...map[string]interface{}) {
	zapFields := za.mapsToZapFields(fields...)
	zapFields = append(zapFields, zap.Error(err))
	za.logger.Fatal(msg, zapFields...)
}

// GetInternalLogger returns the underlying *zap.Logger instance.
func (za *ZapAdapter) GetInternalLogger() interface{} {
	return za.logger
}

// mapsToZapFields converts a slice of map[string]interface{} to a slice of zap.Field.
func (za *ZapAdapter) mapsToZapFields(fields ...map[string]interface{}) []zap.Field {
	if len(fields) == 0 {
		return nil
	}
	
	capacity := 0
	for _, m := range fields {
		capacity += len(m)
	}
	zapFields := make([]zap.Field, 0, capacity)

	for _, fieldMap := range fields {
		for k, v := range fieldMap {
			zapFields = append(zapFields, zap.Any(k, v))
		}
	}
	return zapFields
}