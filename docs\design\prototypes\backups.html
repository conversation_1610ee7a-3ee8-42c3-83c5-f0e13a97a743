<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 系统备份</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        /* 动画效果 */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .animate-pulse {
            animation: pulse 2s infinite;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">系统备份</h2>
                    <button id="createBackup" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                        <span class="relative flex items-center">
                            <i class="fas fa-plus mr-2"></i> 创建备份
                        </span>
                    </button>
                </div>
                <div class="mt-4">
                    <p class="text-gray-400">系统备份允许您定期备份和恢复网站数据、文件和数据库内容。建议至少每周进行一次完整备份，并定期下载备份文件保存到外部存储。</p>
                </div>
            </div>
            
            <!-- 备份概览 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- 备份总数 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center gap-4">
                        <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center">
                            <i class="fas fa-database text-blue-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400">备份总数</h3>
                            <p class="text-2xl font-bold">12</p>
                        </div>
                    </div>
                </div>

                <!-- 备份空间 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center gap-4">
                        <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center">
                            <i class="fas fa-hdd text-green-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400">备份空间</h3>
                            <p class="text-2xl font-bold">2.5GB</p>
                        </div>
                    </div>
                </div>

                <!-- 最近备份 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center gap-4">
                        <div class="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center">
                            <i class="fas fa-clock text-yellow-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400">最近备份</h3>
                            <p class="text-lg">2025-06-15</p>
                        </div>
                    </div>
                </div>

                <!-- 自动备份 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center gap-4">
                        <div class="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center">
                            <i class="fas fa-sync-alt text-purple-400 text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-1">
                                <h3 class="text-gray-400">自动备份</h3>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer" checked>
                                    <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
                                </label>
                            </div>
                            <p class="text-sm text-gray-400">每日凌晨2点</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 主要内容区 -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 左侧备份列表 -->
                <div class="lg:col-span-2">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl overflow-hidden">
                        <!-- 备份列表 -->
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="text-left border-b border-gray-700">
                                        <th class="px-6 py-4 text-sm font-semibold">备份名称</th>
                                        <th class="px-6 py-4 text-sm font-semibold">大小</th>
                                        <th class="px-6 py-4 text-sm font-semibold">创建时间</th>
                                        <th class="px-6 py-4 text-sm font-semibold">状态</th>
                                        <th class="px-6 py-4 text-sm font-semibold">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 正在进行的备份 -->
                                    <tr class="border-b border-gray-700 animate-pulse hover:bg-gray-800/20">
                                        <td class="px-6 py-4">
                                            <div class="flex items-center gap-3">
                                                <i class="fas fa-sync-alt text-blue-500 fa-spin"></i>
                                                <span>backup_20250615_153000</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4">计算中...</td>
                                        <td class="px-6 py-4">2025-06-15 15:30:00</td>
                                        <td class="px-6 py-4">
                                            <span class="px-2 py-1 bg-blue-500 text-white text-xs rounded-full">备份中</span>
                                        </td>
                                        <td class="px-6 py-4">
                                            <button class="text-red-500 hover:text-red-400">
                                                <i class="fas fa-times-circle mr-1"></i>取消
                                            </button>
                                        </td>
                                    </tr>

                                    <!-- 已完成的备份 -->
                                    <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                        <td class="px-6 py-4">
                                            <div class="flex items-center gap-3">
                                                <i class="fas fa-check-circle text-green-500"></i>
                                                <span>backup_20250615_020000</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4">256MB</td>
                                        <td class="px-6 py-4">2025-06-15 02:00:00</td>
                                        <td class="px-6 py-4">
                                            <span class="px-2 py-1 bg-green-500 text-white text-xs rounded-full">已完成</span>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="flex items-center gap-3">
                                                <button class="text-blue-500 hover:text-blue-400">
                                                    <i class="fas fa-undo-alt"></i>
                                                </button>
                                                <button class="text-blue-500 hover:text-blue-400">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                                <button class="text-red-500 hover:text-red-400">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- 失败的备份 -->
                                    <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                        <td class="px-6 py-4">
                                            <div class="flex items-center gap-3">
                                                <i class="fas fa-times-circle text-red-500"></i>
                                                <span>backup_20250614_020000</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4">--</td>
                                        <td class="px-6 py-4">2025-06-14 02:00:00</td>
                                        <td class="px-6 py-4">
                                            <span class="px-2 py-1 bg-red-500 text-white text-xs rounded-full">失败</span>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="flex items-center gap-3">
                                                <button class="text-blue-500 hover:text-blue-400">
                                                    <i class="fas fa-redo"></i>
                                                </button>
                                                <button class="text-red-500 hover:text-red-400">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    
                                    <!-- 更多备份项 -->
                                    <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                        <td class="px-6 py-4">
                                            <div class="flex items-center gap-3">
                                                <i class="fas fa-check-circle text-green-500"></i>
                                                <span>backup_20250613_020000</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4">252MB</td>
                                        <td class="px-6 py-4">2025-06-13 02:00:00</td>
                                        <td class="px-6 py-4">
                                            <span class="px-2 py-1 bg-green-500 text-white text-xs rounded-full">已完成</span>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="flex items-center gap-3">
                                                <button class="text-blue-500 hover:text-blue-400">
                                                    <i class="fas fa-undo-alt"></i>
                                                </button>
                                                <button class="text-blue-500 hover:text-blue-400">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                                <button class="text-red-500 hover:text-red-400">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    
                                    <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                        <td class="px-6 py-4">
                                            <div class="flex items-center gap-3">
                                                <i class="fas fa-check-circle text-green-500"></i>
                                                <span>backup_20250612_020000</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4">248MB</td>
                                        <td class="px-6 py-4">2025-06-12 02:00:00</td>
                                        <td class="px-6 py-4">
                                            <span class="px-2 py-1 bg-green-500 text-white text-xs rounded-full">已完成</span>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="flex items-center gap-3">
                                                <button class="text-blue-500 hover:text-blue-400">
                                                    <i class="fas fa-undo-alt"></i>
                                                </button>
                                                <button class="text-blue-500 hover:text-blue-400">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                                <button class="text-red-500 hover:text-red-400">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <div class="flex justify-between items-center p-6 border-t border-gray-700">
                            <div class="text-gray-400">显示 1-10 条，共 12 条</div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded">1</button>
                                <button class="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded">2</button>
                                <button class="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧备份设置 -->
                <div>
                    <!-- 创建备份选项 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4">创建备份</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                                    <span class="ml-2">数据库</span>
                                </label>
                                <p class="text-sm text-gray-400 ml-6 mt-1">备份所有数据库表和数据</p>
                            </div>
                            
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                                    <span class="ml-2">上传文件</span>
                                </label>
                                <p class="text-sm text-gray-400 ml-6 mt-1">包括所有上传的图片和文档</p>
                            </div>
                            
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                                    <span class="ml-2">系统设置</span>
                                </label>
                                <p class="text-sm text-gray-400 ml-6 mt-1">包括所有系统配置和设置选项</p>
                            </div>
                            
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500">
                                    <span class="ml-2">插件数据</span>
                                </label>
                                <p class="text-sm text-gray-400 ml-6 mt-1">第三方插件的数据和设置</p>
                            </div>
                            
                            <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg transition-colors flex items-center justify-center">
                                <i class="fas fa-download mr-2"></i> 立即创建备份
                            </button>
                        </div>
                    </div>
                    
                    <!-- 备份设置 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <h3 class="text-lg font-semibold mb-4">备份设置</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label class="block text-gray-300 mb-2">自动备份频率:</label>
                                <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option>每天</option>
                                    <option>每周</option>
                                    <option>每两周</option>
                                    <option>每月</option>
                                    <option>从不</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">备份时间:</label>
                                <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option>凌晨 02:00</option>
                                    <option>凌晨 03:00</option>
                                    <option>凌晨 04:00</option>
                                    <option>凌晨 05:00</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">保留备份数量:</label>
                                <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option>5个备份</option>
                                    <option>10个备份</option>
                                    <option>15个备份</option>
                                    <option>20个备份</option>
                                    <option>全部保留</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">远程存储:</label>
                                <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option>本地存储</option>
                                    <option>云存储 - 阿里云OSS</option>
                                    <option>云存储 - 腾讯COS</option>
                                    <option>FTP服务器</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                                    <span class="ml-2">备份完成后发送邮件通知</span>
                                </label>
                            </div>
                            
                            <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg transition-colors mt-4">
                                保存设置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <!-- 创建备份弹窗 -->
    <div id="backupModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-gray-800 rounded-xl p-6 w-full max-w-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">创建新备份</h3>
                <button class="text-gray-400 hover:text-white" id="closeModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="space-y-4 mb-6">
                <div>
                    <label class="block text-gray-300 mb-2">备份名称:</label>
                    <input type="text" value="backup_manual_20250615" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                
                <div>
                    <label class="block text-gray-300 mb-2">备份内容:</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                            <span class="ml-2">数据库</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                            <span class="ml-2">上传文件</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                            <span class="ml-2">系统设置</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500">
                            <span class="ml-2">插件数据</span>
                        </label>
                    </div>
                </div>
                
                <div>
                    <label class="block text-gray-300 mb-2">备份说明:</label>
                    <textarea class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none" rows="3" placeholder="添加备份说明信息..."></textarea>
                </div>
            </div>
            
            <div class="flex justify-end space-x-3">
                <button class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors" id="cancelBackup">
                    取消
                </button>
                <button class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors" id="startBackup">
                    开始备份
                </button>
            </div>
        </div>
    </div>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 备份弹窗相关功能
            const backupModal = document.getElementById('backupModal');
            const createBackupBtn = document.getElementById('createBackup');
            const closeModalBtn = document.getElementById('closeModal');
            const cancelBackupBtn = document.getElementById('cancelBackup');
            const startBackupBtn = document.getElementById('startBackup');
            
            // 打开弹窗
            if(createBackupBtn) {
                createBackupBtn.addEventListener('click', function() {
                    backupModal.classList.remove('hidden');
                });
            }
            
            // 关闭弹窗
            if(closeModalBtn) {
                closeModalBtn.addEventListener('click', function() {
                    backupModal.classList.add('hidden');
                });
            }
            
            // 取消按钮
            if(cancelBackupBtn) {
                cancelBackupBtn.addEventListener('click', function() {
                    backupModal.classList.add('hidden');
                });
            }
            
            // 开始备份
            if(startBackupBtn) {
                startBackupBtn.addEventListener('click', function() {
                    backupModal.classList.add('hidden');
                    // 实际应用中这里会调用API进行备份
                    alert('备份已开始，您可以在列表中查看进度。');
                });
            }
        });
    </script>
</body>
</html> 