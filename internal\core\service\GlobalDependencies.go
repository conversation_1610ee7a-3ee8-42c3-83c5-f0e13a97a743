/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: Clion Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/GlobalDependencies.go
 * @Description: 全局依赖注入容器，为模块工厂提供核心服务
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"fmt"

	"gacms/internal/infrastructure/database"
	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// GlobalDependencies 全局依赖容器
// 包含所有模块工厂需要的核心服务
type GlobalDependencies struct {
	// 基础设施
	Logger *zap.Logger
	DB     *gorm.DB
	Config contract.Config // 配置接口

	// 核心服务
	EventManager contract.EventManager
	
	// 模块管理
	ModuleDiscovery *ModuleDiscovery
	ModuleManager   *ModuleManager
	
	// 许可证管理
	LicenseManager contract.LicenseManager
	LicenseStore   contract.LicenseStore
	
	// 路由管理
	Router contract.Router
}

// GlobalDependenciesParams fx依赖注入参数
type GlobalDependenciesParams struct {
	fx.In

	Logger         *zap.Logger
	DB             *gorm.DB
	Config         contract.Config
	EventManager   contract.EventManager
	ModuleDiscovery *ModuleDiscovery
	ModuleManager  *ModuleManager
	LicenseManager contract.LicenseManager
	LicenseStore   contract.LicenseStore
	Router         contract.Router `optional:"true"`
}

// NewGlobalDependencies 创建全局依赖容器
func NewGlobalDependencies(params GlobalDependenciesParams) *GlobalDependencies {
	return &GlobalDependencies{
		Logger:          params.Logger,
		DB:              params.DB,
		Config:          params.Config,
		EventManager:    params.EventManager,
		ModuleDiscovery: params.ModuleDiscovery,
		ModuleManager:   params.ModuleManager,
		LicenseManager:  params.LicenseManager,
		LicenseStore:    params.LicenseStore,
		Router:          params.Router,
	}
}

// TenantDependencies 租户特定依赖容器
// 继承全局依赖，添加租户上下文
type TenantDependencies struct {
	*GlobalDependencies
	
	// 租户信息
	SiteID        uint
	TenantContext context.Context
}

// NewTenantDependencies 创建租户依赖容器
func NewTenantDependencies(global *GlobalDependencies, siteID uint) *TenantDependencies {
	return &TenantDependencies{
		GlobalDependencies: global,
		SiteID:             siteID,
		TenantContext:      database.WithSiteID(context.Background(), siteID),
	}
}

// GetTenantContext 获取租户上下文
func (d *TenantDependencies) GetTenantContext() context.Context {
	return d.TenantContext
}

// IsGlobal 判断是否为全局依赖
func (d *TenantDependencies) IsGlobal() bool {
	return d.SiteID == 0
}

// ModuleInstance 模块实例
// 包含模块的所有服务和控制器
type ModuleInstance struct {
	// 基本信息
	Name         string
	Version      string
	SiteID       uint
	IsGlobal     bool
	LoadedAt     int64
	
	// 服务实例
	Services    map[string]interface{}
	Controllers map[string]interface{}
	
	// 路由信息
	Routes []RouteInfo
	
	// 生命周期
	Started bool
	Error   error
}

// RouteInfo 路由信息
type RouteInfo struct {
	Pattern     string
	Method      string
	Handler     interface{}
	Controller  string
	Action      string
	Middleware  []string
}

// GetService 获取服务实例
func (m *ModuleInstance) GetService(serviceName string) (interface{}, bool) {
	service, exists := m.Services[serviceName]
	return service, exists
}

// GetController 获取控制器实例
func (m *ModuleInstance) GetController(controllerName string) (interface{}, bool) {
	controller, exists := m.Controllers[controllerName]
	return controller, exists
}

// AddService 添加服务实例
func (m *ModuleInstance) AddService(serviceName string, service interface{}) {
	if m.Services == nil {
		m.Services = make(map[string]interface{})
	}
	m.Services[serviceName] = service
}

// AddController 添加控制器实例
func (m *ModuleInstance) AddController(controllerName string, controller interface{}) {
	if m.Controllers == nil {
		m.Controllers = make(map[string]interface{})
	}
	m.Controllers[controllerName] = controller
}

// AddRoute 添加路由信息
func (m *ModuleInstance) AddRoute(route RouteInfo) {
	m.Routes = append(m.Routes, route)
}

// Start 启动模块实例
func (m *ModuleInstance) Start() error {
	if m.Started {
		return nil
	}
	
	// TODO: 实现模块启动逻辑
	m.Started = true
	return nil
}

// Stop 停止模块实例
func (m *ModuleInstance) Stop() error {
	if !m.Started {
		return nil
	}
	
	// TODO: 实现模块停止逻辑
	m.Started = false
	return nil
}

// ModuleConfig 模块配置
// 包含模块的元信息和工厂函数
type ModuleConfig struct {
	// 基本信息
	Name         string
	Version      string
	Description  string
	Author       string
	Dependencies []string

	// 模块类型和分层
	Type         ModuleType // 模块类型：core/optional/third_party
	LoadPriority int        // 加载优先级，数字越小优先级越高

	// 作用域
	IsGlobal     bool
	SupportsTenant bool

	// 工厂函数
	GlobalFactory func(deps *GlobalDependencies) (*ModuleInstance, error)
	TenantFactory func(deps *TenantDependencies) (*ModuleInstance, error)

	// 路径信息
	Path         string
	ConfigPath   string
}

// ModuleType 模块类型（与model包保持一致）
type ModuleType string

const (
	ModuleTypeCore     ModuleType = "core"     // 核心模块，永远启用
	ModuleTypeOptional ModuleType = "optional" // 可选模块，可启用/停用
	ModuleTypeVendors  ModuleType = "vendors"  // 第三方供应商模块
)

// 业务方法

// IsCore 判断是否为核心模块
func (m *ModuleConfig) IsCore() bool {
	return m.Type == ModuleTypeCore
}

// IsOptional 判断是否为可选模块
func (m *ModuleConfig) IsOptional() bool {
	return m.Type == ModuleTypeOptional
}

// IsVendors 判断是否为第三方供应商模块
func (m *ModuleConfig) IsVendors() bool {
	return m.Type == ModuleTypeVendors
}

// CanDisable 判断是否可以禁用
func (m *ModuleConfig) CanDisable() bool {
	return !m.IsCore()
}

// CanCreateForTenant 判断是否可以为租户创建实例
func (c *ModuleConfig) CanCreateForTenant(siteID uint) bool {
	if c.IsGlobal {
		return true // 全局模块对所有租户可用
	}
	return c.SupportsTenant && siteID > 0
}

// GetFactory 获取适当的工厂函数
func (c *ModuleConfig) GetFactory(siteID uint) interface{} {
	if siteID > 0 && c.TenantFactory != nil {
		return c.TenantFactory
	}
	return c.GlobalFactory
}

// Validate 验证模块配置
func (c *ModuleConfig) Validate() error {
	if c.Name == "" {
		return fmt.Errorf("module name is required")
	}
	
	if c.GlobalFactory == nil && c.TenantFactory == nil {
		return fmt.Errorf("at least one factory function is required")
	}
	
	return nil
}
