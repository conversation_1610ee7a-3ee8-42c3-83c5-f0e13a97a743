//go:build business

/*
Author: <PERSON><PERSON> Nieh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package service

import "gacms/pkg/contract"

// BusinessEditionCompiler 商业版编译器
type BusinessEditionCompiler struct {
	BaseEditionCompiler
}

// 编译时返回商业版管理器
func getCompiledEditionManager() CompileTimeEditionManager {
	return &BusinessEditionCompiler{
		BaseEditionCompiler: BaseEditionCompiler{
			edition: contract.EditionBusiness,
			features: map[string]bool{
				// 商业版功能（全部编译时包含）
				"basic_content":       true,
				"basic_theme":         true,
				"basic_user":          true,
				"advanced_theme":      true,
				"seo_basic":           true,
				"seo_advanced":        true,
				"workflow":            true,
				"api_access":          true,
				"advanced_user":       true,
				"business_security":   true,
				"custom_development":  true,
			},
			limits: &EditionLimits{
				MaxSites:     -1, // 无限制
				MaxUsers:     -1, // 无限制
				MaxStorage:   -1, // 无限制
				MaxBandwidth: -1, // 无限制
				MaxPages:     -1, // 无限制
				MaxPosts:     -1, // 无限制
			},
		},
	}
}

// BusinessFeatureGuard 商业版功能守卫
func BusinessFeatureGuard(featureName string) bool {
	compiler := GetCompiledEditionManager()
	return compiler.IsFeatureCompiledIn(featureName)
}

// BusinessLimitGuard 商业版限制守卫
func BusinessLimitGuard(limitType string, currentValue int) bool {
	// 商业版无限制
	return false
}
