# ADR-007: Convention-over-Configuration for Backend API Routing

## Status
Proposed

## Context
The previous routing mechanism required each module to manually register its routes, leading to boilerplate code and tight coupling between the module and the routing layer. As we moved towards a more dynamic, lazy-loading architecture, we needed a routing system that was decoupled, automated, and did not require module instantiation at startup. Several designs were considered, including declarative definitions via module interfaces and metadata structs. These were ultimately deemed overly complex, still requiring manual definitions. A final proposal was made to use a "convention-over-configuration" approach, leveraging reflection to completely automate route generation for standard backend administrative APIs.

## Decision
We will adopt a routing system for the backend admin API (`/api/admin/*`) based on convention and reflection.

1.  **Convention-Based Route Generation**: Routes will be automatically generated by a central `Router.Manager` at startup. This manager will use reflection to scan the methods of designated "Routable Controllers".
2.  **Naming Convention**: A strict naming convention for controller methods will map directly to HTTP methods and URL paths.
    - `Get...` maps to `GET`
    - `Post...` maps to `POST`
    - `Put...` maps to `PUT` with a `/:id` suffix.
    - `Patch...` maps to `PATCH` with a `/:id` suffix.
    - `Delete...` maps to `DELETE` with a `/:id` suffix.
    - The resource path will be a `snake_case` version of the text following the prefix (e.g., `GetUserByID` -> `/user_by_id/:id`).
3.  **Controller Discovery**: Modules will designate their controllers as "routable" by providing their `reflect.Type` to a specific `fx.Group` ("routable_controllers") in the main DI container. The `Router.Manager` will collect these types from the group.
4.  **Lazy-Loading Handlers**: While routes are registered at startup, their handlers will be lazy-loading closures. A request to a generated route will trigger the closure, which will:
    a. Check the module's runtime status (enabled/disabled) via the `ModuleManager`.
    b. If active, use the `ModuleManager` to lazy-load the required controller instance (on-demand DI sub-container creation).
    c. Execute the target controller method using reflection.

## Alternatives Considered
### Manual Route Registration (Old System)
- **Pros**: Explicit and easy to understand for simple cases.
- **Cons**: Verbose, boilerplate-heavy, coupled modules to the router, incompatible with lazy-loading.
- **Why Not**: Does not meet the requirements of our dynamic, decoupled architecture.

### Declarative Metadata (Intermediate Proposal)
- **Pros**: Decoupled route definitions from controller logic.
- **Cons**: Still required significant manual effort to define all routes in metadata structs (`RouteDefinition`). The architecture became complex with multiple services (`ModuleManager`, `ModuleRegistry`) whose responsibilities were unclear.
- **Why Not**: Overly complex and still required manual work, which could be fully automated.

## Consequences

### Positive
- **Drastically Reduced Boilerplate**: Module developers no longer write any routing code.
- **Ultimate Decoupling**: The routing system is completely independent of module logic.
- **Enforced Consistency**: All backend APIs will follow a uniform, predictable URL structure.
- **Full Lazy-Loading Support**: Achieves our goal of not instantiating any module components until a request is actually served.

### Negative
- **Reduced Flexibility**: Custom or non-standard URLs (e.g., `/posts/archive_all`) cannot be created through this mechanism. This is an acceptable trade-off for the administrative backend.
- **Reliance on Reflection**: Heavy use of reflection can make the system slightly harder to debug and can have a minor performance impact at startup (though not at runtime).
- **Strict Naming Required**: Developers must adhere strictly to the method naming conventions.

### Neutral
- This decision only applies to the backend administrative API. A separate, more flexible routing system (the "Frontend Routing Center") will be required to handle custom, user-facing URLs, multi-site domains, and sub-domain bindings. The two systems are designed to be independent and compatible.

## Implementation Notes
- **Core Implementers**: `internal/port/http/router/Manager.go` will contain the route generator. `internal/core/service/ModuleManager.go` will provide lazy-loading capabilities.
- **Future Evolution**: The route generation rules, currently hard-coded, can be moved to a database and made configurable in the admin panel in a future iteration. This would allow for advanced features like URL rewriting and full static site generation support.
- **Review Date**: After the first set of modules are successfully integrated using this new system. 