<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 插件管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: { 500: '#007bff', 600: '#0069d9' },
                        secondary: { 500: '#00c6ff' },
                        dark: { 800: '#1F2937', 900: '#1A1A1A' }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>

        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">插件管理</h2>
                    <div class="flex items-center space-x-3">
                        <button class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-2 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30">
                            <i class="fas fa-plus mr-2"></i>上传插件
                        </button>
                        <button class="flex items-center justify-center bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-all hover:bg-gray-600">
                            <i class="fas fa-store mr-2"></i>访问插件市场
                        </button>
                    </div>
                </div>
            </div>

            <!-- 插件列表 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                 <div class="overflow-x-auto">
                    <table class="w-full text-sm text-left text-gray-400">
                        <thead class="text-xs text-gray-300 uppercase bg-gray-700/20">
                            <tr>
                                <th scope="col" class="p-4"><input type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-600 ring-offset-gray-800 focus:ring-2"></th>
                                <th scope="col" class="px-6 py-3">插件名称</th>
                                <th scope="col" class="px-6 py-3">描述</th>
                                <th scope="col" class="px-6 py-3">状态</th>
                                <th scope="col" class="px-6 py-3">版本</th>
                                <th scope="col" class="px-6 py-3">作者</th>
                                <th scope="col" class="px-6 py-3">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="w-4 p-4"><input type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-600 ring-offset-gray-800 focus:ring-2"></td>
                                <th scope="row" class="px-6 py-4 font-medium text-white whitespace-nowrap">SEO优化助手</th>
                                <td class="px-6 py-4">自动生成Sitemap，优化页面Meta标签。</td>
                                <td class="px-6 py-4"><span class="text-green-400">已激活</span></td>
                                <td class="px-6 py-4">1.5.2</td>
                                <td class="px-6 py-4"><a href="#" class="text-blue-400 hover:underline">GACMS官方</a></td>
                                <td class="px-6 py-4 flex items-center space-x-3"><a href="#" class="font-medium text-blue-500 hover:underline">设置</a><a href="#" class="font-medium text-yellow-500 hover:underline">停用</a></td>
                            </tr>
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="w-4 p-4"><input type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-600 ring-offset-gray-800 focus:ring-2"></td>
                                <th scope="row" class="px-6 py-4 font-medium text-white whitespace-nowrap">社交分享</th>
                                <td class="px-6 py-4">在文章页添加社交媒体分享按钮。</td>
                                <td class="px-6 py-4"><span class="text-gray-400">未激活</span></td>
                                <td class="px-6 py-4">1.2.0</td>
                                <td class="px-6 py-4"><a href="#" class="text-blue-400 hover:underline">社区开发者</a></td>
                                <td class="px-6 py-4 flex items-center space-x-3"><a href="#" class="font-medium text-green-500 hover:underline">激活</a><a href="#" class="font-medium text-red-500 hover:underline">删除</a></td>
                            </tr>
                             <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="w-4 p-4"><input type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-600 ring-offset-gray-800 focus:ring-2"></td>
                                <th scope="row" class="px-6 py-4 font-medium text-white whitespace-nowrap">图片水印</th>
                                <td class="px-6 py-4">自动为上传的图片添加文字或图片水印。</td>
                                <td class="px-6 py-4"><span class="text-green-400">已激活</span></td>
                                <td class="px-6 py-4">2.0.1</td>
                                <td class="px-6 py-4"><a href="#" class="text-blue-400 hover:underline">GACMS官方</a></td>
                                <td class="px-6 py-4 flex items-center space-x-3"><a href="#" class="font-medium text-blue-500 hover:underline">设置</a><a href="#" class="font-medium text-yellow-500 hover:underline">停用</a></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>

        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
</body>
</html> 