<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 问卷调查管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .survey-card {
            transition: all 0.3s ease;
        }
        
        .survey-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
        }
        
        .survey-item {
            transition: all 0.3s ease;
        }
        
        .survey-item:hover {
            background-color: rgba(75, 85, 99, 0.1);
        }
        
        /* 进度条样式 */
        .progress-bar {
            height: 6px;
            border-radius: 3px;
            overflow: hidden;
        }
        
        /* 问卷预览样式 */
        .question-item {
            border: 1px solid rgba(75, 85, 99, 0.3);
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            transition: all 0.2s ease;
        }
        
        .question-item:hover {
            border-color: rgba(59, 130, 246, 0.5);
        }
        
        .question-handle {
            cursor: move;
            padding: 0.5rem;
            border-radius: 0.25rem;
        }
        
        .question-handle:hover {
            background-color: rgba(75, 85, 99, 0.2);
        }
        
        /* 拖拽样式 */
        .dragging {
            opacity: 0.5;
            border: 2px dashed #3b82f6;
        }
        
        /* 选项样式 */
        .option-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .option-item input[type="radio"],
        .option-item input[type="checkbox"] {
            margin-right: 0.5rem;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="marketing.html" class="hover:text-white">营销</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">问卷调查</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">问卷调查管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="createSurveyBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-plus text-white"></i>
                                </span>
                                创建问卷
                            </span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 问卷数据概览 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <!-- 活跃问卷 -->
                <div class="survey-card bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-clipboard-list text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">活跃问卷</h3>
                            <div class="text-2xl font-bold text-white mt-1">5</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-green-400">
                            <i class="fas fa-arrow-up mr-1"></i>2
                        </span>
                        <span class="text-gray-400 ml-2">较上月</span>
                    </div>
                </div>
                
                <!-- 总回复数 -->
                <div class="survey-card bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-reply-all text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">总回复数</h3>
                            <div class="text-2xl font-bold text-white mt-1">1,248</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-green-400">
                            <i class="fas fa-arrow-up mr-1"></i>12.5%
                        </span>
                        <span class="text-gray-400 ml-2">较上月</span>
                    </div>
                </div>
                
                <!-- 平均完成率 -->
                <div class="survey-card bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-chart-pie text-yellow-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">平均完成率</h3>
                            <div class="text-2xl font-bold text-white mt-1">78.3%</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-green-400">
                            <i class="fas fa-arrow-up mr-1"></i>3.2%
                        </span>
                        <span class="text-gray-400 ml-2">较上周</span>
                    </div>
                </div>
                
                <!-- 平均完成时间 -->
                <div class="survey-card bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-clock text-purple-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">平均完成时间</h3>
                            <div class="text-2xl font-bold text-white mt-1">4分32秒</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-red-400">
                            <i class="fas fa-arrow-up mr-1"></i>12秒
                        </span>
                        <span class="text-gray-400 ml-2">较上月</span>
                    </div>
                </div>
            </div>
            
            <!-- 问卷管理标签页 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="border-b border-gray-700 mb-6">
                    <ul class="flex flex-wrap -mb-px">
                        <li class="mr-2">
                            <a href="#" class="inline-block py-3 px-4 text-white border-b-2 border-blue-500 font-medium">全部问卷</a>
                        </li>
                        <li class="mr-2">
                            <a href="#" class="inline-block py-3 px-4 text-gray-400 hover:text-white border-b-2 border-transparent hover:border-gray-400">活跃问卷</a>
                        </li>
                        <li class="mr-2">
                            <a href="#" class="inline-block py-3 px-4 text-gray-400 hover:text-white border-b-2 border-transparent hover:border-gray-400">草稿</a>
                        </li>
                        <li class="mr-2">
                            <a href="#" class="inline-block py-3 px-4 text-gray-400 hover:text-white border-b-2 border-transparent hover:border-gray-400">已结束</a>
                        </li>
                        <li class="mr-2">
                            <a href="#" class="inline-block py-3 px-4 text-gray-400 hover:text-white border-b-2 border-transparent hover:border-gray-400">问卷模板</a>
                        </li>
                    </ul>
                </div>
                
                <!-- 问卷筛选 -->
                <div class="flex flex-wrap gap-4 mb-6">
                    <div class="relative">
                        <select class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all">所有状态</option>
                            <option value="active">进行中</option>
                            <option value="upcoming">即将开始</option>
                            <option value="ended">已结束</option>
                            <option value="draft">草稿</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </div>
                    </div>
                    
                    <div class="relative">
                        <select class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all">所有类型</option>
                            <option value="customer">客户满意度</option>
                            <option value="product">产品反馈</option>
                            <option value="market">市场调研</option>
                            <option value="employee">员工调查</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </div>
                    </div>
                    
                    <div class="flex-1"></div>
                    
                    <div class="relative">
                        <input type="text" placeholder="搜索问卷..." class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <div class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-400">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 问卷列表 -->
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="py-3 text-left text-gray-400 font-medium">问卷名称</th>
                                <th class="py-3 text-left text-gray-400 font-medium">类型</th>
                                <th class="py-3 text-left text-gray-400 font-medium">状态</th>
                                <th class="py-3 text-left text-gray-400 font-medium">时间范围</th>
                                <th class="py-3 text-left text-gray-400 font-medium">回复数</th>
                                <th class="py-3 text-left text-gray-400 font-medium">完成率</th>
                                <th class="py-3 text-left text-gray-400 font-medium">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 问卷项 1 -->
                            <tr class="survey-item border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded bg-green-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-smile text-green-500"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium">客户满意度调查</div>
                                            <div class="text-gray-400 text-sm">了解客户对我们服务的评价</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-green-500/20 text-green-400">客户满意度</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-blue-500/20 text-blue-400">进行中</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">2025-04-01 ~ 2025-04-30</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-white">432</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <span class="text-white mr-2">82.5%</span>
                                        <div class="w-16 bg-gray-700 rounded-full h-1.5">
                                            <div class="bg-green-500 h-1.5 rounded-full" style="width: 82.5%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-white p-1" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 问卷项 2 -->
                            <tr class="survey-item border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded bg-blue-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-box text-blue-500"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium">新产品功能反馈</div>
                                            <div class="text-gray-400 text-sm">收集用户对新功能的意见</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-blue-500/20 text-blue-400">产品反馈</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-blue-500/20 text-blue-400">进行中</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">2025-04-10 ~ 2025-04-25</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-white">287</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <span class="text-white mr-2">76.2%</span>
                                        <div class="w-16 bg-gray-700 rounded-full h-1.5">
                                            <div class="bg-green-500 h-1.5 rounded-full" style="width: 76.2%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-white p-1" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 问卷项 3 -->
                            <tr class="survey-item border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded bg-purple-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-chart-bar text-purple-500"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium">市场需求调研</div>
                                            <div class="text-gray-400 text-sm">了解目标市场的需求和趋势</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-purple-500/20 text-purple-400">市场调研</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-yellow-500/20 text-yellow-400">即将开始</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">2025-04-20 ~ 2025-05-10</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-400">-</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-400">-</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-white p-1" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 问卷项 4 -->
                            <tr class="survey-item border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded bg-yellow-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-users text-yellow-500"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium">员工满意度调查</div>
                                            <div class="text-gray-400 text-sm">了解员工对工作环境的满意度</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-yellow-500/20 text-yellow-400">员工调查</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-blue-500/20 text-blue-400">进行中</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">2025-04-05 ~ 2025-04-15</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-white">156</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <span class="text-white mr-2">94.8%</span>
                                        <div class="w-16 bg-gray-700 rounded-full h-1.5">
                                            <div class="bg-green-500 h-1.5 rounded-full" style="width: 94.8%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-white p-1" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 问卷项 5 -->
                            <tr class="survey-item border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded bg-red-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-shopping-cart text-red-500"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium">购物体验调查</div>
                                            <div class="text-gray-400 text-sm">了解客户购物过程中的体验</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-red-500/20 text-red-400">客户体验</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-gray-500/20 text-gray-400">已结束</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">2025-03-01 ~ 2025-03-31</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-white">523</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <span class="text-white mr-2">87.2%</span>
                                        <div class="w-16 bg-gray-700 rounded-full h-1.5">
                                            <div class="bg-green-500 h-1.5 rounded-full" style="width: 87.2%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1" title="查看报告">
                                            <i class="fas fa-chart-bar"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-white p-1" title="复制问卷">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div class="flex justify-between items-center mt-6">
                    <div class="text-sm text-gray-400">
                        显示 1-5 条，共 12 条
                    </div>
                    <div class="flex space-x-1">
                        <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="px-3 py-1 bg-blue-500 text-white rounded-md">
                            1
                        </button>
                        <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                            2
                        </button>
                        <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                            3
                        </button>
                        <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 页面底部版权信息 -->
            <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
                © 2025 GACMS 后台管理系统 - 版本 v1.0.0
            </footer>
        </div>
    </main>

    <!-- 创建问卷模态框 -->
    <div id="createSurveyModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-white">创建问卷调查</h3>
                <button id="closeModalBtn" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <!-- 问卷基本信息 -->
            <div class="mb-6 border-b border-gray-700 pb-6">
                <h4 class="text-white font-medium mb-4">基本信息</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-300 mb-2">问卷标题</label>
                        <input type="text" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="输入问卷标题">
                    </div>
                    <div>
                        <label class="block text-gray-300 mb-2">问卷类型</label>
                        <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="customer">客户满意度</option>
                            <option value="product">产品反馈</option>
                            <option value="market">市场调研</option>
                            <option value="employee">员工调查</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                </div>
                <div class="mt-4">
                    <label class="block text-gray-300 mb-2">问卷描述</label>
                    <textarea class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3" placeholder="输入问卷描述"></textarea>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                        <label class="block text-gray-300 mb-2">开始时间</label>
                        <input type="datetime-local" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-gray-300 mb-2">结束时间</label>
                        <input type="datetime-local" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
            </div>
            
            <!-- 问卷设计 -->
            <div class="mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-white font-medium">问卷设计</h4>
                    <div class="flex space-x-2">
                        <button class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded-lg text-sm">
                            <i class="fas fa-plus mr-1"></i> 添加问题
                        </button>
                        <button class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded-lg text-sm">
                            <i class="fas fa-file-import mr-1"></i> 从模板导入
                        </button>
                    </div>
                </div>
                
                <!-- 问题列表 -->
                <div class="space-y-4">
                    <!-- 问题 1 - 单选题 -->
                    <div class="question-item bg-gray-800/30 p-4">
                        <div class="flex justify-between items-start mb-3">
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <span class="bg-blue-500/20 text-blue-400 text-xs px-2 py-1 rounded mr-2">单选题</span>
                                    <h5 class="text-white font-medium">您对我们的产品/服务的整体满意度如何？</h5>
                                </div>
                                <p class="text-gray-400 text-sm">请选择最符合您感受的选项</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="question-handle text-gray-400 hover:text-white">
                                    <i class="fas fa-grip-vertical"></i>
                                </div>
                                <button class="text-gray-400 hover:text-white">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-gray-400 hover:text-red-500">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="mt-3 space-y-2">
                            <div class="option-item">
                                <input type="radio" id="q1_opt1" name="q1" checked disabled>
                                <label for="q1_opt1" class="text-gray-300">非常满意</label>
                            </div>
                            <div class="option-item">
                                <input type="radio" id="q1_opt2" name="q1" disabled>
                                <label for="q1_opt2" class="text-gray-300">满意</label>
                            </div>
                            <div class="option-item">
                                <input type="radio" id="q1_opt3" name="q1" disabled>
                                <label for="q1_opt3" class="text-gray-300">一般</label>
                            </div>
                            <div class="option-item">
                                <input type="radio" id="q1_opt4" name="q1" disabled>
                                <label for="q1_opt4" class="text-gray-300">不满意</label>
                            </div>
                            <div class="option-item">
                                <input type="radio" id="q1_opt5" name="q1" disabled>
                                <label for="q1_opt5" class="text-gray-300">非常不满意</label>
                            </div>
                        </div>
                        <div class="mt-3 flex items-center">
                            <input type="checkbox" id="q1_required" checked>
                            <label for="q1_required" class="text-gray-300 ml-2 text-sm">必答题</label>
                        </div>
                    </div>
                    
                    <!-- 问题 2 - 多选题 -->
                    <div class="question-item bg-gray-800/30 p-4">
                        <div class="flex justify-between items-start mb-3">
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <span class="bg-green-500/20 text-green-400 text-xs px-2 py-1 rounded mr-2">多选题</span>
                                    <h5 class="text-white font-medium">您最喜欢我们产品/服务的哪些方面？</h5>
                                </div>
                                <p class="text-gray-400 text-sm">可多选，最多选择3项</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="question-handle text-gray-400 hover:text-white">
                                    <i class="fas fa-grip-vertical"></i>
                                </div>
                                <button class="text-gray-400 hover:text-white">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-gray-400 hover:text-red-500">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="mt-3 space-y-2">
                            <div class="option-item">
                                <input type="checkbox" id="q2_opt1" name="q2" checked disabled>
                                <label for="q2_opt1" class="text-gray-300">产品质量</label>
                            </div>
                            <div class="option-item">
                                <input type="checkbox" id="q2_opt2" name="q2" checked disabled>
                                <label for="q2_opt2" class="text-gray-300">价格合理</label>
                            </div>
                            <div class="option-item">
                                <input type="checkbox" id="q2_opt3" name="q2" disabled>
                                <label for="q2_opt3" class="text-gray-300">客户服务</label>
                            </div>
                            <div class="option-item">
                                <input type="checkbox" id="q2_opt4" name="q2" disabled>
                                <label for="q2_opt4" class="text-gray-300">使用便捷</label>
                            </div>
                            <div class="option-item">
                                <input type="checkbox" id="q2_opt5" name="q2" disabled>
                                <label for="q2_opt5" class="text-gray-300">设计美观</label>
                            </div>
                            <div class="option-item">
                                <input type="checkbox" id="q2_opt6" name="q2" disabled>
                                <label for="q2_opt6" class="text-gray-300">其他</label>
                            </div>
                        </div>
                        <div class="mt-3 flex items-center">
                            <input type="checkbox" id="q2_required" checked>
                            <label for="q2_required" class="text-gray-300 ml-2 text-sm">必答题</label>
                        </div>
                    </div>
                    
                    <!-- 问题 3 - 文本题 -->
                    <div class="question-item bg-gray-800/30 p-4">
                        <div class="flex justify-between items-start mb-3">
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <span class="bg-purple-500/20 text-purple-400 text-xs px-2 py-1 rounded mr-2">文本题</span>
                                    <h5 class="text-white font-medium">您对我们的产品/服务有什么建议或意见？</h5>
                                </div>
                                <p class="text-gray-400 text-sm">请详细描述您的想法，以帮助我们改进</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="question-handle text-gray-400 hover:text-white">
                                    <i class="fas fa-grip-vertical"></i>
                                </div>
                                <button class="text-gray-400 hover:text-white">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-gray-400 hover:text-red-500">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="mt-3">
                            <textarea class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3" placeholder="请输入您的建议或意见" disabled></textarea>
                        </div>
                        <div class="mt-3 flex items-center">
                            <input type="checkbox" id="q3_required">
                            <label for="q3_required" class="text-gray-300 ml-2 text-sm">必答题</label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 问卷设置 -->
            <div class="mb-6 border-t border-gray-700 pt-6">
                <h4 class="text-white font-medium mb-4">问卷设置</h4>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <input type="checkbox" id="setting_anonymous" checked>
                        <label for="setting_anonymous" class="text-gray-300 ml-2">允许匿名回答</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="setting_result" checked>
                        <label for="setting_result" class="text-gray-300 ml-2">提交后显示统计结果</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="setting_limit">
                        <label for="setting_limit" class="text-gray-300 ml-2">限制每人只能提交一次</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="setting_notification" checked>
                        <label for="setting_notification" class="text-gray-300 ml-2">有新回复时通知我</label>
                    </div>
                </div>
            </div>
            
            <!-- 底部按钮 -->
            <div class="flex justify-end space-x-3">
                <button type="button" id="saveAsDraftBtn" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg">
                    保存为草稿
                </button>
                <button type="button" id="previewBtn" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg">
                    预览
                </button>
                <button type="button" id="publishBtn" class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg">
                    发布问卷
                </button>
            </div>
        </div>
    </div>

    <!-- 必要的JS脚本 -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签页切换
            const tabLinks = document.querySelectorAll('.border-b ul li a');
            
            tabLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 移除所有标签的活动状态
                    tabLinks.forEach(tab => {
                        tab.classList.remove('text-white', 'border-blue-500');
                        tab.classList.add('text-gray-400', 'border-transparent');
                    });
                    
                    // 设置当前标签为活动状态
                    this.classList.remove('text-gray-400', 'border-transparent');
                    this.classList.add('text-white', 'border-blue-500');
                });
            });
            
            // 模态框控制
            const createSurveyBtn = document.getElementById('createSurveyBtn');
            const createSurveyModal = document.getElementById('createSurveyModal');
            const closeModalBtn = document.getElementById('closeModalBtn');
            const saveAsDraftBtn = document.getElementById('saveAsDraftBtn');
            const previewBtn = document.getElementById('previewBtn');
            const publishBtn = document.getElementById('publishBtn');
            
            function openModal() {
                createSurveyModal.classList.remove('hidden');
            }
            
            function closeModal() {
                createSurveyModal.classList.add('hidden');
            }
            
            createSurveyBtn.addEventListener('click', openModal);
            closeModalBtn.addEventListener('click', closeModal);
            
            // 点击模态框外部关闭
            createSurveyModal.addEventListener('click', function(e) {
                if (e.target === createSurveyModal) {
                    closeModal();
                }
            });
            
            // 保存为草稿按钮
            saveAsDraftBtn.addEventListener('click', function() {
                alert('问卷已保存为草稿！');
                closeModal();
            });
            
            // 预览按钮
            previewBtn.addEventListener('click', function() {
                alert('问卷预览功能将在新窗口打开！');
            });
            
            // 发布问卷按钮
            publishBtn.addEventListener('click', function() {
                alert('问卷已成功发布！');
                closeModal();
            });
            
            // 模拟拖拽排序功能
            const questionItems = document.querySelectorAll('.question-item');
            const questionHandles = document.querySelectorAll('.question-handle');
            
            questionHandles.forEach((handle, index) => {
                handle.addEventListener('mousedown', function() {
                    questionItems[index].classList.add('dragging');
                    
                    // 模拟拖拽效果
                    setTimeout(() => {
                        questionItems[index].classList.remove('dragging');
                    }, 500);
                });
            });
        });
    </script>
</body>
</html>