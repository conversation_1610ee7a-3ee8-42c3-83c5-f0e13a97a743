-- 模块配置表
-- 用于管理模块的启用/停用状态和配置信息
CREATE TABLE IF NOT EXISTS module_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    
    -- 模块基本信息
    module_name VARCHAR(50) NOT NULL COMMENT '模块名称',
    module_type ENUM('core', 'optional', 'vendors') NOT NULL DEFAULT 'optional' COMMENT '模块类型',
    
    -- 状态管理
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    auto_load BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否自动加载',
    
    -- 版本信息
    version VARCHAR(20) DEFAULT '1.0.0' COMMENT '模块版本',
    min_gacms_version VARCHAR(20) DEFAULT '1.0.0' COMMENT '最低GACMS版本要求',
    
    -- 依赖关系
    dependencies JSON COMMENT '依赖的模块列表',
    conflicts JSON COMMENT '冲突的模块列表',
    
    -- 配置信息
    config JSON COMMENT '模块配置数据',
    settings JSON COMMENT '用户可配置的设置',
    
    -- 权限和路由
    permissions JSON COMMENT '模块声明的权限',
    routes JSON COMMENT '模块声明的路由',
    events JSON COMMENT '模块声明的事件',
    
    -- 元数据
    display_name VARCHAR(100) COMMENT '显示名称',
    description TEXT COMMENT '模块描述',
    author VARCHAR(100) COMMENT '作者',
    homepage VARCHAR(255) COMMENT '主页地址',
    repository VARCHAR(255) COMMENT '代码仓库地址',
    
    -- 安装信息
    install_path VARCHAR(255) COMMENT '安装路径',
    installed_at TIMESTAMP NULL COMMENT '安装时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 索引
    UNIQUE KEY uk_module_name (module_name),
    INDEX idx_module_type (module_type),
    INDEX idx_enabled (enabled),
    INDEX idx_auto_load (auto_load)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模块配置表';

-- 初始化核心模块配置
INSERT INTO module_configs (
    module_name, module_type, enabled, auto_load, version, 
    display_name, description, author, dependencies, permissions, events
) VALUES 
-- 核心模块（永远启用）
('user', 'core', TRUE, TRUE, '1.0.0', 
 '用户管理', '用户认证、授权和管理功能', 'GACMS Team',
 JSON_ARRAY('database', 'logger', 'event'),
 JSON_ARRAY(
     JSON_OBJECT('name', 'user:manage', 'description', '用户管理权限'),
     JSON_OBJECT('name', 'user:view', 'description', '用户查看权限'),
     JSON_OBJECT('name', 'content_type:manage', 'description', '内容类型管理权限')
 ),
 JSON_OBJECT(
     'publishes', JSON_ARRAY('user.created', 'user.updated', 'user.deleted', 'content_type.created'),
     'listens', JSON_ARRAY()
 )
),

('site', 'core', TRUE, TRUE, '1.0.0',
 '站点管理', '多站点管理、域名绑定和URL重写', 'GACMS Team',
 JSON_ARRAY('database', 'logger', 'event'),
 JSON_ARRAY(
     JSON_OBJECT('name', 'site:manage', 'description', '站点管理权限'),
     JSON_OBJECT('name', 'site:view', 'description', '站点查看权限')
 ),
 JSON_OBJECT(
     'publishes', JSON_ARRAY('site.created', 'site.updated', 'site.deleted'),
     'listens', JSON_ARRAY()
 )
),

('actionlog', 'core', TRUE, TRUE, '1.0.0',
 '操作日志', '系统操作审计和日志记录', 'GACMS Team',
 JSON_ARRAY('database', 'logger', 'event'),
 JSON_ARRAY(
     JSON_OBJECT('name', 'actionlog:view', 'description', '操作日志查看权限')
 ),
 JSON_OBJECT(
     'publishes', JSON_ARRAY('actionlog.created'),
     'listens', JSON_ARRAY('user.created', 'user.updated', 'site.created')
 )
),

('admin', 'core', TRUE, TRUE, '1.0.0',
 '后台管理', '系统后台管理界面和仪表板', 'GACMS Team',
 JSON_ARRAY('user', 'site'),
 JSON_ARRAY(
     JSON_OBJECT('name', 'admin:access', 'description', '后台访问权限'),
     JSON_OBJECT('name', 'admin:dashboard', 'description', '仪表板访问权限')
 ),
 JSON_OBJECT(
     'publishes', JSON_ARRAY('admin.menu.accessed', 'admin.dashboard.viewed'),
     'listens', JSON_ARRAY()
 )
),

('content', 'core', TRUE, TRUE, '1.0.0',
 '内容管理', '统一内容管理系统，包含文章、页面、分类、标签、菜单、横幅和媒体管理', 'GACMS Team',
 JSON_ARRAY('user', 'site'),
 JSON_ARRAY(
     JSON_OBJECT('name', 'content:manage', 'description', '内容管理权限'),
     JSON_OBJECT('name', 'content:publish', 'description', '内容发布权限'),
     JSON_OBJECT('name', 'menu:manage', 'description', '菜单管理权限'),
     JSON_OBJECT('name', 'banner:manage', 'description', '横幅管理权限'),
     JSON_OBJECT('name', 'media:manage', 'description', '媒体管理权限')
 ),
 JSON_OBJECT(
     'publishes', JSON_ARRAY('content.created', 'content.updated', 'content.published', 'menu.created', 'menu.updated', 'banner.created', 'banner.updated', 'media.uploaded'),
     'listens', JSON_ARRAY('user.created')
 )
),

-- 可选模块（可以启用/停用）

('theme', 'optional', TRUE, TRUE, '1.0.0',
 '主题管理', '网站主题和模板管理', 'GACMS Team',
 JSON_ARRAY('content'),
 JSON_ARRAY(
     JSON_OBJECT('name', 'theme:manage', 'description', '主题管理权限')
 ),
 JSON_OBJECT(
     'publishes', JSON_ARRAY('theme.activated', 'theme.deactivated'),
     'listens', JSON_ARRAY()
 )
);
