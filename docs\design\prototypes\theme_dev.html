<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题开发 - GACMS 后台管理系统</title>
    <link rel="icon" href="./assets/images/favicon.ico" type="image/x-icon">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #121826;
            color: #e2e8f0;
        }
        
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #1a2234;
        }
        ::-webkit-scrollbar-thumb {
            background: #2d3748;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #4a5568;
        }
        
        /* 代码编辑器样式 */
        .code-editor {
            font-family: 'Fira Code', monospace;
            line-height: 1.5;
            tab-size: 4;
        }
        
        .editor-line-numbers {
            user-select: none;
            text-align: right;
            color: #4a5568;
            padding-right: 8px;
            border-right: 1px solid #2d3748;
        }
        
        /* 语法高亮 */
        .token-html { color: #e53e3e; }
        .token-tag { color: #38b2ac; }
        .token-attr { color: #d69e2e; }
        .token-string { color: #68d391; }
        .token-comment { color: #718096; font-style: italic; }
        
        /* 文件树样式 */
        .file-tree ul {
            list-style-type: none;
            padding-left: 1.5rem;
        }
        .file-tree li {
            padding: 0.25rem 0;
        }
        .file-tree .folder {
            color: #d69e2e;
            cursor: pointer;
        }
        .file-tree .file {
            color: #e2e8f0;
            cursor: pointer;
        }
        .file-tree .file:hover, .file-tree .folder:hover {
            text-decoration: underline;
        }
        .file-tree .active {
            background-color: rgba(66, 153, 225, 0.2);
            border-radius: 4px;
        }
        
        /* 预览框架样式 */
        .preview-frame {
            border: 1px solid #2d3748;
            border-radius: 8px;
            background-color: white;
        }
        
        /* 主题组件库样式 */
        .component-card {
            border: 1px solid #2d3748;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .component-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-color: #4299e1;
        }
    </style>
</head>
<body class="min-h-screen flex flex-col">
    <!-- 侧边栏 -->
    <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-gray-900 shadow-lg transform transition-transform duration-300 lg:translate-x-0"></div>
    
    <!-- 主内容区 -->
    <main class="flex-1 lg:ml-64 p-6">
        <!-- 顶部导航栏 -->
        <div id="topNavbar" class="mb-6"></div>
        
        <!-- 面包屑导航 -->
        <div class="flex items-center text-sm text-gray-500 mb-6">
            <a href="dashboard.html" class="hover:text-blue-400">首页</a>
            <i class="fas fa-chevron-right mx-2 text-xs"></i>
            <a href="#" class="hover:text-blue-400">开发工具</a>
            <i class="fas fa-chevron-right mx-2 text-xs"></i>
            <span class="text-gray-400">主题开发</span>
        </div>
        
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-white flex items-center">
                <i class="fas fa-paint-brush mr-3 text-blue-500"></i>
                主题开发
                <span class="ml-3 text-sm font-normal bg-blue-600 text-white px-2 py-1 rounded">开发者功能</span>
            </h1>
            <p class="mt-2 text-gray-400">设计、开发和测试自定义主题，使用内置工具和组件库快速构建专业级主题模板</p>
        </div>
        
        <!-- 标签页导航 -->
        <div class="border-b border-gray-700 mb-6">
            <nav class="flex space-x-6" aria-label="Tabs">
                <button role="tab" class="border-b-2 border-blue-500 text-blue-400 py-4 px-1 font-medium" aria-selected="true" data-target="themeEditor">
                    <i class="fas fa-code mr-2"></i>主题编辑器
                </button>
                <button role="tab" class="border-b-2 border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-400 py-4 px-1 font-medium" aria-selected="false" data-target="componentLibrary">
                    <i class="fas fa-th-large mr-2"></i>组件库
                </button>
                <button role="tab" class="border-b-2 border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-400 py-4 px-1 font-medium" aria-selected="false" data-target="assetManager">
                    <i class="fas fa-images mr-2"></i>资源管理
                </button>
                <button role="tab" class="border-b-2 border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-400 py-4 px-1 font-medium" aria-selected="false" data-target="themeSettings">
                    <i class="fas fa-sliders-h mr-2"></i>主题配置
                </button>
            </nav>
        </div>
        
        <!-- 标签页内容 -->
        <div class="tab-contents">
            <!-- 主题编辑器 -->
            <div id="themeEditor" class="tab-content">
                <div class="grid grid-cols-1 lg:grid-cols-12 gap-6">
                    <!-- 左侧文件树 -->
                    <div class="lg:col-span-3">
                        <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-4 h-full">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="font-medium">主题文件</h3>
                                <div class="flex space-x-2">
                                    <button class="text-gray-400 hover:text-blue-400" title="新建文件">
                                        <i class="fas fa-file-plus"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-blue-400" title="新建文件夹">
                                        <i class="fas fa-folder-plus"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-blue-400" title="刷新">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="file-tree overflow-y-auto" style="max-height: 600px;">
                                <ul>
                                    <li>
                                        <div class="folder flex items-center">
                                            <i class="fas fa-folder-open mr-2"></i>
                                            <span>modern-theme</span>
                                        </div>
                                        <ul>
                                            <li>
                                                <div class="folder flex items-center">
                                                    <i class="fas fa-folder mr-2"></i>
                                                    <span>assets</span>
                                                </div>
                                                <ul class="hidden">
                                                    <li>
                                                        <div class="folder flex items-center">
                                                            <i class="fas fa-folder mr-2"></i>
                                                            <span>css</span>
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="folder flex items-center">
                                                            <i class="fas fa-folder mr-2"></i>
                                                            <span>js</span>
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="folder flex items-center">
                                                            <i class="fas fa-folder mr-2"></i>
                                                            <span>images</span>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </li>
                                            <li>
                                                <div class="folder flex items-center">
                                                    <i class="fas fa-folder mr-2"></i>
                                                    <span>templates</span>
                                                </div>
                                                <ul>
                                                    <li>
                                                        <div class="file flex items-center active px-2">
                                                            <i class="fas fa-file-code mr-2"></i>
                                                            <span>header.html</span>
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="file flex items-center px-2">
                                                            <i class="fas fa-file-code mr-2"></i>
                                                            <span>footer.html</span>
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="file flex items-center px-2">
                                                            <i class="fas fa-file-code mr-2"></i>
                                                            <span>sidebar.html</span>
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="file flex items-center px-2">
                                                            <i class="fas fa-file-code mr-2"></i>
                                                            <span>article.html</span>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </li>
                                            <li>
                                                <div class="file flex items-center px-2">
                                                    <i class="fas fa-file-alt mr-2"></i>
                                                    <span>theme.json</span>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="file flex items-center px-2">
                                                    <i class="fas fa-file-alt mr-2"></i>
                                                    <span>README.md</span>
                                                </div>
                                            </li>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 中间代码编辑器 -->
                    <div class="lg:col-span-6">
                        <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-4 h-full">
                            <div class="flex justify-between items-center mb-4">
                                <div class="flex items-center">
                                    <h3 class="font-medium">编辑: <span class="text-blue-400">header.html</span></h3>
                                </div>
                                <div class="flex space-x-3">
                                    <button class="text-gray-400 hover:text-blue-400" title="保存">
                                        <i class="fas fa-save"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-blue-400" title="撤销">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-blue-400" title="重做">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-blue-400" title="格式化">
                                        <i class="fas fa-indent"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="code-editor bg-gray-900 rounded-lg overflow-hidden">
                                <div class="flex">
                                    <div class="editor-line-numbers p-4 select-none">
                                        <div>1</div>
                                        <div>2</div>
                                        <div>3</div>
                                        <div>4</div>
                                        <div>5</div>
                                        <div>6</div>
                                        <div>7</div>
                                        <div>8</div>
                                        <div>9</div>
                                        <div>10</div>
                                        <div>11</div>
                                        <div>12</div>
                                        <div>13</div>
                                        <div>14</div>
                                        <div>15</div>
                                    </div>
                                    <div class="p-4 overflow-x-auto w-full">
                                        <pre><code><span class="token-html">&lt;!-- 网站头部导航 --&gt;</span>
<span class="token-tag">&lt;header</span> <span class="token-attr">class</span>=<span class="token-string">"site-header bg-white shadow-sm"</span><span class="token-tag">&gt;</span>
  <span class="token-tag">&lt;div</span> <span class="token-attr">class</span>=<span class="token-string">"container mx-auto px-4 py-3"</span><span class="token-tag">&gt;</span>
    <span class="token-tag">&lt;div</span> <span class="token-attr">class</span>=<span class="token-string">"flex justify-between items-center"</span><span class="token-tag">&gt;</span>
      <span class="token-tag">&lt;div</span> <span class="token-attr">class</span>=<span class="token-string">"flex items-center"</span><span class="token-tag">&gt;</span>
        <span class="token-tag">&lt;a</span> <span class="token-attr">href</span>=<span class="token-string">"{{ site.url }}"</span> <span class="token-attr">class</span>=<span class="token-string">"text-2xl font-bold text-gray-800"</span><span class="token-tag">&gt;</span>
          {{ site.title }}
        <span class="token-tag">&lt;/a&gt;</span>
      <span class="token-tag">&lt;/div&gt;</span>
      <span class="token-tag">&lt;nav</span> <span class="token-attr">class</span>=<span class="token-string">"hidden md:flex space-x-6"</span><span class="token-tag">&gt;</span>
        {% for item in navigation.main %}
          <span class="token-tag">&lt;a</span> <span class="token-attr">href</span>=<span class="token-string">"{{ item.url }}"</span> <span class="token-attr">class</span>=<span class="token-string">"text-gray-600 hover:text-blue-600"</span><span class="token-tag">&gt;</span>{{ item.title }}<span class="token-tag">&lt;/a&gt;</span>
        {% endfor %}
      <span class="token-tag">&lt;/nav&gt;</span>
    <span class="token-tag">&lt;/div&gt;</span>
  <span class="token-tag">&lt;/div&gt;</span>
<span class="token-tag">&lt;/header&gt;</span></code></pre>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex justify-between mt-4 text-sm text-gray-500">
                                <div>行: 15 | 列: 12</div>
                                <div>UTF-8</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧预览 -->
                    <div class="lg:col-span-3">
                        <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-4 h-full">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="font-medium">实时预览</h3>
                                <div class="flex space-x-2">
                                    <button class="text-gray-400 hover:text-blue-400" title="刷新预览">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-blue-400" title="在新窗口打开">
                                        <i class="fas fa-external-link-alt"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="preview-frame w-full h-64 overflow-hidden rounded-lg">
                                <!-- 模拟预览内容 -->
                                <div class="bg-white p-3">
                                    <div class="flex justify-between items-center">
                                        <div class="text-lg font-bold text-gray-800">GACMS 博客</div>
                                        <div class="hidden md:flex space-x-4">
                                            <a href="#" class="text-gray-600 hover:text-blue-600">首页</a>
                                            <a href="#" class="text-gray-600 hover:text-blue-600">文章</a>
                                            <a href="#" class="text-gray-600 hover:text-blue-600">分类</a>
                                            <a href="#" class="text-gray-600 hover:text-blue-600">关于</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <h4 class="font-medium mb-2">设备预览</h4>
                                <div class="flex space-x-3">
                                    <button class="bg-gray-700 hover:bg-gray-600 p-2 rounded-lg text-gray-300" title="桌面视图">
                                        <i class="fas fa-desktop"></i>
                                    </button>
                                    <button class="bg-gray-800 p-2 rounded-lg text-blue-400" title="平板视图">
                                        <i class="fas fa-tablet-alt"></i>
                                    </button>
                                    <button class="bg-gray-700 hover:bg-gray-600 p-2 rounded-lg text-gray-300" title="手机视图">
                                        <i class="fas fa-mobile-alt"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <h4 class="font-medium mb-2">模板变量</h4>
                                <div class="bg-gray-800/20 rounded-lg p-3 text-sm">
                                    <div class="mb-2">
                                        <span class="text-blue-400">site.title</span>: GACMS 博客
                                    </div>
                                    <div class="mb-2">
                                        <span class="text-blue-400">site.url</span>: https://example.com
                                    </div>
                                    <div>
                                        <span class="text-blue-400">navigation.main</span>: [数组，4个项目]
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 组件库 -->
            <div id="componentLibrary" class="tab-content hidden">
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold">主题组件库</h3>
                        <div class="flex">
                            <input type="text" placeholder="搜索组件..." class="bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- 导航组件 -->
                        <div class="component-card bg-gray-800/20 p-4 rounded-lg">
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="font-medium text-white">导航栏</h4>
                                <button class="text-blue-400 hover:text-blue-300" title="插入组件">
                                    <i class="fas fa-plus-circle"></i>
                                </button>
                            </div>
                            <div class="bg-white rounded-lg p-2 mb-3">
                                <div class="flex justify-between items-center">
                                    <div class="text-gray-800 font-medium">Logo</div>
                                    <div class="flex space-x-3">
                                        <span class="text-gray-600">链接1</span>
                                        <span class="text-gray-600">链接2</span>
                                        <span class="text-gray-600">链接3</span>
                                    </div>
                                </div>
                            </div>
                            <div class="text-xs text-gray-400">响应式导航栏组件，支持移动端折叠菜单</div>
                        </div>
                        
                        <!-- 页头组件 -->
                        <div class="component-card bg-gray-800/20 p-4 rounded-lg">
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="font-medium text-white">页面标题</h4>
                                <button class="text-blue-400 hover:text-blue-300" title="插入组件">
                                    <i class="fas fa-plus-circle"></i>
                                </button>
                            </div>
                            <div class="bg-white rounded-lg p-2 mb-3">
                                <div class="text-center py-4">
                                    <h2 class="text-gray-800 font-bold">页面标题</h2>
                                    <p class="text-gray-600 text-sm">页面描述文本</p>
                                </div>
                            </div>
                            <div class="text-xs text-gray-400">页面标题组件，支持标题、副标题和背景图</div>
                        </div>
                        
                        <!-- 文章卡片 -->
                        <div class="component-card bg-gray-800/20 p-4 rounded-lg">
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="font-medium text-white">文章卡片</h4>
                                <button class="text-blue-400 hover:text-blue-300" title="插入组件">
                                    <i class="fas fa-plus-circle"></i>
                                </button>
                            </div>
                            <div class="bg-white rounded-lg p-2 mb-3">
                                <div class="bg-gray-100 h-16 mb-2 rounded"></div>
                                <h3 class="text-gray-800 font-medium">文章标题</h3>
                                <p class="text-gray-600 text-xs">文章摘要内容...</p>
                                <div class="flex justify-between text-xs mt-2">
                                    <span class="text-gray-500">作者名</span>
                                    <span class="text-gray-500">2023-10-15</span>
                                </div>
                            </div>
                            <div class="text-xs text-gray-400">文章卡片组件，显示标题、摘要、作者和日期</div>
                        </div>
                        
                        <!-- 页脚组件 -->
                        <div class="component-card bg-gray-800/20 p-4 rounded-lg">
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="font-medium text-white">页脚</h4>
                                <button class="text-blue-400 hover:text-blue-300" title="插入组件">
                                    <i class="fas fa-plus-circle"></i>
                                </button>
                            </div>
                            <div class="bg-white rounded-lg p-2 mb-3">
                                <div class="flex justify-between items-center py-2">
                                    <div class="text-gray-800 text-xs">© 2023 网站名称</div>
                                    <div class="flex space-x-2">
                                        <span class="text-xs">●</span>
                                        <span class="text-xs">●</span>
                                        <span class="text-xs">●</span>
                                    </div>
                                </div>
                            </div>
                            <div class="text-xs text-gray-400">页脚组件，包含版权信息和社交媒体链接</div>
                        </div>
                        
                        <!-- 侧边栏组件 -->
                        <div class="component-card bg-gray-800/20 p-4 rounded-lg">
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="font-medium text-white">侧边栏</h4>
                                <button class="text-blue-400 hover:text-blue-300" title="插入组件">
                                    <i class="fas fa-plus-circle"></i>
                                </button>
                            </div>
                            <div class="bg-white rounded-lg p-2 mb-3">
                                <div class="mb-2">
                                    <h4 class="text-gray-800 text-xs font-medium">分类</h4>
                                    <div class="border-t border-gray-200 mt-1 pt-1">
                                        <div class="text-xs text-gray-600">- 分类1</div>
                                        <div class="text-xs text-gray-600">- 分类2</div>
                                    </div>
                                </div>
                                <div>
                                    <h4 class="text-gray-800 text-xs font-medium">最新文章</h4>
                                    <div class="border-t border-gray-200 mt-1 pt-1">
                                        <div class="text-xs text-gray-600">- 文章标题1</div>
                                        <div class="text-xs text-gray-600">- 文章标题2</div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-xs text-gray-400">侧边栏组件，支持多个小部件</div>
                        </div>
                        
                        <!-- 评论组件 -->
                        <div class="component-card bg-gray-800/20 p-4 rounded-lg">
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="font-medium text-white">评论区</h4>
                                <button class="text-blue-400 hover:text-blue-300" title="插入组件">
                                    <i class="fas fa-plus-circle"></i>
                                </button>
                            </div>
                            <div class="bg-white rounded-lg p-2 mb-3">
                                <div class="mb-2">
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 rounded-full bg-gray-300 mr-1"></div>
                                        <span class="text-xs text-gray-800 font-medium">用户名</span>
                                    </div>
                                    <p class="text-xs text-gray-600 mt-1">评论内容...</p>
                                </div>
                                <div class="border-t border-gray-100 pt-1">
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 rounded-full bg-gray-300 mr-1"></div>
                                        <span class="text-xs text-gray-800 font-medium">用户名</span>
                                    </div>
                                    <p class="text-xs text-gray-600 mt-1">评论内容...</p>
                                </div>
                            </div>
                            <div class="text-xs text-gray-400">评论区组件，支持嵌套回复</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 资源管理 -->
            <div id="assetManager" class="tab-content hidden">
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold">主题资源管理</h3>
                        <div class="flex space-x-3">
                            <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center">
                                <i class="fas fa-upload mr-2"></i> 上传资源
                            </button>
                            <button class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors flex items-center">
                                <i class="fas fa-folder-plus mr-2"></i> 新建文件夹
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <div class="flex items-center text-sm text-gray-500 mb-4">
                            <a href="#" class="hover:text-blue-400">modern-theme</a>
                            <i class="fas fa-chevron-right mx-2 text-xs"></i>
                            <a href="#" class="hover:text-blue-400">assets</a>
                            <i class="fas fa-chevron-right mx-2 text-xs"></i>
                            <span class="text-gray-400">images</span>
                        </div>
                        
                        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                            <!-- 文件夹 -->
                            <div class="bg-gray-800/20 rounded-lg p-3 text-center cursor-pointer hover:bg-gray-700/30">
                                <div class="text-yellow-500 text-3xl mb-2">
                                    <i class="fas fa-folder"></i>
                                </div>
                                <div class="text-sm truncate">backgrounds</div>
                            </div>
                            
                            <div class="bg-gray-800/20 rounded-lg p-3 text-center cursor-pointer hover:bg-gray-700/30">
                                <div class="text-yellow-500 text-3xl mb-2">
                                    <i class="fas fa-folder"></i>
                                </div>
                                <div class="text-sm truncate">icons</div>
                            </div>
                            
                            <!-- 图片文件 -->
                            <div class="bg-gray-800/20 rounded-lg p-3 text-center cursor-pointer hover:bg-gray-700/30">
                                <div class="h-16 mb-2 flex items-center justify-center overflow-hidden">
                                    <img src="https://images.unsplash.com/photo-1579546929518-9e396f3cc809" alt="Gradient" class="max-h-full rounded">
                                </div>
                                <div class="text-sm truncate">hero-bg.jpg</div>
                            </div>
                            
                            <div class="bg-gray-800/20 rounded-lg p-3 text-center cursor-pointer hover:bg-gray-700/30">
                                <div class="h-16 mb-2 flex items-center justify-center overflow-hidden">
                                    <img src="https://images.unsplash.com/photo-1557682250-33bd709cbe85" alt="Purple Gradient" class="max-h-full rounded">
                                </div>
                                <div class="text-sm truncate">pattern.jpg</div>
                            </div>
                            
                            <div class="bg-gray-800/20 rounded-lg p-3 text-center cursor-pointer hover:bg-gray-700/30">
                                <div class="h-16 mb-2 flex items-center justify-center overflow-hidden">
                                    <img src="https://images.unsplash.com/photo-1541701494587-cb58502866ab" alt="Abstract" class="max-h-full rounded">
                                </div>
                                <div class="text-sm truncate">featured.jpg</div>
                            </div>
                            
                            <!-- 其他文件 -->
                            <div class="bg-gray-800/20 rounded-lg p-3 text-center cursor-pointer hover:bg-gray-700/30">
                                <div class="text-blue-400 text-3xl mb-2">
                                    <i class="fas fa-file-image"></i>
                                </div>
                                <div class="text-sm truncate">logo.svg</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-800/20 rounded-lg p-4">
                        <h4 class="font-medium mb-3">资源详情</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <div class="mb-4 flex items-center justify-center bg-gray-900/50 rounded-lg p-4">
                                    <img src="https://images.unsplash.com/photo-1579546929518-9e396f3cc809" alt="Selected image" class="max-h-48 rounded">
                                </div>
                            </div>
                            <div>
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">文件名:</span>
                                        <span>hero-bg.jpg</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">类型:</span>
                                        <span>image/jpeg</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">大小:</span>
                                        <span>245 KB</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">尺寸:</span>
                                        <span>1920 × 1080 px</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">上传时间:</span>
                                        <span>2023-10-15 14:30</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">URL:</span>
                                        <span class="text-blue-400 truncate">/themes/modern-theme/assets/images/hero-bg.jpg</span>
                                    </div>
                                </div>
                                
                                <div class="mt-4 flex space-x-2">
                                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded transition-colors text-sm">
                                        <i class="fas fa-copy mr-1"></i> 复制URL
                                    </button>
                                    <button class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded transition-colors text-sm">
                                        <i class="fas fa-edit mr-1"></i> 重命名
                                    </button>
                                    <button class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded transition-colors text-sm">
                                        <i class="fas fa-trash-alt mr-1"></i> 删除
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 主题配置 -->
            <div id="themeSettings" class="tab-content hidden">
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold">主题配置</h3>
                        <div class="flex space-x-3">
                            <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-save mr-2"></i> 保存配置
                            </button>
                            <button class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-undo mr-2"></i> 重置默认
                            </button>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- 基本设置 -->
                        <div class="bg-gray-800/20 rounded-lg p-4">
                            <h4 class="font-medium mb-4 text-white">基本设置</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm text-gray-400 mb-1">主题名称</label>
                                    <input type="text" value="Modern Theme" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm text-gray-400 mb-1">主题描述</label>
                                    <textarea class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" rows="3">一个现代简约风格的响应式主题，适合博客和企业网站使用。</textarea>
                                </div>
                                <div>
                                    <label class="block text-sm text-gray-400 mb-1">版本号</label>
                                    <input type="text" value="1.2.0" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm text-gray-400 mb-1">作者</label>
                                    <input type="text" value="GACMS Team" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                            </div>
                        </div>
                        
                        <!-- 颜色设置 -->
                        <div class="bg-gray-800/20 rounded-lg p-4">
                            <h4 class="font-medium mb-4 text-white">颜色设置</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm text-gray-400 mb-1">主色调</label>
                                    <div class="flex items-center">
                                        <input type="color" value="#3b82f6" class="w-10 h-10 rounded mr-2 border-0">
                                        <input type="text" value="#3b82f6" class="flex-1 bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm text-gray-400 mb-1">次要色调</label>
                                    <div class="flex items-center">
                                        <input type="color" value="#10b981" class="w-10 h-10 rounded mr-2 border-0">
                                        <input type="text" value="#10b981" class="flex-1 bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm text-gray-400 mb-1">背景色</label>
                                    <div class="flex items-center">
                                        <input type="color" value="#ffffff" class="w-10 h-10 rounded mr-2 border-0">
                                        <input type="text" value="#ffffff" class="flex-1 bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm text-gray-400 mb-1">文本色</label>
                                    <div class="flex items-center">
                                        <input type="color" value="#1f2937" class="w-10 h-10 rounded mr-2 border-0">
                                        <input type="text" value="#1f2937" class="flex-1 bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 布局设置 -->
                        <div class="bg-gray-800/20 rounded-lg p-4">
                            <h4 class="font-medium mb-4 text-white">布局设置</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm text-gray-400 mb-1">布局类型</label>
                                    <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option>全宽布局</option>
                                        <option selected>居中布局</option>
                                        <option>盒装布局</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm text-gray-400 mb-1">侧边栏位置</label>
                                    <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option>左侧</option>
                                        <option selected>右侧</option>
                                        <option>无侧边栏</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm text-gray-400 mb-1">容器宽度</label>
                                    <input type="text" value="1200px" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="stickyHeader" checked class="rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600">
                                    <label for="stickyHeader" class="ml-2">启用粘性顶部导航</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="darkModeToggle" checked class="rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600">
                                    <label for="darkModeToggle" class="ml-2">显示暗色模式切换按钮</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <h4 class="font-medium mb-4 text-white">主题高级配置 (JSON)</h4>
                        <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                            <pre class="text-sm text-gray-300">{
  "name": "Modern Theme",
  "version": "1.2.0",
  "author": "GACMS Team",
  "description": "一个现代简约风格的响应式主题，适合博客和企业网站使用。",
  "supports": ["widgets", "menus", "customizer"],
  "colors": {
    "primary": "#3b82f6",
    "secondary": "#10b981",
    "background": "#ffffff",
    "text": "#1f2937"
  },
  "layout": {
    "type": "centered",
    "sidebar": "right",
    "container_width": "1200px",
    "sticky_header": true
  },
  "features": {
    "dark_mode": true,
    "responsive": true,
    "seo_optimized": true
  }
}</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 标签页切换功能
            const tabButtons = document.querySelectorAll('[role="tab"]');
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有标签页的active类
                    tabButtons.forEach(btn => {
                        btn.classList.remove('border-blue-500', 'text-blue-400');
                        btn.classList.add('border-transparent', 'text-gray-400', 'hover:text-gray-300', 'hover:border-gray-400');
                        btn.setAttribute('aria-selected', 'false');
                    });
                    
                    // 添加当前标签页的active类
                    this.classList.remove('border-transparent', 'text-gray-400', 'hover:text-gray-300', 'hover:border-gray-400');
                    this.classList.add('border-blue-500', 'text-blue-400');
                    this.setAttribute('aria-selected', 'true');
                    
                    // 隐藏所有内容
                    const tabContents = document.querySelectorAll('.tab-content');
                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                        content.classList.remove('active');
                    });
                    
                    // 显示对应的内容
                    const targetId = this.getAttribute('data-target');
                    const targetContent = document.getElementById(targetId);
                    if(targetContent) {
                        targetContent.classList.remove('hidden');
                        targetContent.classList.add('active');
                    }
                });
            });
            
            // 文件树交互
            const folderItems = document.querySelectorAll('.file-tree .folder');
            folderItems.forEach(folder => {
                folder.addEventListener('click', function() {
                    const sublist = this.nextElementSibling;
                    if(sublist && sublist.tagName === 'UL') {
                        sublist.classList.toggle('hidden');
                        const icon = this.querySelector('i');
                        if(icon) {
                            if(sublist.classList.contains('hidden')) {
                                icon.classList.remove('fa-folder-open');
                                icon.classList.add('fa-folder');
                            } else {
                                icon.classList.remove('fa-folder');
                                icon.classList.add('fa-folder-open');
                            }
                        }
                    }
                });
            });
        });
    </script>
</body>
</html>