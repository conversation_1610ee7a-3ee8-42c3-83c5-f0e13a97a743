/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/banner/infrastructure/persistence/GormBannerRepository.go
 * @Description: GORM implementation of the BannerRepository.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package persistence

import (
	"context"
	"gacms/internal/modules/banner/domain/contract"
	"gacms/internal/modules/banner/domain/model"
	dbContract "gacms/pkg/contract"
)

type GormBannerRepository struct {
	db dbContract.Database
}

func NewGormBannerRepository(db dbContract.Database) contract.BannerRepository {
	return &GormBannerRepository{db: db}
}

func (r *GormBannerRepository) Create(ctx context.Context, banner *model.Banner) error {
	return r.db.DB(ctx).Create(banner).Error
}

func (r *GormBannerRepository) Update(ctx context.Context, banner *model.Banner) error {
	return r.db.DB(ctx).Save(banner).Error
}

func (r *GormBannerRepository) Delete(ctx context.Context, id uint) error {
	return r.db.DB(ctx).Delete(&model.Banner{}, id).Error
}

func (r *GormBannerRepository) GetByID(ctx context.Context, id uint) (*model.Banner, error) {
	var banner model.Banner
	err := r.db.DB(ctx).First(&banner, id).Error
	return &banner, err
}

func (r *GormBannerRepository) ListByPositionID(ctx context.Context, positionID uint) ([]*model.Banner, error) {
	var banners []*model.Banner
	err := r.db.DB(ctx).Where("position_id = ?", positionID).Order("`order` asc").Find(&banners).Error
	return banners, err
} 