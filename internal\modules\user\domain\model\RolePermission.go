/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: {current_date}
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: {current_date}
 * @FilePath: internal/modules/user/domain/model/RolePermission.go
 * @Description: Defines the join model for the many-to-many relationship between roles and permissions.
 *
 * © {current_year} GACMS. All rights reserved.
 */
package model

// RolePermission is the explicit join table for the many-to-many relationship
// between Role and Permission.
type RolePermission struct {
	RoleID       uint `gorm:"primaryKey"`
	PermissionID uint `gorm:"primaryKey"`
} 