/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON> Nieh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/system/application/service/ModuleService.go
 * @Description: A service for managing system modules.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

// ModuleService provides operations for managing modules.
type ModuleService struct {
	// In the future, this could have dependencies like a template engine.
}

// NewModuleService creates a new ModuleService.
func NewModuleService() *ModuleService {
	return &ModuleService{}
}

// CreateStructure creates the standard directory and file structure for a new module.
func (s *ModuleService) CreateStructure(name string) error {
	moduleName := strings.ToLower(name)
	fmt.Printf("Creating module: %s\n", moduleName)

	basePath := filepath.Join("internal", "modules", moduleName)
	dirs := []string{
		"application/service",
		"application/dto",
		"domain/contract",
		"domain/model",
		"infrastructure/persistence",
		"port/http/controller",
		"port/event",
	}

	for _, dir := range dirs {
		fullPath := filepath.Join(basePath, dir)
		if err := os.MkdirAll(fullPath, os.ModePerm); err != nil {
			return fmt.Errorf("error creating directory %s: %w", fullPath, err)
		}
		fmt.Printf("  [Created] dir: %s\n", fullPath)
	}

	// Create module manifest file
	manifestContent := fmt.Sprintf(`{
  "name": "%s",
  "version": "1.0.0",
  "description": "A new module named %s.",
  "enabled": true
}`, moduleName, moduleName)

	manifestPath := filepath.Join(basePath, "module.json")
	if err := os.WriteFile(manifestPath, []byte(manifestContent), 0644); err != nil {
		return fmt.Errorf("error creating module.json: %w", err)
	}
	fmt.Printf("  [Created] file: %s\n", manifestPath)

	fmt.Printf("Module %s created successfully.\n", moduleName)
	return nil
} 