/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/banner/domain/model/BannerPosition.go
 * @Description: Defines the BannerPosition domain model.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package model

import "gorm.io/gorm"

// BannerPosition represents a location where banners can be displayed.
type BannerPosition struct {
	gorm.Model
	SiteID      uint   `gorm:"index;not null"`
	Name        string `gorm:"type:varchar(255);not null"`
	Slug        string `gorm:"type:varchar(255);uniqueIndex;not null"`
	Description string `gorm:"type:text"`
	Banners     []Banner `gorm:"foreignKey:PositionID"`
} 