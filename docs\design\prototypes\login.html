<!--
Author: <PERSON><PERSON>eh
EMAIL: <EMAIL>
Copyright (c) 2025 Cion Nieh
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 登录</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #0d1117;
            color: #e0e0e0;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            overflow-x: hidden;
        }
        .login-container {
            background: rgba(26, 26, 26, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            padding: 40px;
            width: 90%;
            max-width: 420px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* 移动端适配 */
        @media screen and (max-width: 480px) {
            body {
                padding: 0;
                align-items: center;
            }

            .login-container {
                padding: 30px 20px;
                width: 90%;
                max-width: 100%;
                border-radius: 12px;
                margin: 0 auto;
            }

            .logo-container {
                margin-bottom: 20px;
            }

            .logo-container img {
                width: 80px;
                height: auto;
            }

            .login-title {
                font-size: 1.5rem;
                margin-bottom: 1.5rem;
            }

            .input-field {
                padding: 12px;
                font-size: 0.9rem;
                margin-bottom: 15px;
                border-radius: 8px;
            }

            .btn-primary {
                padding: 12px;
                font-size: 1rem;
                border-radius: 8px;
                margin-top: 10px;
            }

            .extra-links {
                font-size: 0.85rem;
                margin-top: 15px;
                display: flex;
                flex-direction: column;
                gap: 10px;
            }

            .extra-links a {
                padding: 5px 0;
            }

            .copyright {
                font-size: 0.75rem;
                position: fixed;
                bottom: 1rem;
                left: 0;
                right: 0;
                text-align: center;
                color: rgba(255, 255, 255, 0.5);
                padding: 0.5rem;
                background: linear-gradient(to right, transparent, rgba(26, 26, 26, 0.8), transparent);
                backdrop-filter: blur(5px);
                -webkit-backdrop-filter: blur(5px);
                z-index: 10;
                margin-top: 20px;
                padding: 0 10px;
                text-align: center;
            }

            .two-factor-container {
                margin-top: 15px;
            }
        }

        /* 确保在极小屏幕上也能正常显示 */
        @media screen and (max-width: 320px) {
            .login-container {
                padding: 20px 15px;
                width: 95%;
            }

            .login-title {
                font-size: 1.3rem;
            }

            .input-field {
                padding: 10px;
                font-size: 0.85rem;
            }

            .btn-primary {
                padding: 10px;
                font-size: 0.9rem;
            }
        }
        .input-group {
            margin-bottom: 20px;
        }
        .input-label {
            display: block;
            margin-bottom: 8px;
            font-size: 0.875rem; /* 14px */
            color: #c0c0c0; /* 标签颜色调亮一些 */
        }
        .input-field {
            width: 100%;
            padding: 14px 18px;
            box-sizing: border-box;
            background-color: transparent; /* 输入框背景透明 */
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px; /* 更大的圆角 */
            color: #ffffff;
            font-size: 1.05rem; /* 稍微增大字体 */
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1); /* 轻微阴影 */
        }
        .input-field::placeholder {
            color: #888;
        }
        .input-field:focus {
            outline: none;
            border-color: #0066ff; /* 品牌主色 */
            background-color: transparent;
            box-shadow: 0 4px 12px rgba(0, 102, 255, 0.15); /* 聚焦时增强阴影 */
            transform: translateY(-1px); /* 轻微上浮效果 */
        }
        .input-field:-webkit-autofill,
        .input-field:-webkit-autofill:hover,
        .input-field:-webkit-autofill:focus,
        .input-field:-webkit-autofill:active {
            -webkit-box-shadow: 0 0 0 30px transparent inset !important;
            -webkit-text-fill-color: #e0e0e0 !important;
            transition: background-color 5000s ease-in-out 0s;
        }
        .btn-primary {
            width: 100%;
            padding: 14px 18px;
            background-color: #0066ff; /* 调整品牌主色 */
            color: #fff;
            border: none;
            border-radius: 10px;
            font-size: 1.15rem; /* 增大按钮文字 */
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(0, 102, 255, 0.35);
        }
        .btn-primary:hover {
            background-color: #0052cc;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 102, 255, 0.45);
        }
        .btn-primary:active {
            transform: translateY(0);
            box-shadow: 0 4px 15px rgba(0, 102, 255, 0.4);
            background-color: #0047b3;
        }
        .extra-links {
            margin-top: 30px;
            text-align: center;
            font-size: 0.9rem;
            opacity: 0.9;
            animation: fadeIn 1s ease-out;
        }
        .extra-links a {
            color: #66b3ff;
            text-decoration: none;
            transition: all 0.3s ease;
            padding: 6px 12px;
            border-radius: 6px;
        }
        .extra-links a:hover {
            color: #99ccff;
            background-color: rgba(255, 255, 255, 0.1);
            text-decoration: none;
        }
        .logo-container {
            text-align: center;
            margin-bottom: 25px;
            padding-top: 5px;
            animation: fadeInDown 0.8s ease-out;
        }
        .logo-container img, .logo-container svg {
            width: 110px;
            height: 110px;
            opacity: 1;
            display: inline-block;
            margin-bottom: 10px;
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
            transition: transform 0.3s ease, filter 0.3s ease;
        }
        .logo-container img:hover, .logo-container svg:hover {
            transform: scale(1.05);
            filter: drop-shadow(0 6px 12px rgba(0,0,0,0.25));
        }
        .login-title {
            font-size: 2.2rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
            animation: fadeInUp 0.8s ease-out;
        }
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .copyright {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.85rem;
            color: rgba(255, 255, 255, 0.7);
            text-align: center;
            width: 100%;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
            animation: fadeIn 1.2s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo-container">
            <a href="./index.html" title="返回首页">
                <img src="./assets/images/logo.svg" alt="GACMS Logo">
            </a>
        </div>
        <h1 class="login-title">GACMS 登录</h1>
        <form action="#" method="POST">
            <div class="input-group">
                <label for="username" class="input-label">用户名</label>
                <input type="text" id="username" name="username" class="input-field" required placeholder="请输入您的用户名">
            </div>
            <div class="input-group">
                <label for="password" class="input-label">密&emsp;码</label>
                <input type="password" id="password" name="password" class="input-field" required placeholder="请输入您的密码">
            </div>
            <!-- 2FA 输入框 (初始隐藏，根据用户配置显示) -->
            <div class="input-group" id="2fa-group" style="display: none;">
                <label for="2fa_code" class="input-label">两步验证码</label>
                <input type="text" id="2fa_code" name="2fa_code" class="input-field" placeholder="请输入您的两步验证码">
            </div>
            <button type="submit" class="btn-primary">登录</button>
        </form>
        <div class="extra-links">
            <a href="javascript:void(0);" onclick="alert('忘记密码功能正在开发中...');">忘记密码?</a>
            <!-- <span class="mx-2 text-gray-500">|</span>
            <a href="#">注册新账户</a> -->
        </div>
    </div>

    <footer class="copyright">
        &copy; 2025 GACMS. All Rights Reserved.
    </footer>

    <script>
        // 简单的交互示例：如果用户名包含特定字符，显示2FA输入框
        const usernameInput = document.getElementById('username');
        const tfaGroup = document.getElementById('2fa-group');
        if (usernameInput) {
            usernameInput.addEventListener('input', function() {
                if (this.value.includes('admin_2fa')) { // 假设特定用户需要2FA
                    tfaGroup.style.display = 'block';
                } else {
                    tfaGroup.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>