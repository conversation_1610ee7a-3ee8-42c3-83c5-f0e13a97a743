<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 通知设置</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .notification-switch {
            position: relative;
            display: inline-block;
            width: 46px;
            height: 24px;
        }
        
        .notification-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .switch-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #4B5563;
            transition: .4s;
            border-radius: 24px;
        }
        
        .switch-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .switch-slider {
            background-color: #3B82F6;
        }
        
        input:checked + .switch-slider:before {
            transform: translateX(22px);
        }
        
        .template-card {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .template-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0,0,0,0.2);
        }
        
        .template-card.active {
            border: 2px solid #3B82F6;
        }
        
        .template-card.active::after {
            content: '\f00c';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            top: 10px;
            right: 10px;
            width: 24px;
            height: 24px;
            background-color: #3B82F6;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .notification-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部页面标题 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h2 class="text-xl font-bold text-white relative pl-3 section-title">通知设置</h2>
                <p class="mt-4 text-gray-400">配置系统通知和提醒，管理如何接收重要信息和活动更新。通过电子邮件、短信和系统内通知保持信息同步。</p>
            </div>
            
            <!-- 通知总开关 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-semibold">通知总开关</h3>
                        <p class="text-gray-400 text-sm mt-1">启用或禁用所有系统通知</p>
                    </div>
                    <label class="notification-switch">
                        <input type="checkbox" checked>
                        <span class="switch-slider"></span>
                    </label>
                </div>
            </div>
            
            <!-- 通知渠道区域 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <!-- 电子邮件 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex justify-between items-center mb-4">
                        <div class="flex items-center">
                            <div class="notification-icon bg-blue-500/20 text-blue-400 mr-4">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold">电子邮件</h3>
                                <p class="text-gray-400 text-sm"><EMAIL></p>
                            </div>
                        </div>
                        <label class="notification-switch">
                            <input type="checkbox" checked>
                            <span class="switch-slider"></span>
                        </label>
                    </div>
                    <button class="text-blue-400 hover:text-blue-300 text-sm">修改邮箱地址</button>
                </div>
                
                <!-- 短信 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex justify-between items-center mb-4">
                        <div class="flex items-center">
                            <div class="notification-icon bg-green-500/20 text-green-400 mr-4">
                                <i class="fas fa-sms"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold">短信</h3>
                                <p class="text-gray-400 text-sm">+86 139****1234</p>
                            </div>
                        </div>
                        <label class="notification-switch">
                            <input type="checkbox">
                            <span class="switch-slider"></span>
                        </label>
                    </div>
                    <button class="text-blue-400 hover:text-blue-300 text-sm">验证手机号码</button>
                </div>
                
                <!-- 系统内通知 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex justify-between items-center mb-4">
                        <div class="flex items-center">
                            <div class="notification-icon bg-purple-500/20 text-purple-400 mr-4">
                                <i class="fas fa-bell"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold">系统内通知</h3>
                                <p class="text-gray-400 text-sm">显示在顶部导航栏</p>
                            </div>
                        </div>
                        <label class="notification-switch">
                            <input type="checkbox" checked>
                            <span class="switch-slider"></span>
                        </label>
                    </div>
                    <button class="text-blue-400 hover:text-blue-300 text-sm">配置通知中心</button>
                </div>
            </div>
            
            <!-- 通知类型设置 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold mb-6">通知类型设置</h3>
                
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="text-left border-b border-gray-700">
                                <th class="px-6 py-4 text-sm font-semibold">通知类型</th>
                                <th class="px-6 py-4 text-sm font-semibold">电子邮件</th>
                                <th class="px-6 py-4 text-sm font-semibold">短信</th>
                                <th class="px-6 py-4 text-sm font-semibold">系统内通知</th>
                                <th class="px-6 py-4 text-sm font-semibold">声音通知</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 系统安全通知 -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="px-6 py-4">
                                    <div>
                                        <div class="font-medium">系统安全通知</div>
                                        <div class="text-xs text-gray-400">安全事件、登录提醒等</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox" checked>
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox" checked>
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox" checked>
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox" checked>
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                            </tr>
                            
                            <!-- 内容更新通知 -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="px-6 py-4">
                                    <div>
                                        <div class="font-medium">内容更新通知</div>
                                        <div class="text-xs text-gray-400">新文章、评论等</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox" checked>
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox">
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox" checked>
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox">
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                            </tr>
                            
                            <!-- 用户交互通知 -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="px-6 py-4">
                                    <div>
                                        <div class="font-medium">用户交互通知</div>
                                        <div class="text-xs text-gray-400">消息、关注等</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox">
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox">
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox" checked>
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox" checked>
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                            </tr>
                            
                            <!-- 系统维护通知 -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="px-6 py-4">
                                    <div>
                                        <div class="font-medium">系统维护通知</div>
                                        <div class="text-xs text-gray-400">升级、停机等</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox" checked>
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox" checked>
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox" checked>
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox">
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                            </tr>
                            
                            <!-- 营销通知 -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="px-6 py-4">
                                    <div>
                                        <div class="font-medium">营销通知</div>
                                        <div class="text-xs text-gray-400">新功能、产品更新等</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox">
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox">
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox">
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td class="px-6 py-4">
                                    <label class="notification-switch">
                                        <input type="checkbox">
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 通知模板 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold mb-4">通知模板</h3>
                <p class="text-gray-400 text-sm mb-6">选择一个通知模板样式，用于系统内通知和电子邮件</p>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- 默认模板 -->
                    <div class="template-card relative bg-gray-800/10 border border-gray-700 rounded-xl p-6 active">
                        <div class="mb-4">
                            <h4 class="font-semibold">默认模板</h4>
                            <p class="text-gray-400 text-xs mt-1">简洁现代的通知样式</p>
                        </div>
                        <div class="h-32 p-2 bg-gray-700/50 rounded-lg overflow-hidden">
                            <!-- 模板预览 -->
                            <div class="bg-gray-800 p-3 rounded-lg mb-2">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded-full bg-blue-500/20 flex items-center justify-center mr-2">
                                            <i class="fas fa-lock text-blue-400 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-xs">安全提醒</div>
                                            <div class="text-[10px] text-gray-400">有新的登录活动</div>
                                        </div>
                                    </div>
                                    <div class="text-[10px] text-gray-400">刚刚</div>
                                </div>
                            </div>
                            <div class="bg-gray-800 p-3 rounded-lg">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center mr-2">
                                            <i class="fas fa-comment text-green-400 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-xs">新评论</div>
                                            <div class="text-[10px] text-gray-400">您的文章收到新评论</div>
                                        </div>
                                    </div>
                                    <div class="text-[10px] text-gray-400">5分钟前</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 紧凑模板 -->
                    <div class="template-card relative bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <div class="mb-4">
                            <h4 class="font-semibold">紧凑模板</h4>
                            <p class="text-gray-400 text-xs mt-1">适合大量通知的紧凑视图</p>
                        </div>
                        <div class="h-32 p-2 bg-gray-700/50 rounded-lg overflow-hidden">
                            <!-- 模板预览 -->
                            <div class="bg-gray-800 p-2 rounded-lg mb-2 flex items-center">
                                <div class="w-6 h-6 rounded-full bg-blue-500/20 flex items-center justify-center mr-2">
                                    <i class="fas fa-lock text-blue-400 text-xs"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium text-xs">安全提醒：有新的登录活动</div>
                                </div>
                                <div class="text-[10px] text-gray-400 ml-2">刚刚</div>
                            </div>
                            <div class="bg-gray-800 p-2 rounded-lg mb-2 flex items-center">
                                <div class="w-6 h-6 rounded-full bg-green-500/20 flex items-center justify-center mr-2">
                                    <i class="fas fa-comment text-green-400 text-xs"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium text-xs">新评论：您的文章收到新评论</div>
                                </div>
                                <div class="text-[10px] text-gray-400 ml-2">5分钟前</div>
                            </div>
                            <div class="bg-gray-800 p-2 rounded-lg mb-2 flex items-center">
                                <div class="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center mr-2">
                                    <i class="fas fa-user-plus text-purple-400 text-xs"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium text-xs">新关注：一位用户关注了您</div>
                                </div>
                                <div class="text-[10px] text-gray-400 ml-2">10分钟前</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 图文模板 -->
                    <div class="template-card relative bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <div class="mb-4">
                            <h4 class="font-semibold">图文模板</h4>
                            <p class="text-gray-400 text-xs mt-1">带有预览图的丰富通知</p>
                        </div>
                        <div class="h-32 p-2 bg-gray-700/50 rounded-lg overflow-hidden">
                            <!-- 模板预览 -->
                            <div class="bg-gray-800 p-3 rounded-lg mb-2">
                                <div class="flex items-start">
                                    <div class="w-12 h-12 rounded bg-gray-600 mr-3 flex-shrink-0">
                                        <div class="w-full h-full rounded bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?ixlib=rb-1.2.1&auto=format&fit=crop&w=80&q=80');"></div>
                                    </div>
                                    <div class="flex-1">
                                        <div class="font-medium text-xs">新文章已发布</div>
                                        <div class="text-[10px] text-gray-400">2025年技术趋势预测</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 通知频率设置 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold mb-4">通知频率设置</h3>
                
                <div class="space-y-6">
                    <div>
                        <label class="block text-gray-300 mb-2">电子邮件摘要频率</label>
                        <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option>实时发送</option>
                            <option selected>每日摘要</option>
                            <option>每周摘要</option>
                            <option>不发送摘要</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-gray-300 mb-2">静默时间</label>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="text-sm text-gray-400">开始时间</label>
                                <input type="time" value="22:00" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="text-sm text-gray-400">结束时间</label>
                                <input type="time" value="08:00" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                        <p class="text-xs text-gray-400 mt-2">在静默时间段内，将不会收到任何通知</p>
                    </div>
                    
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                            <span class="ml-2">紧急通知不受静默时间限制</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end mb-6">
                <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
                    保存设置
                </button>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 模板卡片选择
            const templateCards = document.querySelectorAll('.template-card');
            
            templateCards.forEach(card => {
                card.addEventListener('click', function() {
                    templateCards.forEach(c => c.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html> 