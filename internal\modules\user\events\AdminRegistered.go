/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: {current_date}
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: {current_date}
 * @FilePath: internal/modules/user/events/AdminRegistered.go
 * @Description: Defines the event dispatched when a new admin user is registered.
 *
 * © {current_year} GACMS. All rights reserved.
 */
package events

import (
	"context"
	"gacms/internal/core/bus"
	"gacms/internal/modules/user/domain/model"
	"gacms/pkg/contract"
)

const AdminRegisteredEventName contract.EventName = "user.admin.registered"

// AdminRegisteredEvent is dispatched when a new admin user is successfully created.
type AdminRegisteredEvent struct {
	bus.BaseEvent
	Payload *model.Admin
}

// NewAdminRegisteredEvent creates a new AdminRegisteredEvent.
func NewAdminRegisteredEvent(ctx context.Context, admin *model.Admin) AdminRegisteredEvent {
	baseEvent := bus.NewBaseEvent(ctx, AdminRegisteredEventName, admin).(*bus.BaseEvent)
	return AdminRegisteredEvent{
		BaseEvent: *baseEvent,
		Payload:   admin,
	}
} 