/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/post/port/http/controller/PostController.go
 * @Description: HTTP Controller for the Post module.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package controller

import (
	"gacms/internal/modules/post/application/service"
	"github.com/gin-gonic/gin"
	"net/http"
)

// PostController handles post-specific API endpoints.
type PostController struct {
	postService *service.PostService
}

func NewPostController(postService *service.PostService) *PostController {
	return &PostController{postService: postService}
}

// GetPostBySlug handles the request to fetch a single post by its slug.
func (c *PostController) GetPostBySlug(ctx *gin.Context) {
	slug := ctx.Param("slug")
	
	// The service layer no longer needs siteID, it's handled by the context.
	post, err := c.postService.GetPostBySlug(ctx, slug)
	if err != nil {
		// In a real app, you'd check for specific errors like gorm.ErrRecordNotFound
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Post not found"})
		return
	}

	ctx.JSON(http.StatusOK, post)
}