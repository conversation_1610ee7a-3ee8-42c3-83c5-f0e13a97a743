/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/system/application/service/SettingService.go
 * @Description: Service for managing system and site settings.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package service

import (
	"gacms/internal/modules/system/application/dto"
	"gacms/internal/modules/system/domain/contract"
	"gacms/internal/modules/system/domain/model"
)

type settingService struct {
	repo contract.SettingRepository
}

func NewSettingService(repo contract.SettingRepository) contract.SettingService {
	return &settingService{repo: repo}
}

func (s *settingService) GetSettingsByGroup(siteID *uint, group string) ([]*dto.SettingDTO, error) {
	settings, err := s.repo.GetByGroup(siteID, group)
	if err != nil {
		return nil, err
	}

	var dtos []*dto.SettingDTO
	for _, setting := range settings {
		val := setting.Value
		if setting.IsSecret {
			val = "******"
		}
		dtos = append(dtos, &dto.SettingDTO{
			Key:         setting.Key,
			Value:       val,
			Name:        setting.Name,
			Description: setting.Description,
		})
	}
	return dtos, nil
}

func (s *settingService) UpdateSettingsByGroup(siteID *uint, group string, settingsToUpdate []*dto.SettingDTO) error {
	for _, settingDto := range settingsToUpdate {
		// We can't update a secret value with this generic method.
		// A dedicated method should be used for updating secrets.
		// For now, we get the setting to check if it's a secret.
		existing, err := s.repo.Get(siteID, group, settingDto.Key)
		if err != nil {
			// Handle case where setting doesn't exist, maybe create it? For now, we skip.
			continue
		}

		if existing.IsSecret {
			// Skip updating secret fields.
			continue
		}

		err = s.repo.Upsert(&model.Setting{
			SiteID: siteID,
			Group:  group,
			Key:    settingDto.Key,
			Value:  settingDto.Value,
			// Copy other fields from `existing` to avoid overwriting them with defaults
			Type:        existing.Type,
			Name:        existing.Name,
			Description: existing.Description,
			IsPublic:    existing.IsPublic,
			IsSecret:    existing.IsSecret,
		})
		if err != nil {
			return err // Or collect errors and return at the end
		}
	}
	return nil
} 