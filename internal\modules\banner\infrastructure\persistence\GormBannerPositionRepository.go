/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/banner/infrastructure/persistence/GormBannerPositionRepository.go
 * @Description: GORM implementation of the BannerPositionRepository.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package persistence

import (
	"context"
	"gacms/internal/modules/banner/domain/contract"
	"gacms/internal/modules/banner/domain/model"
	dbContract "gacms/pkg/contract"
)

type GormBannerPositionRepository struct {
	db dbContract.Database
}

func NewGormBannerPositionRepository(db dbContract.Database) contract.BannerPositionRepository {
	return &GormBannerPositionRepository{db: db}
}

func (r *GormBannerPositionRepository) Create(ctx context.Context, position *model.BannerPosition) error {
	return r.db.DB(ctx).Create(position).Error
}

func (r *GormBannerPositionRepository) Update(ctx context.Context, position *model.BannerPosition) error {
	return r.db.DB(ctx).Save(position).Error
}

func (r *GormBannerPositionRepository) Delete(ctx context.Context, id uint) error {
	return r.db.DB(ctx).Delete(&model.BannerPosition{}, id).Error
}

func (r *GormBannerPositionRepository) GetByID(ctx context.Context, id uint) (*model.BannerPosition, error) {
	var position model.BannerPosition
	err := r.db.DB(ctx).Preload("Banners").First(&position, id).Error
	return &position, err
}

func (r *GormBannerPositionRepository) GetBySlug(ctx context.Context, siteID uint, slug string) (*model.BannerPosition, error) {
	var position model.BannerPosition
	err := r.db.DB(ctx).Where("site_id = ? AND slug = ?", siteID, slug).Preload("Banners").First(&position).Error
	return &position, err
}

func (r *GormBannerPositionRepository) List(ctx context.Context, siteID uint) ([]*model.BannerPosition, error) {
	var positions []*model.BannerPosition
	err := r.db.DB(ctx).Where("site_id = ?", siteID).Find(&positions).Error
	return positions, err
} 