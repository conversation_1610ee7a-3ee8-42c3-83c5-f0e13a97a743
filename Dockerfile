# Stage 1: Build the application
FROM golang:1.19-alpine AS builder

# Set the Current Working Directory inside the container
WORKDIR /app

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download all dependencies. Dependencies will be cached if the go.mod and go.sum files are not changed
RUN go mod download

# Copy the source code
COPY . .

# Build the Go app
# -ldflags="-w -s" is used to make the binary smaller
# CGO_ENABLED=0 is important for a static binary that runs in a scratch or alpine image
RUN CGO_ENABLED=0 GOOS=linux go build -ldflags="-w -s" -o ./gacms ./cmd/cli/main.go

# Stage 2: Deploy the application
FROM alpine:latest

# Set the Current Working Directory inside the container
WORKDIR /app

# Copy the pre-built binary from the previous stage
COPY --from=builder /app/gacms .

# Copy configuration files (if any are needed at runtime and not embedded)
# COPY --from=builder /app/configs ./configs

# Expose port 8080 to the outside world
EXPOSE 8080

# Command to run the executable
CMD ["./gacms"] 