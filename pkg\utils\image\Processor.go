/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: pkg/imageprocessor/Processor.go
 * @Description: Provides image processing functionalities like resizing and watermarking.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package imageprocessor

import (
	"image"
	"image/draw"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"io"

	"github.com/disintegration/imaging"
)

// Processor handles image processing operations.
type Processor struct{}

// New creates a new instance of the Processor.
func New() *Processor {
	return &Processor{}
}

// Decode reads and decodes an image from an io.Reader.
func (p *Processor) Decode(reader io.Reader) (image.Image, error) {
	img, _, err := image.Decode(reader)
	return img, err
}

// Resize resizes the given image to the specified width and height.
// If one of width or height is 0, the aspect ratio is preserved.
func (p *Processor) Resize(img image.Image, width, height int) image.Image {
	return imaging.Resize(img, width, height, imaging.Lanczos)
}

// GenerateThumbnails generates multiple thumbnails from a source image.
// It returns a map where the key is the size name (e.g., "small") and the value is the processed image.
func (p *Processor) GenerateThumbnails(src image.Image, sizes map[string]image.Point) map[string]image.Image {
	thumbnails := make(map[string]image.Image)
	for name, size := range sizes {
		thumbnails[name] = p.Resize(src, size.X, size.Y)
	}
	return thumbnails
}

// Watermark adds a watermark to the source image.
// position: "center", "topleft", "topright", "bottomleft", "bottomright"
// opacity: 0.0 (transparent) to 1.0 (opaque)
func (p *Processor) Watermark(src image.Image, watermark image.Image, position string, opacity float64) (image.Image, error) {
	var point image.Point
	bounds := src.Bounds()

	switch position {
	case "topleft":
		point = image.Point{0, 0}
	case "topright":
		point = image.Point{bounds.Max.X - watermark.Bounds().Max.X, 0}
	case "bottomleft":
		point = image.Point{0, bounds.Max.Y - watermark.Bounds().Max.Y}
	case "bottomright":
		point = image.Point{bounds.Max.X - watermark.Bounds().Max.X, bounds.Max.Y - watermark.Bounds().Max.Y}
	case "center":
		fallthrough
	default:
		point = image.Point{(bounds.Max.X - watermark.Bounds().Max.X) / 2, (bounds.Max.Y - watermark.Bounds().Max.Y) / 2}
	}
	
	// Create a new image canvas
	dst := image.NewRGBA(bounds)
	// Copy the source image to the new canvas
	draw.Draw(dst, bounds, src, image.Point{}, draw.Src)
	// Paste the watermark
	imaging.Paste(dst, watermark, point)
	
	return dst, nil
} 