/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2024-07-16
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2024-07-16
 * @FilePath: internal/modules/user/port/http/controller/MemberAuthController.go
 * @Description: HTTP Controller for Member Authentication
 *
 * © 2024 GACMS. All rights reserved.
 */
package controller

import (
	"gacms/internal/modules/user/application/service"
	pkgContract "gacms/pkg/contract"
	"gacms/pkg/response"
	"net/http"

	"github.com/gin-gonic/gin"
)

// MemberAuthController handles HTTP requests related to member authentication.
type MemberAuthController struct {
	service *service.MemberAuthService
	appCtx  pkgContract.AppContext
}

// NewMemberAuthController creates a new instance of MemberAuthController.
func NewMemberAuthController(service *service.MemberAuthService, appCtx pkgContract.AppContext) *MemberAuthController {
	return &MemberAuthController{service: service, appCtx: appCtx}
}

// RegisterRoutes sets up the routing for the member authentication endpoints.
func (c *MemberAuthController) RegisterRoutes(rg *gin.RouterGroup) {
	rg.POST("/member/register", c.register)
	rg.POST("/member/login", c.login)
	rg.POST("/member/social/link", c.linkSocialIdentity)
}

func (c *MemberAuthController) register(ctx *gin.Context) {
	var payload service.RegisterPayload
	if err := ctx.ShouldBindJSON(&payload); err != nil {
		response.Error(ctx, http.StatusBadRequest, "Invalid request payload", err.Error())
		return
	}

	member, err := c.service.Register(ctx.Request.Context(), &payload)
	if err != nil {
		switch err {
		case service.ErrMemberExists:
			response.Error(ctx, http.StatusConflict, err.Error(), nil)
		default:
			c.appCtx.Logger().Error(ctx, "Failed to register member", "error", err)
			response.Error(ctx, http.StatusInternalServerError, "Failed to register member", nil)
		}
		return
	}

	response.Success(ctx, "Registration successful", member)
}

func (c *MemberAuthController) login(ctx *gin.Context) {
	var payload service.LoginPayload
	if err := ctx.ShouldBindJSON(&payload); err != nil {
		response.Error(ctx, http.StatusBadRequest, "Invalid request payload", err.Error())
		return
	}

	token, err := c.service.Login(ctx.Request.Context(), &payload)
	if err != nil {
		switch err {
		case service.ErrMemberNotFound, service.ErrInvalidPassword:
			response.Error(ctx, http.StatusUnauthorized, "Invalid credentials", nil)
		default:
			c.appCtx.Logger().Error(ctx, "Failed to login member", "error", err)
			response.Error(ctx, http.StatusInternalServerError, "Failed to login", nil)
		}
		return
	}

	response.Success(ctx, "Login successful", gin.H{"token": token})
}

func (c *MemberAuthController) linkSocialIdentity(ctx *gin.Context) {
	var payload service.LinkSocialIdentityPayload
	if err := ctx.ShouldBindJSON(&payload); err != nil {
		response.Error(ctx, http.StatusBadRequest, "Invalid request payload", err.Error())
		return
	}

	identity, err := c.service.LinkSocialIdentity(ctx.Request.Context(), &payload)
	if err != nil {
		switch err {
		case service.ErrSocialIdentityExists, service.ErrMemberNotFound:
			response.Error(ctx, http.StatusConflict, err.Error(), nil)
		default:
			c.appCtx.Logger().Error(ctx, "Failed to link social identity", "error", err)
			response.Error(ctx, http.StatusInternalServerError, "Failed to link social identity", nil)
		}
		return
	}

	response.Success(ctx, "Social identity linked successfully", identity)
} 