# GACMS版本分级配置
# 根据商业授权决策文档设计的版本配置

# 版本配置
editions:
  # 个人版不需要配置，编译时固化

  # 专业版 - 中小企业、专业网站
  professional:
    display_name: "专业版"
    description: "适合中小企业和专业网站使用"
    target_users: "中小企业、专业网站、团队协作"
    price: "¥5,000/年"
    
    # 功能模块（编译时控制）
    features:
      # 基础功能模块（继承个人版）
      - basic_content
      - basic_theme
      - basic_seo
      - basic_user
      
      # 高级功能模块
      - advanced_theme     # 高级主题（自定义CSS、主题编辑器、主题市场）
      - advanced_seo       # 高级SEO（结构化数据、站点地图、SEO分析）
      - workflow           # 工作流管理（内容审核、发布流程、团队协作）
      - api_access         # API访问（REST API、基础Webhook）
    
    # 业务限制（运行时控制）
    limits:
      max_sites: 5                     # 最大站点数
      max_admin_users: 20              # 最大管理用户数
      api_calls_per_day: 10000         # API调用次数/天
      max_file_size: 104857600         # 最大文件上传大小 (100MB)
      max_concurrent_connections: 500  # 最大并发连接数
      max_pages: 1000                  # 最大页面数
      max_posts: 2000                  # 最大文章数
    
    # 功能特性描述
    feature_list:
      - "个人版所有功能"
      - "高级主题管理（自定义CSS、主题编辑器）"
      - "高级SEO工具（结构化数据、站点地图）"
      - "工作流管理（内容审核、发布流程）"
      - "API访问权限（REST API、Webhook）"
      - "优先技术支持"

  # 商业版 - 大型企业、高流量网站
  business:
    display_name: "商业版"
    description: "适合大型企业和高流量网站使用"
    target_users: "大型企业、高流量网站、企业级应用"
    price: "¥15,000/年"
    
    # 功能模块（编译时控制）
    features:
      # 基础功能模块（继承专业版）
      - basic_content
      - basic_theme
      - basic_seo
      - basic_user
      - advanced_theme
      - advanced_seo
      - workflow
      - api_access
      
      # 企业功能模块
      - advanced_user          # 高级用户管理（角色权限、用户组、SSO）
      - enterprise_api         # 企业级API（高级集成、批量操作、GraphQL）
      - enterprise_security    # 企业安全（审计日志、安全策略、合规性）
      - enterprise_integration # 企业集成（LDAP、Active Directory）
      - enterprise_analytics   # 企业分析（高级统计、自定义报表）
    
    # 业务限制（运行时控制）
    limits:
      max_sites: -1                    # 无限制
      max_admin_users: -1              # 无限制
      api_calls_per_day: -1            # 无限制
      max_file_size: 1073741824        # 最大文件上传大小 (1GB)
      max_concurrent_connections: -1   # 无限制
      max_pages: -1                    # 无限制
      max_posts: -1                    # 无限制
    
    # 功能特性描述
    feature_list:
      - "专业版所有功能"
      - "高级用户管理（角色权限、用户组、SSO）"
      - "企业级API（高级集成、批量操作、GraphQL）"
      - "企业安全（审计日志、安全策略、合规性）"
      - "企业集成（LDAP、Active Directory）"
      - "企业分析（高级统计、自定义报表）"
      - "专属客户经理"
      - "SLA保障"

# 功能模块详细定义
feature_definitions:
  # 基础功能模块
  basic_content:
    name: "基础内容管理"
    description: "文章、页面的增删改查功能"
    category: "content"
    
  basic_theme:
    name: "基础主题支持"
    description: "预设主题选择、基本颜色和字体配置"
    category: "theme"
    
  basic_seo:
    name: "基础SEO工具"
    description: "标题、描述、关键词设置"
    category: "seo"
    
  basic_user:
    name: "基础用户管理"
    description: "用户注册、登录、基本权限管理"
    category: "user"
  
  # 高级功能模块
  advanced_theme:
    name: "高级主题管理"
    description: "自定义CSS、主题编辑器、主题市场"
    category: "theme"
    
  advanced_seo:
    name: "高级SEO工具"
    description: "结构化数据、站点地图生成、SEO分析报告"
    category: "seo"
    
  workflow:
    name: "工作流管理"
    description: "内容审核、发布流程、团队协作"
    category: "workflow"
    
  api_access:
    name: "API访问权限"
    description: "REST API、基础Webhook"
    category: "api"
    
  advanced_user:
    name: "高级用户管理"
    description: "角色权限、用户组管理、SSO集成"
    category: "user"
  
  # 企业功能模块
  enterprise_api:
    name: "企业级API"
    description: "高级集成、批量操作、GraphQL"
    category: "api"
    
  enterprise_security:
    name: "企业安全"
    description: "审计日志、安全策略、合规性"
    category: "security"
    
  enterprise_integration:
    name: "企业集成"
    description: "LDAP、Active Directory集成"
    category: "integration"
    
  enterprise_analytics:
    name: "企业分析"
    description: "高级统计、自定义报表"
    category: "analytics"

# 版本升级路径
upgrade_paths:
  personal_to_professional:
    price_difference: "¥5,000/年"
    additional_features:
      - advanced_theme
      - advanced_seo
      - workflow
    
  professional_to_business:
    price_difference: "¥10,000/年"
    additional_features:
      - advanced_user
      - enterprise_api
      - enterprise_security
      - enterprise_integration
      - enterprise_analytics
  
  personal_to_business:
    price_difference: "¥15,000/年"
    additional_features:
      - advanced_theme
      - advanced_seo
      - workflow
      - advanced_user
      - enterprise_api
      - enterprise_security
      - enterprise_integration
      - enterprise_analytics

# 编译配置
build_config:
  default_edition: "personal"  # 默认编译版本
  
  # 编译标签映射
  build_tags:
    personal: "personal"
    professional: "professional"
    business: "business"
  
  # 编译时排除规则
  exclude_patterns:
    personal:
      - "advanced_*"
      - "enterprise_*"
    professional:
      - "enterprise_*"
    business: []  # 商业版包含所有功能
