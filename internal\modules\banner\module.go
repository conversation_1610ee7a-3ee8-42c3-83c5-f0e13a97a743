/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/banner/module.go
 * @Description: DI bindings for the banner module.
 *
 * © 2025 GACMS. All rights reserved.
 */
package banner

import (
	coreSvc "gacms/internal/core/service"
	"gacms/internal/modules/banner/application/service"
	"gacms/internal/modules/banner/domain/contract"
	"gacms/internal/modules/banner/infrastructure/persistence"
	"gacms/internal/modules/banner/port/http/controller"
	"go.uber.org/fx"
)

const ModuleName = "banner"

// Recipe is the DI recipe for the banner module.
var Recipe = coreSvc.ModuleRecipe{
	Name: ModuleName,
	Options: fx.Options(
		fx.Provide(
			// Persistence
			persistence.NewGormBannerRepository,
			persistence.NewGormBannerPositionRepository,

			// Application Services
			service.NewBannerService,
		),

		// HTTP Controller (provided for routing)
		fx.Provide(
			fx.Annotate(
				controller.NewBannerController,
				fx.ResultTags(`name:"routable_controller"`),
			),
		),
	),
} 