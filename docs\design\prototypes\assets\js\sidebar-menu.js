﻿/**
 * @file: sidebar-menu.js
 * @description: GACMS 侧边栏菜单逻辑
 * @author: <PERSON><PERSON> <<EMAIL>>
 * @copyright: Copyright (c) 2025 Cion Nieh
 */

// 子菜单数据
const submenus = {
    contentSubmenu: [
        { title: '内容页面', items: [
            { name: '文章管理', url: 'posts.html', icon: 'fas fa-feather-alt' },
            { name: '页面管理', url: 'pages.html', icon: 'far fa-file-alt' },
            { name: '专题管理', url: 'topics.html', icon: 'fas fa-book-open' }
        ]},
        { title: '内容组织', items: [
            { name: '内容模型', url: 'content_models.html', icon: 'fas fa-cubes' },
            { name: '栏目管理', url: 'categories.html', icon: 'fas fa-folder-open' },
            { name: '菜单管理', url: 'menus.html', icon: 'fas fa-bars' },
            { name: '标签管理', url: 'tags.html', icon: 'fas fa-tags' },
            { name: '评论管理', url: 'comments.html', icon: 'fas fa-comments' }
        ]},
        { title: '资源管理', items: [
            { name: '媒体库', url: 'medias.html', icon: 'fas fa-photo-video' },
            { name: '焦点图', url: 'slides.html', icon: 'fas fa-images' }
        ]}
    ],
    appearanceSubmenu: [
        { title: '主题', items: [
            { name: '主题管理', url: 'themes.html', icon: 'fas fa-paint-brush' },
            { name: '主题市场', url: 'theme_market.html', icon: 'fas fa-store' }
        ]},
        { title: '页面结构', items: [
            { name: '布局管理', url: 'layouts.html', icon: 'fas fa-columns' },
            { name: '模板管理', url: 'templates.html', icon: 'fas fa-file-code' }
        ]},
        { title: '复用组件', items: [
            { name: '区块管理', url: 'blocks.html', icon: 'fas fa-th-large' },
            { name: '组件管理', url: 'components.html', icon: 'fas fa-puzzle-piece' },
            { name: '小工具', url: 'widgets.html', icon: 'fas fa-tools' }
        ]}
    ],
    userSubmenu: [
        { title: '后台用户', items: [
            { name: '用户管理', url: 'users.html', icon: 'fas fa-user-shield' },
            { name: '角色权限', url: 'role_perms.html', icon: 'fas fa-user-tag' }
        ]},
        { title: '前台用户', items: [
            { name: '客户管理', url: 'clients.html', icon: 'fas fa-user-friends' },
            { name: '客户权限', url: 'client_perms.html', icon: 'fas fa-user-check' },
            { name: '客户字段', url: 'client_fields.html', icon: 'fas fa-user-edit' }
        ]}
    ],
    siteSubmenu: [
        { title: '站点管理', items: [
            { name: '多站点管理', url: 'sites.html', icon: 'fas fa-sitemap' },
            { name: '域名绑定', url: 'domains.html', icon: 'fas fa-globe' }
        ]},
        { title: '优化与安全', items: [
            { name: 'SEO设置', url: 'seo_set.html', icon: 'fas fa-search-dollar' },
            { name: '性能优化', url: 'performance.html', icon: 'fas fa-tachometer-alt' },
            { name: "安全设置", url: "security.html", icon: "fas fa-shield-alt"},
            { name: '访问控制', url: 'access_controls.html', icon: 'fas fa-user-lock' }
        ]},
        { title: '高级配置', items: [
            { name: '缓存管理', url: 'caches.html', icon: 'fas fa-broom' },
            { name: '计划任务', url: 'tasks.html', icon: 'fas fa-calendar-alt' },
            { name: '语言设置', url: 'languages.html', icon: 'fas fa-language' }
        ]}
    ],
    marketingSubmenu: [
        { title: '营销工具', items: [
            { name: '工作流', url: 'workflows.html', icon: 'fas fa-project-diagram' },
            { name: '邮件营销', url: 'email_marketing.html', icon: 'fas fa-envelope-open-text' },
            { name: '促销活动', url: 'promotions.html', icon: 'fas fa-percentage' }
        ]},
        { title: '客户互动', items: [
            { name: '表单管理', url: 'client_forms.html', icon: 'fab fa-wpforms' },
            { name: '在线客服', url: 'live_chat.html', icon: 'fas fa-headset' },
            { name: '问卷调查', url: 'surveys.html', icon: 'fas fa-poll-h' }
        ]}
    ],
    reportSubmenu: [
        { title: '数据报告', items: [
            { name: '数据概览', url: 'data_stats.html', icon: 'fas fa-chart-line' },
            { name: '流量分析', url: 'flow_stats.html', icon: 'fas fa-chart-bar' },
            { name: '内容分析', url: 'content_stats.html', icon: 'far fa-file-chart-pie' },
            { name: '客户分析', url: 'client_stats.html', icon: 'fas fa-users-class' },
            { name: 'SEO分析', url: 'seo_stats.html', icon: 'fas fa-chart-area' },
            { name: '数据导出', url: 'data_ex.html', icon: 'fas fa-file-export' }
        ]}
    ],
    settingsSubmenu: [
        { title: '系统', items: [
            { name: '全局设置', url: 'system_set.html', icon: 'fas fa-sliders-h' },
            { name: 'API管理', url: 'apis.html', icon: 'fas fa-plug' },
            { name: '日志管理', url: 'logs.html', icon: 'fas fa-history' },
            { name: '备份恢复', url: 'backups.html', icon: 'fas fa-save' },
            { name: '系统更新', url: 'updates.html', icon: 'fas fa-cloud-download-alt' },
            { name: '通知管理', url: 'notifications.html', icon: 'fas fa-bell' }
        ]},
        { title: '扩展', items: [
            { name: '模块管理', url: 'modules.html', icon: 'fas fa-cogs' },
            { name: '插件管理', url: 'plugins.html', icon: 'fas fa-box-open' },
            { name: '插件市场', url: 'plugin_market.html', icon: 'fas fa-shopping-basket' },
            { name: '应用市场', url: 'app_market.html', icon: 'fas fa-store-alt' }
        ]},
        { title: '开发者', items: [
            { name: '主题开发', url: 'theme_dev.html', icon: 'fas fa-drafting-compass' },
            { name: '事件总线', url: 'hooks.html', icon: 'fas fa-random' },
            { name: '数据库', url: 'database.html', icon: 'fas fa-database' },
            { name: 'Webhooks', url: 'webhooks.html', icon: 'fas fa-network-wired' },
            { name: '开发文档', url: 'dev_docs.html', icon: 'fas fa-book-reader' }
        ]}
    ]
};

/**
 * @function initSidebar
 * @description 初始化侧边栏交互（移动端菜单切换）
 */
function initSidebar() {
    // 移动端菜单切换
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle, .menu-toggle, #menuToggle');
    const sidebar = document.querySelector('.sidebar');
    const overlay = document.querySelector('.sidebar-overlay, #sidebarOverlay');

    if (mobileMenuToggle && sidebar) {
        mobileMenuToggle.addEventListener('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            sidebar.classList.toggle('active');
            sidebar.classList.toggle('mobile-open');
            if (overlay) {
                overlay.classList.toggle('active');
                document.body.classList.toggle('sidebar-open');
            }
        });
    }

    // 点击遮罩层关闭侧边栏
    if (overlay && sidebar) {
        overlay.addEventListener('click', () => {
            sidebar.classList.remove('active', 'mobile-open');
            overlay.classList.remove('active');
            document.body.classList.remove('sidebar-open');
        });
    }
}

/**
 * @function renderSubmenu
 * @description 渲染子菜单内容 - 使用与主菜单一致的深色风格
 */
function renderSubmenu(submenuKey) {
    const submenuContainer = document.getElementById('submenuContainer');
    if (!submenuContainer || !submenus[submenuKey]) return;

    const menuData = submenus[submenuKey];
    let html = '';

    // 构建子菜单HTML - 使用深灰色背景和浅色文字
    menuData.forEach(group => {
        html += `<div class="submenu-group mb-6">`;
        html += `<h3 class="submenu-title text-neutral-700 text-sm font-semibold px-4 py-2">${group.title}</h3>`;
        html += `<ul class="submenu-list">`;
        group.items.forEach(item => {
            html += `<li class="submenu-item">`;
            html += `<a href="${item.url}" class="submenu-link flex items-center px-4 py-2 rounded">`;
            html += `<i class="${item.icon} mr-3 w-5 text-center"></i>`;
            html += `<span>${item.name}</span>`;
            html += `</a>`;
            html += `</li>`;
        });
        html += `</ul></div>`;
    });

    submenuContainer.innerHTML = html;
}

// 全局变量用于控制菜单状态
let currentSubmenu = null;
let hideTimer = null;

/**
 * @function showSubmenu
 * @description 显示子菜单 - 修复位置问题
 */
function showSubmenu(submenuKey, element) {
    clearTimeout(hideTimer);
    
    // 1. 渲染子菜单内容
    renderSubmenu(submenuKey);
    
    const submenuContainer = document.getElementById('submenuContainer');
    const submenuOverlay = document.getElementById('submenuOverlay');
    
    if (!submenuContainer) return;
    
    // 3. 显示子菜单和遮罩层
    submenuContainer.classList.remove('hidden');
    if (submenuOverlay) {
        submenuOverlay.classList.remove('hidden');
    }
    
    currentSubmenu = submenuKey;
}

/**
 * @function hideSubmenu
 * @description 隐藏子菜单
 */
function hideSubmenu() {
    const submenuContainer = document.getElementById('submenuContainer');
    const submenuOverlay = document.getElementById('submenuOverlay');
    
    if (!submenuContainer) return;
    
    submenuContainer.classList.add('hidden');
    if (submenuOverlay) {
        submenuOverlay.classList.add('hidden');
    }
    currentSubmenu = null;
}

/**
 * @function initDynamicSubmenu
 * @description 初始化右侧动态子菜单逻辑 - 彻底解决闪烁问题
 */
function initDynamicSubmenu() {
    const submenuContainer = document.getElementById('submenuContainer');
    const submenuClose = document.getElementById('submenuClose');
    const submenuOverlay = document.getElementById('submenuOverlay');

    // 修复1: 使用更可靠的事件绑定方式
    document.querySelectorAll('.sidebar-menu .has-submenu').forEach(item => {
        const link = item.querySelector('a');
        const submenuKey = link.getAttribute('data-submenu');
        
        if (!submenuKey) return;

        // 点击事件（移动端支持）
        link.addEventListener('click', function(e) {
            if (window.innerWidth >= 1024) {
                e.preventDefault(); // 桌面端阻止默认跳转
            }
            
            // 如果当前子菜单已显示，则隐藏
            if (currentSubmenu === submenuKey) {
                hideSubmenu();
            } else {
                showSubmenu(submenuKey, item);
            }
        });
    });

    // 点击遮罩层关闭子菜单
    if (submenuOverlay) {
        submenuOverlay.addEventListener('click', () => {
            hideSubmenu();
        });
    }

    // 点击关闭按钮
    if (submenuClose) {
        submenuClose.addEventListener('click', function () {
            hideSubmenu();
        });
    }
}

/**
 * @function highlightCurrentPageMenuItem
 * @description 高亮当前页面对应的菜单项
 */
function highlightCurrentPageMenuItem() {
    const currentPath = window.location.pathname;
    const currentPage = currentPath.split('/').pop() || 'dashboard.html';
    const menuLinks = document.querySelectorAll('.sidebar a');

    // 移除旧高亮
    menuLinks.forEach(link => {
        link.classList.remove('active');
    });

    // 查找匹配的链接
    menuLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (!href) return;
        
        // 简化匹配逻辑
        const linkPage = href.split('/').pop() || href;
        if (linkPage === currentPage || currentPath.includes(linkPage)) {
            link.classList.add('active');
            
            // 高亮父级菜单项
            const parentItem = link.closest('.has-submenu');
            if (parentItem) {
                parentItem.classList.add('active');
            }
        }
    });
}

/**
 * @function loadSidebarMenu
 * @description 加载侧边栏模板（仅首次加载时使用）
 * @param {string} selector - 侧边栏容器选择器
 */
function loadSidebarMenu(selector) {
    const sidebarContainer = document.querySelector(selector);
    if (!sidebarContainer) {
        console.error('找不到侧边栏容器:', selector);
        return;
    }

    // 动态加载外部模板
    fetch('./sidebar_menu.html')
        .then(response => {
            if (!response.ok) throw new Error('无法加载侧边栏模板');
            return response.text();
        })
        .then(html => {
            sidebarContainer.innerHTML = html;
            initSidebar();
            initDynamicSubmenu();
            highlightCurrentPageMenuItem();
        })
        .catch(err => {
            console.error('加载侧边栏失败:', err);
            initSidebar();
            initDynamicSubmenu();
            highlightCurrentPageMenuItem();
        });
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function () {
    const sidebar = document.querySelector('.sidebar.load-template');
    if (sidebar) {
        loadSidebarMenu('.sidebar.load-template');
    } else {
        initSidebar();
        initDynamicSubmenu();
        highlightCurrentPageMenuItem();
    }
    
    // 响应式处理：窗口大小变化时调整子菜单行为
    window.addEventListener('resize', function() {
        const submenuContainer = document.getElementById('submenuContainer');
        if (window.innerWidth < 1024 && submenuContainer && !submenuContainer.classList.contains('hidden')) {
            submenuContainer.classList.add('hidden');
            const submenuOverlay = document.getElementById('submenuOverlay');
            if (submenuOverlay) {
                submenuOverlay.classList.add('hidden');
            }
        }
    });
});

// 窗口加载后确保子菜单高亮
window.addEventListener('load', function () {
    highlightCurrentPageMenuItem();
});