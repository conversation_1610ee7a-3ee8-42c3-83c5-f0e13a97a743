/*
Author: <PERSON><PERSON>eh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package controller

import (
	"context"
	"net/http"
	"strconv"

	"gacms/internal/core/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// PerformanceController 性能监控控制器
type PerformanceController struct {
	perfManager     service.ModulePerformanceManager
	preloadManager  service.PreloadStrategyManager
	moduleFactory   *service.ModuleProxyFactory
	logger          *zap.Logger
}

// NewPerformanceController 创建性能监控控制器
func NewPerformanceController(
	perfManager service.ModulePerformanceManager,
	preloadManager service.PreloadStrategyManager,
	moduleFactory *service.ModuleProxyFactory,
	logger *zap.Logger,
) *PerformanceController {
	return &PerformanceController{
		perfManager:    perfManager,
		preloadManager: preloadManager,
		moduleFactory:  moduleFactory,
		logger:         logger,
	}
}

// RegisterRoutes 注册路由
func (c *PerformanceController) RegisterRoutes(group *gin.RouterGroup) {
	perf := group.Group("/performance")
	{
		// 性能统计
		perf.GET("/stats", c.GetStats)
		perf.GET("/stats/:module", c.GetModuleStats)
		perf.GET("/overall", c.GetOverallStats)
		
		// 缓存管理
		perf.GET("/cache", c.GetCacheStats)
		perf.POST("/cache/clear", c.ClearCache)
		perf.PUT("/cache/enable", c.EnableCache)
		perf.PUT("/cache/disable", c.DisableCache)
		
		// 预加载管理
		perf.GET("/preload", c.GetPreloadList)
		perf.POST("/preload", c.AddToPreload)
		perf.DELETE("/preload/:module", c.RemoveFromPreload)
		perf.POST("/preload/execute", c.ExecutePreload)
		
		// 性能建议
		perf.GET("/recommendations", c.GetRecommendations)
		perf.GET("/preload-recommendations", c.GetPreloadRecommendations)
		
		// 模块工厂统计
		perf.GET("/factory", c.GetFactoryStats)
	}
}

// GetStats 获取性能统计
func (c *PerformanceController) GetStats(ctx *gin.Context) {
	stats := c.perfManager.GetOverallStats()
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   stats,
	})
}

// GetModuleStats 获取指定模块的统计
func (c *PerformanceController) GetModuleStats(ctx *gin.Context) {
	moduleName := ctx.Param("module")
	
	stats := c.perfManager.GetLoadStats(moduleName)
	if stats == nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": "Module stats not found",
		})
		return
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   stats,
	})
}

// GetOverallStats 获取整体统计
func (c *PerformanceController) GetOverallStats(ctx *gin.Context) {
	stats := c.perfManager.GetOverallStats()
	factoryStats := c.moduleFactory.GetStats()
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"performance": stats,
			"factory":     factoryStats,
		},
	})
}

// GetCacheStats 获取缓存统计
func (c *PerformanceController) GetCacheStats(ctx *gin.Context) {
	stats := c.perfManager.GetCacheStats()
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   stats,
	})
}

// ClearCache 清空缓存
func (c *PerformanceController) ClearCache(ctx *gin.Context) {
	c.perfManager.ClearCache()
	
	c.logger.Info("Cache cleared by admin request")
	
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Cache cleared successfully",
	})
}

// EnableCache 启用缓存
func (c *PerformanceController) EnableCache(ctx *gin.Context) {
	c.perfManager.SetCacheEnabled(true)
	
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Cache enabled successfully",
	})
}

// DisableCache 禁用缓存
func (c *PerformanceController) DisableCache(ctx *gin.Context) {
	c.perfManager.SetCacheEnabled(false)
	
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Cache disabled successfully",
	})
}

// GetPreloadList 获取预加载列表
func (c *PerformanceController) GetPreloadList(ctx *gin.Context) {
	list := c.perfManager.GetPreloadList()
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"preload_list": list,
			"count":        len(list),
		},
	})
}

// AddToPreload 添加到预加载列表
func (c *PerformanceController) AddToPreload(ctx *gin.Context) {
	var req struct {
		ModuleName string `json:"module_name" binding:"required"`
		Priority   int    `json:"priority"`
	}
	
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}
	
	if req.Priority == 0 {
		req.Priority = 100 // 默认优先级
	}
	
	c.perfManager.AddToPreloadList(req.ModuleName, req.Priority)
	
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Module added to preload list",
	})
}

// RemoveFromPreload 从预加载列表移除
func (c *PerformanceController) RemoveFromPreload(ctx *gin.Context) {
	moduleName := ctx.Param("module")
	
	c.perfManager.RemoveFromPreloadList(moduleName)
	
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Module removed from preload list",
	})
}

// ExecutePreload 执行预加载
func (c *PerformanceController) ExecutePreload(ctx *gin.Context) {
	var req struct {
		Timing string `json:"timing"`
	}
	
	if err := ctx.ShouldBindJSON(&req); err != nil {
		req.Timing = "demand" // 默认按需预加载
	}
	
	// 执行预加载
	err := c.perfManager.ExecutePreload(context.Background())
	if err != nil {
		c.logger.Error("Failed to execute preload", zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to execute preload: " + err.Error(),
		})
		return
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Preload executed successfully",
	})
}

// GetRecommendations 获取性能优化建议
func (c *PerformanceController) GetRecommendations(ctx *gin.Context) {
	recommendations := c.perfManager.GetPerformanceRecommendations()
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"recommendations": recommendations,
			"count":           len(recommendations),
		},
	})
}

// GetPreloadRecommendations 获取预加载建议
func (c *PerformanceController) GetPreloadRecommendations(ctx *gin.Context) {
	recommendations := c.preloadManager.GetPreloadRecommendations()
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"recommendations": recommendations,
			"count":           len(recommendations),
		},
	})
}

// GetFactoryStats 获取模块工厂统计
func (c *PerformanceController) GetFactoryStats(ctx *gin.Context) {
	stats := c.moduleFactory.GetStats()
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   stats,
	})
}
