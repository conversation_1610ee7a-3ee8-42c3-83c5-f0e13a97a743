<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - Webhooks 管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button {
            transition: all 0.3s ease;
        }
        
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .log-viewer {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        
        .log-viewer .log-line {
            padding: 2px 8px;
            border-bottom: 1px solid rgba(75, 85, 99, 0.2);
        }
        
        .log-viewer .log-line:hover {
            background-color: rgba(75, 85, 99, 0.1);
        }
        
        .log-viewer .log-error {
            color: #ef4444;
        }
        
        .log-viewer .log-warning {
            color: #f59e0b;
        }
        
        .log-viewer .log-info {
            color: #3b82f6;
        }
        
        .log-viewer .log-debug {
            color: #10b981;
        }
        
        .tab-active {
            border-bottom: 2px solid #3b82f6;
            color: #3b82f6;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .webhook-card {
            transition: all 0.3s ease;
        }
        
        .webhook-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .status-active {
            background-color: rgba(16, 185, 129, 0.2);
            color: #10b981;
        }
        
        .status-inactive {
            background-color: rgba(107, 114, 128, 0.2);
            color: #9CA3AF;
        }
        
        .status-error {
            background-color: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-4">
                <a href="dashboard.html" class="text-gray-400 hover:text-white">首页</a>
                <span class="mx-2 text-gray-600">/</span>
                <a href="#" class="text-gray-400 hover:text-white">系统</a>
                <span class="mx-2 text-gray-600">/</span>
                <span class="text-white">Webhooks 管理</span>
            </div>
            
            <!-- 页面标题 -->
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold text-white relative pl-3 section-title">Webhooks 管理</h2>
                <div class="flex space-x-3">
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center action-button" id="createWebhookBtn">
                        <i class="fas fa-plus mr-2"></i> 创建 Webhook
                    </button>
                </div>
            </div>
            
            <!-- Webhook 状态概览 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                    <div class="flex items-center">
                        <div class="rounded-full bg-blue-500/10 p-3 mr-4">
                            <i class="fas fa-link text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">总 Webhooks</h3>
                            <p class="text-lg font-bold">12</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                    <div class="flex items-center">
                        <div class="rounded-full bg-green-500/10 p-3 mr-4">
                            <i class="fas fa-check-circle text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">活跃 Webhooks</h3>
                            <p class="text-lg font-bold text-green-400">8</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                    <div class="flex items-center">
                        <div class="rounded-full bg-red-500/10 p-3 mr-4">
                            <i class="fas fa-exclamation-circle text-red-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">失败 Webhooks</h3>
                            <p class="text-lg font-bold text-red-400">2</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                    <div class="flex items-center">
                        <div class="rounded-full bg-yellow-500/10 p-3 mr-4">
                            <i class="fas fa-bolt text-yellow-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">今日触发次数</h3>
                            <p class="text-lg font-bold">156</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 标签页导航 -->
            <div class="bg-gray-800/20 border border-gray-700 rounded-xl overflow-hidden mb-6">
                <div class="border-b border-gray-700">
                    <div class="flex">
                        <button class="tab-button px-6 py-4 text-blue-400 tab-active" data-tab="all-webhooks">
                            <i class="fas fa-link mr-2"></i> 所有 Webhooks
                        </button>
                        <button class="tab-button px-6 py-4 text-gray-400 hover:text-white" data-tab="delivery-logs">
                            <i class="fas fa-history mr-2"></i> 传递日志
                        </button>
                        <button class="tab-button px-6 py-4 text-gray-400 hover:text-white" data-tab="settings">
                            <i class="fas fa-cog mr-2"></i> 设置
                        </button>
                    </div>
                </div>
                
                <!-- 所有 Webhooks 标签内容 -->
                <div id="all-webhooks" class="tab-content active p-6">
                    <div class="flex justify-between items-center mb-4">
                        <div class="relative">
                            <input type="text" placeholder="搜索 Webhook..." class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 pl-10 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        <div class="flex space-x-2">
                            <select class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option>所有状态</option>
                                <option>活跃</option>
                                <option>非活跃</option>
                                <option>错误</option>
                            </select>
                            <select class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option>所有事件</option>
                                <option>内容创建</option>
                                <option>用户注册</option>
                                <option>评论发布</option>
                                <option>系统更新</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Webhook 列表 -->
                    <div class="space-y-4">
                        <!-- Webhook 卡片 1 -->
                        <div class="bg-gray-800/30 border border-gray-700 rounded-xl p-5 webhook-card">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                                <div class="mb-4 md:mb-0">
                                    <div class="flex items-center mb-2">
                                        <h3 class="text-lg font-semibold mr-3">内容更新通知</h3>
                                        <span class="status-badge status-active">
                                            <i class="fas fa-circle text-xs mr-1"></i> 活跃
                                        </span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-2">https://example.com/api/content-webhook</p>
                                    <div class="flex flex-wrap gap-2">
                                        <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-md text-xs">内容创建</span>
                                        <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-md text-xs">内容更新</span>
                                        <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-md text-xs">内容删除</span>
                                    </div>
                                </div>
                                <div class="flex flex-col md:items-end">
                                    <div class="text-sm text-gray-400 mb-2">最近触发: 10分钟前</div>
                                    <div class="flex space-x-2">
                                        <button class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded-lg text-sm">
                                            <i class="fas fa-paper-plane mr-1"></i> 测试
                                        </button>
                                        <button class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded-lg text-sm">
                                            <i class="fas fa-edit mr-1"></i> 编辑
                                        </button>
                                        <button class="bg-red-600/30 hover:bg-red-600/50 text-red-400 px-3 py-1 rounded-lg text-sm">
                                            <i class="fas fa-trash-alt mr-1"></i> 删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Webhook 卡片 2 -->
                        <div class="bg-gray-800/30 border border-gray-700 rounded-xl p-5 webhook-card">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                                <div class="mb-4 md:mb-0">
                                    <div class="flex items-center mb-2">
                                        <h3 class="text-lg font-semibold mr-3">用户活动跟踪</h3>
                                        <span class="status-badge status-active">
                                            <i class="fas fa-circle text-xs mr-1"></i> 活跃
                                        </span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-2">https://analytics.example.org/webhooks/user-activity</p>
                                    <div class="flex flex-wrap gap-2">
                                        <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded-md text-xs">用户注册</span>
                                        <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded-md text-xs">用户登录</span>
                                        <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded-md text-xs">密码重置</span>
                                    </div>
                                </div>
                                <div class="flex flex-col md:items-end">
                                    <div class="text-sm text-gray-400 mb-2">最近触发: 25分钟前</div>
                                    <div class="flex space-x-2">
                                        <button class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded-lg text-sm">
                                            <i class="fas fa-paper-plane mr-1"></i> 测试
                                        </button>
                                        <button class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded-lg text-sm">
                                            <i class="fas fa-edit mr-1"></i> 编辑
                                        </button>
                                        <button class="bg-red-600/30 hover:bg-red-600/50 text-red-400 px-3 py-1 rounded-lg text-sm">
                                            <i class="fas fa-trash-alt mr-1"></i> 删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Webhook 卡片 3 -->
                        <div class="bg-gray-800/30 border border-gray-700 rounded-xl p-5 webhook-card">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                                <div class="mb-4 md:mb-0">
                                    <div class="flex items-center mb-2">
                                        <h3 class="text-lg font-semibold mr-3">评论通知服务</h3>
                                        <span class="status-badge status-error">
                                            <i class="fas fa-circle text-xs mr-1"></i> 错误
                                        </span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-2">https://notifications.example.net/comments-hook</p>
                                    <div class="flex flex-wrap gap-2">
                                        <span class="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded-md text-xs">评论发布</span>
                                        <span class="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded-md text-xs">评论回复</span>
                                    </div>
                                </div>
                                <div class="flex flex-col md:items-end">
                                    <div class="text-sm text-red-400 mb-2">错误: 连接超时 (3小时前)</div>
                                    <div class="flex space-x-2">
                                        <button class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded-lg text-sm">
                                            <i class="fas fa-paper-plane mr-1"></i> 测试
                                        </button>
                                        <button class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded-lg text-sm">
                                            <i class="fas fa-edit mr-1"></i> 编辑
                                        </button>
                                        <button class="bg-red-600/30 hover:bg-red-600/50 text-red-400 px-3 py-1 rounded-lg text-sm">
                                            <i class="fas fa-trash-alt mr-1"></i> 删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Webhook 卡片 4 -->
                        <div class="bg-gray-800/30 border border-gray-700 rounded-xl p-5 webhook-card">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                                <div class="mb-4 md:mb-0">
                                    <div class="flex items-center mb-2">
                                        <h3 class="text-lg font-semibold mr-3">系统状态监控</h3>
                                        <span class="status-badge status-inactive">
                                            <i class="fas fa-circle text-xs mr-1"></i> 非活跃
                                        </span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-2">https://monitor.example.io/system-status</p>
                                    <div class="flex flex-wrap gap-2">
                                        <span class="bg-red-500/20 text-red-400 px-2 py-1 rounded-md text-xs">系统错误</span>
                                        <span class="bg-red-500/20 text-red-400 px-2 py-1 rounded-md text-xs">系统更新</span>
                                        <span class="bg-red-500/20 text-red-400 px-2 py-1 rounded-md text-xs">性能警告</span>
                                    </div>
                                </div>
                                <div class="flex flex-col md:items-end">
                                    <div class="text-sm text-gray-400 mb-2">手动停用于 2023-06-15</div>
                                    <div class="flex space-x-2">
                                        <button class="bg-green-600/30 hover:bg-green-600/50 text-green-400 px-3 py-1 rounded-lg text-sm">
                                            <i class="fas fa-play mr-1"></i> 启用
                                        </button>
                                        <button class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded-lg text-sm">
                                            <i class="fas fa-edit mr-1"></i> 编辑
                                        </button>
                                        <button class="bg-red-600/30 hover:bg-red-600/50 text-red-400 px-3 py-1 rounded-lg text-sm">
                                            <i class="fas fa-trash-alt mr-1"></i> 删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 分页控制 -->
                    <div class="flex justify-between items-center mt-6">
                        <div class="text-sm text-gray-400">
                            显示 1-4 / 共 12 个 Webhooks
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="px-3 py-1 bg-blue-600 text-white rounded-md">1</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">2</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">3</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 传递日志标签内容 -->
                <div id="delivery-logs" class="tab-content p-6">
                    <div class="flex justify-between items-center mb-4">
                        <div class="relative">
                            <input type="text" placeholder="搜索日志..." class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 pl-10 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        <div class="flex space-x-2">
                            <select class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option>所有 Webhooks</option>
                                <option>内容更新通知</option>
                                <option>用户活动跟踪</option>
                                <option>评论通知服务</option>
                                <option>系统状态监控</option>
                            </select>
                            <select class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option>所有状态</option>
                                <option>成功</option>
                                <option>失败</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="bg-gray-800/30 text-left">
                                    <th class="px-4 py-3 font-medium">ID</th>
                                    <th class="px-4 py-3 font-medium">Webhook</th>
                                    <th class="px-4 py-3 font-medium">事件</th>
                                    <th class="px-4 py-3 font-medium">状态</th>
                                    <th class="px-4 py-3 font-medium">响应代码</th>
                                    <th class="px-4 py-3 font-medium">时间</th>
                                    <th class="px-4 py-3 font-medium">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                    <td class="px-4 py-3">#12458</td>
                                    <td class="px-4 py-3">内容更新通知</td>
                                    <td class="px-4 py-3"><span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-md text-xs">内容创建</span></td>
                                    <td class="px-4 py-3"><span class="text-green-400"><i class="fas fa-check-circle mr-1"></i> 成功</span></td>
                                    <td class="px-4 py-3">200</td>
                                    <td class="px-4 py-3">10分钟前</td>
                                    <td class="px-4 py-3">
                                        <button class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                    <td class="px-4 py-3">#12457</td>
                                    <td class="px-4 py-3">用户活动跟踪</td>
                                    <td class="px-4 py-3"><span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded-md text-xs">用户登录</span></td>
                                    <td class="px-4 py-3"><span class="text-green-400"><i class="fas fa-check-circle mr-1"></i> 成功</span></td>
                                    <td class="px-4 py-3">200</td>
                                    <td class="px-4 py-3">25分钟前</td>
                                    <td class="px-4 py-3">
                                        <button class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                    <td class="px-4 py-3">#12456</td>
                                    <td class="px-4 py-3">评论通知服务</td>
                                    <td class="px-4 py-3"><span class="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded-md text-xs">评论发布</span></td>
                                    <td class="px-4 py-3"><span class="text-red-400"><i class="fas fa-times-circle mr-1"></i> 失败</span></td>
                                    <td class="px-4 py-3">408</td>
                                    <td class="px-4 py-3">3小时前</td>
                                    <td class="px-4 py-3">
                                        <button class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                    <td class="px-4 py-3">#12455</td>
                                    <td class="px-4 py-3">内容更新通知</td>
                                    <td class="px-4 py-3"><span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-md text-xs">内容更新</span></td>
                                    <td class="px-4 py-3"><span class="text-green-400"><i class="fas fa-check-circle mr-1"></i> 成功</span></td>
                                    <td class="px-4 py-3">200</td>
                                    <td class="px-4 py-3">5小时前</td>
                                    <td class="px-4 py-3">
                                        <button class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                    <td class="px-4 py-3">#12454</td>
                                    <td class="px-4 py-3">用户活动跟踪</td>
                                    <td class="px-4 py-3"><span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded-md text-xs">用户注册</span></td>
                                    <td class="px-4 py-3"><span class="text-green-400"><i class="fas fa-check-circle mr-1"></i> 成功</span></td>
                                    <td class="px-4 py-3">201</td>
                                    <td class="px-4 py-3">昨天 15:42</td>
                                    <td class="px-4 py-3">
                                        <button class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页控制 -->
                    <div class="flex justify-between items-center mt-6">
                        <div class="text-sm text-gray-400">
                            显示 1-5 / 共 156 条日志
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="px-3 py-1 bg-blue-600 text-white rounded-md">1</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">2</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">3</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">4</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 设置标签内容 -->
                <div id="settings" class="tab-content p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 全局设置 -->
                        <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                            <h3 class="text-lg font-semibold mb-4">全局设置</h3>
                            
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-gray-300 mb-2">默认超时时间 (秒)</label>
                                    <input type="number" value="30" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                
                                <div>
                                    <label class="block text-gray-300 mb-2">最大重试次数</label>
                                    <input type="number" value="3" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                
                                <div>
                                    <label class="block text-gray-300 mb-2">日志保留天数</label>
                                    <input type="number" value="30" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                
                                <div class="flex items-center">
                                    <input type="checkbox" id="enableLogging" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                    <label for="enableLogging" class="ml-2 text-gray-300">启用详细日志记录</label>
                                </div>
                                
                                <div class="flex items-center">
                                    <input type="checkbox" id="enableNotifications" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                    <label for="enableNotifications" class="ml-2 text-gray-300">启用失败通知</label>
                                </div>
                                
                                <div class="pt-2">
                                    <button class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center action-button">
                                        <i class="fas fa-save mr-2"></i> 保存设置
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 安全设置 -->
                        <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                            <h3 class="text-lg font-semibold mb-4">安全设置</h3>
                            
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-gray-300 mb-2">签名密钥</label>
                                    <div class="relative">
                                        <input type="password" value="••••••••••••••••" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <button class="absolute right-2 top-2 text-gray-400 hover:text-white">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-gray-300 mb-2">IP 白名单 (每行一个)</label>
                                    <textarea class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent h-24">***********
***********/24</textarea>
                                </div>
                                
                                <div>
                                    <label class="block text-gray-300 mb-2">签名算法</label>
                                    <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option>SHA-256</option>
                                        <option>SHA-384</option>
                                        <option>SHA-512</option>
                                    </select>
                                </div>
                                
                                <div class="flex items-center">
                                    <input type="checkbox" id="enforceSSL" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                    <label for="enforceSSL" class="ml-2 text-gray-300">强制使用 HTTPS</label>
                                </div>
                                
                                <div class="flex items-center">
                                    <input type="checkbox" id="validateSignature" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                    <label for="validateSignature" class="ml-2 text-gray-300">验证传入的 Webhook 签名</label>
                                </div>
                                
                                <div class="pt-2">
                                    <button class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center action-button">
                                        <i class="fas fa-save mr-2"></i> 保存安全设置
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 创建 Webhook 模态框 (隐藏) -->
            <div id="createWebhookModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
                <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 w-full max-w-2xl">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-xl font-bold">创建新 Webhook</h3>
                        <button id="closeModalBtn" class="text-gray-400 hover:text-white">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-gray-300 mb-2">Webhook 名称</label>
                            <input type="text" placeholder="例如：内容更新通知" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label class="block text-gray-300 mb-2">目标 URL</label>
                            <input type="url" placeholder="https://example.com/webhook" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label class="block text-gray-300 mb-2">描述 (可选)</label>
                            <textarea placeholder="简要描述此 Webhook 的用途..." class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent h-20"></textarea>
                        </div>
                        
                        <div>
                            <label class="block text-gray-300 mb-2">触发事件</label>
                            <div class="grid grid-cols-2 gap-2">
                                <label class="flex items-center p-2 bg-gray-700 rounded-lg">
                                    <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600">
                                    <span class="ml-2 text-gray-300">内容创建</span>
                                </label>
                                <label class="flex items-center p-2 bg-gray-700 rounded-lg">
                                    <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600">
                                    <span class="ml-2 text-gray-300">内容更新</span>
                                </label>
                                <label class="flex items-center p-2 bg-gray-700 rounded-lg">
                                    <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600">
                                    <span class="ml-2 text-gray-300">内容删除</span>
                                </label>
                                <label class="flex items-center p-2 bg-gray-700 rounded-lg">
                                    <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600">
                                    <span class="ml-2 text-gray-300">用户注册</span>
                                </label>
                                <label class="flex items-center p-2 bg-gray-700 rounded-lg">
                                    <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600">
                                    <span class="ml-2 text-gray-300">用户登录</span>
                                </label>
                                <label class="flex items-center p-2 bg-gray-700 rounded-lg">
                                    <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600">
                                    <span class="ml-2 text-gray-300">评论发布</span>
                                </label>
                                <label class="flex items-center p-2 bg-gray-700 rounded-lg">
                                    <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600">
                                    <span class="ml-2 text-gray-300">系统更新</span>
                                </label>
                                <label class="flex items-center p-2 bg-gray-700 rounded-lg">
                                    <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600">
                                    <span class="ml-2 text-gray-300">系统错误</span>
                                </label>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-gray-300 mb-2">高级选项</label>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-gray-400 text-sm mb-1">密钥 (可选)</label>
                                    <input type="text" placeholder="用于签名验证的密钥" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-gray-400 text-sm mb-1">超时时间 (秒)</label>
                                    <input type="number" value="30" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="activeStatus" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                    <label for="activeStatus" class="ml-2 text-gray-300">立即激活</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 mt-6">
                        <button id="cancelBtn" class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                            取消
                        </button>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-plus mr-2"></i> 创建 Webhook
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 标签页切换功能
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tabId = button.getAttribute('data-tab');
                    
                    // 移除所有标签按钮的活动状态
                    tabButtons.forEach(btn => {
                        btn.classList.remove('tab-active');
                        btn.classList.add('text-gray-400');
                        btn.classList.remove('text-blue-400');
                    });
                    
                    // 添加当前标签按钮的活动状态
                    button.classList.add('tab-active');
                    button.classList.remove('text-gray-400');
                    button.classList.add('text-blue-400');
                    
                    // 隐藏所有标签内容
                    tabContents.forEach(content => {
                        content.classList.remove('active');
                    });
                    
                    // 显示当前标签内容
                    document.getElementById(tabId).classList.add('active');
                });
            });
            
            // 模态框功能
            const createWebhookBtn = document.getElementById('createWebhookBtn');
            const createWebhookModal = document.getElementById('createWebhookModal');
            const closeModalBtn = document.getElementById('closeModalBtn');
            const cancelBtn = document.getElementById('cancelBtn');
            
            if(createWebhookBtn && createWebhookModal) {
                createWebhookBtn.addEventListener('click', () => {
                    createWebhookModal.classList.remove('hidden');
                });
                
                if(closeModalBtn) {
                    closeModalBtn.addEventListener('click', () => {
                        createWebhookModal.classList.add('hidden');
                    });
                }
                
                if(cancelBtn) {
                    cancelBtn.addEventListener('click', () => {
                        createWebhookModal.classList.add('hidden');
                    });
                }
            }
        });
    </script>
</body>
</html>