# GACMS 版本分级编译 Makefile
# Author: <PERSON><PERSON> Nieh
# Email: <EMAIL>

# 版本信息
VERSION ?= 1.0.0
BUILD_TIME := $(shell date -u +%Y-%m-%dT%H:%M:%SZ)
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 目录配置
BUILD_DIR := ./dist
SOURCE_DIR := .
SCRIPTS_DIR := ./scripts

# Go 编译配置
GO_VERSION := $(shell go version | cut -d' ' -f3)
GOOS := $(shell go env GOOS)
GOARCH := $(shell go env GOARCH)

# 编译标志
LDFLAGS := -s -w \
	-X main.Version=$(VERSION) \
	-X main.BuildTime=$(BUILD_TIME) \
	-X main.GitCommit=$(GIT_COMMIT) \
	-X main.GoVersion=$(GO_VERSION) \
	-X main.Edition=$(EDITION)

# 版本列表
EDITIONS := personal professional business

# 默认目标
.DEFAULT_GOAL := personal

# 帮助信息
.PHONY: help
help:
	@echo "GACMS Edition Build System"
	@echo ""
	@echo "Available targets:"
	@echo "  help              Show this help message"
	@echo "  clean             Clean build directory"
	@echo "  deps              Install dependencies"
	@echo "  test              Run tests"
	@echo "  lint              Run linter"
	@echo ""
	@echo "Build targets:"
	@echo "  community         Build community edition (default)"
	@echo "  personal          Build personal edition"
	@echo "  professional      Build professional edition"
	@echo "  business          Build business edition"
	@echo "  all               Build all editions"
	@echo ""
	@echo "Package targets:"
	@echo "  package-community     Create community edition package"
	@echo "  package-personal      Create personal edition package"
	@echo "  package-professional  Create professional edition package"
	@echo "  package-business      Create business edition package"
	@echo "  package-all           Create all edition packages"
	@echo ""
	@echo "Release targets:"
	@echo "  release-community     Build and package community edition"
	@echo "  release-personal      Build and package personal edition"
	@echo "  release-professional  Build and package professional edition"
	@echo "  release-business      Build and package business edition"
	@echo "  release-all           Build and package all editions"
	@echo ""
	@echo "Variables:"
	@echo "  VERSION=$(VERSION)    Set version number"
	@echo "  BUILD_DIR=$(BUILD_DIR)      Set build directory"
	@echo ""
	@echo "Examples:"
	@echo "  make community                    # Build community edition"
	@echo "  make VERSION=2.0.0 all          # Build all editions with version 2.0.0"
	@echo "  make release-professional        # Build and package professional edition"

# 清理
.PHONY: clean
clean:
	@echo "Cleaning build directory..."
	@rm -rf $(BUILD_DIR)
	@echo "Clean completed."

# 安装依赖
.PHONY: deps
deps:
	@echo "Installing dependencies..."
	@go mod download
	@go mod tidy
	@echo "Dependencies installed."

# 运行测试
.PHONY: test
test:
	@echo "Running tests..."
	@go test -v ./...

# 运行测试（带覆盖率）
.PHONY: test-coverage
test-coverage:
	@echo "Running tests with coverage..."
	@go test -v -coverprofile=coverage.out ./...
	@go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# 代码检查
.PHONY: lint
lint:
	@echo "Running linter..."
	@golangci-lint run

# 格式化代码
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	@go fmt ./...

# 创建构建目录
$(BUILD_DIR):
	@mkdir -p $(BUILD_DIR)

# 社区版
.PHONY: community
community: $(BUILD_DIR)
	@echo "Building community edition..."
	@go build -tags="community" -ldflags="$(LDFLAGS)" -o $(BUILD_DIR)/gacms-community-$(VERSION) ./cmd/gacms
	@echo "Community edition built: $(BUILD_DIR)/gacms-community-$(VERSION)"

# 个人版
.PHONY: personal
personal: $(BUILD_DIR)
	@echo "Building personal edition..."
	@EDITION=personal go build -tags="personal" -ldflags="$(LDFLAGS)" -o $(BUILD_DIR)/gacms-personal-$(VERSION) ./cmd/gacms
	@echo "Personal edition built: $(BUILD_DIR)/gacms-personal-$(VERSION)"

# 专业版
.PHONY: professional
professional: $(BUILD_DIR)
	@echo "Building professional edition..."
	@EDITION=professional go build -tags="professional" -ldflags="$(LDFLAGS)" -o $(BUILD_DIR)/gacms-professional-$(VERSION) ./cmd/gacms
	@echo "Professional edition built: $(BUILD_DIR)/gacms-professional-$(VERSION)"

# 商业版
.PHONY: business
business: $(BUILD_DIR)
	@echo "Building business edition..."
	@EDITION=business go build -tags="business" -ldflags="$(LDFLAGS)" -o $(BUILD_DIR)/gacms-business-$(VERSION) ./cmd/gacms
	@echo "Business edition built: $(BUILD_DIR)/gacms-business-$(VERSION)"

# 构建所有版本
.PHONY: all
all: community personal professional business
	@echo "All editions built successfully!"

# 验证构建
.PHONY: verify
verify:
	@echo "Verifying builds..."
	@for edition in $(EDITIONS); do \
		binary=$(BUILD_DIR)/gacms-$$edition-$(VERSION); \
		if [ -f "$$binary" ]; then \
			echo "✓ $$edition edition: $$binary"; \
			ls -lh "$$binary"; \
		else \
			echo "✗ $$edition edition: missing"; \
		fi; \
	done

# 打包社区版
.PHONY: package-community
package-community: community
	@$(SCRIPTS_DIR)/build-editions.sh -p community

# 打包个人版
.PHONY: package-personal
package-personal: personal
	@$(SCRIPTS_DIR)/build-editions.sh -p personal

# 打包专业版
.PHONY: package-professional
package-professional: professional
	@$(SCRIPTS_DIR)/build-editions.sh -p professional

# 打包商业版
.PHONY: package-business
package-business: business
	@$(SCRIPTS_DIR)/build-editions.sh -p business

# 打包所有版本
.PHONY: package-all
package-all: all
	@$(SCRIPTS_DIR)/build-editions.sh -p all

# 发布社区版
.PHONY: release-community
release-community: clean
	@$(SCRIPTS_DIR)/build-editions.sh -c -p --verify community

# 发布个人版
.PHONY: release-personal
release-personal: clean
	@$(SCRIPTS_DIR)/build-editions.sh -c -p --verify personal

# 发布专业版
.PHONY: release-professional
release-professional: clean
	@$(SCRIPTS_DIR)/build-editions.sh -c -p --verify professional

# 发布商业版
.PHONY: release-business
release-business: clean
	@$(SCRIPTS_DIR)/build-editions.sh -c -p --verify business

# 发布所有版本
.PHONY: release-all
release-all: clean
	@$(SCRIPTS_DIR)/build-editions.sh -c -p --verify all

# 开发模式（社区版）
.PHONY: dev
dev:
	@echo "Starting development mode (community edition)..."
	@go run -tags="community" ./cmd/gacms

# 开发模式（指定版本）
.PHONY: dev-personal
dev-personal:
	@echo "Starting development mode (personal edition)..."
	@go run -tags="personal" ./cmd/gacms

.PHONY: dev-professional
dev-professional:
	@echo "Starting development mode (professional edition)..."
	@go run -tags="professional" ./cmd/gacms

.PHONY: dev-business
dev-business:
	@echo "Starting development mode (business edition)..."
	@go run -tags="business" ./cmd/gacms

# 安装到系统
.PHONY: install
install: community
	@echo "Installing GACMS community edition..."
	@sudo cp $(BUILD_DIR)/gacms-community-$(VERSION) /usr/local/bin/gacms
	@sudo chmod +x /usr/local/bin/gacms
	@echo "GACMS installed to /usr/local/bin/gacms"

# 卸载
.PHONY: uninstall
uninstall:
	@echo "Uninstalling GACMS..."
	@sudo rm -f /usr/local/bin/gacms
	@echo "GACMS uninstalled."

# Docker 构建
.PHONY: docker-community
docker-community:
	@echo "Building Docker image for community edition..."
	@docker build --build-arg EDITION=community --build-arg VERSION=$(VERSION) -t gacms:community-$(VERSION) .

.PHONY: docker-all
docker-all:
	@for edition in $(EDITIONS); do \
		echo "Building Docker image for $$edition edition..."; \
		docker build --build-arg EDITION=$$edition --build-arg VERSION=$(VERSION) -t gacms:$$edition-$(VERSION) .; \
	done

# 显示构建信息
.PHONY: info
info:
	@echo "Build Information:"
	@echo "  Version:     $(VERSION)"
	@echo "  Build Time:  $(BUILD_TIME)"
	@echo "  Git Commit:  $(GIT_COMMIT)"
	@echo "  Go Version:  $(GO_VERSION)"
	@echo "  OS/Arch:     $(GOOS)/$(GOARCH)"
	@echo "  Build Dir:   $(BUILD_DIR)"
	@echo "  Editions:    $(EDITIONS)"

# 检查工具
.PHONY: check-tools
check-tools:
	@echo "Checking required tools..."
	@command -v go >/dev/null 2>&1 || { echo "Go is required but not installed."; exit 1; }
	@command -v git >/dev/null 2>&1 || { echo "Git is required but not installed."; exit 1; }
	@echo "All required tools are available."

# 性能测试
.PHONY: bench
bench:
	@echo "Running benchmarks..."
	@go test -bench=. -benchmem ./...

# 生成文档
.PHONY: docs
docs:
	@echo "Generating documentation..."
	@godoc -http=:6060 &
	@echo "Documentation server started at http://localhost:6060"

# 清理所有（包括依赖）
.PHONY: clean-all
clean-all: clean
	@echo "Cleaning all..."
	@go clean -modcache
	@rm -f coverage.out coverage.html

# 快速构建（无优化）
.PHONY: quick
quick: $(BUILD_DIR)
	@echo "Quick build (community edition, no optimization)..."
	@go build -tags="community" -o $(BUILD_DIR)/gacms-quick ./cmd/gacms
	@echo "Quick build completed: $(BUILD_DIR)/gacms-quick"

# 商业授权相关目标

# 验证商业授权配置
.PHONY: validate-commercial-config
validate-commercial-config:
	@echo "Validating commercial authorization config..."
	@if [ -f "configs/commercial.yaml" ]; then \
		echo "✓ Commercial config found: configs/commercial.yaml"; \
	else \
		echo "⚠ Commercial config not found, using defaults"; \
	fi
	@if [ -f "configs/editions.yaml" ]; then \
		echo "✓ Editions config found: configs/editions.yaml"; \
	else \
		echo "✗ Editions config missing: configs/editions.yaml"; \
		exit 1; \
	fi

# 生成示例许可证文件
.PHONY: generate-sample-licenses
generate-sample-licenses:
	@echo "Generating sample license files..."
	@mkdir -p licenses/usage
	@mkdir -p keys
	@echo "Sample license directories created"
	@echo "Note: Implement actual license generation logic"

# 启用商业授权的构建
.PHONY: build-with-commercial-auth
build-with-commercial-auth: validate-commercial-config
	@echo "Building with commercial authorization enabled..."
	@GACMS_COMMERCIAL_AUTH_ENABLED=true $(MAKE) all

# 开发模式（启用商业授权）
.PHONY: dev-commercial
dev-commercial:
	@echo "Starting development mode with commercial authorization..."
	@GACMS_COMMERCIAL_AUTH_ENABLED=true GACMS_DEVELOPMENT_MODE=false go run -tags="business" ./cmd/gacms

# 测试商业授权
.PHONY: test-commercial
test-commercial:
	@echo "Testing commercial authorization..."
	@GACMS_COMMERCIAL_AUTH_ENABLED=true go test -v ./internal/core/service/...

# 显示商业授权信息
.PHONY: commercial-info
commercial-info:
	@echo "Commercial Authorization Information:"
	@echo "  Config file:     configs/commercial.yaml"
	@echo "  Editions file:   configs/editions.yaml"
	@echo "  License dir:     licenses/"
	@echo "  Keys dir:        keys/"
	@echo "  Supported editions: $(EDITIONS)"
	@echo ""
	@echo "Environment variables:"
	@echo "  GACMS_COMMERCIAL_AUTH_ENABLED  - Enable/disable commercial auth"
	@echo "  GACMS_DEVELOPMENT_MODE         - Enable development mode"
	@echo "  GACMS_SYSTEM_LICENSE_PATH      - System license file path"
	@echo "  GACMS_USAGE_LICENSE_DIR        - Usage license directory"
