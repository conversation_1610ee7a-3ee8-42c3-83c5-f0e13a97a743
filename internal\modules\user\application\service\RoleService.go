/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/user/application/service/RoleService.go
 * @Description: Service for role management business logic.
 *
 * © 2025 GACMS. All rights reserved.
 */
package service

import (
	"context"
	"errors"
	"gacms/internal/modules/user/domain/contract"
	"gacms/internal/modules/user/domain/model"
	"gacms/internal/modules/user/events"
	pkgContract "gacms/pkg/contract"
	"gacms/pkg/logger"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

var (
	ErrRoleExists    = errors.New("role with this name already exists for the given site and user type")
	ErrRoleNotFound  = errors.New("role not found")
)

// RoleService provides business logic for roles and permissions.
type RoleService struct {
	roleRepo    contract.RoleRepository
	permRepo    contract.PermissionRepository
	log         *logger.Logger
	eventManager pkgContract.EventManager
}

// NewRoleService creates a new instance of RoleService.
func NewRoleService(
	roleRepo contract.RoleRepository, 
	permRepo contract.PermissionRepository, 
	log *logger.Logger,
	eventManager pkgContract.EventManager,
) *RoleService {
	return &RoleService{
		roleRepo: roleRepo, 
		permRepo: permRepo, 
		log: log, 
		eventManager: eventManager,
	}
}

type CreateRolePayload struct {
	SiteID      uint   `json:"site_id" binding:"required"`
	Name        string `json:"name" binding:"required"`
	UserType    string `json:"user_type" binding:"required,oneof=admin member"`
	PermissionIDs []uint `json:"permission_ids"`
	CreatedBy   uint   `json:"created_by"`
}

func (s *RoleService) CreateRole(ctx *gin.Context, payload *CreateRolePayload) (*model.Role, error) {
	userType := model.UserType(payload.UserType)
	
	// Check if role already exists
	if _, err := s.roleRepo.GetByName(ctx, payload.SiteID, payload.Name, userType); !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, ErrRoleExists
	}
	
	// Verify all permissions exist
	if len(payload.PermissionIDs) > 0 {
		perms, err := s.permRepo.GetByIDs(ctx, payload.PermissionIDs)
		if err != nil {
			return nil, err
		}
		if len(perms) != len(payload.PermissionIDs) {
			return nil, errors.New("one or more permissions not found")
		}
	}
	
	role := &model.Role{
		SiteID:   payload.SiteID,
		Name:     payload.Name,
		UserType: userType,
	}

	if err := s.roleRepo.Create(ctx, role); err != nil {
		return nil, err
	}

	if len(payload.PermissionIDs) > 0 {
		if err := s.roleRepo.ReplacePermissionsForRole(ctx, role.ID, payload.PermissionIDs); err != nil {
			// Attempt to roll back role creation
			s.roleRepo.Delete(ctx, role.ID)
			return nil, err
		}
	}

	// 创建成功后发布角色创建事件
	createdEvent := events.NewRoleCreatedEvent(ctx.Request.Context(), role, payload.CreatedBy, payload.SiteID)
	s.eventManager.Dispatch(createdEvent)

	return s.roleRepo.GetByID(ctx, role.ID)
}

type UpdateRolePayload struct {
	Name        string `json:"name"`
	PermissionIDs []uint `json:"permission_ids"`
	UpdatedBy   uint   `json:"updated_by"`
}

func (s *RoleService) UpdateRole(ctx *gin.Context, roleID uint, payload *UpdateRolePayload) (*model.Role, error) {
	// 获取原始角色信息
	originalRole, err := s.roleRepo.GetByID(ctx, roleID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrRoleNotFound
		}
		return nil, err
	}

	oldName := originalRole.Name
	
	// 如果更新名称
	if payload.Name != "" && payload.Name != originalRole.Name {
		originalRole.Name = payload.Name
		if err := s.roleRepo.Update(ctx, originalRole); err != nil {
			return nil, err
		}
	}

	// 如果更新权限
	if payload.PermissionIDs != nil {
		// 获取原始权限ID列表
		originalPerms, err := s.roleRepo.GetPermissionsForRole(ctx, roleID)
		if err != nil {
			return nil, err
		}
		
		originalPermIDs := make([]uint, len(originalPerms))
		for i, p := range originalPerms {
			originalPermIDs[i] = p.ID
		}
		
		// 更新权限
		if err := s.roleRepo.ReplacePermissionsForRole(ctx, roleID, payload.PermissionIDs); err != nil {
			return nil, err
		}
		
		// 获取新增和删除的权限ID
		added, removed := s.diffPermissions(originalPermIDs, payload.PermissionIDs)
		
		// 如果权限有变化，发布权限变更事件
		if len(added) > 0 || len(removed) > 0 {
			// 获取权限名称列表用于事件
			addedNames := make([]string, 0, len(added))
			removedNames := make([]string, 0, len(removed))
			
			if len(added) > 0 {
				perms, err := s.permRepo.GetByIDs(ctx, added)
				if err == nil {
					for _, p := range perms {
						addedNames = append(addedNames, p.Slug)
					}
				}
			}
			
			if len(removed) > 0 {
				perms, err := s.permRepo.GetByIDs(ctx, removed)
				if err == nil {
					for _, p := range perms {
						removedNames = append(removedNames, p.Slug)
					}
				}
			}
			
			permEvent := events.NewRolePermissionsChangedEvent(
				ctx.Request.Context(),
				roleID,
				addedNames,
				removedNames,
				payload.UpdatedBy,
				originalRole.SiteID,
			)
			s.eventManager.Dispatch(permEvent)
		}
	}
	
	// 如果名称有变更，发布更新事件
	if oldName != originalRole.Name {
		updateEvent := events.NewRoleUpdatedEvent(
			ctx.Request.Context(),
			originalRole,
			payload.UpdatedBy,
			originalRole.SiteID,
			oldName,
		)
		s.eventManager.Dispatch(updateEvent)
	}
	
	return s.roleRepo.GetByID(ctx, roleID)
}

func (s *RoleService) DeleteRole(ctx *gin.Context, roleID uint, deletedBy uint) error {
	// 获取角色信息用于事件
	role, err := s.roleRepo.GetByID(ctx, roleID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrRoleNotFound
		}
		return err
	}
	
	// 执行删除
	if err := s.roleRepo.Delete(ctx, roleID); err != nil {
		return err
	}
	
	// 发布删除事件
	deleteEvent := events.NewRoleDeletedEvent(
		ctx.Request.Context(),
		roleID,
		deletedBy,
		role.SiteID,
		role.Name,
	)
	s.eventManager.Dispatch(deleteEvent)
	
	return nil
}

func (s *RoleService) AssignRoleToUser(ctx *gin.Context, userID, roleID uint, userType model.UserType, assignedBy uint) error {
	// 获取角色信息用于事件
	role, err := s.roleRepo.GetByID(ctx, roleID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrRoleNotFound
		}
		return err
	}
	
	// 添加角色
	if err := s.roleRepo.AddRoleToUser(ctx, userID, roleID, userType); err != nil {
		return err
	}
	
	// 发布角色分配事件
	event := events.NewUserRoleAssignedEvent(
		ctx.Request.Context(),
		userID,
		userType,
		roleID,
		role.Name,
		assignedBy,
		role.SiteID,
	)
	s.eventManager.Dispatch(event)
	
	return nil
}

func (s *RoleService) RemoveRoleFromUser(ctx *gin.Context, userID, roleID uint, userType model.UserType, removedBy uint) error {
	// 获取角色信息用于事件
	role, err := s.roleRepo.GetByID(ctx, roleID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrRoleNotFound
		}
		return err
	}
	
	// 移除角色
	if err := s.roleRepo.RemoveRoleFromUser(ctx, userID, roleID, userType); err != nil {
		return err
	}
	
	// 发布角色移除事件
	event := events.NewUserRoleRemovedEvent(
		ctx.Request.Context(),
		userID,
		userType,
		roleID,
		role.Name,
		removedBy,
		role.SiteID,
	)
	s.eventManager.Dispatch(event)
	
	return nil
}

func (s *RoleService) SetUserRoles(ctx *gin.Context, userID uint, roleIDs []uint, userType model.UserType, updatedBy uint) error {
	// 获取当前的角色
	currentRoles, err := s.roleRepo.GetRolesForUser(ctx, userID, userType)
	if err != nil {
		return err
	}
	
	currentRoleIDs := make([]uint, len(currentRoles))
	for i, role := range currentRoles {
		currentRoleIDs[i] = role.ID
	}
	
	// 计算需要添加和移除的角色
	addedRoles, removedRoles := s.diffRoles(currentRoleIDs, roleIDs)
	
	// 批量更新用户角色
	if err := s.roleRepo.ReplaceUserRoles(ctx, userID, roleIDs, userType); err != nil {
		return err
	}
	
	// 如果有变化，发布事件
	if len(addedRoles) > 0 || len(removedRoles) > 0 {
		// 获取角色所属的站点ID（使用第一个角色的站点ID）
		var siteID uint
		if len(currentRoles) > 0 {
			siteID = currentRoles[0].SiteID
		} else if len(roleIDs) > 0 {
			roles, err := s.roleRepo.GetByIDs(ctx, roleIDs)
			if err == nil && len(roles) > 0 {
				siteID = roles[0].SiteID
			}
		}
		
		event := events.NewUserRolesBatchUpdatedEvent(
			ctx.Request.Context(),
			userID,
			userType,
			addedRoles,
			removedRoles,
			updatedBy,
			siteID,
		)
		s.eventManager.Dispatch(event)
	}
	
	return nil
}

func (s *RoleService) SetRolePermissions(ctx context.Context, roleID uint, permissionIDs []uint, updatedBy uint) (*model.Role, error) {
	// 获取角色信息
	role, err := s.roleRepo.GetByID(ctx, roleID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrRoleNotFound
		}
		return nil, err
	}
	
	// 获取当前权限
	currentPerms, err := s.roleRepo.GetPermissionsForRole(ctx, roleID)
	if err != nil {
		return nil, err
	}
	
	currentPermIDs := make([]uint, len(currentPerms))
	for i, p := range currentPerms {
		currentPermIDs[i] = p.ID
	}

	// Verify all permissions exist
	if len(permissionIDs) > 0 {
		perms, err := s.permRepo.GetByIDs(ctx, permissionIDs)
		if err != nil {
			return nil, err
		}
		if len(perms) != len(permissionIDs) {
			return nil, errors.New("one or more permissions not found")
		}
	}

	// 更新权限
	err = s.roleRepo.ReplacePermissionsForRole(ctx, roleID, permissionIDs)
	if err != nil {
		return nil, err
	}
	
	// 计算变更并发布事件
	added, removed := s.diffPermissions(currentPermIDs, permissionIDs)
	
	if len(added) > 0 || len(removed) > 0 {
		// 获取权限名称
		addedNames := make([]string, 0, len(added))
		removedNames := make([]string, 0, len(removed))
		
		if len(added) > 0 {
			perms, err := s.permRepo.GetByIDs(ctx, added)
			if err == nil {
				for _, p := range perms {
					addedNames = append(addedNames, p.Slug)
				}
			}
		}
		
		if len(removed) > 0 {
			perms, err := s.permRepo.GetByIDs(ctx, removed)
			if err == nil {
				for _, p := range perms {
					removedNames = append(removedNames, p.Slug)
				}
			}
		}
		
		// 发布权限变更事件
		event := events.NewRolePermissionsChangedEvent(
			ctx,
			roleID,
			addedNames,
			removedNames,
			updatedBy,
			role.SiteID,
		)
		s.eventManager.Dispatch(event)
	}

	return s.roleRepo.GetByID(ctx, roleID)
}

// 辅助函数：计算两个权限ID列表的差异
func (s *RoleService) diffPermissions(original, updated []uint) (added, removed []uint) {
	originalMap := make(map[uint]bool)
	for _, id := range original {
		originalMap[id] = true
	}
	
	updatedMap := make(map[uint]bool)
	for _, id := range updated {
		updatedMap[id] = true
		
		// 如果更新后的ID不在原始列表中，则为新增
		if !originalMap[id] {
			added = append(added, id)
		}
	}
	
	// 检查移除的ID
	for _, id := range original {
		if !updatedMap[id] {
			removed = append(removed, id)
		}
	}
	
	return added, removed
}

// 辅助函数：计算两个角色ID列表的差异
func (s *RoleService) diffRoles(original, updated []uint) (added, removed []uint) {
	return s.diffPermissions(original, updated) // 可以复用差异计算逻辑
}

// TODO: Implement the business logic for:
// - GetRole
// - ListRoles
// - ListAllPermissions
// - AssignPermissionsToRole
// - AssignRolesToAdmin 