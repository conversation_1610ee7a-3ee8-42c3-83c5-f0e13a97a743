/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/infrastructure/cache/Provider.go
 * @Description: Provides a factory function to instantiate a cache provider based on application configuration.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package cache

import (
	"context"
	"fmt"
	"gacms/internal/infrastructure/config"

	"github.com/redis/go-redis/v9"
)

// NewCacheProvider reads the configuration and returns the appropriate cache implementation.
// This acts as a factory for the Cache interface, supporting dependency injection.
func NewCacheProvider(cfg *config.Loader) (Cache, error) {
	driver := cfg.GetString("cache.driver")
	if driver == "" {
		driver = "file" // Default to file cache if not specified
	}

	switch driver {
	case "redis":
		addr := cfg.GetString("cache.redis.address")
		if addr == "" {
			return nil, fmt.Errorf("redis cache driver selected but address is not configured")
		}
		password := cfg.GetString("cache.redis.password")
		db := cfg.GetInt("cache.redis.db")

		rdb := redis.NewClient(&redis.Options{
			Addr:     addr,
			Password: password,
			DB:       db,
		})

		// Ping the server to ensure connectivity
		if _, err := rdb.Ping(context.Background()).Result(); err != nil {
			return nil, fmt.Errorf("could not connect to redis: %w", err)
		}

		fmt.Println("Cache Provider: Using Redis")
		return NewRedisAdapter(rdb), nil

	case "file":
		path := cfg.GetString("cache.file.path")
		if path == "" {
			path = "storage/cache" // Provide a sensible default
		}

		fmt.Println("Cache Provider: Using File")
		return NewFileAdapter(path)

	default:
		return nil, fmt.Errorf("unsupported cache driver: %s", driver)
	}
} 