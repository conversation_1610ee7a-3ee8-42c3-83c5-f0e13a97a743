/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-09
 * @LastEditors: <PERSON><PERSON> Nieh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/contenttype/module.go
 * @Description: Defines the contenttype module, its services, and its integration with the core system, adhering to the latest module contract.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contenttype

import (
	"gacms/internal/modules/contenttype/application/service"
	domainContract "gacms/internal/modules/contenttype/domain/contract"
	"gacms/internal/modules/contenttype/domain/model"
	"gacms/internal/modules/contenttype/infrastructure/persistence"
	"gacms/internal/modules/contenttype/port/http/controller"
	"gacms/internal/port/http/middleware"
	pkgContract "gacms/pkg/contract"

	"github.com/gin-gonic/gin"
	"go.uber.org/fx"
)

type ContentTypeModule struct {
	contentTypeCtrl *controller.ContentTypeController
	contentItemCtrl *controller.ContentItemController
}

// Info provides metadata about the module.
func (m *ContentTypeModule) Info() pkgContract.ModuleInfo {
	return pkgContract.ModuleInfo{
		Name:        "ContentType",
		Description: "Manages dynamic content types, fields, and content items.",
		Version:     "1.0.0",
	}
}

// GetModels returns the GORM models for auto-migration.
func (m *ContentTypeModule) GetModels() []interface{} {
	return []interface{}{
		&model.ContentType{},
		&model.Field{},
		&model.ContentItem{},
	}
}

// ExposePermissions declares the permissions this module uses.
func (m *ContentTypeModule) ExposePermissions() []pkgContract.PermissionInfo {
	return []pkgContract.PermissionInfo{
		{Name: "content:types:manage", Description: "Manage content types (create, edit, delete)"},
		// Dynamic permissions for each content type (e.g., 'content:posts:create') are managed by the service.
	}
}

// Routes defines the HTTP routes for this module.
func (m *ContentTypeModule) Routes(authMiddleware *middleware.AdminAuthMiddleware) []pkgContract.Route {
	modelGuard := authMiddleware.Handle("content:types:manage")

	var routes []pkgContract.Route
	
	// Routes for managing the content types themselves (admin only)
	routes = append(routes, pkgContract.Route{
		Method: "POST", Path: "/content-types", Handler: m.contentTypeCtrl.CreateContentType, Middlewares: []gin.HandlerFunc{modelGuard},
	}, pkgContract.Route{
		Method: "GET", Path: "/content-types", Handler: m.contentTypeCtrl.ListContentTypes, Middlewares: []gin.HandlerFunc{modelGuard},
	})

	// Routes for managing content items. Permissions are checked dynamically inside the controller.
	routes = append(routes, pkgContract.Route{
		Method: "POST", Path: "/content-items", Handler: m.contentItemCtrl.CreateContentItem,
	}, pkgContract.Route{
		Method: "GET", Path: "/content-items/:slug", Handler: m.contentItemCtrl.GetContentItems,
	})

	return routes
}

// Module bundle for fx
var Module = fx.Options(
	// Repositories
	fx.Provide(
		fx.Annotate(persistence.NewContentTypeRepository, fx.As(new(domainContract.ContentTypeRepository))),
		fx.Annotate(persistence.NewContentItemRepository, fx.As(new(domainContract.ContentItemRepository))),
	),
	// Services
	fx.Provide(
		fx.Annotate(
			service.NewContentTypeService,
		),
		service.NewContentItemService,
	),
	// Controllers
	fx.Provide(
		controller.NewContentTypeController,
		controller.NewContentItemController,
	),
	// Module
	fx.Provide(
		fx.Annotate(
			func(contentTypeCtrl *controller.ContentTypeController, contentItemCtrl *controller.ContentItemController) *ContentTypeModule {
				return &ContentTypeModule{
					contentTypeCtrl: contentTypeCtrl,
					contentItemCtrl: contentItemCtrl,
				}
			},
			fx.As(new(pkgContract.IModule)),
			fx.As(new(pkgContract.IMigratable)),
			fx.As(new(pkgContract.IPermissionExposer)),
		),
	),
	// Invokers
	fx.Invoke(func(router *gin.Engine, module *ContentTypeModule, auth *middleware.AdminAuthMiddleware) {
		for _, route := range module.Routes(auth) {
			fullPath := "/api/admin" + route.Path
			router.Handle(route.Method, fullPath, append(route.Middlewares, route.Handler)...)
		}
	}),
	fx.Invoke(SeedContentTypes),
)
