/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/banner/application/BannerService.go
 * @Description: Banner service implementation
 *
 * © 2025 GACMS. All rights reserved.
 */

package application

import (
	"gacms/internal/modules/banner/domain/contract"
	"gacms/internal/modules/banner/domain/model"
	"gacms/internal/modules/banner/domain/repository"

	"github.com/gin-gonic/gin"
)

type BannerService struct {
	bannerRepo         repository.IBannerRepository
	bannerPositionRepo repository.IBannerPositionRepository
}

func NewBannerService(
	bannerRepo repository.IBannerRepository,
	bannerPositionRepo repository.IBannerPositionRepository,
) contract.IBannerService {
	return &BannerService{
		bannerRepo:         bannerRepo,
		bannerPositionRepo: bannerPositionRepo,
	}
}

// Banner Position Management
func (s *BannerService) CreateBannerPosition(ctx *gin.Context, position *model.BannerPosition) (*model.BannerPosition, error) {
	return s.bannerPositionRepo.Create(ctx, position)
}

func (s *BannerService) UpdateBannerPosition(ctx *gin.Context, position *model.BannerPosition) error {
	return s.bannerPositionRepo.Update(ctx, position)
}

func (s *BannerService) DeleteBannerPosition(ctx *gin.Context, id uint) error {
	return s.bannerPositionRepo.Delete(ctx, id)
}

func (s *BannerService) GetBannerPosition(ctx *gin.Context, id uint) (*model.BannerPosition, error) {
	return s.bannerPositionRepo.GetByID(ctx, id)
}

func (s *BannerService) GetAllBannerPositions(ctx *gin.Context) ([]*model.BannerPosition, error) {
	return s.bannerPositionRepo.GetAll(ctx)
}

// Banner Management
func (s *BannerService) CreateBanner(ctx *gin.Context, banner *model.Banner) (*model.Banner, error) {
	return s.bannerRepo.Create(ctx, banner)
}

func (s *BannerService) UpdateBanner(ctx *gin.Context, banner *model.Banner) error {
	return s.bannerRepo.Update(ctx, banner)
}

func (s *BannerService) DeleteBanner(ctx *gin.Context, id uint) error {
	return s.bannerRepo.Delete(ctx, id)
}

func (s *BannerService) GetBanner(ctx *gin.Context, id uint) (*model.Banner, error) {
	return s.bannerRepo.GetByID(ctx, id)
}

func (s *BannerService) GetBannersByPositionID(ctx *gin.Context, positionID uint) ([]*model.Banner, error) {
	return s.bannerRepo.GetByPositionID(ctx, positionID)
} 