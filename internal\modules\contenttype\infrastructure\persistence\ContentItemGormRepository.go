/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/contenttype/infrastructure/persistence/ContentItemGormRepository.go
 * @Description: GORM implementation of the ContentItemRepository.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package persistence

import (
	"fmt"
	"gacms/internal/modules/contenttype/domain/contract"
	"gacms/internal/modules/contenttype/domain/model"

	"gorm.io/gorm"
)

type contentItemGormRepository struct {
	db *gorm.DB
}

func NewContentItemGormRepository(db *gorm.DB) contract.ContentItemRepository {
	return &contentItemGormRepository{db: db}
}

func (r *contentItemGormRepository) Create(item *model.ContentItem) error {
	return r.db.Create(item).Error
}

func (r *contentItemGormRepository) Update(item *model.ContentItem) error {
	return r.db.Save(item).Error
}

func (r *contentItemGormRepository) Delete(id uint) error {
	return r.db.Delete(&model.ContentItem{}, id).Error
}

func (r *contentItemGormRepository) FindByID(id uint) (*model.ContentItem, error) {
	var item model.ContentItem
	err := r.db.First(&item, id).Error
	return &item, err
}

func (r *contentItemGormRepository) FindAll(siteID uint, contentTypeID uint) ([]*model.ContentItem, error) {
	var items []*model.ContentItem
	err := r.db.Where("site_id = ? AND content_type_id = ?", siteID, contentTypeID).Find(&items).Error
	return items, err
}

func (r *contentItemGormRepository) FindAll(contentTypeID, siteID uint, options contract.QueryOptions) ([]*model.ContentItem, int64, error) {
	var items []*model.ContentItem
	var total int64

	query := r.db.Model(&model.ContentItem{}).Where("content_type_id = ? AND site_id = ?", contentTypeID, siteID)

	// Count total records for pagination
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (options.Page - 1) * options.PerPage
	err := query.Offset(offset).Limit(options.PerPage).Find(&items).Error
	
	return items, total, err
}

func (r *contentItemGormRepository) FindBatchByIDs(ids []uint) ([]*model.ContentItem, error) {
	var items []*model.ContentItem
	if len(ids) == 0 {
		return items, nil
	}
	err := r.db.Where("id IN ?", ids).Find(&items).Error
	return items, err
}

} 