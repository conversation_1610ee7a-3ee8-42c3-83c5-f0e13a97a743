/*
Author: <PERSON><PERSON> Nieh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package service

import (
	"crypto/sha256"
	"encoding/hex"
	"strings"
)

// ModuleAuthorizationRule 模块授权规则
type ModuleAuthorizationRule struct {
	ModuleName      string
	RequiresLicense bool
	LicenseType     string // "official", "third_party", "none"
	MinimumEdition  string // "community", "personal", "professional", "enterprise"
}

// ModuleAuthorizationRules 模块授权规则管理器
type ModuleAuthorizationRules struct {
	rules           map[string]*ModuleAuthorizationRule
	configValidator *ModuleConfigValidator
}

// NewModuleAuthorizationRules 创建模块授权规则管理器
func NewModuleAuthorizationRules() *ModuleAuthorizationRules {
	rules := &ModuleAuthorizationRules{
		rules:           make(map[string]*ModuleAuthorizationRule),
		configValidator: NewModuleConfigValidator(),
	}

	// 基于类型的授权规则，支持扩展
	rules.initializeHardcodedRules()

	return rules
}

// initializeHardcodedRules 初始化基于类型的授权规则
func (r *ModuleAuthorizationRules) initializeHardcodedRules() {
	// 不再硬编码具体模块名称，而是基于模块类型和特征进行判断
	// 这样可以支持未来的扩展模块
}

// RequiresLicense 检查模块是否需要许可证（基于类型的安全版本）
func (r *ModuleAuthorizationRules) RequiresLicense(moduleName string, moduleType ModuleType) bool {
	// 基于模块类型进行判断，而不是硬编码具体模块名称
	switch moduleType {
	case ModuleTypeCore:
		// 核心模块永远免费（基于类型，不是具体名称）
		return false

	case ModuleTypeOptional:
		// 可选模块需要检查具体规则或配置
		return r.checkOptionalModuleLicense(moduleName)

	case ModuleTypeVendors:
		// Vendors模块通过签名验证
		return r.checkVendorsModuleLicense(moduleName)

	default:
		// 未知类型默认需要许可证（安全优先）
		return true
	}
}

// GetLicenseType 获取模块的许可证类型
func (r *ModuleAuthorizationRules) GetLicenseType(moduleName string) string {
	if rule, exists := r.rules[moduleName]; exists {
		return rule.LicenseType
	}
	
	// 对于vendors模块，检查模块签名
	if r.isVendorsModule(moduleName) {
		return r.getVendorsModuleLicenseType(moduleName)
	}
	
	return "official" // 默认需要官方许可证
}

// GetMinimumEdition 获取模块所需的最低版本
func (r *ModuleAuthorizationRules) GetMinimumEdition(moduleName string) string {
	if rule, exists := r.rules[moduleName]; exists {
		return rule.MinimumEdition
	}
	
	return "enterprise" // 默认需要企业版
}

// checkOptionalModuleLicense 检查可选模块的许可证需求
func (r *ModuleAuthorizationRules) checkOptionalModuleLicense(moduleName string) bool {
	// 可选模块的授权规则可以通过以下方式确定：
	// 1. 模块自身的配置文件声明（但需要签名验证防篡改）
	// 2. 模块的特征分析（如文件结构、功能复杂度等）
	// 3. 官方维护的可信模块列表

	// 检查是否在官方免费模块列表中
	if r.isOfficialFreeModule(moduleName) {
		return false
	}

	// 检查模块配置的签名验证
	if r.hasValidFreeModuleSignature(moduleName) {
		return false
	}

	// 默认可选模块需要许可证
	return true
}

// isOfficialFreeModule 检查是否是官方免费模块
func (r *ModuleAuthorizationRules) isOfficialFreeModule(moduleName string) bool {
	// 官方维护的免费可选模块列表
	// 这个列表可以通过配置文件或远程服务器获取，但需要签名验证
	officialFreeModules := r.getOfficialFreeModuleList()

	for _, freeModule := range officialFreeModules {
		if moduleName == freeModule {
			return true
		}
	}

	return false
}

// hasValidFreeModuleSignature 检查模块是否有有效的免费模块签名
func (r *ModuleAuthorizationRules) hasValidFreeModuleSignature(moduleName string) bool {
	// 通过模块路径获取配置
	// 这里需要从模块注册信息中获取模块路径
	modulePath := r.getModulePath(moduleName)
	if modulePath == "" {
		return false
	}

	// 使用配置验证器检查模块是否免费
	isFree, err := r.configValidator.IsModuleFree(modulePath)
	if err != nil {
		// 验证失败，默认需要许可证
		return false
	}

	return isFree
}

// getModulePath 获取模块路径
func (r *ModuleAuthorizationRules) getModulePath(moduleName string) string {
	// TODO: 从模块注册表中获取模块路径
	// 这里需要与ModuleProxyFactory或ModuleManager集成

	// 暂时返回空字符串，需要实现具体逻辑
	return ""
}

// getOfficialFreeModuleList 获取官方免费模块列表
func (r *ModuleAuthorizationRules) getOfficialFreeModuleList() []string {
	// 这个列表可以从安全的配置源获取
	// 例如：加密的配置文件、远程API、硬编码列表等
	return []string{
		"theme",      // 基础主题功能
		"backup",     // 基础备份功能
		"cache",      // 基础缓存功能
		// 可以根据需要添加更多免费模块
	}
}

// isVendorsModule 检查是否是vendors模块
func (r *ModuleAuthorizationRules) isVendorsModule(moduleName string) bool {
	// vendors模块通常有特定的命名规则
	return strings.Contains(moduleName, "/") || strings.HasPrefix(moduleName, "vendors.")
}

// checkVendorsModuleLicense 检查vendors模块的许可证需求
func (r *ModuleAuthorizationRules) checkVendorsModuleLicense(moduleName string) bool {
	// 通过模块签名验证来确定是否需要许可证
	signature := r.getModuleSignature(moduleName)
	
	// 检查已知的免费vendors模块签名
	freeModuleSignatures := r.getFreeVendorsModuleSignatures()
	
	for _, freeSignature := range freeModuleSignatures {
		if signature == freeSignature {
			return false // 免费模块
		}
	}
	
	return true // 默认需要许可证
}

// getVendorsModuleLicenseType 获取vendors模块的许可证类型
func (r *ModuleAuthorizationRules) getVendorsModuleLicenseType(moduleName string) string {
	// 根据模块路径判断许可证类型
	if strings.HasPrefix(moduleName, "gacms/") {
		return "official" // 官方vendors模块
	}
	
	return "third_party" // 第三方vendors模块
}

// getModuleSignature 获取模块签名
func (r *ModuleAuthorizationRules) getModuleSignature(moduleName string) string {
	// 这里应该计算模块文件的哈希值
	// 暂时使用模块名的哈希作为示例
	hash := sha256.Sum256([]byte(moduleName))
	return hex.EncodeToString(hash[:])
}

// getFreeVendorsModuleSignatures 获取免费vendors模块的签名列表
func (r *ModuleAuthorizationRules) getFreeVendorsModuleSignatures() []string {
	// 这里应该包含已知免费模块的签名
	// 可以从配置文件或远程服务器获取
	return []string{
		// 示例免费模块签名
		"a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3", // 示例签名
	}
}

// ValidateModuleIntegrity 验证模块完整性
func (r *ModuleAuthorizationRules) ValidateModuleIntegrity(moduleName, modulePath string) bool {
	// 计算模块文件的实际哈希
	actualSignature := r.calculateModuleHash(modulePath)
	
	// 获取预期的签名
	expectedSignature := r.getExpectedModuleSignature(moduleName)
	
	return actualSignature == expectedSignature
}

// calculateModuleHash 计算模块文件哈希
func (r *ModuleAuthorizationRules) calculateModuleHash(modulePath string) string {
	// TODO: 实现实际的文件哈希计算
	// 这里应该计算模块目录下所有文件的哈希
	return ""
}

// getExpectedModuleSignature 获取预期的模块签名
func (r *ModuleAuthorizationRules) getExpectedModuleSignature(moduleName string) string {
	// TODO: 从安全的地方获取预期签名
	// 可以是硬编码、加密配置或远程服务器
	return ""
}

// IsModuleAuthorized 综合检查模块是否授权（安全版本）
func (r *ModuleAuthorizationRules) IsModuleAuthorized(moduleName, currentEdition string, moduleType ModuleType) bool {
	// 1. 检查是否需要许可证
	if !r.RequiresLicense(moduleName, moduleType) {
		return true // 免费模块
	}
	
	// 2. 检查当前版本是否满足最低要求
	minimumEdition := r.GetMinimumEdition(moduleName)
	if !r.isEditionSufficient(currentEdition, minimumEdition) {
		return false
	}
	
	// 3. 对于vendors模块，还需要验证完整性
	if r.isVendorsModule(moduleName) {
		// TODO: 验证模块完整性
		// return r.ValidateModuleIntegrity(moduleName, modulePath)
	}
	
	return true
}

// isEditionSufficient 检查当前版本是否满足最低要求
func (r *ModuleAuthorizationRules) isEditionSufficient(current, minimum string) bool {
	editionLevels := map[string]int{
		"community":    0,
		"personal":     1,
		"professional": 2,
		"enterprise":   3,
	}
	
	currentLevel, exists := editionLevels[current]
	if !exists {
		return false
	}
	
	minimumLevel, exists := editionLevels[minimum]
	if !exists {
		return false
	}
	
	return currentLevel >= minimumLevel
}

// AddCustomRule 添加自定义规则（仅限管理员）
func (r *ModuleAuthorizationRules) AddCustomRule(rule *ModuleAuthorizationRule) error {
	// 这个方法应该有严格的权限控制
	// 只有超级管理员才能调用
	r.rules[rule.ModuleName] = rule
	return nil
}

// GetAllRules 获取所有授权规则
func (r *ModuleAuthorizationRules) GetAllRules() map[string]*ModuleAuthorizationRule {
	// 返回副本，防止外部修改
	result := make(map[string]*ModuleAuthorizationRule)
	for k, v := range r.rules {
		ruleCopy := *v
		result[k] = &ruleCopy
	}
	return result
}
