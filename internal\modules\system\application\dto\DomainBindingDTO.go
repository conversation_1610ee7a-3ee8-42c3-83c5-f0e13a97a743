/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON> <<EMAIL>>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/system/application/dto/DomainBindingDTO.go
 * @Description: Defines DTOs for DomainBinding operations.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package dto

type DomainBindingDTO struct {
	ID          uint   `json:"id"`
	Domain      string `json:"domain"`
	SiteID      uint   `json:"site_id"`
	BindingType string `json:"binding_type"`
	ModuleSlug  *string `json:"module_slug,omitempty"`
	CategoryID  *uint   `json:"category_id,omitempty"`

	// URL重写配置
	URLRewriteEnabled bool   `json:"url_rewrite_enabled"`
	DefaultController string `json:"default_controller,omitempty"`
	DefaultAction     string `json:"default_action,omitempty"`

	// URL重写规则
	URLRules []URLRewriteRuleDTO `json:"url_rules,omitempty"`

	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

// URLRewriteRuleDTO URL重写规则DTO
type URLRewriteRuleDTO struct {
	ID              uint   `json:"id"`
	DomainBindingID uint   `json:"domain_binding_id"`
	RuleName        string `json:"rule_name"`
	Pattern         string `json:"pattern"`
	Replacement     string `json:"replacement"`
	Priority        int    `json:"priority"`
	IsActive        bool   `json:"is_active"`
	CreatedAt       string `json:"created_at"`
	UpdatedAt       string `json:"updated_at"`
}

// URLRewriteRuleCreateDTO 创建URL重写规则DTO
type URLRewriteRuleCreateDTO struct {
	DomainBindingID uint   `json:"domain_binding_id" binding:"required"`
	RuleName        string `json:"rule_name" binding:"required,max=100"`
	Pattern         string `json:"pattern" binding:"required,max=500"`
	Replacement     string `json:"replacement" binding:"required,max=500"`
	Priority        int    `json:"priority"`
}

// URLRewriteRuleUpdateDTO 更新URL重写规则DTO
type URLRewriteRuleUpdateDTO struct {
	RuleName    string `json:"rule_name" binding:"required,max=100"`
	Pattern     string `json:"pattern" binding:"required,max=500"`
	Replacement string `json:"replacement" binding:"required,max=500"`
	Priority    int    `json:"priority"`
	IsActive    bool   `json:"is_active"`
}

type DomainBindingCreateDTO struct {
	Domain      string  `json:"domain" binding:"required"`
	SiteID      uint    `json:"site_id" binding:"gte=0"`
	BindingType string  `json:"binding_type" binding:"required,oneof=module category platform_admin"`
	ModuleSlug  *string `json:"module_slug"`
	CategoryID  *uint   `json:"category_id"`
} 