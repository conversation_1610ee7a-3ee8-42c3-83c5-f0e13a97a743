/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/contenttype/port/http/controller/ContentTypeController.go
 * @Description: Controller for managing content type definitions.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package controller

import (
	"gacms/internal/modules/contenttype/application/service"
	"gacms/internal/modules/contenttype/domain/model"
	"net/http"

	"github.com/gin-gonic/gin"
)

type ContentTypeController struct {
	svc *service.ContentTypeService
}

func NewContentTypeController(svc *service.ContentTypeService) *ContentTypeController {
	return &ContentTypeController{svc: svc}
}

func (c *ContentTypeController) CreateContentType(ctx *gin.Context) {
	var req model.ContentType
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := c.svc.CreateContentType(&req); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create content type: " + err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, req)
}

func (c *ContentTypeController) ListContentTypes(ctx *gin.Context) {
	types, err := c.svc.GetAll()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list content types: " + err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, types)
}

func (c *ContentTypeController) GetBySlug(ctx *gin.Context) {
	slug := ctx.Param("slug")
	contentType, err := c.svc.GetBySlug(slug)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Content type not found"})
		return
	}
	ctx.JSON(http.StatusOK, contentType)
} 