<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 布局管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .content-section-title::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 5rem;
            height: 3px;
            background: linear-gradient(to right, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .user-link::after, .article-link::after, .version-link::after, .plugin-link::after, .comment-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 1px;
            background: linear-gradient(to right, #007bff, #00c6ff);
            transition: width 0.2s ease;
        }
        
        .user-link:hover, .article-link:hover, .version-link:hover, .plugin-link:hover, .comment-link:hover {
            color: #00c6ff;
        }
        
        .user-link:hover::after, .article-link:hover::after, .version-link:hover::after, .plugin-link:hover::after, .comment-link:hover::after {
            width: 100%;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            background: linear-gradient(145deg, #1e1e1e, #2a2a2a);
            border-left: 4px solid #007bff;
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            backdrop-filter: blur(20px);
            z-index: 1000;
            transform: translateX(120%);
            opacity: 0;
            transition: all 0.2s ease;
            max-width: 350px;
        }
        
        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }
        
        /* 布局拖拽区域样式 */
        .layout-grid {
            background-image: linear-gradient(#2a2a2a 1px, transparent 1px),
                              linear-gradient(to right, #2a2a2a 1px, transparent 1px);
            background-size: 20px 20px;
            background-color: rgba(30, 30, 30, 0.3);
        }
        
        .layout-item {
            cursor: move;
            transition: all 0.2s ease;
        }
        
        .layout-item:hover {
            box-shadow: 0 0 0 2px #007bff;
        }
        
        .layout-item.dragging {
            opacity: 0.5;
            box-shadow: 0 0 0 2px #007bff;
        }
        
        .layout-placeholder {
            background-color: rgba(0, 123, 255, 0.2);
            border: 1px dashed #007bff;
            border-radius: 6px;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">布局管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <a href="#" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-plus text-white"></i>
                                </span>
                                创建布局
                            </span>
                        </a>
                        <a href="#" class="flex items-center justify-center bg-gradient-to-r from-green-500 to-green-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-green-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-save text-white"></i>
                                </span>
                                保存布局
                            </span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 布局选择列表 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold text-white mb-4">选择现有布局</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    <!-- 布局项 1 -->
                    <div class="border border-gray-700 rounded-lg overflow-hidden bg-gray-800/20 hover:bg-gray-800/30 transition-all p-4 flex flex-col">
                        <div class="text-lg font-medium text-white mb-2">默认布局</div>
                        <div class="text-sm text-gray-400 mb-3">全宽三栏布局，适用于大多数页面</div>
                        <div class="w-full h-24 bg-gray-800/40 rounded-md mb-4 p-1">
                            <div class="bg-blue-500/20 w-full h-5 rounded mb-1"></div>
                            <div class="flex h-12 gap-1">
                                <div class="bg-blue-500/20 w-1/4 h-full rounded"></div>
                                <div class="bg-blue-500/50 w-2/4 h-full rounded"></div>
                                <div class="bg-blue-500/20 w-1/4 h-full rounded"></div>
                            </div>
                        </div>
                        <div class="flex justify-between mt-auto">
                            <button class="text-blue-400 hover:text-blue-300 text-sm">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                            <button class="text-blue-400 hover:text-blue-300 text-sm">
                                <i class="fas fa-clone mr-1"></i>复制
                            </button>
                        </div>
                    </div>

                    <!-- 布局项 2 -->
                    <div class="border border-gray-700 rounded-lg overflow-hidden bg-gray-800/20 hover:bg-gray-800/30 transition-all p-4 flex flex-col">
                        <div class="text-lg font-medium text-white mb-2">博客布局</div>
                        <div class="text-sm text-gray-400 mb-3">适合文章内容和侧边栏</div>
                        <div class="w-full h-24 bg-gray-800/40 rounded-md mb-4 p-1">
                            <div class="bg-green-500/20 w-full h-5 rounded mb-1"></div>
                            <div class="flex h-12 gap-1">
                                <div class="bg-green-500/50 w-3/4 h-full rounded"></div>
                                <div class="bg-green-500/20 w-1/4 h-full rounded"></div>
                            </div>
                        </div>
                        <div class="flex justify-between mt-auto">
                            <button class="text-blue-400 hover:text-blue-300 text-sm">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                            <button class="text-blue-400 hover:text-blue-300 text-sm">
                                <i class="fas fa-clone mr-1"></i>复制
                            </button>
                        </div>
                    </div>

                    <!-- 布局项 3 -->
                    <div class="border border-gray-700 rounded-lg overflow-hidden bg-gray-800/20 hover:bg-gray-800/30 transition-all p-4 flex flex-col">
                        <div class="text-lg font-medium text-white mb-2">产品布局</div>
                        <div class="text-sm text-gray-400 mb-3">适合产品展示和电商</div>
                        <div class="w-full h-24 bg-gray-800/40 rounded-md mb-4 p-1">
                            <div class="bg-purple-500/20 w-full h-5 rounded mb-1"></div>
                            <div class="grid grid-cols-2 gap-1 h-12">
                                <div class="bg-purple-500/20 h-full rounded"></div>
                                <div class="bg-purple-500/20 h-full rounded"></div>
                                <div class="bg-purple-500/20 h-full rounded"></div>
                                <div class="bg-purple-500/20 h-full rounded"></div>
                            </div>
                        </div>
                        <div class="flex justify-between mt-auto">
                            <button class="text-blue-400 hover:text-blue-300 text-sm">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                            <button class="text-blue-400 hover:text-blue-300 text-sm">
                                <i class="fas fa-clone mr-1"></i>复制
                            </button>
                        </div>
                    </div>

                    <!-- 布局项 4 -->
                    <div class="border border-gray-700 rounded-lg overflow-hidden bg-gray-800/20 hover:bg-gray-800/30 transition-all p-4 flex flex-col">
                        <div class="text-lg font-medium text-white mb-2">落地页布局</div>
                        <div class="text-sm text-gray-400 mb-3">长滚动单栏布局，适合落地页</div>
                        <div class="w-full h-24 bg-gray-800/40 rounded-md mb-4 p-1">
                            <div class="bg-yellow-500/20 w-full h-5 rounded mb-1"></div>
                            <div class="bg-yellow-500/50 w-full h-5 rounded mb-1"></div>
                            <div class="bg-yellow-500/20 w-full h-5 rounded"></div>
                        </div>
                        <div class="flex justify-between mt-auto">
                            <button class="text-blue-400 hover:text-blue-300 text-sm">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                            <button class="text-blue-400 hover:text-blue-300 text-sm">
                                <i class="fas fa-clone mr-1"></i>复制
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 布局编辑区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-white">编辑布局：默认布局</h3>
                    
                    <!-- 操作按钮组 -->
                    <div class="flex flex-wrap gap-3 mt-3 sm:mt-0">
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition">
                            <i class="fas fa-desktop mr-2"></i>桌面视图
                        </button>
                        <button class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition">
                            <i class="fas fa-tablet-alt mr-2"></i>平板视图
                        </button>
                        <button class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition">
                            <i class="fas fa-mobile-alt mr-2"></i>手机视图
                        </button>
                    </div>
                </div>

                <!-- 布局设计区域 -->
                <div class="mt-6">
                    <div class="flex gap-6 lg:flex-nowrap flex-wrap">
                        <!-- 左侧：组件列表 -->
                        <div class="min-w-[200px] lg:w-1/5 w-full">
                            <div class="bg-gray-800/20 rounded-lg p-4">
                                <h4 class="font-medium text-white mb-3">布局元素</h4>
                                
                                <!-- 组件类别 -->
                                <div class="mb-4">
                                    <div class="text-sm font-medium text-gray-300 mb-2">结构组件</div>
                                    <div class="space-y-2">
                                        <div class="layout-item bg-gray-700 p-3 rounded-lg flex items-center">
                                            <i class="fas fa-columns text-blue-400 mr-2"></i>
                                            <span class="text-sm">行容器</span>
                                        </div>
                                        <div class="layout-item bg-gray-700 p-3 rounded-lg flex items-center">
                                            <i class="fas fa-square text-blue-400 mr-2"></i>
                                            <span class="text-sm">列容器</span>
                                        </div>
                                        <div class="layout-item bg-gray-700 p-3 rounded-lg flex items-center">
                                            <i class="fas fa-grip-horizontal text-blue-400 mr-2"></i>
                                            <span class="text-sm">网格</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 组件类别 -->
                                <div class="mb-4">
                                    <div class="text-sm font-medium text-gray-300 mb-2">内容区块</div>
                                    <div class="space-y-2">
                                        <div class="layout-item bg-gray-700 p-3 rounded-lg flex items-center">
                                            <i class="fas fa-heading text-green-400 mr-2"></i>
                                            <span class="text-sm">标题区块</span>
                                        </div>
                                        <div class="layout-item bg-gray-700 p-3 rounded-lg flex items-center">
                                            <i class="fas fa-align-left text-green-400 mr-2"></i>
                                            <span class="text-sm">文本区块</span>
                                        </div>
                                        <div class="layout-item bg-gray-700 p-3 rounded-lg flex items-center">
                                            <i class="fas fa-image text-green-400 mr-2"></i>
                                            <span class="text-sm">图片区块</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 组件类别 -->
                                <div>
                                    <div class="text-sm font-medium text-gray-300 mb-2">功能区块</div>
                                    <div class="space-y-2">
                                        <div class="layout-item bg-gray-700 p-3 rounded-lg flex items-center">
                                            <i class="fas fa-list text-purple-400 mr-2"></i>
                                            <span class="text-sm">文章列表</span>
                                        </div>
                                        <div class="layout-item bg-gray-700 p-3 rounded-lg flex items-center">
                                            <i class="fas fa-search text-purple-400 mr-2"></i>
                                            <span class="text-sm">搜索区块</span>
                                        </div>
                                        <div class="layout-item bg-gray-700 p-3 rounded-lg flex items-center">
                                            <i class="fas fa-th-large text-purple-400 mr-2"></i>
                                            <span class="text-sm">分类区块</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 右侧：布局设计区域 -->
                        <div class="flex-1">
                            <div class="layout-grid bg-gray-800/20 rounded-lg p-6 min-h-[500px]">
                                <!-- 顶部区域 -->
                                <div class="bg-blue-900/20 border border-blue-800/30 rounded-lg p-4 mb-4">
                                    <div class="flex items-center justify-between">
                                        <span class="text-blue-400 text-sm">页头区域</span>
                                        <div class="flex gap-2">
                                            <button class="text-gray-400 hover:text-white">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                            <button class="text-gray-400 hover:text-white">
                                                <i class="fas fa-arrows-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 主要内容区域 -->
                                <div class="flex gap-4 mb-4">
                                    <!-- 侧边栏 -->
                                    <div class="bg-purple-900/20 border border-purple-800/30 rounded-lg p-4 w-1/4">
                                        <div class="flex items-center justify-between mb-4">
                                            <span class="text-purple-400 text-sm">左侧栏</span>
                                            <div class="flex gap-2">
                                                <button class="text-gray-400 hover:text-white">
                                                    <i class="fas fa-cog"></i>
                                                </button>
                                                <button class="text-gray-400 hover:text-white">
                                                    <i class="fas fa-arrows-alt"></i>
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <!-- 内部区块 -->
                                        <div class="bg-gray-800/40 border border-gray-700 rounded-lg p-3 mb-3">
                                            <div class="flex items-center justify-between">
                                                <span class="text-gray-300 text-sm">分类区块</span>
                                                <div class="flex gap-1">
                                                    <button class="text-gray-400 hover:text-white text-xs">
                                                        <i class="fas fa-arrows-alt"></i>
                                                    </button>
                                                    <button class="text-gray-400 hover:text-white text-xs">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 内部区块 -->
                                        <div class="bg-gray-800/40 border border-gray-700 rounded-lg p-3">
                                            <div class="flex items-center justify-between">
                                                <span class="text-gray-300 text-sm">标签云</span>
                                                <div class="flex gap-1">
                                                    <button class="text-gray-400 hover:text-white text-xs">
                                                        <i class="fas fa-arrows-alt"></i>
                                                    </button>
                                                    <button class="text-gray-400 hover:text-white text-xs">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 主内容区 -->
                                    <div class="bg-green-900/20 border border-green-800/30 rounded-lg p-4 flex-1">
                                        <div class="flex items-center justify-between mb-4">
                                            <span class="text-green-400 text-sm">主内容区</span>
                                            <div class="flex gap-2">
                                                <button class="text-gray-400 hover:text-white">
                                                    <i class="fas fa-cog"></i>
                                                </button>
                                                <button class="text-gray-400 hover:text-white">
                                                    <i class="fas fa-arrows-alt"></i>
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <!-- 内部区块 -->
                                        <div class="bg-gray-800/40 border border-gray-700 rounded-lg p-3 mb-3">
                                            <div class="flex items-center justify-between">
                                                <span class="text-gray-300 text-sm">标题区块</span>
                                                <div class="flex gap-1">
                                                    <button class="text-gray-400 hover:text-white text-xs">
                                                        <i class="fas fa-arrows-alt"></i>
                                                    </button>
                                                    <button class="text-gray-400 hover:text-white text-xs">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 内部区块 -->
                                        <div class="bg-gray-800/40 border border-gray-700 rounded-lg p-3">
                                            <div class="flex items-center justify-between">
                                                <span class="text-gray-300 text-sm">文章内容</span>
                                                <div class="flex gap-1">
                                                    <button class="text-gray-400 hover:text-white text-xs">
                                                        <i class="fas fa-arrows-alt"></i>
                                                    </button>
                                                    <button class="text-gray-400 hover:text-white text-xs">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 右侧栏 -->
                                    <div class="bg-yellow-900/20 border border-yellow-800/30 rounded-lg p-4 w-1/4">
                                        <div class="flex items-center justify-between mb-4">
                                            <span class="text-yellow-400 text-sm">右侧栏</span>
                                            <div class="flex gap-2">
                                                <button class="text-gray-400 hover:text-white">
                                                    <i class="fas fa-cog"></i>
                                                </button>
                                                <button class="text-gray-400 hover:text-white">
                                                    <i class="fas fa-arrows-alt"></i>
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <!-- 内部区块 -->
                                        <div class="bg-gray-800/40 border border-gray-700 rounded-lg p-3 mb-3">
                                            <div class="flex items-center justify-between">
                                                <span class="text-gray-300 text-sm">热门文章</span>
                                                <div class="flex gap-1">
                                                    <button class="text-gray-400 hover:text-white text-xs">
                                                        <i class="fas fa-arrows-alt"></i>
                                                    </button>
                                                    <button class="text-gray-400 hover:text-white text-xs">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 底部区域 -->
                                <div class="bg-red-900/20 border border-red-800/30 rounded-lg p-4">
                                    <div class="flex items-center justify-between">
                                        <span class="text-red-400 text-sm">页脚区域</span>
                                        <div class="flex gap-2">
                                            <button class="text-gray-400 hover:text-white">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                            <button class="text-gray-400 hover:text-white">
                                                <i class="fas fa-arrows-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 布局设置 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold text-white mb-4">布局设置</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 基础信息 -->
                    <div>
                        <h4 class="font-medium text-blue-400 mb-3">基础信息</h4>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-gray-300 mb-2">布局名称</label>
                                <input type="text" value="默认布局" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-gray-300 mb-2">布局描述</label>
                                <textarea class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent" rows="3">全宽三栏布局，适用于大多数页面</textarea>
                            </div>
                            <div>
                                <label class="block text-gray-300 mb-2">适用页面</label>
                                <div class="flex flex-wrap gap-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" class="form-checkbox mr-2" checked>
                                        <span>文章页面</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="form-checkbox mr-2" checked>
                                        <span>首页</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="form-checkbox mr-2">
                                        <span>分类页</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="form-checkbox mr-2">
                                        <span>搜索结果</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 布局属性 -->
                    <div>
                        <h4 class="font-medium text-blue-400 mb-3">布局属性</h4>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-gray-300 mb-2">最大宽度</label>
                                <div class="flex">
                                    <input type="number" value="1200" class="w-24 bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <select class="bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent ml-2">
                                        <option>px</option>
                                        <option>%</option>
                                        <option>em</option>
                                        <option>rem</option>
                                    </select>
                                </div>
                            </div>
                            <div>
                                <label class="block text-gray-300 mb-2">内边距</label>
                                <div class="grid grid-cols-4 gap-2">
                                    <div>
                                        <label class="block text-xs text-gray-400 mb-1">上</label>
                                        <input type="number" value="20" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                    <div>
                                        <label class="block text-xs text-gray-400 mb-1">右</label>
                                        <input type="number" value="20" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                    <div>
                                        <label class="block text-xs text-gray-400 mb-1">下</label>
                                        <input type="number" value="20" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                    <div>
                                        <label class="block text-xs text-gray-400 mb-1">左</label>
                                        <input type="number" value="20" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                </div>
                            </div>
                            <div>
                                <label class="block text-gray-300 mb-2">列间距</label>
                                <input type="range" min="0" max="100" value="30" class="w-full">
                                <div class="flex justify-between text-xs text-gray-400 mt-1">
                                    <span>0</span>
                                    <span>30px</span>
                                    <span>100</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <!-- 通知组件 -->
    <div class="notification" id="notification">
        <div class="mr-3">
            <i class="fas fa-check-circle text-green-400 text-xl"></i>
        </div>
        <div class="flex-1">
            <h4 class="text-white text-sm font-bold">操作成功</h4>
            <p class="text-gray-300 text-xs">布局保存成功。</p>
        </div>
    </div>

    <!-- 加载必要的JS -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
</body>
</html> 