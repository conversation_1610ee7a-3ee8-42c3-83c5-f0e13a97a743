/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2024-07-16
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2024-07-16
 * @FilePath: internal/modules/user/domain/contract/RoleRepository.go
 * @Description: Defines the repository contract for the Role entity.
 *
 * © 2024 GACMS. All rights reserved.
 */
package contract

import (
	"gacms/internal/modules/user/domain/model"
	"gacms/pkg/database"
	"github.com/gin-gonic/gin"
)

// RoleRepository defines the persistence operations for the Role model.
type RoleRepository interface {
	Create(ctx *gin.Context, role *model.Role) error
	GetByID(ctx *gin.Context, id uint) (*model.Role, error)
	GetByName(ctx *gin.Context, siteID uint, name string, userType model.UserType) (*model.Role, error)
	List(ctx *gin.Context, siteID uint, userType model.UserType, options *database.ListOptions) ([]*model.Role, int64, error)
	Update(ctx *gin.Context, role *model.Role) error
	Delete(ctx *gin.Context, id uint) error

	AddPermissionToRole(ctx *gin.Context, roleID uint, permissionID uint) error
	RemovePermissionFromRole(ctx *gin.Context, roleID uint, permissionID uint) error
	FindPermissionsByRoleID(ctx *gin.Context, roleID uint) ([]*model.Permission, error)
	ReplacePermissionsForRole(ctx *gin.Context, roleID uint, permissionIDs []uint) error
	
	AddRoleToUser(ctx *gin.Context, userID uint, roleID uint, userType model.UserType) error
	RemoveRoleFromUser(ctx *gin.Context, userID uint, roleID uint, userType model.UserType) error
	FindRolesByUserID(ctx *gin.Context, userID uint, userType model.UserType) ([]*model.Role, error)
} 