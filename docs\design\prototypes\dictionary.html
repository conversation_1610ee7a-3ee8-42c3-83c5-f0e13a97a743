<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 数据字典</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .table-header {
            background-color: rgba(31, 41, 55, 0.8);
            position: sticky;
            top: 0;
            z-index: 10;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="devtools.html" class="hover:text-white">开发工具</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">数据字典</span>
            </div>

            <!-- 数据字典标题区 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">数据字典</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <div class="relative">
                            <select class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white appearance-none pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="all" selected>所有表</option>
                                <option value="core">核心系统表</option>
                                <option value="content">内容管理表</option>
                                <option value="user">用户管理表</option>
                                <option value="config">配置表</option>
                            </select>
                            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                                <i class="fas fa-chevron-down text-xs"></i>
                            </div>
                        </div>
                        
                        <div class="relative">
                            <input type="text" placeholder="搜索表名或字段..." class="bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>
                        
                        <button class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30">
                            <i class="fas fa-file-export mr-2"></i>
                            导出文档
                        </button>
                    </div>
                </div>
            </div>

            <!-- 数据库概览 -->
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-6">
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-5">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-table text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">总表数</div>
                            <div class="text-xl font-semibold text-white">42</div>
                            <div class="text-xs text-blue-400 mt-0.5">系统表: 28 | 自定义表: 14</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-5">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-columns text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">总字段数</div>
                            <div class="text-xl font-semibold text-white">376</div>
                            <div class="text-xs text-green-400 mt-0.5">平均每表 9 个字段</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-5">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-link text-purple-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">关系数</div>
                            <div class="text-xl font-semibold text-white">54</div>
                            <div class="text-xs text-purple-400 mt-0.5">外键: 48 | 多对多: 6</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-5">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-clock text-yellow-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">最后更新</div>
                            <div class="text-xl font-semibold text-white">今天</div>
                            <div class="text-xs text-yellow-400 mt-0.5">2025-06-05 10:30</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 表格列表 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold mb-4 text-white">核心表结构</h3>
                
                <!-- 表 accordions -->
                <div class="space-y-4">
                    <!-- 用户表 -->
                    <div class="border border-gray-700 rounded-lg overflow-hidden">
                        <!-- 表头部信息 -->
                        <div class="bg-gray-800/20 p-4 cursor-pointer flex justify-between items-center" data-target="ga_users-table">
                            <div class="flex items-center">
                                <i class="fas fa-table text-blue-500 mr-3"></i>
                                <div>
                                    <h4 class="text-white font-medium">ga_users</h4>
                                    <p class="text-gray-400 text-sm">用户信息表</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <span class="text-xs text-gray-500">17 个字段</span>
                                <i class="fas fa-chevron-down text-gray-500 transition-transform transform"></i>
                            </div>
                        </div>
                        
                        <!-- 表字段详情 -->
                        <div id="ga_users-table" class="hidden">
                            <div class="overflow-x-auto">
                                <table class="min-w-full">
                                    <thead>
                                        <tr class="table-header">
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">字段名</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">类型</th>
                                            <th class="py-3 px-4 text-center text-xs font-medium text-gray-400 uppercase tracking-wider">允许NULL</th>
                                            <th class="py-3 px-4 text-center text-xs font-medium text-gray-400 uppercase tracking-wider">键类型</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">默认值</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">描述</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                            <td class="py-3 px-4 text-sm text-white font-medium">id</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">int(11)</td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="inline-block w-5 h-5 rounded-full bg-red-500/20 text-red-400 flex items-center justify-center">
                                                    <i class="fas fa-times text-xs"></i>
                                                </span>
                                            </td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="bg-blue-500/20 text-blue-400 px-2 py-0.5 rounded text-xs">PK</span>
                                            </td>
                                            <td class="py-3 px-4 text-sm text-gray-300">-</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">用户ID</td>
                                        </tr>
                                        <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                            <td class="py-3 px-4 text-sm text-white font-medium">username</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">varchar(50)</td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="inline-block w-5 h-5 rounded-full bg-red-500/20 text-red-400 flex items-center justify-center">
                                                    <i class="fas fa-times text-xs"></i>
                                                </span>
                                            </td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="bg-yellow-500/20 text-yellow-400 px-2 py-0.5 rounded text-xs">UNI</span>
                                            </td>
                                            <td class="py-3 px-4 text-sm text-gray-300">-</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">用户名，唯一</td>
                                        </tr>
                                        <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                            <td class="py-3 px-4 text-sm text-white font-medium">email</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">varchar(100)</td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="inline-block w-5 h-5 rounded-full bg-red-500/20 text-red-400 flex items-center justify-center">
                                                    <i class="fas fa-times text-xs"></i>
                                                </span>
                                            </td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="bg-yellow-500/20 text-yellow-400 px-2 py-0.5 rounded text-xs">UNI</span>
                                            </td>
                                            <td class="py-3 px-4 text-sm text-gray-300">-</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">电子邮箱，唯一</td>
                                        </tr>
                                        <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                            <td class="py-3 px-4 text-sm text-white font-medium">password_hash</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">varchar(255)</td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="inline-block w-5 h-5 rounded-full bg-red-500/20 text-red-400 flex items-center justify-center">
                                                    <i class="fas fa-times text-xs"></i>
                                                </span>
                                            </td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="bg-gray-500/20 text-gray-400 px-2 py-0.5 rounded text-xs">-</span>
                                            </td>
                                            <td class="py-3 px-4 text-sm text-gray-300">-</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">密码哈希值</td>
                                        </tr>
                                        <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                            <td class="py-3 px-4 text-sm text-white font-medium">nickname</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">varchar(50)</td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="inline-block w-5 h-5 rounded-full bg-green-500/20 text-green-400 flex items-center justify-center">
                                                    <i class="fas fa-check text-xs"></i>
                                                </span>
                                            </td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="bg-gray-500/20 text-gray-400 px-2 py-0.5 rounded text-xs">-</span>
                                            </td>
                                            <td class="py-3 px-4 text-sm text-gray-300">NULL</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">用户昵称</td>
                                        </tr>
                                        <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                            <td class="py-3 px-4 text-sm text-white font-medium">avatar</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">varchar(255)</td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="inline-block w-5 h-5 rounded-full bg-green-500/20 text-green-400 flex items-center justify-center">
                                                    <i class="fas fa-check text-xs"></i>
                                                </span>
                                            </td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="bg-gray-500/20 text-gray-400 px-2 py-0.5 rounded text-xs">-</span>
                                            </td>
                                            <td class="py-3 px-4 text-sm text-gray-300">NULL</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">头像URL</td>
                                        </tr>
                                        <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                            <td class="py-3 px-4 text-sm text-white font-medium">role_id</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">int(11)</td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="inline-block w-5 h-5 rounded-full bg-red-500/20 text-red-400 flex items-center justify-center">
                                                    <i class="fas fa-times text-xs"></i>
                                                </span>
                                            </td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="bg-purple-500/20 text-purple-400 px-2 py-0.5 rounded text-xs">FK</span>
                                            </td>
                                            <td class="py-3 px-4 text-sm text-gray-300">-</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">角色ID，关联ga_roles表</td>
                                        </tr>
                                        <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                            <td class="py-3 px-4 text-sm text-white font-medium">status</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">tinyint(1)</td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="inline-block w-5 h-5 rounded-full bg-red-500/20 text-red-400 flex items-center justify-center">
                                                    <i class="fas fa-times text-xs"></i>
                                                </span>
                                            </td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="bg-gray-500/20 text-gray-400 px-2 py-0.5 rounded text-xs">-</span>
                                            </td>
                                            <td class="py-3 px-4 text-sm text-gray-300">1</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">状态：1-正常，0-禁用</td>
                                        </tr>
                                        <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                            <td class="py-3 px-4 text-sm text-white font-medium">last_login</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">datetime</td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="inline-block w-5 h-5 rounded-full bg-green-500/20 text-green-400 flex items-center justify-center">
                                                    <i class="fas fa-check text-xs"></i>
                                                </span>
                                            </td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="bg-gray-500/20 text-gray-400 px-2 py-0.5 rounded text-xs">-</span>
                                            </td>
                                            <td class="py-3 px-4 text-sm text-gray-300">NULL</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">最后登录时间</td>
                                        </tr>
                                        <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                            <td class="py-3 px-4 text-sm text-white font-medium">created_at</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">datetime</td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="inline-block w-5 h-5 rounded-full bg-red-500/20 text-red-400 flex items-center justify-center">
                                                    <i class="fas fa-times text-xs"></i>
                                                </span>
                                            </td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="bg-gray-500/20 text-gray-400 px-2 py-0.5 rounded text-xs">-</span>
                                            </td>
                                            <td class="py-3 px-4 text-sm text-gray-300">CURRENT_TIMESTAMP</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">创建时间</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="p-4 bg-gray-800/10 border-t border-gray-700">
                                <div class="flex flex-wrap gap-4">
                                    <div>
                                        <span class="text-sm text-gray-400">索引:</span>
                                        <span class="ml-2 text-sm text-white">PRIMARY(id), idx_username(username), idx_email(email), idx_role_id(role_id)</span>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-400">外键:</span>
                                        <span class="ml-2 text-sm text-white">fk_user_role: role_id → ga_roles(id)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 角色表 -->
                    <div class="border border-gray-700 rounded-lg overflow-hidden">
                        <!-- 表头部信息 -->
                        <div class="bg-gray-800/20 p-4 cursor-pointer flex justify-between items-center" data-target="ga_roles-table">
                            <div class="flex items-center">
                                <i class="fas fa-table text-blue-500 mr-3"></i>
                                <div>
                                    <h4 class="text-white font-medium">ga_roles</h4>
                                    <p class="text-gray-400 text-sm">角色信息表</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <span class="text-xs text-gray-500">7 个字段</span>
                                <i class="fas fa-chevron-down text-gray-500 transition-transform transform"></i>
                            </div>
                        </div>
                        
                        <!-- 表字段详情 (默认隐藏) -->
                        <div id="ga_roles-table" class="hidden">
                            <div class="overflow-x-auto">
                                <table class="min-w-full">
                                    <thead>
                                        <tr class="table-header">
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">字段名</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">类型</th>
                                            <th class="py-3 px-4 text-center text-xs font-medium text-gray-400 uppercase tracking-wider">允许NULL</th>
                                            <th class="py-3 px-4 text-center text-xs font-medium text-gray-400 uppercase tracking-wider">键类型</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">默认值</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">描述</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                            <td class="py-3 px-4 text-sm text-white font-medium">id</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">int(11)</td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="inline-block w-5 h-5 rounded-full bg-red-500/20 text-red-400 flex items-center justify-center">
                                                    <i class="fas fa-times text-xs"></i>
                                                </span>
                                            </td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="bg-blue-500/20 text-blue-400 px-2 py-0.5 rounded text-xs">PK</span>
                                            </td>
                                            <td class="py-3 px-4 text-sm text-gray-300">-</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">角色ID</td>
                                        </tr>
                                        <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                            <td class="py-3 px-4 text-sm text-white font-medium">name</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">varchar(50)</td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="inline-block w-5 h-5 rounded-full bg-red-500/20 text-red-400 flex items-center justify-center">
                                                    <i class="fas fa-times text-xs"></i>
                                                </span>
                                            </td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="bg-yellow-500/20 text-yellow-400 px-2 py-0.5 rounded text-xs">UNI</span>
                                            </td>
                                            <td class="py-3 px-4 text-sm text-gray-300">-</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">角色名称，唯一</td>
                                        </tr>
                                        <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                            <td class="py-3 px-4 text-sm text-white font-medium">description</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">varchar(255)</td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="inline-block w-5 h-5 rounded-full bg-green-500/20 text-green-400 flex items-center justify-center">
                                                    <i class="fas fa-check text-xs"></i>
                                                </span>
                                            </td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="bg-gray-500/20 text-gray-400 px-2 py-0.5 rounded text-xs">-</span>
                                            </td>
                                            <td class="py-3 px-4 text-sm text-gray-300">NULL</td>
                                            <td class="py-3 px-4 text-sm text-gray-300">角色描述</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="p-4 bg-gray-800/10 border-t border-gray-700">
                                <div class="flex flex-wrap gap-4">
                                    <div>
                                        <span class="text-sm text-gray-400">索引:</span>
                                        <span class="ml-2 text-sm text-white">PRIMARY(id), UNI_name(name)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 文章表 -->
                    <div class="border border-gray-700 rounded-lg overflow-hidden">
                        <!-- 表头部信息 -->
                        <div class="bg-gray-800/20 p-4 cursor-pointer flex justify-between items-center" data-target="ga_articles-table">
                            <div class="flex items-center">
                                <i class="fas fa-table text-blue-500 mr-3"></i>
                                <div>
                                    <h4 class="text-white font-medium">ga_articles</h4>
                                    <p class="text-gray-400 text-sm">文章内容表</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <span class="text-xs text-gray-500">12 个字段</span>
                                <i class="fas fa-chevron-down text-gray-500 transition-transform transform"></i>
                            </div>
                        </div>
                        
                        <!-- 表字段详情 (默认隐藏) -->
                        <div id="ga_articles-table" class="hidden">
                            <div class="p-4 text-center text-gray-400">
                                点击展开查看表结构
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ER 图 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold mb-4 text-white">ER 图</h3>
                <div class="bg-gray-800/20 p-4 rounded-lg flex items-center justify-center min-h-[300px]">
                    <div class="text-center">
                        <i class="fas fa-project-diagram text-blue-500 text-5xl mb-4"></i>
                        <p class="text-gray-400">实体关系图显示区域</p>
                        <p class="text-sm text-gray-500 mt-2">点击下方按钮查看完整 ER 图</p>
                        <button class="mt-4 bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 px-4 py-2 rounded text-sm transition-colors">
                            查看完整 ER 图
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 表格展开/折叠功能
            const tableTitles = document.querySelectorAll('[data-target]');
            
            tableTitles.forEach(title => {
                title.addEventListener('click', function() {
                    const targetId = this.dataset.target;
                    const tableContent = document.getElementById(targetId);
                    
                    // 切换内容显示/隐藏
                    tableContent.classList.toggle('hidden');
                    
                    // 旋转箭头图标
                    const arrow = this.querySelector('.fa-chevron-down');
                    if (tableContent.classList.contains('hidden')) {
                        arrow.classList.remove('rotate-180');
                    } else {
                        arrow.classList.add('rotate-180');
                    }
                });
            });
            
            // 默认展开第一个表
            if (tableTitles.length > 0) {
                const firstTable = tableTitles[0];
                const firstTableId = firstTable.dataset.target;
                const firstTableContent = document.getElementById(firstTableId);
                
                firstTableContent.classList.remove('hidden');
                firstTable.querySelector('.fa-chevron-down').classList.add('rotate-180');
            }
        });
    </script>
</body>
</html> 