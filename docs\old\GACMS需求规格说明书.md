# 亘安内容管理系统（GACMS）需求规格说明书（个人开发版）  
**版本号：v1.0**  
**日期：2025-05-10**  
**作者：Clion Nieh**  

---

## **1. 引言**  
### **1.1 项目背景**  
随着企业数字化转型加速，传统网站管理系统在灵活性、安全性及多端适配方面已无法满足复杂业务需求。本项目旨在开发一个基于CodeIgniter 4的企业级内容管理系统GACMS，支持多语言、静态内容生成、前后台分离及多平台内容分发等核心功能，为企业提供高效、安全、可扩展的内容管理解决方案。  

### **1.2 目标用户群体**  
- **内容编辑人员**：多语言内容发布与管理  
- **系统管理员**：系统配置和权限管理，微信接口配置、文件路径管理  
- **开发人员**：通过插件系统和API接口进行功能扩展，主题开发  
- **市场人员**：负责内容撰写、审核和发布，通过微信公众号推广系统内容，分析用户行为等  
- **终端用户**：访问企业网站、微信公众号及微信小程序内容与互动  

### **1.3 文档目标**  
明确系统功能边界、非功能需求及技术约束，为个人开发提供清晰的实现指南。  

---

## **2. 总体描述**  
### **2.1 系统上下文**  
```mermaid
graph TD
    A[内容管理人员] --> B[后台管理界面]
    C[微信用户] --> D[微信接口服务]
    E[访客] --> F[前端展示层]
    B --> G[CodeIgniter 4 核心框架]
    D --> G
    F --> G
    G --> H[MySQL/MariaDB]
    G --> I[文件存储]
```

### **2.2 功能模块划分**  
| 模块类别       | 子模块                 | 功能描述                     |  
|----------------|------------------------|------------------------------|  
| 基础架构       | 入口控制、多语言、静态生成、缓存管理 | 支撑系统核心运行 |  
| 内容管理       | 栏目管理、专题管理、内容工作流、爬虫集成 | 内容创建与分发 |  
| 多语言支持     | 语言检测、视图渲染     | 自动识别浏览器语言并渲染内容 |  
| 文件管理       | 路径配置、存储策略     | 支持栏目/专题或日期目录存储  |  
| 安全与优化     | 域名绑定、文件上传控制、SEO、CORS、双因子验证 | 安全防护与性能优化 |  
| 扩展集成       | 模板组件、微信生态支持、API接口、数据可视化 | 系统可扩展性保障 |  

### **2.3 技术约束**  
- **核心框架**：CodeIgniter 4.4+（安装位置`vendor/framework`）  
- **数据库**：MySQL 8.0+/MariaDB 10.6+  
- **部署环境**：PHP 8.1-8.3，Apache/Nginx   
- **兼容性**：兼容主流浏览器（Chrome/Firefox/Safari）、移动设备，支持微信公众号及微信小程序  

---

## **3. 具体需求**  
### **3.1 功能需求**  
#### **3.1.1 基础架构功能**  
| 功能编号 | 功能名称 | 详细描述 | 输入 | 输出 |  
|----------|----------|----------|------|------|  
| FR-BA-01 | 唯一入口文件 | 所有请求通过`public/index.php`统一处理 | HTTP请求 | 响应数据 |  
| FR-BA-02 | 前后台分离 | 通过路由分组和命名空间区分前后台控制器 | 路由规则 | 控制器调用 |  
| FR-BA-03 | 子域名路由解析 | 自动解析子域名并路由到对应模块（如 `blog.example.com` 对接博客栏目） | 子域名 | 路由匹配结果 |  
| FR-BA-04 | 多语言支持 | 后端语言包（PHP数组）+前端动态加载JS语言文件，URL保持统一（如 `/about` 通过`Accept-Language`自动匹配用户语言） | 语言标识 | 本地化内容 |  
| FR-BA-05 | 静态内容生成 | 支持整站静态化，内容变更时仅重新生成受影响页面（增量更新） | 内容更新事件 | 静态HTML文件 |  
| FR-BA-06 | 缓存机制 | 支持文件缓存（默认）和Redis缓存（可选） | 缓存配置 | 缓存数据读写 |  
| FR-BA-07 | 部署方式 | 支持Composer安装或手动部署（无强制依赖） | 安装脚本 | 可运行系统 |  

#### **3.1.2 内容管理功能**  
| FR-CM-01 | 专题页面管理 | 支持手动创建专题页面，整合多个栏目或自定义内容 | 专题配置 | 专题页面 |  
| FR-CM-02 | 栏目/专题绑定二级域名 | 管理员可为栏目或专题绑定独立二级域名（如 `news.example.com`） | 域名配置 | DNS解析结果 |  
| FR-CM-03 | 内容推荐系统 | 结合手动关联、标签匹配、关键词自动关联的混合推荐策略 | 内容元数据 | 推荐内容列表 |  
| FR-CM-04 | 内容工作流 | 支持“草稿→审核→发布”流程，支持定时发布 | 审核操作 | 状态变更事件 |  
| FR-CM-05 | 自动爬取内容 | 配置爬虫规则（CSS选择器/XPath），自动抓取外部内容并生成站点内容 | 爬取规则 | 新增内容记录 |  

#### **3.1.3 系统安全与优化**  
| FR-SEC-01 | 后台域名绑定 | 限制后台管理界面仅可通过指定域名访问（如 `admin.example.com`） | 域名白名单 | 访问控制 |  
| FR-SEC-02 | 文件上传路径自定义 | 管理员可配置上传路径（如 `public/uploads/`），支持按栏目、日期存储 | 路径配置 | 文件存储位置 |  
| FR-SEC-03 | SEO优化系统 | 自动生成Meta标签、站点地图（XML）和结构化数据（JSON-LD） | 内容数据 | SEO元数据 |  
| FR-SEC-04 | CORS支持 | 配置允许跨域访问的域名和HTTP方法 | CORS规则 | 响应头设置 |  
| FR-SEC-05 | 双因子验证（TOTP） | 支持Google Authenticator等TOTP工具进行二次认证 | OTP代码 | 登录状态 |  

#### **3.1.4 扩展与集成**  
| FR-EXT-01 | 组件化模板系统 | 基于视图片段的组件系统（如 `{% component 'header' %}`） | 模板语法 | 渲染后的HTML |  
| FR-EXT-02 | 微信生态接入 | 提供RESTful API接口供公众号、小程序调用，支持内容推送、展示、用户交互与数据分析 | API请求 | JSON响应 |  
| FR-EXT-03 | 内容分发API | 标准化内容查询与检索接口（支持OAuth2认证） | API请求 | JSON/XML响应 |  
| FR-EXT-04 | 数据可视化 | 提供访问量、用户行为等数据的可视化图表（基于ECharts/D3.js） | 分析数据 | 图表渲染 |  
| FR-EXT-05 | 内容智能推荐 | 基于用户浏览历史和内容相似度的推荐算法（协同过滤） | 用户行为数据 | 推荐内容列表 |  
| FR-EXT-06 | 全文检索 | 自实现轻量级搜索引擎，支持中文分词与关键词高亮 | 搜索关键词 | 结果列表 |  
| FR-EXT-07 | 主题系统 | 支持多主题切换，主题继承与覆盖机制（如 `themes/default/` 与 `themes/custom/`） | 主题配置 | 前端渲染 |  
| FR-EXT-08 | 插件系统 | 提供标准化插件接口（钩子机制），支持功能扩展（如支付插件、统计插件） | 插件代码 | 功能集成 |  

---

### **3.2 非功能性需求**  
| 类别       | 需求描述                                                                 | 验收标准                          |  
|------------|--------------------------------------------------------------------------|-----------------------------------|  
| **性能**   | 支持500并发访问，静态页面响应时间<200ms                            | Apache Bench压测结果              |  
| **安全性** | 防止SQL注入、XSS攻击、CSRF攻击                                     | 通过OWASP ZAP扫描                 |  
| **兼容性** | 支持Chrome 90+、Firefox 85+、Safari 14+、微信公众号基础库2.0+      | 跨浏览器测试报告                  |  
| **可用性** | 系统可用性≥99.9%                                                         | 运维监控日志                      |  
| **可维护性** | 提供完整API文档和插件开发指南 | 第三方开发者可快速上手 |  
| **可扩展性** | 系统支持水平扩展（如负载均衡部署） | 通过Kubernetes部署验证 |  

---

## **4. 附录**  
### **4.1 术语表**  
| 术语 | 说明 |  
| SSG | 静态站点生成（Static Site Generation） |  
| TOTP | 基于时间的一次性密码（Time-based One-Time Password） |  
| CORS | 跨域资源共享（Cross-Origin Resource Sharing） |  
| RESTful | 表述性状态转移（Representational State Transfer） |
| MVC | 模型-视图-控制器（Model-View-Controller） |  
| XSS | 跨站脚本攻击（Cross-Site Scripting） |  
| API | 应用程序接口（Application Programming Interface） |  
| CSRF | 跨站请求伪造（Cross-Site Request Forgery） |  
| SEO | 搜索引擎优化（Search Engine Optimization） |  
| OAuth | 开放授权（Open Authorization） |  
| ECharts | 百度开源的数据可视化工具 |  

### **4.2 用例图**  
```mermaid
usecase
:系统管理员: --> (配置系统设置, 管理权限)
:内容管理人员: --> (编辑、发布、推送内容，用户行为数据分析)
:微信生态: --> (调用API接口)
:终端用户: --> (浏览内容, 互动)
```

### **4.3 版本变更记录**  
| 版本 | 日期       | 修改内容               |  
|------|------------|------------------------|  
| v1.0 | 2025-05-10 | 初始版本          |  

---

### **文档说明**  
1. **格式要求**：本说明书以Markdown格式编写，图表使用Mermaid语法。  
2. **更新机制**：需求变更需重新评审、更新版本号  

此文档可作为开发、测试及验收的基准依据，建议配合GitBook或Confluence进行在线协作管理。  