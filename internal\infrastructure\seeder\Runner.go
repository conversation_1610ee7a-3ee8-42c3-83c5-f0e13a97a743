/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-13
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-13
 * @FilePath: internal/infrastructure/seeder/Runner.go
 * @Description: Discovers and executes all registered database seeders.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package seeder

import (
	"fmt"
	"gacms/pkg/contract"
	"go.uber.org/fx"
)

// Runner manages and executes database seeders.
type Runner struct {
	seeders []contract.Seeder
}

// RunnerParams defines the dependencies for the Seeder Runner.
// It uses an Fx group to dynamically discover all registered seeders.
type RunnerParams struct {
	fx.In
	Seeders []contract.Seeder `group:"seeders"`
}

// NewRunner creates a new seeder runner.
func NewRunner(p RunnerParams) *Runner {
	return &Runner{
		seeders: p.Seeders,
	}
}

// RunAll executes all registered seeders in order.
func (r *Runner) RunAll() {
	if len(r.seeders) == 0 {
		fmt.Println("No database seeders registered.")
		return
	}

	fmt.Printf("Running %d database seeder(s)...\n", len(r.seeders))
	for _, seeder := range r.seeders {
		fmt.Printf(" -> Running seeder: %s\n", seeder.Name())
		if err := seeder.Run(); err != nil {
			fmt.Printf("  ! Error running seeder %s: %v\n", seeder.Name(), err)
			// Depending on the strategy, you might want to stop on error.
			// For now, we continue with other seeders.
		}
	}
} 