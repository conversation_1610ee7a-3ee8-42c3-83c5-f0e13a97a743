/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/theme/infrastructure/persistence/ThemeSettingGormRepository.go
 * @Description: GORM implementation of the ThemeSettingRepository.
 *
 * © 2025 GACMS. All rights reserved.
 */

package persistence

import (
	"errors"
	"gacms/internal/modules/theme/domain/contract"
	"gacms/internal/modules/theme/domain/model"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type themeSettingGormRepository struct {
	db *gorm.DB
}

// NewThemeSettingGormRepository creates a new GORM repository for theme settings.
func NewThemeSettingGormRepository(db *gorm.DB) contract.ThemeSettingRepository {
	return &themeSettingGormRepository{db: db}
}

// GetSettings retrieves the custom settings for a given site and theme.
func (r *themeSettingGormRepository) GetSettings(siteID uint, themeName string) (*model.ThemeSetting, error) {
	var setting model.ThemeSetting
	err := r.db.Where("site_id = ? AND theme_name = ?", siteID, themeName).First(&setting).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // Return nil, nil when no record is found, as this is not a system error.
		}
		return nil, err
	}
	return &setting, nil
}

// SaveSettings creates or updates the custom settings for a given site and theme.
func (r *themeSettingGormRepository) SaveSettings(setting *model.ThemeSetting) error {
	// Use Clauses(clause.OnConflict) to perform an "upsert" operation.
	// If a record with the same unique index (site_id, theme_name) exists, it updates the 'settings' column.
	// Otherwise, it creates a new record.
	return r.db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "site_id"}, {Name: "theme_name"}},
		DoUpdates: clause.AssignmentColumns([]string{"settings"}),
	}).Create(setting).Error
} 