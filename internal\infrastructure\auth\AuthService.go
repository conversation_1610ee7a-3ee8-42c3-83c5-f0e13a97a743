/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/infrastructure/auth/TokenProcessor.go
 * @Description: A processor for handling JWT generation and parsing. It's a core infrastructure component.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package auth

import (
	"fmt"
	"gacms/pkg/contract"
	"gacms/pkg/jwt"
)

// AuthService implements the contract.Auth interface using JWT.
type AuthService struct {
	jwtHelper *jwt.JWT
}

// NewAuthService creates a new instance of the AuthService.
// It satisfies the contract.Auth interface.
func NewAuthService(jwtHelper *jwt.JWT) contract.Auth {
	return &AuthService{jwtHelper: jwtHelper}
}

// Generate creates a new JWT for a given user ID and additional claims.
func (s *AuthService) Generate(claims map[string]interface{}) (string, error) {
	if _, ok := claims["user_id"]; !ok {
		return "", fmt.Errorf("user_id is a required claim for token generation")
	}

	token, err := s.jwtHelper.GenerateToken(claims)
	if err != nil {
		return "", fmt.Errorf("failed to generate token: %w", err)
	}

	return token, nil
}

// Parse validates a token string and returns the claims contained within.
func (s *AuthService) Parse(tokenString string) (map[string]interface{}, error) {
	claims, err := s.jwtHelper.ParseToken(tokenString)
	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if _, ok := claims["user_id"]; !ok {
		return nil, fmt.Errorf("token is missing user_id claim")
	}

	return claims, nil
} 