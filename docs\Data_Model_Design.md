<!--
Author: <PERSON><PERSON> Nieh
EMAIL: <EMAIL>
Copyright (c) 2025 Cion Nieh
-->
# 数据模型设计文档 (Data Model Design) - GACMS

## 目录

- [1. 文档信息](#1-文档信息)
  - [1.1 版本历史](#11-版本历史)
  - [1.2 文档目的](#12-文档目的)
  - [1.3 相关文档引用](#13-相关文档引用)
- [2. 数据模型概述](#2-数据模型概述)
  - [2.1 设计原则](#21-设计原则)
  - [2.2 数据模型图例说明](#22-数据模型图例说明)
  - [2.3 扩展模块数据模型规范](#23-扩展模块数据模型规范)
- [3. 概念数据模型 (CDM)](#3-概念数据模型-cdm)
  - [3.1 核心实体](#31-核心实体)
  - [3.2 实体关系图 (ERD)](#32-实体关系图-erd)
- [4. 逻辑数据模型 (LDM)](#4-逻辑数据模型-ldm)
  - [4.0 平台核心实体](#40-平台核心实体)
    - [4.0.1 站点表 (sites)](#401-站点表-sites)
  - [4.1 公共认证服务模块](#41-公共认证服务模块)
    - [4.1.1 密码重置表 (password_resets)](#411-密码重置表-password_resets)
    - [4.1.2 API访问令牌表 (personal_access_tokens)](#412-API访问令牌表-personal_access_tokens)
    - [4.1.3 (可选)会话表 (sessions)](#413-可选-会话表-sessions)
  - [4.2 管理员模块 (Admin Module)](#42-管理员模块-admin-module)
    - [4.2.1 管理员表 (admins)](#421-管理员表-admins)
    - [4.2.2 管理员角色表 (admin_roles)](#422-管理员角色表-admin_roles)
    - [4.2.3 管理员权限表 (admin_permissions)](#423-管理员权限表-admin_permissions)
    - [4.2.4 管理员角色关联表 (admin_role_pivot)](#424-管理员角色关联表-admin_role_pivot)
    - [4.2.5 角色权限关联表 (admin_permission_role_pivot)](#425-角色权限关联表-admin_permission_role_pivot)
  - [4.3 会员模块 (Member Module)](#43-会员模块-member-module)
    - [4.3.1 会员表 (members)](#431-会员表-members)
    - [4.3.2 会员第三方认证表 (member_oauth)](#432-会员第三方认证表-member_oauth)
    - [4.3.3 (可选) 会员角色表 (member_roles)](#433-可选-会员角色表-member_roles)
    - [4.3.4 (可选) 会员权限表 (member_permissions)](#434-可选-会员权限表-member_permissions)
    - [4.3.5 (可选) 会员角色关联表 (member_role_pivot)](#435-可选-会员角色关联表-member_role_pivot)
    - [4.3.6 (可选) 会员权限角色关联表 (member_permission_role_pivot)](#436-可选-会员权限角色关联表-member_permission_role_pivot)
    - [4.3.7 (备选) 会员用户组表 (member_user_groups)](#437-备选-会员用户组表-member_user_groups)
    - [4.3.8 (备选) 会员用户与用户组关联表 (member_user_group_pivot)](#438-备选-会员用户与用户组关联表-member_user_group_pivot)
  - [4.4 内容管理模块](#42-内容管理模块)
    - [4.4.1 栏目表 (categories)](#421-栏目表-categories)
    - [4.4.2 内容表 (posts)](#422-内容表-posts)
    - [4.4.3 标签表 (tags)](#423-标签表-tags)
    - [4.4.4 内容标签关联表 (post_tag)](#424-内容标签关联表-post_tag)
    - [4.4.5 评论表 (comments)](#425-评论表-comments)
    - [4.4.6 专题表 (collections)](#426-专题表-collections)
    - [4.4.7 内容专题关联表 (collection_post)](#427-内容专题关联表-collection_post)
    - [4.4.8 自定义字段表 (custom_fields)](#428-自定义字段表-custom_fields)
    - [4.4.9 内容自定义字段值表 (post_custom_field_values)](#429-内容自定义字段值表-post_custom_field_values)
  - [4.5 多站点管理模块](#43-多站点管理模块)
    - [4.5.1 站点表 (sites)](#431-站点表-sites)
  - [4.6 系统配置模块](#44-系统配置模块)
    - [4.6.1 配置项表 (settings)](#441-配置项表-settings)
  - [4.7 插件模块](#45-插件模块)
    - [4.7.1 插件表 (plugins)](#451-插件表-plugins)
  - [4.8 主题模块](#46-主题模块)
    - [4.8.1 主题表 (themes)](#461-主题表-themes)
  - [4.9 版本与授权模块](#47-版本与授权模块)
    - [4.9.1 许可证表 (licenses)](#471-许可证表-licenses)
    - [4.9.2 功能开关表 (feature_toggles)](#472-功能开关表-feature_toggles)
- [5. 物理数据模型 (PDM)](#5-物理数据模型-pdm)
  - [5.1 数据库选型](#51-数据库选型)
  - [5.2 表结构详细设计](#52-表结构详细设计)
  - [5.3 索引设计](#53-索引设计)
  - [5.4 约束设计](#54-约束设计)
- [6. 数据字典](#6-数据字典)
- [7. 数据流设计](#7-数据流设计)
  - [7.1 核心业务数据流](#71-核心业务数据流)
- [8. 数据安全与完整性](#8-数据安全与完整性)
  - [8.1 数据备份与恢复](#81-数据备份与恢复)
  - [8.2 数据校验](#82-数据校验)
- [9. 数据迁移与同步 (可选)](#9-数据迁移与同步-可选)

---

## 1. 文档信息

### 1.1 版本历史

| 版本号 | 日期       | 作者     | 变更说明         |
| ------ | ---------- | -------- | ---------------- |
| 0.1.0  | 2025-05-15 | Cion Nieh | 初稿创建，定义基本结构 |
| 0.2.0  | 2025-05-15 | Cion Nieh | 补充核心实体描述、ER图占位符、部分逻辑模型表结构占位符 |
| 0.3.0  | 2025-05-16 | Trae AI   | 填充内容管理模块下的栏目表和内容表结构 |
| 0.4.0  | 2025-05-16 | Trae AI   | 填充多站点、系统配置、插件和主题模块的表结构 |
| 0.5.0  | 2025-05-16 | Trae AI   | 补充物理数据模型(PDM)的数据库选型内容 |
| 0.6.0  | 2025-05-16 | Trae AI   | 填充PDM中表结构、索引和约束设计的初步内容 |
| 0.7.0  | 2025-05-16 | Trae AI   | 填充数据字典、数据流、数据安全与完整性、数据迁移等章节的初步内容 |
| 0.8.0  | 2025-05-30 | Cion Nieh | 完善多站点管理、认证管理、前后台用户管理等章节的内容 |
| 1.0.0  | 2025-06-10 | Trae AI   | **重大更新**：根据ADR-001 v4架构，明确后台管理员(admins)和前台会员(members)的物理隔离，统一术语，并增加扩展模块的数据模型设计规范。 |
| 1.1.0  | 2025-06-11 | Trae AI   | **架构对齐**：根据ADR-001 v5（模块化核心）架构，重申模块化数据隔离原则，并为扩展模块提供更详细的数据模型规范。 |
| 2.0.0  | {current_date} | Trae AI   | **架构升级 (ADR-004)**: 引入原生多租户设计。将`site_id`作为通用外键注入大多数业务表，废弃"多站点模块"，并将`sites`表提升为平台核心实体。 |

### 1.2 文档目的

本文档旨在详细描述亘安网站内容管理系统 (GACMS) 的数据模型设计，包括概念数据模型、逻辑数据模型和物理数据模型。它是数据库设计和开发的重要依据，确保数据结构的合理性、一致性和可扩展性。

### 1.3 相关文档引用

- [需求规格说明书 (RSD) - GACMS](RSD.md)
- [系统架构设计文档 (SADD) - GACMS](SADD.md)
- [接口设计文档 (Interface Design) - GACMS](Interface_Design.md)

---

## 2. 数据模型概述

### 2.1 设计原则

- **满足需求**: 数据模型能够完整、准确地表达业务需求。
- **规范性**: 遵循数据库设计范式，减少数据冗余，保证数据一致性。
- **原生多租户支持 (Native Multi-tenancy Support)**: 这是`ADR-004`定义的核心原则。除平台级全局实体外（如`admins`），所有业务相关的实体都必须与`sites`实体关联，通过`site_id`实现数据的物理级隔离。数据访问层必须默认启用`site_id`作为查询条件。
- **核心实体物理隔离**: 关键的核心实体，如后台管理员(Admins)和前台会员(Members)，在数据表层面必须完全独立，以实现最高级别的安全和业务隔离。
- **可扩展性**: 易于扩展以适应未来的业务变化，并为第三方扩展模块提供清晰的数据设计指导。
- **性能**: 考虑查询性能，合理设计索引。
- **可理解性**: 数据模型清晰易懂，方便开发和维护人员理解。
- **模块化数据隔离 (Modular Data Isolation)**: 作为核心原则，每个模块负责其自身数据表的创建、迁移和管理。模块间严禁直接跨库表访问，所有数据交换必须通过各模块定义的API或核心事件进行。

### 2.2 数据模型图例说明

*(如果使用特定图例，在此处说明)*

### 2.3 扩展模块数据模型规范
为保证平台的稳定性、安全性和数据一致性，所有第三方扩展模块在设计其数据模型时，必须遵循以下规范：
1.  **独立命名空间**: 模块创建的所有数据表，其表名必须以`[module_slug]_`为前语，其中`module_slug`是模块的唯一英文标识。例如，一个名为`Forum`的论坛模块，其帖子表应命名为`forum_posts`。
    *   *豁免权*：GACMS官方核心模块（如用户、内容）的表名可省略前缀，以保持简洁性。
2.  **自治迁移**: 模块必须包含自身的数据库迁移（Migration）文件。模块的安装过程应自动执行其迁移，创建所需的数据表和字段。同样，卸载过程也应能安全地移除这些数据表。
3.  **禁止外部依赖**: 模块的迁移和模型代码不应包含对其他模块（包括核心模块）的数据库表的直接引用（如外键约束）。如果需要关联，应在应用层通过服务调用来处理，数据层面只存储关联ID。
4.  **共享核心数据**: 模块可以读取（但绝不能写入）平台的核心公共数据表，如`sites`。这种访问应通过平台提供的`CoreServices`接口进行，而不是直接的数据库查询。

---

## 3. 概念数据模型 (CDM)

*(描述系统核心业务实体及其关系，不涉及具体实现细节)*

### 3.1 核心实体

GACMS的核心业务实体主要包括：

- **用户 (User):** 系统操作者，拥有不同的角色和权限。可以是管理员、编辑、普通注册用户等。
- **角色 (Role):** 定义一组权限的集合，用于分配给用户。
- **权限 (Permission):** 定义用户可以执行的特定操作，如创建内容、发布内容、管理用户等。
- **站点 (Site):** 在多站点场景下，代表一个独立的网站实例，拥有独立的配置、内容和用户体系（可选）。
- **栏目 (Category):** 用于组织和分类内容的层级结构。
- **内容 (Post):** 系统管理的核心信息单元，可以是文章、产品、新闻等任何形式的内容。
- **标签 (Tag):** 用于对内容进行多维度标记，方便检索和关联。
- **评论 (Comment):** 用户对内容发表的反馈和讨论。
- **专题 (Collection/Series):** 将相关内容组织成一个系列或专题，方便用户阅读。
- **自定义字段 (CustomField):** 允许管理员为特定内容类型扩展额外的属性字段。
- **媒体 (Media):** 管理上传的图片、视频、附件等文件资源。
- **主题 (Theme):** 控制网站前台的视觉表现和布局。 (由平台ThemeManager管理)
- **插件 (Plugin):** 扩展系统功能的独立模块。 (由平台PluginManager管理)
- **设置 (Setting):** 存储系统级的配置信息。

### 3.2 实体关系图 (ERD)

```mermaid
erDiagram
    admins {
        int id PK
        string username "用户名 (唯一)"
        string email "邮箱 (唯一)"
        string password_hash "密码哈希"
        string avatar_url "头像URL"
        string status "状态 (active, inactive, banned)"
        timestamp last_login_at "最后登录时间"
        string last_login_ip "最后登录IP"
        timestamp created_at
        timestamp updated_at
    }

    members {
        int id PK
        int site_id FK "所属站点ID"
        string username "用户名 (在同一站点内唯一, 可选)"
        string email "邮箱 (在同一站点内唯一)"
        string phone "手机号 (在同一站点内唯一, 可选)"
        string password_hash "密码哈希"
        string nickname "昵称"
        string avatar_url "头像URL"
        string status "状态 (active, inactive, banned)"
        timestamp email_verified_at "邮箱验证时间"
        timestamp phone_verified_at "手机号验证时间"
        timestamp last_login_at "最后登录时间"
        string last_login_ip "最后登录IP"
        timestamp created_at
        timestamp updated_at
    }
    
    admin_roles {
        int id PK
        string name "角色名称 (唯一)"
        string slug "角色标识 (唯一, e.g., super-admin, editor)"
        string description "描述"
        timestamp created_at
        timestamp updated_at
    }

    admin_permissions {
        int id PK
        string name "权限名称 (e.g., create_post)"
        string slug "权限标识 (唯一, e.g., post.create)"
        string description "描述"
        string group_name "权限分组 (e.g., Content, User)"
        timestamp created_at
        timestamp updated_at
    }

    admin_user_role {
        int admin_id PK, FK
        int role_id PK, FK
    }

    admin_role_permission {
        int role_id PK, FK
        int permission_id PK, FK
    }

    admins ||--o{ admin_user_role : "has"
    admin_roles ||--o{ admin_user_role : "belongs_to"
    admin_roles ||--o{ admin_role_permission : "has"
    admin_permissions ||--o{ admin_role_permission : "belongs_to"
    
    sites {
        int id PK
        string name "站点名称 (唯一)"
        string domain "站点域名 (唯一)"
        string base_path "站点子目录 (可选, 如 /blog)"
        string default_language_code "默认语言代码 (e.g., en, zh_CN)"
        boolean is_default "是否默认站点"
        boolean is_active "是否激活"
        text settings "站点特定配置 (JSON)"
        timestamp created_at
        timestamp updated_at
    }
    
    sites ||--o{ members : "has"

    -- 所有与站点业务相关的表（如posts, categories, tags等）都应与sites表建立多对一关系 --
    sites ||--o{ posts : "has"
    sites ||--o{ categories : "has"
    sites ||--o{ tags : "has"
```

---

## 4. 逻辑数据模型 (LDM)

*(基于CDM，进一步细化实体属性和关系，接近数据库表结构)*

### 4.0 平台核心实体

#### 4.0.1 站点表 (sites)
`sites` 表是平台的核心，用于定义和隔离不同的租户。

| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `int` | `PK`, `AI` | 站点ID |
| `name` | `varchar(255)` | `Unique` | 站点名称 |
| `domain` | `varchar(255)` | `Unique` | 站点主域名 |
| `base_path` | `varchar(255)` | `Nullable` | 站点子目录 (用于基于路径的路由) |
| `default_language_code` | `varchar(10)` | `Default: 'en'` | 默认语言代码 |
| `is_default` | `boolean` | `Default: false` | 是否为默认站点 |
| `is_active` | `boolean` | `Default: true` | 是否激活 |
| `settings` | `json` | `Nullable` | 站点的特定配置覆写 |
| `created_at` | `timestamp` | | 创建时间 |
| `updated_at` | `timestamp` | | 更新时间 |

### 4.1 公共认证服务模块

**设计原则：** 本模块提供底层的、统一的认证服务能力，供后台和前台用户模块调用。它不直接管理用户身份信息表，而是处理认证过程中的通用逻辑，如密码处理、令牌管理、会话控制、OAuth流程协调、双因素认证支持等。

#### 4.1.1 密码重置表 (password_resets)

*说明：存储用户请求密码重置时生成的令牌和有效期，适用于后台和前台用户。*

| 字段名     | 数据类型    | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                     |
| ---------- | ----------- | ---- | -------- | --------- | ------ | -------------------------------------------- |
| email      | VARCHAR     | 100  | NOT NULL | INDEX     |        | 用户邮箱                                     |
| user_type  | VARCHAR     | 10   | NOT NULL | INDEX     |        | 用户类型 ('backend', 'frontend')             |
| token      | VARCHAR     | 255  | NOT NULL | UNIQUE    |        | 重置令牌 (哈希存储)                          |
| created_at | TIMESTAMP   |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间 (也作为令牌有效期判断依据)        |

#### 4.1.2 API访问令牌表 (personal_access_tokens)

*说明：用于发放和管理API访问令牌，允许第三方应用或脚本安全地访问系统接口。*

| 字段名        | 数据类型        | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                     |
| ------------- | --------------- | ---- | -------- | --------- | ------ | -------------------------------------------- |
| id            | BIGINT UNSIGNED |      | NOT NULL | PK        |        | 令牌ID                                       |
| tokenable_type| VARCHAR         | 255  | NOT NULL |           |        | 可拥有令牌的模型类型 (如: App\Models\BackendUser) |
| tokenable_id  | BIGINT UNSIGNED |      | NOT NULL |           |        | 可拥有令牌的模型ID                           |
| name          | VARCHAR         | 255  | NOT NULL |           |        | 令牌名称 (用户自定义)                        |
| token         | VARCHAR         | 64   | NOT NULL | UNIQUE    |        | 令牌值 (哈希存储前32位，后32位明文给用户)    |
| abilities     | TEXT            |      | NULL     |           |        | 令牌权限范围 (JSON, e.g., ["post:create", "post:read"]) |
| last_used_at  | TIMESTAMP       |      | NULL     |           |        | 最后使用时间                                 |
| expires_at    | TIMESTAMP       |      | NULL     |           |        | 过期时间 (NULL表示永不过期)                  |
| created_at    | TIMESTAMP       |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                   |
| updated_at    | TIMESTAMP       |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                   |

*索引建议: `INDEX(tokenable_type, tokenable_id)`*

#### 4.1.3 (可选) 会话表 (sessions)

*说明：如果选择数据库驱动的会话管理，则需要此表。*

| 字段名        | 数据类型    | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                     |
| ------------- | ----------- | ---- | -------- | --------- | ------ | -------------------------------------------- |
| id            | VARCHAR     | 255  | NOT NULL | PK        |        | Session ID                                   |
| user_id       | BIGINT UNSIGNED |    | NULL     | INDEX     |        | 关联的用户ID (后台或前台，需配合user_type)   |
| user_type     | VARCHAR     | 10   | NULL     |           |        | 用户类型 ('backend', 'frontend')             |
| ip_address    | VARCHAR     | 45   | NULL     |           |        | IP地址                                       |
| user_agent    | TEXT        |      | NULL     |           |        | User Agent                                   |
| payload       | LONGTEXT    |      | NOT NULL |           |        | Session数据 (序列化后)                       |
| last_activity | INTEGER     |      | NOT NULL | INDEX     |        | 最后活动时间戳                               |

### 4.2 管理员模块 (Admin Module)

**设计原则：** 负责管理后台系统的所有用户（管理员、编辑等）、角色定义（全局角色、站点特定角色）以及基于角色的细粒度权限控制 (RBAC)。

#### 4.2.1 管理员表 (admins)

*说明：存储后台管理系统的用户信息。后台用户是全局的，可以被授权管理一个或多个站点。*

| 字段名        | 数据类型         | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                     |
| ------------- | ---------------- | ---- | -------- | --------- | ------ | ---------------------------- | 
| id            | INT UNSIGNED     |      | NOT NULL | PK        |        | 用户ID，自增                 |
| username      | VARCHAR          | 50   | NOT NULL | UNIQUE    |        | 用户名，唯一                 |
| nickname      | VARCHAR          | 50   | NULL     |           |        | 昵称                        |
| email         | VARCHAR          | 100  | NOT NULL | UNIQUE    |        | 邮箱，唯一                   |
| password_hash | VARCHAR          | 255  | NOT NULL |           |        | 哈希后的密码                 |
| avatar_url    | VARCHAR          | 255  | NULL     |           |        | 用户头像URL                  |
| bio           | TEXT             |      | NULL     |           |        | 个人简介                     |
| status        | TINYINT UNSIGNED |      | NOT NULL |           | 1      | 用户状态 (0:禁用, 1:启用, 2:未激活) |
| is_super_admin| BOOLEAN          |      | NOT NULL |           | FALSE  | 是否超级管理员               |
| last_login_at | TIMESTAMP        |      | NULL     |           |        | 最后登录时间                 |
| last_login_ip | VARCHAR          | 45   | NULL     |           |        | 最后登录IP地址               |
| two_factor_secret | TEXT          |      | NULL     |           |        | 两因素认证密钥 (加密存储)    |
| two_factor_recovery_codes | TEXT    |      | NULL     |           |        | 两因素认证恢复代码 (加密存储)| 
| created_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间 |
| updated_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |
| deleted_at    | TIMESTAMP        |      | NULL     |           |        | 软删除标记                   |

#### 4.2.2 管理员角色表 (admin_roles)

*说明：定义后台系统的角色，如超级管理员、站点管理员、内容编辑等。*

| 字段名      | 数据类型    | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                     |
| ----------- | ----------- | ---- | -------- | --------- | ------ | -------------------------------------------- |
| id          | INT UNSIGNED|      | NOT NULL | PK        |        | 角色ID，自增                                 |
| name        | VARCHAR     | 50   | NOT NULL |           |        | 角色名称                                     |
| slug        | VARCHAR     | 50   | NOT NULL |           |        | 角色标识 (英文, 用于程序识别)                |
| description | VARCHAR     | 255  | NULL     |           |        | 角色描述                                     |
| scope       | VARCHAR     | 10   | NOT NULL |           | 'global' | 角色范围 ('global', 'site')                  |
| site_id     | INT UNSIGNED|      | NULL     | FK (sites.id) |   | 关联站点ID (当scope为'site'时必填)          |
| created_at  | TIMESTAMP   |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间 |
| updated_at  | TIMESTAMP   |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

*索引建议: `UNIQUE(slug, site_id)` (对于全局角色, `site_id` 为 NULL时，需特殊处理唯一性或slug全局唯一)*

#### 4.2.3 管理员权限表 (admin_permissions)

*说明: 定义后台系统中所有可分配的原子权限。*

| 字段名      | 数据类型    | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                     |
| ----------- | ----------- | ---- | -------- | --------- | ------ | -------------------------------------------- |
| id          | INT UNSIGNED|      | NOT NULL | PK        |        | 权限ID，自增                                 |
| name        | VARCHAR     | 100  | NOT NULL |           |        | 权限名称 (如: 管理后台用户)                  |
| slug        | VARCHAR     | 100  | NOT NULL | UNIQUE    |        | 权限标识 (格式: backend.resource.action)     |
| description | VARCHAR     | 255  | NULL     |           |        | 权限描述                                     |
| group_name  | VARCHAR     | 50   | NULL     |           |        | 权限分组 (方便管理, 如: Backend-UserManagement) |
| created_at  | TIMESTAMP   |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间 |
| updated_at  | TIMESTAMP   |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

#### 4.2.4 管理员角色关联表 (admin_role_pivot)

*说明: 多对多关系，一个后台用户可以拥有多个后台角色。*

| 字段名          | 数据类型     | 是否为空 | 主键/外键 | 默认值 | 备注说明                                     |
| --------------- | ------------ | -------- | --------- | ------ | -------------------------------------------- |
| admin_id        | INT UNSIGNED | NOT NULL | PK, FK (admins.id) |        | 后台用户ID                                   |
| role_id         | INT UNSIGNED | NOT NULL | PK, FK (admin_roles.id) |        | 后台角色ID                                   |
| site_id         | INT UNSIGNED | NULL     | PK, FK (sites.id) |        | 当授予站点角色时，此为站点ID；授予全局角色时为NULL | 
| created_at      | TIMESTAMP    | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                                     |

*联合主键: (`admin_id`, `role_id`, `site_id`)*

#### 4.2.5 角色权限关联表 (admin_permission_role_pivot)

*说明: 多对多关系，一个后台角色可以拥有多个后台权限。*

| 字段名        | 数据类型    | 是否为空 | 主键/外键 | 默认值 | 备注说明                                     |
| ------------- | ----------- | -------- | --------- | ------ | -------------------------------------------- |
| permission_id | INT UNSIGNED| NOT NULL | PK, FK (admin_permissions.id) |        | 后台权限ID                                   |
| role_id       | INT UNSIGNED| NOT NULL | PK, FK (admin_roles.id) |        | 后台角色ID                                   |
| created_at    | TIMESTAMP   | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                                     |

*联合主键: (`permission_id`, `role_id`)*

### 4.3 会员模块 (Member Module)

**设计原则：** 负责管理前台网站的用户（访问者、注册会员）、可选的角色与权限体系、以及第三方社交平台登录集成。前台用户通常与特定站点关联。

#### 4.3.1 会员表 (members)

*说明：存储前台网站的用户信息。用户主要注册或关联于特定站点。*

| 字段名             | 数据类型         | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                     |
| ------------------ | ---------------- | ---- | -------- | --------- | ------ | -------------------------------------------- |
| id                 | BIGINT UNSIGNED  |      | NOT NULL | PK        |        | 用户ID，自增                                 |
| site_id            | INT UNSIGNED     |      | NOT NULL | FK (sites.id) |        | 用户主要注册或关联的站点ID                   |
| uuid               | VARCHAR          | 36   | NOT NULL | UNIQUE    |        | UUID，全局唯一标识符，可用于SSO等场景        |
| username           | VARCHAR          | 50   | NULL     |           |        | 用户名 (在同一site_id下可选唯一)             |
| nickname           | VARCHAR          | 50   | NULL     |           |        | 昵称                                         |
| email              | VARCHAR          | 100  | NULL     |           |        | 邮箱 (在同一site_id下可选唯一)               |
| phone_number       | VARCHAR          | 20   | NULL     |           |        | 手机号码 (在同一site_id下可选唯一)           |
| password_hash      | VARCHAR          | 255  | NULL     |           |        | 哈希后的密码 (如果支持账号密码登录)            |
| avatar_url         | VARCHAR          | 255  | NULL     |           |        | 用户头像URL                                  |
| gender             | TINYINT UNSIGNED |      | NULL     |           | 0      | 性别 (0:未知, 1:男, 2:女)                    |
| birthdate          | DATE             |      | NULL     |           |        | 出生日期                                     |
| bio                | TEXT             |      | NULL     |           |        | 个人简介                                     |
| status             | TINYINT UNSIGNED |      | NOT NULL |           | 1      | 用户状态 (0:禁用, 1:启用, 2:未激活/待验证)   |
| registration_source| VARCHAR          | 50   | NULL     |           | 'website' | 注册来源 (website, app, wechat, qq, etc.)    |
| last_login_at      | TIMESTAMP        |      | NULL     |           |        | 最后登录时间                                 |
| last_login_ip      | VARCHAR          | 45   | NULL     |           |        | 最后登录IP地址                               |
| email_verified_at  | TIMESTAMP        |      | NULL     |           |        | 邮箱验证时间                                 |
| phone_verified_at  | TIMESTAMP        |      | NULL     |           |        | 手机验证时间                                 |
| created_at         | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                   |
| updated_at         | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                   |
| deleted_at         | TIMESTAMP        |      | NULL     |           |        | 软删除标记                                   |

*索引建议: `UNIQUE(site_id, username)` (如果username启用), `UNIQUE(site_id, email)` (如果email启用), `UNIQUE(site_id, phone_number)` (如果phone_number启用)*

#### 4.3.2 会员第三方认证表 (member_oauth)

*说明: 管理前台用户通过第三方社交平台（如微信、QQ、GitHub等）登录的信息。*

| 字段名        | 数据类型         | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                   |
| ------------- | ---------------- | ---- | -------- | --------- | ------ | ------------------------------------------ |
| id            | BIGINT UNSIGNED  |      | NOT NULL | PK        |        | 自增ID                                     |
| member_id      | BIGINT UNSIGNED  |      | NOT NULL | FK (members.id) | | 关联的前台用户ID                         |
| provider      | VARCHAR          | 50   | NOT NULL |           |        | 第三方平台名称 (e.g., 'wechat', 'qq', 'github') |
| provider_user_id | VARCHAR          | 255  | NOT NULL |           |        | 第三方平台用户唯一标识符                   |
| access_token  | TEXT             |      | NULL     |           |        | Access Token (加密存储)                      |
| refresh_token | TEXT             |      | NULL     |           |        | Refresh Token (加密存储)                     |
| expires_at    | TIMESTAMP        |      | NULL     |           |        | Token过期时间                              |
| nickname      | VARCHAR          | 100  | NULL     |           |        | 第三方平台昵称                             |
| avatar_url    | VARCHAR          | 255  | NULL     |           |        | 第三方平台头像 (统一字段名)                |
| raw_data      | JSON             |      | NULL     |           |        | 第三方平台返回的原始用户信息 (JSON)        |
| created_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                 |
| updated_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                 |

*索引建议: `UNIQUE(member_id, provider)`, `UNIQUE(provider, provider_user_id)`*

#### 4.3.3 (可选) 会员角色表 (member_roles)

*说明：定义前台用户的角色，如普通会员、VIP会员等。前台角色总是与特定站点关联。*

| 字段名      | 数据类型    | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                     |
| ----------- | ----------- | ---- | -------- | --------- | ------ | -------------------------------------------- |
| id          | INT UNSIGNED|      | NOT NULL | PK        |        | 角色ID，自增                                 |
| site_id     | INT UNSIGNED|      | NOT NULL | FK (sites.id) |   | 关联站点ID                                   |
| name        | VARCHAR     | 50   | NOT NULL |           |        | 角色名称                                     |
| slug        | VARCHAR     | 50   | NOT NULL |           |        | 角色标识 (英文, 程序识别, 同site_id下唯一)   |
| description | VARCHAR     | 255  | NULL     |           |        | 角色描述                                     |
| is_default  | BOOLEAN     |      | NOT NULL |           | FALSE  | 是否为站点默认角色 (新用户自动分配)          |
| created_at  | TIMESTAMP   |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间 |
| updated_at  | TIMESTAMP   |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

*索引建议: `UNIQUE(site_id, slug)`*

#### 4.3.4 (可选) 会员权限表 (member_permissions)

*说明: 定义前台用户可拥有的原子权限，如发表评论、点赞等。*

| 字段名      | 数据类型    | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                     |
| ----------- | ----------- | ---- | -------- | --------- | ------ | -------------------------------------------- |
| id          | INT UNSIGNED|      | NOT NULL | PK        |        | 权限ID，自增                                 |
| name        | VARCHAR     | 100  | NOT NULL |           |        | 权限名称 (如: 发表评论)                      |
| slug        | VARCHAR     | 100  | NOT NULL | UNIQUE    |        | 权限标识 (格式: frontend.resource.action)    |
| description | VARCHAR     | 255  | NULL     |           |        | 权限描述                                     |
| group_name  | VARCHAR     | 50   | NULL     |           |        | 权限分组 (方便管理, 如: Frontend-Interaction) |
| created_at  | TIMESTAMP   |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间 |
| updated_at  | TIMESTAMP   |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

#### 4.3.5 (可选) 会员角色关联表 (member_role_pivot)

*说明: 多对多关系，一个前台用户可以在其关联的站点拥有多个前台角色。*

| 字段名           | 数据类型     | 是否为空 | 主键/外键 | 默认值 | 备注说明                                     |
| ---------------- | ------------ | -------- | --------- | ------ | -------------------------------------------- |
| member_id        | BIGINT UNSIGNED| NOT NULL | PK, FK (members.id) |        | 前台用户ID                                   |
| member_role_id   | INT UNSIGNED | NOT NULL | PK, FK (member_roles.id) |        | 前台角色ID                                   |
| created_at       | TIMESTAMP    | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                                     |

*联合主键: (`member_id`, `member_role_id`)*
*约束: 需确保 `members.site_id` 与 `member_roles.site_id` 一致。*

#### 4.3.6 (可选) 会员权限角色关联表 (member_permission_role_pivot)

*说明: 多对多关系，一个前台角色可以拥有多个前台权限。*

| 字段名        | 数据类型    | 是否为空 | 主键/外键 | 默认值 | 备注说明                                     |
| ------------- | ----------- | -------- | --------- | ------ | -------------------------------------------- |
| member_permission_id | INT UNSIGNED| NOT NULL | PK, FK (member_permissions.id) |        | 前台权限ID                                   |
| member_role_id       | INT UNSIGNED| NOT NULL | PK, FK (member_roles.id) |        | 前台角色ID                                   |
| created_at    | TIMESTAMP   | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                                     |

*联合主键: (`member_permission_id`, `member_role_id`)*

#### 4.3.7 (备选) 会员用户组表 (member_user_groups)

*说明: 如果不需要完整的前台RBAC权限体系，可以采用此轻量级用户组方案。此表定义特定于站点的用户组。*

| 字段名      | 数据类型    | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明             |
| ----------- | ----------- | ---- | -------- | --------- | ------ | -------------------- |
| id          | INT UNSIGNED|      | NOT NULL | PK        |        | 用户组ID，自增       |
| site_id     | INT UNSIGNED|      | NOT NULL | FK (sites.id) |        | 站点ID               |
| name        | VARCHAR     | 50   | NOT NULL |           |        | 用户组名称           |
| description | VARCHAR     | 255  | NULL     |           |        | 用户组描述           |
| created_at  | TIMESTAMP   |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间 |
| updated_at  | TIMESTAMP   |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

*索引建议: `UNIQUE(site_id, name)`*

#### 4.3.8 (备选) 会员用户与用户组关联表 (member_user_group_pivot)

*说明: 连接前台用户和其所属的用户组（如果采用轻量级用户组方案）。*

| 字段名    | 数据类型        | 是否为空 | 主键/外键 | 备注说明         |
| --------- | --------------- | -------- | --------- | ---------------- | 
| member_id   | BIGINT UNSIGNED | NOT NULL | PK, FK (members.id) | 前台用户ID       |
| group_id  | INT UNSIGNED    | NOT NULL | PK, FK (member_user_groups.id) | 用户组ID         |
| created_at | TIMESTAMP      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间         |

*联合主键: (`member_id`, `group_id`)*

**关于前台权限方案的选择：**

- **完整RBAC (4.3.3 - 4.3.6):** 提供最细粒度的权限控制，适用于复杂的前台用户交互和权限分级场景。实现成本相对较高。
- **轻量级用户组 (4.3.7 - 4.3.8):** 适用于简单的用户分类和内容访问控制，实现简单快捷。

**建议：** 根据项目初期需求复杂度选择。如果需求不明朗或初期简单，可以先不实现前台角色权限，或采用轻量级用户组。后续可根据业务发展再引入完整的RBAC。本文档优先描述完整RBAC方案作为标准设计。


**设计原则：** 本模块负责管理系统的所有用户（包括后台管理员 `backend_users`、前台注册用户 `frontend_users`）、角色定义（区分全局与站点、后台与前台）以及细粒度的权限控制。采用基于角色的访问控制 (RBAC) 模型，并适配多站点架构。

### 4.4 内容管理模块

#### 4.4.1 栏目表 (categories)

| 字段名          | 数据类型         | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                     |
| --------------- | ---------------- | ---- | -------- | --------- | ------ | -------------------------------------------- |
| id              | INT UNSIGNED     |      | NOT NULL | PK        |        | 栏目ID，自增                                 |
| site_id         | INT UNSIGNED     |      | NOT NULL | FK (sites.id) |        | 站点ID (多站点时关联)                        |
| parent_id       | INT UNSIGNED     |      | NULL     | FK (categories.id) | NULL   | 父栏目ID，用于实现层级结构                   |
| name            | VARCHAR          | 100  | NOT NULL |           |        | 栏目名称                                     |
| slug            | VARCHAR          | 100  | NOT NULL | UNIQUE    |        | 栏目别名/Slug，用于URL，同站点下唯一         |
| description     | TEXT             |      | NULL     |           |        | 栏目描述                                     |
| cover_image     | VARCHAR          | 255  | NULL     |           |        | 栏目封面图URL                                |
| template_list   | VARCHAR          | 100  | NULL     |           |        | 列表页模板文件路径                           |
| template_detail | VARCHAR          | 100  | NULL     |           |        | 内容页模板文件路径                           |
| sort_order      | INT UNSIGNED     |      | NOT NULL |           | 0      | 显示顺序，数字越小越靠前                       |
| status          | TINYINT UNSIGNED |      | NOT NULL |           | 1      | 状态 (0:禁用, 1:启用)                        |
| meta_title      | VARCHAR          | 255  | NULL     |           |        | SEO Meta标题                                 |
| meta_keywords   | VARCHAR          | 255  | NULL     |           |        | SEO Meta关键词                               |
| meta_description| TEXT             |      | NULL     |           |        | SEO Meta描述                                 |
| created_at      | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                   |
| updated_at      | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                   |

#### 4.4.2 内容表 (posts)

| 字段名             | 数据类型         | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                                     |
| ------------------ | ---------------- | ---- | -------- | --------- | ------ | ------------------------------------------------------------ |
| id                 | BIGINT UNSIGNED  |      | NOT NULL | PK        |        | 内容ID，自增                                                 |
| site_id            | INT UNSIGNED     |      | NOT NULL | FK (sites.id) |        | 站点ID (多站点时关联)                                        |
| category_id        | INT UNSIGNED     |      | NOT NULL | FK (categories.id) |    | 所属栏目ID                                                   |
| user_id            | INT UNSIGNED     |      | NOT NULL | FK (backend_users.id) |        | 作者用户ID (后台用户)                                        |
| type               | VARCHAR          | 50   | NOT NULL |           | 'post' | 内容类型 (如: post, page, product)                           |
| title              | VARCHAR          | 255  | NOT NULL |           |        | 内容标题                                                     |
| slug               | VARCHAR          | 255  | NOT NULL | UNIQUE    |        | 内容别名/Slug，用于URL，同类型同站点下唯一                   |
| summary            | TEXT             |      | NULL     |           |        | 内容摘要/简介                                                |
| content            | LONGTEXT         |      | NULL     |           |        | 内容主体 (HTML或Markdown)                                    |
| cover_image        | VARCHAR          | 255  | NULL     |           |        | 内容封面图URL                                                |
| status             | VARCHAR          | 20   | NOT NULL |           | 'draft'| 内容状态 (draft:草稿, pending:待审核, published:已发布, private:私密, trashed:回收站) |
| visibility         | VARCHAR          | 20   | NOT NULL |           | 'public'| 可见性 (public:公开, private:私密, password:密码保护)        |
| password           | VARCHAR          | 255  | NULL     |           |        | 内容访问密码 (当visibility为password时)                      |
| comment_status     | TINYINT UNSIGNED |      | NOT NULL |           | 1      | 评论状态 (0:关闭, 1:开放)                                    |
| comment_count      | INT UNSIGNED     |      | NOT NULL |           | 0      | 评论数量                                                     |
| view_count         | INT UNSIGNED     |      | NOT NULL |           | 0      | 查看次数                                                     |
| like_count         | INT UNSIGNED     |      | NOT NULL |           | 0      | 点赞次数                                                     |
| published_at       | TIMESTAMP        |      | NULL     |           |        | 发布时间                                                     |
| pinned_at          | TIMESTAMP        |      | NULL     |           |        | 置顶时间                                                     |
| meta_title         | VARCHAR          | 255  | NULL     |           |        | SEO Meta标题                                                 |
| meta_keywords      | VARCHAR          | 255  | NULL     |           |        | SEO Meta关键词                                               |
| meta_description   | TEXT             |      | NULL     |           |        | SEO Meta描述                                                 |
| custom_template    | VARCHAR          | 100  | NULL     |           |        | 自定义内容模板文件路径                                       |
| created_at         | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                                   |
| updated_at         | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                                   |
| deleted_at         | TIMESTAMP        |      | NULL     |           |        | 软删除时间                                                   |

#### 4.4.3 标签表 (tags)

| 字段名        | 数据类型         | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                   |
| ------------- | ---------------- | ---- | -------- | --------- | ------ | ------------------------------------------ |
| id            | INT UNSIGNED     |      | NOT NULL | PK        |        | 标签ID，自增                               |
| site_id       | INT UNSIGNED     |      | NOT NULL | FK (sites.id) |        | 站点ID (多站点时关联)                        |
| name          | VARCHAR          | 50   | NOT NULL |           |        | 标签名称，同一站点下唯一                   |
| slug          | VARCHAR          | 50   | NOT NULL |           |        | 标签别名/Slug，用于URL，同一站点下唯一     |
| description   | VARCHAR          | 255  | NULL     |           |        | 标签描述                                   |
| usage_count   | INT UNSIGNED     |      | NOT NULL |           | 0      | 使用次数 (考虑是否按站点统计或全局统计)    |
| created_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                 |
| updated_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                 |

*说明: (`site_id`, `name`) 应建立联合唯一索引。 (`site_id`, `slug`) 应建立联合唯一索引。*

#### 4.4.4 内容标签关联表 (post_tag)

| 字段名    | 数据类型        | 是否为空 | 主键/外键 | 备注说明         |
| --------- | --------------- | -------- | --------- | ---------------- |
| post_id   | BIGINT UNSIGNED | NOT NULL | PK, FK (posts.id) | 内容ID           |
| tag_id    | INT UNSIGNED    | NOT NULL | PK, FK (tags.id) | 标签ID           |
| created_at | TIMESTAMP      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间 |

*说明: `post_id` 和 `tag_id` 组成联合主键。*

#### 4.4.5 评论表 (comments)

| 字段名        | 数据类型         | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                                     |
| ------------- | ---------------- | ---- | -------- | --------- | ------ | ------------------------------------------------------------ |
| id            | BIGINT UNSIGNED  |      | NOT NULL | PK        |        | 评论ID，自增                                                 |
| post_id       | BIGINT UNSIGNED  |      | NOT NULL | FK (posts.id) |        | 关联的内容ID                                                 |
| user_id       | INT UNSIGNED     |      | NULL     | FK (frontend_users.id) | NULL   | 评论用户ID (前台注册用户)                                        |
| parent_id     | BIGINT UNSIGNED  |      | NULL     | FK (comments.id) | NULL   | 父评论ID，用于实现评论回复层级结构                           |
| author_name   | VARCHAR          | 50   | NULL     |           |        | 评论者名称 (匿名用户)                                        |
| author_email  | VARCHAR          | 100  | NULL     |           |        | 评论者邮箱 (匿名用户)                                        |
| author_url    | VARCHAR          | 255  | NULL     |           |        | 评论者网址 (匿名用户)                                        |
| author_ip     | VARCHAR          | 45   | NULL     |           |        | 评论者IP地址                                                 |
| content       | TEXT             |      | NOT NULL |           |        | 评论内容                                                     |
| status        | VARCHAR          | 20   | NOT NULL |           | 'pending' | 评论状态 (pending:待审核, approved:已批准, spam:垃圾评论, trashed:回收站) |
| user_agent    | VARCHAR          | 255  | NULL     |           |        | 评论者User Agent                                             |
| created_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                                   |
| updated_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                                   |

#### 4.4.6 专题表 (collections)

| 字段名        | 数据类型         | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                   |
| ------------- | ---------------- | ---- | -------- | --------- | ------ | ------------------------------------------ |
| id            | INT UNSIGNED     |      | NOT NULL | PK        |        | 专题ID，自增                               |
| site_id       | INT UNSIGNED     |      | NOT NULL | FK (sites.id) |        | 站点ID (多站点时关联)                        |
| name          | VARCHAR          | 100  | NOT NULL |           |        | 专题名称                                   |
| slug          | VARCHAR          | 100  | NOT NULL | UNIQUE    |        | 专题别名/Slug，用于URL，同站点下唯一       |
| description   | TEXT             |      | NULL     |           |        | 专题描述                                   |
| cover_image   | VARCHAR          | 255  | NULL     |           |        | 专题封面图URL                              |
| status        | TINYINT UNSIGNED |      | NOT NULL |           | 1      | 状态 (0:禁用, 1:启用)                        |
| created_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                 |
| updated_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                 |

#### 4.4.7 内容专题关联表 (collection_post)

| 字段名        | 数据类型        | 是否为空 | 主键/外键 | 备注说明         |
| ------------- | --------------- | -------- | --------- | ---------------- |
| collection_id | INT UNSIGNED    | NOT NULL | PK, FK (collections.id) | 专题ID           |
| post_id       | BIGINT UNSIGNED | NOT NULL | PK, FK (posts.id) | 内容ID           |
| sort_order    | INT UNSIGNED    | NOT NULL |           | 0      | 内容在专题中的排序 |
| created_at    | TIMESTAMP       | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间 |

*说明: `collection_id` 和 `post_id` 组成联合主键。*
    - 内容创建者：`posts.user_id` 字段关联的是 `backend_users.id`，表示内容的发布者是后台用户。
    - 内容评论者：`comments.user_id` 字段关联的是 `frontend_users.id`，表示发表评论的是前台注册用户。如果允许匿名评论，则 `user_id` 为 NULL。
- **避免直接敏感数据关联：** 除非业务逻辑强相关且有严格的安全控制，否则应避免前后台用户表之间直接通过ID进行大规模数据JOIN操作。如需在后台展示前台用户信息，建议通过独立的查询服务或API进行，并注意脱敏处理。

---

#### 4.4.8 自定义字段表 (custom_fields)

| 字段名        | 数据类型         | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                                     |
| ------------- | ---------------- | ---- | -------- | --------- | ------ | ------------------------------------------------------------ |
| id            | INT UNSIGNED     |      | NOT NULL | PK        |        | 自定义字段ID，自增                                           |
| site_id       | INT UNSIGNED     |      | NULL     | FK (sites.id) | NULL   | 站点ID (NULL表示全局共享，否则为特定站点私有)              |
| field_name    | VARCHAR          | 50   | NOT NULL |           |        | 字段标识符 (英文，全局唯一或同一站点下唯一)                  |
| field_label   | VARCHAR          | 100  | NOT NULL |           |        | 字段显示名称                                                 |
| field_type    | VARCHAR          | 20   | NOT NULL |           |        | 字段类型 (text, textarea, number, select, checkbox, radio, date, image, file等) |
| field_options | TEXT             |      | NULL     |           |        | 字段选项 (如select, checkbox, radio的选项，JSON格式)         |
| default_value | VARCHAR          | 255  | NULL     |           |        | 默认值                                                       |
| placeholder   | VARCHAR          | 255  | NULL     |           |        | 输入提示                                                     |
| validation_rules | TEXT          |      | NULL     |           |        | 验证规则 (如: required, email, min:5, max:100)               |
| help_text     | VARCHAR          | 255  | NULL     |           |        | 帮助文本/提示信息                                            |
| sort_order    | INT UNSIGNED     |      | NOT NULL |           | 0      | 显示顺序                                                     |
| is_required   | BOOLEAN          |      | NOT NULL |           | false  | 是否必填                                                     |
| is_filterable | BOOLEAN          |      | NOT NULL |           | false  | 是否可用于筛选                                               |
| is_searchable | BOOLEAN          |      | NOT NULL |           | false  | 是否可用于搜索                                               |
| target_entity | VARCHAR          | 50   | NOT NULL |           | 'post' | 目标实体类型 (如: post, category, user)                      |
| created_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                                   |
| updated_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                                   |

*说明: (`site_id`, `field_name`) 应建立联合唯一索引 (当 `site_id` 不为 NULL 时)。如果 `site_id` 为 NULL，则 `field_name` 应全局唯一。这可能需要应用层逻辑或数据库特定功能（如部分索引）来保证。*

#### 4.4.9 内容自定义字段值表 (post_custom_field_values)

| 字段名          | 数据类型        | 是否为空 | 主键/外键 | 备注说明                                   |
| --------------- | --------------- | -------- | --------- | ------------------------------------------ |
| id              | BIGINT UNSIGNED |      | NOT NULL | PK        | 自增ID                                     |
| post_id         | BIGINT UNSIGNED | NOT NULL | FK (posts.id) | 关联的内容ID                               |
| custom_field_id | INT UNSIGNED    | NOT NULL | FK (custom_fields.id) | 关联的自定义字段ID                         |
| field_value     | LONGTEXT        |      | NULL     |           | 字段值 (根据field_type存储不同格式的数据)    |
| created_at      | TIMESTAMP       | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                 |
| updated_at      | TIMESTAMP       | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                 |

*说明: `post_id` 和 `custom_field_id` 应建立联合唯一索引，确保一个内容的一个自定义字段只有一个值。*

### 4.5 多站点管理模块

#### 4.5.1 站点表 (sites)

| 字段名        | 数据类型         | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                   |
| ------------- | ---------------- | ---- | -------- | --------- | ------ | ------------------------------------------ |
| id            | INT UNSIGNED     |      | NOT NULL | PK        |        | 站点ID，自增                               |
| site_name     | VARCHAR          | 100  | NOT NULL |           |        | 站点名称                                   |
| domain        | VARCHAR          | 255  | NOT NULL | UNIQUE    |        | 站点主域名                                 |
| status        | TINYINT UNSIGNED |      | NOT NULL |           | 1      | 状态 (0:禁用, 1:启用)                        |
| settings      | JSON             |      | NULL     |           |        | 站点特定配置 (JSON格式)                    |
| created_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                 |
| updated_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                 |

### 4.6 系统配置模块

#### 4.6.1 配置项表 (settings)

| 字段名        | 数据类型         | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                   |
| ------------- | ---------------- | ---- | -------- | --------- | ------ | ------------------------------------------ |
| id            | INT UNSIGNED     |      | NOT NULL | PK        |        | 配置项ID，自增                             |
| site_id       | INT UNSIGNED     |      | NULL     | FK (sites.id) | NULL   | 关联的站点ID (NULL表示全局配置)            |
| group_key     | VARCHAR          | 50   | NOT NULL |           |        | 配置分组键 (如: general, mail, seo)        |
| item_key      | VARCHAR          | 100  | NOT NULL |           |        | 配置项键                                   |
| item_value    | TEXT             |      | NULL     |           |        | 配置项值                                   |
| item_type     | VARCHAR          | 20   | NOT NULL |           | 'string' | 值类型 (string, number, boolean, array)    |
| description   | VARCHAR          | 255  | NULL     |           |        | 配置项描述                                 |
| created_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                 |
| updated_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                 |

*说明: `site_id` 和 `item_key` (或者 `site_id`, `group_key`, `item_key`) 应建立联合唯一索引。*

### 4.7 插件模块

**设计原则：** 插件本身是全局安装和管理的，但其在不同站点的启用状态和部分配置可以是站点特定的。

#### 4.7.1 插件表 (plugins)

*描述：存储系统中所有已安装插件的基本信息，这些信息是全局共享的。*

| 字段名        | 数据类型         | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                   |
| ------------- | ---------------- | ---- | -------- | --------- | ------ | ------------------------------------------ |
| id            | INT UNSIGNED     |      | NOT NULL | PK        |        | 插件ID，自增                               |
| name          | VARCHAR          | 100  | NOT NULL | UNIQUE    |        | 插件名称 (唯一标识符，如 `my-seo-plugin`)    |
| title         | VARCHAR          | 100  | NOT NULL |           |        | 插件显示标题                               |
| description   | TEXT             |      | NULL     |           |        | 插件描述                                   |
| version       | VARCHAR          | 20   | NOT NULL |           |        | 插件版本号                                 |
| author        | VARCHAR          | 100  | NULL     |           |        | 插件作者                                   |
| author_url    | VARCHAR          | 255  | NULL     |           |        | 作者链接                                   |
| global_settings | JSON           |      | NULL     |           |        | 插件的全局默认配置 (JSON格式)              |
| requires_license | BOOLEAN        |      | NOT NULL |           | false  | 是否需要许可证                             |
| installed_at  | TIMESTAMP        |      | NULL     |           |        | 安装时间                                   |
| created_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                 |
| updated_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                 |

#### 4.7.2 站点插件关联表 (site_plugins)

*描述：管理插件在各个站点上的激活状态和站点特定配置。*

| 字段名        | 数据类型         | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                   |
| ------------- | ---------------- | ---- | -------- | --------- | ------ | ------------------------------------------ |
| site_id       | INT UNSIGNED     |      | NOT NULL | PK, FK (sites.id) |   | 站点ID                                     |
| plugin_id     | INT UNSIGNED     |      | NOT NULL | PK, FK (plugins.id)|   | 插件ID                                     |
| is_active     | BOOLEAN          |      | NOT NULL |           | false  | 该插件在此站点是否激活                     |
| site_settings | JSON             |      | NULL     |           |        | 插件在此站点的特定配置 (JSON格式，可覆盖全局配置) | 
| license_key   | VARCHAR          | 255  | NULL     |           |        | 站点特定的许可证密钥 (如果插件需要)        |
| created_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                 |
| updated_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                 |

*说明: (`site_id`, `plugin_id`) 组成联合主键。*

### 4.8 主题模块

#### 4.8.1 主题表 (themes)

*描述：存储系统中所有已安装主题的基本信息，这些信息是全局共享的。*

| 字段名        | 数据类型         | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                   |
| ------------- | ---------------- | ---- | -------- | --------- | ------ | ------------------------------------------ |
| id            | INT UNSIGNED     |      | NOT NULL | PK        |        | 主题ID，自增                               |
| name          | VARCHAR          | 100  | NOT NULL | UNIQUE    |        | 主题名称 (唯一标识符，如 `my-awesome-theme`) |
| title         | VARCHAR          | 100  | NOT NULL |           |        | 主题显示标题                               |
| description   | TEXT             |      | NULL     |           |        | 主题描述                                   |
| version       | VARCHAR          | 20   | NOT NULL |           |        | 主题版本号                                 |
| author        | VARCHAR          | 100  | NULL     |           |        | 主题作者                                   |
| author_url    | VARCHAR          | 255  | NULL     |           |        | 作者链接                                   |
| preview_image | VARCHAR          | 255  | NULL     |           |        | 主题预览图路径                             |
| global_config | JSON             |      | NULL     |           |        | 主题的全局默认配置 (JSON格式)              |
| installed_at  | TIMESTAMP        |      | NULL     |           |        | 安装时间                                   |
| created_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                 |
| updated_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                 |

#### 4.8.2 站点主题关联表 (site_themes)

*描述：管理主题在各个站点上的激活状态和站点特定配置。一个站点在同一时间只能激活一个主题。*

| 字段名        | 数据类型         | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                   |
| ------------- | ---------------- | ---- | -------- | --------- | ------ | ------------------------------------------ |
| site_id       | INT UNSIGNED     |      | NOT NULL | PK, FK (sites.id) |   | 站点ID                                     |
| theme_id      | INT UNSIGNED     |      | NOT NULL | FK (themes.id) |   | 主题ID                                     |
| is_active     | BOOLEAN          |      | NOT NULL |           | false  | 该主题在此站点是否为当前激活主题           |
| site_config   | JSON             |      | NULL     |           |        | 主题在此站点的特定配置 (JSON格式，可覆盖全局配置) | 
| created_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                 |
| updated_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                 |

*说明: `site_id` 是主键，确保一个站点只有一个激活的主题记录（通过业务逻辑保证 `is_active` 为 true 的记录唯一，或者 `(site_id, is_active)` 组合唯一且 `is_active` 为 true 的只有一个）。更严格的方式是将 `(site_id, theme_id)` 作为联合主键，并通过应用层逻辑确保每个 `site_id` 只有一个 `is_active=true` 的条目。此处选择 `site_id` 为主键，意味着一个站点只有一条主题配置记录，`theme_id` 指向当前使用的主题，`is_active` 字段可以省略或保留用于标记是否真的"启用"了主题功能。为了清晰，保留 `is_active` 并依赖应用层保证其唯一性。或者，更简单地，`site_id` 作为主键，直接关联 `theme_id` 和 `site_config`，激活状态由该记录的存在即表示。这里采用 `site_id` 为主键，并包含 `theme_id` 和 `is_active`。*

### 4.9 版本与授权模块

本模块负责管理GACMS的产品版本、许可证授权以及基于授权的功能启用控制。

#### 4.9.1 许可证表 (licenses)

存储已激活的或尝试激活的许可证信息。

| 字段名                   | 类型          | 约束/索引 | 描述                                                                 |
| ------------------------ | ------------- | --------- | -------------------------------------------------------------------- |
| `id`                     | `INT`         | `PK`      | 主键ID                                                               |
| `license_key`            | `VARCHAR(255)`| `UK`      | 许可证密钥 (唯一, 应加密存储)                                            |
| `product_version`        | `VARCHAR(50)` |           | 产品版本 (例如: Personal, Professional, Commercial)                    |
| `status`                 | `VARCHAR(20)` | `INDEX`   | 许可证状态 (例如: pending, active, expired, revoked, trial)            |
| `expires_at`             | `TIMESTAMP`   | `NULLABLE`| 过期时间 (NULL 表示永久有效)                                             |
| `licensed_to`            | `VARCHAR(255)`| `NULLABLE`| 授权给 (个人或公司名称)                                                  |
| `licensed_email`         | `VARCHAR(255)`| `NULLABLE`| 授权邮箱                                                               |
| `max_sites`              | `INT`         | `NULLABLE`| 允许的最大站点数量 (NULL 表示无限制)                                     |
| `max_users`              | `INT`         | `NULLABLE`| 允许的最大用户数量 (NULL 表示无限制)                                     |
| `enabled_features`       | `TEXT`        | `NULLABLE`| 此许可证启用的特定功能列表 (例如 JSON 数组: `["multi_site", "workflow"]`) |
| `raw_license_data`       | `TEXT`        | `NULLABLE`| 原始许可证数据 (例如，从授权服务器获取的加密数据)                          |
| `activated_at`           | `TIMESTAMP`   | `NULLABLE`| 首次激活时间                                                             |
| `last_validated_at`      | `TIMESTAMP`   | `NULLABLE`| 最后成功验证时间                                                         |
| `validation_server_url`  | `VARCHAR(255)`| `NULLABLE`| 用于验证此许可证的授权服务器URL (如果适用)                               |
| `notes`                  | `TEXT`        | `NULLABLE`| 备注信息                                                               |
| `created_at`             | `TIMESTAMP`   |           | 创建时间                                                               |
| `updated_at`             | `TIMESTAMP`   |           | 更新时间                                                               |

#### 4.9.2 功能开关表 (feature_toggles)

定义系统中的所有可控功能及其基本属性，用于结合许可证信息判断功能是否可用。

| 字段名                        | 类型          | 约束/索引 | 描述                                                                 |
| ----------------------------- | ------------- | --------- | -------------------------------------------------------------------- |
| `id`                          | `INT`         | `PK`      | 主键ID                                                               |
| `feature_key`                 | `VARCHAR(100)`| `UK`      | 功能的唯一标识符 (例如: `multi_site_management`, `advanced_seo`)         |
| `name`                        | `VARCHAR(255)`|           | 功能的显示名称                                                           |
| `description`                 | `TEXT`        | `NULLABLE`| 功能的详细描述                                                           |
| `required_min_version`        | `VARCHAR(50)` | `NULLABLE`| 启用此功能所需的最低产品版本 (例如: Personal, Professional, Commercial)  |
| `is_enabled_globally`         | `BOOLEAN`     | `DEFAULT 0`| 是否全局启用 (不受许可证限制，例如一些核心基础功能)                      |
| `is_configurable_by_license`  | `BOOLEAN`     | `DEFAULT 1`| 此功能是否可以通过许可证配置来启用或禁用                                   |
| `default_enabled_for_versions`| `TEXT`        | `NULLABLE`| 默认在哪些版本中启用此功能 (JSON 数组, 例如 `["Professional", "Commercial"]`)，如果 `is_configurable_by_license` 为true，此项可作为参考 |
| `created_at`                  | `TIMESTAMP`   |           | 创建时间                                                               |
| `updated_at`                  | `TIMESTAMP`   |           | 更新时间                                                               |

**关系说明:**

- `licenses` 表中的 `enabled_features` 字段可以存储一个由 `feature_toggles.feature_key` 组成的列表，表示该许可证具体激活了哪些功能。
- 系统在判断某个功能是否可用时，会综合考虑当前激活的许可证信息 (`licenses` 表) 和该功能在 `feature_toggles` 表中的定义 (例如 `required_min_version`, `is_enabled_globally` 等)。

| 字段名        | 数据类型         | 长度 | 是否为空 | 主键/外键 | 默认值 | 备注说明                                   |
| ------------- | ---------------- | ---- | -------- | --------- | ------ | ------------------------------------------ |
| id            | INT UNSIGNED     |      | NOT NULL | PK        |        | 主题ID，自增                               |
| name          | VARCHAR          | 100  | NOT NULL | UNIQUE    |        | 主题名称 (唯一标识符，通常是目录名)        |
| title         | VARCHAR          | 100  | NOT NULL |           |        | 主题显示标题                               |
| description   | TEXT             |      | NULL     |           |        | 主题描述                                   |
| version       | VARCHAR          | 20   | NOT NULL |           |        | 主题版本号                                 |
| author        | VARCHAR          | 100  | NULL     |           |        | 主题作者                                   |
| author_url    | VARCHAR          | 255  | NULL     |           |        | 作者链接                                   |
| preview_image | VARCHAR          | 255  | NULL     |           |        | 主题预览图URL                              |
| settings      | JSON             |      | NULL     |           |        | 主题配置 (JSON格式)                        |
| is_active     | BOOLEAN          |      | NOT NULL |           | false  | 是否为当前站点激活的主题 (需结合站点表)    |
| installed_at  | TIMESTAMP        |      | NULL     |           |        | 安装时间                                   |
| created_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间                 |
| updated_at    | TIMESTAMP        |      | NOT NULL |           | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                 |

| 字段名        | 数据类型    | 是否为空 | 主键/外键 | 备注说明           |
| ------------- | ----------- | -------- | --------- | ------------------ |
| permission_id | INT UNSIGNED| NOT NULL | PK, FK (permissions.id) | 权限ID             |
| role_id       | INT UNSIGNED| NOT NULL | PK, FK (roles.id) | 角色ID             |
| created_at    | TIMESTAMP   | NOT NULL |           | CURRENT_TIMESTAMP | 创建时间 |

*说明: `permission_id` 和 `role_id` 组成联合主键。*

---

## 5. 物理数据模型 (PDM)

*(针对特定数据库管理系统(DBMS)的详细设计)*

### 5.1 数据库选型

根据项目需求、技术栈以及 <mcfile name="Technology_Selection.md" path="docs/Technology_Selection.md"></mcfile> 文档中的评估：

- **首选数据库：PostgreSQL (最新稳定版，如 PostgreSQL 15+ )**
  - **理由：**
    - 功能强大：支持更复杂的数据类型（如JSONB、数组）、高级查询、事务控制和并发处理。
    - 可靠性与数据完整性：以其稳定性和对SQL标准的严格遵守而闻名。
    - 可扩展性：良好的水平和垂直扩展能力，适合未来系统发展。
    - 社区活跃：拥有强大的社区支持和丰富的生态系统。
    - 开源免费：无商业许可费用。

- **备选数据库：MySQL (最新稳定版，如 MySQL 8.0+ )**
  - **理由：**
    - 广泛应用：最流行的开源关系型数据库之一，拥有庞大的用户基础和社区。
    - 易用性：相对易于学习、部署和管理。
    - 良好集成：与Go (Gin)框架集成良好，并提供清晰的API接口供前端或其他服务调用。
    - 性能表现：对于大多数Web应用场景，性能表现良好。
    - 开源免费：无商业许可费用。

**最终决策：**

项目将优先采用 **PostgreSQL** 作为主要的关系型数据库管理系统。在特定情况下，如果团队对MySQL有更深厚的经验积累，或者部署环境对MySQL有更好的支持，也可以考虑使用MySQL。

选择的具体版本将是各自数据库在项目开发和部署时的最新稳定版本。

### 5.2 表结构详细设计

本章节将详细定义在选定数据库（优先PostgreSQL，备选MySQL）中各个表的物理结构。这包括每个字段精确的数据类型、长度/精度、是否允许为空 (NULL/NOT NULL)、默认值、主键 (PRIMARY KEY)、外键 (FOREIGN KEY) 约束、唯一约束 (UNIQUE) 和检查约束 (CHECK)。

设计将严格遵循前面逻辑数据模型 (LDM) 中定义的表结构，并根据所选数据库的特性进行微调。例如，PostgreSQL的 `SERIAL` 或 `BIGSERIAL` 类型对应MySQL的 `INT AUTO_INCREMENT` 或 `BIGINT AUTO_INCREMENT`。

**通用约定：**

- **主键：** 通常命名为 `id`，类型为自增整数 (如 `BIGSERIAL` for PostgreSQL, `BIGINT UNSIGNED AUTO_INCREMENT` for MySQL)。
- **外键：** 命名通常为 `[关联表名单数]_id`，例如 `user_id` 关联 `users` 表的 `id` 字段。外键约束将明确定义，并考虑 `ON DELETE` 和 `ON UPDATE` 的行为 (如 `CASCADE`, `SET NULL`, `RESTRICT`)。
- **时间戳：**
    - `created_at`: 记录创建时间，通常设置为 `TIMESTAMP WITH TIME ZONE` (PostgreSQL) 或 `TIMESTAMP` (MySQL)，默认值为当前时间 (`CURRENT_TIMESTAMP`)。
    - `updated_at`: 记录最后更新时间，通常设置为 `TIMESTAMP WITH TIME ZONE` (PostgreSQL) 或 `TIMESTAMP` (MySQL)，默认值为当前时间，并在更新时自动更新 (`ON UPDATE CURRENT_TIMESTAMP` for MySQL, 或通过触发器实现 for PostgreSQL)。
    - `deleted_at`: 用于软删除，类型同上，默认为 `NULL`。
- **布尔值：** 使用数据库原生的布尔类型 (如 `BOOLEAN` for PostgreSQL, `TINYINT(1)` for MySQL)。
- **文本：** 根据长度需求选择 `VARCHAR(n)`, `TEXT`, `MEDIUMTEXT`, `LONGTEXT` (MySQL) 或 `VARCHAR(n)`, `TEXT` (PostgreSQL)。
- **JSON数据：** 使用 `JSONB` (PostgreSQL) 或 `JSON` (MySQL) 存储半结构化数据。
- **字符集与排序规则：** 统一使用 `UTF-8` (如 `utf8mb4` for MySQL) 字符集，并选择合适的排序规则。

**示例 (users 表 - PostgreSQL):**

```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    nickname VARCHAR(50),
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    avatar VARCHAR(255),
    bio TEXT,
    status SMALLINT NOT NULL DEFAULT 1, -- 0:禁用, 1:启用, 2:未激活
    last_login_at TIMESTAMPTZ,
    last_login_ip VARCHAR(45),
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

*(后续将为LDM中定义的每个表提供类似的详细SQL DDL语句或详细的字段描述表。)*

### 5.3 索引设计

为了优化查询性能，将根据常见的查询模式和业务需求为表设计合适的索引。索引设计将遵循以下原则：

- **主键索引：** 数据库通常会自动为主键创建索引。
- **外键索引：** 为所有外键字段创建索引，以加速表连接操作和维护数据完整性。
- **唯一索引：** 为需要保证唯一性的字段或字段组合创建唯一索引 (如用户表的 `username`, `email`；栏目表的 `site_id`, `slug` 组合)。
- **普通索引 (B-Tree)：**
    - 为经常作为查询条件 (`WHERE` 子句)、排序 (`ORDER BY` 子句) 或分组 (`GROUP BY` 子句) 的字段创建索引。
    - 考虑高选择性的列优先创建索引。
    - 对于组合查询条件，可以创建复合索引。复合索引的列顺序非常重要，应将最常用于精确匹配或选择性最高的列放在前面。
- **全文索引：** 对于内容表 (`posts`) 的 `title` 和 `content` 字段，如果需要高效的文本搜索功能，并且不引入外部搜索引擎 (如Elasticsearch)，可以考虑使用数据库内置的全文搜索引擎 (如PostgreSQL的 `GIN` 或 `GiST` 索引配合 `tsvector`，MySQL的 `FULLTEXT` 索引)。
- **JSON字段索引 (如果使用)：** 对于存储在 `JSONB` (PostgreSQL) 或 `JSON` (MySQL) 类型字段中的数据，如果需要基于JSON内部的键值进行查询，可以创建相应的索引 (如PostgreSQL的GIN索引)。
- **覆盖索引：** 在某些情况下，可以创建覆盖索引，即索引包含查询所需的所有列，从而避免回表查询，进一步提升性能。

**注意事项：**

- 避免创建过多的索引，因为索引会增加写操作（INSERT, UPDATE, DELETE）的开销，并占用额外的存储空间。
- 定期审查和优化索引，移除不再使用或低效的索引。
- 使用数据库提供的查询分析工具 (如 `EXPLAIN ANALYZE`) 来评估索引的有效性。

**示例 (posts 表的部分索引 - PostgreSQL):**

```sql
-- 外键索引
CREATE INDEX idx_posts_site_id ON posts(site_id);
CREATE INDEX idx_posts_category_id ON posts(category_id);
CREATE INDEX idx_posts_user_id ON posts(user_id);

-- 常用查询字段索引
CREATE INDEX idx_posts_status ON posts(status);
CREATE INDEX idx_posts_type ON posts(type);
CREATE INDEX idx_posts_published_at ON posts(published_at DESC NULLS LAST); -- 假设常按发布时间降序排序

-- 唯一索引 (slug在同站点同类型下唯一)
CREATE UNIQUE INDEX idx_posts_site_id_type_slug ON posts(site_id, type, slug);

-- 全文索引 (示例)
-- ALTER TABLE posts ADD COLUMN tsv tsvector;
-- CREATE INDEX idx_posts_tsv ON posts USING GIN(tsv);
-- CREATE TRIGGER tsvectorupdate BEFORE INSERT OR UPDATE
-- ON posts FOR EACH ROW EXECUTE PROCEDURE
-- tsvector_update_trigger(tsv, 'pg_catalog.english', title, content);
```

*(后续将为关键表和查询场景提供更详细的索引设计方案。)*

### 5.4 约束设计

除了主键、外键和唯一约束（这些通常通过索引实现或与索引紧密相关）之外，还可以定义其他类型的约束来保证数据的完整性和业务规则的正确性。

- **非空约束 (NOT NULL)：** 在表结构详细设计中已明确。确保关键字段不为空。
- **检查约束 (CHECK)：** 用于限制列中允许的值的范围或格式。例如：
    - `users` 表的 `status` 字段只能是预定义的值 (如 0, 1, 2)。
    - `posts` 表的 `view_count` 或 `like_count` 必须大于等于0。
    - 邮箱格式的简单校验（虽然更复杂的校验通常在应用层进行）。
- **外键约束的引用行为 (ON DELETE / ON UPDATE)：**
    - `ON DELETE`:
        - `CASCADE`: 当父表记录被删除时，自动删除子表中的关联记录。
        - `SET NULL`: 当父表记录被删除时，将子表中的外键字段设置为NULL (要求该外键字段允许为NULL)。
        - `RESTRICT` / `NO ACTION`: 如果子表中存在关联记录，则阻止删除父表记录 (默认行为)。
        - `SET DEFAULT`: 当父表记录被删除时，将子表中的外键字段设置为其默认值。
    - `ON UPDATE`:
        - 行为与 `ON DELETE` 类似，但针对父表主键更新的情况。
    - 选择哪种行为取决于业务逻辑。例如，如果删除了一个用户，其创建的内容 (`posts`) 可能需要 `SET NULL` (如果允许内容没有作者) 或 `RESTRICT` (如果不允许删除有内容的用户)。如果删除了一个栏目，其下的内容可能需要 `SET NULL` (内容变为无栏目) 或 `CASCADE` (内容也一并删除，需谨慎)。

**示例 (部分约束 - PostgreSQL):**

```sql
-- users 表 status 字段的检查约束
ALTER TABLE users ADD CONSTRAINT check_users_status CHECK (status IN (0, 1, 2));

-- posts 表 view_count 字段的检查约束
ALTER TABLE posts ADD CONSTRAINT check_posts_view_count CHECK (view_count >= 0);

-- role_user 表外键约束的引用行为 (示例)
ALTER TABLE role_user
    DROP CONSTRAINT IF EXISTS role_user_user_id_fkey, -- 假设已存在的外键名
    ADD CONSTRAINT role_user_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE role_user
    DROP CONSTRAINT IF EXISTS role_user_role_id_fkey, -- 假设已存在的外键名
    ADD CONSTRAINT role_user_role_id_fkey
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE;
```

*(后续将根据业务规则为相关表和字段补充具体的检查约束和外键引用行为定义。)*

---

## 6. 数据字典

数据字典是对系统中所有数据元素的详细定义和描述，旨在确保数据的一致性、准确性和可理解性。它将包含逻辑数据模型 (LDM) 中定义的所有表及其字段的详细信息。

**数据字典的构成元素：**

对于每个数据表，将包含以下信息：
- **表名 (Table Name):** 数据库中的表名。
- **表描述 (Table Description):** 表的业务含义和用途。

对于表中的每个字段，将包含以下信息：
- **字段名 (Field Name):** 数据库中的字段名。
- **数据类型 (Data Type):** 字段的物理数据类型 (如 `VARCHAR(255)`, `BIGINT`, `TIMESTAMPTZ`, `BOOLEAN`, `JSONB`)。
- **长度/精度 (Length/Precision):** 字段的长度或数字精度。
- **是否为空 (Nullable):** 字段是否允许为空值 (Yes/No)。
- **主键 (Primary Key):** 是否为主键 (Yes/No)。
- **外键 (Foreign Key):** 如果是外键，指出关联的表和字段。
- **默认值 (Default Value):** 字段的默认值（如果有）。
- **约束 (Constraints):** 应用于字段的检查约束或唯一约束。
- **描述/备注 (Description/Remarks):** 字段的业务含义、取值范围、枚举值说明等。

**示例 (users 表的部分数据字典条目):**

**表名:** `users`
**表描述:** 存储系统用户信息。

| 字段名        | 数据类型         | 长度 | 是否为空 | 主键 | 外键 | 默认值 | 约束 | 描述/备注                                       |
| ------------- | ---------------- | ---- | -------- | ---- | ---- | ------ | ---- | ----------------------------------------------- |
| id            | BIGSERIAL        |      | No       | Yes  |      |        |      | 用户ID，自增                                    |
| username      | VARCHAR          | 50   | No       |      |      |        | UNIQUE | 用户名，唯一                                    |
| email         | VARCHAR          | 100  | No       |      |      |        | UNIQUE | 邮箱，唯一，用于登录或找回密码                    |
| password_hash | VARCHAR          | 255  | No       |      |      |        |      | 哈希后的密码                                    |
| status        | SMALLINT         |      | No       |      |      | 1      | CHECK (status IN (0,1,2)) | 用户状态 (0:禁用, 1:启用, 2:未激活)             |
| created_at    | TIMESTAMPTZ      |      | No       |      |      | CURRENT_TIMESTAMP | 创建时间                                        |
| ...           | ...              | ...  | ...      | ...  | ...  | ...    | ...  | ...                                             |

*(后续将为LDM中定义的所有表及其字段提供完整的数据字典。这通常是一个较为繁琐但非常重要的工作，可以考虑使用工具辅助生成或维护。)*

---

## 7. 数据流设计

数据流设计 (Data Flow Design) 描述了数据在系统内部以及系统与外部实体之间的流动路径和处理过程。它有助于理解数据是如何被创建、读取、更新、删除 (CRUD) 以及转换的。

我们将使用数据流图 (DFD - Data Flow Diagrams) 来可视化核心业务流程中的数据流动。

**DFD基本符号：**

- **外部实体 (External Entity):** 方框表示，代表数据的来源或去向（如用户、第三方系统）。
- **处理过程 (Process):** 圆角矩形或圆形表示，代表对数据进行转换或操作的功能模块。
- **数据存储 (Data Store):** 两条平行线或开放式矩形表示，代表数据的存储位置（如数据库表）。
- **数据流 (Data Flow):** 带箭头的线表示，指明数据的流动方向和数据内容。

### 7.1 核心业务数据流

以下列举几个核心业务场景的数据流向简要描述：

1.  **用户注册流程：**
    *   `用户 (外部实体)` -> 提供注册信息 (数据流) -> `注册处理 (过程)`
    *   `注册处理 (过程)` -> 验证用户信息, 生成密码哈希 (数据流) -> `用户表 (数据存储)` (写入新用户)
    *   `注册处理 (过程)` -> 发送验证邮件 (数据流) -> `邮件服务 (外部实体/过程)`
    *   `注册处理 (过程)` -> 返回注册成功/失败消息 (数据流) -> `用户 (外部实体)`

2.  **内容发布流程 (简化版)：**
    *   `编辑/管理员 (外部实体)` -> 提供内容数据 (标题, 正文, 分类等) (数据流) -> `内容创建/编辑处理 (过程)`
    *   `内容创建/编辑处理 (过程)` -> 验证内容数据 (数据流) -> `内容表 (数据存储)` (写入/更新内容)
    *   `内容创建/编辑处理 (过程)` -> (如果涉及) 更新 `栏目表`、`标签表`、`内容标签关联表` (数据存储)
    *   `内容创建/编辑处理 (过程)` -> (如果发布) 清除相关缓存 (数据流) -> `缓存服务 (过程)`
    *   `内容创建/编辑处理 (过程)` -> 返回操作结果 (数据流) -> `编辑/管理员 (外部实体)`

3.  **用户浏览内容流程：**
    *   `访客 (外部实体)` -> 请求内容URL (数据流) -> `内容获取处理 (过程)`
    *   `内容获取处理 (过程)` -> 查询缓存 (数据流) -> `缓存 (数据存储)`
    *   (缓存未命中) `内容获取处理 (过程)` -> 查询内容数据 (数据流) -> `内容表`, `栏目表`, `用户表` 等 (数据存储)
    *   `内容获取处理 (过程)` -> (获取到数据后) 更新缓存 (数据流) -> `缓存 (数据存储)`
    *   `内容获取处理 (过程)` -> 格式化内容数据 (数据流) -> `主题渲染引擎 (过程)`
    *   `主题渲染引擎 (过程)` -> 生成HTML页面 (数据流) -> `访客 (外部实体)`
    *   `内容获取处理 (过程)` -> 更新浏览次数 (数据流) -> `内容表 (数据存储)`

*(后续将针对更具体的业务流程（如用户登录、评论提交、多站点内容管理等）绘制详细的多层数据流图，从顶层上下文图到底层详细处理过程图。)*

---

## 8. 数据安全与完整性

确保数据的安全性和完整性是GACMS数据模型设计的关键目标之一。

### 8.1 数据备份与恢复

**备份策略：**

- **数据库备份：**
    - **全量备份：** 定期（如每日或每周）对整个数据库进行完整备份。使用数据库自带的备份工具（如 `pg_dump` for PostgreSQL, `mysqldump` for MySQL）。
    - **增量/差异备份：** 在全量备份的基础上，定期（如每日）备份自上次备份以来发生变化的数据，以减少备份时间和存储空间。
    - **事务日志/WAL归档 (Point-in-Time Recovery - PITR)：** 持续归档数据库的事务日志 (Write-Ahead Logging)，以便在发生故障时可以将数据库恢复到任意特定时间点。
- **文件备份：**
    - 定期备份用户上传的媒体文件、主题文件、插件文件等。
    - 可以使用 `rsync` 等工具进行文件同步备份。
- **备份存储：**
    - 备份数据应存储在与生产服务器物理隔离的位置（如独立的备份服务器、云存储服务如AWS S3, Azure Blob Storage）。
    - 考虑对备份数据进行加密存储。
    - 至少保留多个历史备份版本。
- **备份验证：** 定期测试备份文件的可用性和完整性，确保可以成功恢复。

**恢复流程：**

- **制定详细的恢复计划：** 包括不同故障场景下的恢复步骤、预计恢复时间目标 (RTO) 和恢复点目标 (RPO)。
- **恢复演练：** 定期进行恢复演练，验证恢复流程的有效性并熟悉操作步骤。
- **恢复步骤（一般）：**
    1.  评估故障范围和影响。
    2.  准备恢复环境（如果需要）。
    3.  从备份存储中获取最新的可用备份（全量备份 + 增量备份 + 日志文件）。
    4.  恢复数据库到指定时间点。
    5.  恢复文件系统数据。
    6.  验证数据一致性和系统功能。
    7.  切换服务到恢复后的系统。

详细的灾难恢复计划请参考 <mcfile name="Deployment_Architecture.md" path="docs/Deployment_Architecture.md"></mcfile>。

### 8.2 数据校验

数据校验是保证数据准确性和一致性的重要手段，将在不同层面实施：

- **应用层校验：**
    - **输入验证：** 对所有来自用户或外部系统的输入数据进行严格验证，包括：
        - **格式校验：** 如邮箱格式、日期格式、URL格式等。
        - **类型校验：** 如数字、字符串、布尔值等。
        - **长度/范围校验：** 如字符串长度、数字大小范围。
        - **存在性校验：** 如必填项不能为空。
        - **业务规则校验：** 如用户名唯一性、密码复杂度等。
    - **输出编码：** 对输出到前端的数据进行适当编码，防止XSS等攻击。
- **数据库层校验：**
    - **数据类型约束：** 在表结构设计中已定义，确保存入字段的数据类型正确。
    - **非空约束 (NOT NULL)：** 确保关键字段不为空。
    - **唯一约束 (UNIQUE)：** 保证特定字段或字段组合的唯一性。
    - **外键约束 (FOREIGN KEY)：** 维护表之间的引用完整性。
    - **检查约束 (CHECK)：** 限制列中允许的值的范围或特定条件 (如 `status IN (0,1,2)`)。
    - **触发器 (Triggers)：** 可用于实现更复杂的数据校验逻辑或在数据变更时自动执行某些操作（谨慎使用，可能影响性能和增加复杂性）。

详细的安全设计（包括输入验证和输出编码）请参考 <mcfile name="Security_Design.md" path="docs/Security_Design.md"></mcfile>。
详细的数据库约束设计请参考本文档的 "5.4 约束设计" 章节。

---

## 9. 数据迁移与同步 (可选)

本章节讨论在GACMS项目中可能涉及的数据迁移和数据同步场景及其应对方案。

### 9.1 数据迁移 (Data Migration)

如果GACMS需要替换现有的旧内容管理系统，或者从其他数据源导入初始数据，就需要进行数据迁移。

**迁移策略与步骤：**

1.  **数据分析与映射：**
    *   分析源系统的数据结构、数据量、数据质量。
    *   将源系统的数据模型映射到GACMS的目标数据模型。识别字段差异、数据类型转换需求、关联关系处理等。
2.  **迁移工具选择/开发：**
    *   根据数据源的类型和复杂性，选择合适的迁移工具（如ETL工具 Talend, Apache NiFi）或编写自定义迁移脚本 (如使用Go, Python脚本连接源数据库和目标数据库进行数据抽取、转换和加载)。
    *   Go生态中有成熟的数据库迁移工具（如 `golang-migrate/migrate`）可用于管理数据库 schema 的版本控制和演进。数据填充 (Seeding) 通常通过自定义脚本或迁移文件中的特定步骤完成，可用于构建目标数据库结构和填充初始数据。
3.  **数据抽取 (Extract)：** 从源系统导出数据，可以是数据库备份、CSV文件、XML文件、API接口等形式。
4.  **数据转换 (Transform)：**
    *   清洗数据：处理无效数据、重复数据、格式错误数据。
    *   转换数据格式和结构，使其符合GACMS的数据模型要求。
    *   处理关联关系，如用户ID映射、栏目ID映射等。
5.  **数据加载 (Load)：** 将转换后的数据导入到GACMS的数据库中。
6.  **迁移测试与验证：**
    *   进行小批量数据迁移测试，验证迁移脚本的正确性和数据转换的准确性。
    *   对迁移后的数据进行抽样检查和完整性校验。
    *   与业务方共同验证迁移数据的正确性。
7.  **全量迁移与增量迁移：**
    *   **一次性全量迁移 (Big Bang)：** 适用于数据量不大、可接受较长停机时间的场景。在预定时间窗口内停止旧系统，完成所有数据的迁移，然后切换到新系统。
    *   **分阶段/增量迁移 (Phased/Incremental)：** 适用于数据量大、停机时间要求严格的场景。先进行一次全量迁移，然后在旧系统继续运行期间，定期将增量数据同步到新系统，直到最终切换。
8.  **迁移回滚计划：** 制定数据迁移失败时的回滚方案。

**注意事项：**

-   数据迁移是一个复杂且风险较高的过程，需要充分的规划和测试。
-   确保迁移过程中数据的安全性和保密性。
-   用户密码等敏感信息通常无法直接迁移，需要用户在新系统中重置密码。

### 9.2 数据同步 (Data Synchronization)

数据同步是指在GACMS与其他系统之间保持数据一致性的过程。

**常见场景：**

-   **GACMS与CRM系统同步用户信息。**
-   **GACMS与电商平台同步产品信息。**
-   **多GACMS实例间的内容同步 (如开发环境与生产环境的内容同步，需谨慎处理)。**

**同步方案：**

-   **API集成：**
    *   通过GACMS提供的API接口（或对方系统提供的API接口）进行数据的双向或单向同步。
    *   可以开发定时任务或事件驱动的同步程序。
-   **消息队列：**
    *   当GACMS中的数据发生变化时（如用户创建、内容发布），通过消息队列将事件通知给其他系统，其他系统订阅消息并更新其数据。
    *   反之亦然，GACMS也可以订阅其他系统的变更消息。
-   **数据库层面同步 (谨慎使用)：**
    *   如数据库复制、触发器等。这种方式耦合度较高，管理复杂，一般不推荐用于异构系统间的数据同步。
-   **ETL工具：** 对于复杂的、定期的批量数据同步，可以使用ETL工具。

**同步挑战：**

-   **数据冲突解决：** 当同一份数据在多个系统都被修改时，如何解决冲突。
-   **数据一致性保证：** 如何确保同步过程中的数据一致性（最终一致性或强一致性）。
-   **性能影响：** 频繁的数据同步可能对系统性能产生影响。
-   **安全性：** 确保数据同步过程中的传输安全和访问控制。

*(如果项目明确需要数据迁移或同步功能，将在此处提供更详细的方案设计。)*