/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/contenttype/domain/model/ContentItem.go
 * @Description: Defines the ContentItem model for storing actual content entries.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

// ValueMap represents the JSONB column for storing dynamic field values.
type ValueMap map[string]interface{}

// Value implements the driver.Valuer interface.
func (vm ValueMap) Value() (driver.Value, error) {
	return json.Marshal(vm)
}

// Scan implements the sql.Scanner interface.
func (vm *ValueMap) Scan(value interface{}) error {
	b, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, &vm)
}

// ContentItem represents an instance of a specific ContentType.
type ContentItem struct {
	ID            uint      `gorm:"primaryKey"`
	ContentTypeID uint      `gorm:"index;not null"`
	SiteID        uint      `gorm:"index;not null"` // For multi-site support
	AuthorID      uint      `gorm:"index"`
	Status        string    `gorm:"type:varchar(50);default:'draft'"`
	Values        ValueMap  `gorm:"type:json"`
	CreatedAt     time.Time
	UpdatedAt     time.Time
}

func (ContentItem) TableName() string {
	return "content_items"
} 