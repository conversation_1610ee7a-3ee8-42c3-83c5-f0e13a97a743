/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/extension/application/strategy/ThemeEnablingStrategy.go
 * @Description: Implements the enabling strategy for themes.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package strategy

import (
	"gacms/internal/modules/extension/domain/contract"
)

// ThemeEnablingStrategy for themes. In our current design, a theme being present
// in the filesystem means it's "enabled" and available for activation.
// Therefore, these methods are no-ops. They exist to satisfy the interface.
type ThemeEnablingStrategy struct {
}

// NewThemeEnablingStrategy creates a new theme enabling strategy.
func NewThemeEnablingStrategy() contract.EnablingStrategy {
	return &ThemeEnablingStrategy{}
}

// Enable for a theme is a no-op as its existence implies it's enabled.
func (s *ThemeEnablingStrategy) Enable(extName string, siteID uint) error {
	// A theme is considered enabled if it exists on the filesystem.
	// No specific action is needed here.
	return nil
}

// Disable for a theme is also a no-op. To "disable" a theme, an admin
// should uninstall it. An active theme cannot be disabled, it must be
// deactivated first from the site.
func (s *ThemeEnablingStrategy) Disable(extName string, siteID uint) error {
	// To "disable" a theme, it should be uninstalled.
	// This operation could check if the theme is active on any site and prevent disabling.
	// For now, it's a no-op.
	return nil
} 