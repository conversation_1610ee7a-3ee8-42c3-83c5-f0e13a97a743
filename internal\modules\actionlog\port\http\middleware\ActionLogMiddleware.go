/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/actionlog/port/http/middleware/ActionLogMiddleware.go
 * @Description: Middleware for logging admin actions.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package middleware

import (
	"gacms/internal/modules/actionlog/application/service"
	"gacms/internal/modules/actionlog/domain/model"
	userModel "gacms/internal/modules/user/domain/model"
	"github.com/gin-gonic/gin"
	"time"
)

type ActionLogMiddleware struct {
	logService *service.ActionLogService
}

func NewActionLogMiddleware(logService *service.ActionLogService) *ActionLogMiddleware {
	return &ActionLogMiddleware{logService: logService}
}

func (m *ActionLogMiddleware) Log() gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()

		// Process request
		c.Next()

		// Don't log if the request was aborted or not handled
		if c.IsAborted() {
			return
		}

		// Don't log GET requests for now to reduce noise.
		if c.Request.Method == "GET" {
			return
		}

		currentUser, exists := c.Get("currentUser")
		if !exists {
			return // Not an authenticated user action
		}

		user, ok := currentUser.(*userModel.Admin)
		if !ok {
			return // Should not happen if auth middleware is correct
		}
		
		desc, _ := c.Get("action_log_description")
		description, _ := desc.(string)

		log := &model.ActionLog{
			UserID:       user.ID,
			Username:     user.Username,
			SiteID:       user.GetSiteID(),
			IPAddress:    c.ClientIP(),
			Method:       c.Request.Method,
			Path:         c.Request.URL.Path,
			Description:  description,
			Status:       c.Writer.Status(),
			ResponseTime: time.Since(startTime).Milliseconds(),
		}

		// Asynchronously save the log
		go m.logService.CreateLog(log)
	}
} 