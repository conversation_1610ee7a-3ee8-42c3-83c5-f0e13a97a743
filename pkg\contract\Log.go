/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: pkg/contract/Log.go
 * @Description: Defines the public logging contract for the GACMS platform.
 *
 * © 2025 GACMS. All rights reserved.
 */

package contract

// Logger defines a standardized logging interface for the entire application.
// This allows the core and modules to log messages without being coupled to a specific
// logging implementation.
type Logger interface {
	// Info logs a message at the Info level.
	Info(msg string, fields ...map[string]interface{})

	// Debug logs a message at the Debug level.
	Debug(msg string, fields ...map[string]interface{})

	// Warn logs a message at the Warning level.
	Warn(msg string, fields ...map[string]interface{})

	// Error logs a message at the Error level.
	Error(msg string, err error, fields ...map[string]interface{})

	// Fatal logs a message at the Fatal level and then calls os.Exit(1).
	Fatal(msg string, err error, fields ...map[string]interface{})

	// GetInternalLogger returns the underlying logger instance.
	// This should be used sparingly and only when direct access to the underlying
	// logger's specific features is absolutely necessary.
	GetInternalLogger() interface{}
} 