/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: pkg/contract/Database.go
 * @Description: Defines the public database service contract for the GACMS platform.
 *
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"context"
	"gorm.io/gorm"
)

// Database provides a standardized interface for database operations.
// It abstracts the underlying GORM instance so that modules can interact with the
// database in a consistent way.
//
// Architectural Decision: We are consciously coupling this interface to GORM's types
// (*gorm.DB) for pragmatic reasons. While this violates perfect abstraction, it
// provides significant development efficiency and access to GORM's powerful feature
// set without needing to write a complex abstraction layer. This trade-off is
// considered acceptable for the project's goals.
type Database interface {
	// DB returns a GORM database instance scoped to the given context.
	// If the context contains a siteID, the returned instance will be scoped to that site.
	DB(ctx context.Context) *gorm.DB

	// AutoMigrate runs the auto-migration for the given models.
	AutoMigrate(models ...interface{}) error
} 