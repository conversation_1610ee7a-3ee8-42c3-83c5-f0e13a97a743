/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON> <<EMAIL>>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/system/application/service/DomainBindingService.go
 * @Description: Service layer for managing Domain Bindings.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package service

import (
	"errors"
	"fmt"
	"gacms/internal/modules/system/application/dto"
	"gacms/internal/modules/system/domain/contract"
	"gacms/internal/modules/system/domain/model"
)

type DomainBindingService struct {
	repo contract.DomainBindingRepository
}

func NewDomainBindingService(repo contract.DomainBindingRepository) *DomainBindingService {
	return &DomainBindingService{repo: repo}
}

func (s *DomainBindingService) CreateBinding(input *dto.DomainBindingCreateDTO) (*model.DomainBinding, error) {
	bindingType := model.BindingType(input.BindingType)

	// Validate based on binding type
	switch bindingType {
	case model.BindingTypeModule:
		if input.ModuleSlug == nil || *input.ModuleSlug == "" {
			return nil, errors.New("module_slug is required for module binding type")
		}
		if input.SiteID == 0 {
			return nil, errors.New("a valid site_id is required for module binding type")
		}
	case model.BindingTypeCategory:
		if input.CategoryID == nil || *input.CategoryID == 0 {
			return nil, errors.New("category_id is required for category binding type")
		}
		if input.SiteID == 0 {
			return nil, errors.New("a valid site_id is required for category binding type")
		}
	case model.BindingTypePlatformAdmin:
		if input.SiteID != 0 {
			return nil, errors.New("site_id must be 0 for platform_admin binding type")
		}
	default:
		return nil, errors.New("invalid binding type")
	}

	// Ensure no existing binding for this domain
	existing, err := s.repo.GetByDomain(input.Domain)
	if err != nil {
		return nil, fmt.Errorf("failed to check for existing domain: %w", err)
	}
	if existing != nil {
		return nil, errors.New("domain is already bound")
	}
	
	binding := &model.DomainBinding{
		Domain:      input.Domain,
		SiteID:      input.SiteID,
		BindingType: bindingType,
		ModuleSlug:  input.ModuleSlug,
		CategoryID:  input.CategoryID,
	}

	if err := s.repo.Create(binding); err != nil {
		return nil, err
	}
	
	return binding, nil
}

func (s *DomainBindingService) DeleteBinding(id uint) error {
	return s.repo.Delete(id)
}

func (s *DomainBindingService) GetBindingByID(id uint) (*model.DomainBinding, error) {
	return s.repo.GetByID(id)
}

func (s *DomainBindingService) GetBindingByDomain(domain string) (*model.DomainBinding, error) {
	return s.repo.GetByDomain(domain)
}

func (s *DomainBindingService) ListBindingsBySite(siteID uint, page, pageSize int) ([]*model.DomainBinding, int64, error) {
	return s.repo.ListBySiteID(siteID, page, pageSize)
}

// URL重写规则相关方法

func (s *DomainBindingService) CreateURLRule(domainBindingID uint, ruleName, pattern, replacement string, priority int) (*model.URLRewriteRule, error) {
	rule := &model.URLRewriteRule{
		DomainBindingID: domainBindingID,
		RuleName:        ruleName,
		Pattern:         pattern,
		Replacement:     replacement,
		Priority:        priority,
		IsActive:        true,
	}

	if err := s.repo.CreateURLRule(rule); err != nil {
		return nil, err
	}

	return rule, nil
}

func (s *DomainBindingService) UpdateURLRule(rule *model.URLRewriteRule) error {
	return s.repo.UpdateURLRule(rule)
}

func (s *DomainBindingService) DeleteURLRule(id uint) error {
	return s.repo.DeleteURLRule(id)
}

func (s *DomainBindingService) GetURLRulesByDomainBinding(domainBindingID uint) ([]*model.URLRewriteRule, error) {
	return s.repo.GetURLRulesByDomainBinding(domainBindingID)
}

func (s *DomainBindingService) EnableURLRewrite(domainBindingID uint, defaultController, defaultAction string) error {
	binding, err := s.repo.GetByID(domainBindingID)
	if err != nil {
		return err
	}

	binding.URLRewriteEnabled = true
	binding.DefaultController = defaultController
	binding.DefaultAction = defaultAction

	return s.repo.UpdateURLRule(&model.URLRewriteRule{
		Model: binding.Model,
		DomainBindingID: binding.ID,
	})
}

func (s *DomainBindingService) DisableURLRewrite(domainBindingID uint) error {
	binding, err := s.repo.GetByID(domainBindingID)
	if err != nil {
		return err
	}

	binding.URLRewriteEnabled = false

	return s.repo.UpdateURLRule(&model.URLRewriteRule{
		Model: binding.Model,
		DomainBindingID: binding.ID,
	})
}