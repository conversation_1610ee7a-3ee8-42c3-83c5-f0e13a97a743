<!--
Author: Cion Nieh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
-->
# ADR-003: 内容建模引擎 (Content Modeling Engine)

## 状态
已提议

## 上下文
当前的 `content` 模块功能有限，只能处理预定义的"文章"类型。为了将 GACMS 从一个标准的 CMS 提升为一个灵活的 Headless 平台，系统必须支持"自定义内容类型"。管理员应能够在后台创建和定义新的内容模型（如"产品"、"活动"、"作者"），并为这些模型配置字段（如文本、数字、图片、关联关系等），而无需编写任何代码。

同时，作为一个多站点平台，此功能必须与现有的多站点和RBAC（基于角色的访问控制）权限系统深度集成。必须能够精细地控制：
1.  哪个站点可以使用哪个内容模型。
2.  哪个管理员角色在哪个站点中对特定内容模型的数据拥有何种操作权限（增、删、改、查、发布等）。

## 决策
我们将废弃原有的 `content` 模块，并引入一个新的、更强大的 **`contenttype` 模块**。该模块将作为平台的内容建模引擎，负责管理所有内容模型的定义和与之相关的数据。

### 1. 核心数据模型

我们将引入三个核心模型来构建此引擎：

- **`ContentType` (内容模型)**: `content_types` 表
  - `ID`: 主键
  - `Name`: `string` (e.g., "Blog Post", "Product")
  - `Slug`: `string`, `unique` (e.g., "blog_post", "product") - 用于 API 路径和内部标识。
  - `Description`: `string`
  - `IsSystem`: `bool` - 标记是否为系统内置模型，防止删除。

- **`Field` (字段定义)**: `content_fields` 表
  - `ID`: 主键
  - `ContentTypeID`: `uint` - 外键，关联到 `content_types`。
  - `Name`: `string` (e.g., "Title", "Price", "Author")
  - `Slug`: `string` (e.g., "title", "price", "author") - 在同一 ContentType 内唯一。
  - `Type`: `string` (e.g., "text", "textarea", "rich_text", "number", "boolean", "datetime", "image", "relation") - 字段的UI类型。
  - `IsRequired`: `bool`
  - `Validations`: `json` - 存储字段的验证规则 (e.g., `{"min": 0, "max": 100}`).

- **`ContentItem` (内容条目)**: `content_items` 表
  - `ID`: 主键
  - `ContentTypeID`: `uint` - 外键，关联到 `content_types`。
  - `SiteID`: `uint` - **外键，关联到 `sites`，实现多站点内容隔离。**
  - `AuthorID`: `uint` - 外键，关联到 `admins`。
  - `Status`: `string` (e.g., "draft", "published", "archived")。
  - `Values`: `json` - **核心字段，用于存储该条目的所有动态字段值** (e.g., `{"title": "My First Post", "price": 99.99}`).

### 2. 多站点与权限集成

- **模型可用性**: 我们将创建一个新的关联表 `site_content_types` (SiteID, ContentTypeID) 来明确指定哪个站点可以使用哪个内容模型。如果某个站点没有被授权，则该内容模型在该站点的后台界面中不可见。

- **动态权限生成**:
  - 当一个新的 `ContentType` (e.g., "Product") 被创建时，系统会自动生成一组与该模型相关的标准权限。
  - 这些权限将遵循 `scope:resource:action` 的格式，例如：
    - `content:product:create`
    - `content:product:read`
    - `content:product:update`
    - `content:product:delete`
    - `content:product:publish`
  - 这些新生成的权限会自动添加到 `admin_permissions` 表中，然后超级管理员可以在角色管理界面中，将这些权限分配给不同的角色。

- **权限检查**:
  - 中间件 `AdminAuthMiddleware` 在进行权限检查时，会同时考虑用户的角色权限和当前操作的上下文（特别是 `SiteID`）。
  - 对于内容操作，控制器将首先从请求中（如URL或请求体）解析出 `SiteID` 和 `ContentType`，然后检查用户是否拥有针对此 `ContentType` 和 `SiteID` 的相应操作权限。

### 3. API 设计

- **模型管理API (用于后台)**:
  - `GET /api/admin/content-types` - 列出所有内容模型。
  - `POST /api/admin/content-types` - 创建一个新的内容模型。
  - `GET /api/admin/content-types/{slug}` - 获取一个内容模型的详细信息（包括其所有字段）。
  - `PUT /api/admin/content-types/{slug}` - 更新一个内容模型。
  - `POST /api/admin/content-types/{slug}/fields` - 为一个模型添加新字段。
  - `PUT /api/admin/content-types/{slug}/fields/{fieldSlug}` - 更新一个字段。

- **内容管理API (数据驱动的RESTful API)**:
  - `GET /api/content/{siteId}/{modelSlug}` - 列出特定站点下某个内容模型的所有条目。
  - `POST /api/content/{siteId}/{modelSlug}` - 在特定站点下为某个模型创建一个新条目。
  - `GET /api/content/{siteId}/{modelSlug}/{itemId}` - 获取单个条目的详细信息。
  - `PUT /api/content/{siteId}/{modelSlug}/{itemId}` - 更新一个条目。
  - `DELETE /api/content/{siteId}/{modelSlug}/{itemId}` - 删除一个条目。

### 4. 迁移路径

1.  创建新的 `contenttype` 模块，并实现上述核心模型和服务。
2.  创建一个迁移脚本，该脚本：
   - 读取旧的 `posts` 表数据。
   - 在 `content_types` 表中创建一个名为 "Post" (`blog_post`) 的系统内容模型。
   - 为 "Post" 模型创建 "Title", "Content", "Slug", "CoverImage" 等字段。
   - 将 `posts` 表中的每一行数据，转换为 `content_items` 表中的一条记录，并将字段值存入 `Values` JSON字段。
3.  在完成迁移后，旧的 `content` 模块可以被安全地移除。

## 后果

### 积极
- **极大的灵活性**: 系统将能够适应几乎任何类型的内容需求。
- **关注点分离**: 内容的"结构定义"与"数据实例"完全分离。
- **清晰的权限模型**: 多站点下的内容权限管理将变得清晰和可控。
- **未来可扩展性**: 为未来的字段类型（如地理位置、颜色选择器）和功能（如内容版本控制）打下坚实基础。

### 消极
- **复杂性增加**: 引入了更复杂的数据库关系和业务逻辑。
- **性能考虑**: 对 `Values` JSON 字段的查询需要特别的数据库优化技巧（如使用JSON索引）。
- **开发工作量大**: 这是对系统核心功能的重大重构，需要大量开发和测试工作。

## 实施注意事项
- **数据库选择**: 必须使用支持高效JSON操作和索引的数据库版本（如 MySQL 8+ 或 PostgreSQL）。
- **事务性**: 创建/更新内容模型及其字段的操作必须在数据库事务中进行，以保证数据一致性。
- **验证层**: 需要一个强大的验证服务，能够根据 `Field` 定义中的 `Validations` 规则，动态地验证传入的 `ContentItem` 数据。

## 参考资料
- Headless CMS 架构模式
- Strapi, Contentful 等现有产品的设计
- 数据库中的JSON类型使用最佳实践 