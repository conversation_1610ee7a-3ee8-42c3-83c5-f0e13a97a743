/*
 * @Author: <PERSON><PERSON>eh <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: pkg/contract/EventDispatcher.go
 * @Description: 定义事件分发器接口，负责将事件路由到适当的处理器
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import "context"

// EventDispatcher 定义了事件分发器的接口
// 负责将事件路由到注册的处理器
type EventDispatcher interface {
	// Dispatch 将事件分发给所有注册的处理器
	// 同步模式，等待所有处理器处理完成
	Dispatch(event Event) error

	// DispatchAsync 异步分发事件
	// 立即返回，不等待处理完成
	DispatchAsync(ctx context.Context, event Event) <-chan error

	// RegisterHandler 注册一个事件处理器
	RegisterHandler(handler EventHandler) error

	// UnregisterHandler 取消注册一个事件处理器
	UnregisterHandler(handler EventHandler) error
}

// EventDispatcherFactory 定义了创建EventDispatcher实例的工厂接口
type EventDispatcherFactory interface {
	// CreateEventDispatcher 创建一个新的EventDispatcher实例
	CreateEventDispatcher() EventDispatcher
}

// EventDispatchStrategy 定义了事件分发策略的类型
type EventDispatchStrategy string

const (
	// SyncDispatch 表示同步分发策略
	// 事件按顺序分发给处理器，等待每个处理器完成
	SyncDispatch EventDispatchStrategy = "sync"

	// AsyncDispatch 表示异步分发策略
	// 事件并行分发给所有处理器，不等待处理完成
	AsyncDispatch EventDispatchStrategy = "async"

	// ParallelDispatch 表示并行分发策略
	// 事件并行分发给所有处理器，但等待所有处理器完成
	ParallelDispatch EventDispatchStrategy = "parallel"

	// PriorityDispatch 表示优先级分发策略
	// 事件按处理器优先级顺序分发
	PriorityDispatch EventDispatchStrategy = "priority"
)

// StrategyAwareEventDispatcher 定义了支持多种分发策略的事件分发器接口
type StrategyAwareEventDispatcher interface {
	EventDispatcher

	// DispatchWithStrategy 使用指定策略分发事件
	DispatchWithStrategy(ctx context.Context, event Event, strategy EventDispatchStrategy) error

	// SetDefaultStrategy 设置默认分发策略
	SetDefaultStrategy(strategy EventDispatchStrategy)

	// GetDefaultStrategy 获取默认分发策略
	GetDefaultStrategy() EventDispatchStrategy
} 