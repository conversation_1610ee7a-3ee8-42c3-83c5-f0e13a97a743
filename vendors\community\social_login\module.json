{"name": "social_login", "version": "1.2.0", "description": "Social media login integration for GACMS - supports Google, Facebook, GitHub, and more", "author": "Community Developer", "vendor": "community", "homepage": "https://github.com/community-dev/gacms-social-login", "repository": "https://github.com/community-dev/gacms-social-login", "license": "Apache-2.0", "dependencies": ["user"], "conflicts": [], "min_gacms_version": "1.0.0", "max_gacms_version": "2.0.0", "permissions": [{"name": "social_login:manage", "description": "Manage social login providers and settings", "category": "authentication"}, {"name": "social_login:view_stats", "description": "View social login statistics", "category": "authentication"}], "routes": [{"path": "/auth/google", "method": "GET", "controller": "SocialAuthController", "action": "googleAuth", "middlewares": ["cors"]}, {"path": "/auth/google/callback", "method": "GET", "controller": "SocialAuthController", "action": "googleCallback", "middlewares": ["cors"]}, {"path": "/auth/facebook", "method": "GET", "controller": "SocialAuthController", "action": "facebookAuth", "middlewares": ["cors"]}, {"path": "/auth/facebook/callback", "method": "GET", "controller": "SocialAuthController", "action": "facebookCallback", "middlewares": ["cors"]}, {"path": "/auth/github", "method": "GET", "controller": "SocialAuthController", "action": "githubAuth", "middlewares": ["cors"]}, {"path": "/auth/github/callback", "method": "GET", "controller": "SocialAuthController", "action": "githubCallback", "middlewares": ["cors"]}, {"path": "/admin/social-login/settings", "method": "GET", "controller": "SocialLoginAdminController", "action": "settings", "permission": "social_login:manage", "middlewares": ["auth", "permission"]}, {"path": "/admin/social-login/settings", "method": "POST", "controller": "SocialLoginAdminController", "action": "updateSettings", "permission": "social_login:manage", "middlewares": ["auth", "permission"]}], "events": {"publishes": ["social_login.user_authenticated", "social_login.provider_linked", "social_login.provider_unlinked"], "listens": [{"event": "user.created", "handler": "SocialLoginUserHandler", "priority": 100}]}, "config": {"enabled_providers": ["google", "facebook"], "auto_create_users": true, "link_existing_accounts": true, "require_email_verification": false}, "settings": [{"key": "google_client_id", "type": "string", "default": "", "required": false, "description": "Google OAuth Client ID"}, {"key": "google_client_secret", "type": "password", "default": "", "required": false, "description": "Google OAuth Client Secret"}, {"key": "facebook_app_id", "type": "string", "default": "", "required": false, "description": "Facebook App ID"}, {"key": "facebook_app_secret", "type": "password", "default": "", "required": false, "description": "Facebook App Secret"}, {"key": "github_client_id", "type": "string", "default": "", "required": false, "description": "GitHub OAuth App Client ID"}, {"key": "github_client_secret", "type": "password", "default": "", "required": false, "description": "GitHub OAuth App Client Secret"}, {"key": "auto_create_users", "type": "boolean", "default": true, "required": false, "description": "Automatically create user accounts for new social logins"}, {"key": "default_user_role", "type": "select", "default": "user", "required": true, "description": "Default role for auto-created users", "options": ["user", "subscriber", "contributor"]}], "entry_point": "main.go", "assets": ["assets/css/social-login.css", "assets/js/social-login.js", "assets/images/providers/"], "templates": ["templates/login-buttons.html", "templates/admin-settings.html"], "enabled": true}