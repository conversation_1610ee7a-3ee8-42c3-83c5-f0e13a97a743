/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2024-07-16
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2024-07-16
 * @FilePath: internal/modules/user/infrastructure/repository/GormMemberRepository.go
 * @Description: GORM implementation of the MemberRepository interface.
 *
 * © 2024 GACMS. All rights reserved.
 */

package repository

import (
	"gacms/internal/modules/user/domain/contract"
	"gacms/internal/modules/user/domain/model"
	"gacms/pkg/database"
	"gacms/pkg/logger"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type GormMemberRepository struct {
	db *gorm.DB
	log *logger.Logger
}

func NewGormMemberRepository(db *gorm.DB, log *logger.Logger) contract.MemberRepository {
	return &GormMemberRepository{db: db, log: log}
}

func (r *GormMemberRepository) Create(ctx *gin.Context, member *model.Member) error {
	return r.db.WithContext(ctx).Create(member).Error
}

func (r *GormMemberRepository) GetByUsername(ctx *gin.Context, siteID uint64, username string) (*model.Member, error) {
	var member model.Member
	err := r.db.WithContext(ctx).Where("site_id = ? AND username = ?", siteID, username).First(&member).Error
	if err != nil {
		return nil, err
	}
	return &member, nil
}

func (r *GormMemberRepository) GetByEmail(ctx *gin.Context, siteID uint64, email string) (*model.Member, error) {
	var member model.Member
	err := r.db.WithContext(ctx).Where("site_id = ? AND email = ?", siteID, email).First(&member).Error
	if err != nil {
		return nil, err
	}
	return &member, nil
}

func (r *GormMemberRepository) GetByPhone(ctx *gin.Context, siteID uint64, phone string) (*model.Member, error) {
	var member model.Member
	err := r.db.WithContext(ctx).Where("site_id = ? AND phone = ?", siteID, phone).First(&member).Error
	if err != nil {
		return nil, err
	}
	return &member, nil
}

func (r *GormMemberRepository) GetByID(ctx *gin.Context, id uint64) (*model.Member, error) {
	var member model.Member
	err := r.db.WithContext(ctx).First(&member, id).Error
	if err != nil {
		return nil, err
	}
	return &member, nil
}

func (r *GormMemberRepository) Update(ctx *gin.Context, member *model.Member) error {
	return r.db.WithContext(ctx).Save(member).Error
}

func (r *GormMemberRepository) Delete(ctx *gin.Context, id uint64) error {
	return r.db.WithContext(ctx).Delete(&model.Member{}, id).Error
}

func (r *GormMemberRepository) List(ctx *gin.Context, siteID uint64, options *database.ListOptions) ([]*model.Member, int64, error) {
	var members []*model.Member
	var total int64

	query := r.db.WithContext(ctx).Model(&model.Member{}).Where("site_id = ?", siteID)
	
	if options.Search != "" {
		searchPattern := "%" + options.Search + "%"
		query = query.Where("username LIKE ? OR nickname LIKE ? OR email LIKE ? OR phone LIKE ?", searchPattern, searchPattern, searchPattern, searchPattern)
	}
	
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if options.SortBy != "" {
		order := options.SortBy
		if options.SortDesc {
			order += " desc"
		}
		query = query.Order(order)
	}

	query = query.Limit(options.PageSize).Offset((options.Page - 1) * options.PageSize)

	if err := query.Find(&members).Error; err != nil {
		return nil, 0, err
	}

	return members, total, nil
}

func (r *GormMemberRepository) CreateSocialIdentity(ctx *gin.Context, identity *model.SocialIdentity) error {
	return r.db.WithContext(ctx).Create(identity).Error
}

func (r *GormMemberRepository) GetSocialIdentity(ctx *gin.Context, provider string, openID string) (*model.SocialIdentity, error) {
	var identity model.SocialIdentity
	err := r.db.WithContext(ctx).Where("provider = ? AND open_id = ?", provider, openID).First(&identity).Error
	if err != nil {
		return nil, err
	}
	return &identity, nil
}

func (r *GormMemberRepository) GetSocialIdentitiesByMemberID(ctx *gin.Context, memberID uint64) ([]*model.SocialIdentity, error) {
	var identities []*model.SocialIdentity
	err := r.db.WithContext(ctx).Where("member_id = ?", memberID).Find(&identities).Error
	if err != nil {
		return nil, err
	}
	return identities, nil
} 