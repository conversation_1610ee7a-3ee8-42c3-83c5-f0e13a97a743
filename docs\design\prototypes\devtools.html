<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 开发者工具</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .log-viewer {
            max-height: 400px;
            overflow-y: auto;
            background-color: #1e1e1e;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #555;
            font-family: 'Courier New', Courier, monospace;
            font-size: 0.85rem;
            white-space: pre-wrap;
            word-break: break-all;
        }
        
        .log-line {
            padding: 2px 0;
        }
        
        .log-line.error { color: #f87171; }
        .log-line.warning { color: #facc15; }
        .log-line.info { color: #34d399; }
        .log-line.debug { color: #9ca3af; }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h2 class="text-xl font-bold text-white relative pl-3 section-title">开发者工具</h2>
                <p class="mt-4 text-gray-400">这些工具仅供系统开发人员使用，提供了查看系统日志、执行数据库查询、测试API接口和管理缓存的功能。</p>
            </div>
            
            <!-- 标签页导航 -->
            <div class="border-b border-gray-700">
                <ul class="flex flex-wrap -mb-px" role="tablist">
                    <li class="mr-2" role="presentation">
                        <button class="inline-block px-4 py-2 border-b-2 border-blue-500 text-blue-400 font-medium" role="tab" aria-selected="true" data-target="systemLogs">系统日志</button>
                    </li>
                    <li class="mr-2" role="presentation">
                        <button class="inline-block px-4 py-2 border-b-2 border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-400 font-medium" role="tab" aria-selected="false" data-target="dbQuery">数据库查询</button>
                    </li>
                    <li class="mr-2" role="presentation">
                        <button class="inline-block px-4 py-2 border-b-2 border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-400 font-medium" role="tab" aria-selected="false" data-target="apiTester">API 测试器</button>
                    </li>
                    <li class="mr-2" role="presentation">
                        <button class="inline-block px-4 py-2 border-b-2 border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-400 font-medium" role="tab" aria-selected="false" data-target="cacheManager">缓存管理</button>
                    </li>
                </ul>
            </div>
            
            <!-- 标签页内容 -->
            <div class="my-6">
                <!-- 系统日志 -->
                <div id="systemLogs" class="tab-content active">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <h3 class="text-lg font-semibold mb-4">系统日志查看器</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-4">
                            <div>
                                <label class="block text-gray-300 mb-2">日志级别:</label>
                                <select id="logLevel" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="all">全部</option>
                                    <option value="error">错误 (Error)</option>
                                    <option value="warning">警告 (Warning)</option>
                                    <option value="info">信息 (Info)</option>
                                    <option value="debug">调试 (Debug)</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">日志文件:</label>
                                <select id="logFile" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="system.log">system.log</option>
                                    <option value="error.log">error.log</option>
                                    <option value="access.log">access.log</option>
                                    <option value="slowquery.log">slowquery.log</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">显示行数:</label>
                                <select id="logLines" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="50">50 行</option>
                                    <option value="100">100 行</option>
                                    <option value="500">500 行</option>
                                    <option value="1000">1000 行</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">操作:</label>
                                <div class="flex gap-2">
                                    <button id="refreshLog" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors flex-1 flex items-center justify-center">
                                        <i class="fas fa-sync-alt mr-2"></i> 刷新
                                    </button>
                                    <button id="downloadLog" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors flex-1 flex items-center justify-center">
                                        <i class="fas fa-download mr-2"></i> 下载
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="log-viewer mb-4">
                            <div class="log-line info">[2025-03-10 08:15:23] [INFO] 系统启动成功</div>
                            <div class="log-line info">[2025-03-10 08:15:24] [INFO] 加载模块: 用户管理</div>
                            <div class="log-line info">[2025-03-10 08:15:24] [INFO] 加载模块: 内容管理</div>
                            <div class="log-line warning">[2025-03-10 08:15:25] [WARNING] 缓存目录权限检查: /cache 目录权限不足</div>
                            <div class="log-line info">[2025-03-10 08:15:26] [INFO] 自动创建缓存目录: /tmp/cache</div>
                            <div class="log-line error">[2025-03-10 08:16:32] [ERROR] 数据库连接失败: Connection refused</div>
                            <div class="log-line info">[2025-03-10 08:16:35] [INFO] 数据库连接重试 (1/3)</div>
                            <div class="log-line info">[2025-03-10 08:16:38] [INFO] 数据库连接成功</div>
                            <div class="log-line debug">[2025-03-10 08:16:39] [DEBUG] SQL查询: SELECT * FROM users LIMIT 10</div>
                            <div class="log-line debug">[2025-03-10 08:16:39] [DEBUG] 查询结果: 10条记录</div>
                            <div class="log-line info">[2025-03-10 08:17:44] [INFO] 用户登录: admin (IP: *************)</div>
                            <div class="log-line warning">[2025-03-10 08:23:12] [WARNING] 用户尝试访问未授权页面: /admin/settings</div>
                            <div class="log-line error">[2025-03-10 08:25:17] [ERROR] 文件上传失败: 磁盘空间不足</div>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-gray-400">
                                显示 13 条记录，共 1,245 条
                            </div>
                            <div class="flex gap-2">
                                <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded">1</button>
                                <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded">2</button>
                                <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded">3</button>
                                <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 数据库查询 -->
                <div id="dbQuery" class="tab-content hidden">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <h3 class="text-lg font-semibold mb-4">数据库查询工具</h3>
                        
                        <div class="mb-6">
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">数据库连接:</label>
                                <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option>主数据库 (MySQL)</option>
                                    <option>只读副本 (MySQL)</option>
                                    <option>缓存数据库 (Redis)</option>
                                    <option>统计数据库 (MongoDB)</option>
                                </select>
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">SQL 查询:</label>
                                <textarea class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-y font-mono" rows="6">SELECT * FROM users WHERE created_at >= '2025-01-01' ORDER BY id DESC LIMIT 10;</textarea>
                            </div>
                            
                            <div class="flex justify-between">
                                <div>
                                    <button class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors mr-2">
                                        <i class="fas fa-save mr-1"></i> 保存查询
                                    </button>
                                    <button class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                                        <i class="fas fa-folder-open mr-1"></i> 加载查询
                                    </button>
                                </div>
                                <div>
                                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                                        <i class="fas fa-play mr-1"></i> 执行查询
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <h4 class="font-medium mb-3 text-white">查询结果</h4>
                            <div class="overflow-x-auto">
                                <table class="w-full text-left border-collapse">
                                    <thead>
                                        <tr class="bg-gray-800">
                                            <th class="p-3 border-b border-gray-700">id</th>
                                            <th class="p-3 border-b border-gray-700">username</th>
                                            <th class="p-3 border-b border-gray-700">email</th>
                                            <th class="p-3 border-b border-gray-700">role</th>
                                            <th class="p-3 border-b border-gray-700">status</th>
                                            <th class="p-3 border-b border-gray-700">created_at</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="hover:bg-gray-800/20">
                                            <td class="p-3 border-b border-gray-700">10</td>
                                            <td class="p-3 border-b border-gray-700">admin</td>
                                            <td class="p-3 border-b border-gray-700"><EMAIL></td>
                                            <td class="p-3 border-b border-gray-700">administrator</td>
                                            <td class="p-3 border-b border-gray-700">active</td>
                                            <td class="p-3 border-b border-gray-700">2025-03-05 14:22:18</td>
                                        </tr>
                                        <tr class="hover:bg-gray-800/20">
                                            <td class="p-3 border-b border-gray-700">9</td>
                                            <td class="p-3 border-b border-gray-700">editor</td>
                                            <td class="p-3 border-b border-gray-700"><EMAIL></td>
                                            <td class="p-3 border-b border-gray-700">editor</td>
                                            <td class="p-3 border-b border-gray-700">active</td>
                                            <td class="p-3 border-b border-gray-700">2025-03-01 09:15:42</td>
                                        </tr>
                                        <tr class="hover:bg-gray-800/20">
                                            <td class="p-3 border-b border-gray-700">8</td>
                                            <td class="p-3 border-b border-gray-700">user1</td>
                                            <td class="p-3 border-b border-gray-700"><EMAIL></td>
                                            <td class="p-3 border-b border-gray-700">user</td>
                                            <td class="p-3 border-b border-gray-700">active</td>
                                            <td class="p-3 border-b border-gray-700">2025-02-28 11:32:07</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-sm text-gray-400 mt-3">
                                查询用时: 0.0045 秒 | 返回 3 条记录
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="font-medium mb-3 text-white">查询历史</h4>
                            <ul class="space-y-2">
                                <li class="flex justify-between items-center bg-gray-800/20 p-2 rounded-lg hover:bg-gray-800/30">
                                    <div class="text-sm text-gray-300 truncate">SELECT * FROM users WHERE created_at &gt;= '2025-01-01'</div>
                                    <div class="text-xs text-gray-400">10分钟前</div>
                                </li>
                                <li class="flex justify-between items-center bg-gray-800/20 p-2 rounded-lg hover:bg-gray-800/30">
                                    <div class="text-sm text-gray-300 truncate">SELECT * FROM articles WHERE status = 'published'</div>
                                    <div class="text-xs text-gray-400">15分钟前</div>
                                </li>
                                <li class="flex justify-between items-center bg-gray-800/20 p-2 rounded-lg hover:bg-gray-800/30">
                                    <div class="text-sm text-gray-300 truncate">SHOW TABLES</div>
                                    <div class="text-xs text-gray-400">20分钟前</div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- API 测试器 -->
                <div id="apiTester" class="tab-content hidden">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <h3 class="text-lg font-semibold mb-4">API 测试工具</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-gray-300 mb-2">请求方式:</label>
                                    <div class="flex space-x-2">
                                        <button class="px-4 py-2 bg-blue-600 text-white rounded-lg">GET</button>
                                        <button class="px-4 py-2 bg-gray-700 text-gray-300 rounded-lg">POST</button>
                                        <button class="px-4 py-2 bg-gray-700 text-gray-300 rounded-lg">PUT</button>
                                        <button class="px-4 py-2 bg-gray-700 text-gray-300 rounded-lg">DELETE</button>
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-gray-300 mb-2">API 端点:</label>
                                    <input type="text" value="/api/v1/articles" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                
                                <div>
                                    <label class="block text-gray-300 mb-2">认证令牌:</label>
                                    <input type="text" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                
                                <div>
                                    <label class="block text-gray-300 mb-2">请求参数:</label>
                                    <textarea class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-y font-mono" rows="8">{
  "page": 1,
  "limit": 10,
  "status": "published",
  "sort": "created_at",
  "order": "desc"
}</textarea>
                                </div>
                                
                                <div>
                                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                                        <i class="fas fa-paper-plane mr-2"></i>发送请求
                                    </button>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">响应结果:</label>
                                <div class="h-[450px] bg-gray-900 border border-gray-600 rounded-lg p-4 font-mono text-sm overflow-y-auto">
<pre class="text-green-400">{
  "success": true,
  "code": 200,
  "message": "获取文章列表成功",
  "data": {
    "total": 48,
    "page": 1,
    "limit": 10,
    "items": [
      {
        "id": 103,
        "title": "GACMS 3.0 更新指南",
        "slug": "gacms-3-update-guide",
        "author": "admin",
        "category": "技术文档",
        "status": "published",
        "created_at": "2025-03-08T14:22:18Z",
        "updated_at": "2025-03-09T10:15:33Z"
      },
      {
        "id": 102,
        "title": "提高网站性能的10个技巧",
        "slug": "10-tips-improve-website-performance",
        "author": "editor",
        "category": "网站优化",
        "status": "published",
        "created_at": "2025-03-07T09:45:12Z",
        "updated_at": "2025-03-07T09:45:12Z"
      }
      // ... 更多数据
    ]
  }
}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 缓存管理 -->
                <div id="cacheManager" class="tab-content hidden">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <h3 class="text-lg font-semibold mb-4">缓存管理工具</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <div class="bg-gray-800/20 rounded-lg p-4 h-full">
                                    <h4 class="font-medium mb-3 text-white">缓存状态</h4>
                                    <div class="space-y-4">
                                        <div class="flex justify-between">
                                            <span>缓存驱动:</span>
                                            <span class="font-medium">Redis</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>缓存大小:</span>
                                            <span class="font-medium">234 MB</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>键总数:</span>
                                            <span class="font-medium">14,352</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>命中率:</span>
                                            <span class="font-medium text-green-400">92%</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>已运行时间:</span>
                                            <span class="font-medium">3天 14小时</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <div class="bg-gray-800/20 rounded-lg p-4 h-full">
                                    <h4 class="font-medium mb-3 text-white">缓存操作</h4>
                                    <div class="space-y-4">
                                        <button class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg transition-colors flex items-center justify-center">
                                            <i class="fas fa-sync-alt mr-2"></i> 刷新所有缓存
                                        </button>
                                        <button class="w-full bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-3 rounded-lg transition-colors flex items-center justify-center">
                                            <i class="fas fa-trash-alt mr-2"></i> 清除过期缓存
                                        </button>
                                        <button class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-lg transition-colors flex items-center justify-center">
                                            <i class="fas fa-bomb mr-2"></i> 清空所有缓存
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <h4 class="font-medium mb-3 text-white">缓存浏览器</h4>
                            <div class="mb-4">
                                <div class="flex">
                                    <input type="text" placeholder="搜索缓存键..." class="flex-1 bg-gray-700/50 border border-gray-600 rounded-l-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-r-lg transition-colors">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="overflow-x-auto">
                                <table class="w-full text-left border-collapse">
                                    <thead>
                                        <tr class="bg-gray-800">
                                            <th class="p-3 border-b border-gray-700">键名</th>
                                            <th class="p-3 border-b border-gray-700">类型</th>
                                            <th class="p-3 border-b border-gray-700">大小</th>
                                            <th class="p-3 border-b border-gray-700">过期时间</th>
                                            <th class="p-3 border-b border-gray-700">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="hover:bg-gray-800/20">
                                            <td class="p-3 border-b border-gray-700">gacms:articles:list:page1</td>
                                            <td class="p-3 border-b border-gray-700">hash</td>
                                            <td class="p-3 border-b border-gray-700">45.2 KB</td>
                                            <td class="p-3 border-b border-gray-700">1小时后</td>
                                            <td class="p-3 border-b border-gray-700">
                                                <button class="text-blue-400 hover:text-blue-300 mr-2">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="text-red-400 hover:text-red-300">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr class="hover:bg-gray-800/20">
                                            <td class="p-3 border-b border-gray-700">gacms:users:online</td>
                                            <td class="p-3 border-b border-gray-700">set</td>
                                            <td class="p-3 border-b border-gray-700">12.7 KB</td>
                                            <td class="p-3 border-b border-gray-700">30分钟后</td>
                                            <td class="p-3 border-b border-gray-700">
                                                <button class="text-blue-400 hover:text-blue-300 mr-2">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="text-red-400 hover:text-red-300">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr class="hover:bg-gray-800/20">
                                            <td class="p-3 border-b border-gray-700">gacms:config:system</td>
                                            <td class="p-3 border-b border-gray-700">string</td>
                                            <td class="p-3 border-b border-gray-700">8.4 KB</td>
                                            <td class="p-3 border-b border-gray-700">永久</td>
                                            <td class="p-3 border-b border-gray-700">
                                                <button class="text-blue-400 hover:text-blue-300 mr-2">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="text-red-400 hover:text-red-300">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-sm text-gray-400 mt-3">
                                显示 3 条记录，共 14,352 条
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 标签页切换功能
            const tabButtons = document.querySelectorAll('[role="tab"]');
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有标签页的active类
                    tabButtons.forEach(btn => {
                        btn.classList.remove('border-blue-500', 'text-blue-400');
                        btn.classList.add('border-transparent', 'text-gray-400', 'hover:text-gray-300', 'hover:border-gray-400');
                        btn.setAttribute('aria-selected', 'false');
                    });
                    
                    // 添加当前标签页的active类
                    this.classList.remove('border-transparent', 'text-gray-400', 'hover:text-gray-300', 'hover:border-gray-400');
                    this.classList.add('border-blue-500', 'text-blue-400');
                    this.setAttribute('aria-selected', 'true');
                    
                    // 隐藏所有内容
                    const tabContents = document.querySelectorAll('.tab-content');
                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                        content.classList.remove('active');
                    });
                    
                    // 显示对应的内容
                    const targetId = this.getAttribute('data-target');
                    const targetContent = document.getElementById(targetId);
                    if(targetContent) {
                        targetContent.classList.remove('hidden');
                        targetContent.classList.add('active');
                    }
                });
            });
            
            // 刷新日志按钮点击事件
            const refreshLogBtn = document.getElementById('refreshLog');
            if(refreshLogBtn) {
                refreshLogBtn.addEventListener('click', function() {
                    const logLevel = document.getElementById('logLevel').value;
                    const logFile = document.getElementById('logFile').value;
                    const logLines = document.getElementById('logLines').value;
                    
                    console.log(`刷新日志: ${logFile}, 级别: ${logLevel}, 行数: ${logLines}`);
                    // 实际应用中这里会调用API获取日志数据
                });
            }
        });
    </script>
</body>
</html> 