/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/port/http/middleware/URLRewrite.go
 * @Description: URL重写中间件，基于域名绑定配置进行URL重写
 * 
 * © 2025 GACMS. All rights reserved.
 */

package middleware

import (
	"regexp"
	"strings"
	"sync"
	"time"

	"gacms/internal/core/system/model"
	"gacms/internal/core/system/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// URLRewriteMiddleware URL重写中间件
type URLRewriteMiddleware struct {
	bindingService *service.DomainBindingService
	logger         *zap.Logger
	cache          map[string]*CacheEntry // 带过期时间的缓存
	cacheMutex     sync.RWMutex
	cacheTTL       time.Duration
}

// CacheEntry 缓存条目
type CacheEntry struct {
	Binding   *model.DomainBinding
	ExpiresAt time.Time
}

// NewURLRewriteMiddleware 创建URL重写中间件
func NewURLRewriteMiddleware(bindingService *service.DomainBindingService, logger *zap.Logger) *URLRewriteMiddleware {
	middleware := &URLRewriteMiddleware{
		bindingService: bindingService,
		logger:         logger,
		cache:          make(map[string]*CacheEntry),
		cacheTTL:       5 * time.Minute, // 缓存5分钟
	}

	// 启动缓存清理协程
	go middleware.startCacheCleanup()

	return middleware
}

// Handle 处理URL重写
func (m *URLRewriteMiddleware) Handle() gin.HandlerFunc {
	return func(c *gin.Context) {
		originalPath := c.Request.URL.Path
		host := c.Request.Host
		
		// 移除端口号
		if strings.Contains(host, ":") {
			host = strings.Split(host, ":")[0]
		}
		
		m.logger.Debug("URL rewrite middleware processing",
			zap.String("host", host),
			zap.String("original_path", originalPath),
		)
		
		// 获取域名绑定配置
		binding := m.getDomainBinding(host)
		if binding == nil || !binding.URLRewriteEnabled {
			// 没有绑定或未启用URL重写，直接继续
			c.Next()
			return
		}
		
		// 尝试URL重写
		newPath := m.rewriteURL(originalPath, binding)
		if newPath != originalPath {
			m.logger.Debug("URL rewritten",
				zap.String("host", host),
				zap.String("original_path", originalPath),
				zap.String("new_path", newPath),
			)
			
			// 保存原始路径
			c.Set("original_path", originalPath)
			c.Set("rewritten", true)
			c.Set("domain_binding", binding)
			
			// 重写请求路径
			c.Request.URL.Path = newPath
		}
		
		c.Next()
	}
}

// getDomainBinding 获取域名绑定配置（带缓存）
func (m *URLRewriteMiddleware) getDomainBinding(domain string) *model.DomainBinding {
	// 先检查缓存
	m.cacheMutex.RLock()
	if entry, exists := m.cache[domain]; exists {
		if time.Now().Before(entry.ExpiresAt) {
			m.cacheMutex.RUnlock()
			return entry.Binding
		}
		// 缓存已过期，删除
		delete(m.cache, domain)
	}
	m.cacheMutex.RUnlock()

	// 从服务获取
	binding, err := m.bindingService.GetBindingByDomainWithRules(domain)
	if err != nil {
		m.logger.Error("Failed to get domain binding",
			zap.String("domain", domain),
			zap.Error(err),
		)
		return nil
	}

	// 缓存结果（包括nil）
	m.cacheMutex.Lock()
	m.cache[domain] = &CacheEntry{
		Binding:   binding,
		ExpiresAt: time.Now().Add(m.cacheTTL),
	}
	m.cacheMutex.Unlock()

	return binding
}

// rewriteURL 执行URL重写
func (m *URLRewriteMiddleware) rewriteURL(originalPath string, binding *model.DomainBinding) string {
	// 如果没有重写规则，检查是否有默认控制器配置
	if len(binding.URLRules) == 0 {
		return m.applyDefaultRewrite(originalPath, binding)
	}
	
	// 按优先级应用重写规则
	for _, rule := range binding.URLRules {
		if !rule.IsActive {
			continue
		}
		
		newPath := m.applyRewriteRule(originalPath, rule)
		if newPath != originalPath {
			return newPath
		}
	}
	
	// 如果没有规则匹配，应用默认重写
	return m.applyDefaultRewrite(originalPath, binding)
}

// applyRewriteRule 应用单个重写规则
func (m *URLRewriteMiddleware) applyRewriteRule(path string, rule *model.URLRewriteRule) string {
	// 将模式转换为正则表达式
	pattern := m.convertPatternToRegex(rule.Pattern)
	
	regex, err := regexp.Compile(pattern)
	if err != nil {
		m.logger.Error("Invalid regex pattern in URL rule",
			zap.String("pattern", rule.Pattern),
			zap.String("rule_name", rule.RuleName),
			zap.Error(err),
		)
		return path
	}
	
	// 检查是否匹配
	if !regex.MatchString(path) {
		return path
	}
	
	// 执行替换
	result := regex.ReplaceAllString(path, rule.Replacement)
	
	m.logger.Debug("URL rule applied",
		zap.String("rule_name", rule.RuleName),
		zap.String("pattern", rule.Pattern),
		zap.String("replacement", rule.Replacement),
		zap.String("original", path),
		zap.String("result", result),
	)
	
	return result
}

// convertPatternToRegex 将URL模式转换为正则表达式
func (m *URLRewriteMiddleware) convertPatternToRegex(pattern string) string {
	// 转义特殊字符
	escaped := regexp.QuoteMeta(pattern)
	
	// 将 {param} 转换为 ([^/]+)
	escaped = regexp.MustCompile(`\\{[^}]+\\}`).ReplaceAllString(escaped, `([^/]+)`)
	
	// 确保完全匹配
	return "^" + escaped + "$"
}

// applyDefaultRewrite 应用默认重写（基于域名绑定配置）
func (m *URLRewriteMiddleware) applyDefaultRewrite(path string, binding *model.DomainBinding) string {
	// 如果绑定到模块，添加模块前缀
	if binding.BindingType == model.BindingTypeModule && binding.ModuleSlug != nil {
		// 如果路径已经以模块名开头，不需要重写
		modulePrefix := "/" + *binding.ModuleSlug
		if strings.HasPrefix(path, modulePrefix) {
			return path
		}
		
		// 添加模块前缀
		if path == "/" {
			// 根路径，使用默认控制器和方法
			if binding.DefaultController != "" && binding.DefaultAction != "" {
				return modulePrefix + "/" + binding.DefaultController + "/" + binding.DefaultAction
			}
			return modulePrefix
		}
		
		// 其他路径，直接添加模块前缀
		return modulePrefix + path
	}
	
	// 如果绑定到栏目，重写为栏目路径
	if binding.BindingType == model.BindingTypeCategory && binding.CategoryID != nil {
		// TODO: 这里需要根据栏目ID获取栏目信息，然后构建路径
		// 暂时返回原路径
		return path
	}
	
	return path
}

// ClearCache 清除缓存
func (m *URLRewriteMiddleware) ClearCache() {
	m.cache = make(map[string]*model.DomainBinding)
}

// ClearDomainCache 清除特定域名的缓存
func (m *URLRewriteMiddleware) ClearDomainCache(domain string) {
	m.cacheMutex.Lock()
	defer m.cacheMutex.Unlock()
	delete(m.cache, domain)
}

// startCacheCleanup 启动缓存清理协程
func (m *URLRewriteMiddleware) startCacheCleanup() {
	ticker := time.NewTicker(1 * time.Minute) // 每分钟清理一次
	defer ticker.Stop()

	for range ticker.C {
		m.cleanExpiredCache()
	}
}

// cleanExpiredCache 清理过期缓存
func (m *URLRewriteMiddleware) cleanExpiredCache() {
	m.cacheMutex.Lock()
	defer m.cacheMutex.Unlock()

	now := time.Now()
	for domain, entry := range m.cache {
		if now.After(entry.ExpiresAt) {
			delete(m.cache, domain)
		}
	}
}

// GetCacheStats 获取缓存统计信息
func (m *URLRewriteMiddleware) GetCacheStats() map[string]interface{} {
	m.cacheMutex.RLock()
	defer m.cacheMutex.RUnlock()

	total := len(m.cache)
	expired := 0
	now := time.Now()

	for _, entry := range m.cache {
		if now.After(entry.ExpiresAt) {
			expired++
		}
	}

	return map[string]interface{}{
		"total_entries":   total,
		"expired_entries": expired,
		"active_entries":  total - expired,
		"cache_ttl":       m.cacheTTL.String(),
	}
}
