<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 栏目编辑</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .content-section-title::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 5rem;
            height: 3px;
            background: linear-gradient(to right, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            background: linear-gradient(145deg, #1e1e1e, #2a2a2a);
            border-left: 4px solid #007bff;
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            backdrop-filter: blur(20px);
            z-index: 1000;
            transform: translateX(120%);
            opacity: 0;
            transition: all 0.2s ease;
            max-width: 350px;
        }
        
        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <nav class="flex mb-5" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="dashboard.html" class="inline-flex items-center text-sm font-medium text-gray-400 hover:text-white">
                            <i class="fas fa-home mr-2"></i>
                            仪表盘
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-gray-500 mx-2 text-xs"></i>
                            <a href="categorys.html" class="text-sm font-medium text-gray-400 hover:text-white">栏目管理</a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-gray-500 mx-2 text-xs"></i>
                            <span class="text-sm font-medium text-blue-400">编辑栏目</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- 栏目编辑表单 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h2 class="text-xl font-bold text-white relative pl-3 mb-6 section-title">编辑栏目</h2>
                
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- 左侧：主要信息 -->
                    <div class="lg:col-span-2">
                        <!-- 栏目名称和别名 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label for="category_name" class="block text-gray-300 mb-2 font-medium">栏目名称 <span class="text-red-500">*</span></label>
                                <input type="text" id="category_name" name="category_name" value="技术教程" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <p class="text-xs text-gray-500 mt-1">显示在导航和列表中的名称</p>
                            </div>
                            <div>
                                <label for="category_slug" class="block text-gray-300 mb-2 font-medium">栏目别名</label>
                                <input type="text" id="category_slug" name="category_slug" value="tech-tutorials" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <p class="text-xs text-gray-500 mt-1">用于URL，只能包含字母、数字、连字符</p>
                            </div>
                        </div>
                        
                        <!-- 描述 -->
                        <div class="mb-6">
                            <label for="category_description" class="block text-gray-300 mb-2 font-medium">栏目描述</label>
                            <textarea id="category_description" name="category_description" rows="4" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">这个栏目包含各类技术教程，包括前端、后端、移动端开发等内容。</textarea>
                            <p class="text-xs text-gray-500 mt-1">描述会显示在栏目页面，有助于SEO优化</p>
                        </div>
                        
                        <!-- 元标题和元描述 -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-2">
                                <label for="meta_title" class="block text-gray-300 font-medium">SEO 元标题</label>
                                <button type="button" id="advanced_seo_toggle" class="text-xs text-blue-400 hover:underline flex items-center">
                                    <span>高级SEO设置</span>
                                    <i class="fas fa-chevron-down ml-1"></i>
                                </button>
                            </div>
                            <input type="text" id="meta_title" name="meta_title" value="技术教程 | GACMS官方网站" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div class="mb-6">
                            <label for="meta_description" class="block text-gray-300 mb-2 font-medium">SEO 元描述</label>
                            <textarea id="meta_description" name="meta_description" rows="3" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">探索GACMS提供的全面技术教程，包含Web开发、移动应用、服务器配置等专业知识，助您快速掌握各类技术。</textarea>
                        </div>
                        
                        <!-- 高级SEO设置（默认隐藏） -->
                        <div id="advanced_seo_section" class="hidden bg-gray-800/20 border border-gray-700 rounded-lg p-5 mb-6">
                            <h3 class="text-lg font-semibold text-white mb-4">高级SEO设置</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label for="canonical_url" class="block text-gray-300 mb-2 font-medium">规范链接</label>
                                    <input type="text" id="canonical_url" name="canonical_url" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label for="focus_keyword" class="block text-gray-300 mb-2 font-medium">焦点关键词</label>
                                    <input type="text" id="focus_keyword" name="focus_keyword" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                            </div>
                            <div class="mb-4">
                                <label for="meta_keywords" class="block text-gray-300 mb-2 font-medium">元关键词</label>
                                <textarea id="meta_keywords" name="meta_keywords" rows="2" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">技术教程,GACMS教程,Web开发,前端开发,后端开发,移动开发</textarea>
                                <p class="text-xs text-gray-500 mt-1">用逗号分隔多个关键词</p>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="robots_meta" class="block text-gray-300 mb-2 font-medium">Robots 元标记</label>
                                    <select id="robots_meta" name="robots_meta" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="index,follow" selected>index, follow</option>
                                        <option value="noindex,follow">noindex, follow</option>
                                        <option value="index,nofollow">index, nofollow</option>
                                        <option value="noindex,nofollow">noindex, nofollow</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="og_type" class="block text-gray-300 mb-2 font-medium">Open Graph 类型</label>
                                    <select id="og_type" name="og_type" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="article" selected>article</option>
                                        <option value="website">website</option>
                                        <option value="product">product</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 按钮组 -->
                        <div class="flex space-x-4">
                            <button type="submit" class="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg font-medium hover:shadow-lg hover:shadow-blue-500/30 transition-all relative overflow-hidden action-button">
                                保存栏目
                            </button>
                            <a href="categorys.html" class="px-6 py-3 bg-gray-700 text-gray-300 rounded-lg font-medium hover:bg-gray-600 transition-all flex items-center">
                                返回列表
                            </a>
                        </div>
                    </div>
                    
                    <!-- 右侧：附加设置 -->
                    <div class="lg:col-span-1">
                        <!-- 父栏目选择 -->
                        <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5 mb-6">
                            <h3 class="font-medium text-white mb-4">栏目设置</h3>
                            
                            <div class="mb-4">
                                <label for="parent_category" class="block text-gray-300 mb-2 font-medium">父级栏目</label>
                                <select id="parent_category" name="parent_category" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="0">-- 顶级栏目 --</option>
                                    <option value="1">新闻中心</option>
                                    <option value="2" selected>技术分享</option>
                                    <option value="3">产品中心</option>
                                    <option value="4">关于我们</option>
                                </select>
                                <p class="text-xs text-gray-500 mt-1">选择此栏目的父级栏目</p>
                            </div>
                            
                            <div class="mb-4">
                                <label for="category_sort" class="block text-gray-300 mb-2 font-medium">排序</label>
                                <input type="number" id="category_sort" name="category_sort" value="10" min="0" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <p class="text-xs text-gray-500 mt-1">值越小，排序越靠前</p>
                            </div>
                        </div>
                        
                        <!-- 显示设置 -->
                        <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5 mb-6">
                            <h3 class="font-medium text-white mb-4">显示设置</h3>
                            
                            <div class="mb-4">
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" name="show_in_menu" checked class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-0 focus:ring-offset-0">
                                    <span class="ml-2 text-gray-300">在导航菜单中显示</span>
                                </label>
                            </div>
                            
                            <div class="mb-4">
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" name="allow_comment" checked class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-0 focus:ring-offset-0">
                                    <span class="ml-2 text-gray-300">允许评论</span>
                                </label>
                            </div>
                            
                            <div class="mb-4">
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" name="need_approval" class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-0 focus:ring-offset-0">
                                    <span class="ml-2 text-gray-300">内容需要审核</span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- 模板设置 -->
                        <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5 mb-6">
                            <h3 class="font-medium text-white mb-4">模板设置</h3>
                            
                            <div class="mb-4">
                                <label for="list_template" class="block text-gray-300 mb-2 font-medium">列表页模板</label>
                                <select id="list_template" name="list_template" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="default">默认模板</option>
                                    <option value="grid" selected>网格模板</option>
                                    <option value="list">列表模板</option>
                                    <option value="masonry">瀑布流模板</option>
                                </select>
                            </div>
                            
                            <div class="mb-4">
                                <label for="content_template" class="block text-gray-300 mb-2 font-medium">内容页模板</label>
                                <select id="content_template" name="content_template" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="default">默认模板</option>
                                    <option value="tutorial" selected>教程模板</option>
                                    <option value="article">文章模板</option>
                                    <option value="product">产品模板</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 特色图像 -->
                        <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5 mb-6">
                            <h3 class="font-medium text-white mb-4">特色图像</h3>
                            
                            <div class="text-center p-4 border-2 border-dashed border-gray-600 rounded-lg">
                                <img id="featured_image_preview" src="https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80" alt="栏目图片" class="mx-auto mb-4 rounded-lg max-h-40">
                                
                                <label for="featured_image" class="px-4 py-2 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-all inline-flex items-center cursor-pointer">
                                    <i class="fas fa-upload mr-2"></i>
                                    更改图片
                                    <input type="file" id="featured_image" name="featured_image" accept="image/*" class="hidden">
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            
            // 高级SEO设置切换
            const advancedSeoToggle = document.getElementById('advanced_seo_toggle');
            const advancedSeoSection = document.getElementById('advanced_seo_section');
            
            if(advancedSeoToggle && advancedSeoSection) {
                advancedSeoToggle.addEventListener('click', function() {
                    advancedSeoSection.classList.toggle('hidden');
                    const icon = this.querySelector('i');
                    if(advancedSeoSection.classList.contains('hidden')) {
                        icon.classList.replace('fa-chevron-up', 'fa-chevron-down');
                    } else {
                        icon.classList.replace('fa-chevron-down', 'fa-chevron-up');
                    }
                });
            }
            
            // 特色图片预览
            const featuredImageInput = document.getElementById('featured_image');
            const featuredImagePreview = document.getElementById('featured_image_preview');
            
            if(featuredImageInput && featuredImagePreview) {
                featuredImageInput.addEventListener('change', function(event) {
                    const file = event.target.files[0];
                    if(file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            featuredImagePreview.src = e.target.result;
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }
        });
    </script>
</body>
</html> 