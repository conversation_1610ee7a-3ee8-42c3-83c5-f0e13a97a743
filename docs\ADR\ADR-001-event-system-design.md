# ADR-001: 事件驱动架构设计与实现

## 状态
已接受

## 上下文
GACMS采用微核心+独立模块架构，需要一种机制使模块之间能够松耦合地通信，同时保持系统的可扩展性和可维护性。事件驱动架构是实现这一目标的理想选择，它允许模块通过发布和订阅事件进行通信，而不需要直接依赖彼此。

当前系统面临的挑战：
- 模块间存在紧耦合，导致变更困难
- 缺乏标准化的通信机制
- 需要支持异步处理流程
- 需要与fx依赖注入框架无缝集成

## 决策
我们决定实现一个完整的事件系统，基于以下原则和架构：

### 1. 核心组件

1. **Event接口**：所有事件的基础接口
   - 包含上下文、名称、载荷和元数据等属性
   - 提供BaseEvent作为基础实现

2. **EventHandler接口**：事件处理器接口
   - 支持同步和异步处理
   - 支持优先级和重试机制

3. **EventBus接口**：事件总线接口
   - 负责事件的注册和分发
   - 支持多种分发策略

4. **EventManager接口**：事件管理器接口
   - 作为系统入口点
   - 封装EventBus并提供友好API

5. **EventStore接口**：事件存储接口
   - 支持事件溯源模式
   - 提供事件的持久化和检索

6. **EventSerializer接口**：事件序列化接口
   - 支持多种序列化格式
   - 用于事件的持久化和传输

7. **EventDispatcher接口**：事件分发器接口
   - 负责将事件路由到处理器
   - 支持多种分发策略

### 2. 通信流程

1. **事件发布流程**：
   - 模块创建事件并通过EventManager发布
   - EventManager将事件转发给EventBus
   - EventBus查找注册的处理器
   - EventDispatcher按策略分发事件给处理器
   - 处理器执行业务逻辑

2. **事件订阅流程**：
   - 模块实现EventHandler接口
   - 通过fx声明式配置注册处理器
   - 系统启动时自动发现并注册处理器

### 3. 与fx集成

1. **依赖注入方式**：
   - 核心服务通过接口注入到模块
   - 模块通过构造函数声明依赖
   - fx容器负责解析依赖关系

2. **声明式配置**：
   - 模块通过fx.Provide提供处理器
   - 通过fx.Invoke注册处理器
   - 支持分组和自动发现

### 4. 核心服务调用原则

1. **需要通过公共库接口调用的场景**：
   - 核心基础设施服务（数据库、缓存、日志、配置等）
   - 跨模块通信（如事件系统）
   - 需要延迟加载的服务

2. **直接由fx注入的场景**：
   - 模块内部组件之间的通信
   - 工具类和辅助函数
   - 不需要延迟加载的服务

### 5. 事件序列化策略

1. **序列化的必要性**：
   - 持久化需求：事件溯源模式需要将事件存储到数据库
   - 跨进程通信：分布式系统中事件需通过消息队列传递
   - 版本兼容性：序列化格式可包含版本信息，便于系统升级时处理历史事件
   - 性能优化：某些序列化格式在传输和存储上更高效

2. **灵活选择**：
   - 同进程内可直接传递对象
   - 跨进程或需持久化时使用序列化

## 替代方案

### 方案1：直接使用消息队列
- **优点**：成熟的中间件，功能丰富
- **缺点**：引入外部依赖，增加复杂性，不适合单体应用

### 方案2：简单的观察者模式
- **优点**：简单易实现
- **缺点**：缺乏高级特性，如异步处理、事件存储等

### 方案3：使用第三方事件库
- **优点**：减少开发工作
- **缺点**：可能与系统架构不匹配，定制困难

## 影响

### 积极影响
- 模块间完全解耦，提高系统可维护性
- 标准化的通信机制，简化开发
- 支持异步处理，提高系统响应性
- 与fx无缝集成，保持一致的编程模型
- 支持事件溯源，便于审计和调试

### 消极影响
- 增加一定的学习成本
- 可能引入一些性能开销
- 调试复杂性增加（事件追踪）

### 中性影响
- 需要更多的接口和抽象
- 代码量增加

## 实施注意事项

1. **技术细节**：
   - 使用Go的channel和goroutine实现异步处理
   - 使用反射进行事件类型匹配
   - 使用接口隔离原则设计接口

2. **集成点**：
   - 与数据库集成，实现事件持久化
   - 与日志系统集成，记录事件流
   - 与监控系统集成，监控事件处理性能

3. **迁移路径**：
   - 先实现核心接口
   - 逐步将现有通信机制迁移到事件系统
   - 为旧代码提供适配器

4. **成功指标**：
   - 模块间耦合度降低
   - 系统可扩展性提高
   - 开发效率提升

5. **复审日期**：
   - 6个月后评估系统性能和开发体验

## 参考资料
- Martin Fowler的《企业应用架构模式》
- CQRS和事件溯源模式
- Go语言并发编程实践
- Uber的fx依赖注入框架文档 