/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/user/port/http/controller/UserController.go
 * @Description: Controller for user and admin operations, designed for convention-based routing.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package controller

import (
	"gacms/internal/modules/user/application/dto"
	"gacms/internal/modules/user/application/service"
	"gacms/internal/port/http/response"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"net/http"
	"strconv"
)

type UserController struct {
	adminSvc *service.AdminService
	userSvc  *service.UserService
}

func NewUserController(adminSvc *service.AdminService, userSvc *service.UserService) *UserController {
	return &UserController{
		adminSvc: adminSvc,
		userSvc:  userSvc,
	}
}

// <PERSON><PERSON> handles admin login requests.
// @Summary Admin login
// @Description Authenticates an administrator for a specific site and returns a JWT.
// @Tags Auth
// @Accept  json
// @Produce  json
// @Param   login  body   dto.AdminLoginDTO  true  "Login Credentials"
// @Success 200 {object} response.SuccessResponse{data=string} "Returns the JWT token"
// @Failure 400 {object} response.ErrorResponse "Invalid input"
// @Failure 401 {object} response.ErrorResponse "Authentication failed"
// @Failure 500 {object} response.ErrorResponse "Server error"
// @Router /api/user/admin/login [post]
func (c *UserController) Login(ctx *gin.Context) {
	var input dto.AdminLoginDTO
	if err := ctx.ShouldBindJSON(&input); err != nil {
		response.Fail(ctx, http.StatusBadRequest, err.Error())
		return
	}

	token, err := c.adminSvc.Login(ctx, &input)
	if err != nil {
		// Differentiate between auth failure and other errors
		if err.Error() == "authentication failed" || err.Error() == "user not authorized for this site" {
			response.Fail(ctx, http.StatusUnauthorized, err.Error())
		} else {
			response.Fail(ctx, http.StatusInternalServerError, err.Error())
		}
		return
	}

	response.Success(ctx, gin.H{"token": token})
}

// --- Admin Management Methods ---

func (c *UserController) GetAdmins(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	admins, total, err := c.adminSvc.ListAdmins(ctx, page, pageSize)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	response.Page(ctx, admins, total)
}

func (c *UserController) GetAdmin(ctx *gin.Context) {
	id, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "Invalid admin ID")
		return
	}
	admin, err := c.adminSvc.GetAdminByID(ctx, id)
	if err != nil {
		response.Fail(ctx, http.StatusNotFound, "Admin not found")
		return
	}
	response.Success(ctx, admin)
}

func (c *UserController) PostAdmin(ctx *gin.Context) {
	var input dto.CreateAdminDTO
	if err := ctx.ShouldBindJSON(&input); err != nil {
		response.Fail(ctx, http.StatusBadRequest, err.Error())
		return
	}
	admin, err := c.adminSvc.CreateAdmin(ctx, &input)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	response.Created(ctx, admin)
}

func (c *UserController) PutAdmin(ctx *gin.Context) {
	id, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "Invalid admin ID")
		return
	}
	var input dto.UpdateAdminDTO
	if err := ctx.ShouldBindJSON(&input); err != nil {
		response.Fail(ctx, http.StatusBadRequest, err.Error())
		return
	}
	admin, err := c.adminSvc.UpdateAdmin(ctx, id, &input)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	response.Success(ctx, admin)
}

func (c *UserController) DeleteAdmin(ctx *gin.Context) {
	id, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "Invalid admin ID")
		return
	}
	if err := c.adminSvc.DeleteAdmin(ctx, id); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	response.Success(ctx, nil)
}

// --- Role Management Methods ---

func (c *UserController) GetRoles(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "10"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	roles, total, err := c.adminSvc.ListRoles(ctx, page, pageSize)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	response.Page(ctx, roles, total)
}

// ... other role and permission methods would follow the same pattern ... 