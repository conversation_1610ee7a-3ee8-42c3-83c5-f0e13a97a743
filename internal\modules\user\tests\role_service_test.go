/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/user/tests/role_service_test.go
 * @Description: RoleService单元测试
 * 
 * © 2025 GACMS. All rights reserved.
 */
package tests

import (
	"context"
	"errors"
	"gacms/internal/modules/user/application/service"
	"gacms/internal/modules/user/domain/model"
	"gacms/pkg/contract"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// 测试用例

func TestRoleService_CreateRole(t *testing.T) {
	// 准备模拟对象
	mockRoleRepo := new(MockRoleRepository)
	mockPermRepo := new(MockPermissionRepository)
	mockLogger := new(MockLogger)
	mockEventManager := new(MockEventManager)

	// 创建服务实例
	roleService := service.NewRoleService(
		mockRoleRepo,
		mockPerm<PERSON><PERSON><PERSON>,
		mockLogger,
		mockEventManager,
	)

	// 测试场景: 成功创建角色
	t.Run("CreateRole_Success", func(t *testing.T) {
		// 准备测试数据
		role := &model.Role{
			Name:        "Editor",
			Description: "Content editor role",
			Type:        model.AdminRole,
			Level:       2,
		}

		// 模拟仓库方法
		mockRoleRepo.On("Create", mock.Anything, role).Run(func(args mock.Arguments) {
			r := args.Get(1).(*model.Role)
			r.ID = 1 // 设置ID模拟数据库自增
		}).Return(nil).Once()

		// 模拟事件分发
		mockEventManager.On("Dispatch", mock.AnythingOfType("*events.RoleCreatedEvent")).Return().Once()

		// 执行测试
		err := roleService.CreateRole(context.Background(), role, 1, 1)

		// 验证结果
		assert.NoError(t, err)
		assert.Equal(t, uint(1), role.ID)
		mockRoleRepo.AssertExpectations(t)
		mockEventManager.AssertExpectations(t)
	})

	// 测试场景: 创建角色失败
	t.Run("CreateRole_Failure", func(t *testing.T) {
		// 准备测试数据
		role := &model.Role{
			Name:        "Admin",
			Description: "Administrator role",
			Type:        model.AdminRole,
			Level:       1,
		}

		// 模拟仓库方法返回错误
		expectedErr := errors.New("database error")
		mockRoleRepo.On("Create", mock.Anything, role).Return(expectedErr).Once()

		// 模拟日志记录
		mockLogger.On("Error", mock.Anything, "Failed to create role", "error", expectedErr).Return().Once()

		// 执行测试
		err := roleService.CreateRole(context.Background(), role, 1, 1)

		// 验证结果
		assert.Error(t, err)
		assert.Equal(t, expectedErr, err)
		mockRoleRepo.AssertExpectations(t)
		mockLogger.AssertExpectations(t)
		// 确保事件不会被分发
		mockEventManager.AssertNotCalled(t, "Dispatch", mock.Anything)
	})
}

func TestRoleService_UpdateRole(t *testing.T) {
	// 准备模拟对象
	mockRoleRepo := new(MockRoleRepository)
	mockPermRepo := new(MockPermissionRepository)
	mockLogger := new(MockLogger)
	mockEventManager := new(MockEventManager)

	// 创建服务实例
	roleService := service.NewRoleService(
		mockRoleRepo,
		mockPermRepo,
		mockLogger,
		mockEventManager,
	)

	// 测试场景: 成功更新角色
	t.Run("UpdateRole_Success", func(t *testing.T) {
		// 准备测试数据
		existingRole := &model.Role{
			ID:          1,
			Name:        "Editor",
			Description: "Content editor role",
			Type:        model.AdminRole,
			Level:       2,
		}

		updatedRole := &model.Role{
			ID:          1,
			Name:        "Senior Editor",
			Description: "Senior content editor role",
			Type:        model.AdminRole,
			Level:       3,
		}

		// 模拟获取旧角色
		mockRoleRepo.On("FindByID", mock.Anything, uint(1)).Return(existingRole, nil).Once()

		// 模拟更新角色
		mockRoleRepo.On("Update", mock.Anything, updatedRole).Return(nil).Once()

		// 模拟事件分发
		mockEventManager.On("Dispatch", mock.AnythingOfType("*events.RoleUpdatedEvent")).Return().Once()

		// 执行测试
		err := roleService.UpdateRole(context.Background(), updatedRole, 1, 1)

		// 验证结果
		assert.NoError(t, err)
		mockRoleRepo.AssertExpectations(t)
		mockEventManager.AssertExpectations(t)
	})

	// 测试场景: 找不到要更新的角色
	t.Run("UpdateRole_RoleNotFound", func(t *testing.T) {
		// 准备测试数据
		role := &model.Role{
			ID:          999,
			Name:        "Non-existent",
			Description: "This role doesn't exist",
			Type:        model.AdminRole,
			Level:       1,
		}

		// 模拟查找角色失败
		expectedErr := errors.New("role not found")
		mockRoleRepo.On("FindByID", mock.Anything, uint(999)).Return(nil, expectedErr).Once()

		// 模拟日志记录
		mockLogger.On("Error", mock.Anything, "Failed to find role for update", 
			"roleID", uint(999), "error", expectedErr).Return().Once()

		// 执行测试
		err := roleService.UpdateRole(context.Background(), role, 1, 1)

		// 验证结果
		assert.Error(t, err)
		assert.Equal(t, expectedErr, err)
		mockRoleRepo.AssertExpectations(t)
		mockLogger.AssertExpectations(t)
		// 确保事件不会被分发
		mockEventManager.AssertNotCalled(t, "Dispatch", mock.Anything)
	})

	// 测试场景: 更新角色失败
	t.Run("UpdateRole_UpdateFailure", func(t *testing.T) {
		// 准备测试数据
		existingRole := &model.Role{
			ID:          2,
			Name:        "Reviewer",
			Description: "Content reviewer role",
			Type:        model.AdminRole,
			Level:       2,
		}

		updatedRole := &model.Role{
			ID:          2,
			Name:        "Senior Reviewer",
			Description: "Senior content reviewer role",
			Type:        model.AdminRole,
			Level:       3,
		}

		// 模拟获取旧角色成功
		mockRoleRepo.On("FindByID", mock.Anything, uint(2)).Return(existingRole, nil).Once()

		// 模拟更新角色失败
		expectedErr := errors.New("database error")
		mockRoleRepo.On("Update", mock.Anything, updatedRole).Return(expectedErr).Once()

		// 模拟日志记录
		mockLogger.On("Error", mock.Anything, "Failed to update role", 
			"roleID", uint(2), "error", expectedErr).Return().Once()

		// 执行测试
		err := roleService.UpdateRole(context.Background(), updatedRole, 1, 1)

		// 验证结果
		assert.Error(t, err)
		assert.Equal(t, expectedErr, err)
		mockRoleRepo.AssertExpectations(t)
		mockLogger.AssertExpectations(t)
		// 确保事件不会被分发
		mockEventManager.AssertNotCalled(t, "Dispatch", mock.Anything)
	})

	// 测试场景: 无实际变更不触发事件
	t.Run("UpdateRole_NoChanges_NoEvent", func(t *testing.T) {
		// 准备测试数据 - 角色信息完全相同
		existingRole := &model.Role{
			ID:          3,
			Name:        "Moderator",
			Description: "Content moderator role",
			Type:        model.AdminRole,
			Level:       2,
		}

		updatedRole := &model.Role{
			ID:          3,
			Name:        "Moderator", // 名称相同
			Description: "Content moderator role updated", // 只有描述变更，不触发事件
			Type:        model.AdminRole,
			Level:       2, // 等级相同
		}

		// 模拟获取旧角色
		mockRoleRepo.On("FindByID", mock.Anything, uint(3)).Return(existingRole, nil).Once()

		// 模拟更新角色
		mockRoleRepo.On("Update", mock.Anything, updatedRole).Return(nil).Once()

		// 执行测试
		err := roleService.UpdateRole(context.Background(), updatedRole, 1, 1)

		// 验证结果
		assert.NoError(t, err)
		mockRoleRepo.AssertExpectations(t)
		// 确保事件不会被分发 - 因为名称和等级没有变更
		mockEventManager.AssertNotCalled(t, "Dispatch", mock.Anything)
	})
}

func TestRoleService_DeleteRole(t *testing.T) {
	// 准备模拟对象
	mockRoleRepo := new(MockRoleRepository)
	mockPermRepo := new(MockPermissionRepository)
	mockLogger := new(MockLogger)
	mockEventManager := new(MockEventManager)

	// 创建服务实例
	roleService := service.NewRoleService(
		mockRoleRepo,
		mockPermRepo,
		mockLogger,
		mockEventManager,
	)

	// 测试场景: 成功删除角色
	t.Run("DeleteRole_Success", func(t *testing.T) {
		// 准备测试数据
		existingRole := &model.Role{
			ID:          1,
			Name:        "Temporary Role",
			Description: "Role to be deleted",
			Type:        model.AdminRole,
			Level:       2,
		}

		// 模拟查找角色
		mockRoleRepo.On("FindByID", mock.Anything, uint(1)).Return(existingRole, nil).Once()

		// 模拟删除角色
		mockRoleRepo.On("Delete", mock.Anything, uint(1)).Return(nil).Once()

		// 模拟事件分发
		mockEventManager.On("Dispatch", mock.AnythingOfType("*events.RoleDeletedEvent")).Return().Once()

		// 执行测试
		err := roleService.DeleteRole(context.Background(), 1, 1, 1)

		// 验证结果
		assert.NoError(t, err)
		mockRoleRepo.AssertExpectations(t)
		mockEventManager.AssertExpectations(t)
	})

	// 测试场景: 找不到要删除的角色
	t.Run("DeleteRole_RoleNotFound", func(t *testing.T) {
		// 模拟查找角色失败
		expectedErr := errors.New("role not found")
		mockRoleRepo.On("FindByID", mock.Anything, uint(999)).Return(nil, expectedErr).Once()

		// 模拟日志记录
		mockLogger.On("Error", mock.Anything, "Failed to find role for deletion", 
			"roleID", uint(999), "error", expectedErr).Return().Once()

		// 执行测试
		err := roleService.DeleteRole(context.Background(), 999, 1, 1)

		// 验证结果
		assert.Error(t, err)
		assert.Equal(t, expectedErr, err)
		mockRoleRepo.AssertExpectations(t)
		mockLogger.AssertExpectations(t)
		// 确保事件不会被分发
		mockEventManager.AssertNotCalled(t, "Dispatch", mock.Anything)
	})

	// 测试场景: 删除角色失败
	t.Run("DeleteRole_DeleteFailure", func(t *testing.T) {
		// 准备测试数据
		existingRole := &model.Role{
			ID:          2,
			Name:        "Protected Role",
			Description: "Role that cannot be deleted",
			Type:        model.AdminRole,
			Level:       1,
		}

		// 模拟查找角色成功
		mockRoleRepo.On("FindByID", mock.Anything, uint(2)).Return(existingRole, nil).Once()

		// 模拟删除角色失败
		expectedErr := errors.New("cannot delete protected role")
		mockRoleRepo.On("Delete", mock.Anything, uint(2)).Return(expectedErr).Once()

		// 模拟日志记录
		mockLogger.On("Error", mock.Anything, "Failed to delete role", 
			"roleID", uint(2), "error", expectedErr).Return().Once()

		// 执行测试
		err := roleService.DeleteRole(context.Background(), 2, 1, 1)

		// 验证结果
		assert.Error(t, err)
		assert.Equal(t, expectedErr, err)
		mockRoleRepo.AssertExpectations(t)
		mockLogger.AssertExpectations(t)
		// 确保事件不会被分发
		mockEventManager.AssertNotCalled(t, "Dispatch", mock.Anything)
	})
}

func TestRoleService_AssignPermissionsToRole(t *testing.T) {
	// 准备模拟对象
	mockRoleRepo := new(MockRoleRepository)
	mockPermRepo := new(MockPermissionRepository)
	mockLogger := new(MockLogger)
	mockEventManager := new(MockEventManager)

	// 创建服务实例
	roleService := service.NewRoleService(
		mockRoleRepo,
		mockPermRepo,
		mockLogger,
		mockEventManager,
	)

	// 测试场景: 成功为角色分配权限
	t.Run("AssignPermissions_Success", func(t *testing.T) {
		// 准备测试数据
		role := &model.Role{
			ID:          1,
			Name:        "Content Manager",
			Description: "Manages all content",
			Type:        model.AdminRole,
			Level:       2,
		}

		// 当前权限
		currentPermissions := []*model.Permission{
			{ID: 1, Slug: "content.view"},
			{ID: 2, Slug: "content.edit"},
		}

		// 要分配的权限ID
		newPermissionIDs := []uint{1, 2, 3, 4} // 保留1,2，添加3,4

		// 要添加的权限
		permissionsToAdd := []*model.Permission{
			{ID: 3, Slug: "content.create"},
			{ID: 4, Slug: "content.delete"},
		}

		// 模拟查找角色
		mockRoleRepo.On("FindByID", mock.Anything, uint(1)).Return(role, nil).Once()

		// 模拟获取当前权限
		mockRoleRepo.On("FindPermissionsByRoleID", mock.Anything, uint(1)).Return(currentPermissions, nil).Once()

		// 模拟获取要添加的权限详情
		mockPermRepo.On("FindByIDs", mock.Anything, []uint{3, 4}).Return(permissionsToAdd, nil).Once()

		// 模拟同步权限
		mockRoleRepo.On("SyncPermissions", mock.Anything, uint(1), newPermissionIDs).Return(nil).Once()

		// 模拟事件分发
		mockEventManager.On("Dispatch", mock.AnythingOfType("*events.RolePermissionsChangedEvent")).Return().Once()

		// 执行测试
		err := roleService.AssignPermissionsToRole(context.Background(), 1, newPermissionIDs, 1, 1)

		// 验证结果
		assert.NoError(t, err)
		mockRoleRepo.AssertExpectations(t)
		mockPermRepo.AssertExpectations(t)
		mockEventManager.AssertExpectations(t)
	})

	// 测试场景: 未找到角色
	t.Run("AssignPermissions_RoleNotFound", func(t *testing.T) {
		// 要分配的权限ID
		permissionIDs := []uint{1, 2, 3}

		// 模拟查找角色失败
		expectedErr := errors.New("role not found")
		mockRoleRepo.On("FindByID", mock.Anything, uint(999)).Return(nil, expectedErr).Once()

		// 模拟日志记录
		mockLogger.On("Error", mock.Anything, "Failed to find role for permission assignment", 
			"roleID", uint(999), "error", expectedErr).Return().Once()

		// 执行测试
		err := roleService.AssignPermissionsToRole(context.Background(), 999, permissionIDs, 1, 1)

		// 验证结果
		assert.Error(t, err)
		assert.Equal(t, expectedErr, err)
		mockRoleRepo.AssertExpectations(t)
		mockLogger.AssertExpectations(t)
	})

	// 测试场景: 无权限变更不触发事件
	t.Run("AssignPermissions_NoChanges_NoEvent", func(t *testing.T) {
		// 准备测试数据
		role := &model.Role{
			ID:          2,
			Name:        "Viewer",
			Description: "Can only view content",
			Type:        model.AdminRole,
			Level:       3,
		}

		// 当前权限
		currentPermissions := []*model.Permission{
			{ID: 1, Slug: "content.view"},
		}

		// 要分配的权限ID - 与当前相同
		samePermissionIDs := []uint{1}

		// 模拟查找角色
		mockRoleRepo.On("FindByID", mock.Anything, uint(2)).Return(role, nil).Once()

		// 模拟获取当前权限
		mockRoleRepo.On("FindPermissionsByRoleID", mock.Anything, uint(2)).Return(currentPermissions, nil).Once()

		// 模拟同步权限
		mockRoleRepo.On("SyncPermissions", mock.Anything, uint(2), samePermissionIDs).Return(nil).Once()

		// 执行测试
		err := roleService.AssignPermissionsToRole(context.Background(), 2, samePermissionIDs, 1, 1)

		// 验证结果
		assert.NoError(t, err)
		mockRoleRepo.AssertExpectations(t)
		// 确保事件不会被分发 - 因为权限没有变更
		mockEventManager.AssertNotCalled(t, "Dispatch", mock.Anything)
	})
}