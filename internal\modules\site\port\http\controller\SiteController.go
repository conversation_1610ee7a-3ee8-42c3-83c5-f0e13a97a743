/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/site/port/http/controller/SiteController.go
 * @Description: Controller for site operations.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package controller

import (
	"context"
	"gacms/internal/modules/site/application/dto"
	"gacms/internal/modules/site/application/service"
	"gacms/internal/modules/site/domain/model"
	themeController "gacms/internal/modules/theme/port/http/controller"
	"gacms/internal/port/http/middleware"
	"gacms/pkg/contract"
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
)

type SiteController struct {
	service         *service.SiteService
	themeCtrl       *themeController.ThemeController
	bindingService  contract.DomainBindingService
	eventManager    contract.EventManager
}

func NewSiteController(
	service *service.SiteService,
	themeCtrl *themeController.ThemeController,
	bindingService contract.DomainBindingService,
	eventManager contract.EventManager,
) *SiteController {
	return &SiteController{
		service:        service,
		themeCtrl:     themeCtrl,
		bindingService: bindingService,
		eventManager:   eventManager,
	}
}

func (c *SiteController) RegisterRoutes(rg *gin.RouterGroup, auth *middleware.AdminAuthMiddleware) {
	// All site management endpoints are for super admins only.
	sites := rg.Group("/sites")
	sites.Use(auth.RequireAuth(), auth.RequirePermission("system:sites:manage"))
	{
		sites.POST("", c.Create)
		sites.GET("", c.List)
		sites.GET("/:id", c.Get)
		sites.PUT("/:id", c.Update)
		sites.DELETE("/:id", c.Delete)

		// Register theme sub-routes under /sites/:siteId
		c.themeCtrl.RegisterSiteThemeRoutes(sites.Group("/:siteId"))

		// 域名绑定管理路由
		sites.GET("/:id/domain-bindings", c.ListDomainBindings)
		sites.POST("/:id/domain-bindings/module", c.CreateModuleBinding)
		sites.POST("/:id/domain-bindings/category", c.CreateCategoryBinding)
		sites.PUT("/:id/domain-bindings/:bindingId", c.UpdateDomainBinding)
		sites.DELETE("/:id/domain-bindings/:bindingId", c.DeleteDomainBinding)

		// URL重写管理路由
		sites.POST("/:id/domain-bindings/:bindingId/url-rewrite/enable", c.EnableURLRewrite)
		sites.POST("/:id/domain-bindings/:bindingId/url-rewrite/disable", c.DisableURLRewrite)
		sites.GET("/:id/domain-bindings/:bindingId/url-rules", c.ListURLRules)
		sites.POST("/:id/domain-bindings/:bindingId/url-rules", c.CreateURLRule)
		sites.PUT("/:id/domain-bindings/:bindingId/url-rules/:ruleId", c.UpdateURLRule)
		sites.DELETE("/:id/domain-bindings/:bindingId/url-rules/:ruleId", c.DeleteURLRule)
	}
}

func (c *SiteController) Create(ctx *gin.Context) {
	var input dto.SiteCreateDTO
	if err := ctx.ShouldBindJSON(&input); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	site, err := c.service.CreateSite(&input)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	// 通过事件系统记录操作日志
	c.publishOperationLog(ctx, "create", "Created site: "+site.Name, "site", site.ID)
	ctx.JSON(http.StatusCreated, site)
}

func (c *SiteController) List(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	sites, total, err := c.service.ListSites(page, pageSize)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"data": sites, "total": total})
}

func (c *SiteController) Get(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid site ID"})
		return
	}
	site, err := c.service.GetSite(uint(id))
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Site not found"})
		return
	}
	ctx.JSON(http.StatusOK, site)
}

func (c *SiteController) Update(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid site ID"})
		return
	}
	var input dto.SiteUpdateDTO
	if err := ctx.ShouldBindJSON(&input); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	site, err := c.service.UpdateSite(uint(id), &input)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.Set("action_log_description", "Updated site: "+site.Name)
	ctx.JSON(http.StatusOK, site)
}

func (c *SiteController) Delete(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid site ID"})
		return
	}
	
	// For logging purposes, get the site name before deleting
	site, err := c.service.GetSite(uint(id))
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Site not found"})
		return
	}
	
	err = c.service.DeleteSite(uint(id))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.Set("action_log_description", "Deleted site: "+site.Name)
	ctx.Status(http.StatusNoContent)
}

// 域名绑定管理方法

// ListDomainBindings 获取站点的域名绑定列表
func (c *SiteController) ListDomainBindings(ctx *gin.Context) {
	siteID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid site ID"})
		return
	}

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))

	bindings, total, err := c.bindingService.ListBindingsBySite(uint(siteID), page, pageSize)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data": gin.H{
			"bindings": bindings,
			"total":    total,
			"page":     page,
			"page_size": pageSize,
		},
	})
}

// CreateModuleBinding 创建模块绑定
func (c *SiteController) CreateModuleBinding(ctx *gin.Context) {
	siteID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid site ID"})
		return
	}

	var req struct {
		Domain     string `json:"domain" binding:"required"`
		ModuleSlug string `json:"module_slug" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	binding, err := c.bindingService.CreateModuleBinding(req.Domain, uint(siteID), req.ModuleSlug)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Set("action_log_description", "Created module binding: "+req.Domain+" -> "+req.ModuleSlug)
	ctx.JSON(http.StatusCreated, gin.H{
		"data": binding,
		"message": "Module binding created successfully",
	})
}

// CreateCategoryBinding 创建栏目绑定
func (c *SiteController) CreateCategoryBinding(ctx *gin.Context) {
	siteID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid site ID"})
		return
	}

	var req struct {
		Domain     string `json:"domain" binding:"required"`
		CategoryID uint   `json:"category_id" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	binding, err := c.bindingService.CreateCategoryBinding(req.Domain, uint(siteID), req.CategoryID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 通过事件系统记录操作日志
	c.publishOperationLog(ctx, "create", "Created category binding: "+req.Domain+" -> category "+strconv.Itoa(int(req.CategoryID)), "domain_binding", binding.ID)
	ctx.JSON(http.StatusCreated, gin.H{
		"data": binding,
		"message": "Category binding created successfully",
	})
}

// publishOperationLog 通过事件系统发布操作日志
func (c *SiteController) publishOperationLog(ctx *gin.Context, action, description string, resourceType string, resourceID uint) {
	// 从上下文获取用户信息
	userID, _ := ctx.Get("user_id")
	userType, _ := ctx.Get("user_type")

	// 构建操作日志事件数据
	eventData := map[string]interface{}{
		"user_id":       userID,
		"user_type":     userType,
		"action":        action,
		"description":   description,
		"resource_type": resourceType,
		"resource_id":   resourceID,
		"ip_address":    ctx.ClientIP(),
		"user_agent":    ctx.GetHeader("User-Agent"),
	}

	// 创建操作日志事件（由audit模块处理）
	event := c.eventManager.CreateEvent(context.Background(), "audit.operation.create", eventData)

	// 发布事件（异步处理，不影响主业务流程）
	go func() {
		if err := c.eventManager.PublishEvent(event); err != nil {
			// 日志记录失败不应该影响主业务，只记录错误
			// 这里可以记录到系统日志
		}
	}()
}

// publishOperationLog 通过事件系统发布操作日志
func (c *SiteController) publishOperationLog(ctx *gin.Context, action, description string, resourceType string, resourceID uint) {
	// 从上下文获取用户信息
	userID, _ := ctx.Get("user_id")
	userType, _ := ctx.Get("user_type")

	// 构建操作日志事件数据
	eventData := map[string]interface{}{
		"user_id":       userID,
		"user_type":     userType,
		"action":        action,
		"description":   description,
		"resource_type": resourceType,
		"resource_id":   resourceID,
		"ip_address":    ctx.ClientIP(),
		"user_agent":    ctx.GetHeader("User-Agent"),
		"timestamp":     ctx.Request.Header.Get("X-Request-Time"),
	}

	// 创建操作日志事件
	event := c.eventManager.CreateEvent(context.Background(), "operation.log.create", eventData)

	// 发布事件（异步处理，不影响主业务流程）
	go func() {
		if err := c.eventManager.PublishEvent(event); err != nil {
			// 日志记录失败不应该影响主业务，只记录错误
			// 这里可以记录到系统日志
		}
	}()
}

func (sc *SiteController) ToSiteDTO(site *model.Site) *dto.SiteDTO {
	return &dto.SiteDTO{
		ID:             site.ID,
		Name:           site.Name,
		Domain:         site.Domain,
		IsActive:       site.IsActive,
		FrontendTheme:  site.FrontendTheme,
		BackendTheme:   site.BackendTheme,
		DefaultLang:    site.DefaultLang,
		AvailableLangs: site.AvailableLangs,
		CreatedAt:      site.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:      site.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}