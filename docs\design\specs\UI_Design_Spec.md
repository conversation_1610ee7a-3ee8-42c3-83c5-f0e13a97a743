# GACMS UI/UX 设计规范

## 1. 概述

本文档定义了 GACMS 后台管理系统的 UI/UX 设计规范，旨在确保整个系统界面的一致性、专业性和易用性。设计规范基于 Tailwind CSS 框架和 Font Awesome 图标库，适用于所有 GACMS 后台管理界面的开发和维护。

### 1.1 设计原则

- **一致性**: 保持界面元素、交互模式和视觉风格的一致
- **简洁性**: 减少视觉噪音，突出重要信息和操作
- **响应式**: 确保在不同设备和屏幕尺寸上的良好体验
- **可访问性**: 符合 WCAG 2.1 AA 级别的可访问性标准
- **效率优先**: 优化常用操作路径，减少用户操作步骤

### 1.2 目标平台

根据 PRD 中的目标平台列表，本设计规范主要针对以下平台进行优化：

- **Web (PC端)**: 响应式设计，适配主流桌面浏览器
- **Web (移动端)**: 响应式设计，适配主流移动浏览器
- **微信公众号**: 提供内容接口，支持公众号文章发布、管理与用户行为数据分析
- **微信小程序**: 提供内容接口，支持小程序内容展示与交互

## 2. 颜色系统

### 2.1 主色调

- **主色 (Primary)**: `#007BFF` - 用于主要按钮、链接和强调元素
- **次要色 (Secondary)**: `#6C757D` - 用于次要按钮和辅助元素
- **成功色 (Success)**: `#28A745` - 用于表示成功状态和积极反馈
- **危险色 (Danger)**: `#DC3545` - 用于表示错误、警告和危险操作
- **警告色 (Warning)**: `#FFC107` - 用于表示需要注意的状态和信息
- **信息色 (Info)**: `#17A2B8` - 用于表示提示和信息性内容

### 2.2 中性色

- **深色 (Dark)**: `#343A40` - 用于主要文本和标题
- **中灰 (Medium)**: `#6C757D` - 用于次要文本和边框
- **浅灰 (Light)**: `#F8F9FA` - 用于背景和分隔元素
- **白色 (White)**: `#FFFFFF` - 用于卡片背景和主要内容区域

### 2.3 状态色

- **活动状态 (Active)**: `#0069D9` - 主色的深色版本，用于按钮和链接的活动状态
- **悬停状态 (Hover)**: `#0062CC` - 主色的更深版本，用于按钮和链接的悬停状态
- **禁用状态 (Disabled)**: `#6C757D` 透明度 65% - 用于禁用的按钮和控件

## 3. 排版

### 3.1 字体

- **主要字体**: 系统默认无衬线字体栈
  ```css
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  ```

- **等宽字体**: 用于代码和技术内容
  ```css
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  ```

### 3.2 字体大小

- **标题 1**: 2.5rem (40px) - 用于主要页面标题
- **标题 2**: 2rem (32px) - 用于主要区域标题
- **标题 3**: 1.75rem (28px) - 用于卡片和区块标题
- **标题 4**: 1.5rem (24px) - 用于小区块标题
- **标题 5**: 1.25rem (20px) - 用于小标题
- **标题 6**: 1rem (16px) - 用于最小标题
- **正文**: 1rem (16px) - 用于主要内容文本
- **小文本**: 0.875rem (14px) - 用于辅助文本和注释
- **超小文本**: 0.75rem (12px) - 用于极小的辅助文本

### 3.3 字重

- **Light**: 300 - 用于大标题和装饰性文本
- **Regular**: 400 - 用于正文和一般内容
- **Medium**: 500 - 用于强调和小标题
- **Bold**: 700 - 用于主要标题和重点强调

### 3.4 行高

- **紧凑**: 1.2 - 用于标题和短文本
- **标准**: 1.5 - 用于正文和一般内容
- **宽松**: 1.8 - 用于长段落和易读性要求高的内容

## 4. 间距系统

### 4.1 基础间距单位

基础间距单位为 0.25rem (4px)，所有间距都是这个基础单位的倍数。

### 4.2 常用间距

- **超小 (xs)**: 0.25rem (4px)
- **小 (sm)**: 0.5rem (8px)
- **中 (md)**: 1rem (16px)
- **大 (lg)**: 1.5rem (24px)
- **超大 (xl)**: 3rem (48px)

### 4.3 内边距 (Padding)

- **卡片内边距**: 1.5rem (24px)
- **按钮内边距**: 水平 1rem (16px)，垂直 0.5rem (8px)
- **表单控件内边距**: 0.375rem 0.75rem (6px 12px)

### 4.4 外边距 (Margin)

- **区块间距**: 1.5rem (24px)
- **段落间距**: 1rem (16px)
- **列表项间距**: 0.5rem (8px)

## 5. 布局系统

### 5.1 网格系统

采用 12 列响应式网格系统，基于 Tailwind CSS 的 grid 和 flex 功能实现。

### 5.2 断点

- **xs**: < 576px (移动设备)
- **sm**: ≥ 576px (平板竖屏)
- **md**: ≥ 768px (平板横屏)
- **lg**: ≥ 992px (桌面显示器)
- **xl**: ≥ 1200px (大型桌面显示器)
- **2xl**: ≥ 1400px (超大型桌面显示器)

### 5.3 容器

- **固定宽度容器**: 在不同断点下有不同的最大宽度
  - xs: 100%
  - sm: 540px
  - md: 720px
  - lg: 960px
  - xl: 1140px
  - 2xl: 1320px

- **流体容器**: 宽度始终为 100%，仅有水平内边距

### 5.4 布局模式

- **侧边栏 + 内容区**: 用于主要管理界面
- **居中内容**: 用于登录、注册等简单页面
- **全宽内容**: 用于数据可视化和复杂表格

## 6. 组件库

### 6.1 导航组件

#### 6.1.1 侧边栏导航

- **样式**: 深色背景 (#343A40)，白色文本
- **悬停状态**: 半透明白色背景 (rgba(255,255,255,0.1))
- **活动状态**: 主色背景或左侧边框
- **图标**: 每个菜单项左侧配有 Font Awesome 图标
- **折叠功能**: 支持展开/折叠子菜单，以及整个侧边栏的展开/折叠

#### 6.1.2 顶部导航栏

- **样式**: 白色背景，深色文本，底部细边框
- **元素**: 包含汉堡菜单按钮、面包屑导航、搜索框、通知图标、用户头像
- **响应式**: 在小屏幕上自动调整或隐藏部分元素

#### 6.1.3 面包屑导航

- **样式**: 轻量级设计，使用 / 或 > 作为分隔符
- **交互**: 每级路径可点击，最后一级为当前页面（不可点击）

### 6.2 表单组件

#### 6.2.1 输入框

- **样式**: 圆角边框，内边距一致，获取焦点时显示主色边框
- **状态**: 默认、获取焦点、禁用、错误、成功
- **变体**: 文本输入、数字输入、密码输入、文本区域

#### 6.2.2 选择控件

- **下拉选择框**: 与输入框样式一致，下拉列表有最大高度和滚动
- **单选按钮**: 圆形选择器，选中时内部填充主色
- **复选框**: 方形选择器，选中时显示对勾图标
- **开关**: 滑动式开关，开启状态显示主色背景

#### 6.2.3 日期和时间选择器

- **日期选择**: 弹出日历界面，支持月份和年份快速切换
- **时间选择**: 小时和分钟选择器，可选择上午/下午
- **日期时间组合**: 结合日期和时间选择功能

#### 6.2.4 文件上传

- **基本上传**: 按钮式上传控件，显示已选文件名
- **拖放上传**: 支持文件拖放的区域，显示上传进度
- **图片上传**: 支持图片预览的上传控件

### 6.3 按钮

#### 6.3.1 按钮类型

- **主要按钮**: 实心背景，用于主要操作
- **次要按钮**: 轮廓样式，用于次要操作
- **文本按钮**: 无背景无边框，用于辅助操作
- **图标按钮**: 仅包含图标的小型按钮

#### 6.3.2 按钮状态

- **默认**: 基本样式
- **悬停**: 颜色加深
- **活动**: 颜色进一步加深，可能有轻微内阴影
- **禁用**: 降低透明度，移除交互效果
- **加载中**: 显示加载指示器，防止重复点击

#### 6.3.3 按钮尺寸

- **小型**: 用于紧凑界面和行内操作
- **中型**: 标准尺寸，默认选项
- **大型**: 用于强调和主要操作

### 6.4 表格

#### 6.4.1 基本表格

- **表头**: 粗体文本，浅灰色背景
- **表格行**: 交替背景色，悬停时高亮
- **边框**: 轻量级边框或无边框设计

#### 6.4.2 数据表格

- **排序**: 支持点击表头排序
- **筛选**: 每列可添加筛选功能
- **分页**: 底部分页控件
- **选择**: 行选择功能，支持批量操作
- **展开行**: 支持行详情展开

### 6.5 卡片

- **样式**: 白色背景，轻微阴影，圆角边框
- **结构**: 卡片头部（标题）、卡片内容、卡片底部（操作区）
- **变体**: 无边框卡片、简单卡片、带图片卡片

### 6.6 对话框和弹出层

#### 6.6.1 模态对话框

- **结构**: 标题栏、内容区、操作按钮区
- **尺寸**: 小型、中型、大型、全屏
- **动画**: 淡入/淡出或滑入/滑出

#### 6.6.2 抽屉

- **位置**: 左侧、右侧、顶部、底部
- **用途**: 详情查看、表单填写、过滤器设置

#### 6.6.3 提示和通知

- **提示框**: 简短消息，自动消失
- **通知**: 可包含操作的消息提醒
- **确认对话框**: 需要用户确认的操作提示

### 6.7 导航和标签

#### 6.7.1 标签页

- **水平标签**: 顶部水平排列的标签
- **垂直标签**: 左侧垂直排列的标签
- **卡片式标签**: 带有卡片样式的标签

#### 6.7.2 步骤条

- **水平步骤条**: 适用于较少步骤的流程
- **垂直步骤条**: 适用于步骤较多或内容较复杂的流程
- **状态**: 完成、当前、等待、错误

### 6.8 数据展示

#### 6.8.1 统计卡片

- **结构**: 标题、数值、同比变化、图表
- **样式**: 简洁设计，突出核心数据

#### 6.8.2 图表

- **类型**: 折线图、柱状图、饼图、雷达图等
- **交互**: 数据点悬停提示、缩放、图例交互

#### 6.8.3 列表

- **基础列表**: 简单的项目列表
- **头像列表**: 带有用户头像的列表
- **卡片列表**: 每项为卡片样式的列表

## 7. 图标系统

### 7.1 图标库

使用 Font Awesome 作为主要图标库，确保图标风格统一。

### 7.2 图标尺寸

- **小型**: 0.875rem (14px)
- **中型**: 1rem (16px)
- **大型**: 1.5rem (24px)
- **超大**: 2rem (32px)

### 7.3 图标颜色

图标颜色应与周围文本颜色一致，或使用语义化颜色（如成功色、警告色等）。

## 8. 响应式设计指南

### 8.1 移动优先原则

采用移动优先的设计方法，先为小屏幕设计界面，再逐步扩展到大屏幕。

### 8.2 响应式调整策略

- **布局调整**: 从单列到多列
- **导航转换**: 侧边栏在小屏幕上转为抽屉菜单
- **内容优先级**: 在小屏幕上隐藏次要内容或将其移至折叠区域
- **触摸友好**: 在移动设备上增大可点击区域

### 8.3 常见响应式模式

- **堆叠卡片**: 在小屏幕上卡片垂直堆叠，大屏幕上水平排列
- **表格响应**: 在小屏幕上转为卡片式显示或允许水平滚动
- **表单响应**: 在小屏幕上标签和输入框垂直排列

## 9. 动效指南

### 9.1 过渡效果

- **持续时间**: 150ms - 300ms
- **缓动函数**: ease-in-out 或 cubic-bezier(0.4, 0, 0.2, 1)
- **常见过渡**: 颜色变化、透明度变化、位置移动

### 9.2 动画效果

- **加载动画**: 用于表示内容正在加载
- **提交动画**: 用于表示表单正在提交
- **成功/失败动画**: 用于表示操作结果

### 9.3 动效原则

- **目的性**: 动效应有明确目的，增强用户体验
- **克制性**: 避免过度使用动效，以免分散注意力
- **一致性**: 相似操作应有相似动效

## 10. 可访问性指南

### 10.1 颜色对比度

- 文本与背景的对比度应符合 WCAG 2.1 AA 级别标准
- 主要文本: 对比度至少 4.5:1
- 大文本: 对比度至少 3:1

### 10.2 键盘可访问性

- 所有交互元素应可通过键盘访问和操作
- 焦点状态应有明显的视觉指示

### 10.3 屏幕阅读器支持

- 使用适当的语义化 HTML 元素
- 为非文本内容提供替代文本
- 确保表单控件有关联的标签

## 11. 设计资源

### 11.1 Tailwind CSS 配置

```javascript
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: '#007BFF',
        secondary: '#6C757D',
        success: '#28A745',
        danger: '#DC3545',
        warning: '#FFC107',
        info: '#17A2B8',
        dark: '#343A40',
        light: '#F8F9FA',
      },
      spacing: {
        // 可以添加自定义间距
      },
      borderRadius: {
        // 可以添加自定义圆角
      },
      boxShadow: {
        // 可以添加自定义阴影
      },
    },
  },
  variants: {
    extend: {
      // 可以启用更多状态变体
    },
  },
  plugins: [
    // 可以添加自定义插件
  ],
}
```

### 11.2 常用 Tailwind CSS 类组合

- **按钮**: `px-4 py-2 bg-primary text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`
- **卡片**: `bg-white rounded-lg shadow p-6`
- **表单输入**: `w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent`

## 12. 实现指南

### 12.1 HTML 结构

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GACMS - 页面标题</title>
  <!-- Tailwind CSS CDN -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <!-- Font Awesome CDN -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <!-- 自定义样式 -->
  <link href="/assets/css/gacms-admin.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
  <!-- 页面结构 -->
  <div class="flex h-screen">
    <!-- 侧边栏 -->
    <aside class="bg-dark text-white w-64 flex-shrink-0 hidden md:block">
      <!-- 侧边栏内容 -->
    </aside>
    
    <!-- 主内容区 -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- 顶部导航栏 -->
      <header class="bg-white border-b border-gray-200">
        <!-- 顶部导航内容 -->
      </header>
      
      <!-- 页面内容 -->
      <main class="flex-1 overflow-x-hidden overflow-y-auto p-6">
        <!-- 页面主要内容 -->
      </main>
      
      <!-- 页脚 -->
      <footer class="bg-white border-t border-gray-200 p-4">
        <!-- 页脚内容 -->
      </footer>
    </div>
  </div>
  
  <!-- JavaScript -->
  <script src="/assets/js/gacms-admin.js"></script>
</body>
</html>
```

### 12.2 组件实现示例

#### 按钮组件

```html
<!-- 主要按钮 -->
<button class="px-4 py-2 bg-primary text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
  <i class="fas fa-save mr-2"></i>保存
</button>

<!-- 次要按钮 -->
<button class="px-4 py-2 border border-gray-300 text-gray-700 rounded hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50">
  <i class="fas fa-times mr-2"></i>取消
</button>

<!-- 危险按钮 -->
<button class="px-4 py-2 bg-danger text-white rounded hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50">
  <i class="fas fa-trash mr-2"></i>删除
</button>
```

#### 表单组件

```html
<div class="mb-4">
  <label for="title" class="block text-sm font-medium text-gray-700 mb-1">标题</label>
  <input type="text" id="title" name="title" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
</div>

<div class="mb-4">
  <label for="category" class="block text-sm font-medium text-gray-700 mb-1">分类</label>
  <select id="category" name="category" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
    <option value="">请选择分类</option>
    <option value="1">新闻</option>
    <option value="2">公告</option>
    <option value="3">产品</option>
  </select>
</div>

<div class="mb-4">
  <label for="content" class="block text-sm font-medium text-gray-700 mb-1">内容</label>
  <textarea id="content" name="content" rows="5" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"></textarea>
</div>
```

#### 数据表格

```html
<div class="bg-white rounded-lg shadow overflow-hidden">
  <table class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
      <tr>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
          标题
        </th>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
          分类
        </th>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
          状态
        </th>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
          发布日期
        </th>
        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
          操作
        </th>
      </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
      <tr class="hover:bg-gray-50">
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm font-medium text-gray-900">示例文章标题</div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm text-gray-500">新闻</div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
            已发布
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          2025-05-15
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <a href="#" class="text-primary hover:text-blue-800 mr-3">编辑</a>
          <a href="#" class="text-danger hover:text-red-800">删除</a>
        </td>
      </tr>
      <!-- 更多行... -->
    </tbody>
  </table>
</div>
```

## 13. 最佳实践

### 13.1 代码组织

- 使用语义化 HTML 标签
- 将通用样式抽取为可复用的类
- 使用 Tailwind CSS 的 @apply 指令组织复杂组件样式

### 13.2 性能优化

- 使用 Tailwind CSS 的 JIT 模式减少 CSS 文件大小
- 延迟加载非关键资源
- 优化图片和图标资源

### 13.3 可维护性

- 保持命名一致性
- 使用注释说明复杂组件的结构和用途
- 遵循组件化开发方法

## 14. 附录

### 14.1 常见问题解答

- **Q: 如何处理自定义主题？**
  A: 可以通过扩展 Tailwind CSS 配置或使用 CSS 变量实现主题切换。

- **Q: 如何处理复杂的表单验证？**
  A: 建议使用 JavaScript 表单验证库，并遵循本文档中的错误状态样式指南。

- **Q: 如何实现暗色模式？**
  A: 使用 Tailwind CSS 的暗色模式功能，通过 `dark:` 前缀定义暗色模式下的样式。

### 14.2 参考资源

- [Tailwind CSS 官方文档](https://tailwindcss.com/docs)
- [Font Awesome 图标库](https://fontawesome.com/icons)
- [WCAG 2.1 可访问性指南](https://www.w3.org/TR/WCAG21/)