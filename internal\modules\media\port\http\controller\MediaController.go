/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/media/port/http/controller/MediaController.go
 * @Description: Controller for media asset operations.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package controller

import (
	"fmt"
	"gacms/internal/modules/media/application/service"
	userModel "gacms/internal/modules/user/domain/model"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type MediaController struct {
	mediaSvc *service.MediaService
}

func NewMediaController(mediaSvc *service.MediaService) *MediaController {
	return &MediaController{mediaSvc: mediaSvc}
}

type GetUploadTokenRequest struct {
	Filename string `json:"filename" binding:"required"`
	MimeType string `json:"mimeType" binding:"required"`
}

func (c *MediaController) GetUploadToken(ctx *gin.Context) {
	var req GetUploadTokenRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	user, _ := ctx.Get("currentUser")

	creds, err := c.mediaSvc.GetUploadCredentials(user.(*userModel.Admin), req.Filename, req.MimeType)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get upload credentials: %v", err)})
		return
	}

	ctx.JSON(http.StatusOK, creds)
}

func (c *MediaController) HandleUpload(ctx *gin.Context) {
	user, _ := ctx.Get("currentUser")
	
	file, err := ctx.FormFile("file")
    if err != nil {
        ctx.JSON(http.StatusBadRequest, gin.H{"error": "file not provided"})
        return
    }

	media, err := c.mediaSvc.HandleUpload(user.(*userModel.Admin), file)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to process upload: %v", err)})
		return
	}
	
	ctx.Set("action_log_description", fmt.Sprintf("Finalized upload for media: %s (ID: %d)", media.Name, media.ID))
	ctx.JSON(http.StatusCreated, media)
}

func (c *MediaController) ListMedia(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	filters := make(map[string]interface{})
	if mediaType := ctx.Query("mediaType"); mediaType != "" {
		filters["media_type"] = mediaType
	}

	mediaList, total, err := c.mediaSvc.ListMedia(ctx, page, pageSize, filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"data": mediaList, "total": total})
}

func (c *MediaController) GetMedia(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid media ID"})
		return
	}

	media, err := c.mediaSvc.GetMedia(ctx, uint(id))
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Media not found"})
		return
	}

	ctx.JSON(http.StatusOK, media)
}

func (c *MediaController) DeleteMedia(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid media ID"})
		return
	}

	user, _ := ctx.Get("currentUser")

	err = c.mediaSvc.DeleteMedia(ctx, uint(id), user.(*userModel.Admin))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	ctx.Set("action_log_description", fmt.Sprintf("Deleted media ID: %d", id))
	ctx.Status(http.StatusNoContent)
} 