<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 多语言管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }

        .language-flag {
            width: 24px;
            height: 16px;
            border-radius: 2px;
            object-fit: cover;
            margin-right: 10px;
        }
        
        .badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .badge-active {
            background-color: rgba(16, 185, 129, 0.2);
            color: rgb(16, 185, 129);
        }
        
        .badge-inactive {
            background-color: rgba(239, 68, 68, 0.2);
            color: rgb(239, 68, 68);
        }
        
        .badge-default {
            background-color: rgba(59, 130, 246, 0.2);
            color: rgb(59, 130, 246);
        }
        
        .progress {
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            border-radius: 4px;
            background: linear-gradient(90deg, #3B82F6, #60A5FA);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">多语言管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-plus text-white"></i>
                                </span>
                                添加语言
                            </span>
                        </button>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="relative w-full md:w-96">
                        <input type="text" placeholder="搜索语言..." 
                               class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent pl-10">
                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 已安装语言列表 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold mb-4">已安装语言</h3>
                
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="text-left border-b border-gray-700">
                                <th class="pb-3 pl-3">语言</th>
                                <th class="pb-3">代码</th>
                                <th class="pb-3">状态</th>
                                <th class="pb-3">翻译进度</th>
                                <th class="pb-3">最后更新</th>
                                <th class="pb-3 text-right pr-3">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 中文 -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 pl-3">
                                    <div class="flex items-center">
                                        <img src="./assets/images/flags/cn.png" alt="中文" class="language-flag">
                                        <span>简体中文</span>
                                    </div>
                                </td>
                                <td class="py-4">zh-CN</td>
                                <td class="py-4">
                                    <span class="badge badge-default">默认</span>
                                </td>
                                <td class="py-4">
                                    <div class="w-48 flex items-center">
                                        <div class="w-full mr-3">
                                            <div class="progress">
                                                <div class="progress-bar" style="width: 100%"></div>
                                            </div>
                                        </div>
                                        <span class="text-sm">100%</span>
                                    </div>
                                </td>
                                <td class="py-4">2025-06-01</td>
                                <td class="py-4 text-right pr-3">
                                    <button class="text-gray-400 hover:text-white px-2" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-white px-2" title="导出">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </td>
                            </tr>
                            
                            <!-- 英文 -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 pl-3">
                                    <div class="flex items-center">
                                        <img src="./assets/images/flags/us.png" alt="英文" class="language-flag">
                                        <span>英文</span>
                                    </div>
                                </td>
                                <td class="py-4">en-US</td>
                                <td class="py-4">
                                    <span class="badge badge-active">已启用</span>
                                </td>
                                <td class="py-4">
                                    <div class="w-48 flex items-center">
                                        <div class="w-full mr-3">
                                            <div class="progress">
                                                <div class="progress-bar" style="width: 95%"></div>
                                            </div>
                                        </div>
                                        <span class="text-sm">95%</span>
                                    </div>
                                </td>
                                <td class="py-4">2025-05-28</td>
                                <td class="py-4 text-right pr-3">
                                    <button class="text-gray-400 hover:text-white px-2" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-white px-2" title="导出">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-white px-2" title="禁用">
                                        <i class="fas fa-toggle-on"></i>
                                    </button>
                                </td>
                            </tr>
                            
                            <!-- 日语 -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 pl-3">
                                    <div class="flex items-center">
                                        <img src="./assets/images/flags/jp.png" alt="日语" class="language-flag">
                                        <span>日语</span>
                                    </div>
                                </td>
                                <td class="py-4">ja-JP</td>
                                <td class="py-4">
                                    <span class="badge badge-active">已启用</span>
                                </td>
                                <td class="py-4">
                                    <div class="w-48 flex items-center">
                                        <div class="w-full mr-3">
                                            <div class="progress">
                                                <div class="progress-bar" style="width: 85%"></div>
                                            </div>
                                        </div>
                                        <span class="text-sm">85%</span>
                                    </div>
                                </td>
                                <td class="py-4">2025-05-15</td>
                                <td class="py-4 text-right pr-3">
                                    <button class="text-gray-400 hover:text-white px-2" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-white px-2" title="导出">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-white px-2" title="禁用">
                                        <i class="fas fa-toggle-on"></i>
                                    </button>
                                </td>
                            </tr>
                            
                            <!-- 德语 -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 pl-3">
                                    <div class="flex items-center">
                                        <img src="./assets/images/flags/de.png" alt="德语" class="language-flag">
                                        <span>德语</span>
                                    </div>
                                </td>
                                <td class="py-4">de-DE</td>
                                <td class="py-4">
                                    <span class="badge badge-inactive">已禁用</span>
                                </td>
                                <td class="py-4">
                                    <div class="w-48 flex items-center">
                                        <div class="w-full mr-3">
                                            <div class="progress">
                                                <div class="progress-bar" style="width: 65%"></div>
                                            </div>
                                        </div>
                                        <span class="text-sm">65%</span>
                                    </div>
                                </td>
                                <td class="py-4">2025-05-10</td>
                                <td class="py-4 text-right pr-3">
                                    <button class="text-gray-400 hover:text-white px-2" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-white px-2" title="导出">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-white px-2" title="启用">
                                        <i class="fas fa-toggle-off"></i>
                                    </button>
                                </td>
                            </tr>
                            
                            <!-- 法语 -->
                            <tr class="hover:bg-gray-800/20">
                                <td class="py-4 pl-3">
                                    <div class="flex items-center">
                                        <img src="./assets/images/flags/fr.png" alt="法语" class="language-flag">
                                        <span>法语</span>
                                    </div>
                                </td>
                                <td class="py-4">fr-FR</td>
                                <td class="py-4">
                                    <span class="badge badge-inactive">已禁用</span>
                                </td>
                                <td class="py-4">
                                    <div class="w-48 flex items-center">
                                        <div class="w-full mr-3">
                                            <div class="progress">
                                                <div class="progress-bar" style="width: 70%"></div>
                                            </div>
                                        </div>
                                        <span class="text-sm">70%</span>
                                    </div>
                                </td>
                                <td class="py-4">2025-05-05</td>
                                <td class="py-4 text-right pr-3">
                                    <button class="text-gray-400 hover:text-white px-2" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-white px-2" title="导出">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-white px-2" title="启用">
                                        <i class="fas fa-toggle-off"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 可安装语言 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold mb-4">可用语言包</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- 西班牙语 -->
                    <div class="border border-gray-700 rounded-lg p-4 hover:border-blue-500 transition-colors">
                        <div class="flex justify-between items-center mb-3">
                            <div class="flex items-center">
                                <img src="./assets/images/flags/es.png" alt="西班牙语" class="language-flag">
                                <span class="font-medium">西班牙语 (es-ES)</span>
                            </div>
                            <span class="text-xs text-gray-400">v1.2.3</span>
                        </div>
                        <p class="text-sm text-gray-400 mb-4">西班牙语言包，包含网站前后台所有翻译内容。</p>
                        <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg transition-colors">
                            安装
                        </button>
                    </div>
                    
                    <!-- 葡萄牙语 -->
                    <div class="border border-gray-700 rounded-lg p-4 hover:border-blue-500 transition-colors">
                        <div class="flex justify-between items-center mb-3">
                            <div class="flex items-center">
                                <img src="./assets/images/flags/pt.png" alt="葡萄牙语" class="language-flag">
                                <span class="font-medium">葡萄牙语 (pt-PT)</span>
                            </div>
                            <span class="text-xs text-gray-400">v1.2.0</span>
                        </div>
                        <p class="text-sm text-gray-400 mb-4">葡萄牙语言包，包含网站前后台所有翻译内容。</p>
                        <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg transition-colors">
                            安装
                        </button>
                    </div>
                    
                    <!-- 意大利语 -->
                    <div class="border border-gray-700 rounded-lg p-4 hover:border-blue-500 transition-colors">
                        <div class="flex justify-between items-center mb-3">
                            <div class="flex items-center">
                                <img src="./assets/images/flags/it.png" alt="意大利语" class="language-flag">
                                <span class="font-medium">意大利语 (it-IT)</span>
                            </div>
                            <span class="text-xs text-gray-400">v1.2.1</span>
                        </div>
                        <p class="text-sm text-gray-400 mb-4">意大利语言包，包含网站前后台所有翻译内容。</p>
                        <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg transition-colors">
                            安装
                        </button>
                    </div>
                </div>
                
                <div class="mt-4 text-center">
                    <button class="text-blue-400 hover:underline">查看更多语言包</button>
                </div>
            </div>
            
            <!-- 语言设置 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold mb-4">语言设置</h3>
                
                <div class="max-w-lg">
                    <div class="mb-4">
                        <label class="block text-gray-300 mb-2 font-medium">默认前台语言</label>
                        <div class="relative">
                            <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none">
                                <option>简体中文 (zh-CN)</option>
                                <option>英文 (en-US)</option>
                                <option>日语 (ja-JP)</option>
                            </select>
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-300 mb-2 font-medium">默认后台语言</label>
                        <div class="relative">
                            <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none">
                                <option>简体中文 (zh-CN)</option>
                                <option>英文 (en-US)</option>
                            </select>
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="flex items-center gap-2 cursor-pointer">
                            <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                            <span>允许用户切换语言</span>
                        </label>
                    </div>
                    
                    <div class="mb-4">
                        <label class="flex items-center gap-2 cursor-pointer">
                            <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                            <span>自动检测用户浏览器语言</span>
                        </label>
                    </div>
                    
                    <div class="mt-6">
                        <button class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-lg transition-colors">
                            保存设置
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 其他初始化...
        });
    </script>
</body>
</html> 