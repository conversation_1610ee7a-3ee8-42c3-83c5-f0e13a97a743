<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 缓存管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .cache-card {
            border-radius: 8px;
            border: 1px solid rgba(255,255,255,0.05);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .cache-card:hover {
            transform: translateY(-5px);
            border-color: rgba(59, 130, 246, 0.3);
            box-shadow: 0 10px 15px rgba(0,0,0,0.2);
        }
        
        .cache-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
        }
        
        .cache-card.system::before {
            background: linear-gradient(90deg, #3B82F6, #60A5FA);
        }
        
        .cache-card.content::before {
            background: linear-gradient(90deg, #10B981, #34D399);
        }
        
        .cache-card.user::before {
            background: linear-gradient(90deg, #F59E0B, #FBBF24);
        }
        
        .cache-card.media::before {
            background: linear-gradient(90deg, #EC4899, #F472B6);
        }
        
        .cache-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-bottom: 16px;
        }
        
        .progress-bar {
            height: 6px;
            background-color: #374151;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 8px;
        }
        
        .progress-value {
            height: 100%;
            border-radius: 3px;
        }
        
        .progress-low {
            background: linear-gradient(90deg, #10B981, #34D399);
        }
        
        .progress-medium {
            background: linear-gradient(90deg, #F59E0B, #FBBF24);
        }
        
        .progress-high {
            background: linear-gradient(90deg, #EF4444, #F87171);
        }
        
        .cache-switch {
            position: relative;
            display: inline-block;
            width: 46px;
            height: 24px;
        }
        
        .cache-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .switch-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #4B5563;
            transition: .4s;
            border-radius: 24px;
        }
        
        .switch-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .switch-slider {
            background-color: #3B82F6;
        }
        
        input:checked + .switch-slider:before {
            transform: translateX(22px);
        }
        
        .refresh-spinner {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">缓存管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="clearAllCaches" class="flex items-center justify-center bg-gradient-to-r from-red-500 to-red-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-red-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-trash-alt text-white"></i>
                                </span>
                                清空所有缓存
                            </span>
                        </button>
                        <button id="refreshAllCaches" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span id="refreshIcon" class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-sync-alt text-white"></i>
                                </span>
                                刷新缓存
                            </span>
                        </button>
                    </div>
                </div>
                <div class="mt-4">
                    <p class="text-gray-400">缓存管理允许您清空、刷新和配置系统缓存，以提高网站性能和响应速度。使用缓存可以减轻服务器负载，加快页面加载速度。</p>
                </div>
            </div>
            
            <!-- 缓存状态概览 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <!-- 总体缓存使用情况 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">总体缓存</h3>
                        <span class="text-2xl font-bold">72%</span>
                    </div>
                    <div class="progress-bar mb-2">
                        <div class="progress-value progress-medium" style="width: 72%"></div>
                    </div>
                    <div class="flex justify-between text-sm text-gray-400">
                        <span>已用: 360MB</span>
                        <span>总计: 500MB</span>
                    </div>
                </div>

                <!-- 命中率 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">命中率</h3>
                        <span class="text-2xl font-bold">92%</span>
                    </div>
                    <div class="progress-bar mb-2">
                        <div class="progress-value progress-low" style="width: 92%"></div>
                    </div>
                    <div class="flex justify-between text-sm text-gray-400">
                        <span>总请求: 25,432</span>
                        <span>命中: 23,397</span>
                    </div>
                </div>

                <!-- 平均加载时间 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">平均加载时间</h3>
                        <span class="text-2xl font-bold">0.4s</span>
                    </div>
                    <div class="progress-bar mb-2">
                        <div class="progress-value progress-low" style="width: 40%"></div>
                    </div>
                    <div class="flex justify-between text-sm text-gray-400">
                        <span>有缓存: 0.4s</span>
                        <span>无缓存: 1.8s</span>
                    </div>
                </div>

                <!-- 缓存状态 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">缓存状态</h3>
                        <span class="text-2xl font-bold text-green-500">正常</span>
                    </div>
                    <div class="progress-bar mb-2">
                        <div class="progress-value progress-low" style="width: 100%"></div>
                    </div>
                    <div class="flex justify-between text-sm text-gray-400">
                        <span>上次清理: 2小时前</span>
                        <span>自动优化</span>
                    </div>
                </div>
            </div>

            <!-- 缓存类型卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <!-- 系统缓存 -->
                <div class="cache-card system bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="cache-icon bg-blue-500/20 text-blue-400">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold">系统缓存</h3>
                                <p class="text-gray-400 text-sm">系统配置、路由、模板等核心系统文件的缓存</p>
                            </div>
                        </div>
                        <label class="cache-switch">
                            <input type="checkbox" checked>
                            <span class="switch-slider"></span>
                        </label>
                    </div>
                    <div class="grid grid-cols-3 gap-4 mb-4">
                        <div>
                            <div class="text-sm text-gray-400">大小</div>
                            <div class="font-semibold">42MB</div>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">项目数</div>
                            <div class="font-semibold">846</div>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">过期时间</div>
                            <div class="font-semibold">12小时</div>
                        </div>
                    </div>
                    <div class="flex justify-between">
                        <button class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors flex items-center">
                            <i class="fas fa-trash-alt mr-2"></i>
                            清空缓存
                        </button>
                        <button class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center">
                            <i class="fas fa-sync-alt mr-2"></i>
                            刷新缓存
                        </button>
                    </div>
                </div>
                
                <!-- 内容缓存 -->
                <div class="cache-card content bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="cache-icon bg-green-500/20 text-green-400">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold">内容缓存</h3>
                                <p class="text-gray-400 text-sm">文章、页面、评论等内容数据的缓存</p>
                            </div>
                        </div>
                        <label class="cache-switch">
                            <input type="checkbox" checked>
                            <span class="switch-slider"></span>
                        </label>
                    </div>
                    <div class="grid grid-cols-3 gap-4 mb-4">
                        <div>
                            <div class="text-sm text-gray-400">大小</div>
                            <div class="font-semibold">128MB</div>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">项目数</div>
                            <div class="font-semibold">2,356</div>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">过期时间</div>
                            <div class="font-semibold">6小时</div>
                        </div>
                    </div>
                    <div class="flex justify-between">
                        <button class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors flex items-center">
                            <i class="fas fa-trash-alt mr-2"></i>
                            清空缓存
                        </button>
                        <button class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center">
                            <i class="fas fa-sync-alt mr-2"></i>
                            刷新缓存
                        </button>
                    </div>
                </div>
                
                <!-- 用户缓存 -->
                <div class="cache-card user bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="cache-icon bg-yellow-500/20 text-yellow-400">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold">用户缓存</h3>
                                <p class="text-gray-400 text-sm">用户数据、权限、会话等用户相关数据的缓存</p>
                            </div>
                        </div>
                        <label class="cache-switch">
                            <input type="checkbox" checked>
                            <span class="switch-slider"></span>
                        </label>
                    </div>
                    <div class="grid grid-cols-3 gap-4 mb-4">
                        <div>
                            <div class="text-sm text-gray-400">大小</div>
                            <div class="font-semibold">85MB</div>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">项目数</div>
                            <div class="font-semibold">1,285</div>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">过期时间</div>
                            <div class="font-semibold">24小时</div>
                        </div>
                    </div>
                    <div class="flex justify-between">
                        <button class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors flex items-center">
                            <i class="fas fa-trash-alt mr-2"></i>
                            清空缓存
                        </button>
                        <button class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center">
                            <i class="fas fa-sync-alt mr-2"></i>
                            刷新缓存
                        </button>
                    </div>
                </div>
                
                <!-- 媒体缓存 -->
                <div class="cache-card media bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="cache-icon bg-pink-500/20 text-pink-400">
                                <i class="fas fa-images"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold">媒体缓存</h3>
                                <p class="text-gray-400 text-sm">图片、视频等媒体文件的缩略图和元数据缓存</p>
                            </div>
                        </div>
                        <label class="cache-switch">
                            <input type="checkbox" checked>
                            <span class="switch-slider"></span>
                        </label>
                    </div>
                    <div class="grid grid-cols-3 gap-4 mb-4">
                        <div>
                            <div class="text-sm text-gray-400">大小</div>
                            <div class="font-semibold">105MB</div>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">项目数</div>
                            <div class="font-semibold">3,472</div>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">过期时间</div>
                            <div class="font-semibold">48小时</div>
                        </div>
                    </div>
                    <div class="flex justify-between">
                        <button class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors flex items-center">
                            <i class="fas fa-trash-alt mr-2"></i>
                            清空缓存
                        </button>
                        <button class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center">
                            <i class="fas fa-sync-alt mr-2"></i>
                            刷新缓存
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 缓存设置 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold mb-6">缓存设置</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2 font-medium">缓存驱动</label>
                            <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option selected>Redis</option>
                                <option>Memcached</option>
                                <option>文件缓存</option>
                                <option>数据库缓存</option>
                            </select>
                            <p class="text-xs text-gray-500 mt-1">选择用于存储缓存数据的驱动程序</p>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2 font-medium">缓存前缀</label>
                            <input type="text" value="gacms_" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <p class="text-xs text-gray-500 mt-1">为缓存键添加前缀，避免键名冲突</p>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2 font-medium">最大缓存大小</label>
                            <div class="flex items-center">
                                <input type="number" value="500" class="w-full bg-gray-700/50 border border-gray-600 rounded-l-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <span class="bg-gray-600 px-4 py-3 rounded-r-lg text-gray-300">MB</span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">缓存可以使用的最大内存</p>
                        </div>
                    </div>
                    
                    <div>
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2 font-medium">内容缓存时间</label>
                            <div class="flex items-center">
                                <input type="number" value="6" class="w-full bg-gray-700/50 border border-gray-600 rounded-l-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <span class="bg-gray-600 px-4 py-3 rounded-r-lg text-gray-300">小时</span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">内容缓存的默认过期时间</p>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2 font-medium">系统缓存时间</label>
                            <div class="flex items-center">
                                <input type="number" value="12" class="w-full bg-gray-700/50 border border-gray-600 rounded-l-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <span class="bg-gray-600 px-4 py-3 rounded-r-lg text-gray-300">小时</span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">系统缓存的默认过期时间</p>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2 font-medium">自动清理周期</label>
                            <div class="flex items-center">
                                <input type="number" value="24" class="w-full bg-gray-700/50 border border-gray-600 rounded-l-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <span class="bg-gray-600 px-4 py-3 rounded-r-lg text-gray-300">小时</span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">系统自动执行缓存清理的周期</p>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6">
                    <h4 class="font-medium mb-3">缓存选项</h4>
                    <div class="space-y-3">
                        <label class="flex items-center gap-2 cursor-pointer">
                            <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                            <span>启用页面缓存</span>
                        </label>
                        <label class="flex items-center gap-2 cursor-pointer">
                            <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                            <span>启用数据查询缓存</span>
                        </label>
                        <label class="flex items-center gap-2 cursor-pointer">
                            <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                            <span>启用API响应缓存</span>
                        </label>
                        <label class="flex items-center gap-2 cursor-pointer">
                            <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500">
                            <span>启用调试模式（会记录缓存命中日志）</span>
                        </label>
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end">
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
                        保存设置
                    </button>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 刷新缓存按钮动画
            const refreshAllBtn = document.getElementById('refreshAllCaches');
            const refreshIcon = document.getElementById('refreshIcon').querySelector('i');
            
            refreshAllBtn.addEventListener('click', function() {
                // 添加旋转动画
                refreshIcon.classList.add('refresh-spinner');
                
                // 模拟API调用延迟
                setTimeout(function() {
                    // 移除旋转动画
                    refreshIcon.classList.remove('refresh-spinner');
                    
                    // 显示成功消息
                    alert('所有缓存已成功刷新！');
                }, 1500);
            });
            
            // 清空所有缓存按钮事件
            const clearAllBtn = document.getElementById('clearAllCaches');
            clearAllBtn.addEventListener('click', function() {
                if(confirm('确定要清空所有缓存吗？这可能会暂时影响网站性能。')) {
                    // 模拟API调用延迟
                    setTimeout(function() {
                        // 显示成功消息
                        alert('所有缓存已成功清空！');
                    }, 1000);
                }
            });
            
            // 各缓存类型的开关切换
            const cacheToggles = document.querySelectorAll('.cache-switch input');
            cacheToggles.forEach(toggle => {
                toggle.addEventListener('change', function() {
                    const cacheCard = this.closest('.cache-card');
                    const cacheName = cacheCard.querySelector('h3').textContent;
                    
                    if(this.checked) {
                        console.log(`${cacheName}已启用`);
                    } else {
                        console.log(`${cacheName}已禁用`);
                    }
                });
            });
        });
    </script>
</body>
</html> 