<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" style="background-color: #3B82F6;">
<!--
  Author: Cion Nieh
  EMAIL: <EMAIL>
  Copyright (c) 2025 Cion Nieh
-->
<defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#93C5FD;stop-opacity:1" />
        <stop offset="20%" style="stop-color:#60A5FA;stop-opacity:1" />
        <stop offset="40%" style="stop-color:#3B82F6;stop-opacity:1" />
        <stop offset="60%" style="stop-color:#2563EB;stop-opacity:1" />
        <stop offset="80%" style="stop-color:#3B82F6;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#93C5FD;stop-opacity:1" />
        <animate attributeName="x1" values="0%;10%;0%" dur="20s" repeatCount="indefinite" />
        <animate attributeName="y1" values="0%;10%;0%" dur="20s" repeatCount="indefinite" />
    </linearGradient>
    <filter id="blurMe">
        <feGaussianBlur in="SourceGraphic" stdDeviation="20" />
        <feColorMatrix type="matrix" values="1.2 0 0 0 0  0 1.2 0 0 0  0 0 1.2 0 0  0 0 0 20 -8"/>
        <feComposite in2="SourceGraphic" operator="arithmetic" k1="0.5" k2="0.5" k3="0.5" k4="0"/>
    </filter>
    <filter id="softBlur">
        <feGaussianBlur in="SourceGraphic" stdDeviation="10" />
        <feColorMatrix type="matrix" values="1.1 0 0 0 0  0 1.1 0 0 0  0 0 1.1 0 0  0 0 0 18 -7"/>
        <feComposite in2="SourceGraphic" operator="arithmetic" k1="0.6" k2="0.4" k3="0.4" k4="0"/>
    </filter>
    <radialGradient id="glow" cx="50%" cy="50%" r="70%" fx="50%" fy="50%">
        <stop offset="0%" style="stop-color:rgba(147,197,253,0.3);stop-opacity:1" />
        <stop offset="35%" style="stop-color:rgba(96,165,250,0.2);stop-opacity:1" />
        <stop offset="65%" style="stop-color:rgba(59,130,246,0.1);stop-opacity:1" />
        <stop offset="100%" style="stop-color:rgba(37,99,235,0);stop-opacity:1" />
        <animate attributeName="fx" values="50%;45%;50%" dur="15s" repeatCount="indefinite" />
        <animate attributeName="fy" values="50%;45%;50%" dur="15s" repeatCount="indefinite" />
    </radialGradient>
    <pattern id="gridPattern" width="80" height="80" patternUnits="userSpaceOnUse">
        <path d="M 80 0 L 0 0 0 80" fill="none" stroke="rgba(255,255,255,0.18)" stroke-width="0.6">
            <animate attributeName="stroke-opacity" values="0.18;0.1;0.18" dur="5s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
            <animate attributeName="stroke-width" values="0.6;0.4;0.6" dur="5s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
        </path>
    </pattern>
</defs>
<rect width="1920" height="1080" fill="url(#grad1)"/>
<rect width="1920" height="1080" fill="url(#gridPattern)" opacity="0.4" filter="url(#softBlur)"/>

<!-- Abstract flowing shapes with enhanced animation and colors -->
<path d="M-250 350 C 150 50, 450 550, 750 350 S 1350 -50, 1650 450 S 2250 150, 2250 550 V 1180 H -250 Z" fill="rgba(96,165,250,0.08)">
    <animate attributeName="d" values="M-250 350 C 150 50, 450 550, 750 350 S 1350 -50, 1650 450 S 2250 150, 2250 550 V 1180 H -250 Z; M-250 450 C 250 150, 550 650, 850 450 S 1450 50, 1750 550 S 2350 250, 2350 650 V 1180 H -250 Z; M-250 350 C 150 50, 450 550, 750 350 S 1350 -50, 1650 450 S 2250 150, 2250 550 V 1180 H -250 Z" dur="20s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="0.08;0.05;0.08" dur="10s" repeatCount="indefinite"/>
</path>
<path d="M2050 750 C 1750 950, 1450 450, 1150 750 S 550 1050, 250 650 S -250 850, -250 550 V 1180 H 2050 Z" fill="rgba(59,130,246,0.06)">
    <animate attributeName="d" values="M2050 750 C 1750 950, 1450 450, 1150 750 S 550 1050, 250 650 S -250 850, -250 550 V 1180 H 2050 Z; M2050 650 C 1650 850, 1350 350, 1050 650 S 450 950, 150 550 S -350 750, -350 450 V 1180 H 2050 Z; M2050 750 C 1750 950, 1450 450, 1150 750 S 550 1050, 250 650 S -250 850, -250 550 V 1180 H 2050 Z" dur="25s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="0.06;0.04;0.06" dur="12s" repeatCount="indefinite"/>
</path>
<path d="M-150 550 C 250 350, 550 750, 850 550 S 1450 250, 1750 650 S 2350 450, 2350 750 V 1180 H -150 Z" fill="rgba(37,99,235,0.04)">
    <animate attributeName="d" values="M-150 550 C 250 350, 550 750, 850 550 S 1450 250, 1750 650 S 2350 450, 2350 750 V 1180 H -150 Z; M-150 450 C 150 250, 450 650, 750 450 S 1350 150, 1650 550 S 2250 350, 2250 650 V 1180 H -150 Z; M-150 550 C 250 350, 550 750, 850 550 S 1450 250, 1750 650 S 2350 450, 2350 750 V 1180 H -150 Z" dur="30s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="0.04;0.02;0.04" dur="15s" repeatCount="indefinite"/>
</path>

<!-- Enhanced Glowing circles with smooth animations and layered effects -->
<circle cx="350" cy="250" r="320" fill="url(#glow)" filter="url(#blurMe)" opacity="0.85">
    <animate attributeName="r" values="320;380;320" dur="12s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1" />
    <animate attributeName="opacity" values="0.85;0.6;0.85" dur="12s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1" />
</circle>
<circle cx="1650" cy="850" r="380" fill="url(#glow)" filter="url(#blurMe)" opacity="0.75">
    <animate attributeName="r" values="380;440;380" dur="15s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1" />
    <animate attributeName="opacity" values="0.75;0.5;0.75" dur="15s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1" />
</circle>
<circle cx="960" cy="540" r="420" fill="url(#glow)" filter="url(#blurMe)" opacity="0.65">
    <animate attributeName="r" values="420;490;420" dur="18s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1" />
    <animate attributeName="opacity" values="0.65;0.4;0.65" dur="18s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1" />
</circle>

<!-- Enhanced animated particles with smooth transitions and varied paths -->
<g class="particles">
    <defs>
        <radialGradient id="particleGlow" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stop-color="rgba(255,255,255,0.8)"/>
            <stop offset="50%" stop-color="rgba(255,255,255,0.3)"/>
            <stop offset="100%" stop-color="rgba(255,255,255,0)"/>
        </radialGradient>
    </defs>
    <circle r="3" fill="url(#particleGlow)">
        <animateMotion dur="18s" repeatCount="indefinite" path="M120,120 Q960,220 1800,120 T120,120" calcMode="spline" keySplines="0.4 0 0.6 1"/>
        <animate attributeName="r" values="3;1;3" dur="9s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
        <animate attributeName="opacity" values="0.8;0.3;0.8" dur="9s" repeatCount="indefinite"/>
    </circle>
    <circle r="2.5" fill="url(#particleGlow)">
        <animateMotion dur="22s" repeatCount="indefinite" path="M220,880 Q960,680 1700,880 T220,880" calcMode="spline" keySplines="0.4 0 0.6 1"/>
        <animate attributeName="r" values="2.5;0.8;2.5" dur="11s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
        <animate attributeName="opacity" values="0.7;0.2;0.7" dur="11s" repeatCount="indefinite"/>
    </circle>
    <circle r="2" fill="url(#particleGlow)">
        <animateMotion dur="25s" repeatCount="indefinite" path="M1780,220 Q960,420 140,220 T1780,220" calcMode="spline" keySplines="0.4 0 0.6 1"/>
        <animate attributeName="r" values="2;1;2" dur="12.5s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
        <animate attributeName="opacity" values="0.6;0.2;0.6" dur="12.5s" repeatCount="indefinite"/>
    </circle>
    <circle r="3.5" fill="url(#particleGlow)">
        <animateMotion dur="20s" repeatCount="indefinite" path="M70,520 Q960,620 1850,520 T70,520" calcMode="spline" keySplines="0.4 0 0.6 1"/>
        <animate attributeName="r" values="3.5;1.2;3.5" dur="10s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
        <animate attributeName="opacity" values="0.8;0.3;0.8" dur="10s" repeatCount="indefinite"/>
    </circle>
    <circle r="2.8" fill="url(#particleGlow)">
        <animateMotion dur="23s" repeatCount="indefinite" path="M1500,50 Q700,300 100,50 T1500,50" calcMode="spline" keySplines="0.4 0 0.6 1"/>
        <animate attributeName="r" values="2.8;0.9;2.8" dur="11.5s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
        <animate attributeName="opacity" values="0.7;0.2;0.7" dur="11.5s" repeatCount="indefinite"/>
    </circle>
    <circle r="3.2" fill="url(#particleGlow)">
        <animateMotion dur="19s" repeatCount="indefinite" path="M500,950 Q1200,750 1900,950 T500,950" calcMode="spline" keySplines="0.4 0 0.6 1"/>
        <animate attributeName="r" values="3.2;1.1;3.2" dur="9.5s" repeatCount="indefinite" calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1"/>
        <animate attributeName="opacity" values="0.8;0.3;0.8" dur="9.5s" repeatCount="indefinite"/>
    </circle>
</g>

<style>
    .particles circle {
        animation-timing-function: cubic-bezier(0.42, 0, 0.58, 1); /* Smoother easing */
    }
</style>

</svg>