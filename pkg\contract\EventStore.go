/*
 * @Author: <PERSON><PERSON>eh <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: pkg/contract/EventStore.go
 * @Description: 定义事件存储接口，用于实现事件溯源模式
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"context"
	"time"
)

// EventStore 定义了事件存储库的接口
// 事件存储是事件溯源模式的核心组件，负责持久化和检索事件
type EventStore interface {
	// SaveEvent 将事件保存到存储库
	// 返回保存后的事件ID和可能的错误
	SaveEvent(ctx context.Context, event Event) (string, error)

	// SaveEvents 批量保存多个事件
	// 原子操作，要么全部成功，要么全部失败
	SaveEvents(ctx context.Context, events []Event) error

	// GetEventsByAggregate 获取指定聚合的所有事件
	// 按时间顺序返回事件列表
	GetEventsByAggregate(ctx context.Context, aggregateType string, aggregateID string) ([]Event, error)

	// GetEventsByType 获取指定类型的所有事件
	// 可选地指定时间范围
	GetEventsByType(ctx context.Context, eventType EventName, fromTime, toTime time.Time) ([]Event, error)

	// GetEventByID 通过ID获取单个事件
	GetEventByID(ctx context.Context, eventID string) (Event, error)

	// GetAllEvents 获取所有事件
	// 可选地指定时间范围和分页参数
	GetAllEvents(ctx context.Context, fromTime, toTime time.Time, offset, limit int) ([]Event, error)

	// CountEvents 计算事件总数
	// 可选地指定事件类型和时间范围
	CountEvents(ctx context.Context, eventType EventName, fromTime, toTime time.Time) (int, error)
}

// EventStoreFactory 定义了创建EventStore实例的工厂接口
type EventStoreFactory interface {
	// CreateEventStore 创建一个新的EventStore实例
	CreateEventStore() EventStore
}

// EventDescriptor 定义了存储在事件存储中的事件描述符
// 包含事件本身和元数据
type EventDescriptor struct {
	// EventID 是事件的唯一标识符
	EventID string

	// AggregateType 是事件所属聚合的类型
	AggregateType string

	// AggregateID 是事件所属聚合的ID
	AggregateID string

	// EventType 是事件的类型
	EventType EventName

	// EventData 是事件的序列化数据
	EventData []byte

	// Timestamp 是事件的创建时间
	Timestamp time.Time

	// Version 是聚合的版本号
	Version int

	// Metadata 是事件的额外元数据
	Metadata map[string]interface{}
} 