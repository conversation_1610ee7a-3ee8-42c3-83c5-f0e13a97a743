/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/infrastructure/cache/FileAdapter.go
 * @Description: Provides a file-based cache implementation, suitable for development or environments without Redis.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package cache

import (
	"context"
	"encoding/gob"
	"fmt"
	"gacms/internal/infrastructure/config"
	"gacms/pkg/contract"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// FileAdapter implements the contract.Cache interface using the local filesystem.
type FileAdapter struct {
	path string
	mu   sync.RWMutex
}

type fileAdapterItem struct {
	Value      interface{}
	Expiration int64
}

// NewFileAdapter creates a new instance of FileAdapter.
// It reads the cache path from the application configuration.
func NewFileAdapter(cfg contract.Config) (contract.Cache, error) {
	path := cfg.GetString("cache.file.path")
	if path == "" {
		path = "storage/cache" // Default path
	}
	if err := os.Mkdir<PERSON>ll(path, 0755); err != nil {
		return nil, err
	}
	return &FileAdapter{path: path}, nil
}

func (f *FileAdapter) getFilePath(key string) string {
	return filepath.Join(f.path, key+".gacache")
}

// Set stores a value in the file cache.
func (f *FileAdapter) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	f.mu.Lock()
	defer f.mu.Unlock()

	file, err := os.Create(f.getFilePath(key))
	if err != nil {
		return err
	}
	defer file.Close()

	var expiration int64
	if ttl > 0 {
		expiration = time.Now().Add(ttl).UnixNano()
	}

	encoder := gob.NewEncoder(file)
	return encoder.Encode(&fileAdapterItem{
		Value:      value,
		Expiration: expiration,
	})
}

// Get retrieves a value from the file cache.
func (f *FileAdapter) Get(ctx context.Context, key string) (interface{}, bool) {
	f.mu.RLock()
	defer f.mu.RUnlock()

	filePath := f.getFilePath(key)
	file, err := os.Open(filePath)
	if err != nil {
		return nil, false // Not found or other error
	}
	defer file.Close()

	var item fileAdapterItem
	decoder := gob.NewDecoder(file)
	if err := decoder.Decode(&item); err != nil {
		return nil, false
	}

	if item.Expiration > 0 && time.Now().UnixNano() > item.Expiration {
		// Eagerly delete expired item
		_ = os.Remove(filePath)
		return nil, false
	}

	return item.Value, true
}

// Delete removes a value from the file cache.
func (f *FileAdapter) Delete(ctx context.Context, key string) error {
	f.mu.Lock()
	defer f.mu.Unlock()

	err := os.Remove(f.getFilePath(key))
	if os.IsNotExist(err) {
		return nil // Deleting a non-existent key is not an error
	}
	return err
}

// Has checks if a key exists and is not expired in the file cache.
func (f *FileAdapter) Has(ctx context.Context, key string) bool {
	_, ok := f.Get(ctx, key)
	return ok
} 