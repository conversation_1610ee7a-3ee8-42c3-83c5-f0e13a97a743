/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/actionlog/domain/contract/ActionLogRepository.go
 * @Description: Defines the repository interface for action logs.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"context"
	"gacms/internal/modules/actionlog/domain/model"
)

// ActionLogRepository defines the interface for action log data operations.
type ActionLogRepository interface {
	Create(ctx context.Context, log *model.ActionLog) error
	List(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*model.ActionLog, int64, error)
} 