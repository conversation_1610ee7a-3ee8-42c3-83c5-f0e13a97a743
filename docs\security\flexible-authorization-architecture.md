# GACMS 灵活授权架构设计

## 🎯 设计目标

解决用户提出的关键问题：
> "核心不应该知道具体会有什么扩展模块，硬编码具体模块名称会限制扩展性"

## 🏗️ 新架构设计

### 1. 基于类型的动态授权

#### A. 模块类型分层
```go
// ✅ 基于模块类型而不是具体名称
func (r *ModuleAuthorizationRules) RequiresLicense(moduleName string, moduleType ModuleType) bool {
    switch moduleType {
    case ModuleTypeCore:
        // 核心模块永远免费（基于类型，不是具体名称）
        return false
        
    case ModuleTypeOptional:
        // 可选模块需要检查具体规则或配置
        return r.checkOptionalModuleLicense(moduleName)
        
    case ModuleTypeVendors:
        // Vendors模块通过签名验证
        return r.checkVendorsModuleLicense(moduleName)
        
    default:
        // 未知类型默认需要许可证（安全优先）
        return true
    }
}
```

#### B. 可选模块的灵活处理
```go
// ✅ 支持多种授权判断方式
func (r *ModuleAuthorizationRules) checkOptionalModuleLicense(moduleName string) bool {
    // 1. 检查官方免费模块列表
    if r.isOfficialFreeModule(moduleName) {
        return false
    }
    
    // 2. 检查模块配置的签名验证
    if r.hasValidFreeModuleSignature(moduleName) {
        return false
    }
    
    // 3. 默认可选模块需要许可证
    return true
}
```

### 2. 签名验证的模块配置

#### A. 模块配置声明
```json
{
  "module_name": "advanced_theme",
  "version": "1.0.0",
  "author": "ThirdParty Developer",
  "requires_license": false,
  "license_type": "none",
  "minimum_edition": "community",
  "features": ["custom_templates", "theme_editor"],
  "dependencies": ["theme"],
  "signature": "a1b2c3d4e5f6...",
  "signed_by": "community"
}
```

#### B. 配置验证机制
```go
// ✅ 防篡改的配置验证
type ModuleConfigValidator struct {
    publicKeys map[string]*rsa.PublicKey // 信任的公钥列表
}

func (v *ModuleConfigValidator) ValidateModuleConfig(modulePath string) (*ModuleConfigDeclaration, error) {
    // 1. 读取模块配置文件
    // 2. 解析配置
    // 3. 验证数字签名
    // 4. 返回验证后的配置
}
```

### 3. 多层安全验证

#### A. 官方免费模块列表
```go
// ✅ 官方维护的免费模块列表
func (r *ModuleAuthorizationRules) getOfficialFreeModuleList() []string {
    return []string{
        "theme",      // 基础主题功能
        "backup",     // 基础备份功能
        "cache",      // 基础缓存功能
        // 可以根据需要添加更多免费模块
    }
}
```

#### B. 第三方免费模块支持
```go
// ✅ 支持第三方开发的免费模块（通过签名验证）
func (v *ModuleConfigValidator) IsModuleFree(modulePath string) (bool, error) {
    config, err := v.ValidateModuleConfig(modulePath)
    if err != nil {
        return false, err // 验证失败，默认需要许可证
    }
    
    // 只有经过签名验证的配置才能声明免费
    return !config.RequiresLicense, nil
}
```

## 🔒 安全特性

### 1. 防配置篡改
- ✅ **数字签名验证**：模块配置必须有有效的数字签名
- ✅ **信任的公钥**：只信任官方和认证的社区公钥
- ✅ **完整性检查**：验证配置文件未被篡改

### 2. 防绕过攻击
- ✅ **类型级安全**：基于模块类型而不是可修改的配置
- ✅ **多层验证**：官方列表 + 签名验证 + 许可证检查
- ✅ **默认安全**：未知模块默认需要许可证

### 3. 防伪造模块
- ✅ **签名验证**：防止伪造免费模块声明
- ✅ **公钥管理**：严格控制信任的签名者
- ✅ **审计日志**：记录所有授权决策

## 🚀 扩展性支持

### 1. 动态模块支持
```bash
✅ 核心不需要知道具体模块名称
✅ 支持未来新增的任意模块
✅ 基于类型的通用授权规则
✅ 第三方开发者友好
```

### 2. 灵活的商业模式
```bash
✅ 官方免费模块：基础功能免费提供
✅ 官方付费模块：高级功能按版本收费
✅ 第三方免费模块：社区贡献，签名验证
✅ 第三方付费模块：独立商业化，自主授权
```

### 3. 开发者生态
```bash
✅ 简单的模块开发：标准的配置格式
✅ 免费模块支持：通过签名验证声明免费
✅ 商业化支持：独立的许可证验证
✅ 安全保障：防篡改和防伪造
```

## 📋 实施示例

### 1. 核心模块（永远免费）
```go
// 任何标记为ModuleTypeCore的模块都免费
moduleType := ModuleTypeCore
requiresLicense := authRules.RequiresLicense("any_core_module", moduleType)
// 结果：false（不需要许可证）
```

### 2. 官方免费可选模块
```go
// 在官方免费列表中的模块
requiresLicense := authRules.RequiresLicense("theme", ModuleTypeOptional)
// 结果：false（官方免费模块）
```

### 3. 第三方免费模块
```json
// 第三方开发者的免费模块配置（需要签名）
{
  "module_name": "community_plugin",
  "requires_license": false,
  "signature": "valid_signature_here",
  "signed_by": "community"
}
```

### 4. 付费模块
```go
// 不在免费列表且没有有效免费签名的模块
requiresLicense := authRules.RequiresLicense("advanced_seo", ModuleTypeOptional)
// 结果：true（需要许可证）
```

## 🎯 架构优势

### 1. 高扩展性
- ✅ 核心不依赖具体模块名称
- ✅ 支持无限扩展新模块
- ✅ 第三方开发者友好

### 2. 高安全性
- ✅ 防配置文件篡改
- ✅ 防伪造免费模块
- ✅ 多层安全验证

### 3. 商业化友好
- ✅ 灵活的免费/付费策略
- ✅ 支持第三方商业化
- ✅ 版本分级支持

### 4. 维护性好
- ✅ 清晰的授权逻辑
- ✅ 统一的验证机制
- ✅ 完善的审计日志

## 🔧 配置管理

### 1. 官方免费模块
```go
// 通过代码维护，定期更新
func (r *ModuleAuthorizationRules) getOfficialFreeModuleList() []string {
    // 可以从配置文件、数据库或远程API获取
    // 但需要签名验证确保完整性
}
```

### 2. 信任的公钥
```go
// 严格控制的公钥列表
func (v *ModuleConfigValidator) loadTrustedPublicKeys() {
    // 官方公钥
    v.publicKeys["official"] = loadOfficialKey()
    
    // 认证的社区公钥
    v.publicKeys["community"] = loadCommunityKey()
    
    // 可以动态添加新的信任公钥
}
```

### 3. 模块配置模板
```go
// 为开发者提供标准模板
template := validator.CreateModuleConfigTemplate("my_module", "developer", false)
// 开发者填写配置并签名
```

## ✅ 问题解决确认

### 原问题：硬编码限制扩展性 ✅
**解决方案**：
- ✅ 基于模块类型而不是具体名称
- ✅ 支持动态模块注册和验证
- ✅ 第三方模块通过签名验证自主声明

### 安全性：防篡改和防绕过 ✅
**解决方案**：
- ✅ 数字签名验证防篡改
- ✅ 多层安全检查防绕过
- ✅ 默认安全策略防伪造

### 商业化：灵活的商业模式 ✅
**解决方案**：
- ✅ 支持免费和付费模块共存
- ✅ 第三方开发者生态支持
- ✅ 版本分级和功能控制

**结论**：新架构既保证了安全性，又提供了充分的扩展性，完美解决了硬编码限制的问题。
