/*
Author: <PERSON><PERSON>eh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package service

import (
	"context"
	"crypto"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"strings"
	"sync"
	"time"

	"gacms/pkg/contract"
	"go.uber.org/zap"
)

// OfficialVendorsValidator 官方Vendors验证器
type OfficialVendorsValidator struct {
	name       string
	version    string
	publicKey  *rsa.PublicKey
	config     map[string]interface{}
	logger     *zap.Logger
}

// NewOfficialVendorsValidator 创建官方Vendors验证器
func NewOfficialVendorsValidator(logger *zap.Logger) ThirdPartyLicenseValidator {
	// 官方公钥（实际应用中应该从安全的地方加载）
	publicKeyPEM := `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdef...
-----END PUBLIC KEY-----`
	
	block, _ := pem.Decode([]byte(publicKeyPEM))
	if block == nil {
		logger.Error("Failed to decode official public key")
		return nil
	}
	
	publicKey, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		logger.Error("Failed to parse official public key", zap.Error(err))
		return nil
	}
	
	rsaPublicKey, ok := publicKey.(*rsa.PublicKey)
	if !ok {
		logger.Error("Official public key is not RSA key")
		return nil
	}
	
	return &OfficialVendorsValidator{
		name:      "official",
		version:   "1.0.0",
		publicKey: rsaPublicKey,
		config:    make(map[string]interface{}),
		logger:    logger,
	}
}

// GetValidatorName 获取验证器名称
func (v *OfficialVendorsValidator) GetValidatorName() string {
	return v.name
}

// GetValidatorVersion 获取验证器版本
func (v *OfficialVendorsValidator) GetValidatorVersion() string {
	return v.version
}

// GetSupportedModules 获取支持的模块
func (v *OfficialVendorsValidator) GetSupportedModules() []string {
	return []string{"*"} // 支持所有模块
}

// ValidateLicense 验证许可证
func (v *OfficialVendorsValidator) ValidateLicense(ctx context.Context, licenseData []byte) (*contract.LicenseInfo, error) {
	// 1. 解析许可证JSON
	var licensePayload struct {
		Header    map[string]interface{} `json:"header"`
		Payload   map[string]interface{} `json:"payload"`
		Signature string                 `json:"signature"`
	}
	
	if err := json.Unmarshal(licenseData, &licensePayload); err != nil {
		return nil, fmt.Errorf("invalid license format: %w", err)
	}
	
	// 2. 验证签名
	payloadBytes, err := json.Marshal(licensePayload.Payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payload: %w", err)
	}
	
	if err := v.VerifySignature(payloadBytes, []byte(licensePayload.Signature)); err != nil {
		return nil, fmt.Errorf("signature verification failed: %w", err)
	}
	
	// 3. 解析许可证信息
	licenseInfo := &contract.LicenseInfo{}
	
	// 许可证类型
	if licenseType, ok := licensePayload.Payload["type"].(string); ok {
		licenseInfo.Type = contract.LicenseType(licenseType)
	} else {
		licenseInfo.Type = contract.LicenseTypePaid
	}
	
	// 版本信息
	if edition, ok := licensePayload.Payload["edition"].(string); ok {
		licenseInfo.Edition = contract.Edition(edition)
	}
	
	// 过期时间
	if expiresAt, ok := licensePayload.Payload["expires_at"].(string); ok {
		if t, err := time.Parse(time.RFC3339, expiresAt); err == nil {
			licenseInfo.ExpiresAt = &t
		}
	}
	
	// 功能列表
	if features, ok := licensePayload.Payload["features"].([]interface{}); ok {
		for _, feature := range features {
			if featureStr, ok := feature.(string); ok {
				licenseInfo.Features = append(licenseInfo.Features, featureStr)
			}
		}
	}
	
	// 限制条件
	if restrictions, ok := licensePayload.Payload["restrictions"].(map[string]interface{}); ok {
		licenseInfo.Restrictions = restrictions
	}
	
	// 4. 检查许可证是否过期
	if licenseInfo.ExpiresAt != nil && time.Now().After(*licenseInfo.ExpiresAt) {
		return nil, fmt.Errorf("license has expired")
	}
	
	return licenseInfo, nil
}

// VerifySignature 验证签名
func (v *OfficialVendorsValidator) VerifySignature(data []byte, signature []byte) error {
	// 计算数据哈希
	hash := sha256.Sum256(data)
	
	// 验证RSA签名
	return rsa.VerifyPKCS1v15(v.publicKey, crypto.SHA256, hash[:], signature)
}

// Configure 配置验证器
func (v *OfficialVendorsValidator) Configure(config map[string]interface{}) error {
	v.config = config
	return nil
}

// GetConfiguration 获取配置
func (v *OfficialVendorsValidator) GetConfiguration() map[string]interface{} {
	return v.config
}

// HealthCheck 健康检查
func (v *OfficialVendorsValidator) HealthCheck(ctx context.Context) error {
	// 检查公钥是否有效
	if v.publicKey == nil {
		return fmt.Errorf("public key is not loaded")
	}
	
	// 可以添加更多健康检查逻辑
	return nil
}

// CommunityVendorsValidator 社区Vendors验证器
type CommunityVendorsValidator struct {
	name    string
	version string
	config  map[string]interface{}
	logger  *zap.Logger
}

// NewCommunityVendorsValidator 创建社区Vendors验证器
func NewCommunityVendorsValidator(logger *zap.Logger) ThirdPartyLicenseValidator {
	return &CommunityVendorsValidator{
		name:    "community",
		version: "1.0.0",
		config:  make(map[string]interface{}),
		logger:  logger,
	}
}

// GetValidatorName 获取验证器名称
func (v *CommunityVendorsValidator) GetValidatorName() string {
	return v.name
}

// GetValidatorVersion 获取验证器版本
func (v *CommunityVendorsValidator) GetValidatorVersion() string {
	return v.version
}

// GetSupportedModules 获取支持的模块
func (v *CommunityVendorsValidator) GetSupportedModules() []string {
	return []string{"community/*"} // 只支持社区模块
}

// ValidateLicense 验证许可证
func (v *CommunityVendorsValidator) ValidateLicense(ctx context.Context, licenseData []byte) (*contract.LicenseInfo, error) {
	// 社区验证器简单验证
	licenseStr := string(licenseData)
	
	// 检查是否是有效的社区许可证格式
	if !strings.HasPrefix(licenseStr, "GACMS-COMMUNITY-") {
		return nil, fmt.Errorf("invalid community license format")
	}
	
	// 解析许可证信息
	parts := strings.Split(licenseStr, "-")
	if len(parts) < 4 {
		return nil, fmt.Errorf("invalid community license structure")
	}
	
	licenseInfo := &contract.LicenseInfo{
		Type:    contract.LicenseTypeFree,
		Edition: contract.EditionPersonal, // 社区模块默认支持个人版
		Features: []string{
			"basic_functionality",
			"community_support",
		},
	}
	
	// 检查版本兼容性
	if len(parts) >= 4 {
		version := parts[3]
		if !v.isVersionCompatible(version) {
			return nil, fmt.Errorf("incompatible license version: %s", version)
		}
	}
	
	return licenseInfo, nil
}

// VerifySignature 验证签名
func (v *CommunityVendorsValidator) VerifySignature(data []byte, signature []byte) error {
	// 社区验证器不需要签名验证
	return nil
}

// Configure 配置验证器
func (v *CommunityVendorsValidator) Configure(config map[string]interface{}) error {
	v.config = config
	return nil
}

// GetConfiguration 获取配置
func (v *CommunityVendorsValidator) GetConfiguration() map[string]interface{} {
	return v.config
}

// HealthCheck 健康检查
func (v *CommunityVendorsValidator) HealthCheck(ctx context.Context) error {
	// 社区验证器总是健康的
	return nil
}

// isVersionCompatible 检查版本兼容性
func (v *CommunityVendorsValidator) isVersionCompatible(version string) bool {
	// 简单的版本兼容性检查
	supportedVersions := []string{"1.0", "1.1", "1.2"}
	
	for _, supported := range supportedVersions {
		if strings.HasPrefix(version, supported) {
			return true
		}
	}
	
	return false
}

// ThirdPartyValidatorRegistry 第三方验证器注册表
type ThirdPartyValidatorRegistry struct {
	validators map[string]ThirdPartyLicenseValidator
	mu         sync.RWMutex
	logger     *zap.Logger
}

// NewThirdPartyValidatorRegistry 创建第三方验证器注册表
func NewThirdPartyValidatorRegistry(logger *zap.Logger) *ThirdPartyValidatorRegistry {
	return &ThirdPartyValidatorRegistry{
		validators: make(map[string]ThirdPartyLicenseValidator),
		logger:     logger,
	}
}

// Register 注册验证器
func (r *ThirdPartyValidatorRegistry) Register(validator ThirdPartyLicenseValidator) error {
	name := validator.GetValidatorName()
	
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.validators[name]; exists {
		return fmt.Errorf("validator already registered: %s", name)
	}
	
	r.validators[name] = validator
	
	r.logger.Info("Validator registered",
		zap.String("name", name),
		zap.String("version", validator.GetValidatorVersion()),
	)
	
	return nil
}

// Unregister 注销验证器
func (r *ThirdPartyValidatorRegistry) Unregister(name string) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.validators[name]; !exists {
		return fmt.Errorf("validator not found: %s", name)
	}
	
	delete(r.validators, name)
	
	r.logger.Info("Validator unregistered", zap.String("name", name))
	
	return nil
}

// Get 获取验证器
func (r *ThirdPartyValidatorRegistry) Get(name string) (ThirdPartyLicenseValidator, bool) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	validator, exists := r.validators[name]
	return validator, exists
}

// List 列出所有验证器
func (r *ThirdPartyValidatorRegistry) List() []string {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	var names []string
	for name := range r.validators {
		names = append(names, name)
	}
	
	return names
}
