/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/contenttype/seeder.go
 * @Description: Seeds the database with essential, default content types.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contenttype

import (
	"gacms/internal/modules/contenttype/application/service"
	"gacms/internal/modules/contenttype/domain/model"
	"log"

	"gorm.io/gorm"
)

// SeedContentTypes ensures that the essential content types like 'Category' and 'Post' exist.
func SeedContentTypes(svc *service.ContentTypeService) {
	log.Println("Seeding essential content types...")
	
	ensureCategoryType(svc)
	ensurePostType(svc)
	ensureMenuGroupType(svc)
	ensureMenuItemType(svc)
	ensureBannerPositionType(svc)
	ensureBannerType(svc)
	ensurePageType(svc)
	ensureTopicType(svc)
	ensureTagType(svc)
}

func ensureCategoryType(svc *service.ContentTypeService) {
	const categorySlug = "category"
	
	_, err := svc.GetBySlug(categorySlug)
	if err == nil {
		log.Printf("Content type '%s' already exists. Skipping.", categorySlug)
		return
	}
	
	if err != gorm.ErrRecordNotFound {
		log.Printf("Error checking for content type '%s': %v. Skipping.", categorySlug, err)
		return
	}
	
	log.Printf("Creating '%s' content type...", categorySlug)
	
	categoryType := &model.ContentType{
		Name:        "Category",
		Slug:        categorySlug,
		Description: "Used to group and classify content, like blog posts.",
		IsSystem:    true,
		Fields: []model.Field{
			{Name: "Name", Slug: "name", Type: "text", IsRequired: true},
			{Name: "Slug", Slug: "slug", Type: "text", IsRequired: true},
			{Name: "Description", Slug: "description", Type: "textarea"},
			{Name: "Parent", Slug: "parent", Type: "relation", IsRequired: false, Validations: model.ValidationMap{"related_type": "category"}},
		},
	}
	
	if err := svc.CreateContentType(categoryType); err != nil {
		log.Printf("Failed to seed content type '%s': %v", categorySlug, err)
	} else {
		log.Printf("Successfully seeded content type '%s'.", categorySlug)
	}
}

func ensurePostType(svc *service.ContentTypeService) {
	const postSlug = "post"

	_, err := svc.GetBySlug(postSlug)
	if err == nil {
		log.Printf("Content type '%s' already exists. Skipping.", postSlug)
		return
	}

	if err != gorm.ErrRecordNotFound {
		log.Printf("Error checking for content type '%s': %v. Skipping.", postSlug, err)
		return
	}

	log.Printf("Creating '%s' content type...", postSlug)

	postType := &model.ContentType{
		Name:        "Post",
		Slug:        postSlug,
		Description: "Represents a standard blog post or article.",
		IsSystem:    true,
		Fields: []model.Field{
			{Name: "Title", Slug: "title", Type: "text", IsRequired: true},
			{Name: "Slug", Slug: "slug", Type: "text", IsRequired: true},
			{Name: "Content", Slug: "richtext", Type: "richtext", IsRequired: true},
			{Name: "Excerpt", Slug: "excerpt", Type: "textarea"},
			{Name: "Featured Image", Slug: "featured_image", Type: "media"},
			{Name: "Category", Slug: "category", Type: "relation", IsRequired: true, Validations: model.ValidationMap{"related_type": "category"}},
			{Name: "Tags", Slug: "tags", Type: "text_array"}, // Assuming a simple text array for tags for now
		},
	}

	if err := svc.CreateContentType(postType); err != nil {
		log.Printf("Failed to seed content type '%s': %v", postSlug, err)
	} else {
		log.Printf("Successfully seeded content type '%s'.", postSlug)
	}
}

func ensureMenuGroupType(svc *service.ContentTypeService) {
	const slug = "menu_group"
	_, err := svc.GetBySlug(slug)
	if err == nil {
		log.Printf("Content type '%s' already exists. Skipping.", slug)
		return
	}
	if err != gorm.ErrRecordNotFound {
		log.Printf("Error checking for content type '%s': %v. Skipping.", slug, err)
		return
	}
	log.Printf("Creating '%s' content type...", slug)
	contentType := &model.ContentType{
		Name:        "Menu Group",
		Slug:        slug,
		Description: "A container for a set of menu items, e.g., 'Main Navigation'.",
		IsSystem:    true,
		Fields: []model.Field{
			{Name: "Name", Slug: "name", Type: "text", IsRequired: true},
		},
	}
	if err := svc.CreateContentType(contentType); err != nil {
		log.Printf("Failed to seed content type '%s': %v", slug, err)
	} else {
		log.Printf("Successfully seeded content type '%s'.", slug)
	}
}

func ensureMenuItemType(svc *service.ContentTypeService) {
	const slug = "menu_item"
	_, err := svc.GetBySlug(slug)
	if err == nil {
		log.Printf("Content type '%s' already exists. Skipping.", slug)
		return
	}
	if err != gorm.ErrRecordNotFound {
		log.Printf("Error checking for content type '%s': %v. Skipping.", slug, err)
		return
	}
	log.Printf("Creating '%s' content type...", slug)
	contentType := &model.ContentType{
		Name:        "Menu Item",
		Slug:        slug,
		Description: "A single item within a menu.",
		IsSystem:    true,
		Fields: []model.Field{
			{Name: "Title", Slug: "title", Type: "text", IsRequired: true},
			{Name: "URL", Slug: "url", Type: "text", IsRequired: true},
			{Name: "Target", Slug: "target", Type: "select", IsRequired: false, Validations: model.ValidationMap{"options": []string{"_self", "_blank"}}},
			{Name: "Icon", Slug: "icon", Type: "text"},
			{Name: "Order", Slug: "order", Type: "number"},
			{Name: "Parent", Slug: "parent", Type: "relation", Validations: model.ValidationMap{"related_type": "menu_item"}},
			{Name: "Menu Group", Slug: "menu_group", Type: "relation", IsRequired: true, Validations: model.ValidationMap{"related_type": "menu_group"}},
		},
	}
	if err := svc.CreateContentType(contentType); err != nil {
		log.Printf("Failed to seed content type '%s': %v", slug, err)
	} else {
		log.Printf("Successfully seeded content type '%s'.", slug)
	}
}

func ensureBannerPositionType(svc *service.ContentTypeService) {
	const slug = "banner_position"
	_, err := svc.GetBySlug(slug)
	if err == nil {
		log.Printf("Content type '%s' already exists. Skipping.", slug)
		return
	}
	if err != gorm.ErrRecordNotFound {
		log.Printf("Error checking for content type '%s': %v. Skipping.", slug, err)
		return
	}
	log.Printf("Creating '%s' content type...", slug)
	contentType := &model.ContentType{
		Name:        "Banner Position",
		Slug:        slug,
		Description: "Defines a location where banners can be displayed, e.g., 'homepage-slider'.",
		IsSystem:    true,
		Fields: []model.Field{
			{Name: "Name", Slug: "name", Type: "text", IsRequired: true},
			{Name: "Slug", Slug: "slug", Type: "text", IsRequired: true, IsUnique: true},
		},
	}
	if err := svc.CreateContentType(contentType); err != nil {
		log.Printf("Failed to seed content type '%s': %v", slug, err)
	} else {
		log.Printf("Successfully seeded content type '%s'.", slug)
	}
}

func ensureBannerType(svc *service.ContentTypeService) {
	const slug = "banner"
	_, err := svc.GetBySlug(slug)
	if err == nil {
		log.Printf("Content type '%s' already exists. Skipping.", slug)
		return
	}
	if err != gorm.ErrRecordNotFound {
		log.Printf("Error checking for content type '%s': %v. Skipping.", slug, err)
		return
	}
	log.Printf("Creating '%s' content type...", slug)
	contentType := &model.ContentType{
		Name:        "Banner",
		Slug:        slug,
		Description: "A single banner image with a link.",
		IsSystem:    true,
		Fields: []model.Field{
			{Name: "Title", Slug: "title", Type: "text", IsRequired: true},
			{Name: "Image", Slug: "image", Type: "media", IsRequired: true},
			{Name: "URL", Slug: "url", Type: "text"},
			{Name: "Order", Slug: "order", Type: "number"},
			{Name: "Position", Slug: "position", Type: "relation", IsRequired: true, Validations: model.ValidationMap{"related_type": "banner_position"}},
		},
	}
	if err := svc.CreateContentType(contentType); err != nil {
		log.Printf("Failed to seed content type '%s': %v", slug, err)
	} else {
		log.Printf("Successfully seeded content type '%s'.", slug)
	}
}

func ensurePageType(svc *service.ContentTypeService) {
	const slug = "page"
	_, err := svc.GetBySlug(slug)
	if err == nil {
		log.Printf("Content type '%s' already exists. Skipping.", slug)
		return
	}
	if err != gorm.ErrRecordNotFound {
		log.Printf("Error checking for content type '%s': %v. Skipping.", slug, err)
		return
	}
	log.Printf("Creating '%s' content type...", slug)
	contentType := &model.ContentType{
		Name:        "Page",
		Slug:        slug,
		Description: "A standalone page, like 'About Us' or 'Contact'.",
		IsSystem:    true,
		Fields: []model.Field{
			{Name: "Title", Slug: "title", Type: "text", IsRequired: true},
			{Name: "Slug", Slug: "slug", Type: "text", IsRequired: true, IsUnique: true},
			{Name: "Content", Slug: "content", Type: "richtext", IsRequired: true},
			{Name: "SEO Title", Slug: "seo_title", Type: "text"},
			{Name: "SEO Description", Slug: "seo_description", Type: "textarea"},
		},
	}
	if err := svc.CreateContentType(contentType); err != nil {
		log.Printf("Failed to seed content type '%s': %v", slug, err)
	} else {
		log.Printf("Successfully seeded content type '%s'.", slug)
	}
}

func ensureTopicType(svc *service.ContentTypeService) {
	const slug = "topic"
	_, err := svc.GetBySlug(slug)
	if err == nil {
		log.Printf("Content type '%s' already exists. Skipping.", slug)
		return
	}
	if err != gorm.ErrRecordNotFound {
		log.Printf("Error checking for content type '%s': %v. Skipping.", slug, err)
		return
	}
	log.Printf("Creating '%s' content type...", slug)
	contentType := &model.ContentType{
		Name:        "Topic",
		Slug:        slug,
		Description: "A collection of posts, like a special feature or a series.",
		IsSystem:    true,
		Fields: []model.Field{
			{Name: "Name", Slug: "name", Type: "text", IsRequired: true},
			{Name: "Slug", Slug: "slug", Type: "text", IsRequired: true, IsUnique: true},
			{Name: "Description", Slug: "description", Type: "textarea"},
			{Name: "Cover Image", Slug: "cover_image", Type: "media"},
			// This field will store an array of post IDs.
			{Name: "Posts", Slug: "posts", Type: "relation_many", Validations: model.ValidationMap{"related_type": "post"}},
		},
	}
	if err := svc.CreateContentType(contentType); err != nil {
		log.Printf("Failed to seed content type '%s': %v", slug, err)
	} else {
		log.Printf("Successfully seeded content type '%s'.", slug)
	}
}

func ensureTagType(svc *service.ContentTypeService) {
	const slug = "tag"
	_, err := svc.GetBySlug(slug)
	if err == nil {
		log.Printf("Content type '%s' already exists. Skipping.", slug)
		return
	}
	if err != gorm.ErrRecordNotFound {
		log.Printf("Error checking for content type '%s': %v. Skipping.", slug, err)
		return
	}
	log.Printf("Creating '%s' content type...", slug)
	contentType := &model.ContentType{
		Name:        "Tag",
		Slug:        slug,
		Description: "A keyword or label for categorization.",
		IsSystem:    true,
		Fields: []model.Field{
			{Name: "Name", Slug: "name", Type: "text", IsRequired: true, IsUnique: true},
			{Name: "Slug", Slug: "slug", Type: "text", IsRequired: true, IsUnique: true},
		},
	}
	if err := svc.CreateContentType(contentType); err != nil {
		log.Printf("Failed to seed content type '%s': %v", slug, err)
	} else {
		log.Printf("Successfully seeded content type '%s'.", slug)
	}
} 