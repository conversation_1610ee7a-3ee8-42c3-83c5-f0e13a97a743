/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON> <<EMAIL>>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/system/domain/model/DomainBinding.go
 * @Description: Defines the DomainBinding model for mapping domains to modules or categories.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package model

import (
	"gorm.io/gorm"
)

// BindingType defines the type of entity a domain can be bound to.
type BindingType string

const (
	BindingTypeModule        BindingType = "module"
	BindingTypeCategory      BindingType = "category"
	BindingTypePlatformAdmin BindingType = "platform_admin"
)

type DomainBinding struct {
	gorm.Model
	Domain      string      `gorm:"type:varchar(255);not null;uniqueIndex:idx_site_domain"`
	SiteID      uint        `gorm:"not null;uniqueIndex:idx_site_domain"`
	BindingType BindingType `gorm:"type:varchar(50);not null"`
	ModuleSlug  *string     `gorm:"type:varchar(100)"`
	CategoryID  *uint

	// URL重写配置
	URLRewriteEnabled bool   `gorm:"default:false"`
	DefaultController string `gorm:"type:varchar(100)"` // 默认控制器
	DefaultAction     string `gorm:"type:varchar(100)"` // 默认方法

	// 关联的URL重写规则
	URLRules []URLRewriteRule `gorm:"foreignKey:DomainBindingID"`
}

// URLRewriteRule URL重写规则
type URLRewriteRule struct {
	gorm.Model
	DomainBindingID uint   `gorm:"not null;index"`
	RuleName        string `gorm:"type:varchar(100);not null"`
	Pattern         string `gorm:"type:varchar(500);not null"` // 匹配模式，如 /p/{id}
	Replacement     string `gorm:"type:varchar(500);not null"` // 替换模式，如 /content/post/show/{id}
	Priority        int    `gorm:"default:0"`                  // 优先级，数字越大优先级越高
	IsActive        bool   `gorm:"default:true"`

	// 关联的域名绑定
	DomainBinding DomainBinding `gorm:"foreignKey:DomainBindingID"`
}