<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 访问控制管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .access-card {
            transition: all 0.3s ease;
        }
        
        .access-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
        }
        
        .rule-item {
            transition: all 0.3s ease;
        }
        
        .rule-item:hover {
            background-color: rgba(75, 85, 99, 0.1);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="security.html" class="hover:text-white">安全管理</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">访问控制</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">访问控制管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="addRuleBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-plus text-white"></i>
                                </span>
                                添加规则
                            </span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 访问控制状态卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <!-- 访问控制状态 -->
                <div class="access-card bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-shield-alt text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white">访问控制状态</h3>
                            <div class="flex items-center mt-2">
                                <span class="status-badge bg-green-500/20 text-green-400 mr-2">已启用</span>
                                <span class="text-gray-400 text-sm">最近更新: 2025-04-15</span>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <label class="inline-flex items-center cursor-pointer">
                            <span class="mr-2 text-gray-300">启用访问控制</span>
                            <div class="relative">
                                <input type="checkbox" class="sr-only" checked>
                                <div class="w-10 h-5 bg-gray-700 rounded-full shadow-inner"></div>
                                <div class="dot absolute w-4 h-4 bg-blue-500 rounded-full transition left-0.5 top-0.5 transform translate-x-5"></div>
                            </div>
                        </label>
                    </div>
                </div>
                
                <!-- 活跃规则 -->
                <div class="access-card bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-list-check text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white">活跃规则</h3>
                            <div class="flex items-center mt-2">
                                <span class="text-2xl font-bold text-white">8</span>
                                <span class="text-gray-400 text-sm ml-2">条规则生效中</span>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">IP限制规则:</span>
                            <span class="text-white">5条</span>
                        </div>
                        <div class="flex justify-between text-sm mt-1">
                            <span class="text-gray-400">时间限制规则:</span>
                            <span class="text-white">3条</span>
                        </div>
                    </div>
                </div>
                
                <!-- 安全日志 -->
                <div class="access-card bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-file-alt text-purple-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white">安全日志</h3>
                            <div class="flex items-center mt-2">
                                <span class="text-2xl font-bold text-white">24</span>
                                <span class="text-gray-400 text-sm ml-2">条拦截记录 (今日)</span>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="security_logs.html" class="text-blue-400 hover:text-blue-300 text-sm flex items-center">
                            查看详细日志
                            <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 访问控制规则列表 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold mb-4 text-white">访问控制规则</h3>
                
                <!-- 规则筛选 -->
                <div class="flex flex-wrap gap-4 mb-4">
                    <div class="relative">
                        <select class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all">所有规则类型</option>
                            <option value="ip">IP限制</option>
                            <option value="time">时间限制</option>
                            <option value="user">用户组限制</option>
                            <option value="geo">地理位置限制</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </div>
                    </div>
                    
                    <div class="relative">
                        <select class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all">所有状态</option>
                            <option value="active">已启用</option>
                            <option value="inactive">已禁用</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </div>
                    </div>
                    
                    <div class="flex-1"></div>
                    
                    <div class="relative">
                        <input type="text" placeholder="搜索规则" class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <div class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-400">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 规则列表 -->
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="py-3 text-left text-gray-400 font-medium">规则名称</th>
                                <th class="py-3 text-left text-gray-400 font-medium">类型</th>
                                <th class="py-3 text-left text-gray-400 font-medium">规则内容</th>
                                <th class="py-3 text-left text-gray-400 font-medium">优先级</th>
                                <th class="py-3 text-left text-gray-400 font-medium">状态</th>
                                <th class="py-3 text-left text-gray-400 font-medium">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 规则项 1 -->
                            <tr class="rule-item border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <i class="fas fa-ban text-red-500 mr-2"></i>
                                        <span class="text-white">禁止特定IP访问</span>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-red-500/20 text-red-400">IP限制</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">*************, *************</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-white">高</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-green-500/20 text-green-400">已启用</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 规则项 2 -->
                            <tr class="rule-item border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <i class="fas fa-clock text-yellow-500 mr-2"></i>
                                        <span class="text-white">非工作时间限制</span>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-yellow-500/20 text-yellow-400">时间限制</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">工作日 18:00-09:00, 周末全天</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-white">中</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-green-500/20 text-green-400">已启用</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 规则项 3 -->
                            <tr class="rule-item border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <i class="fas fa-users text-blue-500 mr-2"></i>
                                        <span class="text-white">仅管理员访问</span>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-blue-500/20 text-blue-400">用户组限制</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">仅允许管理员组访问系统设置页面</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-white">高</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-green-500/20 text-green-400">已启用</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 规则项 4 -->
                            <tr class="rule-item border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <i class="fas fa-globe text-green-500 mr-2"></i>
                                        <span class="text-white">地区访问限制</span>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-green-500/20 text-green-400">地理位置限制</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">仅允许中国大陆IP访问</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-white">中</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-green-500/20 text-green-400">已启用</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 规则项 5 -->
                            <tr class="rule-item border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <i class="fas fa-shield-alt text-purple-500 mr-2"></i>
                                        <span class="text-white">API访问限制</span>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-purple-500/20 text-purple-400">API限制</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">限制API调用频率: 100次/分钟</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-white">高</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-green-500/20 text-green-400">已启用</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 规则项 6 -->
                            <tr class="rule-item border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <i class="fas fa-robot text-orange-500 mr-2"></i>
                                        <span class="text-white">爬虫限制</span>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-orange-500/20 text-orange-400">爬虫限制</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">限制非授权爬虫访问</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-white">中</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-gray-500/20 text-gray-400">已禁用</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div class="flex justify-between items-center mt-4">
                    <div class="text-sm text-gray-400">
                        显示 1-6 条，共 8 条
                    </div>
                    <div class="flex space-x-1">
                        <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="px-3 py-1 bg-blue-500 text-white rounded-md">
                            1
                        </button>
                        <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                            2
                        </button>
                        <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 白名单设置 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold mb-4 text-white">IP白名单设置</h3>
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">添加IP白名单</label>
                    <div class="flex">
                        <input type="text" placeholder="输入IP地址或IP段 (如: *********** 或 ***********/24)" class="flex-1 bg-gray-700 border border-gray-600 rounded-l-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-r-lg">
                            添加
                        </button>
                    </div>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="py-3 text-left text-gray-400 font-medium">IP地址/IP段</th>
                                <th class="py-3 text-left text-gray-400 font-medium">添加时间</th>
                                <th class="py-3 text-left text-gray-400 font-medium">备注</th>
                                <th class="py-3 text-left text-gray-400 font-medium">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="rule-item border-b border-gray-700">
                                <td class="py-3">
                                    <span class="text-white">************</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">2025-04-10 14:30</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">办公室固定IP</span>
                                </td>
                                <td class="py-3">
                                    <button class="text-gray-400 hover:text-red-500 p-1">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr class="rule-item border-b border-gray-700">
                                <td class="py-3">
                                    <span class="text-white">10.0.0.0/8</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">2025-04-05 09:15</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">内部网络</span>
                                </td>
                                <td class="py-3">
                                    <button class="text-gray-400 hover:text-red-500 p-1">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 页面底部版权信息 -->
            <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
                © 2025 GACMS 后台管理系统 - 版本 v1.0.0
            </footer>
        </div>
    </main>

    <!-- 添加规则模态框 -->
    <div id="addRuleModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 w-full max-w-lg">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-white">添加访问控制规则</h3>
                <button id="closeModalBtn" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form>
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">规则名称</label>
                    <input type="text" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="输入规则名称">
                </div>
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">规则类型</label>
                    <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="ip">IP限制</option>
                        <option value="time">时间限制</option>
                        <option value="user">用户组限制</option>
                        <option value="geo">地理位置限制</option>
                        <option value="api">API限制</option>
                        <option value="crawler">爬虫限制</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">规则内容</label>
                    <textarea class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3" placeholder="输入规则内容"></textarea>
                </div>
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">优先级</label>
                    <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="high">高</option>
                        <option value="medium">中</option>
                        <option value="low">低</option>
                    </select>
                </div>
                <div class="mb-4 flex items-center">
                    <input type="checkbox" id="enableRule" class="mr-2" checked>
                    <label for="enableRule" class="text-gray-300">立即启用</label>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancelBtn" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg">取消</button>
                    <button type="button" class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 必要的JS脚本 -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 开关切换效果
            const toggleSwitches = document.querySelectorAll('input[type="checkbox"]');
            
            toggleSwitches.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const dot = this.nextElementSibling.nextElementSibling;
                    if (this.checked) {
                        dot.classList.add('bg-blue-500', 'transform', 'translate-x-5');
                        dot.classList.remove('bg-gray-400');
                    } else {
                        dot.classList.remove('bg-blue-500', 'transform', 'translate-x-5');
                        dot.classList.add('bg-gray-400');
                    }
                });
            });
            
            // 模态框控制
            const addRuleBtn = document.getElementById('addRuleBtn');
            const addRuleModal = document.getElementById('addRuleModal');
            const closeModalBtn = document.getElementById('closeModalBtn');
            const cancelBtn = document.getElementById('cancelBtn');
            
            function openModal() {
                addRuleModal.classList.remove('hidden');
            }
            
            function closeModal() {
                addRuleModal.classList.add('hidden');
            }
            
            addRuleBtn.addEventListener('click', openModal);
            closeModalBtn.addEventListener('click', closeModal);
            cancelBtn.addEventListener('click', closeModal);
            
            // 点击模态框外部关闭
            addRuleModal.addEventListener('click', function(e) {
                if (e.target === addRuleModal) {
                    closeModal();
                }
            });
        });
    </script>
</body>
</html>