/*
Author: <PERSON><PERSON> Nieh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package service

// EditionCompiler 编译时版本管理器
// 通过编译标签控制不同版本包含的功能代码

import (
	"gacms/pkg/contract"
)

// CompileTimeEditionManager 编译时版本管理器接口
type CompileTimeEditionManager interface {
	// 编译时功能检查
	IsFeatureCompiledIn(featureName string) bool
	GetCompiledFeatures() []string
	GetCompiledEdition() contract.Edition
	
	// 编译时限制
	GetCompiledLimits() *EditionLimits
	IsLimitCompiledIn(limitType string) bool
}

// GetCompiledEditionManager 获取编译时版本管理器
// 根据编译标签返回对应的实现
func GetCompiledEditionManager() CompileTimeEditionManager {
	// 这个函数会根据编译标签返回不同的实现
	// 具体实现在各个版本的文件中
	return getCompiledEditionManager()
}

// 这个函数的实现会根据编译标签在不同文件中定义
func getCompiledEditionManager() CompileTimeEditionManager {
	// 默认返回个人版（如果没有指定编译标签）
	return &PersonalEditionCompiler{}
}

// BaseEditionCompiler 基础版本编译器
type BaseEditionCompiler struct {
	edition  contract.Edition
	features map[string]bool
	limits   *EditionLimits
}

// IsFeatureCompiledIn 检查功能是否编译进来
func (c *BaseEditionCompiler) IsFeatureCompiledIn(featureName string) bool {
	return c.features[featureName]
}

// GetCompiledFeatures 获取编译进来的功能列表
func (c *BaseEditionCompiler) GetCompiledFeatures() []string {
	var features []string
	for feature, compiled := range c.features {
		if compiled {
			features = append(features, feature)
		}
	}
	return features
}

// GetCompiledEdition 获取编译版本
func (c *BaseEditionCompiler) GetCompiledEdition() contract.Edition {
	return c.edition
}

// GetCompiledLimits 获取编译限制
func (c *BaseEditionCompiler) GetCompiledLimits() *EditionLimits {
	return c.limits
}

// IsLimitCompiledIn 检查限制是否编译进来
func (c *BaseEditionCompiler) IsLimitCompiledIn(limitType string) bool {
	// 根据限制类型检查是否编译进来
	switch limitType {
	case "sites":
		return c.limits.MaxSites > 0
	case "users":
		return c.limits.MaxUsers > 0
	case "storage":
		return c.limits.MaxStorage > 0
	case "bandwidth":
		return c.limits.MaxBandwidth > 0
	case "pages":
		return c.limits.MaxPages > 0
	case "posts":
		return c.limits.MaxPosts > 0
	default:
		return false
	}
}
