/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/system/application/dto/SettingDTO.go
 * @Description: Defines the DTO for system settings.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package dto

// SettingDTO is used for transferring setting data to/from the API layer.
type SettingDTO struct {
	Key         string `json:"key"`
	Value       string `json:"value"`
	Name        string `json:"name,omitempty"`
	Description string `json:"description,omitempty"`
} 