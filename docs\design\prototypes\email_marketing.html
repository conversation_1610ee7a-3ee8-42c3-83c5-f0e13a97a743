<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 邮件营销</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .campaign-card {
            transition: all 0.3s ease;
        }
        
        .campaign-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
        }
        
        .template-card {
            transition: all 0.3s ease;
        }
        
        .template-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .template-preview {
            height: 160px;
            overflow: hidden;
            border-radius: 0.5rem;
        }
        
        .template-overlay {
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .template-card:hover .template-overlay {
            opacity: 1;
        }
        
        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        /* 进度条样式 */
        .progress-bar {
            height: 6px;
            border-radius: 3px;
            overflow: hidden;
        }
        
        /* 表格行悬停效果 */
        .campaign-row {
            transition: background-color 0.2s ease;
        }
        
        .campaign-row:hover {
            background-color: rgba(75, 85, 99, 0.1);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="marketing.html" class="hover:text-white">营销</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">邮件营销</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">邮件营销</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="createCampaignBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-plus text-white"></i>
                                </span>
                                创建活动
                            </span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 营销数据概览 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <!-- 总订阅用户 -->
                <div class="campaign-card bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-users text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">总订阅用户</h3>
                            <div class="text-2xl font-bold text-white mt-1">12,458</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-green-400">
                            <i class="fas fa-arrow-up mr-1"></i>5.2%
                        </span>
                        <span class="text-gray-400 ml-2">较上月</span>
                    </div>
                </div>
                
                <!-- 平均打开率 -->
                <div class="campaign-card bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-envelope-open text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">平均打开率</h3>
                            <div class="text-2xl font-bold text-white mt-1">32.7%</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-green-400">
                            <i class="fas fa-arrow-up mr-1"></i>1.8%
                        </span>
                        <span class="text-gray-400 ml-2">较行业平均</span>
                    </div>
                </div>
                
                <!-- 平均点击率 -->
                <div class="campaign-card bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-mouse-pointer text-yellow-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">平均点击率</h3>
                            <div class="text-2xl font-bold text-white mt-1">8.4%</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-green-400">
                            <i class="fas fa-arrow-up mr-1"></i>0.6%
                        </span>
                        <span class="text-gray-400 ml-2">较上季度</span>
                    </div>
                </div>
                
                <!-- 转化率 -->
                <div class="campaign-card bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-chart-line text-purple-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">转化率</h3>
                            <div class="text-2xl font-bold text-white mt-1">3.2%</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-red-400">
                            <i class="fas fa-arrow-down mr-1"></i>0.3%
                        </span>
                        <span class="text-gray-400 ml-2">较上月</span>
                    </div>
                </div>
            </div>
            
            <!-- 活动管理标签页 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="border-b border-gray-700 mb-6">
                    <ul class="flex flex-wrap -mb-px">
                        <li class="mr-2">
                            <a href="#" class="inline-block py-3 px-4 text-white border-b-2 border-blue-500 font-medium">活动列表</a>
                        </li>
                        <li class="mr-2">
                            <a href="#" class="inline-block py-3 px-4 text-gray-400 hover:text-white border-b-2 border-transparent hover:border-gray-400">邮件模板</a>
                        </li>
                        <li class="mr-2">
                            <a href="#" class="inline-block py-3 px-4 text-gray-400 hover:text-white border-b-2 border-transparent hover:border-gray-400">订阅者管理</a>
                        </li>
                        <li class="mr-2">
                            <a href="#" class="inline-block py-3 px-4 text-gray-400 hover:text-white border-b-2 border-transparent hover:border-gray-400">自动化规则</a>
                        </li>
                    </ul>
                </div>
                
                <!-- 活动筛选 -->
                <div class="flex flex-wrap gap-4 mb-6">
                    <div class="relative">
                        <select class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all">所有状态</option>
                            <option value="draft">草稿</option>
                            <option value="scheduled">已计划</option>
                            <option value="sending">发送中</option>
                            <option value="sent">已发送</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </div>
                    </div>
                    
                    <div class="relative">
                        <select class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all">所有类型</option>
                            <option value="newsletter">新闻通讯</option>
                            <option value="promotion">促销活动</option>
                            <option value="announcement">公告</option>
                            <option value="welcome">欢迎邮件</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </div>
                    </div>
                    
                    <div class="flex-1"></div>
                    
                    <div class="relative">
                        <input type="text" placeholder="搜索活动" class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <div class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-400">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 活动列表 -->
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="py-3 text-left text-gray-400 font-medium">活动名称</th>
                                <th class="py-3 text-left text-gray-400 font-medium">类型</th>
                                <th class="py-3 text-left text-gray-400 font-medium">状态</th>
                                <th class="py-3 text-left text-gray-400 font-medium">发送时间</th>
                                <th class="py-3 text-left text-gray-400 font-medium">打开率</th>
                                <th class="py-3 text-left text-gray-400 font-medium">点击率</th>
                                <th class="py-3 text-left text-gray-400 font-medium">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 活动项 1 -->
                            <tr class="campaign-row border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded bg-blue-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-envelope text-blue-500"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium">2025年春季新品发布</div>
                                            <div class="text-gray-400 text-sm">收件人: 全部订阅者</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-purple-500/20 text-purple-400">新闻通讯</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-green-500/20 text-green-400">已发送</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">2025-04-10 09:00</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <span class="text-white mr-2">38.2%</span>
                                        <div class="w-16 bg-gray-700 rounded-full h-1.5">
                                            <div class="bg-green-500 h-1.5 rounded-full" style="width: 38%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <span class="text-white mr-2">12.5%</span>
                                        <div class="w-16 bg-gray-700 rounded-full h-1.5">
                                            <div class="bg-blue-500 h-1.5 rounded-full" style="width: 12.5%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1" title="查看报告">
                                            <i class="fas fa-chart-bar"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-white p-1" title="复制活动">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 活动项 2 -->
                            <tr class="campaign-row border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded bg-yellow-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-tag text-yellow-500"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium">限时促销：五一特惠</div>
                                            <div class="text-gray-400 text-sm">收件人: VIP客户 (2,145)</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-yellow-500/20 text-yellow-400">促销活动</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-blue-500/20 text-blue-400">已计划</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">2025-04-25 08:00</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-400">-</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-400">-</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-white p-1" title="预览">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 活动项 3 -->
                            <tr class="campaign-row border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded bg-green-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-bullhorn text-green-500"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium">系统更新公告</div>
                                            <div class="text-gray-400 text-sm">收件人: 所有用户 (12,458)</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-green-500/20 text-green-400">公告</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-gray-500/20 text-gray-400">草稿</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-400">-</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-400">-</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-400">-</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-white p-1" title="预览">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 活动项 4 -->
                            <tr class="campaign-row border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded bg-blue-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-handshake text-blue-500"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium">欢迎新用户</div>
                                            <div class="text-gray-400 text-sm">收件人: 自动触发</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-blue-500/20 text-blue-400">欢迎邮件</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-purple-500/20 text-purple-400">自动</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">持续发送</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <span class="text-white mr-2">65.8%</span>
                                        <div class="w-16 bg-gray-700 rounded-full h-1.5">
                                            <div class="bg-green-500 h-1.5 rounded-full" style="width: 65.8%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <span class="text-white mr-2">24.3%</span>
                                        <div class="w-16 bg-gray-700 rounded-full h-1.5">
                                            <div class="bg-blue-500 h-1.5 rounded-full" style="width: 24.3%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1" title="查看报告">
                                            <i class="fas fa-chart-bar"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-white p-1" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-white p-1" title="预览">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 活动项 5 -->
                            <tr class="campaign-row border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded bg-red-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-heart text-red-500"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium">客户回馈调查</div>
                                            <div class="text-gray-400 text-sm">收件人: 活跃用户 (5,234)</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-red-500/20 text-red-400">调查</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-green-500/20 text-green-400">已发送</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">2025-04-05 10:30</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <span class="text-white mr-2">42.1%</span>
                                        <div class="w-16 bg-gray-700 rounded-full h-1.5">
                                            <div class="bg-green-500 h-1.5 rounded-full" style="width: 42.1%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <span class="text-white mr-2">18.7%</span>
                                        <div class="w-16 bg-gray-700 rounded-full h-1.5">
                                            <div class="bg-blue-500 h-1.5 rounded-full" style="width: 18.7%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1" title="查看报告">
                                            <i class="fas fa-chart-bar"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-white p-1" title="复制活动">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div class="flex justify-between items-center mt-6">
                    <div class="text-sm text-gray-400">
                        显示 1-5 条，共 12 条
                    </div>
                    <div class="flex space-x-1">
                        <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="px-3 py-1 bg-blue-500 text-white rounded-md">
                            1
                        </button>
                        <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                            2
                        </button>
                        <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                            3
                        </button>
                        <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 邮件模板预览 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold text-white">热门邮件模板</h3>
                    <a href="#" class="text-blue-400 hover:text-blue-300 text-sm flex items-center">
                        查看全部模板
                        <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- 模板 1 -->
                    <div class="template-card bg-gray-800/10 border border-gray-700 rounded-xl overflow-hidden">
                        <div class="relative">
                            <div class="template-preview">
                                <img src="https://images.unsplash.com/photo-1596526131083-e8c633c948d2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=400&q=80" alt="新闻通讯模板" class="w-full h-full object-cover">
                            </div>
                            <div class="template-overlay absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                <div class="flex space-x-2">
                                    <button class="bg-white text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-gray-200">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-blue-600">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="p-4">
                            <h4 class="text-white font-medium">现代新闻通讯</h4>
                            <div class="flex items-center justify-between mt-2">
                                <div class="flex items-center">
                                    <span class="tag bg-blue-500/20 text-blue-400">新闻通讯</span>
                                </div>
                                <span class="text-gray-400 text-sm">使用次数: 28</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 模板 2 -->
                    <div class="template-card bg-gray-800/10 border border-gray-700 rounded-xl overflow-hidden">
                        <div class="relative">
                            <div class="template-preview">
                                <img src="https://images.unsplash.com/photo-1607082349566-187342175e2f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=400&q=80" alt="促销模板" class="w-full h-full object-cover">
                            </div>
                            <div class="template-overlay absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                <div class="flex space-x-2">
                                    <button class="bg-white text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-gray-200">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-blue-600">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="p-4">
                            <h4 class="text-white font-medium">限时促销</h4>
                            <div class="flex items-center justify-between mt-2">
                                <div class="flex items-center">
                                    <span class="tag bg-yellow-500/20 text-yellow-400">促销</span>
                                </div>
                                <span class="text-gray-400 text-sm">使用次数: 42</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 模板 3 -->
                    <div class="template-card bg-gray-800/10 border border-gray-700 rounded-xl overflow-hidden">
                        <div class="relative">
                            <div class="template-preview">
                                <img src="https://images.unsplash.com/photo-1579389083078-4e7018379f7e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=400&q=80" alt="欢迎模板" class="w-full h-full object-cover">
                            </div>
                            <div class="template-overlay absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                <div class="flex space-x-2">
                                    <button class="bg-white text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-gray-200">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-blue-600">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="p-4">
                            <h4 class="text-white font-medium">欢迎新用户</h4>
                            <div class="flex items-center justify-between mt-2">
                                <div class="flex items-center">
                                    <span class="tag bg-green-500/20 text-green-400">欢迎</span>
                                </div>
                                <span class="text-gray-400 text-sm">使用次数: 65</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 模板 4 -->
                    <div class="template-card bg-gray-800/10 border border-gray-700 rounded-xl overflow-hidden">
                        <div class="relative">
                            <div class="template-preview">
                                <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=400&q=80" alt="调查模板" class="w-full h-full object-cover">
                            </div>
                            <div class="template-overlay absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                <div class="flex space-x-2">
                                    <button class="bg-white text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-gray-200">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-blue-600">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="p-4">
                            <h4 class="text-white font-medium">客户调查</h4>
                            <div class="flex items-center justify-between mt-2">
                                <div class="flex items-center">
                                    <span class="tag bg-red-500/20 text-red-400">调查</span>
                                </div>
                                <span class="text-gray-400 text-sm">使用次数: 19</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 页面底部版权信息 -->
            <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
                © 2025 GACMS 后台管理系统 - 版本 v1.0.0
            </footer>
        </div>
    </main>

    <!-- 创建活动模态框 -->
    <div id="createCampaignModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 w-full max-w-2xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-white">创建邮件营销活动</h3>
                <button id="closeModalBtn" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form>
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">活动名称</label>
                    <input type="text" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="输入活动名称">
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-gray-300 mb-2">活动类型</label>
                        <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="newsletter">新闻通讯</option>
                            <option value="promotion">促销活动</option>
                            <option value="announcement">公告</option>
                            <option value="welcome">欢迎邮件</option>
                            <option value="survey">调查</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-gray-300 mb-2">收件人</label>
                        <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all">所有订阅者</option>
                            <option value="vip">VIP客户</option>
                            <option value="active">活跃用户</option>
                            <option value="inactive">不活跃用户</option>
                            <option value="custom">自定义分组</option>
                        </select>
                    </div>
                </div>
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">选择模板</label>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <div class="border border-blue-500 rounded-lg p-1 relative">
                            <img src="https://images.unsplash.com/photo-1596526131083-e8c633c948d2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=200&h=100&q=80" alt="模板1" class="w-full h-20 object-cover rounded">
                            <div class="absolute top-1 right-1 bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center">
                                <i class="fas fa-check text-xs"></i>
                            </div>
                        </div>
                        <div class="border border-gray-600 hover:border-blue-500 rounded-lg p-1">
                            <img src="https://images.unsplash.com/photo-1607082349566-187342175e2f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=200&h=100&q=80" alt="模板2" class="w-full h-20 object-cover rounded">
                        </div>
                        <div class="border border-gray-600 hover:border-blue-500 rounded-lg p-1">
                            <img src="https://images.unsplash.com/photo-1579389083078-4e7018379f7e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=200&h=100&q=80" alt="模板3" class="w-full h-20 object-cover rounded">
                        </div>
                        <div class="border border-gray-600 hover:border-blue-500 rounded-lg p-1">
                            <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=200&h=100&q=80" alt="模板4" class="w-full h-20 object-cover rounded">
                        </div>
                    </div>
                </div>
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">邮件主题</label>
                    <input type="text" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="输入邮件主题">
                </div>
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">发送时间</label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="flex items-center">
                            <input type="radio" id="sendNow" name="sendTime" class="mr-2" checked>
                            <label for="sendNow" class="text-gray-300">立即发送</label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="scheduleSend" name="sendTime" class="mr-2">
                            <label for="scheduleSend" class="text-gray-300">计划发送</label>
                        </div>
                    </div>
                    <div class="mt-3 hidden" id="scheduleTimeContainer">
                        <input type="datetime-local" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancelBtn" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg">取消</button>
                    <button type="button" class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg">下一步：编辑内容</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 必要的JS脚本 -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签页切换
            const tabLinks = document.querySelectorAll('.border-b ul li a');
            
            tabLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 移除所有标签的活动状态
                    tabLinks.forEach(tab => {
                        tab.classList.remove('text-white', 'border-blue-500');
                        tab.classList.add('text-gray-400', 'border-transparent');
                    });
                    
                    // 设置当前标签为活动状态
                    this.classList.remove('text-gray-400', 'border-transparent');
                    this.classList.add('text-white', 'border-blue-500');
                });
            });
            
            // 模态框控制
            const createCampaignBtn = document.getElementById('createCampaignBtn');
            const createCampaignModal = document.getElementById('createCampaignModal');
            const closeModalBtn = document.getElementById('closeModalBtn');
            const cancelBtn = document.getElementById('cancelBtn');
            
            function openModal() {
                createCampaignModal.classList.remove('hidden');
            }
            
            function closeModal() {
                createCampaignModal.classList.add('hidden');
            }
            
            createCampaignBtn.addEventListener('click', openModal);
            closeModalBtn.addEventListener('click', closeModal);
            cancelBtn.addEventListener('click', closeModal);
            
            // 点击模态框外部关闭
            createCampaignModal.addEventListener('click', function(e) {
                if (e.target === createCampaignModal) {
                    closeModal();
                }
            });
            
            // 计划发送时间控制
            const sendNow = document.getElementById('sendNow');
            const scheduleSend = document.getElementById('scheduleSend');
            const scheduleTimeContainer = document.getElementById('scheduleTimeContainer');
            
            sendNow.addEventListener('change', function() {
                if (this.checked) {
                    scheduleTimeContainer.classList.add('hidden');
                }
            });
            
            scheduleSend.addEventListener('change', function() {
                if (this.checked) {
                    scheduleTimeContainer.classList.remove('hidden');
                }
            });
        });
    </script>
</body>
</html>