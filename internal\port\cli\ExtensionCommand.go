/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/port/cli/ExtensionCommand.go
 * @Description: Defines generic CLI commands for extension management.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package cli

import (
	"fmt"
	"gacms/internal/modules/extension/application/service"
	"github.com/spf13/cobra"
)

func NewExtensionCommand(service *service.ExtensionService) *cobra.Command {
	var extensionType string

	cmd := &cobra.Command{
		Use:   "extension",
		Short: "Manage extensions (modules, themes, etc.)",
		PersistentPreRunE: func(cmd *cobra.Command, args []string) error {
			if extensionType != "module" && extensionType != "theme" && extensionType != "plugin" {
				return fmt.Errorf("invalid extension type '%s'. Must be one of: module, theme, plugin", extensionType)
			}
			return nil
		},
	}
	
	cmd.PersistentFlags().StringVarP(&extensionType, "type", "t", "module", "Type of the extension (module, theme, plugin)")

	listCmd := &cobra.Command{
		Use:   "list",
		Short: "List all extensions of a given type",
		RunE: func(cmd *cobra.Command, args []string) error {
			items, err := service.List(extensionType)
			if err != nil {
				return err
			}
			fmt.Printf("Available %ss:\n", extensionType)
			for _, item := range items {
				fmt.Printf("- %s\n", item)
			}
			return nil
		},
	}

	enableCmd := &cobra.Command{
		Use:   "enable [name]",
		Short: "Enable an extension",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			name := args[0]
			err := service.Enable(extensionType, name)
			if err != nil {
				return err
			}
			fmt.Printf("%s '%s' enabled successfully.\n", extensionType, name)
			return nil
		},
	}

	disableCmd := &cobra.Command{
		Use:   "disable [name]",
		Short: "Disable an extension",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			name := args[0]
			err := service.Disable(extensionType, name)
			if err != nil {
				return err
			}
			fmt.Printf("%s '%s' disabled successfully.\n", extensionType, name)
			return nil
		},
	}

	cmd.AddCommand(listCmd, enableCmd, disableCmd)

	return cmd
} 