/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/extension/application/strategy/ModuleStrategy.go
 * @Description: Concrete strategy for managing module activation.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package strategy

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
)

const modulesPath = "internal/modules"

type ModuleInfo struct {
	Name      string `json:"name"`
	Enabled   bool   `json:"enabled"`
	Version   string `json:"version"`
	// other fields can be added here
}

// ModuleStrategy implements ActivationStrategy for modules.
type ModuleStrategy struct{}

// NewModuleStrategy creates a new module strategy.
func NewModuleStrategy() *ModuleStrategy {
	return &ModuleStrategy{}
}

// Enable activates a module by setting 'enabled' to true in its module.json.
func (s *ModuleStrategy) Enable(name string) error {
	return s.setModuleEnabled(name, true)
}

// Disable deactivates a module by setting 'enabled' to false in its module.json.
func (s *ModuleStrategy) Disable(name string) error {
	return s.setModuleEnabled(name, false)
}

// List returns a list of all modules.
func (s *ModuleStrategy) List() ([]string, error) {
	var modules []string
	files, err := ioutil.ReadDir(modulesPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read modules directory: %w", err)
	}

	for _, f := range files {
		if f.IsDir() {
			modules = append(modules, f.Name())
		}
	}
	return modules, nil
}


func (s *ModuleStrategy) setModuleEnabled(name string, enabled bool) error {
	jsonPath := filepath.Join(modulesPath, name, "module.json")

	info, err := s.readModuleInfo(jsonPath)
	if err != nil {
		return err
	}

	if info.Enabled == enabled {
		status := "enabled"
		if !enabled {
			status = "disabled"
		}
		fmt.Printf("Module '%s' is already %s.\n", name, status)
		return nil
	}

	info.Enabled = enabled

	return s.writeModuleInfo(jsonPath, info)
}

func (s *ModuleStrategy) readModuleInfo(path string) (*ModuleInfo, error) {
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return nil, fmt.Errorf("module.json not found for module at %s", path)
	}

	data, err := ioutil.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("failed to read module.json: %w", err)
	}

	var info ModuleInfo
	if err := json.Unmarshal(data, &info); err != nil {
		return nil, fmt.Errorf("failed to parse module.json: %w", err)
	}
	return &info, nil
}

func (s *ModuleStrategy) writeModuleInfo(path string, info *ModuleInfo) error {
	data, err := json.MarshalIndent(info, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to serialize module.json: %w", err)
	}
	return ioutil.WriteFile(path, data, 0644)
} 