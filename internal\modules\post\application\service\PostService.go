/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/post/application/service/PostService.go
 * @Description: Application service for post-related business logic.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package service

import (
	"context"
	"gacms/internal/modules/post/domain/contract"
	"gacms/internal/modules/post/domain/model"
)

// PostService encapsulates business logic specific to posts.
type PostService struct {
	postRepo contract.PostRepository
}

func NewPostService(postRepo contract.PostRepository) *PostService {
	return &PostService{postRepo: postRepo}
}

func (s *PostService) GetPostBySlug(ctx context.Context, slug string) (*model.Post, error) {
	// The siteID is implicitly handled by the context via the repository.
	return s.postRepo.GetBySlug(ctx, slug)
}

// NOTE: We will add methods like PublishPost, SchedulePost, etc., here later.
// These methods will contain business logic that is specific to a "post"
// and not applicable to a generic content item.
// For example, publishing a post might involve several steps:
// 1. Validate that it has a title and content.
// 2. Change its internal status field.
// 3. Clear relevant caches.
// 4. Send notifications.
