/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/system/model/DomainBinding.go
 * @Description: 域名绑定模型，用于将域名绑定到特定的站点和模块（基础设施）
 * 
 * © 2025 GACMS. All rights reserved.
 */

package model

import (
	"gorm.io/gorm"
)

// BindingType 绑定类型枚举
type BindingType string

const (
	BindingTypeModule        BindingType = "module"
	BindingTypeCategory      BindingType = "category"
	BindingTypePlatformAdmin BindingType = "platform_admin"
)

// DomainBinding 域名绑定模型
type DomainBinding struct {
	gorm.Model
	Domain      string      `gorm:"type:varchar(255);not null;uniqueIndex:idx_site_domain"`
	SiteID      uint        `gorm:"not null;uniqueIndex:idx_site_domain"`
	BindingType BindingType `gorm:"type:varchar(50);not null"`
	ModuleSlug  *string     `gorm:"type:varchar(100)"`
	CategoryID  *uint
	
	// URL重写配置
	URLRewriteEnabled bool   `gorm:"default:false"`
	DefaultController string `gorm:"type:varchar(100)"` // 默认控制器
	DefaultAction     string `gorm:"type:varchar(100)"` // 默认方法
	
	// 关联的URL重写规则
	URLRules []URLRewriteRule `gorm:"foreignKey:DomainBindingID"`
}

// URLRewriteRule URL重写规则
type URLRewriteRule struct {
	gorm.Model
	DomainBindingID uint   `gorm:"not null;index"`
	RuleName        string `gorm:"type:varchar(100);not null"`
	Pattern         string `gorm:"type:varchar(500);not null"` // 匹配模式，如 /p/{id}
	Replacement     string `gorm:"type:varchar(500);not null"` // 替换模式，如 /content/post/show/{id}
	Priority        int    `gorm:"default:0"`                  // 优先级，数字越大优先级越高
	IsActive        bool   `gorm:"default:true"`
	
	// 关联的域名绑定
	DomainBinding DomainBinding `gorm:"foreignKey:DomainBindingID"`
}

// Site 站点模型
type Site struct {
	gorm.Model
	Name        string `gorm:"type:varchar(255);not null"`
	Domain      string `gorm:"type:varchar(255);not null;uniqueIndex"`
	Description string `gorm:"type:text"`
	IsActive    bool   `gorm:"default:true"`
	
	// 站点配置
	Config SiteConfig `gorm:"embedded"`
	
	// 关联的域名绑定
	DomainBindings []DomainBinding `gorm:"foreignKey:SiteID"`
}

// SiteConfig 站点配置
type SiteConfig struct {
	Title       string `gorm:"type:varchar(255)"`
	Keywords    string `gorm:"type:varchar(500)"`
	Description string `gorm:"type:text"`
	Logo        string `gorm:"type:varchar(500)"`
	Favicon     string `gorm:"type:varchar(500)"`
	
	// SEO配置
	SEOEnabled    bool   `gorm:"default:true"`
	SEOTitle      string `gorm:"type:varchar(255)"`
	SEOKeywords   string `gorm:"type:varchar(500)"`
	SEODescription string `gorm:"type:text"`
	
	// 安全配置
	HTTPSEnabled  bool `gorm:"default:false"`
	ForceHTTPS    bool `gorm:"default:false"`
	
	// 缓存配置
	CacheEnabled  bool `gorm:"default:true"`
	CacheTTL      int  `gorm:"default:3600"` // 缓存时间（秒）
}

// TableName 指定表名
func (DomainBinding) TableName() string {
	return "domain_bindings"
}

func (URLRewriteRule) TableName() string {
	return "url_rewrite_rules"
}

func (Site) TableName() string {
	return "sites"
}

// IsModuleBinding 检查是否为模块绑定
func (db *DomainBinding) IsModuleBinding() bool {
	return db.BindingType == BindingTypeModule && db.ModuleSlug != nil
}

// IsCategoryBinding 检查是否为栏目绑定
func (db *DomainBinding) IsCategoryBinding() bool {
	return db.BindingType == BindingTypeCategory && db.CategoryID != nil
}

// IsPlatformAdmin 检查是否为平台管理
func (db *DomainBinding) IsPlatformAdmin() bool {
	return db.BindingType == BindingTypePlatformAdmin
}

// GetModuleSlug 获取模块标识
func (db *DomainBinding) GetModuleSlug() string {
	if db.ModuleSlug != nil {
		return *db.ModuleSlug
	}
	return ""
}

// GetCategoryID 获取栏目ID
func (db *DomainBinding) GetCategoryID() uint {
	if db.CategoryID != nil {
		return *db.CategoryID
	}
	return 0
}

// HasURLRewrite 检查是否启用URL重写
func (db *DomainBinding) HasURLRewrite() bool {
	return db.URLRewriteEnabled
}

// GetActiveURLRules 获取激活的URL重写规则
func (db *DomainBinding) GetActiveURLRules() []URLRewriteRule {
	var activeRules []URLRewriteRule
	for _, rule := range db.URLRules {
		if rule.IsActive {
			activeRules = append(activeRules, rule)
		}
	}
	return activeRules
}

// IsActive 检查站点是否激活
func (s *Site) IsActive() bool {
	return s.IsActive
}

// GetPrimaryDomain 获取主域名
func (s *Site) GetPrimaryDomain() string {
	return s.Domain
}

// HasHTTPS 检查是否启用HTTPS
func (s *Site) HasHTTPS() bool {
	return s.Config.HTTPSEnabled
}

// ShouldForceHTTPS 检查是否强制HTTPS
func (s *Site) ShouldForceHTTPS() bool {
	return s.Config.ForceHTTPS
}

// PlatformUser 平台用户模型（超级管理员）
type PlatformUser struct {
	gorm.Model
	Username    string `gorm:"type:varchar(100);not null;uniqueIndex"`
	Email       string `gorm:"type:varchar(255);not null;uniqueIndex"`
	Password    string `gorm:"type:varchar(255);not null"`
	DisplayName string `gorm:"type:varchar(255)"`
	Avatar      string `gorm:"type:varchar(500)"`
	IsActive    bool   `gorm:"default:true"`
	LastLoginAt *gorm.DeletedAt

	// 权限配置
	Permissions PlatformPermissions `gorm:"embedded"`
}

// PlatformPermissions 平台权限配置
type PlatformPermissions struct {
	SiteManagement       bool `gorm:"default:true"`  // 站点管理
	DomainBinding        bool `gorm:"default:true"`  // 域名绑定
	SystemConfig         bool `gorm:"default:true"`  // 系统配置
	GlobalUserManagement bool `gorm:"default:true"`  // 全局用户管理
	SystemLogs           bool `gorm:"default:true"`  // 系统日志
	PlatformUserManagement bool `gorm:"default:false"` // 平台用户管理
}

// SystemLog 系统日志模型
type SystemLog struct {
	gorm.Model
	Level     string `gorm:"type:varchar(20);not null;index"`  // DEBUG, INFO, WARN, ERROR
	Module    string `gorm:"type:varchar(100);not null;index"` // 模块名称
	Action    string `gorm:"type:varchar(100);not null"`       // 操作名称
	Message   string `gorm:"type:text;not null"`               // 日志消息
	UserID    *uint  `gorm:"index"`                            // 操作用户ID
	UserType  string `gorm:"type:varchar(50)"`                 // 用户类型：platform, site, user
	SiteID    *uint  `gorm:"index"`                            // 站点ID
	IP        string `gorm:"type:varchar(45)"`                 // IP地址
	UserAgent string `gorm:"type:varchar(500)"`                // 用户代理
	Extra     string `gorm:"type:json"`                        // 额外数据（JSON格式）
}

// SystemConfig 系统配置模型
type SystemConfig struct {
	gorm.Model
	Key         string `gorm:"type:varchar(255);not null;uniqueIndex"`
	Value       string `gorm:"type:text"`
	Group       string `gorm:"type:varchar(100);index"`
	Description string `gorm:"type:varchar(500)"`
	IsPublic    bool   `gorm:"default:false"` // 是否为公开配置
}

// TableName 指定表名
func (PlatformUser) TableName() string {
	return "platform_users"
}

func (SystemLog) TableName() string {
	return "system_logs"
}

func (SystemConfig) TableName() string {
	return "system_configs"
}

// HasPermission 检查平台用户是否有指定权限
func (u *PlatformUser) HasPermission(permission string) bool {
	if !u.IsActive {
		return false
	}

	switch permission {
	case "site_management":
		return u.Permissions.SiteManagement
	case "domain_binding":
		return u.Permissions.DomainBinding
	case "system_config":
		return u.Permissions.SystemConfig
	case "global_user_management":
		return u.Permissions.GlobalUserManagement
	case "system_logs":
		return u.Permissions.SystemLogs
	case "platform_user_management":
		return u.Permissions.PlatformUserManagement
	default:
		return false
	}
}
