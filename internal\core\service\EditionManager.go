/*
Author: <PERSON><PERSON>eh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// EditionManager 版本管理器接口
type EditionManager interface {
	// 版本信息
	GetCurrentEdition() contract.Edition
	GetEditionInfo() *EditionInfo
	GetEditionFeatures() []string
	GetEditionLimits() *EditionLimits

	// 版本检查
	IsFeatureAvailable(featureName string) bool
	IsLimitExceeded(limitType string, currentValue int) bool
	CheckEditionRequirement(requiredEdition contract.Edition) bool

	// 功能访问检查（新增，支持商业授权）
	CheckFeatureAccess(edition contract.Edition, featureName string) error
	GetLimit(edition contract.Edition, limitType string) int

	// 版本升级/降级
	UpgradeEdition(ctx context.Context, targetEdition contract.Edition, licenseKey string) error
	DowngradeEdition(ctx context.Context, targetEdition contract.Edition) error

	// 功能限制
	GetFeatureLimit(featureName string) *FeatureLimit
	GetUsageStats() *UsageStats

	// 版本管理
	GetAvailableEditions() []EditionOption
	GetUpgradeOptions() []UpgradeOption
}

// EditionInfo 版本信息
type EditionInfo struct {
	Edition     contract.Edition `json:"edition"`
	DisplayName string           `json:"display_name"`
	Description string           `json:"description"`
	Price       string           `json:"price"`
	Features    []string         `json:"features"`
	Limits      *EditionLimits   `json:"limits"`
	IsActive    bool             `json:"is_active"`
	ExpiresAt   *time.Time       `json:"expires_at,omitempty"`
}

// EditionLimits 版本限制
type EditionLimits struct {
	MaxSites     int `json:"max_sites"`
	MaxUsers     int `json:"max_users"`
	MaxStorage   int `json:"max_storage_gb"`
	MaxBandwidth int `json:"max_bandwidth_gb"`
	MaxPages     int `json:"max_pages"`
	MaxPosts     int `json:"max_posts"`
}

// FeatureLimit 功能限制
type FeatureLimit struct {
	FeatureName string `json:"feature_name"`
	MaxUsage    int    `json:"max_usage"`
	CurrentUsage int   `json:"current_usage"`
	IsUnlimited bool   `json:"is_unlimited"`
	IsAvailable bool   `json:"is_available"`
}

// UsageStats 使用统计
type UsageStats struct {
	Sites     int `json:"sites"`
	Users     int `json:"users"`
	Storage   int `json:"storage_gb"`
	Bandwidth int `json:"bandwidth_gb"`
	Pages     int `json:"pages"`
	Posts     int `json:"posts"`
}

// EditionOption 版本选项
type EditionOption struct {
	Edition     contract.Edition `json:"edition"`
	DisplayName string           `json:"display_name"`
	Description string           `json:"description"`
	Price       string           `json:"price"`
	Features    []string         `json:"features"`
	Limits      *EditionLimits   `json:"limits"`
	IsRecommended bool           `json:"is_recommended"`
}

// UpgradeOption 升级选项
type UpgradeOption struct {
	FromEdition contract.Edition `json:"from_edition"`
	ToEdition   contract.Edition `json:"to_edition"`
	Price       string           `json:"price"`
	Discount    string           `json:"discount,omitempty"`
	Benefits    []string         `json:"benefits"`
	IsAvailable bool             `json:"is_available"`
}

// DefaultEditionManager 默认版本管理器实现
type DefaultEditionManager struct {
	// 当前版本信息
	currentEdition contract.Edition
	editionInfo    *EditionInfo
	
	// 版本配置
	editionConfigs map[contract.Edition]*EditionInfo
	featureMatrix  map[contract.Edition]map[string]bool
	
	// 依赖服务
	licenseManager contract.LicenseManager
	
	// 并发控制
	mu sync.RWMutex
	
	logger *zap.Logger
}

// EditionManagerParams fx依赖注入参数
type EditionManagerParams struct {
	fx.In

	LicenseManager contract.LicenseManager
	Logger         *zap.Logger
}

// NewDefaultEditionManager 创建默认版本管理器
func NewDefaultEditionManager(params EditionManagerParams) EditionManager {
	manager := &DefaultEditionManager{
		currentEdition: contract.EditionPersonal, // 默认个人版
		editionConfigs: make(map[contract.Edition]*EditionInfo),
		featureMatrix:  make(map[contract.Edition]map[string]bool),
		licenseManager: params.LicenseManager,
		logger:         params.Logger,
	}
	
	// 初始化版本配置
	manager.initializeEditionConfigs()
	manager.initializeFeatureMatrix()
	
	// 从许可证管理器获取当前版本
	manager.updateCurrentEdition()
	
	return manager
}

// initializeEditionConfigs 初始化版本配置
func (m *DefaultEditionManager) initializeEditionConfigs() {
	// 根据商业授权决策文档更新版本配置

	m.editionConfigs[contract.EditionPersonal] = &EditionInfo{
		Edition:     contract.EditionPersonal,
		DisplayName: "个人版",
		Description: "适合个人博客和小型网站使用，内容管理无限制",
		Price:       "免费",
		Features: []string{
			"基础内容管理（无限制）",
			"基础主题支持",
			"基础SEO工具",
			"基础用户管理",
			"API访问",
		},
		Limits: &EditionLimits{
			MaxSites: 1, // 1个站点
			MaxUsers: 3, // 3个管理用户
			// 删除页面、文章等限制，个人版内容无限制
		},
		IsActive: true,
	}
	
	m.editionConfigs[contract.EditionProfessional] = &EditionInfo{
		Edition:     contract.EditionProfessional,
		DisplayName: "专业版",
		Description: "适合中小企业和专业网站使用",
		Price:       "¥5,000/年",
		Features: []string{
			"个人版所有功能",
			"高级主题管理（自定义CSS、主题编辑器）",
			"高级SEO工具（结构化数据、站点地图）",
			"工作流管理（内容审核、发布流程）",
			"API访问权限（REST API、Webhook）",
			"优先技术支持",
		},
		Limits: &EditionLimits{
			MaxSites:     5,    // 5个站点
			MaxUsers:     20,   // 20个管理用户
			MaxStorage:   -1,   // 不限制存储（由服务器决定）
			MaxBandwidth: -1,   // 不限制带宽（由服务器决定）
			MaxPages:     1000, // 1000个页面
			MaxPosts:     2000, // 2000篇文章
		},
		IsActive: true,
	}

	m.editionConfigs[contract.EditionBusiness] = &EditionInfo{
		Edition:     contract.EditionBusiness,
		DisplayName: "商业版",
		Description: "适合大型企业和高流量网站使用",
		Price:       "¥15,000/年",
		Features: []string{
			"专业版所有功能",
			"高级用户管理（角色权限、用户组、SSO）",
			"企业级API（高级集成、批量操作、GraphQL）",
			"企业安全（审计日志、安全策略、合规性）",
			"企业集成（LDAP、Active Directory）",
			"企业分析（高级统计、自定义报表）",
			"专属客户经理",
			"SLA保障",
		},
		Limits: &EditionLimits{
			MaxSites:     -1, // 无限制
			MaxUsers:     -1, // 无限制
			MaxStorage:   -1, // 无限制
			MaxBandwidth: -1, // 无限制
			MaxPages:     -1, // 无限制
			MaxPosts:     -1, // 无限制
		},
		IsActive: true,
	}
}

// initializeFeatureMatrix 初始化功能矩阵
func (m *DefaultEditionManager) initializeFeatureMatrix() {

	
	// 个人版功能
	m.featureMatrix[contract.EditionPersonal] = map[string]bool{
		"basic_content":    true,
		"basic_theme":      true,
		"basic_user":       true,
		"advanced_theme":   true,
		"seo_basic":        true,
		"seo_advanced":     false,
		"workflow":         false,
		"api_access":       false,
		"advanced_user":    false,
		"enterprise_security": false,
		"custom_development":  false,
	}
	
	// 专业版功能
	m.featureMatrix[contract.EditionProfessional] = map[string]bool{
		"basic_content":    true,
		"basic_theme":      true,
		"basic_user":       true,
		"advanced_theme":   true,
		"seo_basic":        true,
		"seo_advanced":     true,
		"workflow":         true,
		"api_access":       true,
		"advanced_user":    true,
		"enterprise_security": false,
		"custom_development":  false,
	}
	
	// 商业版功能
	m.featureMatrix[contract.EditionBusiness] = map[string]bool{
		"basic_content":       true,
		"basic_theme":         true,
		"basic_user":          true,
		"advanced_theme":      true,
		"seo_basic":           true,
		"seo_advanced":        true,
		"workflow":            true,
		"api_access":          true,
		"advanced_user":       true,
		"business_security": true,
		"custom_development":  true,
	}
}

// updateCurrentEdition 更新当前版本
func (m *DefaultEditionManager) updateCurrentEdition() {
	licenseInfo := m.licenseManager.GetLicenseInfo()
	if licenseInfo != nil && licenseInfo.Edition != "" {
		m.mu.Lock()
		m.currentEdition = licenseInfo.Edition
		m.editionInfo = m.editionConfigs[m.currentEdition]
		m.mu.Unlock()
	}
}

// GetCurrentEdition 获取当前版本
func (m *DefaultEditionManager) GetCurrentEdition() contract.Edition {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return m.currentEdition
}

// GetEditionInfo 获取版本信息
func (m *DefaultEditionManager) GetEditionInfo() *EditionInfo {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.editionInfo == nil {
		return m.editionConfigs[m.currentEdition]
	}

	// 返回副本
	info := *m.editionInfo
	return &info
}

// GetEditionFeatures 获取版本功能列表
func (m *DefaultEditionManager) GetEditionFeatures() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if config, exists := m.editionConfigs[m.currentEdition]; exists {
		// 返回副本
		features := make([]string, len(config.Features))
		copy(features, config.Features)
		return features
	}

	return []string{}
}

// GetEditionLimits 获取版本限制
func (m *DefaultEditionManager) GetEditionLimits() *EditionLimits {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if config, exists := m.editionConfigs[m.currentEdition]; exists && config.Limits != nil {
		// 返回副本
		limits := *config.Limits
		return &limits
	}

	return &EditionLimits{}
}

// IsFeatureAvailable 检查功能是否可用
func (m *DefaultEditionManager) IsFeatureAvailable(featureName string) bool {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if features, exists := m.featureMatrix[m.currentEdition]; exists {
		return features[featureName]
	}

	return false
}

// IsLimitExceeded 检查是否超出限制
func (m *DefaultEditionManager) IsLimitExceeded(limitType string, currentValue int) bool {
	limits := m.GetEditionLimits()

	switch limitType {
	case "sites":
		return limits.MaxSites > 0 && currentValue >= limits.MaxSites
	case "users":
		return limits.MaxUsers > 0 && currentValue >= limits.MaxUsers
	default:
		return false
	}
}

// CheckFeatureAccess 检查功能访问权限（新增方法）
func (m *DefaultEditionManager) CheckFeatureAccess(edition contract.Edition, featureName string) error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if features, exists := m.featureMatrix[edition]; exists {
		if features[featureName] {
			return nil // 功能可用
		}
	}

	return fmt.Errorf("feature %s not available in %s edition", featureName, edition)
}

// GetLimit 获取指定版本的限制值（新增方法）
func (m *DefaultEditionManager) GetLimit(edition contract.Edition, limitType string) int {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if config, exists := m.editionConfigs[edition]; exists && config.Limits != nil {
		switch limitType {
		case "max_sites":
			return config.Limits.MaxSites
		case "max_admin_users", "max_users":
			return config.Limits.MaxUsers
		case "api_calls_per_day":
			// 这个限制需要从配置文件读取，暂时返回默认值
			switch edition {
			case contract.EditionPersonal:
				return 1000
			case contract.EditionProfessional:
				return 10000
			case contract.EditionBusiness:
				return -1 // 无限制
			}

		default:
			return 0
		}
	}

	return 0 // 默认限制为0
}

// CheckEditionRequirement 检查版本要求
func (m *DefaultEditionManager) CheckEditionRequirement(requiredEdition contract.Edition) bool {
	currentLevel := m.getEditionLevel(m.GetCurrentEdition())
	requiredLevel := m.getEditionLevel(requiredEdition)

	return currentLevel >= requiredLevel
}

// getEditionLevel 获取版本级别
func (m *DefaultEditionManager) getEditionLevel(edition contract.Edition) int {
	levels := map[contract.Edition]int{
		contract.EditionPersonal:     0,
		contract.EditionProfessional: 1,
		contract.EditionBusiness:     2,
	}

	if level, exists := levels[edition]; exists {
		return level
	}

	return -1
}

// UpgradeEdition 升级版本
func (m *DefaultEditionManager) UpgradeEdition(ctx context.Context, targetEdition contract.Edition, licenseKey string) error {
	currentLevel := m.getEditionLevel(m.GetCurrentEdition())
	targetLevel := m.getEditionLevel(targetEdition)

	if targetLevel <= currentLevel {
		return fmt.Errorf("cannot upgrade to a lower or same edition")
	}

	// 验证许可证
	if err := m.licenseManager.ActivateModuleLicense(ctx, "system", licenseKey); err != nil {
		return fmt.Errorf("license activation failed: %w", err)
	}

	// 更新当前版本
	m.mu.Lock()
	m.currentEdition = targetEdition
	m.editionInfo = m.editionConfigs[targetEdition]
	m.mu.Unlock()

	m.logger.Info("Edition upgraded successfully",
		zap.String("from", string(m.currentEdition)),
		zap.String("to", string(targetEdition)),
	)

	return nil
}

// DowngradeEdition 降级版本
func (m *DefaultEditionManager) DowngradeEdition(ctx context.Context, targetEdition contract.Edition) error {
	currentLevel := m.getEditionLevel(m.GetCurrentEdition())
	targetLevel := m.getEditionLevel(targetEdition)

	if targetLevel >= currentLevel {
		return fmt.Errorf("cannot downgrade to a higher or same edition")
	}

	// 检查当前使用情况是否超出目标版本限制
	if err := m.validateDowngrade(targetEdition); err != nil {
		return fmt.Errorf("downgrade validation failed: %w", err)
	}

	// 停用当前许可证
	if err := m.licenseManager.DeactivateModuleLicense(ctx, "system"); err != nil {
		m.logger.Warn("Failed to deactivate license during downgrade", zap.Error(err))
	}

	// 更新当前版本
	m.mu.Lock()
	m.currentEdition = targetEdition
	m.editionInfo = m.editionConfigs[targetEdition]
	m.mu.Unlock()

	m.logger.Info("Edition downgraded successfully",
		zap.String("from", string(m.currentEdition)),
		zap.String("to", string(targetEdition)),
	)

	return nil
}

// validateDowngrade 验证降级是否可行
func (m *DefaultEditionManager) validateDowngrade(targetEdition contract.Edition) error {
	// TODO: 实现实际的使用情况检查
	// 1. 检查当前站点数量
	// 2. 检查当前用户数量
	// 3. 检查存储使用量
	// 4. 检查其他限制项

	targetLimits := m.editionConfigs[targetEdition].Limits
	currentUsage := m.GetUsageStats()

	if targetLimits.MaxSites > 0 && currentUsage.Sites > targetLimits.MaxSites {
		return fmt.Errorf("current sites (%d) exceed target limit (%d)", currentUsage.Sites, targetLimits.MaxSites)
	}

	if targetLimits.MaxUsers > 0 && currentUsage.Users > targetLimits.MaxUsers {
		return fmt.Errorf("current users (%d) exceed target limit (%d)", currentUsage.Users, targetLimits.MaxUsers)
	}

	return nil
}

// GetFeatureLimit 获取功能限制
func (m *DefaultEditionManager) GetFeatureLimit(featureName string) *FeatureLimit {
	// TODO: 实现具体的功能限制查询
	// 这里需要根据功能名称返回具体的限制信息

	return &FeatureLimit{
		FeatureName:  featureName,
		MaxUsage:     -1, // 暂时返回无限制
		CurrentUsage: 0,
		IsUnlimited:  true,
		IsAvailable:  m.IsFeatureAvailable(featureName),
	}
}

// GetUsageStats 获取使用统计
func (m *DefaultEditionManager) GetUsageStats() *UsageStats {
	// TODO: 实现实际的使用统计查询
	// 这里需要从数据库或其他服务获取实际使用情况

	return &UsageStats{
		Sites:     1,  // 示例数据
		Users:     3,  // 示例数据
		Storage:   0,  // 示例数据
		Bandwidth: 0,  // 示例数据
		Pages:     10, // 示例数据
		Posts:     20, // 示例数据
	}
}

// GetAvailableEditions 获取可用版本列表
func (m *DefaultEditionManager) GetAvailableEditions() []EditionOption {
	var options []EditionOption

	m.mu.RLock()
	defer m.mu.RUnlock()

	for edition, config := range m.editionConfigs {
		if config.IsActive {
			option := EditionOption{
				Edition:       edition,
				DisplayName:   config.DisplayName,
				Description:   config.Description,
				Price:         config.Price,
				Features:      make([]string, len(config.Features)),
				Limits:        config.Limits,
				IsRecommended: edition == contract.EditionProfessional, // 推荐专业版
			}
			copy(option.Features, config.Features)
			options = append(options, option)
		}
	}

	return options
}

// GetUpgradeOptions 获取升级选项
func (m *DefaultEditionManager) GetUpgradeOptions() []UpgradeOption {
	var options []UpgradeOption

	currentEdition := m.GetCurrentEdition()
	currentLevel := m.getEditionLevel(currentEdition)

	m.mu.RLock()
	defer m.mu.RUnlock()

	for edition, config := range m.editionConfigs {
		targetLevel := m.getEditionLevel(edition)

		// 只显示可以升级的版本
		if targetLevel > currentLevel && config.IsActive {
			option := UpgradeOption{
				FromEdition: currentEdition,
				ToEdition:   edition,
				Price:       config.Price,
				Benefits:    m.getUpgradeBenefits(currentEdition, edition),
				IsAvailable: true,
			}

			// 添加折扣信息
			if edition == contract.EditionProfessional {
				option.Discount = "首年8折优惠"
			}

			options = append(options, option)
		}
	}

	return options
}

// getUpgradeBenefits 获取升级收益
func (m *DefaultEditionManager) getUpgradeBenefits(fromEdition, toEdition contract.Edition) []string {
	fromFeatures := m.featureMatrix[fromEdition]
	toFeatures := m.featureMatrix[toEdition]

	var benefits []string

	for feature, available := range toFeatures {
		if available && !fromFeatures[feature] {
			switch feature {
			case "advanced_theme":
				benefits = append(benefits, "解锁高级主题功能")
			case "seo_basic":
				benefits = append(benefits, "基础SEO优化工具")
			case "seo_advanced":
				benefits = append(benefits, "高级SEO分析和优化")
			case "workflow":
				benefits = append(benefits, "工作流管理系统")
			case "api_access":
				benefits = append(benefits, "API访问权限")
			case "advanced_user":
				benefits = append(benefits, "高级用户权限管理")
			case "enterprise_security":
				benefits = append(benefits, "企业级安全功能")
			case "custom_development":
				benefits = append(benefits, "定制开发支持")
			}
		}
	}

	// 添加限制提升信息
	fromLimits := m.editionConfigs[fromEdition].Limits
	toLimits := m.editionConfigs[toEdition].Limits

	if toLimits.MaxSites > fromLimits.MaxSites {
		if toLimits.MaxSites == -1 {
			benefits = append(benefits, "无限站点数量")
		} else {
			benefits = append(benefits, fmt.Sprintf("站点数量提升至%d个", toLimits.MaxSites))
		}
	}

	if toLimits.MaxUsers > fromLimits.MaxUsers {
		if toLimits.MaxUsers == -1 {
			benefits = append(benefits, "无限用户数量")
		} else {
			benefits = append(benefits, fmt.Sprintf("用户数量提升至%d个", toLimits.MaxUsers))
		}
	}

	return benefits
}
