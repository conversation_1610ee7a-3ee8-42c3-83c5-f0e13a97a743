/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/theme/application/dto/theme_dto.go
 * @Description: Defines DTOs for the theme module.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package dto

// Manifest represents the structure of theme.json.
// Using map[string]interface{} for flexibility with layouts, templates, and settings.
type Manifest struct {
	Name        string                            `json:"name"`
	Type        string                            `json:"type"` // "frontend" or "backend"
	Version     string                            `json:"version"`
	Description string                            `json:"description"`
	Author      string                            `json:"author"`
	Extends     *string                           `json:"extends"` // Use a pointer to handle null
	Layouts     map[string]map[string]interface{} `json:"layouts"`
	Templates   map[string]map[string]interface{} `json:"templates"`
	Blocks      map[string]map[string]interface{} `json:"blocks"`
	Components  map[string]map[string]interface{} `json:"components"`
	Settings    []map[string]interface{}          `json:"settings"`
} 