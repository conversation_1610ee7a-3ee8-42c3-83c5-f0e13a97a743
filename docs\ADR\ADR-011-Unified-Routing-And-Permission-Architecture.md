# ADR-011: Unified Routing, Permission and Communication Architecture

## Status
Accepted

## Context
During a series of deep architectural reviews, several critical issues were identified in our existing design, leading to repeated refactoring and architectural ambiguity:
1.  **Unclear Entrypoint Semantics**: The distinction between APIs for the backend management UI, frontend websites, and third-party developers was blurred, especially concerning the `/api` prefix.
2.  **Implicit Core-Module Dependency**: The core routing and permission-checking logic had a tendency to depend directly on module-specific implementations (e.g., the `user` module), violating proper architectural layering and creating tight coupling.
3.  **Complex Permission Logic**: The initial permission model was not robust enough to handle complex, multi-tenant RBAC scenarios, where permissions depend on user roles, actions, and sometimes site context.
4.  **Inconsistent Inter-Module Communication**: The mechanism for one module to call another's API was not clearly defined, leading to potential inconsistencies.

A unified, scalable, and maintainable architecture was needed to address these challenges definitively.

## Decision
We have decided to implement a new, unified top-level architecture based on three core principles: **semantic routing**, **dependency inversion for permissions**, and **standardized contracts**.

### 1. Three-Tier Semantic Routing Entrypoints
The `router.Manager` will define three distinct, top-level routing groups, each with a clear purpose and its own middleware pipeline:
*   **`/` (Frontend/Public Root)**: Serves our "official" frontend applications (e.g., React/Next.js websites). It is responsible for user-facing interactions.
    *   **Middleware**: Must use a **mandatory `SiteResolver`** to identify the site context. Authentication is optional, for member-only content.
*   **`/admin` (Configurable Backend Path)**: Serves the official React-based administration panel. The path is configurable via `config.admin_path` for security.
    *   **Middleware**: Uses a **mandatory `AdminAuth`** (for user session), an **optional `SiteResolver`** (for site-specific admin actions), and a **declarative `Permission` checker**.
*   **`/api` (Third-Party/Platform)**: Serves external developers, mobile apps, and other machine-to-machine integrations.
    *   **Middleware**: Must use **strict, non-session-based authentication** (e.g., API Keys, OAuth 2.0).

### 2. Dependency Inversion for Permissions (RBAC)
To decouple the core from the `user` module, we will use the Dependency Inversion Principle.
*   **Abstract Contract**: A `PermissionChecker` interface will be defined in `pkg/contract/Permission.go`. This interface will expose a `Wrap(permission string, handler gin.HandlerFunc) gin.HandlerFunc` method.
*   **Module as Provider**: The `user` module will provide the concrete implementation of the `PermissionChecker` interface. It will register this implementation with the `fx` dependency injection container.
*   **Controllers as Consumers**: Any controller requiring permission-protected routes will depend on the `contract.PermissionChecker` interface, not a concrete service. It will wrap its route handlers declaratively: `rg.POST("/posts", c.permission.Wrap("post.create", c.CreatePost))`. The `Wrap` method handles all the logic of checking the current user's roles and permissions against the required permission string, considering the site context if applicable.

### 3. Unified Inter-Module Communication
*   **Simplified `APIClient`**: The `APIClient` interface and its implementation will be simplified. It will no longer need `serviceName` or `target` parameters.
*   **RESTful Path-Based Calls**: To call another module's API, the caller must know the full, unambiguous path of the target endpoint (e.g., `/admin/users`, `/api/posts/123`). The `APIClient` simply takes this path and the request payload, making it a pure, universal HTTP transport layer. `client.Post(ctx, "/admin/users", payload)`.

## Consequences
### Positive
*   **High Decoupling**: The core system is now completely decoupled from module-specific implementations. Modules are decoupled from each other.
*   **Clear Separation of Concerns**: The roles of the three entrypoints (`/`, `/admin`, `/api`) are semantically clear and enforced by distinct middleware pipelines.
*   **Robust & Scalable RBAC**: The declarative permission system is powerful, easy to use in controllers, and separates security logic from business logic. It naturally handles multi-tenancy.
*   **Architectural Stability**: This design is robust and scalable, providing a stable foundation that should prevent future large-scale refactoring.
*   **Testability**: Decoupling via interfaces makes all components, especially controllers and core services, much easier to test in isolation.

### Negative
*   **Increased Initial Complexity**: The introduction of the `PermissionChecker` interface and the DI wiring adds a layer of abstraction that requires understanding.
*   **Boilerplate in Controllers**: Every permission-protected route now needs to be wrapped (e.g., `c.permission.Wrap(...)`), adding a small amount of boilerplate code. However, this boilerplate makes security explicit.

## Alternatives Considered
*   **Core-Directly-Depends-on-Module**: We considered letting the core router or middleware directly call the `PermissionService` from the `user` module. This was rejected as it creates tight coupling and violates architectural layering.
*   **Moving User/Permission Logic to Core**: We considered moving the entire user and permission system into the `core`. This was rejected because it would bloat the core and violate the principle of modularity. The `user` module is a standard application module, not a core infrastructure component.
*   **Target-Aware APIClient**: We considered an `APIClient` that takes a `target` enum (`AdminAPI`, `FrontendAPI`). This was rejected in favor of a simpler, more RESTful approach where the caller provides the full, unambiguous path, making the `APIClient` itself stateless and dumber. 