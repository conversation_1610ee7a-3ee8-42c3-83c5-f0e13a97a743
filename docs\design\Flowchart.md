# GACMS 用户操作流程图

本文档使用 Mermaid 语法描述 GACMS 的核心用户操作流程，包括后台管理系统和前台用户访问的主要交互路径和功能流程。

## 1. 核心后台管理流程

### 1.1 用户登录与仪表盘

```mermaid
graph TD
    A[用户访问后台登录页] --> B{输入凭据};
    B -- 凭据正确 --> C[进入后台仪表盘 Dashboard];
    C --> D[查看站点概览信息];
    C --> E[快速访问常用功能入口];
    B -- 凭据错误 --> F[提示登录失败];
    F --> A;
```

### 1.2 内容管理 (以文章为例)

```mermaid
graph TD
    subgraph 文章管理
        A[进入文章管理列表] --> B{选择操作};
        B -- 新建文章 --> C[进入文章编辑器];
        B -- 编辑现有文章 --> D[加载文章内容至编辑器];
        B -- 删除文章 --> E{确认删除?};
        E -- 是 --> F[执行删除操作];
        E -- 否 --> A;
        C --> G{填写文章信息};
        G -- 标题、内容、摘要等 --> H[设置分类、标签];
        H --> I[选择特色图片];
        I --> J[设置发布选项：立即发布/定时发布/存为草稿];
        J --> K{保存/发布文章?};
        K -- 保存/发布 --> L[文章保存成功/发布成功];
        L --> A;
        K -- 取消 --> A;
        D --> G;
    end
```

### 1.3 栏目管理

```mermaid
graph TD
    subgraph 栏目管理
        A[进入栏目管理列表] --> B{选择操作};
        B -- 新建栏目 --> C[填写栏目信息：名称、别名、父栏目、模板等];
        C --> D[保存栏目];
        D --> A;
        B -- 编辑栏目 --> E[加载栏目信息进行修改];
        E --> D;
        B -- 删除栏目 --> F{确认删除?（注意子栏目和内容处理）};
        F -- 是 --> G[执行删除操作];
        G --> A;
        F -- 否 --> A;
    end
```

### 1.4 主题管理

```mermaid
graph TD
    subgraph 主题管理
        A[进入主题管理界面] --> B[浏览已安装主题列表];
        B --> C{选择操作};
        C -- 激活主题 --> D[设置选中主题为当前站点主题];
        D --> A;
        C -- 预览主题 --> E[在新标签页预览主题效果];
        C -- 删除主题 --> F{确认删除?};
        F -- 是 --> G[执行删除操作];
        G --> A;
        F -- 否 --> A;
        C -- 上传新主题 --> H[选择主题包进行上传安装];
        H --> I[主题安装成功/失败];
        I --> A;
        C -- 访问主题市场 (可选) --> J[跳转到主题市场浏览/购买主题];
    end
```

### 1.5 插件管理

```mermaid
graph TD
    subgraph 插件管理
        A[进入插件管理界面] --> B[浏览已安装插件列表];
        B --> C{选择操作};
        C -- 启用插件 --> D[激活插件功能];
        D --> A;
        C -- 禁用插件 --> E[停用插件功能];
        E --> A;
        C -- 配置插件 (若插件支持) --> F[进入插件配置页面];
        F --> G[保存配置];
        G --> A;
        C -- 删除插件 --> H{确认删除?};
        H -- 是 --> I[执行删除操作];
        I --> A;
        H -- 否 --> A;
        C -- 上传新插件 --> J[选择插件包进行上传安装];
        J --> K[插件安装成功/失败];
        K --> A;
    end
```

### 1.6 用户与权限管理

```mermaid
graph TD
    subgraph 用户管理
        A[进入用户管理列表] --> B{选择操作};
        B -- 新建用户 --> C[填写用户信息：用户名、密码、邮箱、角色等];
        C --> D[保存用户];
        D --> A;
        B -- 编辑用户 --> E[加载用户信息进行修改];
        E --> D;
        B -- 删除用户 --> F{确认删除?};
        F -- 是 --> G[执行删除操作];
        G --> A;
        F -- 否 --> A;
    end

    subgraph 角色与权限管理
        H[进入角色管理列表] --> I{选择操作};
        I -- 新建角色 --> J[填写角色名称、描述];
        J --> K[为角色分配权限（勾选权限项）];
        K --> L[保存角色];
        L --> H;
        I -- 编辑角色 --> M[加载角色信息及权限进行修改];
        M --> K;
        I -- 删除角色 --> N{确认删除?（注意关联用户处理）};
        N -- 是 --> O[执行删除操作];
        O --> H;
        N -- 否 --> H;
    end
```

## 2. 前台用户访问流程 (示例)

```mermaid
graph TD
    A[用户访问网站首页] --> B[浏览页面内容];
    B --> C{发现感兴趣内容};
    C -- 点击链接/按钮 --> D[跳转到对应页面（文章页、栏目页等）];
    D --> E[阅读内容];
    E --> F{进行交互 (如评论、分享，若支持)};
    F --> D;
    C -- 使用导航菜单 --> G[跳转到其他主要栏目];
    G --> B;
    C -- 使用搜索功能 --> H[输入关键词搜索];
    H --> I[显示搜索结果页];
    I --> D;
```

*(注：以上流程图为核心功能的高度概括，以下将提供更多详细的操作流程。)*

## 3. 详细操作流程

### 3.1 系统设置流程

```mermaid
graph TD
    A[进入系统设置页面] --> B[显示设置选项卡]
    B --> C{选择设置类别}
    C -->|基本设置| D[显示基本设置表单]
    C -->|安全设置| E[显示安全设置表单]
    C -->|高级设置| F[显示高级设置表单]
    D --> G[修改设置]
    E --> G
    F --> G
    G --> H{用户操作}
    H -->|保存| I[验证设置数据]
    I -->|验证通过| J[保存设置数据]
    I -->|验证失败| K[显示错误信息]
    K --> G
    J --> L[显示保存成功提示]
    L --> M[刷新设置页面]
    H -->|重置| N{确认重置?}
    N -->|是| O[恢复默认设置]
    N -->|否| G
    O --> P[显示重置成功提示]
    P --> M
```

### 3.2 媒体库管理流程

```mermaid
graph TD
    A[进入媒体库页面] --> B[显示媒体文件网格]
    B --> C{用户操作}
    C -->|上传文件| D[打开文件选择器]
    D --> E[选择文件]
    E --> F[上传文件]
    F --> G[显示上传进度]
    G --> H[上传完成]
    H --> I[更新媒体库显示]
    I --> C
    C -->|搜索| J[输入搜索条件]
    J --> K[提交搜索]
    K --> L[显示搜索结果]
    L --> C
    C -->|筛选类型| M[选择文件类型]
    M --> N[应用筛选]
    N --> O[显示筛选结果]
    O --> C
    C -->|选择文件| P[显示文件详情]
    P --> Q{文件操作}
    Q -->|编辑信息| R[显示编辑表单]
    R --> S[保存更改]
    S --> T[更新文件信息]
    T --> P
    Q -->|删除| U{确认删除?}
    U -->|是| V[执行删除操作]
    U -->|否| P
    V --> W[显示操作结果]
    W --> B
    Q -->|插入到内容| X[复制文件URL]
    X --> Y[返回到编辑器]
```

## 4. 统一操作流程模式

在整个 GACMS 后台管理系统中，我们遵循以下统一的操作流程模式：

1. **列表-详情模式**：大多数内容管理页面采用列表视图和详情视图相结合的模式，用户可以从列表快速进入详情页进行编辑。

2. **操作确认机制**：所有可能造成数据丢失的操作（如删除）都会有确认对话框，防止误操作。

3. **即时反馈原则**：所有用户操作都会有明确的成功或失败反馈，通常以通知提示的形式出现。

4. **渐进式表单验证**：表单在提交时进行验证，并在出错字段旁即时显示错误提示。

5. **状态保持机制**：系统会记住用户的筛选、排序和分页选择，即使在页面刷新后也能保持这些状态。

6. **批量操作支持**：对于列表页面，支持选择多个项目进行批量操作，提高工作效率。

## 5. 移动端适配考虑

移动端界面遵循相同的操作流程逻辑，但在交互设计上有以下调整：

1. **简化视图**：移动端界面会隐藏部分次要功能，专注于核心操作。

2. **触摸优化**：所有可点击元素都有足够大的触摸区域，确保在触摸屏上易于操作。

3. **手势支持**：添加滑动、长按等移动端常用手势，增强操作便捷性。

4. **分步表单**：复杂表单在移动端会拆分为多个步骤，减轻单页信息负担。