/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/GinConventionHandler.go
 * @Description: Gin约定路由处理器，实现基于约定的路由解析和模块懒加载
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"fmt"
	"strings"

	"gacms/pkg/contract"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// GinConventionHandler Gin约定路由处理器
type GinConventionHandler struct {
	config        contract.Config
	moduleFactory *ModuleProxyFactory
	eventMgr      contract.EventManager
	logger        *zap.Logger
	
	// 入口点配置
	adminEntryPoint  string
	apiEntryPoint    string
	publicEntryPoint string
}

// NewGinConventionHandler 创建Gin约定路由处理器
func NewGinConventionHandler(
	config contract.Config,
	moduleFactory *ModuleProxyFactory,
	eventMgr contract.EventManager,
	logger *zap.Logger,
) *GinConventionHandler {
	handler := &GinConventionHandler{
		config:        config,
		moduleFactory: moduleFactory,
		eventMgr:      eventMgr,
		logger:        logger,
	}
	
	// 加载入口点配置
	handler.loadEntryPointConfig()
	
	return handler
}

// loadEntryPointConfig 加载入口点配置
func (h *GinConventionHandler) loadEntryPointConfig() {
	h.adminEntryPoint = h.config.GetString("routing.entry_points.admin")
	h.apiEntryPoint = h.config.GetString("routing.entry_points.api")
	h.publicEntryPoint = h.config.GetString("routing.entry_points.public")

	// 设置默认值
	if h.adminEntryPoint == "" {
		h.adminEntryPoint = "admin"
	}
	if h.apiEntryPoint == "" {
		h.apiEntryPoint = "api"
	}
	if h.publicEntryPoint == "" {
		h.publicEntryPoint = ""
	}

	h.logger.Info("Entry points configured for Gin handler",
		zap.String("admin_entry", h.adminEntryPoint),
		zap.String("api_entry", h.apiEntryPoint),
		zap.String("public_entry", h.publicEntryPoint),
	)
}

// HandleConvention 处理约定路由
func (h *GinConventionHandler) HandleConvention(entryPoint string) gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Param("path")
		method := c.Request.Method
		
		h.logger.Debug("Handling convention route",
			zap.String("entry_point", entryPoint),
			zap.String("path", path),
			zap.String("method", method),
		)
		
		// 1. 解析约定路由
		route := h.parseConventionRoute(entryPoint, path, method)
		if route == nil {
			h.logger.Debug("Failed to parse convention route",
				zap.String("entry_point", entryPoint),
				zap.String("path", path),
			)
			c.JSON(404, gin.H{"error": "Route not found"})
			return
		}
		
		// 2. 权限检查
		if !h.checkEntryPointAccess(entryPoint, c) {
			h.logger.Debug("Access denied for entry point",
				zap.String("entry_point", entryPoint),
				zap.String("path", path),
			)
			
			// 根据配置决定返回403还是404
			if entryPoint == "admin" && h.config.GetBool("routing.security.hide_admin_path") {
				c.JSON(404, gin.H{"error": "Not found"})
			} else {
				c.JSON(403, gin.H{"error": "Forbidden"})
			}
			return
		}
		
		// 3. 获取站点ID（多租户支持）
		siteID := h.getSiteID(c)
		
		// 4. 懒加载模块
		moduleInstance, err := h.moduleFactory.GetOrCreateModule(route.Module, siteID)
		if err != nil {
			h.logger.Error("Failed to load module",
				zap.String("module", route.Module),
				zap.Uint("site_id", siteID),
				zap.Error(err),
			)
			c.JSON(500, gin.H{"error": "Internal server error"})
			return
		}
		
		// 5. 获取控制器
		controller, exists := moduleInstance.GetController(route.Controller)
		if !exists {
			h.logger.Debug("Controller not found",
				zap.String("module", route.Module),
				zap.String("controller", route.Controller),
			)
			c.JSON(404, gin.H{"error": "Controller not found"})
			return
		}
		
		// 6. 调用控制器方法
		err = h.invokeControllerMethod(controller, route.Action, c, route.Params)
		if err != nil {
			h.logger.Error("Failed to invoke controller method",
				zap.String("module", route.Module),
				zap.String("controller", route.Controller),
				zap.String("action", route.Action),
				zap.Error(err),
			)
			c.JSON(500, gin.H{"error": "Internal server error"})
			return
		}
		
		h.logger.Debug("Convention route handled successfully",
			zap.String("entry_point", entryPoint),
			zap.String("module", route.Module),
			zap.String("controller", route.Controller),
			zap.String("action", route.Action),
		)
	}
}

// ConventionRoute 约定路由信息
type ConventionRoute struct {
	EntryPoint string            `json:"entry_point"`
	Module     string            `json:"module"`
	Controller string            `json:"controller"`
	Action     string            `json:"action"`
	Params     map[string]string `json:"params"`
}

// parseConventionRoute 解析约定路由
func (h *GinConventionHandler) parseConventionRoute(entryPoint, path, method string) *ConventionRoute {
	// 移除前导斜杠并分割路径
	parts := strings.Split(strings.Trim(path, "/"), "/")
	
	if len(parts) < 1 || parts[0] == "" {
		return nil
	}
	
	route := &ConventionRoute{
		EntryPoint: entryPoint,
		Params:     make(map[string]string),
	}
	
	// 解析模块名
	route.Module = parts[0]
	
	// 解析控制器名
	if len(parts) >= 2 && parts[1] != "" {
		route.Controller = h.normalizeControllerName(parts[1])
	} else {
		route.Controller = h.normalizeControllerName(route.Module)
	}
	
	// 解析方法名
	if len(parts) >= 3 && parts[2] != "" {
		route.Action = h.normalizeMethodName(parts[2])
	} else {
		route.Action = h.getDefaultMethodName(method, entryPoint)
	}
	
	// 提取参数
	if len(parts) > 3 {
		for i, param := range parts[3:] {
			route.Params[fmt.Sprintf("param%d", i)] = param
		}
	}
	
	return route
}

// getDefaultMethodName 根据HTTP方法和入口点获取默认方法名
func (h *GinConventionHandler) getDefaultMethodName(httpMethod, entryPoint string) string {
	switch entryPoint {
	case "admin":
		switch strings.ToUpper(httpMethod) {
		case "GET":
			return "AdminIndex"
		case "POST":
			return "AdminStore"
		case "PUT", "PATCH":
			return "AdminUpdate"
		case "DELETE":
			return "AdminDelete"
		default:
			return "AdminIndex"
		}
	case "api":
		switch strings.ToUpper(httpMethod) {
		case "GET":
			return "ApiIndex"
		case "POST":
			return "ApiStore"
		case "PUT", "PATCH":
			return "ApiUpdate"
		case "DELETE":
			return "ApiDelete"
		default:
			return "ApiIndex"
		}
	default: // public
		switch strings.ToUpper(httpMethod) {
		case "GET":
			return "Index"
		case "POST":
			return "Store"
		case "PUT", "PATCH":
			return "Update"
		case "DELETE":
			return "Delete"
		default:
			return "Index"
		}
	}
}

// normalizeControllerName 标准化控制器名称
func (h *GinConventionHandler) normalizeControllerName(name string) string {
	if name == "" {
		return ""
	}
	
	// 首字母大写，并添加Controller后缀（如果没有的话）
	normalized := strings.Title(strings.ToLower(name))
	if !strings.HasSuffix(normalized, "Controller") {
		normalized += "Controller"
	}
	
	return normalized
}

// normalizeMethodName 标准化方法名称
func (h *GinConventionHandler) normalizeMethodName(name string) string {
	if name == "" {
		return ""
	}
	
	// 首字母大写
	return strings.Title(strings.ToLower(name))
}

// checkEntryPointAccess 检查入口点访问权限
func (h *GinConventionHandler) checkEntryPointAccess(entryPoint string, c *gin.Context) bool {
	switch entryPoint {
	case "admin":
		return h.checkAdminAccess(c)
	case "api":
		return h.checkAPIAccess(c)
	case "public":
		return true
	default:
		return false
	}
}

// checkAdminAccess 检查管理员访问权限
func (h *GinConventionHandler) checkAdminAccess(c *gin.Context) bool {
	// TODO: 实现真正的管理员权限检查
	h.logger.Debug("Checking admin access",
		zap.String("path", c.Request.URL.Path),
		zap.String("remote_addr", c.ClientIP()),
	)
	
	// 暂时允许所有访问
	return true
}

// checkAPIAccess 检查API访问权限
func (h *GinConventionHandler) checkAPIAccess(c *gin.Context) bool {
	// TODO: 实现真正的API认证检查
	h.logger.Debug("Checking API access",
		zap.String("path", c.Request.URL.Path),
		zap.String("user_agent", c.Request.UserAgent()),
	)
	
	// 暂时允许所有访问
	return true
}

// getSiteID 获取站点ID（多租户支持）
func (h *GinConventionHandler) getSiteID(c *gin.Context) uint {
	// TODO: 从中间件或上下文中获取站点ID
	// 暂时返回默认站点ID
	return 1
}

// invokeControllerMethod 调用控制器方法
func (h *GinConventionHandler) invokeControllerMethod(controller interface{}, action string, c *gin.Context, params map[string]string) error {
	// TODO: 使用反射调用控制器方法
	// 或者通过事件系统调用
	
	h.logger.Debug("Invoking controller method",
		zap.String("controller", fmt.Sprintf("%T", controller)),
		zap.String("action", action),
		zap.Any("params", params),
	)
	
	// 临时实现：直接返回成功
	c.JSON(200, gin.H{
		"message": "Controller method invoked",
		"controller": fmt.Sprintf("%T", controller),
		"action": action,
		"params": params,
	})
	
	return nil
}
