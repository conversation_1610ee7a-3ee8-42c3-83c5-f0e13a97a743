/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/actionlog/infrastructure/persistence/ActionLogGormRepository.go
 * @Description: GORM implementation of the action log repository.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package persistence

import (
	"context"
	"gacms/internal/modules/actionlog/domain/contract"
	"gacms/internal/modules/actionlog/domain/model"
	"gacms/pkg/contract"
)

type ActionLogGormRepository struct {
	db contract.Database
}

func NewActionLogGormRepository(db contract.Database) contract.ActionLogRepository {
	return &ActionLogGormRepository{db: db}
}

func (r *ActionLogGormRepository) Create(ctx context.Context, log *model.ActionLog) error {
	return r.db.DB(ctx).Create(log).Error
}

func (r *ActionLogGormRepository) List(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*model.ActionLog, int64, error) {
	var logs []*model.ActionLog
	var total int64

	query := r.db.DB(ctx).Model(&model.ActionLog{})

	if filters != nil {
		for key, value := range filters {
			query = query.Where(key+" = ?", value)
		}
	}

	offset := (page - 1) * pageSize
	err := query.Order("id desc").Count(&total).Limit(pageSize).Offset(offset).Find(&logs).Error
	return logs, total, err
} 