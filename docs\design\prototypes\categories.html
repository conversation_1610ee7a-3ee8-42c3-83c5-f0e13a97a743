<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 栏目管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .content-section-title::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 5rem;
            height: 3px;
            background: linear-gradient(to right, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .user-link::after, .article-link::after, .version-link::after, .plugin-link::after, .comment-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 1px;
            background: linear-gradient(to right, #007bff, #00c6ff);
            transition: width 0.2s ease;
        }
        
        .user-link:hover, .article-link:hover, .version-link:hover, .plugin-link:hover, .comment-link:hover {
            color: #00c6ff;
        }
        
        .user-link:hover::after, .article-link:hover::after, .version-link:hover::after, .plugin-link:hover::after, .comment-link:hover::after {
            width: 100%;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            background: linear-gradient(145deg, #1e1e1e, #2a2a2a);
            border-left: 4px solid #007bff;
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            backdrop-filter: blur(20px);
            z-index: 1000;
            transform: translateX(120%);
            opacity: 0;
            transition: all 0.2s ease;
            max-width: 350px;
        }
        
        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        /* 树形结构样式 */
        .tree-item {
            border-left: 2px solid #444;
            margin-left: 20px;
            padding-left: 15px;
            position: relative;
        }
        .tree-item::before {
            content: '';
            position: absolute;
            left: -2px;
            top: 0;
            width: 2px;
            height: 100%;
            background: linear-gradient(180deg, #444 0%, #666 100%);
            opacity: 0;
            transition: opacity 0.3s;
        }
        .tree-item:hover::before {
            opacity: 1;
        }

        /* 拖拽提示样式 */
        .drag-over {
            border: 2px dashed #007bff;
            background-color: rgba(0,123,255,0.1);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">栏目管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <a href="category_edit.html" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-folder-plus text-white"></i>
                                </span>
                                添加栏目
                            </span>
                        </a>
                    </div>
                </div>
                <div class="mt-4 flex flex-wrap gap-4">
                    <div class="relative w-full md:w-96">
                        <input type="text" placeholder="搜索栏目..." 
                               class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent pl-10">
                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                    
                    <!-- 视图切换按钮 -->
                    <div class="flex gap-2">
                        <button class="px-4 py-2 rounded bg-blue-500 text-white text-sm flex items-center">
                            <i class="fas fa-th-list mr-2"></i>列表视图
                        </button>
                        <button class="px-4 py-2 rounded bg-gray-700 hover:bg-gray-600 text-white text-sm flex items-center">
                            <i class="fas fa-sitemap mr-2"></i>树形视图
                        </button>
                    </div>
                </div>
            </div>

            <!-- 栏目列表表格 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">栏目名称</th>
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">别名</th>
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">文章数量</th>
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">排序</th>
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">状态</th>
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 栏目项 1 (一级栏目) -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-3 px-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-folder text-yellow-400 mr-3"></i>
                                        <span>新闻资讯</span>
                                    </div>
                                </td>
                                <td class="py-3 px-4 text-gray-300">news</td>
                                <td class="py-3 px-4 text-gray-300">45</td>
                                <td class="py-3 px-4 text-gray-300">1</td>
                                <td class="py-3 px-4">
                                    <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs font-medium">
                                        已发布
                                    </span>
                                </td>
                                <td class="py-3 px-4">
                                    <div class="flex space-x-3">
                                        <a href="category_edit.html" class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="text-purple-400 hover:text-purple-300">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="text-red-400 hover:text-red-300">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>

                            <!-- 栏目项 2 (二级栏目) -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-3 px-4">
                                    <div class="flex items-center pl-6">
                                        <i class="fas fa-folder text-yellow-400 mr-3"></i>
                                        <span>公司动态</span>
                                    </div>
                                </td>
                                <td class="py-3 px-4 text-gray-300">company-news</td>
                                <td class="py-3 px-4 text-gray-300">28</td>
                                <td class="py-3 px-4 text-gray-300">1</td>
                                <td class="py-3 px-4">
                                    <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs font-medium">
                                        已发布
                                    </span>
                                </td>
                                <td class="py-3 px-4">
                                    <div class="flex space-x-3">
                                        <a href="category_edit.html" class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="text-purple-400 hover:text-purple-300">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="text-red-400 hover:text-red-300">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>

                            <!-- 栏目项 3 (二级栏目) -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-3 px-4">
                                    <div class="flex items-center pl-6">
                                        <i class="fas fa-folder text-yellow-400 mr-3"></i>
                                        <span>行业资讯</span>
                                    </div>
                                </td>
                                <td class="py-3 px-4 text-gray-300">industry-news</td>
                                <td class="py-3 px-4 text-gray-300">17</td>
                                <td class="py-3 px-4 text-gray-300">2</td>
                                <td class="py-3 px-4">
                                    <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs font-medium">
                                        已发布
                                    </span>
                                </td>
                                <td class="py-3 px-4">
                                    <div class="flex space-x-3">
                                        <a href="category_edit.html" class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="text-purple-400 hover:text-purple-300">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="text-red-400 hover:text-red-300">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>

                            <!-- 栏目项 4 (一级栏目) -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-3 px-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-folder text-yellow-400 mr-3"></i>
                                        <span>产品中心</span>
                                    </div>
                                </td>
                                <td class="py-3 px-4 text-gray-300">products</td>
                                <td class="py-3 px-4 text-gray-300">36</td>
                                <td class="py-3 px-4 text-gray-300">2</td>
                                <td class="py-3 px-4">
                                    <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs font-medium">
                                        已发布
                                    </span>
                                </td>
                                <td class="py-3 px-4">
                                    <div class="flex space-x-3">
                                        <a href="category_edit.html" class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="text-purple-400 hover:text-purple-300">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="text-red-400 hover:text-red-300">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>

                            <!-- 栏目项 5 (二级栏目) -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-3 px-4">
                                    <div class="flex items-center pl-6">
                                        <i class="fas fa-folder text-yellow-400 mr-3"></i>
                                        <span>软件产品</span>
                                    </div>
                                </td>
                                <td class="py-3 px-4 text-gray-300">software</td>
                                <td class="py-3 px-4 text-gray-300">18</td>
                                <td class="py-3 px-4 text-gray-300">1</td>
                                <td class="py-3 px-4">
                                    <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs font-medium">
                                        已发布
                                    </span>
                                </td>
                                <td class="py-3 px-4">
                                    <div class="flex space-x-3">
                                        <a href="category_edit.html" class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="text-purple-400 hover:text-purple-300">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="text-red-400 hover:text-red-300">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>

                            <!-- 栏目项 6 (二级栏目) -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-3 px-4">
                                    <div class="flex items-center pl-6">
                                        <i class="fas fa-folder text-yellow-400 mr-3"></i>
                                        <span>硬件产品</span>
                                    </div>
                                </td>
                                <td class="py-3 px-4 text-gray-300">hardware</td>
                                <td class="py-3 px-4 text-gray-300">14</td>
                                <td class="py-3 px-4 text-gray-300">2</td>
                                <td class="py-3 px-4">
                                    <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs font-medium">
                                        已发布
                                    </span>
                                </td>
                                <td class="py-3 px-4">
                                    <div class="flex space-x-3">
                                        <a href="category_edit.html" class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="text-purple-400 hover:text-purple-300">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="text-red-400 hover:text-red-300">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>

                            <!-- 栏目项 7 (三级栏目) -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-3 px-4">
                                    <div class="flex items-center pl-12">
                                        <i class="fas fa-folder text-yellow-400 mr-3"></i>
                                        <span>网络设备</span>
                                    </div>
                                </td>
                                <td class="py-3 px-4 text-gray-300">network</td>
                                <td class="py-3 px-4 text-gray-300">8</td>
                                <td class="py-3 px-4 text-gray-300">1</td>
                                <td class="py-3 px-4">
                                    <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs font-medium">
                                        已发布
                                    </span>
                                </td>
                                <td class="py-3 px-4">
                                    <div class="flex space-x-3">
                                        <a href="category_edit.html" class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="text-purple-400 hover:text-purple-300">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="text-red-400 hover:text-red-300">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>

                            <!-- 栏目项 8 (三级栏目) -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-3 px-4">
                                    <div class="flex items-center pl-12">
                                        <i class="fas fa-folder text-yellow-400 mr-3"></i>
                                        <span>存储设备</span>
                                    </div>
                                </td>
                                <td class="py-3 px-4 text-gray-300">storage</td>
                                <td class="py-3 px-4 text-gray-300">6</td>
                                <td class="py-3 px-4 text-gray-300">2</td>
                                <td class="py-3 px-4">
                                    <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs font-medium">
                                        已发布
                                    </span>
                                </td>
                                <td class="py-3 px-4">
                                    <div class="flex space-x-3">
                                        <a href="category_edit.html" class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="text-purple-400 hover:text-purple-300">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="text-red-400 hover:text-red-300">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>

                            <!-- 栏目项 9 (一级栏目) -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-3 px-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-folder text-yellow-400 mr-3"></i>
                                        <span>解决方案</span>
                                    </div>
                                </td>
                                <td class="py-3 px-4 text-gray-300">solutions</td>
                                <td class="py-3 px-4 text-gray-300">12</td>
                                <td class="py-3 px-4 text-gray-300">3</td>
                                <td class="py-3 px-4">
                                    <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs font-medium">
                                        已发布
                                    </span>
                                </td>
                                <td class="py-3 px-4">
                                    <div class="flex space-x-3">
                                        <a href="category_edit.html" class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="text-purple-400 hover:text-purple-300">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="text-red-400 hover:text-red-300">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>

                            <!-- 栏目项 10 (一级栏目) -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-3 px-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-folder text-yellow-400 mr-3"></i>
                                        <span>关于我们</span>
                                    </div>
                                </td>
                                <td class="py-3 px-4 text-gray-300">about</td>
                                <td class="py-3 px-4 text-gray-300">5</td>
                                <td class="py-3 px-4 text-gray-300">4</td>
                                <td class="py-3 px-4">
                                    <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs font-medium">
                                        已发布
                                    </span>
                                </td>
                                <td class="py-3 px-4">
                                    <div class="flex space-x-3">
                                        <a href="category_edit.html" class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="text-purple-400 hover:text-purple-300">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="text-red-400 hover:text-red-300">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页控件 -->
                <div class="flex items-center justify-between mt-6">
                    <div class="text-sm text-gray-400">
                        显示 <span class="font-medium">1-10</span> 共 <span class="font-medium">24</span> 条
                    </div>
                    <div class="flex">
                        <a href="#" class="px-3 py-1 rounded border border-gray-600 text-gray-400 mr-2 hover:bg-gray-700">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <a href="#" class="px-3 py-1 rounded border border-gray-600 text-white bg-blue-500 mr-2">1</a>
                        <a href="#" class="px-3 py-1 rounded border border-gray-600 text-gray-400 mr-2 hover:bg-gray-700">2</a>
                        <a href="#" class="px-3 py-1 rounded border border-gray-600 text-gray-400 mr-2 hover:bg-gray-700">3</a>
                        <a href="#" class="px-3 py-1 rounded border border-gray-600 text-gray-400 hover:bg-gray-700">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 栏目统计 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold text-white mb-4">栏目统计</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- 统计卡片 1 -->
                    <div class="bg-gray-800/20 rounded-xl p-4">
                        <div class="flex items-center justify-between">
                            <h4 class="text-gray-300">总栏目数</h4>
                            <div class="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                                <i class="fas fa-folder text-blue-400"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="text-2xl font-bold text-white">24</div>
                            <div class="text-xs text-gray-400 mt-1">较上月增长 5%</div>
                        </div>
                    </div>

                    <!-- 统计卡片 2 -->
                    <div class="bg-gray-800/20 rounded-xl p-4">
                        <div class="flex items-center justify-between">
                            <h4 class="text-gray-300">一级栏目</h4>
                            <div class="w-10 h-10 rounded-full bg-purple-500/20 flex items-center justify-center">
                                <i class="fas fa-sitemap text-purple-400"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="text-2xl font-bold text-white">6</div>
                            <div class="text-xs text-gray-400 mt-1">主要导航入口</div>
                        </div>
                    </div>

                    <!-- 统计卡片 3 -->
                    <div class="bg-gray-800/20 rounded-xl p-4">
                        <div class="flex items-center justify-between">
                            <h4 class="text-gray-300">最多文章</h4>
                            <div class="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                                <i class="fas fa-file-alt text-green-400"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="text-2xl font-bold text-white">45</div>
                            <div class="text-xs text-gray-400 mt-1">新闻资讯栏目</div>
                        </div>
                    </div>

                    <!-- 统计卡片 4 -->
                    <div class="bg-gray-800/20 rounded-xl p-4">
                        <div class="flex items-center justify-between">
                            <h4 class="text-gray-300">最多访问</h4>
                            <div class="w-10 h-10 rounded-full bg-yellow-500/20 flex items-center justify-center">
                                <i class="fas fa-eye text-yellow-400"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="text-2xl font-bold text-white">8,652</div>
                            <div class="text-xs text-gray-400 mt-1">产品中心栏目</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <!-- 通知组件 -->
    <div class="notification" id="notification">
        <div class="mr-3">
            <i class="fas fa-check-circle text-green-400 text-xl"></i>
        </div>
        <div class="flex-1">
            <h4 class="text-white text-sm font-bold">操作成功</h4>
            <p class="text-gray-300 text-xs">栏目更新成功。</p>
        </div>
    </div>

    <!-- 加载必要的JS -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
</body>
</html>