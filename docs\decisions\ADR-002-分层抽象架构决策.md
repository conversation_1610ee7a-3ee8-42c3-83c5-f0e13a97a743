# ADR-002: 分层抽象架构决策

## 状态
已接受 (2025-06-14)

## 背景
在GACMS架构设计过程中，遇到了核心架构定位问题：
1. 用户管理：与核心强相关，但又要符合微核心+模块化设计
2. 后台管理：既有系统级功能，又有业务级功能
3. 站点管理：涉及多租户基础设施，但也有业务逻辑

这些组件的定位直接影响整个系统的架构清晰度和可维护性。

## 决策

### 1. 核心架构原则

#### 1.1 判断标准
**基础设施特征**：
- 系统启动时必须可用
- 其他模块都依赖它
- 不可插拔（移除会导致系统无法运行）
- 跨租户功能
- 提供核心抽象和契约

**业务模块特征**：
- 可插拔（可以启用/禁用而不影响系统核心）
- 实现具体的业务功能
- 在租户边界内工作
- 按需懒加载
- 可以独立开发和部署

#### 1.2 分层抽象原则
- **基础设施层**：只提供抽象契约，不包含具体实现
- **业务模块层**：实现具体的业务逻辑和功能
- **界面模块层**：聚合和展示，基于权限动态呈现

### 2. 具体组件定位

#### 2.1 用户管理（混合模式）
**基础设施部分**：
```go
// 认证抽象
type Authenticator interface {
    Authenticate(ctx context.Context, credentials Credentials) (User, error)
    GetCurrentUser(ctx context.Context) (User, error)
}

// 权限抽象
type PermissionChecker interface {
    HasPermission(ctx context.Context, user User, permission string) bool
    Wrap(permission string, handler gin.HandlerFunc) gin.HandlerFunc
}

// 用户上下文抽象
type UserContext interface {
    GetCurrentUser(ctx context.Context) (User, error)
    SetCurrentUser(ctx context.Context, user User) context.Context
}
```

**业务模块部分**：
- user模块：用户CRUD、角色管理、用户业务逻辑
- auth模块：登录、注册、密码重置等认证业务流程

#### 2.2 后台管理（纯业务模块）
**定位**：纯业务模块，可插拔

**理由**：
- 可以完全移除而不影响API功能
- 主要是UI聚合，不包含核心逻辑
- 可以有多个不同的后台管理实现
- 可以按需启用/禁用

**功能**：
- 基于权限的菜单生成
- 各模块功能的聚合展示
- 后台管理界面和交互逻辑

#### 2.3 站点管理（混合模式）
**基础设施部分**：
```go
// 站点解析抽象
type SiteResolver interface {
    ResolveSite(domain string) (Site, error)
    GetCurrentSite(ctx context.Context) (Site, error)
}

// 租户上下文抽象
type TenantContext interface {
    GetCurrentTenant(ctx context.Context) (Tenant, error)
    WithTenant(ctx context.Context, tenant Tenant) context.Context
}
```

**业务模块部分**：
- site模块：站点CRUD、站点配置管理、站点业务逻辑

### 3. 架构层次结构

```
核心基础设施（不可插拔）：
├── 认证抽象层
│   ├── contract.Authenticator
│   ├── contract.UserContext
│   └── contract.SessionManager
├── 权限抽象层
│   ├── contract.PermissionChecker
│   ├── contract.RoleManager
│   └── contract.AuthorizationProvider
├── 站点抽象层
│   ├── contract.SiteResolver
│   ├── contract.TenantContext
│   └── contract.MultiTenantProvider
├── 路由抽象层
│   ├── contract.Router
│   ├── contract.Middleware
│   └── contract.RouteProvider
└── 事件抽象层
    ├── contract.EventManager
    ├── contract.EventPublisher
    └── contract.EventSubscriber

业务模块层（可插拔）：
├── user模块（用户管理业务逻辑）
├── auth模块（认证业务逻辑）
├── admin模块（后台管理界面）
├── content模块（内容管理）
├── site模块（站点管理业务逻辑）
└── 其他业务模块...
```

### 4. 依赖注入策略

#### 4.1 基础设施注入
```go
// 基础设施模块提供抽象契约
var InfrastructureModule = fx.Module("infrastructure",
    fx.Provide(
        // 提供抽象接口的默认实现或代理
        fx.Annotate(
            NewDefaultAuthenticator,
            fx.As(new(contract.Authenticator)),
        ),
        fx.Annotate(
            NewDefaultPermissionChecker,
            fx.As(new(contract.PermissionChecker)),
        ),
    ),
)
```

#### 4.2 模块实现注入
```go
// 业务模块提供具体实现
var UserModule = fx.Module("user",
    fx.Provide(
        // 用户模块提供认证的具体实现
        fx.Annotate(
            service.NewUserAuthenticator,
            fx.As(new(contract.Authenticator)),
        ),
        fx.Annotate(
            service.NewUserPermissionChecker,
            fx.As(new(contract.PermissionChecker)),
        ),
    ),
)
```

## 实施计划

### 阶段1：抽象层重构
1. 创建核心抽象契约
2. 提取认证抽象到基础设施
3. 提取权限抽象到基础设施
4. 提取站点解析抽象到基础设施

### 阶段2：模块重新定位
1. user模块：重构为认证抽象的实现
2. admin模块：重新定位为纯UI聚合模块
3. site模块：重构为站点管理业务逻辑模块

### 阶段3：依赖注入重构
1. 基础设施模块提供抽象
2. 业务模块提供实现
3. 通过fx进行依赖注入和接口替换

### 阶段4：测试和验证
1. 验证模块可插拔性
2. 验证抽象层的有效性
3. 性能和稳定性测试

## 影响

### 正面影响
- **架构清晰**：基础设施与业务模块职责明确分离
- **可插拔性**：业务模块可以独立开发、测试、部署
- **可扩展性**：可以有多种实现方式，支持不同的业务需求
- **可维护性**：抽象层稳定，具体实现可以独立演进

### 风险缓解
- **复杂度控制**：通过清晰的分层减少架构复杂度
- **向后兼容**：渐进式重构，保持现有功能不受影响
- **性能优化**：抽象层设计考虑性能，避免过度抽象

## 相关决策
- 基于ADR-001的路由架构决策
- 符合GACMS的微核心+模块化设计原则
- 支持事件驱动的模块间通信

## 备注
本决策解决了核心组件定位的架构疑惑，建立了清晰的分层抽象原则，为后续的架构演进提供了指导。
