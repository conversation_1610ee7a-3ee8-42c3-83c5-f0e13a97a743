<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 应用市场</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .dark-theme {
            background-color: #1F2937;
            color: #F9FAFB;
        }
        
        .card {
            transition: all 0.3s ease;
            border: 1px solid #374151;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
            border-color: #4B5563;
        }
        
        .btn {
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            margin-right: 5px;
        }
        
        .app-card {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .app-preview {
            height: 160px;
            overflow: hidden;
            position: relative;
            border-top-left-radius: 0.5rem;
            border-top-right-radius: 0.5rem;
        }
        
        .app-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }
        
        .card:hover .app-preview img {
            transform: scale(1.05);
        }
        
        .rating {
            display: flex;
            align-items: center;
        }
        
        .rating .star {
            color: #FCD34D;
        }
        
        .rating .star.empty {
            color: #4B5563;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-4">
                <a href="dashboard.html" class="text-gray-400 hover:text-white">首页</a>
                <span class="mx-2 text-gray-600">/</span>
                <a href="#" class="text-gray-400 hover:text-white">功能扩展</a>
                <span class="mx-2 text-gray-600">/</span>
                <span class="text-white">应用市场</span>
            </div>
            
            <!-- 页面标题和操作按钮 -->
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold text-white relative pl-3 section-title">应用市场</h2>
                <div class="flex space-x-3">
                    <button class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center btn">
                        <i class="fas fa-upload mr-2"></i> 上传应用
                    </button>
                    <button class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center btn">
                        <i class="fas fa-box-open mr-2"></i> 我的应用
                    </button>
                </div>
            </div>
            
            <!-- 统计概览 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                    <div class="flex items-center">
                        <div class="rounded-full bg-blue-500/10 p-3 mr-4">
                            <i class="fas fa-cubes text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">应用总数</h3>
                            <p class="text-2xl font-bold">128</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                    <div class="flex items-center">
                        <div class="rounded-full bg-green-500/10 p-3 mr-4">
                            <i class="fas fa-check-circle text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">已安装应用</h3>
                            <p class="text-2xl font-bold">12</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                    <div class="flex items-center">
                        <div class="rounded-full bg-yellow-500/10 p-3 mr-4">
                            <i class="fas fa-sync-alt text-yellow-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">可更新应用</h3>
                            <p class="text-2xl font-bold">3</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                    <div class="flex items-center">
                        <div class="rounded-full bg-purple-500/10 p-3 mr-4">
                            <i class="fas fa-download text-purple-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">下载总量</h3>
                            <p class="text-2xl font-bold">25.6K</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 主要内容区 -->
            <div class="flex flex-col lg:flex-row gap-6">
                <!-- 左侧筛选区 -->
                <div class="lg:w-1/4">
                    <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5 mb-6">
                        <h3 class="text-lg font-semibold mb-4">搜索</h3>
                        <div class="relative">
                            <input type="text" placeholder="搜索应用..." class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 pl-10 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                    </div>
                    
                    <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5 mb-6">
                        <h3 class="text-lg font-semibold mb-4">应用分类</h3>
                        <ul class="space-y-2">
                            <li>
                                <a href="#" class="flex items-center justify-between text-blue-400 hover:text-blue-300">
                                    <span>全部应用</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">128</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>内容增强</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">42</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>SEO 工具</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">24</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>社交媒体</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">18</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>电子商务</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">15</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>安全防护</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">12</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>性能优化</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">9</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>数据分析</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">8</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5 mb-6">
                        <h3 class="text-lg font-semibold mb-4">价格</h3>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                <span class="ml-2 text-gray-400">免费</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                <span class="ml-2 text-gray-400">付费</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                        <h3 class="text-lg font-semibold mb-4">兼容性</h3>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                <span class="ml-2 text-gray-400">GACMS v1.0+</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                <span class="ml-2 text-gray-400">GACMS v2.0+</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600">
                                <span class="ml-2 text-gray-400">GACMS v3.0+ (Beta)</span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧应用列表 -->
                <div class="lg:w-3/4">
                    <!-- 排序工具栏 -->
                    <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-4 mb-6 flex flex-wrap items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="text-gray-400">排序:</span>
                            <button class="bg-blue-600 text-white px-3 py-1 rounded-lg text-sm">最新发布</button>
                            <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-1 rounded-lg text-sm">下载最多</button>
                            <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-1 rounded-lg text-sm">评分最高</button>
                        </div>
                        <div class="flex items-center space-x-2 mt-2 sm:mt-0">
                            <span class="text-gray-400">视图:</span>
                            <button class="bg-blue-600 text-white p-1 rounded-lg">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 p-1 rounded-lg">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 应用列表 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                        <!-- 应用卡片 1 -->
                        <div class="card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="app-card">
                                <div class="app-preview">
                                    <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="SEO 优化工具">
                                    <!-- 图片来源: https://unsplash.com/photos/blue-and-white-light-illustration-vBCVcWUyvyM -->
                                </div>
                                <div class="p-5">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-white">SEO 优化工具</h3>
                                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">免费</span>
                                    </div>
                                    <div class="flex items-center mb-2">
                                        <div class="rating mr-2">
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star-half-alt star"></i>
                                        </div>
                                        <span class="text-gray-400 text-sm">4.5 (120)</span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-3">全面的 SEO 工具包，包含关键词分析、元标签优化、内容建议和搜索引擎排名跟踪。</p>
                                    <div class="flex flex-wrap mb-4">
                                        <span class="tag bg-blue-500/10 text-blue-400">SEO</span>
                                        <span class="tag bg-green-500/10 text-green-400">分析</span>
                                        <span class="tag bg-purple-500/10 text-purple-400">优化</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400 text-sm">v2.3.0 | 10.2K 下载</span>
                                        <button class="bg-gradient-to-r from-blue-500 to-blue-700 text-white px-4 py-1 rounded-lg text-sm hover:shadow-lg hover:shadow-blue-500/30 transition-all btn">
                                            安装
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 应用卡片 2 -->
                        <div class="card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="app-card">
                                <div class="app-preview">
                                    <img src="https://images.unsplash.com/photo-1563986768609-322da13575f3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="社交媒体连接器">
                                    <!-- 图片来源: https://unsplash.com/photos/blue-and-white-light-digital-wallpaper-8bghKxNU1j0 -->
                                </div>
                                <div class="p-5">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-white">社交媒体连接器</h3>
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">¥199</span>
                                    </div>
                                    <div class="flex items-center mb-2">
                                        <div class="rating mr-2">
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                        </div>
                                        <span class="text-gray-400 text-sm">5.0 (86)</span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-3">一键连接所有主流社交媒体平台，自动发布内容，统一管理评论和消息。</p>
                                    <div class="flex flex-wrap mb-4">
                                        <span class="tag bg-blue-500/10 text-blue-400">社交</span>
                                        <span class="tag bg-red-500/10 text-red-400">营销</span>
                                        <span class="tag bg-yellow-500/10 text-yellow-400">自动化</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400 text-sm">v3.1.2 | 8.7K 下载</span>
                                        <button class="bg-gradient-to-r from-green-500 to-green-700 text-white px-4 py-1 rounded-lg text-sm hover:shadow-lg hover:shadow-green-500/30 transition-all btn">
                                            购买
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 应用卡片 3 -->
                        <div class="card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="app-card">
                                <div class="app-preview">
                                    <img src="https://images.unsplash.com/photo-1544197150-b99a580bb7a8?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="安全防护盾">
                                    <!-- 图片来源: https://unsplash.com/photos/a-computer-screen-with-a-bunch-of-numbers-on-it-N7RiDzfF2iw -->
                                </div>
                                <div class="p-5">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-white">安全防护盾</h3>
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">¥299</span>
                                    </div>
                                    <div class="flex items-center mb-2">
                                        <div class="rating mr-2">
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star empty"></i>
                                        </div>
                                        <span class="text-gray-400 text-sm">4.0 (52)</span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-3">全方位网站安全保护，包括防火墙、恶意软件扫描、入侵检测和自动备份。</p>
                                    <div class="flex flex-wrap mb-4">
                                        <span class="tag bg-red-500/10 text-red-400">安全</span>
                                        <span class="tag bg-yellow-500/10 text-yellow-400">防护</span>
                                        <span class="tag bg-blue-500/10 text-blue-400">备份</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400 text-sm">v2.0.5 | 5.3K 下载</span>
                                        <button class="bg-gradient-to-r from-green-500 to-green-700 text-white px-4 py-1 rounded-lg text-sm hover:shadow-lg hover:shadow-green-500/30 transition-all btn">
                                            购买
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 应用卡片 4 -->
                        <div class="card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="app-card">
                                <div class="app-preview">
                                    <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="内容编辑器增强">
                                    <!-- 图片来源: https://unsplash.com/photos/blue-and-white-light-illustration-vBCVcWUyvyM -->
                                </div>
                                <div class="p-5">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-white">内容编辑器增强</h3>
                                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">免费</span>
                                    </div>
                                    <div class="flex items-center mb-2">
                                        <div class="rating mr-2">
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                        </div>
                                        <span class="text-gray-400 text-sm">4.9 (215)</span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-3">为内容编辑器添加高级功能，包括表格、代码高亮、高级排版和多媒体嵌入。</p>
                                    <div class="flex flex-wrap mb-4">
                                        <span class="tag bg-purple-500/10 text-purple-400">编辑器</span>
                                        <span class="tag bg-blue-500/10 text-blue-400">内容</span>
                                        <span class="tag bg-green-500/10 text-green-400">排版</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400 text-sm">v1.8.3 | 15.6K 下载</span>
                                        <button class="bg-gradient-to-r from-blue-500 to-blue-700 text-white px-4 py-1 rounded-lg text-sm hover:shadow-lg hover:shadow-blue-500/30 transition-all btn">
                                            安装
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 应用卡片 5 -->
                        <div class="card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="app-card">
                                <div class="app-preview">
                                    <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="数据分析大师">
                                    <!-- 图片来源: https://unsplash.com/photos/business-chart-on-laptop-screen-15Vb4B_ma_s -->
                                </div>
                                <div class="p-5">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-white">数据分析大师</h3>
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">¥399</span>
                                    </div>
                                    <div class="flex items-center mb-2">
                                        <div class="rating mr-2">
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star-half-alt star"></i>
                                        </div>
                                        <span class="text-gray-400 text-sm">4.7 (68)</span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-3">高级数据分析工具，提供详细的用户行为分析、转化漏斗和自定义报表。</p>
                                    <div class="flex flex-wrap mb-4">
                                        <span class="tag bg-blue-500/10 text-blue-400">分析</span>
                                        <span class="tag bg-yellow-500/10 text-yellow-400">报表</span>
                                        <span class="tag bg-purple-500/10 text-purple-400">数据</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400 text-sm">v2.5.1 | 4.8K 下载</span>
                                        <button class="bg-gradient-to-r from-green-500 to-green-700 text-white px-4 py-1 rounded-lg text-sm hover:shadow-lg hover:shadow-green-500/30 transition-all btn">
                                            购买
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 应用卡片 6 -->
                        <div class="card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="app-card">
                                <div class="app-preview">
                                    <img src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="缓存加速器">
                                    <!-- 图片来源: https://unsplash.com/photos/person-using-macbook-pro-on-person-s-lap-gcsNOsPEXfs -->
                                </div>
                                <div class="p-5">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-white">缓存加速器</h3>
                                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">免费</span>
                                    </div>
                                    <div class="flex items-center mb-2">
                                        <div class="rating mr-2">
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star empty"></i>
                                        </div>
                                        <span class="text-gray-400 text-sm">4.2 (93)</span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-3">全面的缓存解决方案，包括页面缓存、对象缓存和数据库查询缓存，显著提升网站速度。</p>
                                    <div class="flex flex-wrap mb-4">
                                        <span class="tag bg-green-500/10 text-green-400">性能</span>
                                        <span class="tag bg-blue-500/10 text-blue-400">缓存</span>
                                        <span class="tag bg-red-500/10 text-red-400">优化</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400 text-sm">v1.4.2 | 7.9K 下载</span>
                                        <button class="bg-gradient-to-r from-blue-500 to-blue-700 text-white px-4 py-1 rounded-lg text-sm hover:shadow-lg hover:shadow-blue-500/30 transition-all btn">
                                            安装
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 分页控制 -->
                    <div class="flex justify-between items-center">
                        <div class="text-sm text-gray-400">
                            显示 1-6 / 共 128 个应用
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="px-3 py-1 bg-blue-600 text-white rounded-md">1</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">2</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">3</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">4</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 卡片悬停效果
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.classList.add('transform', 'scale-[1.02]');
                });
                card.addEventListener('mouseleave', function() {
                    this.classList.remove('transform', 'scale-[1.02]');
                });
            });
            
            // 按钮悬停效果
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.classList.add('transform', 'scale-[1.05]');
                });
                button.addEventListener('mouseleave', function() {
                    this.classList.remove('transform', 'scale-[1.05]');
                });
            });
        });
    </script>
</body>
</html>