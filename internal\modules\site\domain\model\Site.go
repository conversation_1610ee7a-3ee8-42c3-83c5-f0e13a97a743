/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/site/domain/model/Site.go
 * @Description: Defines the data model for a site.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package model

import (
	"gorm.io/gorm"
)

// Site represents a website instance managed by the CMS.
type Site struct {
	gorm.Model
	Name           string `gorm:"type:varchar(100);not null"`
	Domain         string `gorm:"type:varchar(255);not null;unique"`
	IsActive       bool   `gorm:"default:true"`
	FrontendTheme  string `gorm:"type:varchar(100)"`
	BackendTheme   string `gorm:"type:varchar(100)"`
	DefaultLang    string `gorm:"type:varchar(20)"`
	AvailableLangs string `gorm:"type:varchar(255)"` // Comma-separated list of available languages
	LogoURL        string `gorm:"type:varchar(512)"`
	FaviconURL     string `gorm:"type:varchar(512)"`
	Description    string `gorm:"type:text"`
} 