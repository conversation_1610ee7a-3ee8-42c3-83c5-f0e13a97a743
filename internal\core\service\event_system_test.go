/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/event_system_test.go
 * @Description: 事件系统集成测试
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"testing"
	"time"

	"gacms/internal/core/bus"
	"gacms/pkg/contract"
	"go.uber.org/zap"
)

// TestEvent 测试事件
type TestEvent struct {
	*bus.BaseEvent
	TestData string
}

// NewTestEvent 创建测试事件
func NewTestEvent(ctx context.Context, data string) contract.Event {
	baseEvent := bus.NewBaseEvent(ctx, "test.event", data)
	return &TestEvent{
		BaseEvent: baseEvent.(*bus.BaseEvent),
		TestData:  data,
	}
}

// TestEventHandler 测试事件处理器
type TestEventHandler struct {
	name           string
	handledEvents  []contract.Event
	supportedEvents []contract.EventName
}

// NewTestEventHandler 创建测试事件处理器
func NewTestEventHandler(name string, events ...contract.EventName) *TestEventHandler {
	return &TestEventHandler{
		name:            name,
		handledEvents:   make([]contract.Event, 0),
		supportedEvents: events,
	}
}

// Handle 处理事件
func (h *TestEventHandler) Handle(event contract.Event) error {
	h.handledEvents = append(h.handledEvents, event)
	return nil
}

// SupportedEvents 返回支持的事件类型
func (h *TestEventHandler) SupportedEvents() []contract.EventName {
	return h.supportedEvents
}

// HandlerName 返回处理器名称
func (h *TestEventHandler) HandlerName() string {
	return h.name
}

// GetHandledEvents 获取已处理的事件
func (h *TestEventHandler) GetHandledEvents() []contract.Event {
	return h.handledEvents
}

// TestEventBus 测试事件总线
func TestEventBus(t *testing.T) {
	logger := zap.NewNop()
	
	// 创建事件总线
	eventBus := NewDefaultEventBus(DefaultEventBusParams{
		Logger: logger,
	})

	// 创建测试处理器
	handler1 := NewTestEventHandler("handler1", "test.event")
	handler2 := NewTestEventHandler("handler2", "test.event", "other.event")

	// 注册处理器
	err := eventBus.Register(handler1)
	if err != nil {
		t.Fatalf("Failed to register handler1: %v", err)
	}

	err = eventBus.Register(handler2)
	if err != nil {
		t.Fatalf("Failed to register handler2: %v", err)
	}

	// 检查处理器是否注册成功
	if !eventBus.HasHandlers("test.event") {
		t.Error("Expected handlers for test.event")
	}

	handlers := eventBus.GetHandlers("test.event")
	if len(handlers) != 2 {
		t.Errorf("Expected 2 handlers, got %d", len(handlers))
	}

	// 创建并发布事件
	ctx := context.Background()
	event := NewTestEvent(ctx, "test data")

	err = eventBus.Publish(event)
	if err != nil {
		t.Fatalf("Failed to publish event: %v", err)
	}

	// 验证处理器是否收到事件
	if len(handler1.GetHandledEvents()) != 1 {
		t.Errorf("Handler1 expected 1 event, got %d", len(handler1.GetHandledEvents()))
	}

	if len(handler2.GetHandledEvents()) != 1 {
		t.Errorf("Handler2 expected 1 event, got %d", len(handler2.GetHandledEvents()))
	}

	// 测试取消注册
	err = eventBus.Unregister(handler1)
	if err != nil {
		t.Fatalf("Failed to unregister handler1: %v", err)
	}

	handlers = eventBus.GetHandlers("test.event")
	if len(handlers) != 1 {
		t.Errorf("Expected 1 handler after unregister, got %d", len(handlers))
	}
}

// TestEventManager 测试事件管理器
func TestEventManager(t *testing.T) {
	logger := zap.NewNop()

	// 创建依赖组件
	eventBus := NewDefaultEventBus(DefaultEventBusParams{Logger: logger})
	handlerRegistry := NewDefaultEventHandlerRegistry(DefaultEventHandlerRegistryParams{Logger: logger})

	// 创建事件管理器
	eventManager := NewDefaultEventManager(DefaultEventManagerParams{
		EventBus:        eventBus,
		HandlerRegistry: handlerRegistry,
		Logger:          logger,
	})

	// 创建测试处理器
	handler := NewTestEventHandler("test-handler", "test.event")

	// 注册处理器
	err := eventManager.RegisterHandler(handler)
	if err != nil {
		t.Fatalf("Failed to register handler: %v", err)
	}

	// 创建事件
	ctx := context.Background()
	event := eventManager.CreateEvent(ctx, "test.event", "test payload")

	// 发布事件
	err = eventManager.PublishEvent(event)
	if err != nil {
		t.Fatalf("Failed to publish event: %v", err)
	}

	// 验证处理器收到事件
	if len(handler.GetHandledEvents()) != 1 {
		t.Errorf("Expected 1 handled event, got %d", len(handler.GetHandledEvents()))
	}

	// 测试异步发布
	errChan := eventManager.PublishEventAsync(ctx, event)
	select {
	case err := <-errChan:
		if err != nil {
			t.Fatalf("Async publish failed: %v", err)
		}
	case <-time.After(time.Second):
		t.Error("Async publish timeout")
	}

	// 验证处理器收到第二个事件
	if len(handler.GetHandledEvents()) != 2 {
		t.Errorf("Expected 2 handled events, got %d", len(handler.GetHandledEvents()))
	}
}

// TestEventSerializer 测试事件序列化器
func TestEventSerializer(t *testing.T) {
	logger := zap.NewNop()
	
	serializer := NewDefaultEventSerializer(DefaultEventSerializerParams{
		Logger: logger,
	})

	// 创建测试事件
	ctx := context.Background()
	originalEvent := NewTestEvent(ctx, "test data")

	// 序列化事件
	data, err := serializer.Serialize(originalEvent)
	if err != nil {
		t.Fatalf("Failed to serialize event: %v", err)
	}

	if len(data) == 0 {
		t.Error("Serialized data is empty")
	}

	// 反序列化事件
	deserializedEvent, err := serializer.Deserialize(data, "test.event")
	if err != nil {
		t.Fatalf("Failed to deserialize event: %v", err)
	}

	// 验证反序列化的事件
	if deserializedEvent.ID() != originalEvent.ID() {
		t.Errorf("Event ID mismatch: expected %s, got %s", originalEvent.ID(), deserializedEvent.ID())
	}

	if deserializedEvent.Name() != originalEvent.Name() {
		t.Errorf("Event name mismatch: expected %s, got %s", originalEvent.Name(), deserializedEvent.Name())
	}
}

// TestEventStore 测试事件存储器
func TestEventStore(t *testing.T) {
	logger := zap.NewNop()
	
	serializer := NewDefaultEventSerializer(DefaultEventSerializerParams{Logger: logger})
	eventStore := NewDefaultEventStore(DefaultEventStoreParams{
		Serializer: serializer,
		Logger:     logger,
	})

	// 创建测试事件
	ctx := context.Background()
	event := NewTestEvent(ctx, "test data")

	// 保存事件
	eventID, err := eventStore.SaveEvent(ctx, event)
	if err != nil {
		t.Fatalf("Failed to save event: %v", err)
	}

	if eventID != event.ID() {
		t.Errorf("Event ID mismatch: expected %s, got %s", event.ID(), eventID)
	}

	// 获取事件
	retrievedEvent, err := eventStore.GetEventByID(ctx, eventID)
	if err != nil {
		t.Fatalf("Failed to get event: %v", err)
	}

	if retrievedEvent.ID() != event.ID() {
		t.Errorf("Retrieved event ID mismatch: expected %s, got %s", event.ID(), retrievedEvent.ID())
	}

	// 测试按类型获取事件
	events, err := eventStore.GetEventsByType(ctx, "test.event", time.Time{}, time.Time{})
	if err != nil {
		t.Fatalf("Failed to get events by type: %v", err)
	}

	if len(events) != 1 {
		t.Errorf("Expected 1 event, got %d", len(events))
	}

	// 测试计数
	count, err := eventStore.CountEvents(ctx, "test.event", time.Time{}, time.Time{})
	if err != nil {
		t.Fatalf("Failed to count events: %v", err)
	}

	if count != 1 {
		t.Errorf("Expected count 1, got %d", count)
	}
}
