<!--
Author: <PERSON><PERSON>eh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
-->
# ADR-007: 模块分层配置策略 (Module Layered Configuration Strategy)

## 状态
已接受

## 上下文
在GACMS系统的发展过程中，我们需要一个清晰的配置策略来管理不同类型的模块和配置。系统包含多种类型的模块：微核心基础设施、官方核心模块、官方高级功能模块、商业版模块和第三方扩展。每种类型的模块有不同的配置需求和管理方式。

### 当前面临的问题
1. **配置方式不统一**: 不同模块使用不同的配置方式，缺乏统一的配置策略
2. **依赖/权限/配置显式声明**: 需要符合第12条核心准则的显式声明原则
3. **商业化需求**: 需要支持个人版、专业版、商业版的功能分级
4. **第三方生态**: 需要支持第三方开发者的模块开发和商业化
5. **懒加载与配置**: 需要在保持懒加载优势的同时实现配置管理

## 决策

### 1. 模块分层体系
我们决定采用五层模块分层体系：

#### 第1层：微核心基础设施（Go配置，永远启用）
- **模块**: database, logger, config, event, cache
- **配置方式**: Go代码配置（fx.Options）
- **特点**: 系统基础设施，永远不可停用，编译时验证
- **理由**: 确保系统稳定性和类型安全

#### 第2层：官方核心模块（Go配置，永远启用，支持懒加载）
- **模块**: user, site, actionlog, content, admin
- **配置方式**: Go代码配置（fx.Options）
- **特点**: 
  - 内容管理平台的核心功能
  - 路由和事件固定，不需要配置管理
  - 永远启用，不可停用
  - 保留懒加载逻辑优化性能
- **理由**: 这些是内容管理平台最核心的功能，必须保证稳定性

#### 第3层：官方高级功能模块（Go配置+数据库配置）
- **模块**: menu, theme, seo, analytics, workflow
- **配置方式**: 
  - Go配置：核心服务注册和依赖注入
  - 数据库配置：启用/停用状态、业务配置
- **特点**: 
  - 可选的高级功能
  - 后台可配置启用/停用
  - 支持懒加载
- **理由**: 平衡稳定性和灵活性

#### 第4层：商业版/专业版模块（Go配置+数据库配置+授权验证）
- **模块**: advanced_seo, multi_language, advanced_workflow, api_management
- **配置方式**: 
  - Go配置：核心服务注册
  - 数据库配置：启用状态 + 授权状态
  - 授权验证：license检查
- **特点**: 
  - 商业功能，需要授权
  - 支持试用、正式授权等状态
  - 支持懒加载
- **理由**: 支持商业化模式

#### 第5层：第三方扩展（JSON配置）
- **模块**: 第三方插件、主题、扩展
- **配置方式**: 完全JSON配置（module.json）
- **特点**: 
  - 第三方开发友好
  - 动态安装/卸载
  - 不需要重新编译系统
- **理由**: 降低第三方开发门槛，支持生态发展

### 2. 配置类型策略

#### A. Go配置（编译时验证，类型安全）
- **适用**: 微核心基础设施、官方核心模块、官方高级功能模块的核心服务
- **优势**: 类型安全、编译时检查、性能优化、fx依赖注入支持
- **使用场景**: 需要编译验证的核心功能

#### B. 数据库配置（运行时可调整，支持热重载）
- **适用**: 后台可设置的配置
- **内容**: 模块启用/停用状态、权限配置、业务配置、事件监听优先级
- **优势**: 后台管理界面、热重载、多租户支持
- **使用场景**: 需要运行时调整的配置

#### C. YAML配置（环境相关）
- **适用**: 环境相关配置
- **内容**: 数据库连接、日志级别、缓存配置、第三方服务配置
- **优势**: 环境隔离、版本控制友好
- **使用场景**: 不同环境的配置差异

#### D. JSON配置（第三方扩展）
- **适用**: 第三方插件和主题
- **内容**: 完整的模块配置、事件声明、路由声明、权限声明
- **优势**: 第三方开发友好、动态安装
- **使用场景**: 第三方开发的扩展

### 3. 版本体系设计

#### 个人版（Personal）
- **包含**: 微核心 + 官方核心模块
- **特点**: 基础功能，免费使用
- **限制**: 最大1个站点，最大100个用户
- **目标**: 个人博客、小型网站

#### 专业版（Professional）
- **包含**: 个人版 + 专业功能模块
- **特点**: 高级功能，需要授权
- **限制**: 最大10个站点，最大1000个用户，支持第三方模块
- **目标**: 中小企业、专业网站

#### 商业版（Commercial）
- **包含**: 专业版 + 商业功能模块
- **特点**: 企业级功能，最高级授权
- **限制**: 无限站点，无限用户，支持所有功能
- **目标**: 大型企业、复杂应用

## 实施策略

### 1. 保持懒加载机制
- 所有模块（包括核心模块）保留懒加载逻辑
- 懒加载前进行授权验证
- 未授权模块返回403错误

### 2. 事件注册优化
- 核心模块：固定注册，不依赖配置
- 可配置模块：基于数据库配置的条件注册
- 第三方模块：基于JSON配置的动态注册

### 3. 渐进式实施
- 第一阶段：完善核心模块的Go配置
- 第二阶段：实现数据库配置管理
- 第三阶段：实现授权体系（可选，后期实施）
- 第四阶段：完善第三方模块支持

## 后果

### 正面影响
1. **系统稳定性**: 核心功能使用Go配置，确保稳定性
2. **灵活性**: 高级功能支持运行时配置
3. **商业化支持**: 支持多版本商业模式
4. **第三方生态**: 降低第三方开发门槛
5. **性能优化**: 保持懒加载优势

### 负面影响
1. **复杂性增加**: 多种配置方式增加系统复杂性
2. **学习成本**: 开发者需要理解不同的配置策略
3. **维护成本**: 需要维护多种配置机制

### 风险缓解
1. **文档完善**: 提供详细的配置策略文档
2. **工具支持**: 提供配置管理工具
3. **渐进实施**: 分阶段实施，降低风险

## 相关决策
- ADR-006: 模块运行时懒加载策略
- ADR-001: 微核心架构设计
- 未来ADR: 授权体系设计（如果需要实施）
