<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 组件编辑</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .code-editor {
            font-family: 'Cascadia Code', 'Fira Code', Consolas, 'Courier New', monospace;
            background-color: #1a1a1a;
            color: #e0e0e0;
            font-size: 14px;
            line-height: 1.5;
            tab-size: 4;
            height: 400px;
            overflow: auto;
        }
        
        .preview-container {
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 8px;
            background-color: rgba(31, 41, 55, 0.3);
            overflow: hidden;
        }
        
        .form-section {
            border-bottom: 1px solid rgba(75, 85, 99, 0.3);
            padding-bottom: 16px;
            margin-bottom: 16px;
        }
        
        .form-section:last-child {
            border-bottom: none;
            padding-bottom: 0;
            margin-bottom: 0;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="content.html" class="hover:text-white">内容管理</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="components.html" class="hover:text-white">组件管理</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">编辑组件</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">编辑组件: 特色服务卡片</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="previewBtn" class="flex items-center justify-center bg-gradient-to-r from-purple-500 to-purple-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-purple-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-eye text-white"></i>
                                </span>
                                预览
                            </span>
                        </button>
                        <button id="saveBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-save text-white"></i>
                                </span>
                                保存
                            </span>
                        </button>
                        <a href="components.html" class="flex items-center justify-center bg-gradient-to-r from-gray-600 to-gray-700 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-gray-600/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-times text-white"></i>
                                </span>
                                取消
                            </span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                <!-- 左侧：组件信息和配置 -->
                <div class="lg:col-span-2">
                    <!-- 基本信息 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">基本信息</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-gray-300 mb-2">组件名称 <span class="text-red-500">*</span></label>
                                <input type="text" value="特色服务卡片" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">组件标识符 <span class="text-red-500">*</span></label>
                                <input type="text" value="feature-service-card" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <p class="text-gray-400 text-sm mt-1">只能包含小写字母、数字和连字符</p>
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">组件类型</label>
                                <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="content">内容组件</option>
                                    <option value="layout" selected>布局组件</option>
                                    <option value="interactive">交互组件</option>
                                    <option value="form">表单组件</option>
                                    <option value="media">媒体组件</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">组件版本</label>
                                <input type="text" value="1.2.0" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <label class="block text-gray-300 mb-2">组件描述</label>
                            <textarea rows="3" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">一个展示服务特点的卡片组件，包含图标、标题和描述文本。可用于首页、服务页面等场景，支持多种颜色主题和布局方式。</textarea>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                            <div>
                                <label class="block text-gray-300 mb-2">作者</label>
                                <input type="text" value="设计团队" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">标签</label>
                                <input type="text" value="卡片, 服务, 特色, 首页" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <p class="text-gray-400 text-sm mt-1">多个标签用逗号分隔</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 组件参数配置 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">参数配置</h3>
                        
                        <!-- 标题配置 -->
                        <div class="form-section">
                            <h4 class="font-medium text-white mb-3">标题设置</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-gray-300 mb-2">标题文本</label>
                                    <input type="text" value="专业数据分析" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                
                                <div>
                                    <label class="block text-gray-300 mb-2">标题颜色</label>
                                    <div class="flex">
                                        <span class="inline-flex items-center px-3 bg-gray-800 border border-r-0 border-gray-600 rounded-l-lg text-gray-400">
                                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                                        </span>
                                        <input type="text" value="#3B82F6" class="flex-1 bg-gray-700 border border-gray-600 rounded-r-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-gray-300 mb-2">字体大小</label>
                                    <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="sm">小 (14px)</option>
                                        <option value="base">中 (16px)</option>
                                        <option value="lg" selected>大 (18px)</option>
                                        <option value="xl">特大 (20px)</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-gray-300 mb-2">字体粗细</label>
                                    <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="normal">常规</option>
                                        <option value="medium">中等</option>
                                        <option value="bold" selected>粗体</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 图标配置 -->
                        <div class="form-section">
                            <h4 class="font-medium text-white mb-3">图标设置</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-gray-300 mb-2">图标</label>
                                    <div class="flex">
                                        <span class="inline-flex items-center px-3 bg-gray-800 border border-r-0 border-gray-600 rounded-l-lg text-gray-400">
                                            <i class="fas fa-chart-bar"></i>
                                        </span>
                                        <input type="text" value="fas fa-chart-bar" class="flex-1 bg-gray-700 border border-gray-600 rounded-r-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                    <p class="text-gray-400 text-sm mt-1">输入FontAwesome图标类名</p>
                                </div>
                                
                                <div>
                                    <label class="block text-gray-300 mb-2">图标颜色</label>
                                    <div class="flex">
                                        <span class="inline-flex items-center px-3 bg-gray-800 border border-r-0 border-gray-600 rounded-l-lg text-gray-400">
                                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                                        </span>
                                        <input type="text" value="#3B82F6" class="flex-1 bg-gray-700 border border-gray-600 rounded-r-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-gray-300 mb-2">图标大小</label>
                                    <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="sm">小 (16px)</option>
                                        <option value="md">中 (24px)</option>
                                        <option value="lg" selected>大 (32px)</option>
                                        <option value="xl">特大 (48px)</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-gray-300 mb-2">图标背景</label>
                                    <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="none">无</option>
                                        <option value="circle" selected>圆形</option>
                                        <option value="rounded-square">圆角方形</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 内容配置 -->
                        <div class="form-section">
                            <h4 class="font-medium text-white mb-3">内容设置</h4>
                            <div>
                                <label class="block text-gray-300 mb-2">描述文本</label>
                                <textarea rows="3" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">通过先进的数据分析工具，帮助企业从海量数据中挖掘有价值的信息，为决策提供可靠依据。</textarea>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                <div>
                                    <label class="block text-gray-300 mb-2">文本颜色</label>
                                    <div class="flex">
                                        <span class="inline-flex items-center px-3 bg-gray-800 border border-r-0 border-gray-600 rounded-l-lg text-gray-400">
                                            <div class="w-4 h-4 rounded-full bg-gray-300"></div>
                                        </span>
                                        <input type="text" value="#D1D5DB" class="flex-1 bg-gray-700 border border-gray-600 rounded-r-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-gray-300 mb-2">对齐方式</label>
                                    <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="left" selected>左对齐</option>
                                        <option value="center">居中</option>
                                        <option value="right">右对齐</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 卡片样式 -->
                        <div>
                            <h4 class="font-medium text-white mb-3">卡片样式</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-gray-300 mb-2">背景颜色</label>
                                    <div class="flex">
                                        <span class="inline-flex items-center px-3 bg-gray-800 border border-r-0 border-gray-600 rounded-l-lg text-gray-400">
                                            <div class="w-4 h-4 rounded-full bg-gray-800 border border-gray-700"></div>
                                        </span>
                                        <input type="text" value="#1F2937" class="flex-1 bg-gray-700 border border-gray-600 rounded-r-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-gray-300 mb-2">边框样式</label>
                                    <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="none">无边框</option>
                                        <option value="border" selected>普通边框</option>
                                        <option value="border-2">粗边框</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-gray-300 mb-2">边框颜色</label>
                                    <div class="flex">
                                        <span class="inline-flex items-center px-3 bg-gray-800 border border-r-0 border-gray-600 rounded-l-lg text-gray-400">
                                            <div class="w-4 h-4 rounded-full bg-gray-600"></div>
                                        </span>
                                        <input type="text" value="#4B5563" class="flex-1 bg-gray-700 border border-gray-600 rounded-r-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-gray-300 mb-2">圆角大小</label>
                                    <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="none">无圆角</option>
                                        <option value="sm">小圆角</option>
                                        <option value="md">中等圆角</option>
                                        <option value="lg" selected>大圆角</option>
                                        <option value="xl">特大圆角</option>
                                    </select>
                                </div>
                                
                                <div class="md:col-span-2">
                                    <label class="block text-gray-300 mb-2">悬停效果</label>
                                    <div class="space-y-2">
                                        <label class="flex items-center">
                                            <input type="checkbox" checked class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-900 w-5 h-5">
                                            <span class="ml-2 text-gray-300">缩放</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" checked class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-900 w-5 h-5">
                                            <span class="ml-2 text-gray-300">阴影</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-900 w-5 h-5">
                                            <span class="ml-2 text-gray-300">边框高亮</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：组件预览和代码 -->
                <div class="lg:col-span-1">
                    <!-- 组件预览 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white flex justify-between items-center">
                            <span>组件预览</span>
                            <select class="text-sm bg-gray-700 border border-gray-600 rounded-lg px-2 py-1 text-white">
                                <option value="light">浅色模式</option>
                                <option value="dark" selected>深色模式</option>
                            </select>
                        </h3>
                        
                        <div class="preview-container p-6">
                            <div class="border border-gray-600 rounded-lg p-6 bg-gray-800 hover:scale-105 hover:shadow-lg hover:shadow-blue-500/20 transition-all">
                                <div class="flex justify-center items-center w-16 h-16 rounded-full bg-blue-500/20 mb-4">
                                    <i class="fas fa-chart-bar text-blue-500 text-3xl"></i>
                                </div>
                                <h3 class="text-lg font-bold text-blue-500 mb-2">专业数据分析</h3>
                                <p class="text-gray-300">通过先进的数据分析工具，帮助企业从海量数据中挖掘有价值的信息，为决策提供可靠依据。</p>
                            </div>
                        </div>
                        
                        <div class="flex justify-end mt-4">
                            <button class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm">
                                <i class="fas fa-expand-alt mr-1"></i> 全屏预览
                            </button>
                        </div>
                    </div>
                    
                    <!-- 组件HTML代码 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">HTML代码</h3>
                        
                        <div class="code-editor p-4 rounded-lg mb-4">
                            <pre class="text-gray-300"><span class="text-blue-400">&lt;div</span> <span class="text-green-400">class</span>=<span class="text-yellow-400">"border border-gray-600 rounded-lg p-6 bg-gray-800 hover:scale-105 hover:shadow-lg hover:shadow-blue-500/20 transition-all"</span><span class="text-blue-400">&gt;</span>
  <span class="text-blue-400">&lt;div</span> <span class="text-green-400">class</span>=<span class="text-yellow-400">"flex justify-center items-center w-16 h-16 rounded-full bg-blue-500/20 mb-4"</span><span class="text-blue-400">&gt;</span>
    <span class="text-blue-400">&lt;i</span> <span class="text-green-400">class</span>=<span class="text-yellow-400">"fas fa-chart-bar text-blue-500 text-3xl"</span><span class="text-blue-400">&gt;&lt;/i&gt;</span>
  <span class="text-blue-400">&lt;/div&gt;</span>
  <span class="text-blue-400">&lt;h3</span> <span class="text-green-400">class</span>=<span class="text-yellow-400">"text-lg font-bold text-blue-500 mb-2"</span><span class="text-blue-400">&gt;</span>专业数据分析<span class="text-blue-400">&lt;/h3&gt;</span>
  <span class="text-blue-400">&lt;p</span> <span class="text-green-400">class</span>=<span class="text-yellow-400">"text-gray-300"</span><span class="text-blue-400">&gt;</span>通过先进的数据分析工具，帮助企业从海量数据中挖掘有价值的信息，为决策提供可靠依据。<span class="text-blue-400">&lt;/p&gt;</span>
<span class="text-blue-400">&lt;/div&gt;</span></pre>
                        </div>
                        
                        <div class="flex justify-end">
                            <button class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm mr-2">
                                <i class="fas fa-copy mr-1"></i> 复制代码
                            </button>
                            <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                                <i class="fas fa-code mr-1"></i> 编辑代码
                            </button>
                        </div>
                    </div>
                    
                    <!-- 组件状态和依赖 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">组件状态</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label class="block text-gray-300 mb-2">发布状态</label>
                                <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="published" selected>已发布</option>
                                    <option value="draft">草稿</option>
                                    <option value="pending">待审核</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">组件依赖</label>
                                <div class="bg-gray-700 border border-gray-600 rounded-lg p-3 text-white text-sm">
                                    <div class="flex justify-between items-center mb-2">
                                        <span>FontAwesome</span>
                                        <span class="text-green-400">已安装</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span>Tailwind CSS</span>
                                        <span class="text-green-400">已安装</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">最后更新</label>
                                <div class="text-white">2025-05-10 15:40</div>
                                <div class="text-gray-400 text-sm">由 设计团队 更新</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 预览按钮功能
            document.getElementById('previewBtn').addEventListener('click', function() {
                alert('在实际应用中，这里会打开组件预览窗口');
            });
            
            // 表单保存功能
            document.getElementById('saveBtn').addEventListener('click', function() {
                alert('组件保存成功！');
                // 在实际场景中，这里会有AJAX请求保存表单数据
            });
        });
    </script>
</body>
</html> 