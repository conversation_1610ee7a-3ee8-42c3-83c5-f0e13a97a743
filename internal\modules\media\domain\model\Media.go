/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON> Nieh
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/media/domain/model/Media.go
 * @Description: Defines the Media model for managing all media assets.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"gorm.io/gorm"
)

// JSONB provides a way to store JSON data in the database.
type JSONB map[string]interface{}

// Value implements the driver.Valuer interface, allowing us to save JSONB to the database.
func (j JSONB) Value() (driver.Value, error) {
	if len(j) == 0 {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan implements the sql.Scanner interface, allowing us to read JSONB from the database.
func (j *JSONB) Scan(value interface{}) error {
	if value == nil {
		*j = JSONB{}
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, j)
}

// Media represents a media asset, such as an image, video, or document.
type Media struct {
	gorm.Model
	SiteID     uint   `gorm:"not null;index;comment:The site this media asset belongs to"`
	UserID     uint   `gorm:"not null;comment:The ID of the user who uploaded the file"`
	Storage    string `gorm:"type:varchar(50);not null;comment:The storage driver used, e.g., 'local', 's3'"`
	Path       string `gorm:"type:varchar(2048);not null;comment:The relative path to the file in the storage"`
	Name       string `gorm:"type:varchar(255);not null;comment:The original filename"`
	Size       int64  `gorm:"not null;comment:File size in bytes"`
	MediaType  string `gorm:"type:varchar(50);not null;index;comment:e.g., 'image', 'video', 'audio', 'document'"`
	MimeType   string `gorm:"type:varchar(100);not null;comment:The full MIME type, e.g., 'image/jpeg'"`
	Metadata   JSONB  `gorm:"type:json;comment:Flexible field for media-specific metadata (e.g., width, height, duration)"`
	Thumbnails JSONB  `gorm:"type:json;comment:JSON object storing URLs for different thumbnail sizes"`
} 