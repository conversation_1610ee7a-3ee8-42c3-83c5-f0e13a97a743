/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/banner/interfaces/http/BannerController.go
 * @Description: Banner controller
 *
 * © 2025 GACMS. All rights reserved.
 */
package http

import (
	"gacms/internal/modules/banner/domain/contract"
	"gacms/internal/modules/banner/domain/model"
	"gacms/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

type BannerController struct {
	bannerService contract.IBannerService
}

func NewBannerController(bannerService contract.IBannerService) *BannerController {
	return &BannerController{
		bannerService: bannerService,
	}
}

func (c *BannerController) RegisterRoutes(rg *gin.RouterGroup) {
	// Banner Positions
	positionGroup := rg.Group("/banner-positions")
	{
		positionGroup.POST("", c.CreateBannerPosition)
		positionGroup.PUT("/:id", c.UpdateBannerPosition)
		positionGroup.DELETE("/:id", c.DeleteBannerPosition)
		positionGroup.GET("/:id", c.GetBannerPosition)
		positionGroup.GET("", c.GetAllBannerPositions)
	}

	// Banners
	bannerGroup := rg.Group("/banners")
	{
		bannerGroup.POST("", c.CreateBanner)
		bannerGroup.PUT("/:id", c.UpdateBanner)
		bannerGroup.DELETE("/:id", c.DeleteBanner)
		bannerGroup.GET("/:id", c.GetBanner)
		bannerGroup.GET("/position/:position_id", c.GetBannersByPositionID)
	}
}

// Banner Position Handlers
func (c *BannerController) CreateBannerPosition(ctx *gin.Context) {
	var position model.BannerPosition
	if err := ctx.ShouldBindJSON(&position); err != nil {
		response.Error(ctx, 400, err)
		return
	}

	createdPosition, err := c.bannerService.CreateBannerPosition(ctx, &position)
	if err != nil {
		response.Error(ctx, 500, err)
		return
	}
	response.Success(ctx, createdPosition)
}

func (c *BannerController) UpdateBannerPosition(ctx *gin.Context) {
	id, _ := strconv.ParseUint(ctx.Param("id"), 10, 32)
	var position model.BannerPosition
	if err := ctx.ShouldBindJSON(&position); err != nil {
		response.Error(ctx, 400, err)
		return
	}
	position.ID = uint(id)

	err := c.bannerService.UpdateBannerPosition(ctx, &position)
	if err != nil {
		response.Error(ctx, 500, err)
		return
	}
	response.Success(ctx, nil)
}

func (c *BannerController) DeleteBannerPosition(ctx *gin.Context) {
	id, _ := strconv.ParseUint(ctx.Param("id"), 10, 32)
	err := c.bannerService.DeleteBannerPosition(ctx, uint(id))
	if err != nil {
		response.Error(ctx, 500, err)
		return
	}
	response.Success(ctx, nil)
}

func (c *BannerController) GetBannerPosition(ctx *gin.Context) {
	id, _ := strconv.ParseUint(ctx.Param("id"), 10, 32)
	position, err := c.bannerService.GetBannerPosition(ctx, uint(id))
	if err != nil {
		response.Error(ctx, 500, err)
		return
	}
	response.Success(ctx, position)
}

func (c *BannerController) GetAllBannerPositions(ctx *gin.Context) {
	positions, err := c.bannerService.GetAllBannerPositions(ctx)
	if err != nil {
		response.Error(ctx, 500, err)
		return
	}
	response.Success(ctx, positions)
}

// Banner Handlers
func (c *BannerController) CreateBanner(ctx *gin.Context) {
	var banner model.Banner
	if err := ctx.ShouldBindJSON(&banner); err != nil {
		response.Error(ctx, 400, err)
		return
	}

	createdBanner, err := c.bannerService.CreateBanner(ctx, &banner)
	if err != nil {
		response.Error(ctx, 500, err)
		return
	}
	response.Success(ctx, createdBanner)
}

func (c *BannerController) UpdateBanner(ctx *gin.Context) {
	id, _ := strconv.ParseUint(ctx.Param("id"), 10, 32)
	var banner model.Banner
	if err := ctx.ShouldBindJSON(&banner); err != nil {
		response.Error(ctx, 400, err)
		return
	}
	banner.ID = uint(id)

	err := c.bannerService.UpdateBanner(ctx, &banner)
	if err != nil {
		response.Error(ctx, 500, err)
		return
	}
	response.Success(ctx, nil)
}

func (c *BannerController) DeleteBanner(ctx *gin.Context) {
	id, _ := strconv.ParseUint(ctx.Param("id"), 10, 32)
	err := c.bannerService.DeleteBanner(ctx, uint(id))
	if err != nil {
		response.Error(ctx, 500, err)
		return
	}
	response.Success(ctx, nil)
}

func (c *BannerController) GetBanner(ctx *gin.Context) {
	id, _ := strconv.ParseUint(ctx.Param("id"), 10, 32)
	banner, err := c.bannerService.GetBanner(ctx, uint(id))
	if err != nil {
		response.Error(ctx, 500, err)
		return
	}
	response.Success(ctx, banner)
}

func (c *BannerController) GetBannersByPositionID(ctx *gin.Context) {
	positionID, _ := strconv.ParseUint(ctx.Param("position_id"), 10, 32)
	banners, err := c.bannerService.GetBannersByPositionID(ctx, uint(positionID))
	if err != nil {
		response.Error(ctx, 500, err)
		return
	}
	response.Success(ctx, banners)
} 