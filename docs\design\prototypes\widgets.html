<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 小工具管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }

        .widget-area {
            border: 1px dashed rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            background: rgba(0,0,0,0.1);
            min-height: 150px;
        }
        
        .widget-item {
            background: #2a2a2a;
            border: 1px solid rgba(255,255,255,0.05);
            border-radius: 6px;
            padding: 10px 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: move;
            transition: all 0.2s ease;
        }
        
        .widget-item:hover {
            background: #333;
            border-color: rgba(0,123,255,0.3);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">

    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">小工具管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <a href="#" class="flex items-center justify-center bg-gradient-to-r from-green-500 to-green-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-green-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-save text-white"></i>
                                </span>
                                保存所有更改
                            </span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 左侧：小工具区域 -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- 侧边栏小工具区域 -->
                    <div class="widget-area bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <h3 class="text-lg font-bold text-white mb-4">侧边栏小工具区</h3>
                        <div class="space-y-3" id="sidebar-widget-area">
                            <!-- Widget Item -->
                            <div class="widget-item flex items-center p-3 rounded-lg bg-gray-700/50">
                                <i class="fas fa-grip-vertical text-gray-500 mr-3 cursor-move"></i>
                                <div class="flex-1">
                                    <p class="font-medium text-white">搜索</p>
                                    <p class="text-xs text-gray-400">显示搜索框</p>
                                </div>
                                <button class="text-gray-400 hover:text-white"><i class="fas fa-cog"></i></button>
                                <button class="text-gray-400 hover:text-red-500 ml-2"><i class="fas fa-trash-alt"></i></button>
                            </div>
                             <!-- Widget Item -->
                             <div class="widget-item flex items-center p-3 rounded-lg bg-gray-700/50">
                                <i class="fas fa-grip-vertical text-gray-500 mr-3 cursor-move"></i>
                                <div class="flex-1">
                                    <p class="font-medium text-white">最新文章</p>
                                    <p class="text-xs text-gray-400">显示最近发布的文章列表</p>
                                </div>
                                <button class="text-gray-400 hover:text-white"><i class="fas fa-cog"></i></button>
                                <button class="text-gray-400 hover:text-red-500 ml-2"><i class="fas fa-trash-alt"></i></button>
                            </div>
                        </div>
                    </div>

                    <!-- 页脚小工具区域 -->
                    <div class="widget-area bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <h3 class="text-lg font-bold text-white mb-4">页脚小工具区</h3>
                        <div class="space-y-3" id="footer-widget-area">
                             <!-- Widget Item -->
                             <div class="widget-item flex items-center p-3 rounded-lg bg-gray-700/50">
                                <i class="fas fa-grip-vertical text-gray-500 mr-3 cursor-move"></i>
                                <div class="flex-1">
                                    <p class="font-medium text-white">标签云</p>
                                    <p class="text-xs text-gray-400">显示热门标签</p>
                                </div>
                                <button class="text-gray-400 hover:text-white"><i class="fas fa-cog"></i></button>
                                <button class="text-gray-400 hover:text-red-500 ml-2"><i class="fas fa-trash-alt"></i></button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧：可用小工具 -->
                <div class="lg:col-span-1">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <h3 class="text-lg font-bold text-white mb-4">可用小工具</h3>
                        <div class="space-y-3" id="available-widgets">
                            <!-- Available Widget -->
                            <div class="widget-item flex items-center p-3 rounded-lg bg-gray-700/50 cursor-pointer">
                                <div class="flex-1">
                                    <p class="font-medium text-white">搜索</p>
                                </div>
                                <button class="text-blue-400 hover:text-blue-300"><i class="fas fa-plus"></i></button>
                            </div>
                             <!-- Available Widget -->
                            <div class="widget-item flex items-center p-3 rounded-lg bg-gray-700/50 cursor-pointer">
                                <div class="flex-1">
                                    <p class="font-medium text-white">最新文章</p>
                                </div>
                                <button class="text-blue-400 hover:text-blue-300"><i class="fas fa-plus"></i></button>
                            </div>
                             <!-- Available Widget -->
                             <div class="widget-item flex items-center p-3 rounded-lg bg-gray-700/50 cursor-pointer">
                                <div class="flex-1">
                                    <p class="font-medium text-white">标签云</p>
                                </div>
                                <button class="text-blue-400 hover:text-blue-300"><i class="fas fa-plus"></i></button>
                            </div>
                            <!-- Available Widget -->
                            <div class="widget-item flex items-center p-3 rounded-lg bg-gray-700/50 cursor-pointer">
                                <div class="flex-1">
                                    <p class="font-medium text-white">分类目录</p>
                                </div>
                                <button class="text-blue-400 hover:text-blue-300"><i class="fas fa-plus"></i></button>
                            </div>
                            <!-- Available Widget -->
                            <div class="widget-item flex items-center p-3 rounded-lg bg-gray-700/50 cursor-pointer">
                                <div class="flex-1">
                                    <p class="font-medium text-white">自定义HTML</p>
                                </div>
                                <button class="text-blue-400 hover:text-blue-300"><i class="fas fa-plus"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <!-- 加载必要的JS -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>

</body>
</html> 