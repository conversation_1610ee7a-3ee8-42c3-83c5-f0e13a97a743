/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2024-07-16
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2024-07-16
 * @FilePath: internal/modules/user/infrastructure/repository/GormRoleRepository.go
 * @Description: GORM implementation for the RoleRepository contract.
 *
 * © 2024 GACMS. All rights reserved.
 */
package repository

import (
	"gacms/internal/modules/user/domain/contract"
	"gacms/internal/modules/user/domain/model"
	"gacms/pkg/database"
	"gacms/pkg/logger"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type GormRoleRepository struct {
	db  *gorm.DB
	log *logger.Logger
}

func NewGormRoleRepository(db *gorm.DB, log *logger.Logger) contract.RoleRepository {
	return &GormRoleRepository{db: db, log: log}
}

func (r *GormRoleRepository) Create(ctx *gin.Context, role *model.Role) error {
	return r.db.WithContext(ctx).Create(role).Error
}

func (r *GormRoleRepository) GetByID(ctx *gin.Context, id uint) (*model.Role, error) {
	var role model.Role
	err := r.db.WithContext(ctx).Preload("Permissions").First(&role, id).Error
	return &role, err
}

func (r *GormRoleRepository) GetByName(ctx *gin.Context, siteID uint, name string, userType model.UserType) (*model.Role, error) {
	var role model.Role
	err := r.db.WithContext(ctx).Where("site_id = ? AND name = ? AND user_type = ?", siteID, name, userType).First(&role).Error
	return &role, err
}

func (r *GormRoleRepository) List(ctx *gin.Context, siteID uint, userType model.UserType, options *database.ListOptions) ([]*model.Role, int64, error) {
	var roles []*model.Role
	var total int64

	query := r.db.WithContext(ctx).Model(&model.Role{}).Where("site_id = ? AND user_type = ?", siteID, userType)

	if options.Search != "" {
		query = query.Where("name LIKE ?", "%"+options.Search+"%")
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if options.SortBy != "" {
		order := options.SortBy
		if options.SortDesc {
			order += " desc"
		}
		query = query.Order(order)
	}

	query = query.Limit(options.PageSize).Offset((options.Page - 1) * options.PageSize)

	if err := query.Find(&roles).Error; err != nil {
		return nil, 0, err
	}

	return roles, total, nil
}

func (r *GormRoleRepository) Update(ctx *gin.Context, role *model.Role) error {
	return r.db.WithContext(ctx).Save(role).Error
}

func (r *GormRoleRepository) Delete(ctx *gin.Context, id uint) error {
	// Need to manually delete associations for many2many
	tx := r.db.WithContext(ctx).Begin()
	if err := tx.Model(&model.Role{ID: id}).Association("Permissions").Clear(); err != nil {
		tx.Rollback()
		return err
	}
	if err := tx.Delete(&model.Role{}, id).Error; err != nil {
		tx.Rollback()
		return err
	}
	return tx.Commit().Error
}

func (r *GormRoleRepository) AddPermissionToRole(ctx *gin.Context, roleID uint, permissionID uint) error {
	role := model.Role{ID: roleID}
	perm := model.Permission{ID: permissionID}
	return r.db.WithContext(ctx).Model(&role).Association("Permissions").Append(&perm)
}

func (r *GormRoleRepository) RemovePermissionFromRole(ctx *gin.Context, roleID uint, permissionID uint) error {
	role := model.Role{ID: roleID}
	perm := model.Permission{ID: permissionID}
	return r.db.WithContext(ctx).Model(&role).Association("Permissions").Delete(&perm)
}

func (r *GormRoleRepository) FindPermissionsByRoleID(ctx *gin.Context, roleID uint) ([]*model.Permission, error) {
	var role model.Role
	if err := r.db.WithContext(ctx).Preload("Permissions").First(&role, roleID).Error; err != nil {
		return nil, err
	}
	return role.Permissions, nil
}

func (r *GormRoleRepository) ReplacePermissionsForRole(ctx *gin.Context, roleID uint, permissionIDs []uint) error {
	role := model.Role{ID: roleID}
	var perms []*model.Permission
	for _, pid := range permissionIDs {
		perms = append(perms, &model.Permission{ID: pid})
	}
	return r.db.WithContext(ctx).Model(&role).Association("Permissions").Replace(perms)
}

func (r *GormRoleRepository) AddRoleToUser(ctx *gin.Context, userID uint, roleID uint, userType model.UserType) error {
	userRole := model.UserRole{
		UserID:   userID,
		RoleID:   roleID,
		UserType: userType,
	}
	return r.db.WithContext(ctx).Create(&userRole).Error
}

func (r *GormRoleRepository) RemoveRoleFromUser(ctx *gin.Context, userID uint, roleID uint, userType model.UserType) error {
	return r.db.WithContext(ctx).Where("user_id = ? AND role_id = ? AND user_type = ?", userID, roleID, userType).Delete(&model.UserRole{}).Error
}

func (r *GormRoleRepository) FindRolesByUserID(ctx *gin.Context, userID uint, userType model.UserType) ([]*model.Role, error) {
	var roles []*model.Role
	err := r.db.WithContext(ctx).
		Joins("JOIN user_roles ON user_roles.role_id = roles.id").
		Where("user_roles.user_id = ? AND user_roles.user_type = ?", userID, userType).
		Find(&roles).Error
	return roles, err
} 