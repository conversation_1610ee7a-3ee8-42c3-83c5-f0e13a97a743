/*
 * @Author: <PERSON><PERSON>eh <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: Clion Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: pkg/contract/EventBus.go
 * @Description: 定义应用程序的中央事件总线接口
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import "context"

// EventBus 定义了应用程序中央事件分发器的接口
// 它负责管理订阅关系并将事件分发给适当的处理器
type EventBus interface {
	// Register 注册一个EventHandler来处理特定类型的事件
	// 处理器将自动订阅其SupportedEvents()方法返回的所有事件
	Register(handler EventHandler) error

	// Unregister 取消注册一个EventHandler
	// 处理器将不再接收任何事件
	Unregister(handler EventHandler) error

	// Publish 发布一个事件到总线
	// 总线负责找到正确的处理器并执行它们
	// 同步模式下，事件处理完成后才返回
	Publish(event Event) error

	// PublishAsync 异步发布一个事件到总线
	// 立即返回，不等待事件处理完成
	// 返回一个通道，当所有处理器完成处理时关闭
	PublishAsync(ctx context.Context, event Event) <-chan error

	// HasHandlers 检查是否有处理器注册了指定的事件类型
	HasHandlers(eventName EventName) bool

	// GetHandlers 获取注册了指定事件类型的所有处理器
	GetHandlers(eventName EventName) []EventHandler
}

// EventBusFactory 定义了创建EventBus实例的工厂接口
type EventBusFactory interface {
	// CreateEventBus 创建一个新的EventBus实例
	CreateEventBus() EventBus
}

// TransactionalEventBus 定义了支持事务的事件总线接口
// 事务事件总线可以将事件发布与数据库事务绑定
type TransactionalEventBus interface {
	EventBus

	// PublishInTransaction 在事务中发布事件
	// 只有当事务成功提交时，事件才会被实际发布
	PublishInTransaction(ctx context.Context, event Event) error
} 