/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/user/port/http/controller/Router.go
 * @Description: This file defines a router that aggregates all controllers for the user module.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package controller

// Router acts as a container for all the controllers in the user module.
// The core routing system will discover this struct and can use reflection
// to register routes from all the embedded controllers.
type Router struct {
	AdminController      *AdminController
	AdminAuthController  *AdminAuthController
	AdminRoleController  *AdminRoleController
	MemberAuthController *MemberAuthController
	UserController       *UserController
}

// NewRouter creates a new router that aggregates all user module controllers.
func NewRouter(
	adminController *AdminController,
	adminAuthController *AdminAuthController,
	adminRoleController *Admin<PERSON><PERSON>Controller,
	memberAuthController *Member<PERSON><PERSON><PERSON><PERSON>roller,
	userController *UserController,
) *Router {
	return &Router{
		AdminController:      admin<PERSON><PERSON><PERSON>er,
		AdminAuthController:  adminAuthController,
		AdminRoleController:  adminRoleController,
		MemberAuthController: memberAuthController,
		UserController:       userController,
	}
} 