/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/infrastructure/database/Scope.go
 * @Description: Defines GORM scopes for multi-tenancy.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package database

import "gorm.io/gorm"

// SiteScope returns a GORM scope that filters queries by the 'site_id' column.
// This is a core part of the multi-tenancy data isolation.
func SiteScope(siteID uint) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("site_id = ?", siteID)
	}
}
