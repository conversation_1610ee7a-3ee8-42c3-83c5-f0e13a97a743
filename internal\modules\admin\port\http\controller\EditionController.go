/*
Author: <PERSON><PERSON>eh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package controller

import (
	"net/http"
	"strconv"

	"gacms/internal/core/service"
	"gacms/pkg/contract"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// EditionController 版本管理控制器
type EditionController struct {
	editionManager service.EditionManager
	featureGuard   *service.FeatureGuard
	logger         *zap.Logger
}

// NewEditionController 创建版本管理控制器
func NewEditionController(
	editionManager service.EditionManager,
	logger *zap.Logger,
) *EditionController {
	return &EditionController{
		editionManager: editionManager,
		featureGuard:   service.NewFeatureGuard(editionManager),
		logger:         logger,
	}
}

// RegisterRoutes 注册路由
func (c *EditionController) RegisterRoutes(group *gin.RouterGroup) {
	edition := group.Group("/edition")
	{
		// 版本信息
		edition.GET("/current", c.GetCurrentEdition)
		edition.GET("/info", c.GetEditionInfo)
		edition.GET("/features", c.GetEditionFeatures)
		edition.GET("/limits", c.GetEditionLimits)
		edition.GET("/comparison", c.GetEditionComparison)
		
		// 功能检查
		edition.GET("/features/:name/check", c.CheckFeature)
		edition.GET("/limits/:type/check", c.CheckLimit)
		
		// 版本管理
		edition.GET("/available", c.GetAvailableEditions)
		edition.GET("/upgrade-options", c.GetUpgradeOptions)
		edition.POST("/upgrade", c.UpgradeEdition)
		edition.POST("/downgrade", c.DowngradeEdition)
		
		// 使用统计
		edition.GET("/usage", c.GetUsageStats)
		edition.GET("/feature-limits", c.GetFeatureLimits)
		
		// 编译时信息
		edition.GET("/compiled", c.GetCompiledInfo)
		edition.GET("/validate", c.ValidateEditionConsistency)
	}
}

// GetCurrentEdition 获取当前版本
func (c *EditionController) GetCurrentEdition(ctx *gin.Context) {
	currentEdition := c.editionManager.GetCurrentEdition()
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"edition": currentEdition,
		},
	})
}

// GetEditionInfo 获取版本信息
func (c *EditionController) GetEditionInfo(ctx *gin.Context) {
	info := c.editionManager.GetEditionInfo()
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   info,
	})
}

// GetEditionFeatures 获取版本功能
func (c *EditionController) GetEditionFeatures(ctx *gin.Context) {
	features := c.editionManager.GetEditionFeatures()
	availableFeatures := c.featureGuard.GetAvailableFeatures()
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"all_features":       features,
			"available_features": availableFeatures,
			"count":              len(availableFeatures),
		},
	})
}

// GetEditionLimits 获取版本限制
func (c *EditionController) GetEditionLimits(ctx *gin.Context) {
	limits := c.editionManager.GetEditionLimits()
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   limits,
	})
}

// GetEditionComparison 获取版本对比信息
func (c *EditionController) GetEditionComparison(ctx *gin.Context) {
	comparison := c.featureGuard.GetEditionInfo()
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   comparison,
	})
}

// CheckFeature 检查功能是否可用
func (c *EditionController) CheckFeature(ctx *gin.Context) {
	featureName := ctx.Param("name")
	
	isAvailable := c.featureGuard.IsFeatureAvailable(featureName)
	
	response := gin.H{
		"feature":   featureName,
		"available": isAvailable,
	}
	
	if !isAvailable {
		if err := c.featureGuard.CheckFeatureAccess(featureName); err != nil {
			response["reason"] = err.Error()
		}
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   response,
	})
}

// CheckLimit 检查限制
func (c *EditionController) CheckLimit(ctx *gin.Context) {
	limitType := ctx.Param("type")
	currentValueStr := ctx.Query("current")
	
	currentValue := 0
	if currentValueStr != "" {
		if val, err := strconv.Atoi(currentValueStr); err == nil {
			currentValue = val
		}
	}
	
	isExceeded := c.featureGuard.IsLimitExceeded(limitType, currentValue)
	limits := c.editionManager.GetEditionLimits()
	
	var maxValue int
	switch limitType {
	case "sites":
		maxValue = limits.MaxSites
	case "users":
		maxValue = limits.MaxUsers
	case "storage":
		maxValue = limits.MaxStorage
	case "bandwidth":
		maxValue = limits.MaxBandwidth
	case "pages":
		maxValue = limits.MaxPages
	case "posts":
		maxValue = limits.MaxPosts
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"limit_type":    limitType,
			"current_value": currentValue,
			"max_value":     maxValue,
			"is_exceeded":   isExceeded,
			"is_unlimited":  maxValue == -1,
		},
	})
}

// GetAvailableEditions 获取可用版本
func (c *EditionController) GetAvailableEditions(ctx *gin.Context) {
	editions := c.editionManager.GetAvailableEditions()
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"editions": editions,
			"count":    len(editions),
		},
	})
}

// GetUpgradeOptions 获取升级选项
func (c *EditionController) GetUpgradeOptions(ctx *gin.Context) {
	options := c.editionManager.GetUpgradeOptions()
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"upgrade_options": options,
			"count":           len(options),
		},
	})
}

// UpgradeEdition 升级版本
func (c *EditionController) UpgradeEdition(ctx *gin.Context) {
	var req struct {
		TargetEdition contract.Edition `json:"target_edition" binding:"required"`
		LicenseKey    string           `json:"license_key" binding:"required"`
	}
	
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}
	
	err := c.editionManager.UpgradeEdition(ctx, req.TargetEdition, req.LicenseKey)
	if err != nil {
		c.logger.Error("Failed to upgrade edition", zap.Error(err))
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":   "Failed to upgrade edition",
			"details": err.Error(),
		})
		return
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Edition upgraded successfully",
		"data": gin.H{
			"new_edition": req.TargetEdition,
		},
	})
}

// DowngradeEdition 降级版本
func (c *EditionController) DowngradeEdition(ctx *gin.Context) {
	var req struct {
		TargetEdition contract.Edition `json:"target_edition" binding:"required"`
	}
	
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}
	
	err := c.editionManager.DowngradeEdition(ctx, req.TargetEdition)
	if err != nil {
		c.logger.Error("Failed to downgrade edition", zap.Error(err))
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":   "Failed to downgrade edition",
			"details": err.Error(),
		})
		return
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Edition downgraded successfully",
		"data": gin.H{
			"new_edition": req.TargetEdition,
		},
	})
}

// GetUsageStats 获取使用统计
func (c *EditionController) GetUsageStats(ctx *gin.Context) {
	stats := c.editionManager.GetUsageStats()
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   stats,
	})
}

// GetFeatureLimits 获取功能限制
func (c *EditionController) GetFeatureLimits(ctx *gin.Context) {
	// TODO: 实现获取所有功能的限制信息
	// 这里可以返回各个功能的使用限制
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"feature_limits": []gin.H{
				{
					"feature_name":   "api_calls",
					"max_usage":      1000,
					"current_usage":  150,
					"is_unlimited":   false,
					"is_available":   true,
				},
			},
		},
	})
}

// GetCompiledInfo 获取编译时信息
func (c *EditionController) GetCompiledInfo(ctx *gin.Context) {
	compiler := service.GetCompiledEditionManager()
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"compiled_edition":  compiler.GetCompiledEdition(),
			"compiled_features": compiler.GetCompiledFeatures(),
			"compiled_limits":   compiler.GetCompiledLimits(),
		},
	})
}

// ValidateEditionConsistency 验证版本一致性
func (c *EditionController) ValidateEditionConsistency(ctx *gin.Context) {
	err := c.featureGuard.ValidateEditionConsistency()
	
	if err != nil {
		ctx.JSON(http.StatusConflict, gin.H{
			"status": "error",
			"error":  "Edition consistency validation failed",
			"details": err.Error(),
		})
		return
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Edition consistency validation passed",
	})
}
