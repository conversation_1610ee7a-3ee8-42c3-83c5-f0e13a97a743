/*
Author: <PERSON><PERSON>eh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package service

import (
	"context"
	"fmt"
	"strings"

	"gacms/internal/core/constants"
	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// FeatureGuard 功能守卫接口
type FeatureGuard interface {
	// 功能访问检查
	IsFeatureAvailable(ctx context.Context, tenantDomain string, featureName string) bool
	CheckFeatureAccess(ctx context.Context, tenantDomain string, featureName string) error

	// 限制检查
	IsLimitExceeded(ctx context.Context, tenantDomain string, limitType string, currentValue int) bool
	GetLimit(ctx context.Context, tenantDomain string, limitType string) int

	// 版本一致性验证
	ValidateEditionConsistency(ctx context.Context) error

	// 功能降级处理
	HandleFeatureDegradation(ctx context.Context, tenantDomain string, featureName string, err error) error

	// 批量检查
	BatchFeatureCheck(ctx context.Context, tenantDomain string, features []string) map[string]error
	GetAvailableFeatures(ctx context.Context, tenantDomain string) []string
}

// DefaultFeatureGuard 默认功能守卫实现
// 编译时和运行时双重检查功能可用性
type DefaultFeatureGuard struct {
	// 编译时管理器
	compileTimeManager CompileTimeEditionManager

	// 运行时管理器
	runtimeManager EditionManager

	// 许可证管理器（统一的许可证管理）
	licenseManager contract.LicenseManager

	// 个人版配置提供者
	personalProvider constants.PersonalEditionProvider

	// 专业版配置提供者
	professionalProvider constants.ProfessionalEditionProvider

	logger *zap.Logger
}

// FeatureGuardParams fx依赖注入参数
type FeatureGuardParams struct {
	fx.In

	RuntimeManager EditionManager           `optional:"true"`
	LicenseManager contract.LicenseManager `optional:"true"`
	Logger         *zap.Logger
}

// NewDefaultFeatureGuard 创建默认功能守卫
func NewDefaultFeatureGuard(params FeatureGuardParams) FeatureGuard {
	return &DefaultFeatureGuard{
		compileTimeManager:   GetCompiledEditionManager(),
		runtimeManager:       params.RuntimeManager,
		licenseManager:       params.LicenseManager,
		personalProvider:     constants.GetPersonalEditionProvider(),
		professionalProvider: constants.GetProfessionalEditionProvider(),
		logger:               params.Logger,
	}
}

// 保持向后兼容的构造函数
func NewFeatureGuard(runtimeManager EditionManager) *DefaultFeatureGuard {
	return &DefaultFeatureGuard{
		compileTimeManager: GetCompiledEditionManager(),
		runtimeManager:     runtimeManager,
	}
}

// IsFeatureAvailable 检查功能是否可用
func (g *DefaultFeatureGuard) IsFeatureAvailable(ctx context.Context, tenantDomain string, featureName string) bool {
	return g.CheckFeatureAccess(ctx, tenantDomain, featureName) == nil
}

// CheckFeatureAccess 检查功能访问权限（三层检查）
func (g *DefaultFeatureGuard) CheckFeatureAccess(ctx context.Context, tenantDomain string, featureName string) error {
	// 第一层：编译边界检查
	if !g.compileTimeManager.IsFeatureCompiledIn(featureName) {
		// 尝试优雅降级到个人版
		if g.gracefulDegradationToPersonal(featureName) {
			if g.logger != nil {
				g.logger.Info("Feature access granted through degradation (compile boundary)",
					zap.String("tenant_domain", tenantDomain),
					zap.String("feature", featureName))
			}
			return nil
		}

		compiledEdition := g.compileTimeManager.GetCompiledEdition()
		return fmt.Errorf("feature %s not available in %s edition", featureName, compiledEdition)
	}

	// 第二层：安装配置检查（个人版跳过）
	if !g.isPersonalEdition() && !g.isFeatureEnabledInInstallation(featureName) {
		// 尝试优雅降级到个人版
		if g.gracefulDegradationToPersonal(featureName) {
			if g.logger != nil {
				g.logger.Info("Feature access granted through degradation (installation config)",
					zap.String("tenant_domain", tenantDomain),
					zap.String("feature", featureName))
			}
			return nil
		}

		return fmt.Errorf("feature %s not enabled in installation", featureName)
	}

	// 第三层：许可证验证检查
	if g.licenseManager != nil && !g.licenseManager.IsFeatureAuthorized(ctx, featureName) {
		// 尝试优雅降级到个人版
		if g.gracefulDegradationToPersonal(featureName) {
			if g.logger != nil {
				g.logger.Info("Feature access granted through degradation (license)",
					zap.String("tenant_domain", tenantDomain),
					zap.String("feature", featureName))
			}
			return nil
		}

		return fmt.Errorf("feature %s not authorized by license", featureName)
	}

	if g.logger != nil {
		g.logger.Debug("Feature access granted (three-layer check passed)",
			zap.String("tenant_domain", tenantDomain),
			zap.String("feature", featureName))
	}
	return nil
}

// legacyCheckFeatureAccess 向后兼容的功能检查
func (g *DefaultFeatureGuard) legacyCheckFeatureAccess(featureName string) error {
	// 编译时检查
	if !g.compileTimeManager.IsFeatureCompiledIn(featureName) {
		compiledEdition := g.compileTimeManager.GetCompiledEdition()
		return fmt.Errorf("feature %s is not available in %s edition", featureName, compiledEdition)
	}

	// 运行时检查
	if g.runtimeManager != nil && !g.runtimeManager.IsFeatureAvailable(featureName) {
		currentEdition := g.runtimeManager.GetCurrentEdition()
		return fmt.Errorf("feature %s requires higher edition than %s", featureName, currentEdition)
	}

	return nil
}

// IsLimitExceeded 检查是否超出限制
func (g *DefaultFeatureGuard) IsLimitExceeded(ctx context.Context, tenantDomain string, limitType string, currentValue int) bool {
	// 个人版检查
	if g.isPersonalEdition() {
		limit := g.getPersonalEditionLimit(limitType)
		return limit > 0 && currentValue >= limit
	}

	// 获取许可证信息
	if g.licenseManager != nil {
		licenseInfo := g.licenseManager.GetLicenseInfo()
		if licenseInfo == nil || !licenseInfo.IsValid {
			// 许可证无效，降级到个人版限制
			limit := g.getPersonalEditionLimit(limitType)
			return limit > 0 && currentValue >= limit
		}

		// 商业版无限制
		if licenseInfo.Edition == contract.EditionBusiness {
			return false // 商业版跳过所有限制检查
		}

		// 专业版检查编译时固化的限制
		if licenseInfo.Edition == contract.EditionProfessional {
			limit := g.professionalProvider.GetLimit(limitType)
			return limit > 0 && currentValue >= limit
		}
	}

	return false
}

// GetLimit 获取限制值
func (g *DefaultFeatureGuard) GetLimit(ctx context.Context, tenantDomain string, limitType string) int {
	// 个人版限制
	if g.isPersonalEdition() {
		return g.getPersonalEditionLimit(limitType)
	}

	// 获取许可证信息
	if g.licenseManager != nil {
		licenseInfo := g.licenseManager.GetLicenseInfo()
		if licenseInfo == nil || !licenseInfo.IsValid {
			// 许可证无效，降级到个人版限制
			return g.getPersonalEditionLimit(limitType)
		}

		// 商业版无限制
		if licenseInfo.Edition == contract.EditionBusiness {
			return 0 // 商业版无限制
		}

		// 专业版返回编译时固化的限制
		if licenseInfo.Edition == contract.EditionProfessional {
			return g.professionalProvider.GetLimit(limitType)
		}
	}

	return 0
}

// getPersonalEditionLimit 获取个人版限制
func (g *DefaultFeatureGuard) getPersonalEditionLimit(limitType string) int {
	return g.personalProvider.GetLimit(limitType)
}

// legacyIsLimitExceeded 向后兼容的限制检查
func (g *DefaultFeatureGuard) legacyIsLimitExceeded(limitType string, currentValue int) bool {
	// 1. 编译时检查：限制是否编译进来
	if !g.compileTimeManager.IsLimitCompiledIn(limitType) {
		return false // 编译时没有限制
	}

	// 2. 运行时检查：当前使用是否超出限制
	if g.runtimeManager != nil {
		return g.runtimeManager.IsLimitExceeded(limitType, currentValue)
	}

	return false
}

// ValidateEditionConsistency 验证版本一致性
func (g *DefaultFeatureGuard) ValidateEditionConsistency(ctx context.Context) error {
	// 如果有许可证管理器，使用新的一致性验证逻辑
	if g.licenseManager != nil {
		// 验证许可证
		licenseInfo, err := g.licenseManager.ValidateLicense(ctx)
		if err != nil {
			return fmt.Errorf("license validation failed: %w", err)
		}

		// 获取当前系统版本
		if g.runtimeManager != nil {
			currentEdition := g.runtimeManager.GetCurrentEdition()

			// 检查系统版本是否超出许可证版本
			if getEditionLevel(currentEdition) > getEditionLevel(licenseInfo.Edition) {
				return fmt.Errorf("current edition %s exceeds license edition %s",
					currentEdition, licenseInfo.Edition)
			}

			if g.logger != nil {
				g.logger.Info("Edition consistency validation passed",
					zap.String("current_edition", string(currentEdition)),
					zap.String("license_edition", string(licenseInfo.Edition)))
			}
		}

		return nil
	}

	// 向后兼容：使用原有的一致性验证
	return g.legacyValidateEditionConsistency()
}

// HandleFeatureDegradation 处理功能降级
func (g *DefaultFeatureGuard) HandleFeatureDegradation(ctx context.Context, tenantDomain string, featureName string, err error) error {
	// 如果许可证管理器可用且许可证有效，不需要降级
	if g.licenseManager != nil {
		licenseInfo := g.licenseManager.GetLicenseInfo()
		if licenseInfo != nil && licenseInfo.IsValid {
			// 许可证有效，不需要降级
			return nil
		}
	}

	if g.logger != nil {
		g.logger.Warn("Feature access denied, attempting degradation",
			zap.String("tenant_domain", tenantDomain),
			zap.String("feature", featureName),
			zap.Error(err))
	}

	// 根据功能类型进行降级处理
	switch {
	case strings.HasPrefix(featureName, "advanced_"):
		// 高级功能降级到基础功能
		basicFeature := strings.Replace(featureName, "advanced_", "basic_", 1)
		if g.IsFeatureAvailable(ctx, tenantDomain, basicFeature) {
			if g.logger != nil {
				g.logger.Info("Feature degraded to basic version",
					zap.String("original_feature", featureName),
					zap.String("degraded_feature", basicFeature))
			}
			return nil
		}

	case strings.HasPrefix(featureName, "enterprise_"):
		// 企业功能降级到高级功能
		advancedFeature := strings.Replace(featureName, "enterprise_", "advanced_", 1)
		if g.IsFeatureAvailable(ctx, tenantDomain, advancedFeature) {
			if g.logger != nil {
				g.logger.Info("Feature degraded to advanced version",
					zap.String("original_feature", featureName),
					zap.String("degraded_feature", advancedFeature))
			}
			return nil
		}

		// 如果高级功能也不可用，尝试基础功能
		basicFeature := strings.Replace(featureName, "enterprise_", "basic_", 1)
		if g.IsFeatureAvailable(ctx, tenantDomain, basicFeature) {
			if g.logger != nil {
				g.logger.Info("Feature degraded to basic version",
					zap.String("original_feature", featureName),
					zap.String("degraded_feature", basicFeature))
			}
			return nil
		}
	}

	// 无法降级，返回原始错误
	return fmt.Errorf("feature degradation failed: %w", err)
}

// BatchFeatureCheck 批量功能检查
func (g *DefaultFeatureGuard) BatchFeatureCheck(ctx context.Context, tenantDomain string, features []string) map[string]error {
	results := make(map[string]error)

	for _, feature := range features {
		results[feature] = g.CheckFeatureAccess(ctx, tenantDomain, feature)
	}

	return results
}

// GetAvailableFeatures 获取可用功能列表
func (g *DefaultFeatureGuard) GetAvailableFeatures(ctx context.Context, tenantDomain string) []string {
	// 个人版功能
	if g.isPersonalEdition() {
		return g.personalProvider.GetFeatures()
	}

	// 获取许可证信息
	if g.licenseManager != nil {
		licenseInfo := g.licenseManager.GetLicenseInfo()
		if licenseInfo == nil || !licenseInfo.IsValid {
			// 许可证无效，返回个人版功能
			return g.personalProvider.GetFeatures()
		}

		// 商业版返回编译时包含的所有功能
		if licenseInfo.Edition == contract.EditionBusiness {
			return g.compileTimeManager.GetCompiledFeatures()
		}

		// 专业版返回编译时固化的功能
		if licenseInfo.Edition == contract.EditionProfessional {
			return g.professionalProvider.GetFeatures()
		}
	}

	// 默认返回个人版功能
	return g.personalProvider.GetFeatures()
}

// 删除isPersonalContentUnlimited函数，个人版直接在限制检查中处理

// gracefulDegradationToPersonal 优雅降级到个人版功能
func (g *DefaultFeatureGuard) gracefulDegradationToPersonal(featureName string) bool {
	return g.personalProvider.IsFeature(featureName)
}

// isPersonalEdition 检查是否是个人版
func (g *DefaultFeatureGuard) isPersonalEdition() bool {
	if g.compileTimeManager != nil {
		return g.compileTimeManager.GetCompiledEdition() == contract.EditionPersonal
	}
	return false
}

// isFeatureEnabledInInstallation 检查功能是否在安装时启用
func (g *DefaultFeatureGuard) isFeatureEnabledInInstallation(featureName string) bool {
	// 个人版功能始终启用
	if g.personalProvider.IsFeature(featureName) {
		return true
	}

	// 专业版/商业版需要检查安装配置
	// TODO: 实现安装配置的持久化和加载
	// 目前暂时返回true，表示所有编译时包含的功能都启用
	return true
}

// legacyGetAvailableFeatures 向后兼容的功能获取
func (g *DefaultFeatureGuard) legacyGetAvailableFeatures() []string {
	compiledFeatures := g.compileTimeManager.GetCompiledFeatures()
	var availableFeatures []string

	for _, feature := range compiledFeatures {
		if g.runtimeManager != nil && g.runtimeManager.IsFeatureAvailable(feature) {
			availableFeatures = append(availableFeatures, feature)
		}
	}

	return availableFeatures
}

// legacyValidateEditionConsistency 向后兼容的版本一致性验证
func (g *DefaultFeatureGuard) legacyValidateEditionConsistency() error {
	if g.runtimeManager == nil {
		return nil
	}

	compiledEdition := g.compileTimeManager.GetCompiledEdition()
	runtimeEdition := g.runtimeManager.GetCurrentEdition()

	// 检查运行时版本是否超出编译时版本
	compiledLevel := getEditionLevel(compiledEdition)
	runtimeLevel := getEditionLevel(runtimeEdition)

	if runtimeLevel > compiledLevel {
		return fmt.Errorf("runtime edition %s exceeds compiled edition %s", runtimeEdition, compiledEdition)
	}

	return nil
}

// GetEditionInfo 获取版本信息（编译时+运行时）
func (g *FeatureGuard) GetEditionInfo() *EditionComparisonInfo {
	return &EditionComparisonInfo{
		CompiledEdition: g.compileTimeManager.GetCompiledEdition(),
		RuntimeEdition:  g.runtimeManager.GetCurrentEdition(),
		CompiledFeatures: g.compileTimeManager.GetCompiledFeatures(),
		RuntimeFeatures:  g.runtimeManager.GetEditionFeatures(),
		CompiledLimits:   g.compileTimeManager.GetCompiledLimits(),
		RuntimeLimits:    g.runtimeManager.GetEditionLimits(),
	}
}

// EditionComparisonInfo 版本对比信息
type EditionComparisonInfo struct {
	CompiledEdition  contract.Edition `json:"compiled_edition"`
	RuntimeEdition   contract.Edition `json:"runtime_edition"`
	CompiledFeatures []string         `json:"compiled_features"`
	RuntimeFeatures  []string         `json:"runtime_features"`
	CompiledLimits   *EditionLimits   `json:"compiled_limits"`
	RuntimeLimits    *EditionLimits   `json:"runtime_limits"`
}

// ValidateEditionConsistency 验证版本一致性
func (g *FeatureGuard) ValidateEditionConsistency() error {
	compiledEdition := g.compileTimeManager.GetCompiledEdition()
	runtimeEdition := g.runtimeManager.GetCurrentEdition()
	
	// 检查运行时版本是否超出编译时版本
	compiledLevel := getEditionLevel(compiledEdition)
	runtimeLevel := getEditionLevel(runtimeEdition)
	
	if runtimeLevel > compiledLevel {
		return fmt.Errorf("runtime edition %s exceeds compiled edition %s", runtimeEdition, compiledEdition)
	}
	
	return nil
}

// getEditionLevel 获取版本级别
func getEditionLevel(edition contract.Edition) int {
	levels := map[contract.Edition]int{
		contract.EditionPersonal:     0,
		contract.EditionProfessional: 1,
		contract.EditionBusiness:     2,
	}
	
	if level, exists := levels[edition]; exists {
		return level
	}
	
	return -1
}

// FeatureAccessMiddleware 功能访问中间件
func FeatureAccessMiddleware(guard FeatureGuard, featureName string) func(ctx context.Context, tenantDomain string, next func() error) error {
	return func(ctx context.Context, tenantDomain string, next func() error) error {
		// 检查功能访问权限
		if err := guard.CheckFeatureAccess(ctx, tenantDomain, featureName); err != nil {
			// 尝试功能降级
			if degradeErr := guard.HandleFeatureDegradation(ctx, tenantDomain, featureName, err); degradeErr != nil {
				return fmt.Errorf("feature access denied and degradation failed: %w", degradeErr)
			}
		}

		// 执行下一步
		return next()
	}
}

// LimitCheckMiddleware 限制检查中间件
func LimitCheckMiddleware(guard FeatureGuard, limitType string, getCurrentValue func(ctx context.Context, tenantDomain string) int) func(ctx context.Context, tenantDomain string, next func() error) error {
	return func(ctx context.Context, tenantDomain string, next func() error) error {
		// 获取当前值
		currentValue := getCurrentValue(ctx, tenantDomain)

		// 检查是否超出限制
		if guard.IsLimitExceeded(ctx, tenantDomain, limitType, currentValue) {
			limit := guard.GetLimit(ctx, tenantDomain, limitType)
			return fmt.Errorf("limit exceeded: current %d, limit %d", currentValue, limit)
		}

		// 执行下一步
		return next()
	}
}

// 向后兼容的中间件方法
func (g *DefaultFeatureGuard) FeatureAccessMiddleware(featureName string) func() error {
	return func() error {
		return g.legacyCheckFeatureAccess(featureName)
	}
}

func (g *DefaultFeatureGuard) LimitCheckMiddleware(limitType string, getCurrentValue func() int) func() error {
	return func() error {
		currentValue := getCurrentValue()
		if g.legacyIsLimitExceeded(limitType, currentValue) {
			if g.runtimeManager != nil {
				limits := g.runtimeManager.GetEditionLimits()
				var maxValue int

				switch limitType {
				case "sites":
					maxValue = limits.MaxSites
				case "users":
					maxValue = limits.MaxUsers
				}

				return fmt.Errorf("%s limit exceeded: current %d, max %d", limitType, currentValue, maxValue)
			}
		}
		return nil
	}
}
