<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 内容审核</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="content.html" class="hover:text-white">内容管理</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">内容审核</span>
            </div>

            <!-- 内容审核概览 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">内容审核</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <a href="content.html" class="flex items-center justify-center bg-gradient-to-r from-gray-600 to-gray-700 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-gray-600/30 relative overflow-hidden">
                            <i class="fas fa-arrow-left mr-2"></i>
                            返回内容管理
                        </a>
                    </div>
                </div>

                <!-- 内容统计卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-6">
                    <!-- 待审核 -->
                    <div class="bg-gray-800/20 p-5 rounded-xl border border-gray-700">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center mr-4">
                                <i class="fas fa-hourglass-half text-yellow-500 text-xl"></i>
                            </div>
                            <div>
                                <div class="text-sm text-gray-400">待审核</div>
                                <div class="text-xl font-semibold text-white">12</div>
                                <div class="text-xs text-yellow-400 mt-0.5">需要审核</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 今日审核通过 -->
                    <div class="bg-gray-800/20 p-5 rounded-xl border border-gray-700">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                                <i class="fas fa-check-circle text-green-500 text-xl"></i>
                            </div>
                            <div>
                                <div class="text-sm text-gray-400">今日审核通过</div>
                                <div class="text-xl font-semibold text-white">8</div>
                                <div class="text-xs text-green-400 mt-0.5">已发布</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 今日审核拒绝 -->
                    <div class="bg-gray-800/20 p-5 rounded-xl border border-gray-700">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-red-500/20 flex items-center justify-center mr-4">
                                <i class="fas fa-times-circle text-red-500 text-xl"></i>
                            </div>
                            <div>
                                <div class="text-sm text-gray-400">今日审核拒绝</div>
                                <div class="text-xl font-semibold text-white">3</div>
                                <div class="text-xs text-red-400 mt-0.5">需要修改</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 平均审核时长 -->
                    <div class="bg-gray-800/20 p-5 rounded-xl border border-gray-700">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                                <i class="fas fa-clock text-blue-500 text-xl"></i>
                            </div>
                            <div>
                                <div class="text-sm text-gray-400">平均审核时长</div>
                                <div class="text-xl font-semibold text-white">2.5 小时</div>
                                <div class="text-xs text-blue-400 mt-0.5">今日</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 内容审核筛选 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap gap-4">
                    <!-- 状态筛选 -->
                    <div class="flex-grow lg:flex-grow-0">
                        <label class="block text-sm text-gray-400 mb-1">状态</label>
                        <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2.5 text-white appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="pending" selected>待审核</option>
                            <option value="approved">已通过</option>
                            <option value="rejected">已拒绝</option>
                            <option value="all">全部</option>
                        </select>
                    </div>
                    
                    <!-- 分类筛选 -->
                    <div class="flex-grow lg:flex-grow-0">
                        <label class="block text-sm text-gray-400 mb-1">分类</label>
                        <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2.5 text-white appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all" selected>全部分类</option>
                            <option value="tech">技术</option>
                            <option value="design">设计</option>
                            <option value="marketing">营销</option>
                            <option value="business">商业</option>
                        </select>
                    </div>
                    
                    <!-- 作者筛选 -->
                    <div class="flex-grow lg:flex-grow-0">
                        <label class="block text-sm text-gray-400 mb-1">作者</label>
                        <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2.5 text-white appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all" selected>全部作者</option>
                            <option value="zhangsan">张三</option>
                            <option value="lisi">李四</option>
                            <option value="wangwu">王五</option>
                            <option value="zhaoliu">赵六</option>
                        </select>
                    </div>
                    
                    <!-- 日期筛选 -->
                    <div class="flex-grow lg:flex-grow-0">
                        <label class="block text-sm text-gray-400 mb-1">提交日期</label>
                        <div class="relative">
                            <input type="date" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2.5 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    
                    <!-- 搜索 -->
                    <div class="flex-grow">
                        <label class="block text-sm text-gray-400 mb-1">搜索</label>
                        <div class="relative">
                            <input type="text" placeholder="搜索标题、内容..." class="w-full bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2.5 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 内容列表 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold mb-4 text-white">待审核内容</h3>
                
                <!-- 待审核内容列表 -->
                <div class="space-y-4">
                    <!-- 内容项 1 -->
                    <div class="border border-gray-700 border-l-4 border-l-yellow-500 rounded-lg overflow-hidden">
                        <div class="flex flex-col md:flex-row">
                            <!-- 内容缩略图 -->
                            <div class="w-full md:w-48 lg:w-56 flex-shrink-0">
                                <img src="https://images.unsplash.com/photo-1487014679447-9f8336841d58?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&h=300&q=80" 
                                     alt="AI Trends" class="w-full h-48 md:h-full object-cover">
                            </div>
                            
                            <!-- 内容信息 -->
                            <div class="p-5 flex-grow">
                                <div class="flex flex-wrap justify-between items-center mb-2">
                                    <div class="flex items-center mb-2 md:mb-0">
                                        <span class="bg-yellow-500/20 text-yellow-500 text-xs px-2 py-1 rounded">待审核</span>
                                        <span class="ml-3 bg-blue-500/20 text-blue-500 text-xs px-2 py-1 rounded">技术</span>
                                        <span class="ml-3 text-gray-400 text-xs">ID: *********</span>
                                    </div>
                                    <div class="text-gray-400 text-xs">
                                        提交时间: 2025-06-05 09:42
                                    </div>
                                </div>
                                
                                <h4 class="text-white text-lg font-medium mb-2">2025年人工智能发展趋势预测</h4>
                                <p class="text-gray-400 text-sm mb-3 line-clamp-2">本文深入探讨了2025年人工智能领域的十大发展趋势，包括生成式AI的广泛应用、多模态模型的成熟、联邦学习的普及以及AI伦理的重要性提升等。文章基于最新研究和行业数据，对各趋势进行了详细分析，并提供了实际案例。</p>
                                
                                <div class="flex flex-wrap justify-between items-center">
                                    <div class="flex items-center">
                                        <img src="./assets/images/avatar-1.jpg" alt="Author" class="w-8 h-8 rounded-full object-cover">
                                        <span class="ml-2 text-gray-300">张小明</span>
                                        <span class="ml-3 text-gray-500 text-sm flex items-center">
                                            <i class="fas fa-eye mr-1"></i> 0
                                        </span>
                                        <span class="ml-3 text-gray-500 text-sm flex items-center">
                                            <i class="fas fa-comment mr-1"></i> 0
                                        </span>
                                    </div>
                                    
                                    <div class="flex space-x-2 mt-3 md:mt-0">
                                        <a href="post_edit.html" class="bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-2 rounded text-sm transition-colors">
                                            预览
                                        </a>
                                        <button class="bg-green-500/20 hover:bg-green-500/30 text-green-400 px-3 py-2 rounded text-sm transition-colors">
                                            通过
                                        </button>
                                        <button class="bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-400 px-3 py-2 rounded text-sm transition-colors">
                                            修改后通过
                                        </button>
                                        <button class="bg-red-500/20 hover:bg-red-500/30 text-red-400 px-3 py-2 rounded text-sm transition-colors">
                                            拒绝
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 内容项 2 -->
                    <div class="border border-gray-700 border-l-4 border-l-yellow-500 rounded-lg overflow-hidden">
                        <div class="flex flex-col md:flex-row">
                            <!-- 内容缩略图 -->
                            <div class="w-full md:w-48 lg:w-56 flex-shrink-0">
                                <img src="https://images.unsplash.com/photo-1504639725590-34d0984388bd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&h=300&q=80" 
                                     alt="Marketing" class="w-full h-48 md:h-full object-cover">
                            </div>
                            
                            <!-- 内容信息 -->
                            <div class="p-5 flex-grow">
                                <div class="flex flex-wrap justify-between items-center mb-2">
                                    <div class="flex items-center mb-2 md:mb-0">
                                        <span class="bg-yellow-500/20 text-yellow-500 text-xs px-2 py-1 rounded">待审核</span>
                                        <span class="ml-3 bg-purple-500/20 text-purple-500 text-xs px-2 py-1 rounded">营销</span>
                                        <span class="ml-3 text-gray-400 text-xs">ID: *********</span>
                                    </div>
                                    <div class="text-gray-400 text-xs">
                                        提交时间: 2025-06-05 08:15
                                    </div>
                                </div>
                                
                                <h4 class="text-white text-lg font-medium mb-2">数字营销新策略：个性化内容与算法优化</h4>
                                <p class="text-gray-400 text-sm mb-3 line-clamp-2">本文分析了数字营销中个性化内容的重要性，详细介绍了如何利用人工智能和用户行为数据构建精准的个性化内容策略。文章还探讨了多种内容分发算法的工作原理，并提供了实用的优化技巧和最佳实践案例。</p>
                                
                                <div class="flex flex-wrap justify-between items-center">
                                    <div class="flex items-center">
                                        <img src="./assets/images/avatar-2.jpg" alt="Author" class="w-8 h-8 rounded-full object-cover">
                                        <span class="ml-2 text-gray-300">李华</span>
                                        <span class="ml-3 text-gray-500 text-sm flex items-center">
                                            <i class="fas fa-eye mr-1"></i> 0
                                        </span>
                                        <span class="ml-3 text-gray-500 text-sm flex items-center">
                                            <i class="fas fa-comment mr-1"></i> 0
                                        </span>
                                    </div>
                                    
                                    <div class="flex space-x-2 mt-3 md:mt-0">
                                        <a href="post_edit.html" class="bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-2 rounded text-sm transition-colors">
                                            预览
                                        </a>
                                        <button class="bg-green-500/20 hover:bg-green-500/30 text-green-400 px-3 py-2 rounded text-sm transition-colors">
                                            通过
                                        </button>
                                        <button class="bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-400 px-3 py-2 rounded text-sm transition-colors">
                                            修改后通过
                                        </button>
                                        <button class="bg-red-500/20 hover:bg-red-500/30 text-red-400 px-3 py-2 rounded text-sm transition-colors">
                                            拒绝
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 内容项 3 -->
                    <div class="border border-gray-700 border-l-4 border-l-yellow-500 rounded-lg overflow-hidden">
                        <div class="flex flex-col md:flex-row">
                            <!-- 内容缩略图 -->
                            <div class="w-full md:w-48 lg:w-56 flex-shrink-0">
                                <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&h=300&q=80" 
                                     alt="UX Design" class="w-full h-48 md:h-full object-cover">
                            </div>
                            
                            <!-- 内容信息 -->
                            <div class="p-5 flex-grow">
                                <div class="flex flex-wrap justify-between items-center mb-2">
                                    <div class="flex items-center mb-2 md:mb-0">
                                        <span class="bg-yellow-500/20 text-yellow-500 text-xs px-2 py-1 rounded">待审核</span>
                                        <span class="ml-3 bg-pink-500/20 text-pink-500 text-xs px-2 py-1 rounded">设计</span>
                                        <span class="ml-3 text-gray-400 text-xs">ID: *********</span>
                                    </div>
                                    <div class="text-gray-400 text-xs">
                                        提交时间: 2025-06-04 16:30
                                    </div>
                                </div>
                                
                                <h4 class="text-white text-lg font-medium mb-2">如何提升用户体验：最新UX设计趋势与方法</h4>
                                <p class="text-gray-400 text-sm mb-3 line-clamp-2">本文探讨了2025年最新的UX设计趋势，包括微交互设计、声音用户界面、情感化设计、沉浸式界面等。文章结合实际案例分析了每种趋势的应用场景和实现方法，并提供了可操作性强的设计建议和最佳实践。</p>
                                
                                <div class="flex flex-wrap justify-between items-center">
                                    <div class="flex items-center">
                                        <img src="./assets/images/avatar-3.jpg" alt="Author" class="w-8 h-8 rounded-full object-cover">
                                        <span class="ml-2 text-gray-300">王刚</span>
                                        <span class="ml-3 text-gray-500 text-sm flex items-center">
                                            <i class="fas fa-eye mr-1"></i> 0
                                        </span>
                                        <span class="ml-3 text-gray-500 text-sm flex items-center">
                                            <i class="fas fa-comment mr-1"></i> 0
                                        </span>
                                    </div>
                                    
                                    <div class="flex space-x-2 mt-3 md:mt-0">
                                        <a href="post_edit.html" class="bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-2 rounded text-sm transition-colors">
                                            预览
                                        </a>
                                        <button class="bg-green-500/20 hover:bg-green-500/30 text-green-400 px-3 py-2 rounded text-sm transition-colors">
                                            通过
                                        </button>
                                        <button class="bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-400 px-3 py-2 rounded text-sm transition-colors">
                                            修改后通过
                                        </button>
                                        <button class="bg-red-500/20 hover:bg-red-500/30 text-red-400 px-3 py-2 rounded text-sm transition-colors">
                                            拒绝
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 分页 -->
                <div class="mt-6 flex justify-between items-center">
                    <div class="text-sm text-gray-400">
                        显示 <span class="text-white">1-3</span> 条，共 <span class="text-white">12</span> 条
                    </div>
                    <div class="flex space-x-1">
                        <a href="#" class="px-3 py-1 rounded border border-gray-700 bg-gray-800/20 text-gray-400 hover:bg-gray-800/40 hover:text-white transition-colors">
                            <i class="fas fa-chevron-left text-xs"></i>
                        </a>
                        <a href="#" class="px-3 py-1 rounded border border-blue-500 bg-blue-500/20 text-white">1</a>
                        <a href="#" class="px-3 py-1 rounded border border-gray-700 bg-gray-800/20 text-gray-400 hover:bg-gray-800/40 hover:text-white transition-colors">2</a>
                        <a href="#" class="px-3 py-1 rounded border border-gray-700 bg-gray-800/20 text-gray-400 hover:bg-gray-800/40 hover:text-white transition-colors">3</a>
                        <a href="#" class="px-3 py-1 rounded border border-gray-700 bg-gray-800/20 text-gray-400 hover:bg-gray-800/40 hover:text-white transition-colors">4</a>
                        <a href="#" class="px-3 py-1 rounded border border-gray-700 bg-gray-800/20 text-gray-400 hover:bg-gray-800/40 hover:text-white transition-colors">
                            <i class="fas fa-chevron-right text-xs"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
</body>
</html> 