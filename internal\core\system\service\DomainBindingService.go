/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/system/service/DomainBindingService.go
 * @Description: 域名绑定基础设施服务实现
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"fmt"

	"gacms/internal/core/system/contract"
	"gacms/internal/core/system/model"
	pkgContract "gacms/pkg/contract"
	"go.uber.org/zap"
)

// DomainBindingService 域名绑定服务
type DomainBindingService struct {
	repo     contract.DomainBindingRepository
	siteRepo contract.SiteRepository
	eventMgr pkgContract.EventManager
	logger   *zap.Logger
}

// NewDomainBindingService 创建域名绑定服务
func NewDomainBindingService(
	repo contract.DomainBindingRepository,
	siteRepo contract.SiteRepository,
	eventMgr pkgContract.EventManager,
	logger *zap.Logger,
) *DomainBindingService {
	return &DomainBindingService{
		repo:     repo,
		siteRepo: siteRepo,
		eventMgr: eventMgr,
		logger:   logger,
	}
}

// CreateModuleBinding 创建模块绑定
func (s *DomainBindingService) CreateModuleBinding(domain string, siteID uint, moduleSlug string) (*model.DomainBinding, error) {
	// 验证站点存在
	site, err := s.siteRepo.GetByID(siteID)
	if err != nil {
		return nil, fmt.Errorf("failed to get site: %w", err)
	}
	if site == nil {
		return nil, fmt.Errorf("site not found")
	}
	
	// 检查域名是否已绑定
	existing, err := s.repo.GetByDomain(domain)
	if err != nil {
		return nil, fmt.Errorf("failed to check domain binding: %w", err)
	}
	if existing != nil {
		return nil, fmt.Errorf("domain %s already bound", domain)
	}
	
	binding := &model.DomainBinding{
		Domain:      domain,
		SiteID:      siteID,
		BindingType: model.BindingTypeModule,
		ModuleSlug:  &moduleSlug,
	}
	
	if err := s.repo.Create(binding); err != nil {
		return nil, fmt.Errorf("failed to create domain binding: %w", err)
	}
	
	// 发布域名绑定事件
	s.publishBindingEvent("domain.binding.created", binding)
	
	s.logger.Info("Module binding created",
		zap.String("domain", domain),
		zap.Uint("site_id", siteID),
		zap.String("module", moduleSlug),
	)
	
	return binding, nil
}

// CreateCategoryBinding 创建栏目绑定
func (s *DomainBindingService) CreateCategoryBinding(domain string, siteID uint, categoryID uint) (*model.DomainBinding, error) {
	// 验证站点存在
	site, err := s.siteRepo.GetByID(siteID)
	if err != nil {
		return nil, fmt.Errorf("failed to get site: %w", err)
	}
	if site == nil {
		return nil, fmt.Errorf("site not found")
	}
	
	// 检查域名是否已绑定
	existing, err := s.repo.GetByDomain(domain)
	if err != nil {
		return nil, fmt.Errorf("failed to check domain binding: %w", err)
	}
	if existing != nil {
		return nil, fmt.Errorf("domain %s already bound", domain)
	}
	
	binding := &model.DomainBinding{
		Domain:      domain,
		SiteID:      siteID,
		BindingType: model.BindingTypeCategory,
		CategoryID:  &categoryID,
	}
	
	if err := s.repo.Create(binding); err != nil {
		return nil, fmt.Errorf("failed to create domain binding: %w", err)
	}
	
	// 发布域名绑定事件
	s.publishBindingEvent("domain.binding.created", binding)
	
	s.logger.Info("Category binding created",
		zap.String("domain", domain),
		zap.Uint("site_id", siteID),
		zap.Uint("category_id", categoryID),
	)
	
	return binding, nil
}

// UpdateBinding 更新域名绑定
func (s *DomainBindingService) UpdateBinding(bindingID uint, domain string) (*model.DomainBinding, error) {
	binding, err := s.repo.GetByID(bindingID)
	if err != nil {
		return nil, fmt.Errorf("failed to get binding: %w", err)
	}
	if binding == nil {
		return nil, fmt.Errorf("binding not found")
	}
	
	// 如果域名有变化，检查新域名是否已绑定
	if binding.Domain != domain {
		existing, err := s.repo.GetByDomain(domain)
		if err != nil {
			return nil, fmt.Errorf("failed to check domain binding: %w", err)
		}
		if existing != nil && existing.ID != bindingID {
			return nil, fmt.Errorf("domain %s already bound", domain)
		}
	}
	
	binding.Domain = domain
	
	if err := s.repo.Update(binding); err != nil {
		return nil, fmt.Errorf("failed to update binding: %w", err)
	}
	
	// 发布域名绑定更新事件
	s.publishBindingEvent("domain.binding.updated", binding)
	
	s.logger.Info("Domain binding updated",
		zap.Uint("binding_id", bindingID),
		zap.String("domain", domain),
	)
	
	return binding, nil
}

// DeleteBinding 删除域名绑定
func (s *DomainBindingService) DeleteBinding(bindingID uint) error {
	binding, err := s.repo.GetByID(bindingID)
	if err != nil {
		return fmt.Errorf("failed to get binding: %w", err)
	}
	if binding == nil {
		return fmt.Errorf("binding not found")
	}
	
	if err := s.repo.Delete(bindingID); err != nil {
		return fmt.Errorf("failed to delete binding: %w", err)
	}
	
	// 发布域名绑定删除事件
	s.publishBindingEvent("domain.binding.deleted", binding)
	
	s.logger.Info("Domain binding deleted",
		zap.Uint("binding_id", bindingID),
		zap.String("domain", binding.Domain),
	)
	
	return nil
}

// GetBinding 获取域名绑定
func (s *DomainBindingService) GetBinding(bindingID uint) (*model.DomainBinding, error) {
	return s.repo.GetByID(bindingID)
}

// GetBindingByDomain 根据域名获取绑定
func (s *DomainBindingService) GetBindingByDomain(domain string) (*model.DomainBinding, error) {
	return s.repo.GetByDomain(domain)
}

// GetBindingByDomainWithRules 根据域名获取绑定（包含URL重写规则）
func (s *DomainBindingService) GetBindingByDomainWithRules(domain string) (*model.DomainBinding, error) {
	return s.repo.GetByDomainWithRules(domain)
}

// ListBindingsBySite 列出站点的域名绑定
func (s *DomainBindingService) ListBindingsBySite(siteID uint, page, pageSize int) ([]*model.DomainBinding, int64, error) {
	return s.repo.ListBySiteID(siteID, page, pageSize)
}

// EnableURLRewrite 启用URL重写
func (s *DomainBindingService) EnableURLRewrite(bindingID uint, defaultController, defaultAction string) error {
	binding, err := s.repo.GetByID(bindingID)
	if err != nil {
		return fmt.Errorf("failed to get binding: %w", err)
	}
	if binding == nil {
		return fmt.Errorf("binding not found")
	}
	
	binding.URLRewriteEnabled = true
	binding.DefaultController = defaultController
	binding.DefaultAction = defaultAction
	
	if err := s.repo.Update(binding); err != nil {
		return fmt.Errorf("failed to enable URL rewrite: %w", err)
	}
	
	// 发布URL重写启用事件
	eventData := map[string]interface{}{
		"binding_id":         bindingID,
		"domain":            binding.Domain,
		"default_controller": defaultController,
		"default_action":     defaultAction,
	}
	event := s.eventMgr.CreateEvent(nil, "domain.url_rewrite.enabled", eventData)
	s.eventMgr.PublishEvent(event)
	
	s.logger.Info("URL rewrite enabled",
		zap.Uint("binding_id", bindingID),
		zap.String("domain", binding.Domain),
		zap.String("default_controller", defaultController),
		zap.String("default_action", defaultAction),
	)
	
	return nil
}

// DisableURLRewrite 禁用URL重写
func (s *DomainBindingService) DisableURLRewrite(bindingID uint) error {
	binding, err := s.repo.GetByID(bindingID)
	if err != nil {
		return fmt.Errorf("failed to get binding: %w", err)
	}
	if binding == nil {
		return fmt.Errorf("binding not found")
	}
	
	binding.URLRewriteEnabled = false
	binding.DefaultController = ""
	binding.DefaultAction = ""
	
	if err := s.repo.Update(binding); err != nil {
		return fmt.Errorf("failed to disable URL rewrite: %w", err)
	}
	
	// 发布URL重写禁用事件
	eventData := map[string]interface{}{
		"binding_id": bindingID,
		"domain":     binding.Domain,
	}
	event := s.eventMgr.CreateEvent(nil, "domain.url_rewrite.disabled", eventData)
	s.eventMgr.PublishEvent(event)
	
	s.logger.Info("URL rewrite disabled",
		zap.Uint("binding_id", bindingID),
		zap.String("domain", binding.Domain),
	)
	
	return nil
}

// URL重写规则管理方法

// CreateURLRule 创建URL重写规则
func (s *DomainBindingService) CreateURLRule(domainBindingID uint, ruleName, pattern, replacement string, priority int) (*model.URLRewriteRule, error) {
	// 验证域名绑定存在
	binding, err := s.repo.GetByID(domainBindingID)
	if err != nil {
		return nil, fmt.Errorf("failed to get domain binding: %w", err)
	}
	if binding == nil {
		return nil, fmt.Errorf("domain binding not found")
	}

	rule := &model.URLRewriteRule{
		DomainBindingID: domainBindingID,
		RuleName:        ruleName,
		Pattern:         pattern,
		Replacement:     replacement,
		Priority:        priority,
		IsActive:        true,
	}

	if err := s.repo.CreateURLRule(rule); err != nil {
		return nil, fmt.Errorf("failed to create URL rule: %w", err)
	}

	// 发布URL规则创建事件
	s.publishURLRuleEvent("url_rule.created", rule)

	s.logger.Info("URL rule created",
		zap.Uint("domain_binding_id", domainBindingID),
		zap.String("rule_name", ruleName),
		zap.String("pattern", pattern),
		zap.String("replacement", replacement),
	)

	return rule, nil
}

// UpdateURLRule 更新URL重写规则
func (s *DomainBindingService) UpdateURLRule(ruleID uint, ruleName, pattern, replacement string, priority int, isActive bool) (*model.URLRewriteRule, error) {
	rule, err := s.repo.GetURLRuleByID(ruleID)
	if err != nil {
		return nil, fmt.Errorf("failed to get URL rule: %w", err)
	}
	if rule == nil {
		return nil, fmt.Errorf("URL rule not found")
	}

	rule.RuleName = ruleName
	rule.Pattern = pattern
	rule.Replacement = replacement
	rule.Priority = priority
	rule.IsActive = isActive

	if err := s.repo.UpdateURLRule(rule); err != nil {
		return nil, fmt.Errorf("failed to update URL rule: %w", err)
	}

	// 发布URL规则更新事件
	s.publishURLRuleEvent("url_rule.updated", rule)

	s.logger.Info("URL rule updated",
		zap.Uint("rule_id", ruleID),
		zap.String("rule_name", ruleName),
	)

	return rule, nil
}

// DeleteURLRule 删除URL重写规则
func (s *DomainBindingService) DeleteURLRule(ruleID uint) error {
	rule, err := s.repo.GetURLRuleByID(ruleID)
	if err != nil {
		return fmt.Errorf("failed to get URL rule: %w", err)
	}
	if rule == nil {
		return fmt.Errorf("URL rule not found")
	}

	if err := s.repo.DeleteURLRule(ruleID); err != nil {
		return fmt.Errorf("failed to delete URL rule: %w", err)
	}

	// 发布URL规则删除事件
	s.publishURLRuleEvent("url_rule.deleted", rule)

	s.logger.Info("URL rule deleted",
		zap.Uint("rule_id", ruleID),
		zap.String("rule_name", rule.RuleName),
	)

	return nil
}

// GetURLRulesByDomainBinding 获取域名绑定的URL重写规则
func (s *DomainBindingService) GetURLRulesByDomainBinding(domainBindingID uint) ([]*model.URLRewriteRule, error) {
	return s.repo.GetURLRulesByDomainBinding(domainBindingID)
}

// GetURLRuleByID 根据ID获取URL重写规则
func (s *DomainBindingService) GetURLRuleByID(ruleID uint) (*model.URLRewriteRule, error) {
	return s.repo.GetURLRuleByID(ruleID)
}

// publishURLRuleEvent 发布URL规则事件
func (s *DomainBindingService) publishURLRuleEvent(eventType string, rule *model.URLRewriteRule) {
	eventData := map[string]interface{}{
		"rule_id":            rule.ID,
		"domain_binding_id":  rule.DomainBindingID,
		"rule_name":          rule.RuleName,
		"pattern":            rule.Pattern,
		"replacement":        rule.Replacement,
		"priority":           rule.Priority,
		"is_active":          rule.IsActive,
	}

	event := s.eventMgr.CreateEvent(nil, eventType, eventData)
	if err := s.eventMgr.PublishEvent(event); err != nil {
		s.logger.Error("Failed to publish URL rule event",
			zap.String("event_type", eventType),
			zap.Uint("rule_id", rule.ID),
			zap.Error(err),
		)
	}
}

// publishBindingEvent 发布域名绑定事件
func (s *DomainBindingService) publishBindingEvent(eventType string, binding *model.DomainBinding) {
	eventData := map[string]interface{}{
		"binding_id":   binding.ID,
		"domain":       binding.Domain,
		"site_id":      binding.SiteID,
		"binding_type": string(binding.BindingType),
	}

	if binding.ModuleSlug != nil {
		eventData["module_slug"] = *binding.ModuleSlug
	}
	if binding.CategoryID != nil {
		eventData["category_id"] = *binding.CategoryID
	}

	event := s.eventMgr.CreateEvent(nil, eventType, eventData)
	if err := s.eventMgr.PublishEvent(event); err != nil {
		s.logger.Error("Failed to publish binding event",
			zap.String("event_type", eventType),
			zap.Uint("binding_id", binding.ID),
			zap.Error(err),
		)
	}
}
