# RBAC权限中间件使用指南

本文档详细说明了GACMS中RBAC权限系统的权限中间件使用方法，包括中间件的设置、权限检查方式以及最佳实践。

## 目录

- [简介](#简介)
- [中间件配置](#中间件配置)
- [基本用法](#基本用法)
- [高级用法](#高级用法)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)

## 简介

GACMS采用基于角色的访问控制（RBAC）模型，通过权限中间件实现对API端点的权限检查。该中间件可以应用于任何Gin路由，确保只有具有特定权限的用户才能访问特定的功能。

权限控制基于以下核心概念：

- **用户类型**：区分`AdminUser`和`MemberUser`，实现前后台用户的物理隔离
- **权限**：以`.`分隔的字符串标识，例如`content.create`，`user.delete`等
- **角色**：一组权限的集合，用户通过被分配角色获得相应权限
- **站点隔离**：通过`site_id`实现多站点/多租户的权限隔离

## 中间件配置

### 依赖注入设置

在你的模块初始化或应用启动时，需要正确配置权限中间件的依赖：

```go
// 在wire.go或依赖注入配置中
func InitPermissionMiddleware(
    permissionService service.IPermissionService, 
    sessionHandler ISessionHandler,
) *middleware.PermissionMiddleware {
    return middleware.NewPermissionMiddleware(permissionService, sessionHandler)
}
```

### 路由器集成

在路由设置中集成权限中间件：

```go
// 在routes.go或相关路由配置文件中
func SetupRoutes(
    router *gin.Engine, 
    permMiddleware *middleware.PermissionMiddleware,
    // ...其他依赖
) {
    api := router.Group("/api")
    
    // 应用权限中间件到特定路由组
    contentApi := api.Group("/content")
    {
        // 获取内容列表仅需要查看权限
        contentApi.GET("", permMiddleware.RequirePermission("content.view"), contentController.List)
        
        // 创建内容需要创建权限
        contentApi.POST("", permMiddleware.RequirePermission("content.create"), contentController.Create)
        
        // 编辑内容需要编辑权限
        contentApi.PUT("/:id", permMiddleware.RequirePermission("content.edit"), contentController.Update)
        
        // 删除内容需要删除权限
        contentApi.DELETE("/:id", permMiddleware.RequirePermission("content.delete"), contentController.Delete)
    }
}
```

## 基本用法

### 单一权限检查

最基本的用法是检查用户是否具有单一特定权限：

```go
// 要求用户具有查看文章的权限
router.GET("/posts", permMiddleware.RequirePermission("post.view"), postController.List)
```

### 多权限检查

你也可以要求用户同时具有多个权限：

```go
// 要求用户同时具有发布和审核权限
router.POST("/posts/publish", 
    permMiddleware.RequirePermissions([]string{"post.publish", "post.approve"}), 
    postController.Publish)
```

### 权限检查与路由参数

中间件支持基于路由参数的权限检查：

```go
// 检查用户是否有权限管理特定分类下的文章
router.GET("/categories/:categoryID/posts", 
    permMiddleware.RequirePermissionWithParam("category.manage", "categoryID"), 
    categoryController.ListPosts)
```

## 高级用法

### 条件权限检查

某些情况下，你可能需要根据请求内容动态决定所需权限：

```go
// 自定义权限检查处理函数
router.PUT("/posts/:id", func(c *gin.Context) {
    // 获取请求数据
    var postData PostUpdateData
    if err := c.ShouldBindJSON(&postData); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        c.Abort()
        return
    }
    
    // 根据操作类型确定所需权限
    var requiredPermission string
    if postData.Status == "published" {
        requiredPermission = "post.publish"
    } else {
        requiredPermission = "post.edit"
    }
    
    // 动态应用权限中间件
    permMiddleware.RequirePermission(requiredPermission)(c)
    
    // 如果中间件没有中止请求，继续处理
    if !c.IsAborted() {
        postController.Update(c)
    }
})
```

### 跳过特定条件下的权限检查

在某些特殊情况下，你可能需要绕过权限检查：

```go
// 公开API端点，但管理员仍然需要权限验证
router.GET("/public-data", func(c *gin.Context) {
    // 检查是否为公开访问
    isPublic := c.Query("access") == "public"
    
    if isPublic {
        // 公开访问直接通过
        c.Next()
    } else {
        // 非公开访问需要权限验证
        permMiddleware.RequirePermission("data.access")(c)
    }
    
    // 如果请求未被中止，调用处理函数
    if !c.IsAborted() {
        dataController.GetPublicData(c)
    }
})
```

## 最佳实践

### 权限命名约定

为确保权限管理的一致性和可维护性，建议遵循以下命名约定：

1. 使用小写字母和点号分隔
2. 格式为：`资源.操作`，例如`user.create`、`post.delete`
3. 资源名使用单数形式，与模块名一致
4. 通用操作使用：`create`、`read`、`update`、`delete`、`list`、`manage`等

常见权限命名示例：

| 权限名 | 描述 |
|-------|------|
| `user.create` | 创建用户 |
| `user.update` | 更新用户信息 |
| `user.delete` | 删除用户 |
| `user.view` | 查看用户信息 |
| `user.list` | 列出用户 |
| `post.publish` | 发布文章 |
| `post.approve` | 审核文章 |
| `site.manage` | 管理站点设置 |

### 权限粒度控制

权限粒度过细会导致管理复杂，过粗则无法满足精细控制需求。建议：

1. 根据业务场景合理划分权限粒度
2. 对于简单模块，可使用`模块.manage`这样的整体权限
3. 对于复杂模块，细分为`模块.create`、`模块.update`等具体操作权限
4. 对于特殊操作，可设置单独的权限，如`post.publish`、`post.feature`

### 性能优化

权限检查可能影响API性能，特别是在高并发场景下：

1. 合理使用缓存减少数据库查询
2. 在用户登录时预加载其所有权限
3. 考虑实现权限结果缓存机制，避免短时间内重复验证相同权限
4. 在非关键路径上可考虑异步权限检查

## 故障排除

### 常见问题

1. **权限验证总是失败**
   - 检查用户是否已正确分配角色
   - 确认角色是否包含所需权限
   - 验证站点ID是否正确匹配

2. **中间件不生效**
   - 确认中间件注册顺序正确
   - 检查路由配置是否正确应用了中间件
   - 验证依赖注入配置是否正确

3. **权限验证性能问题**
   - 考虑实现权限缓存
   - 优化数据库查询
   - 检查是否有不必要的重复权限检查

### 调试技巧

在开发环境中，可以启用权限中间件的调试模式：

```go
// 在开发环境配置中
if env.IsDevelopment() {
    permMiddleware.EnableDebug(true)
}
```

这将在日志中记录详细的权限检查过程，帮助你诊断问题。

---

## 附录：完整API参考

### PermissionMiddleware方法

| 方法 | 描述 |
|-----|------|
| `RequirePermission(permission string) gin.HandlerFunc` | 检查用户是否具有指定权限 |
| `RequirePermissions(permissions []string) gin.HandlerFunc` | 检查用户是否同时具有多个权限 |
| `RequirePermissionWithParam(permission, paramName string) gin.HandlerFunc` | 检查用户是否有权限操作特定参数标识的资源 |
| `RequireAnyPermission(permissions []string) gin.HandlerFunc` | 检查用户是否至少具有一个指定权限 |
| `EnableDebug(enable bool)` | 启用或禁用调试模式 | 