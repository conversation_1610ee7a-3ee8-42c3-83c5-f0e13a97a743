<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 流量分析</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .content-section-title::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 5rem;
            height: 3px;
            background: linear-gradient(to right, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            background: linear-gradient(145deg, #1e1e1e, #2a2a2a);
            border-left: 4px solid #007bff;
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            backdrop-filter: blur(20px);
            z-index: 1000;
            transform: translateX(120%);
            opacity: 0;
            transition: all 0.2s ease;
            max-width: 350px;
        }
        
        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }
        
        /* 图表容器样式 */
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
        
        /* 数据卡片渐变效果 */
        .stat-card-1 {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        }
        
        .stat-card-2 {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        .stat-card-3 {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }
        
        .stat-card-4 {
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
        }
        
        /* 热力图样式 */
        .heatmap-cell {
            transition: all 0.3s ease;
        }
        
        .heatmap-cell:hover {
            transform: scale(1.05);
            z-index: 10;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm text-gray-400 mb-4">
                <a href="dashboard.html" class="hover:text-blue-400">首页</a>
                <span class="mx-2">/</span>
                <a href="#" class="hover:text-blue-400">数据报告</a>
                <span class="mx-2">/</span>
                <span class="text-gray-200">流量分析</span>
            </div>
            
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">流量分析</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <div class="relative">
                            <select class="bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none pr-10">
                                <option value="7">最近7天</option>
                                <option value="30" selected>最近30天</option>
                                <option value="90">最近90天</option>
                                <option value="365">最近一年</option>
                            </select>
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none text-gray-400">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                        <a href="#" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-2 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <i class="fas fa-file-export mr-2"></i>
                                导出报表
                            </span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 数据卡片区域 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- 页面浏览量卡片 -->
                <div class="stat-card-1 rounded-xl p-6 text-white shadow-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold">页面浏览量</h3>
                        <div class="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                            <i class="fas fa-eye text-white"></i>
                        </div>
                    </div>
                    <div class="text-3xl font-bold mb-1">245,678</div>
                    <div class="flex items-center text-sm">
                        <span class="flex items-center text-green-300 mr-2">
                            <i class="fas fa-arrow-up mr-1"></i>15.2%
                        </span>
                        <span class="text-white/70">较上月</span>
                    </div>
                </div>
                
                <!-- 独立访客卡片 -->
                <div class="stat-card-2 rounded-xl p-6 text-white shadow-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold">独立访客</h3>
                        <div class="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                            <i class="fas fa-user-friends text-white"></i>
                        </div>
                    </div>
                    <div class="text-3xl font-bold mb-1">87,342</div>
                    <div class="flex items-center text-sm">
                        <span class="flex items-center text-green-300 mr-2">
                            <i class="fas fa-arrow-up mr-1"></i>9.7%
                        </span>
                        <span class="text-white/70">较上月</span>
                    </div>
                </div>
                
                <!-- 平均停留时间卡片 -->
                <div class="stat-card-3 rounded-xl p-6 text-white shadow-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold">平均停留时间</h3>
                        <div class="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                            <i class="fas fa-clock text-white"></i>
                        </div>
                    </div>
                    <div class="text-3xl font-bold mb-1">3分42秒</div>
                    <div class="flex items-center text-sm">
                        <span class="flex items-center text-green-300 mr-2">
                            <i class="fas fa-arrow-up mr-1"></i>12.3%
                        </span>
                        <span class="text-white/70">较上月</span>
                    </div>
                </div>
                
                <!-- 跳出率卡片 -->
                <div class="stat-card-4 rounded-xl p-6 text-white shadow-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold">跳出率</h3>
                        <div class="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                            <i class="fas fa-sign-out-alt text-white"></i>
                        </div>
                    </div>
                    <div class="text-3xl font-bold mb-1">28.5%</div>
                    <div class="flex items-center text-sm">
                        <span class="flex items-center text-red-300 mr-2">
                            <i class="fas fa-arrow-down mr-1"></i>2.1%
                        </span>
                        <span class="text-white/70">较上月</span>
                    </div>
                </div>
            </div>

            <!-- 流量趋势图 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-white">流量趋势</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 rounded-lg bg-blue-500/20 text-blue-400 border border-blue-500/30 text-sm">日</button>
                        <button class="px-3 py-1 rounded-lg bg-gray-700/50 text-gray-300 border border-gray-600 text-sm">周</button>
                        <button class="px-3 py-1 rounded-lg bg-gray-700/50 text-gray-300 border border-gray-600 text-sm">月</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="trafficTrendChart"></canvas>
                </div>
            </div>

            <!-- 流量分析区域 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- 流量来源分析 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <h3 class="text-lg font-bold text-white mb-4">流量来源分析</h3>
                    <div class="chart-container" style="height: 250px;">
                        <canvas id="trafficSourceChart"></canvas>
                    </div>
                    <div class="mt-4 grid grid-cols-2 sm:grid-cols-3 gap-4">
                        <div class="bg-gray-800/20 rounded-lg p-3 text-center">
                            <div class="text-blue-400 text-lg font-bold">45%</div>
                            <div class="text-gray-400 text-xs">搜索引擎</div>
                        </div>
                        <div class="bg-gray-800/20 rounded-lg p-3 text-center">
                            <div class="text-green-400 text-lg font-bold">25%</div>
                            <div class="text-gray-400 text-xs">直接访问</div>
                        </div>
                        <div class="bg-gray-800/20 rounded-lg p-3 text-center">
                            <div class="text-yellow-400 text-lg font-bold">15%</div>
                            <div class="text-gray-400 text-xs">社交媒体</div>
                        </div>
                        <div class="bg-gray-800/20 rounded-lg p-3 text-center">
                            <div class="text-purple-400 text-lg font-bold">10%</div>
                            <div class="text-gray-400 text-xs">外部链接</div>
                        </div>
                        <div class="bg-gray-800/20 rounded-lg p-3 text-center">
                            <div class="text-pink-400 text-lg font-bold">5%</div>
                            <div class="text-gray-400 text-xs">邮件营销</div>
                        </div>
                    </div>
                </div>
                
                <!-- 地域分布 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <h3 class="text-lg font-bold text-white mb-4">地域分布</h3>
                    <div class="chart-container" style="height: 250px;">
                        <canvas id="regionChart"></canvas>
                    </div>
                    <div class="mt-4 overflow-x-auto">
                        <table class="w-full text-left">
                            <thead>
                                <tr class="border-b border-gray-700">
                                    <th class="py-2 px-3 text-gray-400 font-medium text-sm">地区</th>
                                    <th class="py-2 px-3 text-gray-400 font-medium text-sm">访问量</th>
                                    <th class="py-2 px-3 text-gray-400 font-medium text-sm">占比</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b border-gray-700/50">
                                    <td class="py-2 px-3">北京</td>
                                    <td class="py-2 px-3">42,356</td>
                                    <td class="py-2 px-3">18.2%</td>
                                </tr>
                                <tr class="border-b border-gray-700/50">
                                    <td class="py-2 px-3">上海</td>
                                    <td class="py-2 px-3">35,782</td>
                                    <td class="py-2 px-3">15.4%</td>
                                </tr>
                                <tr class="border-b border-gray-700/50">
                                    <td class="py-2 px-3">广州</td>
                                    <td class="py-2 px-3">28,945</td>
                                    <td class="py-2 px-3">12.5%</td>
                                </tr>
                                <tr class="border-b border-gray-700/50">
                                    <td class="py-2 px-3">深圳</td>
                                    <td class="py-2 px-3">25,673</td>
                                    <td class="py-2 px-3">11.1%</td>
                                </tr>
                                <tr>
                                    <td class="py-2 px-3">其他</td>
                                    <td class="py-2 px-3">98,922</td>
                                    <td class="py-2 px-3">42.8%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 访问热力图 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold text-white mb-4">访问热力图</h3>
                <div class="overflow-x-auto">
                    <div class="min-w-[800px]">
                        <div class="flex mb-2">
                            <div class="w-20"></div>
                            <div class="flex-1 grid grid-cols-7 gap-1 text-center">
                                <div class="text-xs text-gray-400">周一</div>
                                <div class="text-xs text-gray-400">周二</div>
                                <div class="text-xs text-gray-400">周三</div>
                                <div class="text-xs text-gray-400">周四</div>
                                <div class="text-xs text-gray-400">周五</div>
                                <div class="text-xs text-gray-400">周六</div>
                                <div class="text-xs text-gray-400">周日</div>
                            </div>
                        </div>
                        <div class="flex items-center mb-1">
                            <div class="w-20 text-right pr-2 text-xs text-gray-400">00:00-04:00</div>
                            <div class="flex-1 grid grid-cols-7 gap-1">
                                <div class="heatmap-cell h-10 rounded bg-blue-500/20" style="--value: 20"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/10" style="--value: 10"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/10" style="--value: 10"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/20" style="--value: 20"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/30" style="--value: 30"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/50" style="--value: 50"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/40" style="--value: 40"></div>
                            </div>
                        </div>
                        <div class="flex items-center mb-1">
                            <div class="w-20 text-right pr-2 text-xs text-gray-400">04:00-08:00</div>
                            <div class="flex-1 grid grid-cols-7 gap-1">
                                <div class="heatmap-cell h-10 rounded bg-blue-500/10" style="--value: 10"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/20" style="--value: 20"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/20" style="--value: 20"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/10" style="--value: 10"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/20" style="--value: 20"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/30" style="--value: 30"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/30" style="--value: 30"></div>
                            </div>
                        </div>
                        <div class="flex items-center mb-1">
                            <div class="w-20 text-right pr-2 text-xs text-gray-400">08:00-12:00</div>
                            <div class="flex-1 grid grid-cols-7 gap-1">
                                <div class="heatmap-cell h-10 rounded bg-blue-500/60" style="--value: 60"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/70" style="--value: 70"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/80" style="--value: 80"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/70" style="--value: 70"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/60" style="--value: 60"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/40" style="--value: 40"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/30" style="--value: 30"></div>
                            </div>
                        </div>
                        <div class="flex items-center mb-1">
                            <div class="w-20 text-right pr-2 text-xs text-gray-400">12:00-16:00</div>
                            <div class="flex-1 grid grid-cols-7 gap-1">
                                <div class="heatmap-cell h-10 rounded bg-blue-500/80" style="--value: 80"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/90" style="--value: 90"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/100" style="--value: 100"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/90" style="--value: 90"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/80" style="--value: 80"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/50" style="--value: 50"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/40" style="--value: 40"></div>
                            </div>
                        </div>
                        <div class="flex items-center mb-1">
                            <div class="w-20 text-right pr-2 text-xs text-gray-400">16:00-20:00</div>
                            <div class="flex-1 grid grid-cols-7 gap-1">
                                <div class="heatmap-cell h-10 rounded bg-blue-500/70" style="--value: 70"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/80" style="--value: 80"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/90" style="--value: 90"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/80" style="--value: 80"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/90" style="--value: 90"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/60" style="--value: 60"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/50" style="--value: 50"></div>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-20 text-right pr-2 text-xs text-gray-400">20:00-24:00</div>
                            <div class="flex-1 grid grid-cols-7 gap-1">
                                <div class="heatmap-cell h-10 rounded bg-blue-500/60" style="--value: 60"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/50" style="--value: 50"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/60" style="--value: 60"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/70" style="--value: 70"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/80" style="--value: 80"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/90" style="--value: 90"></div>
                                <div class="heatmap-cell h-10 rounded bg-blue-500/70" style="--value: 70"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-center mt-4">
                    <div class="flex items-center">
                        <div class="w-3 h-3 rounded-full bg-blue-500/10 mr-1"></div>
                        <span class="text-xs text-gray-400 mr-3">低</span>
                        <div class="w-3 h-3 rounded-full bg-blue-500/30 mr-1"></div>
                        <span class="text-xs text-gray-400 mr-3">中低</span>
                        <div class="w-3 h-3 rounded-full bg-blue-500/50 mr-1"></div>
                        <span class="text-xs text-gray-400 mr-3">中</span>
                        <div class="w-3 h-3 rounded-full bg-blue-500/70 mr-1"></div>
                        <span class="text-xs text-gray-400 mr-3">中高</span>
                        <div class="w-3 h-3 rounded-full bg-blue-500/90 mr-1"></div>
                        <span class="text-xs text-gray-400">高</span>
                    </div>
                </div>
            </div>

            <!-- 热门入口页面 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold text-white mb-4">热门入口页面</h3>
                <div class="overflow-x-auto">
                    <table class="w-full text-left">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="py-3 px-4 text-gray-400 font-medium">页面URL</th>
                                <th class="py-3 px-4 text-gray-400 font-medium">访问量</th>
                                <th class="py-3 px-4 text-gray-400 font-medium">跳出率</th>
                                <th class="py-3 px-4 text-gray-400 font-medium">平均停留时间</th>
                                <th class="py-3 px-4 text-gray-400 font-medium">转化率</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border-b border-gray-700/50 hover:bg-gray-700/20">
                                <td class="py-3 px-4">
                                    <a href="#" class="text-blue-400 hover:text-blue-300">/</a>
                                </td>
                                <td class="py-3 px-4">78,452</td>
                                <td class="py-3 px-4">32.5%</td>
                                <td class="py-3 px-4">2分45秒</td>
                                <td class="py-3 px-4">
                                    <span class="text-green-400">4.2%</span>
                                </td>
                            </tr>
                            <tr class="border-b border-gray-700/50 hover:bg-gray-700/20">
                                <td class="py-3 px-4">
                                    <a href="#" class="text-blue-400 hover:text-blue-300">/products</a>
                                </td>
                                <td class="py-3 px-4">45,678</td>
                                <td class="py-3 px-4">25.8%</td>
                                <td class="py-3 px-4">3分12秒</td>
                                <td class="py-3 px-4">
                                    <span class="text-green-400">5.7%</span>
                                </td>
                            </tr>
                            <tr class="border-b border-gray-700/50 hover:bg-gray-700/20">
                                <td class="py-3 px-4">
                                    <a href="#" class="text-blue-400 hover:text-blue-300">/blog</a>
                                </td>
                                <td class="py-3 px-4">32,456</td>
                                <td class="py-3 px-4">28.3%</td>
                                <td class="py-3 px-4">4分05秒</td>
                                <td class="py-3 px-4">
                                    <span class="text-green-400">3.8%</span>
                                </td>
                            </tr>
                            <tr class="border-b border-gray-700/50 hover:bg-gray-700/20">
                                <td class="py-3 px-4">
                                    <a href="#" class="text-blue-400 hover:text-blue-300">/about</a>
                                </td>
                                <td class="py-3 px-4">18,975</td>
                                <td class="py-3 px-4">35.2%</td>
                                <td class="py-3 px-4">2分18秒</td>
                                <td class="py-3 px-4">
                                    <span class="text-green-400">2.5%</span>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-700/20">
                                <td class="py-3 px-4">
                                    <a href="#" class="text-blue-400 hover:text-blue-300">/contact</a>
                                </td>
                                <td class="py-3 px-4">15,324</td>
                                <td class="py-3 px-4">22.7%</td>
                                <td class="py-3 px-4">3分35秒</td>
                                <td class="py-3 px-4">
                                    <span class="text-green-400">6.3%</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="mt-4 text-right">
                    <a href="#" class="text-blue-400 hover:text-blue-300 inline-flex items-center">
                        查看更多 <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <!-- 通知组件 -->
    <div class="notification" id="notification">
        <div class="mr-3">
            <i class="fas fa-info-circle text-blue-400 text-xl"></i>
        </div>
        <div class="flex-1">
            <h4 class="text-white text-sm font-bold">数据已更新</h4>
            <p class="text-gray-300 text-xs">最新的流量分析数据已加载完成。</p>
        </div>
    </div>

    <!-- 加载必要的JS -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initCharts();
            
            // 显示通知
            setTimeout(() => {
                document.getElementById('notification').classList.add('show');
                setTimeout(() => {
                    document.getElementById('notification').classList.remove('show');
                }, 3000);
            }, 1000);
            
            // 热力图单元格效果
            const heatmapCells = document.querySelectorAll('.heatmap-cell');
            heatmapCells.forEach(cell => {
                const value = cell.getAttribute('style').match(/--value:\s*(\d+)/)[1];
                cell.style.backgroundColor = `rgba(59, 130, 246, ${value / 100})`;
                
                // 添加提示信息
                cell.setAttribute('title', `访问量: ${value}%`);
            });
        });
        
        /**
         * @function initCharts
         * @description 初始化所有图表
         */
        function initCharts() {
            // 设置Chart.js全局配置
            Chart.defaults.color = '#a0a0a0';
            Chart.defaults.borderColor = 'rgba(107, 114, 128, 0.3)';
            
            // 流量趋势图
            const trafficTrendCtx = document.getElementById('trafficTrendChart').getContext('2d');
            new Chart(trafficTrendCtx, {
                type: 'line',
                data: {
                    labels: ['5/1', '5/2', '5/3', '5/4', '5/5', '5/6', '5/7', '5/8', '5/9', '5/10', '5/11', '5/12', '5/13', '5/14'],
                    datasets: [
                        {
                            label: '页面浏览量',
                            data: [8500, 9200, 8700, 9500, 10200, 11500, 10800, 9700, 10500, 11200, 12000, 11500, 10800, 11700],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '独立访客',
                            data: [3200, 3500, 3300, 3600, 3900, 4200, 4000, 3700, 3900, 4100, 4300, 4200, 4000, 4300],
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                boxWidth: 12,
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(107, 114, 128, 0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    }
                }
            });
            
            // 流量来源分析饼图
            const trafficSourceCtx = document.getElementById('trafficSourceChart').getContext('2d');
            new Chart(trafficSourceCtx, {
                type: 'doughnut',
                data: {
                    labels: ['搜索引擎', '直接访问', '社交媒体', '外部链接', '邮件营销'],
                    datasets: [{
                        data: [45, 25, 15, 10, 5],
                        backgroundColor: [
                            '#3b82f6',
                            '#10b981',
                            '#f59e0b',
                            '#6366f1',
                            '#ec4899'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 12,
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        }
                    },
                    cutout: '70%'
                }
            });
            
            // 地域分布饼图
            const regionCtx = document.getElementById('regionChart').getContext('2d');
            new Chart(regionCtx, {
                type: 'doughnut',
                data: {
                    labels: ['北京', '上海', '广州', '深圳', '其他'],
                    datasets: [{
                        data: [18.2, 15.4, 12.5, 11.1, 42.8],
                        backgroundColor: [
                            '#3b82f6',
                            '#10b981',
                            '#f59e0b',
                            '#6366f1',
                            '#64748b'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 12,
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        }
                    },
                    cutout: '70%'
                }
            });
        }
    </script>
</body>
</html>