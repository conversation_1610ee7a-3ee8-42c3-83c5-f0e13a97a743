<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 区块管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .content-section-title::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 5rem;
            height: 3px;
            background: linear-gradient(to right, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .user-link::after, .article-link::after, .version-link::after, .plugin-link::after, .comment-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 1px;
            background: linear-gradient(to right, #007bff, #00c6ff);
            transition: width 0.2s ease;
        }
        
        .user-link:hover, .article-link:hover, .version-link:hover, .plugin-link:hover, .comment-link:hover {
            color: #00c6ff;
        }
        
        .user-link:hover::after, .article-link:hover::after, .version-link:hover::after, .plugin-link:hover::after, .comment-link:hover::after {
            width: 100%;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            background: linear-gradient(145deg, #1e1e1e, #2a2a2a);
            border-left: 4px solid #007bff;
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            backdrop-filter: blur(20px);
            z-index: 1000;
            transform: translateX(120%);
            opacity: 0;
            transition: all 0.2s ease;
            max-width: 350px;
        }
        
        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">区块管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <a href="#" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-th-large text-white"></i>
                                </span>
                                创建新区块
                            </span>
                        </a>
                        <a href="#" class="flex items-center justify-center bg-gradient-to-r from-green-500 to-green-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-green-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-upload text-white"></i>
                                </span>
                                导入区块
                            </span>
                        </a>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex flex-wrap gap-4">
                        <div class="relative w-full md:w-96">
                            <input type="text" placeholder="搜索区块..." 
                                class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent pl-10">
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                        
                        <!-- 区块类型筛选 -->
                        <div class="flex gap-2">
                            <button class="px-4 py-2 rounded bg-blue-500 text-white text-sm">所有区块</button>
                            <button class="px-4 py-2 rounded bg-gray-700 hover:bg-gray-600 text-white text-sm">内容区块</button>
                            <button class="px-4 py-2 rounded bg-gray-700 hover:bg-gray-600 text-white text-sm">功能区块</button>
                            <button class="px-4 py-2 rounded bg-gray-700 hover:bg-gray-600 text-white text-sm">结构区块</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 区块类别分组 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold text-white mb-4">内容区块</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    <!-- 区块项 1 -->
                    <div class="border border-gray-700 rounded-lg overflow-hidden bg-gray-800/20 hover:bg-gray-800/30 transition-all p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium text-blue-400">轮播图区块</h4>
                            <span class="bg-green-500/20 text-green-400 px-2 py-0.5 rounded-full text-xs font-medium">
                                内容
                            </span>
                        </div>
                        <p class="text-sm text-gray-400 mb-3 line-clamp-2">展示多图轮播内容，支持自动切换和手动控制</p>
                        <div class="w-full h-24 bg-gradient-to-r from-blue-900/30 to-purple-900/30 rounded mb-3 flex items-center justify-center">
                            <i class="fas fa-images text-2xl text-blue-400"></i>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="text-xs text-gray-500">使用次数: 5</div>
                            <div class="flex space-x-2">
                                <button class="text-blue-400 hover:text-blue-300">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-green-400 hover:text-green-300">
                                    <i class="fas fa-clone"></i>
                                </button>
                                <button class="text-gray-400 hover:text-gray-300">
                                    <i class="fas fa-cog"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 区块项 2 -->
                    <div class="border border-gray-700 rounded-lg overflow-hidden bg-gray-800/20 hover:bg-gray-800/30 transition-all p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium text-blue-400">文章列表区块</h4>
                            <span class="bg-green-500/20 text-green-400 px-2 py-0.5 rounded-full text-xs font-medium">
                                内容
                            </span>
                        </div>
                        <p class="text-sm text-gray-400 mb-3 line-clamp-2">展示文章列表，支持多种样式和分页</p>
                        <div class="w-full h-24 bg-gradient-to-r from-blue-900/30 to-purple-900/30 rounded mb-3 flex items-center justify-center">
                            <i class="fas fa-list-alt text-2xl text-blue-400"></i>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="text-xs text-gray-500">使用次数: 12</div>
                            <div class="flex space-x-2">
                                <button class="text-blue-400 hover:text-blue-300">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-green-400 hover:text-green-300">
                                    <i class="fas fa-clone"></i>
                                </button>
                                <button class="text-gray-400 hover:text-gray-300">
                                    <i class="fas fa-cog"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 区块项 3 -->
                    <div class="border border-gray-700 rounded-lg overflow-hidden bg-gray-800/20 hover:bg-gray-800/30 transition-all p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium text-blue-400">产品展示区块</h4>
                            <span class="bg-green-500/20 text-green-400 px-2 py-0.5 rounded-full text-xs font-medium">
                                内容
                            </span>
                        </div>
                        <p class="text-sm text-gray-400 mb-3 line-clamp-2">展示产品信息，支持网格和列表两种布局</p>
                        <div class="w-full h-24 bg-gradient-to-r from-blue-900/30 to-purple-900/30 rounded mb-3 flex items-center justify-center">
                            <i class="fas fa-box text-2xl text-blue-400"></i>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="text-xs text-gray-500">使用次数: 8</div>
                            <div class="flex space-x-2">
                                <button class="text-blue-400 hover:text-blue-300">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-green-400 hover:text-green-300">
                                    <i class="fas fa-clone"></i>
                                </button>
                                <button class="text-gray-400 hover:text-gray-300">
                                    <i class="fas fa-cog"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 功能区块 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold text-white mb-4">功能区块</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    <!-- 区块项 1 -->
                    <div class="border border-gray-700 rounded-lg overflow-hidden bg-gray-800/20 hover:bg-gray-800/30 transition-all p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium text-purple-400">搜索框</h4>
                            <span class="bg-purple-500/20 text-purple-400 px-2 py-0.5 rounded-full text-xs font-medium">
                                功能
                            </span>
                        </div>
                        <p class="text-sm text-gray-400 mb-3 line-clamp-2">提供站内搜索功能，支持自动完成</p>
                        <div class="w-full h-24 bg-gradient-to-r from-purple-900/30 to-pink-900/30 rounded mb-3 flex items-center justify-center">
                            <i class="fas fa-search text-2xl text-purple-400"></i>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="text-xs text-gray-500">使用次数: 2</div>
                            <div class="flex space-x-2">
                                <button class="text-blue-400 hover:text-blue-300">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-green-400 hover:text-green-300">
                                    <i class="fas fa-clone"></i>
                                </button>
                                <button class="text-gray-400 hover:text-gray-300">
                                    <i class="fas fa-cog"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 区块项 2 -->
                    <div class="border border-gray-700 rounded-lg overflow-hidden bg-gray-800/20 hover:bg-gray-800/30 transition-all p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium text-purple-400">登录表单</h4>
                            <span class="bg-purple-500/20 text-purple-400 px-2 py-0.5 rounded-full text-xs font-medium">
                                功能
                            </span>
                        </div>
                        <p class="text-sm text-gray-400 mb-3 line-clamp-2">用户登录表单，支持多种验证方式</p>
                        <div class="w-full h-24 bg-gradient-to-r from-purple-900/30 to-pink-900/30 rounded mb-3 flex items-center justify-center">
                            <i class="fas fa-sign-in-alt text-2xl text-purple-400"></i>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="text-xs text-gray-500">使用次数: 1</div>
                            <div class="flex space-x-2">
                                <button class="text-blue-400 hover:text-blue-300">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-green-400 hover:text-green-300">
                                    <i class="fas fa-clone"></i>
                                </button>
                                <button class="text-gray-400 hover:text-gray-300">
                                    <i class="fas fa-cog"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 区块项 3 -->
                    <div class="border border-gray-700 rounded-lg overflow-hidden bg-gray-800/20 hover:bg-gray-800/30 transition-all p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium text-purple-400">评论区块</h4>
                            <span class="bg-purple-500/20 text-purple-400 px-2 py-0.5 rounded-full text-xs font-medium">
                                功能
                            </span>
                        </div>
                        <p class="text-sm text-gray-400 mb-3 line-clamp-2">文章评论功能，支持回复和点赞</p>
                        <div class="w-full h-24 bg-gradient-to-r from-purple-900/30 to-pink-900/30 rounded mb-3 flex items-center justify-center">
                            <i class="fas fa-comments text-2xl text-purple-400"></i>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="text-xs text-gray-500">使用次数: 4</div>
                            <div class="flex space-x-2">
                                <button class="text-blue-400 hover:text-blue-300">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-green-400 hover:text-green-300">
                                    <i class="fas fa-clone"></i>
                                </button>
                                <button class="text-gray-400 hover:text-gray-300">
                                    <i class="fas fa-cog"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 结构区块 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold text-white mb-4">结构区块</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    <!-- 区块项 1 -->
                    <div class="border border-gray-700 rounded-lg overflow-hidden bg-gray-800/20 hover:bg-gray-800/30 transition-all p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium text-yellow-400">页头区块</h4>
                            <span class="bg-yellow-500/20 text-yellow-400 px-2 py-0.5 rounded-full text-xs font-medium">
                                结构
                            </span>
                        </div>
                        <p class="text-sm text-gray-400 mb-3 line-clamp-2">网站顶部导航和标识区域</p>
                        <div class="w-full h-24 bg-gradient-to-r from-yellow-900/30 to-orange-900/30 rounded mb-3 flex items-center justify-center">
                            <i class="fas fa-heading text-2xl text-yellow-400"></i>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="text-xs text-gray-500">使用次数: 7</div>
                            <div class="flex space-x-2">
                                <button class="text-blue-400 hover:text-blue-300">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-green-400 hover:text-green-300">
                                    <i class="fas fa-clone"></i>
                                </button>
                                <button class="text-gray-400 hover:text-gray-300">
                                    <i class="fas fa-cog"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 区块项 2 -->
                    <div class="border border-gray-700 rounded-lg overflow-hidden bg-gray-800/20 hover:bg-gray-800/30 transition-all p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium text-yellow-400">侧边栏</h4>
                            <span class="bg-yellow-500/20 text-yellow-400 px-2 py-0.5 rounded-full text-xs font-medium">
                                结构
                            </span>
                        </div>
                        <p class="text-sm text-gray-400 mb-3 line-clamp-2">侧边辅助内容区域</p>
                        <div class="w-full h-24 bg-gradient-to-r from-yellow-900/30 to-orange-900/30 rounded mb-3 flex items-center justify-center">
                            <i class="fas fa-columns text-2xl text-yellow-400"></i>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="text-xs text-gray-500">使用次数: 6</div>
                            <div class="flex space-x-2">
                                <button class="text-blue-400 hover:text-blue-300">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-green-400 hover:text-green-300">
                                    <i class="fas fa-clone"></i>
                                </button>
                                <button class="text-gray-400 hover:text-gray-300">
                                    <i class="fas fa-cog"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 区块项 3 -->
                    <div class="border border-gray-700 rounded-lg overflow-hidden bg-gray-800/20 hover:bg-gray-800/30 transition-all p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium text-yellow-400">页脚区块</h4>
                            <span class="bg-yellow-500/20 text-yellow-400 px-2 py-0.5 rounded-full text-xs font-medium">
                                结构
                            </span>
                        </div>
                        <p class="text-sm text-gray-400 mb-3 line-clamp-2">网站底部信息和导航区域</p>
                        <div class="w-full h-24 bg-gradient-to-r from-yellow-900/30 to-orange-900/30 rounded mb-3 flex items-center justify-center">
                            <i class="fas fa-ellipsis-h text-2xl text-yellow-400"></i>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="text-xs text-gray-500">使用次数: 5</div>
                            <div class="flex space-x-2">
                                <button class="text-blue-400 hover:text-blue-300">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-green-400 hover:text-green-300">
                                    <i class="fas fa-clone"></i>
                                </button>
                                <button class="text-gray-400 hover:text-gray-300">
                                    <i class="fas fa-cog"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分页控件 -->
            <div class="flex items-center justify-center mt-6">
                <div class="flex">
                    <a href="#" class="px-3 py-1 rounded border border-gray-600 text-gray-400 mr-2 hover:bg-gray-700">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                    <a href="#" class="px-3 py-1 rounded border border-gray-600 text-white bg-blue-500 mr-2">1</a>
                    <a href="#" class="px-3 py-1 rounded border border-gray-600 text-gray-400 mr-2 hover:bg-gray-700">2</a>
                    <a href="#" class="px-3 py-1 rounded border border-gray-600 text-gray-400 mr-2 hover:bg-gray-700">3</a>
                    <a href="#" class="px-3 py-1 rounded border border-gray-600 text-gray-400 hover:bg-gray-700">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <!-- 通知组件 -->
    <div class="notification" id="notification">
        <div class="mr-3">
            <i class="fas fa-check-circle text-green-400 text-xl"></i>
        </div>
        <div class="flex-1">
            <h4 class="text-white text-sm font-bold">操作成功</h4>
            <p class="text-gray-300 text-xs">区块保存成功。</p>
        </div>
    </div>

    <!-- 加载必要的JS -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
</body>
</html> 