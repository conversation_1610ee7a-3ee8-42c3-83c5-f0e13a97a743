/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: pkg/contract/LicenseValidator.go
 * @Description: 许可证验证器接口，支持官方和第三方许可证验证
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"context"
	"time"
)

// LicenseInfo 许可证信息
type LicenseInfo struct {
	Key         string                 `json:"key"`
	Type        string                 `json:"type"`         // official/third_party/none
	ModuleName  string                 `json:"module_name"`
	IssuedTo    string                 `json:"issued_to"`
	IssuedAt    time.Time              `json:"issued_at"`
	ExpiresAt   *time.Time             `json:"expires_at,omitempty"`
	Edition     Edition                `json:"edition,omitempty"`     // 许可证版本
	Features    []string               `json:"features,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	IsValid     bool                   `json:"is_valid"`
	ErrorMsg    string                 `json:"error_msg,omitempty"`
}

// LicenseValidator 许可证验证器接口
type LicenseValidator interface {
	// ValidateLicense 验证许可证
	ValidateLicense(ctx context.Context, licenseKey, moduleName string) (*LicenseInfo, error)
	
	// GetValidatorName 获取验证器名称
	GetValidatorName() string
	
	// SupportedModules 获取支持的模块列表
	SupportedModules() []string
	
	// IsOnlineValidator 是否需要在线验证
	IsOnlineValidator() bool
}

// OfficialLicenseValidator 官方许可证验证器
type OfficialLicenseValidator interface {
	LicenseValidator
	
	// ActivateLicense 激活官方许可证
	ActivateLicense(ctx context.Context, licenseKey, moduleName string) error
	
	// DeactivateLicense 停用官方许可证
	DeactivateLicense(ctx context.Context, licenseKey, moduleName string) error
	
	// GetLicenseUsage 获取许可证使用情况
	GetLicenseUsage(ctx context.Context, licenseKey string) (*LicenseUsage, error)
}

// ThirdPartyLicenseValidator 第三方许可证验证器
type ThirdPartyLicenseValidator interface {
	LicenseValidator
	
	// RegisterModule 注册模块到验证器
	RegisterModule(moduleName string, config map[string]interface{}) error
	
	// UnregisterModule 从验证器注销模块
	UnregisterModule(moduleName string) error
	
	// GetValidatorConfig 获取验证器配置
	GetValidatorConfig() map[string]interface{}
}

// LicenseUsage 许可证使用情况
type LicenseUsage struct {
	LicenseKey    string    `json:"license_key"`
	ModuleName    string    `json:"module_name"`
	ActivatedAt   time.Time `json:"activated_at"`
	LastUsedAt    time.Time `json:"last_used_at"`
	UsageCount    int64     `json:"usage_count"`
	MaxUsage      int64     `json:"max_usage,omitempty"`
	IsActive      bool      `json:"is_active"`
}

// LicenseValidatorRegistry 许可证验证器注册表
type LicenseValidatorRegistry interface {
	// RegisterValidator 注册验证器
	RegisterValidator(validator LicenseValidator) error
	
	// GetValidator 获取验证器
	GetValidator(name string) (LicenseValidator, error)
	
	// GetOfficialValidator 获取官方验证器
	GetOfficialValidator() (OfficialLicenseValidator, error)
	
	// GetThirdPartyValidator 获取第三方验证器
	GetThirdPartyValidator(name string) (ThirdPartyLicenseValidator, error)
	
	// ListValidators 列出所有验证器
	ListValidators() []string
	
	// ValidateModuleLicense 验证模块许可证
	ValidateModuleLicense(ctx context.Context, moduleName, licenseKey, validatorName string) (*LicenseInfo, error)
}

// LicenseManager 许可证管理器
type LicenseManager interface {
	// ActivateModuleLicense 激活模块许可证
	ActivateModuleLicense(ctx context.Context, moduleName, licenseKey string) error

	// DeactivateModuleLicense 停用模块许可证
	DeactivateModuleLicense(ctx context.Context, moduleName string) error

	// ValidateModuleLicense 验证模块许可证
	ValidateModuleLicense(ctx context.Context, moduleName string) (*LicenseInfo, error)

	// GetModuleLicenseInfo 获取模块许可证信息
	GetModuleLicenseInfo(ctx context.Context, moduleName string) (*LicenseInfo, error)

	// ListLicensedModules 列出已授权的模块
	ListLicensedModules(ctx context.Context) ([]string, error)

	// RegisterThirdPartyValidator 注册第三方验证器
	RegisterThirdPartyValidator(validator ThirdPartyLicenseValidator) error

	// SetModuleLicenseValidator 设置模块的许可证验证器
	SetModuleLicenseValidator(moduleName, validatorName string) error

	// 新增方法用于支持LicenseController
	// ValidateLicense 验证许可证（全局）
	ValidateLicense(ctx context.Context) (*LicenseInfo, error)

	// GetLicenseInfo 获取许可证信息（全局）
	GetLicenseInfo() *LicenseInfo

	// GetLicenseStatus 获取许可证状态
	GetLicenseStatus() LicenseStatus

	// InstallLicense 安装许可证
	InstallLicense(licenseData string) error

	// RefreshLicense 刷新许可证
	RefreshLicense() error

	// IsModuleAuthorized 检查模块是否授权
	IsModuleAuthorized(ctx context.Context, moduleName string) bool

	// IsFeatureAuthorized 检查功能是否授权
	IsFeatureAuthorized(ctx context.Context, featureName string) bool

	// GetCacheStats 获取缓存统计
	GetCacheStats() *LicenseCacheStats

	// ClearCache 清空缓存
	ClearCache()
}

// LicenseEvent 许可证事件
type LicenseEvent struct {
	Type       LicenseEventType `json:"type"`
	ModuleName string           `json:"module_name"`
	LicenseKey string           `json:"license_key"`
	Validator  string           `json:"validator"`
	Timestamp  time.Time        `json:"timestamp"`
	Success    bool             `json:"success"`
	ErrorMsg   string           `json:"error_msg,omitempty"`
}

// LicenseEventType 许可证事件类型
type LicenseEventType string

const (
	LicenseActivated   LicenseEventType = "activated"
	LicenseDeactivated LicenseEventType = "deactivated"
	LicenseValidated   LicenseEventType = "validated"
	LicenseExpired     LicenseEventType = "expired"
	LicenseInvalid     LicenseEventType = "invalid"
)

// LicenseStatus 许可证状态
type LicenseStatus string

const (
	LicenseStatusValid    LicenseStatus = "valid"     // 有效
	LicenseStatusExpired  LicenseStatus = "expired"   // 已过期
	LicenseStatusInvalid  LicenseStatus = "invalid"   // 无效
	LicenseStatusMissing  LicenseStatus = "missing"   // 缺失
	LicenseStatusExceeded LicenseStatus = "exceeded"  // 超出限制
)

// LicenseCacheStats 许可证缓存统计
type LicenseCacheStats struct {
	CacheEnabled    bool      `json:"cache_enabled"`
	LastValidation  time.Time `json:"last_validation"`
	ValidationCount int64     `json:"validation_count"`
	CacheHits       int64     `json:"cache_hits"`
	CacheMisses     int64     `json:"cache_misses"`
}

// Edition 版本类型
type Edition string

const (
	EditionPersonal     Edition = "personal"     // 个人版
	EditionProfessional Edition = "professional" // 专业版
	EditionBusiness     Edition = "business"     // 商业版
)

// LicenseEventHandler 许可证事件处理器
type LicenseEventHandler interface {
	EventHandler
	
	// HandleLicenseEvent 处理许可证事件
	HandleLicenseEvent(event LicenseEvent) error
}

// LicenseConfig 许可证配置
type LicenseConfig struct {
	// 官方许可证配置
	OfficialLicenseURL    string `json:"official_license_url"`
	OfficialLicenseAPIKey string `json:"official_license_api_key"`
	
	// 第三方许可证配置
	AllowThirdPartyLicense bool                            `json:"allow_third_party_license"`
	ThirdPartyValidators   map[string]map[string]interface{} `json:"third_party_validators"`
	
	// 验证配置
	ValidateOnStartup    bool          `json:"validate_on_startup"`
	ValidationInterval   time.Duration `json:"validation_interval"`
	OfflineGracePeriod   time.Duration `json:"offline_grace_period"`
	
	// 缓存配置
	CacheLicenseInfo     bool          `json:"cache_license_info"`
	CacheExpiration      time.Duration `json:"cache_expiration"`
}

// LicenseStore 许可证存储接口
type LicenseStore interface {
	// SaveLicenseInfo 保存许可证信息
	SaveLicenseInfo(ctx context.Context, moduleName string, info *LicenseInfo) error
	
	// GetLicenseInfo 获取许可证信息
	GetLicenseInfo(ctx context.Context, moduleName string) (*LicenseInfo, error)
	
	// DeleteLicenseInfo 删除许可证信息
	DeleteLicenseInfo(ctx context.Context, moduleName string) error
	
	// ListLicensedModules 列出已授权的模块
	ListLicensedModules(ctx context.Context) ([]string, error)
	
	// SaveLicenseUsage 保存许可证使用记录
	SaveLicenseUsage(ctx context.Context, usage *LicenseUsage) error
	
	// GetLicenseUsage 获取许可证使用记录
	GetLicenseUsage(ctx context.Context, licenseKey, moduleName string) (*LicenseUsage, error)
}
