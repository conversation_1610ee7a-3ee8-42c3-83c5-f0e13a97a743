/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/FileSystemWatcher.go
 * @Description: 文件系统监控器
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"path/filepath"
	"sync"
	"time"

	"go.uber.org/zap"
)

// FileSystemWatcher 文件系统监控器
type FileSystemWatcher struct {
	path      string
	logger    *zap.Logger
	callbacks []ModuleChangeCallback
	mu        sync.RWMutex
	running   bool
	stopChan  chan struct{}
}

// NewFileSystemWatcher 创建文件系统监控器
func NewFileSystemWatcher(path string, logger *zap.Logger) *FileSystemWatcher {
	return &FileSystemWatcher{
		path:      path,
		logger:    logger,
		callbacks: make([]ModuleChangeCallback, 0),
		stopChan:  make(chan struct{}),
	}
}

// Start 启动监控
func (w *FileSystemWatcher) Start() error {
	w.mu.Lock()
	defer w.mu.Unlock()

	if w.running {
		return nil
	}

	w.running = true
	w.stopChan = make(chan struct{})

	// 启动监控goroutine
	go w.watchLoop()

	w.logger.Info("File system watcher started", zap.String("path", w.path))
	return nil
}

// Stop 停止监控
func (w *FileSystemWatcher) Stop() error {
	w.mu.Lock()
	defer w.mu.Unlock()

	if !w.running {
		return nil
	}

	w.running = false
	close(w.stopChan)

	w.logger.Info("File system watcher stopped", zap.String("path", w.path))
	return nil
}

// AddCallback 添加回调函数
func (w *FileSystemWatcher) AddCallback(callback ModuleChangeCallback) {
	w.mu.Lock()
	defer w.mu.Unlock()
	w.callbacks = append(w.callbacks, callback)
}

// watchLoop 监控循环
func (w *FileSystemWatcher) watchLoop() {
	ticker := time.NewTicker(5 * time.Second) // 每5秒检查一次
	defer ticker.Stop()

	lastScan := make(map[string]time.Time)

	for {
		select {
		case <-w.stopChan:
			return
		case <-ticker.C:
			w.scanForChanges(lastScan)
		}
	}
}

// scanForChanges 扫描变化
func (w *FileSystemWatcher) scanForChanges(lastScan map[string]time.Time) {
	// 简化的文件系统扫描实现
	// 在实际环境中，应该使用fsnotify等库进行更高效的监控

	err := filepath.WalkDir(w.path, func(path string, entry fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		// 只监控模块目录
		if !entry.IsDir() {
			return nil
		}

		// 跳过根目录
		if path == w.path {
			return nil
		}

		// 检查是否是模块目录
		modulePath := filepath.Join(path, "module.go")
		if !w.fileExists(modulePath) {
			return nil
		}

		// 获取模块名称
		moduleName := w.extractModuleName(path)
		if moduleName == "" {
			return nil
		}

		// 检查文件修改时间
		modTime := w.getModTime(modulePath)
		if lastTime, exists := lastScan[moduleName]; exists {
			if modTime.After(lastTime) {
				// 文件已修改
				w.notifyChange(ModuleModified, moduleName, path)
			}
		} else {
			// 新文件
			w.notifyChange(ModuleAdded, moduleName, path)
		}

		lastScan[moduleName] = modTime
		return nil
	})

	if err != nil {
		w.logger.Error("Failed to scan for changes", zap.Error(err))
	}

	// 检查删除的模块
	for moduleName := range lastScan {
		modulePath := filepath.Join(w.path, moduleName, "module.go")
		if !w.fileExists(modulePath) {
			w.notifyChange(ModuleRemoved, moduleName, "")
			delete(lastScan, moduleName)
		}
	}
}

// notifyChange 通知变化
func (w *FileSystemWatcher) notifyChange(changeType ModuleChangeType, moduleName, path string) {
	event := ModuleChangeEvent{
		Type:       changeType,
		ModuleName: moduleName,
		Metadata: &ModuleMetadata{
			Name:         moduleName,
			Path:         path,
			Dependencies: []string{},
			Enabled:      true,
		},
		Timestamp: time.Now(),
	}

	w.mu.RLock()
	callbacks := make([]ModuleChangeCallback, len(w.callbacks))
	copy(callbacks, w.callbacks)
	w.mu.RUnlock()

	for _, callback := range callbacks {
		if err := callback(event); err != nil {
			w.logger.Error("Callback failed",
				zap.String("module", moduleName),
				zap.Error(err),
			)
		}
	}
}

// fileExists 检查文件是否存在
func (w *FileSystemWatcher) fileExists(path string) bool {
	// TODO: 实现真实的文件存在检查
	// 这里返回true作为示例
	return true
}

// getModTime 获取文件修改时间
func (w *FileSystemWatcher) getModTime(path string) time.Time {
	// TODO: 实现真实的文件修改时间获取
	// 这里返回当前时间作为示例
	return time.Now()
}

// extractModuleName 从路径提取模块名称
func (w *FileSystemWatcher) extractModuleName(path string) string {
	return filepath.Base(path)
}
