/*
 * @Author: C<PERSON> Nieh <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: Clion Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: pkg/contract/Event.go
 * @Description: 定义事件系统的基础接口和类型
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"context"
	"time"
)

// EventName 是事件名称的类型别名，用于标识事件类型
type EventName string

// Event 定义了系统中所有事件必须实现的接口
type Event interface {
	// ID 返回事件的唯一标识符
	ID() string

	// Context 返回与事件关联的上下文
	// 这对于传递请求范围的数据（如跟踪ID、用户ID和租户/站点ID）至关重要
	Context() context.Context

	// Name 返回事件的名称，用于标识事件类型
	Name() EventName

	// Payload 返回事件的有效载荷数据
	// 返回interface{}以支持任意类型的事件数据
	Payload() interface{}

	// Timestamp 返回事件的创建时间
	Timestamp() time.Time

	// GetMetadata 获取特定的元数据值
	GetMetadata(key string) (interface{}, bool)

	// SetMetadata 设置元数据值
	SetMetadata(key string, value interface{})

	// AllMetadata 返回所有元数据
	AllMetadata() map[string]interface{}

	// WithContext 返回带有新上下文的事件副本
	WithContext(ctx context.Context) Event

	// Clone 创建事件的深拷贝
	Clone() Event
} 