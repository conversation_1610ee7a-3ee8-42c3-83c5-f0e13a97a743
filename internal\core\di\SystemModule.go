/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/di/SystemModule.go
 * @Description: 系统设置基础设施依赖注入模块
 * 
 * © 2025 GACMS. All rights reserved.
 */

package di

import (
	"gacms/internal/core/system/model"
	"gacms/internal/core/system/service"
	"gacms/internal/port/http/middleware"
	"go.uber.org/fx"
)

// SystemModule 系统设置基础设施模块
var SystemModule = fx.Module("system",
	// 基础设施服务
	fx.Provide(
		// 站点管理服务
		fx.Annotate(
			service.NewSiteService,
			fx.As(new(SiteServiceInterface)),
		),
		
		// 域名绑定服务
		fx.Annotate(
			service.NewDomainBindingService,
			fx.As(new(DomainBindingServiceInterface)),
		),
		
		// URL重写中间件
		middleware.NewURLRewriteMiddleware,
		
		// 站点解析中间件（更新为使用新服务）
		middleware.NewSiteResolver,
	),
	
	// TODO: 基础设施仓储实现
	// fx.Provide(
	//     repository.NewSiteGormRepository,
	//     repository.NewDomainBindingGormRepository,
	//     repository.NewSystemConfigGormRepository,
	// ),
)

// SiteServiceInterface 站点服务接口（用于依赖注入）
type SiteServiceInterface interface {
	CreateSite(name, domain, description string) (*model.Site, error)
	UpdateSite(siteID uint, name, description string) (*model.Site, error)
	DeleteSite(siteID uint) error
	GetSite(siteID uint) (*model.Site, error)
	GetSiteByDomain(domain string) (*model.Site, error)
	ListSites(page, pageSize int) ([]*model.Site, int64, error)
	ListActiveSites() ([]*model.Site, error)
}

// DomainBindingServiceInterface 域名绑定服务接口（用于依赖注入）
type DomainBindingServiceInterface interface {
	CreateModuleBinding(domain string, siteID uint, moduleSlug string) (*model.DomainBinding, error)
	CreateCategoryBinding(domain string, siteID uint, categoryID uint) (*model.DomainBinding, error)
	UpdateBinding(bindingID uint, domain string) (*model.DomainBinding, error)
	DeleteBinding(bindingID uint) error
	GetBinding(bindingID uint) (*model.DomainBinding, error)
	GetBindingByDomain(domain string) (*model.DomainBinding, error)
	GetBindingByDomainWithRules(domain string) (*model.DomainBinding, error)
	ListBindingsBySite(siteID uint, page, pageSize int) ([]*model.DomainBinding, int64, error)
	EnableURLRewrite(bindingID uint, defaultController, defaultAction string) error
	DisableURLRewrite(bindingID uint) error
}
