/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/menu/infrastructure/persistence/GormRepository.go
 * @Description: GORM implementation of the menu repositories.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package persistence

import (
	"gacms/internal/modules/menu/domain/contract"
	"gacms/internal/modules/menu/domain/model"
	"gorm.io/gorm"
)

// gormMenuRepository implements the contract.MenuRepository interface using GORM.
type gormMenuRepository struct {
	db *gorm.DB
}

// gormMenuItemRepository implements the contract.MenuItemRepository interface using GORM.
type gormMenuItemRepository struct {
	db *gorm.DB
}

// NewGormMenuRepository creates a new menu repository.
func NewGormMenuRepository(db *gorm.DB) contract.MenuRepository {
	return &gormMenuRepository{db: db}
}

// NewGormMenuItemRepository creates a new menu item repository.
func NewGormMenuItemRepository(db *gorm.DB) contract.MenuItemRepository {
	return &gormMenuItemRepository{db: db}
}

// MenuRepository implementation
func (r *gormMenuRepository) Create(menu *model.Menu) error {
	return r.db.Create(menu).Error
}

func (r *gormMenuRepository) Update(menu *model.Menu) error {
	return r.db.Save(menu).Error
}

func (r *gormMenuRepository) Delete(id uint) error {
	// GORM's cascading delete constraint on the model will handle deleting items.
	return r.db.Delete(&model.Menu{}, id).Error
}

func (r *gormMenuRepository) GetByID(id uint) (*model.Menu, error) {
	var menu model.Menu
	err := r.db.First(&menu, id).Error
	return &menu, err
}

func (r *gormMenuRepository) GetBySlug(siteID uint, slug string) (*model.Menu, error) {
	var menu model.Menu
	err := r.db.Where("site_id = ? AND slug = ?", siteID, slug).First(&menu).Error
	return &menu, err
}

func (r *gormMenuRepository) GetAllBySiteID(siteID uint) ([]*model.Menu, error) {
	var menus []*model.Menu
	err := r.db.Where("site_id = ?", siteID).Find(&menus).Error
	return menus, err
}

// MenuItemRepository implementation
func (r *gormMenuItemRepository) Create(item *model.MenuItem) error {
	return r.db.Create(item).Error
}

func (r *gormMenuItemRepository) Update(item *model.MenuItem) error {
	return r.db.Save(item).Error
}

func (r *gormMenuItemRepository) Delete(id uint) error {
	return r.db.Delete(&model.MenuItem{}, id).Error
}

func (r *gormMenuItemRepository) GetByID(id uint) (*model.MenuItem, error) {
	var item model.MenuItem
	err := r.db.First(&item, id).Error
	return &item, err
}

func (r *gormMenuItemRepository) GetByMenuID(menuID uint) ([]*model.MenuItem, error) {
	var items []*model.MenuItem
	err := r.db.Where("menu_id = ?", menuID).Order("sort_order asc").Find(&items).Error
	return items, err
}

func (r *gormMenuItemRepository) DeleteByMenuID(menuID uint) error {
	return r.db.Where("menu_id = ?", menuID).Delete(&model.MenuItem{}).Error
}