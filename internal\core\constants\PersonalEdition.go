/*
Author: <PERSON><PERSON>eh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package constants

// PersonalEditionProvider 个人版配置提供者接口
type PersonalEditionProvider interface {
	GetFeatures() []string
	IsFeature(featureName string) bool
	GetLimit(limitType string) int
}

// personalEditionConfig 个人版配置实现（私有）
type personalEditionConfig struct{}

// 个人版配置常量（私有）
const (
	personalMaxSites    = 1    // 最大站点数
	personalMaxUsers    = 3    // 最大用户数
	personalMaxAPICalls = 1000 // API调用限制/天
)

// 个人版功能列表（私有）
var personalFeatures = []string{
	"basic_content", "basic_theme", "basic_seo", "basic_user", "api_access",
}

// GetFeatures 获取个人版功能列表
func (p *personalEditionConfig) GetFeatures() []string {
	return personalFeatures
}

// IsFeature 检查是否是个人版功能
func (p *personalEditionConfig) IsFeature(featureName string) bool {
	for _, feature := range personalFeatures {
		if featureName == feature {
			return true
		}
	}
	return false
}

// GetLimit 获取个人版限制值
func (p *personalEditionConfig) GetLimit(limitType string) int {
	switch limitType {
	case "sites", "max_sites":
		return personalMaxSites
	case "users", "max_users", "admin_users", "max_admin_users":
		return personalMaxUsers
	case "api_calls", "api_calls_per_day":
		return personalMaxAPICalls
	default:
		return 0 // 其他限制不存在
	}
}

// GetPersonalEditionProvider 获取个人版配置提供者
func GetPersonalEditionProvider() PersonalEditionProvider {
	return &personalEditionConfig{}
}
