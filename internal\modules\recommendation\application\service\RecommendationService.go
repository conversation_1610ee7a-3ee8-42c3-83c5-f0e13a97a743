/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/recommendation/application/service/RecommendationService.go
 * @Description: Service for handling content recommendations.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package service

import (
	"fmt"
	"gacms/internal/modules/contenttype/application/service"
	"gacms/internal/modules/contenttype/domain/model"
	"gacms/internal/modules/recommendation/domain/contract"
)

// RecommendationService provides content recommendations.
// It uses a configured strategy to fetch the recommendations.
type RecommendationService struct {
	itemSvc  *service.ContentItemService
	strategy contract.RecommendationStrategy
}

// NewRecommendationService creates a new recommendation service.
func NewRecommendationService(itemSvc *service.ContentItemService, strategy contract.RecommendationStrategy) *RecommendationService {
	return &RecommendationService{
		itemSvc:  itemSvc,
		strategy: strategy,
	}
}

// GetRelatedContent fetches recommendations for a given content item.
func (s *RecommendationService) GetRelatedContent(siteID uint, itemID uint, limit int) ([]*model.ContentItem, error) {
	// 1. Get the source content item.
	item, err := s.itemSvc.GetItemByID(itemID)
	if err != nil {
		return nil, fmt.Errorf("could not find source item with ID %d: %w", itemID, err)
	}

	// 2. Ensure the item belongs to the requested site.
	if item.SiteID != siteID {
		return nil, fmt.Errorf("item with ID %d does not belong to site %d", itemID, siteID)
	}

	// 3. Use the configured strategy to get recommendations.
	return s.strategy.GetRecommendations(item, siteID, limit)
} 