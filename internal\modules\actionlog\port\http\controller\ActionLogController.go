/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/actionlog/port/http/controller/ActionLogController.go
 * @Description: Controller for action log operations, providing generic endpoints.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package controller

import (
	"gacms/internal/modules/actionlog/application/service"
	"gacms/internal/modules/actionlog/domain/model"
	pkgContract "gacms/pkg/contract"
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
)

type ActionLogController struct {
	service *service.ActionLogService
	appCtx  pkgContract.AppContext
}

func NewActionLogController(service *service.ActionLogService, appCtx pkgContract.AppContext) *ActionLogController {
	return &ActionLogController{
		service: service,
		appCtx:  appCtx,
	}
}

// RegisterRoutes sets up the routing for the actionlog endpoints.
func (c *ActionLogController) RegisterRoutes(rg *gin.RouterGroup) {
	// Note: The /api prefix is handled by the router manager.
	// The 'actionlog' prefix can be added here for namespacing.
	logGroup := rg.Group("/actionlog")
	{
		logGroup.GET("/logs", c.GetLogs)
		logGroup.POST("/logs", c.CreateLog)
	}
}

// GetLogs will be automatically mapped to GET /logs.
// It relies on upstream middleware to provide user context for authorization.
func (c *ActionLogController) GetLogs(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	filters := make(map[string]interface{})

	// User context (userID, siteID, isSuperAdmin) is expected to be in the gin.Context,
	// placed there by an authentication/authorization middleware.
	isSuperAdmin, _ := ctx.Get("isSuperAdmin")
	
	if isSuperAdmin, ok := isSuperAdmin.(bool); ok && !isSuperAdmin {
		if siteID, exists := ctx.Get("siteID"); exists {
			filters["site_id"] = siteID
		} else {
			// If a non-super-admin has no siteID in context, they can't see any logs.
			// This is a security precaution.
			ctx.JSON(http.StatusOK, gin.H{"data": []interface{}{}, "total": 0})
			return
		}
	} else {
		// Super admin can optionally filter by siteId
		if siteID := ctx.Query("siteId"); siteID != "" {
			filters["site_id"] = siteID
		}
	}
	
	if userID := ctx.Query("userId"); userID != "" {
		filters["user_id"] = userID
	}

	logs, total, err := c.service.ListLogs(ctx.Request.Context(), page, pageSize, filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"data": logs, "total": total})
}

// CreateLog will be automatically mapped to POST /logs by the convention-based router.
// It provides a generic, centralized endpoint for any module to record an action.
// This method automatically enriches the log with common data from the request context.
func (c *ActionLogController) CreateLog(ctx *gin.Context) {
	var logData model.ActionLog
	// We only bind the fields that the client should provide, e.g., Description, Status.
	if err := ctx.ShouldBindJSON(&logData); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid log data: " + err.Error()})
		return
	}

	// Enrich the log data with information from the context, which is populated by middleware.
	// This centralizes the logic for gathering common log info.
	if userID, exists := ctx.Get("userID"); exists {
		if id, ok := userID.(uint); ok {
			logData.UserID = id
		}
	}
	if username, exists := ctx.Get("username"); exists {
		if name, ok := username.(string); ok {
			logData.Username = name
		}
	}
	logData.IPAddress = ctx.ClientIP()
	logData.Method = ctx.Request.Method
	logData.Path = ctx.Request.URL.Path

	// The service layer receives a fully enriched log object.
	if err := c.service.CreateLog(ctx.Request.Context(), &logData); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create log"})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{"message": "Log created successfully"})
}