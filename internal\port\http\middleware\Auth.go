/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/port/http/middleware/Auth.go
 * @Description: Provides a generic, reusable authentication middleware using JWT.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package middleware

import (
	"gacms/internal/infrastructure/auth"
	"gacms/internal/port/http/response"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// NewAuth creates a new authentication middleware handler.
// It depends on the TokenProcessor to validate JWTs.
func NewAuth(tokenProcessor *auth.TokenProcessor) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			response.Fail(c, http.StatusUnauthorized, "Authorization header is required")
			c.Abort()
			return
		}

		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			response.Fail(c, http.StatusUnauthorized, "Authorization header format must be Bearer {token}")
			c.Abort()
			return
		}

		tokenString := parts[1]
		claims, err := tokenProcessor.Parse(tokenString)
		if err != nil {
			response.Fail(c, http.StatusUnauthorized, "Invalid or expired token")
			c.Abort()
			return
		}

		userID, _ := claims["user_id"].(float64) // user_id from JWT is float64
		roles, _ := claims["roles"].([]interface{})

		// Convert roles to []string
		var rolesStr []string
		for _, r := range roles {
			if role, ok := r.(string); ok {
				rolesStr = append(rolesStr, role)
			}
		}
		
		// Set user information in the context for downstream handlers.
		ctx := auth.WithUser(c.Request.Context(), uint(userID), rolesStr)
		c.Request = c.Request.WithContext(ctx)

		c.Next()
	}
} 