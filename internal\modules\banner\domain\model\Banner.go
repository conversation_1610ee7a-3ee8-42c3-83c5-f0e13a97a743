/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/banner/domain/model/Banner.go
 * @Description: Defines the Banner domain model.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package model

import (
	"time"
	"gorm.io/gorm"
)

// Banner represents a single banner item.
type Banner struct {
	gorm.Model
	SiteID      uint   `gorm:"index;not null"`
	PositionID  uint   `gorm:"index;not null"`
	Title       string `gorm:"type:varchar(255);not null"`
	ImageURL    string `gorm:"type:varchar(512);not null"`
	LinkURL     string `gorm:"type:varchar(512)"`
	Order       int    `gorm:"default:0"`
	IsActive    bool   `gorm:"default:true"`
	StartDate   *time.Time
	EndDate     *time.Time
} 