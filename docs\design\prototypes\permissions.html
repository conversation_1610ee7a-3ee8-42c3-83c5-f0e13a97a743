<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 权限管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        /* 权限项样式 */
        .permission-item {
            transition: all 0.3s ease;
        }
        .permission-item:hover {
            background-color: rgba(55, 65, 81, 0.5);
        }

        /* 开关样式 */
        .switch {
            width: 40px;
            height: 24px;
            background-color: #444;
            border-radius: 12px;
            padding: 2px;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-block;
            position: relative;
        }
        .switch-handle {
            width: 20px;
            height: 20px;
            background-color: white;
            border-radius: 10px;
            transition: all 0.3s ease;
            position: absolute;
            left: 2px;
            top: 2px;
        }
        .switch.active {
            background-color: #007bff;
        }
        .switch.active .switch-handle {
            transform: translateX(16px);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">权限管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-plus text-white"></i>
                                </span>
                                创建角色
                            </span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 角色统计 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- 角色总数 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-gray-400">角色总数</span>
                        <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center">
                            <i class="fas fa-users-cog text-blue-500 text-xl"></i>
                        </div>
                    </div>
                    <div class="text-3xl font-bold mb-2 text-white">8</div>
                    <div class="text-sm text-gray-400">包含系统预设角色</div>
                </div>

                <!-- 自定义角色 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-gray-400">自定义角色</span>
                        <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center">
                            <i class="fas fa-user-tag text-green-500 text-xl"></i>
                        </div>
                    </div>
                    <div class="text-3xl font-bold mb-2 text-white">5</div>
                    <div class="text-sm text-gray-400">用户创建的角色</div>
                </div>

                <!-- 权限总数 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-gray-400">权限总数</span>
                        <div class="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center">
                            <i class="fas fa-shield-alt text-purple-500 text-xl"></i>
                        </div>
                    </div>
                    <div class="text-3xl font-bold mb-2 text-white">128</div>
                    <div class="text-sm text-gray-400">可分配的权限项</div>
                </div>

                <!-- 用户数量 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-gray-400">用户数量</span>
                        <div class="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center">
                            <i class="fas fa-user text-yellow-500 text-xl"></i>
                        </div>
                    </div>
                    <div class="text-3xl font-bold mb-2 text-white">256</div>
                    <div class="text-sm text-gray-400">已分配角色的用户</div>
                </div>
            </div>

            <!-- 主要内容区 -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 左侧：角色列表 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <h3 class="text-lg font-semibold mb-6 text-white">角色列表</h3>
                    <div class="space-y-4">
                        <!-- 超级管理员 -->
                        <div class="permission-item p-4 rounded-lg flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 rounded-full bg-red-500/20 flex items-center justify-center text-red-500">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div>
                                    <div class="font-medium text-white">超级管理员</div>
                                    <div class="text-sm text-gray-400">系统最高权限</div>
                                </div>
                            </div>
                            <button class="px-3 py-1 rounded text-blue-400 hover:bg-gray-700">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>

                        <!-- 内容管理员 -->
                        <div class="permission-item p-4 rounded-lg flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center text-blue-500">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div>
                                    <div class="font-medium text-white">内容管理员</div>
                                    <div class="text-sm text-gray-400">内容相关权限</div>
                                </div>
                            </div>
                            <button class="px-3 py-1 rounded text-blue-400 hover:bg-gray-700">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>

                        <!-- 编辑 -->
                        <div class="permission-item p-4 rounded-lg flex items-center justify-between bg-gray-800/20">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center text-green-500">
                                    <i class="fas fa-edit"></i>
                                </div>
                                <div>
                                    <div class="font-medium text-white">编辑</div>
                                    <div class="text-sm text-gray-400">文章编辑权限</div>
                                </div>
                            </div>
                            <button class="px-3 py-1 rounded text-blue-400 hover:bg-gray-700">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>

                        <!-- 审核员 -->
                        <div class="permission-item p-4 rounded-lg flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 rounded-full bg-yellow-500/20 flex items-center justify-center text-yellow-500">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div>
                                    <div class="font-medium text-white">审核员</div>
                                    <div class="text-sm text-gray-400">内容审核权限</div>
                                </div>
                            </div>
                            <button class="px-3 py-1 rounded text-blue-400 hover:bg-gray-700">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>

                        <!-- 访客 -->
                        <div class="permission-item p-4 rounded-lg flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 rounded-full bg-gray-500/20 flex items-center justify-center text-gray-400">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div>
                                    <div class="font-medium text-white">访客</div>
                                    <div class="text-sm text-gray-400">只读权限</div>
                                </div>
                            </div>
                            <button class="px-3 py-1 rounded text-blue-400 hover:bg-gray-700">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 中间：权限详情 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold text-white">编辑角色权限</h3>
                        <div class="flex gap-2">
                            <button class="px-3 py-1 rounded bg-red-500/20 text-red-400 hover:bg-red-500/30">
                                删除
                            </button>
                            <button class="px-3 py-1 rounded bg-blue-500/20 text-blue-400 hover:bg-blue-500/30">
                                保存
                            </button>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <!-- 角色基本信息 -->
                        <div>
                            <label class="block text-gray-400 text-sm font-medium mb-2">角色名称</label>
                            <input type="text" value="编辑" 
                                  class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>

                        <div>
                            <label class="block text-gray-400 text-sm font-medium mb-2">描述</label>
                            <textarea class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent" rows="3">负责文章的撰写和编辑工作，但不具备发布权限。</textarea>
                        </div>

                        <!-- 权限组 -->
                        <div>
                            <h4 class="font-medium text-white mb-4">内容管理权限</h4>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center p-3 bg-gray-800/20 rounded-lg">
                                    <span>查看文章</span>
                                    <div class="switch active">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-gray-800/20 rounded-lg">
                                    <span>创建文章</span>
                                    <div class="switch active">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-gray-800/20 rounded-lg">
                                    <span>编辑文章</span>
                                    <div class="switch active">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-gray-800/20 rounded-lg">
                                    <span>删除文章</span>
                                    <div class="switch">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-gray-800/20 rounded-lg">
                                    <span>发布文章</span>
                                    <div class="switch">
                                        <div class="switch-handle"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧：分配用户 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <h3 class="text-lg font-semibold mb-6 text-white">分配用户</h3>
                    
                    <!-- 搜索用户 -->
                    <div class="relative mb-6">
                        <input type="text" placeholder="搜索用户..." 
                              class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 pl-10 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <div class="absolute left-3 top-2.5 text-gray-400">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                    
                    <!-- 用户列表 -->
                    <div class="space-y-3 mb-6 max-h-[400px] overflow-y-auto">
                        <div class="flex justify-between items-center p-3 bg-gray-800/20 rounded-lg">
                            <div class="flex items-center gap-3">
                                <img src="https://via.placeholder.com/40" alt="User" class="w-8 h-8 rounded-full">
                                <div>
                                    <div class="text-sm font-medium text-white">张三</div>
                                    <div class="text-xs text-gray-400"><EMAIL></div>
                                </div>
                            </div>
                            <input type="checkbox" class="form-checkbox h-5 w-5 text-blue-500 rounded" checked>
                        </div>
                        
                        <div class="flex justify-between items-center p-3 bg-gray-800/20 rounded-lg">
                            <div class="flex items-center gap-3">
                                <img src="https://via.placeholder.com/40" alt="User" class="w-8 h-8 rounded-full">
                                <div>
                                    <div class="text-sm font-medium text-white">李四</div>
                                    <div class="text-xs text-gray-400"><EMAIL></div>
                                </div>
                            </div>
                            <input type="checkbox" class="form-checkbox h-5 w-5 text-blue-500 rounded" checked>
                        </div>
                        
                        <div class="flex justify-between items-center p-3 bg-gray-800/20 rounded-lg">
                            <div class="flex items-center gap-3">
                                <img src="https://via.placeholder.com/40" alt="User" class="w-8 h-8 rounded-full">
                                <div>
                                    <div class="text-sm font-medium text-white">王五</div>
                                    <div class="text-xs text-gray-400"><EMAIL></div>
                                </div>
                            </div>
                            <input type="checkbox" class="form-checkbox h-5 w-5 text-blue-500 rounded">
                        </div>
                        
                        <div class="flex justify-between items-center p-3 bg-gray-800/20 rounded-lg">
                            <div class="flex items-center gap-3">
                                <img src="https://via.placeholder.com/40" alt="User" class="w-8 h-8 rounded-full">
                                <div>
                                    <div class="text-sm font-medium text-white">赵六</div>
                                    <div class="text-xs text-gray-400"><EMAIL></div>
                                </div>
                            </div>
                            <input type="checkbox" class="form-checkbox h-5 w-5 text-blue-500 rounded">
                        </div>
                    </div>
                    
                    <!-- 保存按钮 -->
                    <button class="w-full py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors">
                        保存用户分配
                    </button>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 权限开关切换
            const switches = document.querySelectorAll('.switch');
            switches.forEach(switchEl => {
                switchEl.addEventListener('click', function() {
                    this.classList.toggle('active');
                });
            });
        });
    </script>
</body>
</html> 