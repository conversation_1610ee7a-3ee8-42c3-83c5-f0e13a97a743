/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/extension/port/http/controller/ExtensionController.go
 * @Description: Controller for installing and managing extensions.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package controller

import (
	"fmt"
	"gacms/internal/modules/extension/application/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type ExtensionController struct {
	service *service.ExtensionService
}

func NewExtensionController(service *service.ExtensionService) *ExtensionController {
	return &ExtensionController{service: service}
}

func (c *ExtensionController) RegisterRoutes(rg *gin.RouterGroup) {
	rg.POST("/upload/:type", c.InstallFromUpload)
	rg.GET("/installed/:type", c.ListInstalled)
	rg.POST("/enable", c.Enable)
	rg.POST("/disable", c.Disable)
	rg.DELETE("/uninstall/:type/:dirName", c.Uninstall)
	// Routes for other activation types, deletion, etc., will be added here
}

func (c *ExtensionController) InstallFromUpload(ctx *gin.Context) {
	extType := ctx.Param("type")
	if extType != service.TypeTheme && extType != service.TypeModule && extType != service.TypePlugin {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid extension type specified."})
		return
	}

	fileHeader, err := ctx.FormFile("file")
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "File not provided or invalid. Please upload a zip file."})
		return
	}

	if err := c.service.InstallFromUpload(extType, fileHeader); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to install extension: %v", err)})
		return
	}
	
	ctx.Set("action_log_description", fmt.Sprintf("Installed new %s from file: %s", extType, fileHeader.Filename))
	ctx.JSON(http.StatusOK, gin.H{"message": fmt.Sprintf("%s installed successfully.", extType)})
}

func (c *ExtensionController) ListInstalled(ctx *gin.Context) {
	extType := ctx.Param("type")
	extensions, err := c.service.ListInstalled(extType)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to list extensions: %v", err)})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"data": extensions})
}

type EnableRequest struct {
	SiteID  uint   `json:"siteId"`
	ExtType string `json:"type" binding:"required"`
	ExtName string `json:"name" binding:"required"`
}

func (c *ExtensionController) Enable(ctx *gin.Context) {
	var req EnableRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	if err := c.service.Enable(req.ExtType, req.ExtName, req.SiteID); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to enable %s: %v", req.ExtType, err)})
		return
	}
	
	ctx.Set("action_log_description", fmt.Sprintf("Enabled %s '%s'", req.ExtType, req.ExtName))
	ctx.JSON(http.StatusOK, gin.H{"message": fmt.Sprintf("%s enabled successfully.", req.ExtType)})
}

type DisableRequest struct {
	SiteID  uint   `json:"siteId"`
	ExtType string `json:"type" binding:"required"`
	ExtName string `json:"name" binding:"required"`
}

func (c *ExtensionController) Disable(ctx *gin.Context) {
	var req DisableRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	if err := c.service.Disable(req.ExtType, req.ExtName, req.SiteID); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to disable %s: %v", req.ExtType, err)})
		return
	}
	
	ctx.Set("action_log_description", fmt.Sprintf("Disabled %s '%s'", req.ExtType, req.ExtName))
	ctx.JSON(http.StatusOK, gin.H{"message": fmt.Sprintf("%s disabled successfully.", req.ExtType)})
}

func (c *ExtensionController) Uninstall(ctx *gin.Context) {
	extType := ctx.Param("type")
	dirName := ctx.Param("dirName")

	if extType == "" || dirName == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Extension type and directory name are required."})
		return
	}

	if err := c.service.Uninstall(extType, dirName); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to uninstall extension: %v", err)})
		return
	}

	ctx.Set("action_log_description", fmt.Sprintf("Uninstalled %s: %s", extType, dirName))
	ctx.JSON(http.StatusOK, gin.H{"message": "Extension uninstalled successfully."})
} 