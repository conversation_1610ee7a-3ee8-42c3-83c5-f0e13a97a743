/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/modules/user/events/UserRoleChanged.go
 * @Description: User role assignment event models for the RBAC system.
 *
 * © 2025 GACMS. All rights reserved.
 */
package events

import (
	"context"
	"gacms/internal/core/bus"
	"gacms/internal/modules/user/domain/model"
	"gacms/pkg/contract"
)

// 用户角色事件类型
const (
	UserRoleAssignedEventName contract.EventName = "user.role.assigned"
	UserRoleRevokedEventName  contract.EventName = "user.role.revoked"
)

// UserRoleAssignedEvent 用户角色分配事件
type UserRoleAssignedEvent struct {
	bus.BaseEvent
	UserID     uint           `json:"user_id"`
	UserType   model.UserType `json:"user_type"`
	Roles      []string       `json:"roles"` // 角色名称列表
	OperatedBy uint           `json:"operated_by"`
	SiteID     uint           `json:"site_id"`
}

// UserRoleRevokedEvent 用户角色撤销事件
type UserRoleRevokedEvent struct {
	bus.BaseEvent
	UserID     uint           `json:"user_id"`
	UserType   model.UserType `json:"user_type"`
	Roles      []string       `json:"roles"` // 角色名称列表
	OperatedBy uint           `json:"operated_by"`
	SiteID     uint           `json:"site_id"`
}

// 事件创建函数

// NewUserRoleAssignedEvent 创建用户角色分配事件
func NewUserRoleAssignedEvent(
	ctx context.Context,
	userID uint,
	userType model.UserType,
	roleNames []string,
	operatedBy uint,
	siteID uint,
) *UserRoleAssignedEvent {
	payload := map[string]interface{}{
		"user_id": userID,
		"user_type": userType,
		"roles": roleNames,
		"operated_by": operatedBy,
		"site_id": siteID,
	}
	baseEvent := bus.NewBaseEvent(ctx, UserRoleAssignedEventName, payload).(*bus.BaseEvent)
	return &UserRoleAssignedEvent{
		BaseEvent:  *baseEvent,
		UserID:     userID,
		UserType:   userType,
		Roles:      roleNames,
		OperatedBy: operatedBy,
		SiteID:     siteID,
	}
}

// NewUserRoleRevokedEvent 创建用户角色撤销事件
func NewUserRoleRevokedEvent(
	ctx context.Context,
	userID uint,
	userType model.UserType,
	roleNames []string,
	operatedBy uint,
	siteID uint,
) *UserRoleRevokedEvent {
	payload := map[string]interface{}{
		"user_id": userID,
		"user_type": userType,
		"roles": roleNames,
		"operated_by": operatedBy,
		"site_id": siteID,
	}
	baseEvent := bus.NewBaseEvent(ctx, UserRoleRevokedEventName, payload).(*bus.BaseEvent)
	return &UserRoleRevokedEvent{
		BaseEvent:  *baseEvent,
		UserID:     userID,
		UserType:   userType,
		Roles:      roleNames,
		OperatedBy: operatedBy,
		SiteID:     siteID,
	}
}

// 用户角色移除事件
const UserRoleRemovedEventName contract.EventName = "user.role.removed"

type UserRoleRemovedEvent struct {
	bus.BaseEvent
	UserID        uint
	UserType      model.UserType
	RoleID        uint
	RoleName      string
	RemovedBy     uint
	SiteID        uint
}

func NewUserRoleRemovedEvent(
	ctx context.Context, 
	userID uint, 
	userType model.UserType, 
	roleID uint, 
	roleName string, 
	removedBy uint, 
	siteID uint,
) UserRoleRemovedEvent {
	payload := map[string]interface{}{
		"user_id": userID,
		"user_type": userType,
		"role_id": roleID,
		"role_name": roleName,
		"removed_by": removedBy,
		"site_id": siteID,
	}
	baseEvent := bus.NewBaseEvent(ctx, UserRoleRemovedEventName, payload).(*bus.BaseEvent)
	return UserRoleRemovedEvent{
		BaseEvent:     *baseEvent,
		UserID:        userID,
		UserType:      userType,
		RoleID:        roleID,
		RoleName:      roleName,
		RemovedBy:     removedBy,
		SiteID:        siteID,
	}
}

// 用户角色批量更新事件
const UserRolesBatchUpdatedEventName contract.EventName = "user.roles.batch_updated"

type UserRolesBatchUpdatedEvent struct {
	bus.BaseEvent
	UserID        uint
	UserType      model.UserType
	AddedRoles    []uint
	RemovedRoles  []uint
	UpdatedBy     uint
	SiteID        uint
}

func NewUserRolesBatchUpdatedEvent(
	ctx context.Context, 
	userID uint, 
	userType model.UserType, 
	addedRoles []uint, 
	removedRoles []uint, 
	updatedBy uint, 
	siteID uint,
) UserRolesBatchUpdatedEvent {
	payload := map[string]interface{}{
		"user_id": userID,
		"user_type": userType,
		"added_roles": addedRoles,
		"removed_roles": removedRoles,
		"updated_by": updatedBy,
		"site_id": siteID,
	}
	baseEvent := bus.NewBaseEvent(ctx, UserRolesBatchUpdatedEventName, payload).(*bus.BaseEvent)
	return UserRolesBatchUpdatedEvent{
		BaseEvent:     *baseEvent,
		UserID:        userID,
		UserType:      userType,
		AddedRoles:    addedRoles,
		RemovedRoles:  removedRoles,
		UpdatedBy:     updatedBy,
		SiteID:        siteID,
	}
} 