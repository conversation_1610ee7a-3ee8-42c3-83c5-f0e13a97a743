<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 媒体库</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .media-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 16px;
        }
        
        .media-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.2s ease;
            height: 0;
            padding-bottom: 75%;
            background-color: rgba(44, 44, 44, 0.5);
        }
        
        .media-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.3);
        }
        
        .media-item.selected {
            border: 2px solid #3B82F6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
        }
        
        .media-item.selected::after {
            content: '\f00c';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            top: 8px;
            right: 8px;
            width: 24px;
            height: 24px;
            background-color: #3B82F6;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .media-thumbnail {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .media-info {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            padding: 8px;
            background: linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0));
            color: white;
        }
        
        .media-file-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 3rem;
            color: #718096;
        }
        
        .file-type-badge {
            position: absolute;
            top: 8px;
            left: 8px;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .file-type-image {
            background-color: rgba(59, 130, 246, 0.8);
            color: white;
        }
        
        .file-type-video {
            background-color: rgba(236, 72, 153, 0.8);
            color: white;
        }
        
        .file-type-document {
            background-color: rgba(16, 185, 129, 0.8);
            color: white;
        }
        
        .file-type-audio {
            background-color: rgba(245, 158, 11, 0.8);
            color: white;
        }
        
        .upload-zone {
            border: 2px dashed #4B5563;
            border-radius: 8px;
            padding: 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            transition: all 0.3s ease;
            background-color: rgba(75, 85, 99, 0.1);
        }
        
        .upload-zone:hover {
            border-color: #3B82F6;
            background-color: rgba(59, 130, 246, 0.05);
        }
        
        .upload-icon {
            font-size: 3rem;
            color: #4B5563;
            margin-bottom: 16px;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">媒体库</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="uploadBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-cloud-upload-alt text-white"></i>
                                </span>
                                上传文件
                            </span>
                        </button>
                        <button class="flex items-center justify-center bg-gray-700 text-white px-4 py-3 rounded-lg font-medium transition-all hover:bg-gray-600">
                            <i class="fas fa-folder-plus mr-2"></i> 新建文件夹
                        </button>
                    </div>
                </div>
            </div>

            <!-- 统计概览 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- 总文件数 -->
                <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-file text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">总文件数</div>
                            <div class="text-xl font-semibold text-white">2,653</div>
                            <div class="text-xs text-gray-400 mt-1">今日 +15 个</div>
                        </div>
                    </div>
                </div>

                <!-- 图片 -->
                <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-image text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">图片</div>
                            <div class="text-xl font-semibold text-white">1,842</div>
                            <div class="text-xs text-gray-400 mt-1">占用 4.2 GB</div>
                        </div>
                    </div>
                </div>

                <!-- 视频 -->
                <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-pink-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-film text-pink-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">视频</div>
                            <div class="text-xl font-semibold text-white">145</div>
                            <div class="text-xs text-gray-400 mt-1">占用 8.7 GB</div>
                        </div>
                    </div>
                </div>

                <!-- 存储空间 -->
                <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-hdd text-purple-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">存储空间</div>
                            <div class="text-xl font-semibold text-white">13.5 GB</div>
                            <div class="text-xs text-gray-400 mt-1">总容量 50 GB</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 媒体库主内容 -->
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- 左侧文件夹导航 -->
                <div class="lg:col-span-1">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold text-white mb-4">文件夹</h3>
                        
                        <!-- 搜索框 -->
                        <div class="mb-6">
                            <div class="relative">
                                <input type="text" placeholder="搜索文件..." 
                                       class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent pl-10">
                                <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                    <i class="fas fa-search"></i>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 文件夹列表 -->
                        <div class="space-y-2">
                            <div class="flex items-center p-2 rounded bg-blue-500/20 text-blue-400 border border-blue-500/30">
                                <i class="fas fa-folder mr-3"></i>
                                <span>所有文件</span>
                                <span class="ml-auto text-xs">2,653</span>
                            </div>
                            <div class="flex items-center p-2 rounded hover:bg-gray-700/50 cursor-pointer">
                                <i class="fas fa-folder mr-3"></i>
                                <span>图片素材</span>
                                <span class="ml-auto text-xs">874</span>
                            </div>
                            <div class="flex items-center p-2 rounded hover:bg-gray-700/50 cursor-pointer">
                                <i class="fas fa-folder mr-3"></i>
                                <span>产品图片</span>
                                <span class="ml-auto text-xs">326</span>
                            </div>
                            <div class="flex items-center p-2 rounded hover:bg-gray-700/50 cursor-pointer">
                                <i class="fas fa-folder mr-3"></i>
                                <span>营销素材</span>
                                <span class="ml-auto text-xs">145</span>
                            </div>
                            <div class="flex items-center p-2 rounded hover:bg-gray-700/50 cursor-pointer">
                                <i class="fas fa-folder mr-3"></i>
                                <span>视频资源</span>
                                <span class="ml-auto text-xs">78</span>
                            </div>
                            <div class="flex items-center p-2 rounded hover:bg-gray-700/50 cursor-pointer">
                                <i class="fas fa-folder mr-3"></i>
                                <span>文档文件</span>
                                <span class="ml-auto text-xs">213</span>
                            </div>
                        </div>
                        
                        <hr class="border-gray-700 my-4">
                        
                        <!-- 筛选选项 -->
                        <div class="mb-4">
                            <h4 class="font-medium text-white mb-3 text-sm">文件类型</h4>
                            <div class="space-y-2">
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" class="form-checkbox rounded text-blue-500 border-gray-600 bg-gray-700 mr-2" checked>
                                    <span class="text-gray-300">图片</span>
                                    <span class="ml-auto text-sm text-gray-500">1,842</span>
                                </label>
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" class="form-checkbox rounded text-blue-500 border-gray-600 bg-gray-700 mr-2" checked>
                                    <span class="text-gray-300">视频</span>
                                    <span class="ml-auto text-sm text-gray-500">145</span>
                                </label>
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" class="form-checkbox rounded text-blue-500 border-gray-600 bg-gray-700 mr-2" checked>
                                    <span class="text-gray-300">文档</span>
                                    <span class="ml-auto text-sm text-gray-500">382</span>
                                </label>
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" class="form-checkbox rounded text-blue-500 border-gray-600 bg-gray-700 mr-2" checked>
                                    <span class="text-gray-300">音频</span>
                                    <span class="ml-auto text-sm text-gray-500">84</span>
                                </label>
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" class="form-checkbox rounded text-blue-500 border-gray-600 bg-gray-700 mr-2" checked>
                                    <span class="text-gray-300">其他</span>
                                    <span class="ml-auto text-sm text-gray-500">200</span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- 上传区域 -->
                        <div class="upload-zone mt-6 cursor-pointer" id="dropZone">
                            <i class="fas fa-cloud-upload-alt upload-icon"></i>
                            <h4 class="text-white font-medium mb-2">拖放文件上传</h4>
                            <p class="text-gray-400 text-sm">或点击此处选择文件</p>
                            <input type="file" multiple class="hidden" id="fileInput">
                        </div>
                    </div>
                </div>
                
                <!-- 右侧媒体内容 -->
                <div class="lg:col-span-3">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <!-- 操作菜单 -->
                        <div class="flex flex-wrap justify-between items-center mb-6">
                            <div class="flex items-center text-gray-400">
                                <span>当前位置：</span>
                                <a href="#" class="ml-2 text-blue-400 hover:underline">所有文件</a>
                                <span class="mx-2">/</span>
                                <span class="text-white">图片素材</span>
                            </div>
                            
                            <div class="flex flex-wrap gap-3 mt-2 sm:mt-0">
                                <select class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-gray-300 focus:ring-2 focus:ring-blue-500">
                                    <option value="date_desc">最新上传</option>
                                    <option value="date_asc">最早上传</option>
                                    <option value="name_asc">名称 A-Z</option>
                                    <option value="name_desc">名称 Z-A</option>
                                    <option value="size_desc">大小 由大到小</option>
                                </select>
                                <div class="flex border border-gray-600 rounded-lg overflow-hidden">
                                    <button class="bg-gray-700 px-3 py-2 text-gray-300 hover:bg-gray-600">
                                        <i class="fas fa-th"></i>
                                    </button>
                                    <button class="bg-gray-700 px-3 py-2 text-blue-500 border-l border-gray-600">
                                        <i class="fas fa-list"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 媒体网格 -->
                        <div class="media-grid">
                            <!-- 图片项 -->
                            <div class="media-item">
                                <span class="file-type-badge file-type-image">JPG</span>
                                <img src="./assets/images/photo-1.jpg" alt="图片" class="media-thumbnail">
                                <div class="media-info">
                                    <div class="truncate text-sm">产品展示.jpg</div>
                                </div>
                            </div>
                            
                            <div class="media-item">
                                <span class="file-type-badge file-type-image">PNG</span>
                                <img src="./assets/images/photo-2.jpg" alt="图片" class="media-thumbnail">
                                <div class="media-info">
                                    <div class="truncate text-sm">宣传图.png</div>
                                </div>
                            </div>
                            
                            <div class="media-item selected">
                                <span class="file-type-badge file-type-image">JPG</span>
                                <img src="./assets/images/photo-3.jpg" alt="图片" class="media-thumbnail">
                                <div class="media-info">
                                    <div class="truncate text-sm">背景图.jpg</div>
                                </div>
                            </div>
                            
                            <div class="media-item">
                                <span class="file-type-badge file-type-video">MP4</span>
                                <img src="./assets/images/video-thumb-1.jpg" alt="视频" class="media-thumbnail">
                                <div class="media-info">
                                    <div class="truncate text-sm">产品介绍.mp4</div>
                                </div>
                            </div>
                            
                            <div class="media-item">
                                <span class="file-type-badge file-type-document">PDF</span>
                                <div class="media-file-icon">
                                    <i class="far fa-file-pdf"></i>
                                </div>
                                <div class="media-info">
                                    <div class="truncate text-sm">产品手册.pdf</div>
                                </div>
                            </div>
                            
                            <div class="media-item">
                                <span class="file-type-badge file-type-document">DOCX</span>
                                <div class="media-file-icon">
                                    <i class="far fa-file-word"></i>
                                </div>
                                <div class="media-info">
                                    <div class="truncate text-sm">会议纪要.docx</div>
                                </div>
                            </div>
                            
                            <div class="media-item">
                                <span class="file-type-badge file-type-audio">MP3</span>
                                <div class="media-file-icon">
                                    <i class="far fa-file-audio"></i>
                                </div>
                                <div class="media-info">
                                    <div class="truncate text-sm">背景音乐.mp3</div>
                                </div>
                            </div>
                            
                            <div class="media-item">
                                <span class="file-type-badge file-type-image">SVG</span>
                                <img src="./assets/images/icon-1.svg" alt="SVG图标" class="media-thumbnail">
                                <div class="media-info">
                                    <div class="truncate text-sm">图标.svg</div>
                                </div>
                            </div>
                            
                            <!-- 可以添加更多媒体项... -->
                        </div>
                        
                        <!-- 分页 -->
                        <div class="flex justify-between items-center mt-6">
                            <div class="text-sm text-gray-400">
                                显示 1 至 8，共 874 个
                            </div>
                            <div class="flex space-x-2">
                                <button disabled class="bg-gray-800 text-gray-500 px-4 py-2 rounded-lg">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <button class="bg-blue-500 text-white px-4 py-2 rounded-lg">1</button>
                                <button class="bg-gray-800 text-white hover:bg-gray-700 px-4 py-2 rounded-lg">2</button>
                                <button class="bg-gray-800 text-white hover:bg-gray-700 px-4 py-2 rounded-lg">3</button>
                                <button class="bg-gray-800 text-white hover:bg-gray-700 px-4 py-2 rounded-lg">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            
            // 加载顶部导航栏
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 上传按钮点击事件
            document.getElementById('uploadBtn').addEventListener('click', function() {
                document.getElementById('fileInput').click();
            });
            
            // 拖放上传区域
            const dropZone = document.getElementById('dropZone');
            const fileInput = document.getElementById('fileInput');
            
            // 点击上传区域触发文件选择
            dropZone.addEventListener('click', function() {
                fileInput.click();
            });
            
            // 文件选择变化事件
            fileInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    // 这里处理文件上传
                    console.log('选择了 ' + this.files.length + ' 个文件');
                    // 实际应用中应该调用上传API
                }
            });
            
            // 拖拽相关事件
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, preventDefaults, false);
            });
            
            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            // 拖拽进入样式
            ['dragenter', 'dragover'].forEach(eventName => {
                dropZone.addEventListener(eventName, highlight, false);
            });
            
            // 拖拽离开样式
            ['dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, unhighlight, false);
            });
            
            function highlight() {
                dropZone.classList.add('border-blue-500');
                dropZone.classList.add('bg-blue-500/10');
            }
            
            function unhighlight() {
                dropZone.classList.remove('border-blue-500');
                dropZone.classList.remove('bg-blue-500/10');
            }
            
            // 处理拖放的文件
            dropZone.addEventListener('drop', handleDrop, false);
            
            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                
                if (files.length > 0) {
                    console.log('拖放了 ' + files.length + ' 个文件');
                    // 实际应用中应该调用上传API
                }
            }
        });
    </script>
</body>
</html> 