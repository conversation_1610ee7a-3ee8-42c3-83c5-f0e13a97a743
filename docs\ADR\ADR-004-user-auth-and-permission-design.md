# ADR-004: User Module Authentication and Permission Design

## Status
Proposed

## Context
The GACMS system requires a robust, multi-tenant authentication and permission system. The `user` module is the sole authority for managing both backend (admin) and frontend (member) users. A core requirement is the complete physical and logical separation of these user types. The system must also support various authentication methods (password, 2FA, social login) and a flexible Role-Based Access Control (RBAC) system, all while adhering to the overarching multi-tenancy architecture. This design leverages the existing `TokenProcessor` for JWT handling.

## Decision
We will implement a physically and logically separated architecture for backend and frontend users within the `user` module. This involves distinct data models, services, and repositories for each user type. A unified RBAC model, governed by `site_id`, will manage permissions across the system.

### 1. Physical Isolation via Distinct Data Models

- **Backend User (`admins`)**:
  ```sql
  CREATE TABLE `admins` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `site_id` INT UNSIGNED NOT NULL,
    `email` VARCHAR(255) NOT NULL,
    `password_hash` VARCHAR(255) NOT NULL,
    `tfa_secret` VARCHAR(255) NULL,
    `is_super_admin` BOOLEAN NOT NULL DEFAULT FALSE,
    `created_at` TIMESTAMP,
    `updated_at` TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY (`site_id`, `email`)
  );
  ```
- **Frontend User (`members`)**:
  ```sql
  CREATE TABLE `members` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `site_id` INT UNSIGNED NOT NULL,
    `username` VARCHAR(255) NOT NULL,
    `email` VARCHAR(255) NULL,
    `password_hash` VARCHAR(255) NOT NULL,
    `created_at` TIMESTAMP,
    `updated_at` TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY (`site_id`, `username`)
  );
  ```
- **Social Logins (`social_identities`)**:
  ```sql
  CREATE TABLE `social_identities` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `member_id` INT UNSIGNED NOT NULL,
    `provider` VARCHAR(50) NOT NULL, -- e.g., 'google', 'github'
    `provider_user_id` VARCHAR(255) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY (`provider`, `provider_user_id`)
  );
  ```

### 2. RBAC Model for Unified Permission Management

- **Roles (`roles`)**:
  ```sql
  CREATE TABLE `roles` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `site_id` INT UNSIGNED NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `user_type` ENUM('admin', 'member') NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY (`site_id`, `name`, `user_type`)
  );
  ```
- **Permissions (`permissions`)**:
  ```sql
  CREATE TABLE `permissions` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `slug` VARCHAR(255) NOT NULL, -- e.g., 'posts.create', 'users.edit'
    `description` TEXT,
    PRIMARY KEY (`id`),
    UNIQUE KEY (`slug`)
  );
  ```
- **Pivot Tables**:
  ```sql
  -- Link users to roles
  CREATE TABLE `user_roles` (
    `user_id` INT UNSIGNED NOT NULL,
    `role_id` INT UNSIGNED NOT NULL,
    `user_type` ENUM('admin', 'member') NOT NULL,
    PRIMARY KEY (`user_id`, `role_id`, `user_type`)
  );

  -- Link roles to permissions
  CREATE TABLE `role_permissions` (
    `role_id` INT UNSIGNED NOT NULL,
    `permission_id` INT UNSIGNED NOT NULL,
    PRIMARY KEY (`role_id`, `permission_id`)
  );
  ```

### 3. Service Layer Architecture

- **`AdminAuthService`**: Handles login, 2FA verification, registration, etc., for backend users.
- **`MemberAuthService`**: Handles login, social login, registration, etc., for frontend users.
- **`PermissionService`**: Provides a `Can(ctx, userID, userType, permissionSlug)` method for checking permissions. It will be injected into other services where authorization is needed.

All services will be context-aware, extracting `site_id` from `ctx` for all database operations.

### 4. Authentication Flow

1.  A request hits a controller (e.g., `AdminController`).
2.  The controller calls the appropriate service (e.g., `AdminAuthService.Login(ctx, dto)`).
3.  The service performs business logic:
    a. Retrieves user from the database via its repository.
    b. Verifies credentials (password, 2FA code).
    c. If successful, it constructs a `claims` map, including `user_id`, `site_id`, `user_type`, and `exp`.
4.  The service calls `TokenProcessor.Generate(claims)` to get a JWT.
5.  The JWT is returned to the user.

## Alternatives Considered

### Single User Table
- **Pros**: Simpler table structure.
- **Cons**: Violates the "physical isolation" requirement. Makes the table wide and potentially sparse if frontend and backend users have very different fields. Introduces complexity with a `user_type` column in almost every query.

## Consequences

### Positive
- **Strong Isolation**: Achieves the core requirement of physically separating backend and frontend user data.
- **Clarity**: The role of each service and repository is unambiguous.
- **Scalability**: Different user types can evolve independently without impacting each other.
- **Multi-tenancy by Design**: `site_id` is a first-class citizen in the entire design.

### Negative
- **More Tables**: Increases the number of database tables.
- **Slight Code Duplication**: Some boilerplate for services and repositories might be similar for `Admin` and `Member`. This is an acceptable trade-off for the sake of clarity and isolation.

## Implementation Notes
- All database interactions must go through a repository layer.
- Services should be registered in the custom DI container.
- API endpoints should be clearly versioned and separated (e.g., `/api/v1/admin/...` and `/api/v1/member/...`).
- Ensure proper indexing on `site_id` and other frequently queried columns. 