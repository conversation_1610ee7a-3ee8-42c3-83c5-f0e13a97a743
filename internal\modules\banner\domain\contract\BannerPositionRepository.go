/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/banner/domain/contract/BannerPositionRepository.go
 * @Description: Defines the contract for banner position data access.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"context"
	"gacms/internal/modules/banner/domain/model"
)

// BannerPositionRepository defines the interface for banner position data operations.
type BannerPositionRepository interface {
	Create(ctx context.Context, position *model.BannerPosition) error
	Update(ctx context.Context, position *model.BannerPosition) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*model.BannerPosition, error)
	GetBySlug(ctx context.Context, siteID uint, slug string) (*model.BannerPosition, error)
	List(ctx context.Context, siteID uint) ([]*model.BannerPosition, error)
} 