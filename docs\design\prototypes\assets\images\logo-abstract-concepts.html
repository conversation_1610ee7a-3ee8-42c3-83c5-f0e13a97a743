<!--
© 2025 Clion Nieh. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 抽象字母Logo设计方案</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background-color: #0F172A;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            gap: 60px;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }
        .logo-section {
            width: 100%;
            max-width: 1200px;
            margin-bottom: 40px;
        }
        .concept-intro {
            color: #94A3B8;
            text-align: center;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 40px;
            max-width: 800px;
        }
        .logo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 30px;
            width: 100%;
        }
        .logo-card {
            background-color: #1E293B;
            border-radius: 12px;
            padding: 25px;
            display: flex;
            flex-direction: column;
            align-items: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }
        .logo-display {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 25px 0;
            gap: 16px;
            height: 100px;
            width: 100%;
        }
        .logo-name {
            font-size: 20px;
            font-weight: 600;
            color: #E2E8F0;
            margin-bottom: 15px;
        }
        .logo-description {
            color: #94A3B8;
            text-align: center;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 15px;
        }
        .concept-highlights {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 15px;
            justify-content: center;
        }
        .concept-tag {
            background-color: rgba(14, 165, 233, 0.15);
            color: #7DD3FC;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        .page-title {
            color: white;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            text-align: center;
        }
        .page-subtitle {
            color: #94A3B8;
            font-size: 16px;
            text-align: center;
            max-width: 700px;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1 class="page-title">GACMS 抽象字母Logo设计方案</h1>
    <p class="page-subtitle">极简 · 现代 · 抽象 · 理念融合</p>
    <div class="concept-intro">
        本组Logo以抽象字母为核心，采用极简现代的设计语言，融合GACMS的高性能、模块化、安全可靠、多端协同、内容价值等理念，
        通过负空间、几何形状与线条，展现品牌独特气质与未来感。
    </div>
    <div class="logo-section">
        <div class="logo-grid">
            <!-- 1. 负空间G（包容与开放） -->
            <div class="logo-card">
                <h3 class="logo-name">负空间G</h3>
                <div class="logo-display">
                    <svg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="40" cy="40" r="28" fill="none" stroke="#38BDF8" stroke-width="3" opacity="0.7" />
                        <path d="M40,20 A20,20 0 1 1 20,40" fill="none" stroke="#0EA5E9" stroke-width="4" stroke-linecap="round" />
                        <rect x="40" y="40" width="18" height="4" rx="2" fill="#0EA5E9" />
                    </svg>
                    <div style="display: flex; align-items: baseline; margin-left: 8px;">
                        <span style="font-size: 32px; font-weight: 600; color: white;">GA</span>
                        <span style="font-size: 28px; font-weight: 300; color: #64748B;">CMS</span>
                    </div>
                </div>
                <p class="logo-description">以负空间手法抽象出字母G，象征系统的包容性与开放性，简洁有力，极具现代感。</p>
                <div class="concept-highlights">
                    <span class="concept-tag">极简</span><span class="concept-tag">负空间</span><span class="concept-tag">包容开放</span><span class="concept-tag">现代感</span>
                </div>
            </div>
            <!-- 2. G与A几何融合（模块化协同） -->
            <div class="logo-card">
                <h3 class="logo-name">G与A几何融合</h3>
                <div class="logo-display">
                    <svg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="40" cy="40" r="28" fill="none" stroke="#0EA5E9" stroke-width="2.5" opacity="0.5" />
                        <path d="M30,50 Q40,20 50,50" fill="none" stroke="#38BDF8" stroke-width="4" stroke-linecap="round" />
                        <polygon points="40,30 45,50 35,50" fill="#0EA5E9" opacity="0.7" />
                    </svg>
                    <div style="display: flex; align-items: baseline; margin-left: 8px;">
                        <span style="font-size: 32px; font-weight: 600; color: white;">GA</span>
                        <span style="font-size: 28px; font-weight: 300; color: #64748B;">CMS</span>
                    </div>
                </div>
                <p class="logo-description">将G与A以几何方式融合，象征模块化架构与多端协同，结构简明，极具设计感。</p>
                <div class="concept-highlights">
                    <span class="concept-tag">模块化</span><span class="concept-tag">协同</span><span class="concept-tag">几何美学</span><span class="concept-tag">抽象字母</span>
                </div>
            </div>
            <!-- 3. 流动G（高性能与内容流动） -->
            <div class="logo-card">
                <h3 class="logo-name">流动G</h3>
                <div class="logo-display">
                    <svg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
                        <path d="M25,40 Q40,15 55,40 Q40,65 25,40" fill="none" stroke="#0EA5E9" stroke-width="4" />
                        <path d="M40,40 L55,40" stroke="#38BDF8" stroke-width="3" stroke-linecap="round" />
                        <circle cx="55" cy="40" r="4" fill="#38BDF8" />
                    </svg>
                    <div style="display: flex; align-items: baseline; margin-left: 8px;">
                        <span style="font-size: 32px; font-weight: 600; color: white;">GA</span>
                        <span style="font-size: 28px; font-weight: 300; color: #64748B;">CMS</span>
                    </div>
                </div>
                <p class="logo-description">以流动曲线抽象G，表现内容流动与高性能，整体动感强烈，极简而富有未来感。</p>
                <div class="concept-highlights">
                    <span class="concept-tag">高性能</span><span class="concept-tag">内容流动</span><span class="concept-tag">极简线条</span><span class="concept-tag">未来感</span>
                </div>
            </div>
            <!-- 4. G盾极简（安全可靠） -->
            <div class="logo-card">
                <h3 class="logo-name">G盾极简</h3>
                <div class="logo-display">
                    <svg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
                        <path d="M40,20 Q60,30 60,50 Q40,65 20,50 Q20,30 40,20" fill="none" stroke="#0EA5E9" stroke-width="3" />
                        <path d="M30,45 Q40,55 50,45" fill="none" stroke="#38BDF8" stroke-width="2.5" />
                        <path d="M40,35 L40,50" stroke="#38BDF8" stroke-width="2" stroke-linecap="round" />
                    </svg>
                    <div style="display: flex; align-items: baseline; margin-left: 8px;">
                        <span style="font-size: 32px; font-weight: 600; color: white;">GA</span>
                        <span style="font-size: 28px; font-weight: 300; color: #64748B;">CMS</span>
                    </div>
                </div>
                <p class="logo-description">以极简盾牌轮廓融合G字母，突出安全可靠理念，造型坚固，极具信任感。</p>
                <div class="concept-highlights">
                    <span class="concept-tag">安全可靠</span><span class="concept-tag">极简盾牌</span><span class="concept-tag">信任感</span><span class="concept-tag">抽象字母</span>
                </div>
            </div>
            <!-- 5. G网格（数字协同） -->
            <div class="logo-card">
                <h3 class="logo-name">G网格</h3>
                <div class="logo-display">
                    <svg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
                        <rect x="20" y="20" width="40" height="40" rx="10" fill="none" stroke="#38BDF8" stroke-width="2.5" />
                        <line x1="40" y1="20" x2="40" y2="60" stroke="#0EA5E9" stroke-width="2" />
                        <line x1="20" y1="40" x2="60" y2="40" stroke="#0EA5E9" stroke-width="2" />
                        <circle cx="55" cy="40" r="5" fill="#0EA5E9" />
                    </svg>
                    <div style="display: flex; align-items: baseline; margin-left: 8px;">
                        <span style="font-size: 32px; font-weight: 600; color: white;">GA</span>
                        <span style="font-size: 28px; font-weight: 300; color: #64748B;">CMS</span>
                    </div>
                </div>
                <p class="logo-description">以网格和像素点抽象G，表现数字化、多端协同与系统的结构化能力，极简且富有秩序美。</p>
                <div class="concept-highlights">
                    <span class="concept-tag">数字协同</span><span class="concept-tag">网格结构</span><span class="concept-tag">极简</span><span class="concept-tag">秩序美</span>
                </div>
            </div>
            <!-- 6. G价值曲线（内容价值提升） -->
            <div class="logo-card">
                <h3 class="logo-name">G价值曲线</h3>
                <div class="logo-display">
                    <svg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
                        <path d="M25,55 Q40,30 55,55" fill="none" stroke="#0EA5E9" stroke-width="4" />
                        <path d="M40,55 L40,35" stroke="#38BDF8" stroke-width="2.5" stroke-linecap="round" />
                        <circle cx="40" cy="35" r="4" fill="#38BDF8" />
                    </svg>
                    <div style="display: flex; align-items: baseline; margin-left: 8px;">
                        <span style="font-size: 32px; font-weight: 600; color: white;">GA</span>
                        <span style="font-size: 28px; font-weight: 300; color: #64748B;">CMS</span>
                    </div>
                </div>
                <p class="logo-description">以上升曲线抽象G，表现内容价值提升与成长，极简造型，寓意积极向上。</p>
                <div class="concept-highlights">
                    <span class="concept-tag">内容价值</span><span class="concept-tag">成长曲线</span><span class="concept-tag">极简抽象</span><span class="concept-tag">积极向上</span>
                </div>
            </div>
        </div>
    </div>
    <p style="color: #94A3B8; margin-top: 20px; font-size: 14px; text-align: center; max-width: 700px;">
        以上6个方案均以抽象字母为核心，极简现代，融合GACMS的核心理念，适合多场景品牌应用。<br>
        可根据实际需求灵活调整细节与配色，欢迎进一步反馈优化方向。
    </p>
</body>
</html> 