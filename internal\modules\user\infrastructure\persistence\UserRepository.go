/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/modules/user/infrastructure/persistence/UserRepository.go
 * @Description: 用户仓储实现
 * 
 * © 2025 GACMS. All rights reserved.
 */

package persistence

import (
	"context"
	"errors"
	"sync"
	"time"

	"github.com/google/uuid"
	"go.uber.org/fx"
	"go.uber.org/zap"

	"gacms/internal/infrastructure/database"
	"gacms/internal/modules/user/domain/contract"
	"gacms/internal/modules/user/domain/model"
)

// UserRepository 用户仓储实现
type UserRepository struct {
	db     *database.DB
	logger *zap.Logger
	// 内存存储，实际应用中应该使用数据库
	users map[string]*model.User
	mu    sync.RWMutex
}

// UserRepositoryParams 定义了创建 UserRepository 所需的参数
type UserRepositoryParams struct {
	fx.In

	DB     *database.DB
	Logger *zap.Logger
}

// NewUserRepository 创建一个新的 UserRepository 实例
func NewUserRepository(params UserRepositoryParams) contract.UserRepository {
	return &UserRepository{
		db:     params.DB,
		logger: params.Logger,
		users:  make(map[string]*model.User),
	}
}

// Create 创建用户
func (r *UserRepository) Create(ctx context.Context, user *model.User) error {
	if user == nil {
		return errors.New("user cannot be nil")
	}

	// 生成唯一ID
	if user.ID == "" {
		user.ID = uuid.New().String()
	}

	// 设置创建时间
	if user.CreatedAt.IsZero() {
		user.CreatedAt = time.Now()
	}

	// 设置更新时间
	user.UpdatedAt = time.Now()

	r.mu.Lock()
	defer r.mu.Unlock()

	// 检查用户名是否已存在
	for _, u := range r.users {
		if u.Username == user.Username {
			return errors.New("username already exists")
		}
		if u.Email == user.Email {
			return errors.New("email already exists")
		}
	}

	// 存储用户
	r.users[user.ID] = user

	r.logger.Debug("Created user",
		zap.String("id", user.ID),
		zap.String("username", user.Username),
	)

	return nil
}

// Update 更新用户
func (r *UserRepository) Update(ctx context.Context, user *model.User) error {
	if user == nil {
		return errors.New("user cannot be nil")
	}

	if user.ID == "" {
		return errors.New("user ID cannot be empty")
	}

	r.mu.Lock()
	defer r.mu.Unlock()

	// 检查用户是否存在
	_, exists := r.users[user.ID]
	if !exists {
		return errors.New("user not found")
	}

	// 检查用户名是否已被其他用户使用
	for id, u := range r.users {
		if id != user.ID && u.Username == user.Username {
			return errors.New("username already exists")
		}
		if id != user.ID && u.Email == user.Email {
			return errors.New("email already exists")
		}
	}

	// 设置更新时间
	user.UpdatedAt = time.Now()

	// 更新用户
	r.users[user.ID] = user

	r.logger.Debug("Updated user",
		zap.String("id", user.ID),
		zap.String("username", user.Username),
	)

	return nil
}

// Delete 删除用户
func (r *UserRepository) Delete(ctx context.Context, userID string) error {
	if userID == "" {
		return errors.New("user ID cannot be empty")
	}

	r.mu.Lock()
	defer r.mu.Unlock()

	// 检查用户是否存在
	_, exists := r.users[userID]
	if !exists {
		return errors.New("user not found")
	}

	// 删除用户
	delete(r.users, userID)

	r.logger.Debug("Deleted user",
		zap.String("id", userID),
	)

	return nil
}

// FindByID 根据ID查找用户
func (r *UserRepository) FindByID(ctx context.Context, userID string) (*model.User, error) {
	if userID == "" {
		return nil, errors.New("user ID cannot be empty")
	}

	r.mu.RLock()
	defer r.mu.RUnlock()

	user, exists := r.users[userID]
	if !exists {
		return nil, nil
	}

	// 返回用户的副本
	return r.cloneUser(user), nil
}

// FindByUsername 根据用户名查找用户
func (r *UserRepository) FindByUsername(ctx context.Context, username string) (*model.User, error) {
	if username == "" {
		return nil, errors.New("username cannot be empty")
	}

	r.mu.RLock()
	defer r.mu.RUnlock()

	for _, user := range r.users {
		if user.Username == username {
			// 返回用户的副本
			return r.cloneUser(user), nil
		}
	}

	return nil, nil
}

// FindByEmail 根据邮箱查找用户
func (r *UserRepository) FindByEmail(ctx context.Context, email string) (*model.User, error) {
	if email == "" {
		return nil, errors.New("email cannot be empty")
	}

	r.mu.RLock()
	defer r.mu.RUnlock()

	for _, user := range r.users {
		if user.Email == email {
			// 返回用户的副本
			return r.cloneUser(user), nil
		}
	}

	return nil, nil
}

// ExistsByUsername 检查用户名是否存在
func (r *UserRepository) ExistsByUsername(ctx context.Context, username string) (bool, error) {
	if username == "" {
		return false, errors.New("username cannot be empty")
	}

	r.mu.RLock()
	defer r.mu.RUnlock()

	for _, user := range r.users {
		if user.Username == username {
			return true, nil
		}
	}

	return false, nil
}

// ExistsByEmail 检查邮箱是否存在
func (r *UserRepository) ExistsByEmail(ctx context.Context, email string) (bool, error) {
	if email == "" {
		return false, errors.New("email cannot be empty")
	}

	r.mu.RLock()
	defer r.mu.RUnlock()

	for _, user := range r.users {
		if user.Email == email {
			return true, nil
		}
	}

	return false, nil
}

// FindBySiteID 查找指定站点的所有用户
func (r *UserRepository) FindBySiteID(ctx context.Context, siteID string, offset, limit int) ([]*model.User, int, error) {
	if siteID == "" {
		return nil, 0, errors.New("site ID cannot be empty")
	}

	r.mu.RLock()
	defer r.mu.RUnlock()

	var users []*model.User
	for _, user := range r.users {
		if user.SiteID == siteID {
			users = append(users, r.cloneUser(user))
		}
	}

	// 计算总数
	total := len(users)

	// 分页
	if offset >= total {
		return []*model.User{}, total, nil
	}

	end := offset + limit
	if end > total {
		end = total
	}

	return users[offset:end], total, nil
}

// cloneUser 克隆用户对象
func (r *UserRepository) cloneUser(user *model.User) *model.User {
	if user == nil {
		return nil
	}

	clone := *user

	// 深拷贝切片
	if user.Roles != nil {
		clone.Roles = make([]string, len(user.Roles))
		copy(clone.Roles, user.Roles)
	}

	// 深拷贝指针
	if user.DeletedAt != nil {
		deletedAt := *user.DeletedAt
		clone.DeletedAt = &deletedAt
	}

	return &clone
} 