// 封装下拉菜单切换逻辑
function toggleDropdown(id) {
    const submenu = document.querySelector(`${id} .submenu`);
    if (submenu) {
        submenu.classList.toggle('hidden');
    }
}
function bindNavbarEvents() {
    // 事件委托处理动态加载的元素
    document.addEventListener('click', function (e) {
        // 处理下拉菜单点击
        if (e.target.closest('#userDropdown')) {
            toggleDropdown('#userDropdown');
        } else if (e.target.closest('#languageDropdown')) {
            toggleDropdown('#languageDropdown');
        }

        // 处理语言切换
        if (e.target.matches('[data-lang]')) {
            const lang = e.target.getAttribute('data-lang');
            const button = document.querySelector('#languageDropdown button img');
            if (button) {
                button.src = lang === 'zh' ? './assets/images/cn.jpg' : './assets/images/en.jpg';
            }
            e.target.closest('.submenu')?.classList.add('hidden');
        }
    });

    // 全屏切换功能
    const fullscreenToggle = document.getElementById('fullscreenToggle');
    if (fullscreenToggle) {
        fullscreenToggle.addEventListener('click', function () {
            const icon = this.querySelector('i');
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen()
                    .then(() => {
                        icon.className = 'fas fa-compress text-lg';
                        this.title = '退出全屏';
                    })
                    .catch(err => console.error('全屏请求失败:', err));
            } else {
                document.exitFullscreen()
                    .then(() => {
                        icon.className = 'fas fa-expand text-lg';
                        this.title = '进入全屏';
                    })
                    .catch(err => console.error('退出全屏失败:', err));
            }
        });
    }
}

document.addEventListener('DOMContentLoaded', function () {
    const navbarContainer = document.getElementById('topNavbar');
    if (!navbarContainer) return;

    // 监听动态加载的元素
    const observer = new MutationObserver(() => {
        bindNavbarEvents();
        observer.disconnect(); // 仅执行一次
    });
    observer.observe(navbarContainer, { childList: true });
});