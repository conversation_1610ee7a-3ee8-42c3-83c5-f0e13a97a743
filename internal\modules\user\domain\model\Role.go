/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: {current_date}
 * @LastEditors: Clion Nieh
 * @LastEditTime: {current_date}
 * @FilePath: internal/modules/user/domain/model/Role.go
 * @Description: Defines the data model for a user role.
 *
 * © {current_year} GACMS. All rights reserved.
 */
package model

import "time"

// UserType defines the type of user a role can be applied to.
type UserType string

const (
	AdminUser  UserType = "admin"
	MemberUser UserType = "member"
)

// Role represents a user role within a specific site.
type Role struct {
	ID        uint      `gorm:"primaryKey"`
	SiteID    uint      `gorm:"not null;uniqueIndex:idx_site_name_type"`
	Name      string    `gorm:"not null;size:255;uniqueIndex:idx_site_name_type"`
	UserType  UserType  `gorm:"not null;type:enum('admin','member');uniqueIndex:idx_site_name_type"`
	CreatedAt time.Time
	UpdatedAt time.Time

	// Relationships
	Permissions []*Permission `gorm:"many2many:role_permissions;"`
} 