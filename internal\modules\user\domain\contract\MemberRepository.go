/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2024-07-16
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2024-07-16
 * @FilePath: internal/modules/user/domain/contract/MemberRepository.go
 * @Description: 前台会员仓库接口
 *
 * © 2024 GACMS. All rights reserved.
 */

package contract

import (
	"gacms/internal/modules/user/domain/model"
	"gacms/pkg/database"

	"github.com/gin-gonic/gin"
)

type MemberRepository interface {
	Create(ctx *gin.Context, member *model.Member) error
	GetByUsername(ctx *gin.Context, siteID uint64, username string) (*model.Member, error)
	GetByEmail(ctx *gin.Context, siteID uint64, email string) (*model.Member, error)
	GetByPhone(ctx *gin.Context, siteID uint64, phone string) (*model.Member, error)
	GetByID(ctx *gin.Context, id uint64) (*model.Member, error)
	Update(ctx *gin.Context, member *model.Member) error
	Delete(ctx *gin.Context, id uint64) error
	List(ctx *gin.Context, siteID uint64, options *database.ListOptions) ([]*model.Member, int64, error)

	// Social Identity related methods
	CreateSocialIdentity(ctx *gin.Context, identity *model.SocialIdentity) error
	GetSocialIdentity(ctx *gin.Context, provider string, openID string) (*model.SocialIdentity, error)
	GetSocialIdentitiesByMemberID(ctx *gin.Context, memberID uint64) ([]*model.SocialIdentity, error)
} 