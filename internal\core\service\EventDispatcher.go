/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/EventDispatcher.go
 * @Description: 事件分发器实现
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"errors"
	"sync"

	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// DefaultEventDispatcher 是 contract.EventDispatcher 接口的默认实现
type DefaultEventDispatcher struct {
	handlers        map[contract.EventName][]contract.EventHandler
	handlerMap      map[string][]contract.EventName
	defaultStrategy contract.EventDispatchStrategy
	logger          *zap.Logger
	mu              sync.RWMutex
}

// DefaultEventDispatcherParams 定义了创建 DefaultEventDispatcher 所需的参数
type DefaultEventDispatcherParams struct {
	fx.In

	Logger *zap.Logger
}

// NewDefaultEventDispatcher 创建一个新的 DefaultEventDispatcher 实例
func NewDefaultEventDispatcher(params DefaultEventDispatcherParams) contract.EventDispatcher {
	return &DefaultEventDispatcher{
		handlers:        make(map[contract.EventName][]contract.EventHandler),
		handlerMap:      make(map[string][]contract.EventName),
		defaultStrategy: contract.SyncDispatch, // 默认使用同步策略
		logger:          params.Logger,
	}
}

// Dispatch 将事件分发给所有注册的处理器
func (d *DefaultEventDispatcher) Dispatch(event contract.Event) error {
	if event == nil {
		return errors.New("event cannot be nil")
	}

	d.mu.RLock()
	handlers, exists := d.handlers[event.Name()]
	d.mu.RUnlock()

	if !exists || len(handlers) == 0 {
		d.logger.Debug("No handlers registered for event",
			zap.String("event", string(event.Name())),
		)
		return nil
	}

	d.logger.Info("Dispatching event",
		zap.String("event", string(event.Name())),
		zap.Int("handler_count", len(handlers)),
	)

	// 同步分发事件到所有处理器
	for _, handler := range handlers {
		if err := handler.Handle(event); err != nil {
			d.logger.Error("Error handling event",
				zap.String("event", string(event.Name())),
				zap.String("handler", handler.HandlerName()),
				zap.Error(err),
			)
			return err
		}
	}

	return nil
}

// DispatchAsync 异步分发事件
func (d *DefaultEventDispatcher) DispatchAsync(ctx context.Context, event contract.Event) <-chan error {
	errChan := make(chan error, 1)

	if event == nil {
		errChan <- errors.New("event cannot be nil")
		close(errChan)
		return errChan
	}

	d.mu.RLock()
	handlers, exists := d.handlers[event.Name()]
	d.mu.RUnlock()

	if !exists || len(handlers) == 0 {
		d.logger.Debug("No handlers registered for event",
			zap.String("event", string(event.Name())),
		)
		close(errChan)
		return errChan
	}

	d.logger.Info("Async dispatching event",
		zap.String("event", string(event.Name())),
		zap.Int("handler_count", len(handlers)),
	)

	go func() {
		defer close(errChan)

		// 使用WaitGroup等待所有处理器完成
		var wg sync.WaitGroup
		wg.Add(len(handlers))

		// 为每个处理器创建一个goroutine
		for _, handler := range handlers {
			go func(h contract.EventHandler) {
				defer wg.Done()

				// 检查上下文是否已取消
				select {
				case <-ctx.Done():
					errChan <- ctx.Err()
					return
				default:
					// 继续处理
				}

				// 尝试处理事件
				if err := h.Handle(event); err != nil {
					d.logger.Error("Error async handling event",
						zap.String("event", string(event.Name())),
						zap.String("handler", h.HandlerName()),
						zap.Error(err),
					)
					errChan <- err
				}
			}(handler)
		}

		// 等待所有处理器完成
		wg.Wait()
	}()

	return errChan
}

// RegisterHandler 注册一个事件处理器
func (d *DefaultEventDispatcher) RegisterHandler(handler contract.EventHandler) error {
	if handler == nil {
		return errors.New("handler cannot be nil")
	}

	d.mu.Lock()
	defer d.mu.Unlock()

	// 获取处理器支持的事件
	events := handler.SupportedEvents()
	if len(events) == 0 {
		return errors.New("handler does not support any events")
	}

	// 注册处理器到每个支持的事件
	for _, eventName := range events {
		d.handlers[eventName] = append(d.handlers[eventName], handler)
		d.logger.Debug("Registered handler for event",
			zap.String("event", string(eventName)),
			zap.String("handler", handler.HandlerName()),
		)
	}

	// 记录处理器支持的事件
	d.handlerMap[handler.HandlerName()] = events

	return nil
}

// UnregisterHandler 取消注册一个事件处理器
func (d *DefaultEventDispatcher) UnregisterHandler(handler contract.EventHandler) error {
	if handler == nil {
		return errors.New("handler cannot be nil")
	}

	d.mu.Lock()
	defer d.mu.Unlock()

	handlerName := handler.HandlerName()
	events, exists := d.handlerMap[handlerName]
	if !exists {
		return errors.New("handler not registered")
	}

	// 从每个事件的处理器列表中移除该处理器
	for _, eventName := range events {
		handlers := d.handlers[eventName]
		for i, h := range handlers {
			if h.HandlerName() == handlerName {
				// 移除处理器
				d.handlers[eventName] = append(handlers[:i], handlers[i+1:]...)
				break
			}
		}

		// 如果该事件没有处理器了，删除该事件的映射
		if len(d.handlers[eventName]) == 0 {
			delete(d.handlers, eventName)
		}
	}

	// 删除处理器映射
	delete(d.handlerMap, handlerName)

	d.logger.Debug("Unregistered handler",
		zap.String("handler", handlerName),
	)

	return nil
}

