/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/port/cli/DisableModule.go
 * @Description: Defines the 'module:disable' command.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package cli

import (
	"fmt"
	"gacms/internal/modules/system/application/service"

	"github.com/spf13/cobra"
)

// NewDisableModuleCmd creates the 'module:disable' command.
func NewDisableModuleCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "module:disable [moduleName]",
		Short: "Disable a module.",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			moduleService := service.NewModuleCommandService()
			moduleName := args[0]
			if err := moduleService.Disable(moduleName); err != nil {
				return fmt.Errorf("failed to disable module '%s': %w", moduleName, err)
			}
			fmt.Printf("Module '%s' disabled successfully.\n", moduleName)
			return nil
		},
	}
	return cmd
} 