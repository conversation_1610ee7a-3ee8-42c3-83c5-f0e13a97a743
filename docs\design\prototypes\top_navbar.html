<!--
© 2025 Clion Nieh. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

GACMS 顶部导航栏组件
功能包括：logo、搜索栏、语言切换、全屏切换、通知信息、用户头像操作下拉菜单、日夜主题切换、侧边栏切换
-->
<nav class="top-navbar w-full flex items-center">
    <div class="flex items-center justify-between w-full max-w-full">
        <!-- 菜单切换按钮 -->
        <button class="menu-toggle text-gray-400">
            <i class="fas fa-bars"></i>
        </button>

        <!-- 搜索框 -->
        <div class="search-container flex-1 max-w-md hidden sm:block">
            <div class="relative">
                <input type="text" 
                        class="search-input w-full pl-4 pr-12 py-1 rounded-2xl focus:outline-none" 
                        placeholder="搜索内容、用户、设置...">
                <button class="search-btn absolute right-1 top-1 bottom-1 px-3 rounded-md">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
        
        <!-- 右侧：功能按钮组 -->
        <div class="flex items-center space-x-2 ml-auto h-16">
            <!-- 语言切换 -->
            <div class="relative" id="languageDropdown" class="has-submenu">
                <button class="nav-btn p-2 rounded-lg flex items-center" title="语言切换">
                    <img src="./assets/images/cn.jpg" 
                        alt="中文" 
                        class="w-5 inline-block">
                </button>
                <div class="submenu absolute right-0 top-12 w-36 rounded-lg py-2 hidden">
                    <a href="#" class="dropdown-item block px-4 py-2 text-white text-sm flex items-center" data-lang="zh">
                        <img src="./assets/images/cn.jpg" 
                            alt="中文" 
                            class="w-4 inline-block">
                            <span class="ml-1">中文</span>
                    </a>
                    <a href="#" class="dropdown-item block px-4 py-2 text-white text-sm flex items-center" data-lang="en">
                        <img src="./assets/images/en.jpg" 
                            alt="English" 
                            class="w-4 inline-block">
                            <span class="ml-1">English</span>
                    </a>
                </div>
            </div>
            
            <!-- 全屏切换 -->
            <button id="fullscreenToggle" class="fullscreen-btn nav-btn p-2 rounded-lg" title="全屏切换">
                <i class="fas fa-expand text-lg"></i>
            </button>
            
            <!-- 通知信息 -->
            <div class="relative">
                <button class="nav-btn p-2 rounded-lg relative" title="通知">
                    <i class="fas fa-bell text-lg"></i>
                    <span class="notification-badge absolute w-3 h-3 rounded-full">3</span>
                </button>
            </div>
            
            <!-- 用户头像下拉菜单 -->
            <div class="relative" id="userDropdown" class="has-submenu">
                <div class="nav-btn p-1 rounded-lg">
                    <img src="./assets/images/avatar.jpg" 
                            alt="用户头像" 
                            class="w-8 h-8 rounded-full inline-block object-cover">
                            <span>管理员</span>
                            <i class="fas fa-chevron-down"></i>
                </div>
                <div class="submenu absolute right-0 top-12 w-48 rounded-lg py-2 hidden">
                    <a href="user_profile.html" class="dropdown-item block px-4 py-2 text-white">
                        <i class="fas fa-user mr-2"></i>个人资料
                    </a>
                    <a href="user_set.html" class="dropdown-item block px-4 py-2 text-white">
                        <i class="fas fa-cog mr-2"></i>账号设置
                    </a>
                    <div class="border-t border-gray-600 mt-2 pt-2">
                        <a href="login.html" class="dropdown-item block px-4 py-2 text-red-400">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</nav>