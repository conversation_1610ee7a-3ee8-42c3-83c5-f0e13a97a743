{"name": "Default Theme", "version": "1.0.0", "description": "The base theme that all other themes should inherit from.", "author": "GACMS Team", "extends": null, "layouts": {"default": {"name": "Default Layout", "description": "Standard two-column layout.", "file": "layouts/default.html", "placeholders": ["header", "sidebar", "content", "footer"]}, "homepage": {"name": "Homepage Layout", "description": "Layout for the main homepage.", "file": "layouts/homepage.html", "placeholders": ["hero", "main_content", "footer"]}}, "templates": {"post": {"name": "Post Template", "description": "Template for displaying a single blog post.", "layout": "default", "file": "templates/post.html"}, "page": {"name": "Page Template", "description": "Template for displaying a static page.", "layout": "default", "file": "templates/page.html"}}, "blocks": {"header": {"name": "Site Header", "description": "The main header block, usually contains logo and navigation.", "file": "blocks/header.html"}, "footer": {"name": "Site Footer", "description": "The main footer block, usually contains copyright and links.", "file": "blocks/footer.html"}}, "components": {"button": {"name": "<PERSON><PERSON>", "description": "A standard clickable button.", "file": "components/button.html"}, "card": {"name": "Card", "description": "A content container.", "file": "components/card.html"}}, "settings": [{"name": "main_color", "label": "Main Color", "type": "color", "default": "#3498db"}, {"name": "logo_url", "label": "Logo URL", "type": "image", "default": "/assets/images/logo.png"}, {"name": "primary_color", "label": "Primary Color", "type": "color", "default": "#007bff"}, {"name": "footer_text", "label": "Footer Text", "type": "text", "default": "© 2025 GACMS. All rights reserved."}]}