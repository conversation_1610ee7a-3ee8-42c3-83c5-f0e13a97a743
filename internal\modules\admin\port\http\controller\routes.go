package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/your-project/auth"
)

func (h *AdminController) RegisterRoutes(rg *gin.RouterGroup) {
	siteGroup := rg.Group("/sites")
	siteGroup.Use(authMiddleware.RequirePermission("system:sites:manage"))
	{
		siteGroup.GET("", siteController.GetSites)
		siteGroup.POST("", siteController.CreateSite)
		siteGroup.GET("/:id", siteController.GetSite)
		siteGroup.PUT("/:id", siteController.UpdateSite)
		siteGroup.DELETE("/:id", siteController.DeleteSite)
		
		// Installed themes for a site
		installedThemesGroup := siteGroup.Group("/:siteId/installed-themes")
		{
			installedThemesGroup.GET("", themeController.GetInstalledThemesForSite)
			installedThemesGroup.POST("/:themeName", themeController.InstallThemeForSite)
			installedThemesGroup.DELETE("/:themeName", themeController.UninstallThemeForSite)
		}
	}

	themeGroup := rg.Group("/themes")
	themeGroup.Use(authMiddleware.RequirePermission("system:sites:manage"))
	{
		themeGroup.GET("", themeController.GetThemes)
		themeGroup.GET("/:themeName", themeController.GetThemeByName)
		themeGroup.GET("/:themeName/files", themeController.ListThemeFiles)
	}

	themeEditorGroup := rg.Group("/themes")
	themeEditorGroup.Use(authMiddleware.RequirePermission("system:themes:edit"))
	{
		themeEditorGroup.GET("/:themeName/files/*filePath", themeController.GetThemeFileContent)
		themeEditorGroup.PUT("/:themeName/files/*filePath", themeController.UpdateThemeFileContent)
		themeEditorGroup.DELETE("/:themeName/files/*filePath", themeController.DeleteThemeFile)
	}
} 