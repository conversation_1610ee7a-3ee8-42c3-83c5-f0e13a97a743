/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/admin/module.go
 * @Description: Defines the DI bindings for the admin module.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package admin

import (
	"gacms/internal/modules/admin/application/service"
	"gacms/internal/modules/admin/port/http/controller"
	pkgContract "gacms/pkg/contract"
	"go.uber.org/fx"
)

const ModuleName = "admin"

// Module is the standard fx dependency injection module for the admin package.
var Module = fx.Options(
	// Application Services
	fx.Provide(service.New),

	// HTTP Controller (provided for routing using standard pattern)
	fx.Provide(controller.New),

	// Routable Registration using standard pattern
	fx.Provide(
		fx.Annotate(
			func(c *controller.AdminController) pkgContract.RoutableRegistration {
				return pkgContract.RoutableRegistration{
					Target:   pkgContract.AdminRoute,
					Routable: c,
				}
			},
			fx.ResultTags(`group:"routables"`),
		),
	),
)