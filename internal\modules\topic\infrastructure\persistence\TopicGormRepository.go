/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/topic/infrastructure/persistence/TopicGormRepository.go
 * @Description: GORM implementation of the TopicRepository.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package persistence

import (
	"context"
	"gacms/internal/modules/topic/domain/contract"
	"gacms/internal/modules/topic/domain/model"
	dbContract "gacms/pkg/contract"
)

type TopicGormRepository struct {
	dbService dbContract.Database
}

func NewTopicGormRepository(dbService dbContract.Database) contract.TopicRepository {
	return &TopicGormRepository{dbService: dbService}
}

func (r *TopicGormRepository) Create(ctx context.Context, topic *model.Topic) error {
	return r.dbService.DB(ctx).Create(topic).Error
}

func (r *TopicGormRepository) GetBySlug(ctx context.Context, slug string) (*model.Topic, error) {
	var topic model.Topic
	// The site_id is automatically applied by the r.dbService.DB(ctx) call.
	err := r.dbService.DB(ctx).Where("slug = ?", slug).First(&topic).Error
	if err != nil {
		return nil, err
	}
	return &topic, nil
} 