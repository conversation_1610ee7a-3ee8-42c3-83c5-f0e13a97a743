# GACMS 高保真 HTML 原型

本项目是 GACMS (Go Advanced Content Management System) 的高保真 HTML 原型实现。

## 项目概览

这些原型旨在提供 GACMS 后台管理界面的视觉和基础交互参考。所有界面均采用暗黑主题风格，并优先考虑桌面端 Web 应用的体验。

原型通过一个主入口页面 `prototypes/index.html` 进行统一展示，该页面使用 `<iframe>` 嵌入了所有独立的界面 HTML 文件，并采用纵向排列方式，方便滚动查看。

## 已实现的核心界面

原型文件位于 `prototypes/` 目录下，包括：

*   `login.html`: 用户登录页面
*   `dashboard.html`: 系统仪表盘
*   `article_list.html`: 文章列表管理
*   `article_edit.html`: 文章编辑/新建
*   `site_settings.html`: 站点配置
*   `theme_management.html`: 主题管理
*   `plugin_management.html`: 插件管理
*   `user_management.html`: 用户管理
*   `developer_tools.html`: 开发者工具

## 技术栈

*   **HTML5**: 页面结构
*   **Tailwind CSS (v3 via CDN)**: UI 样式和布局
*   **Font Awesome (v6.5.1 via CDN)**: 图标库
*   **JavaScript (Vanilla JS)**: 简单的交互效果和 `index.html` 的动态加载逻辑
*   **图片资源**: 主要来自 [Unsplash](https://unsplash.com/) 和 [Pexels](https://pexels.com/) (已在 HTML 注释中注明来源)

## 主要设计规范参考

详细的设计规范（颜色、字体、布局、组件等）请参考 `specs/Design_Spec.md`。

## 用户操作流程参考

核心用户操作流程图请参考 `Flowchart.md`。

## 如何查看原型

1.  克隆或下载本仓库。
2.  直接在浏览器中打开 `design/prototypes/index.html` 文件即可。

## 注意事项

*   这些是静态 HTML 原型，主要用于 UI/UX 展示，不包含后端逻辑。
*   部分交互（如表单提交、复杂数据操作）仅为视觉模拟。
*   图片资源直接从 CDN 或外部链接加载，请确保网络连接正常。