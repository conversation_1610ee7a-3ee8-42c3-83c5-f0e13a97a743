<!--
Author: <PERSON><PERSON> Nieh
EMAIL: <EMAIL>
Copyright (c) 2025 Cion Nieh
-->
# 系统设计文档 (SDD) - GACMS

## 目录

- [1. 文档信息](#1-文档信息)
    - [1.1 版本历史](#11-版本历史)
    - [1.2 文档目的](#12-文档目的)
    - [1.3 相关文档引用](#13-相关文档引用)
- [2. 系统概述](#2-系统概述)
    - [2.1 系统目标](#21-系统目标)
    - [2.2 设计原则](#22-设计原则)
- [3. 架构设计](#3-架构设计)
    - [3.1 总体架构](#31-总体架构)
    - [3.2 技术架构](#32-技术架构)
    - [3.3 应用架构](#33-应用架构)
    - [3.4 数据架构](#34-数据架构)
- [4. 模块设计](#4-模块设计)
    - [4.1 核心模块](#41-核心模块)
        - [4.1.1 内容管理模块](#411-内容管理模块)
        - [4.1.2 用户管理模块](#412-用户管理模块)
        - [4.1.3 主题管理模块](#413-主题管理模块)
        - [4.1.4 系统设置模块](#414-系统设置模块)
        - [4.1.5 开发者文档模块 (新增)](#415-开发者文档模块-新增)
        - [4.1.6 专题内容管理模块 (新增)](#416-专题内容管理模块-新增)
    - [4.2 扩展模块](#42-扩展模块)
        - [4.2.1 插件管理模块](#421-插件管理模块)
        - [4.2.2 多站点管理模块](#422-多站点管理模块)
        - [4.2.3 API接口模块](#423-api接口模块)
        - [4.2.4 数据分析模块](#424-数据分析模块)
        - [4.2.5 焦点图管理模块 (新增)](#425-焦点图管理模块-新增)
- [5. 接口设计](#5-接口设计)
    - [5.1 API接口](#51-api接口)
    - [5.2 内部接口](#52-内部接口)
    - [5.3 外部接口](#53-外部接口)
- [6. 数据设计](#6-数据设计)
    - [6.1 数据模型](#61-数据模型)
    - [6.2 数据流](#62-数据流)
    - [6.3 数据库设计](#63-数据库设计)
- [7. 安全设计](#7-安全设计)
    - [7.1 认证与授权](#71-认证与授权)
    - [7.2 数据安全](#72-数据安全)
    - [7.3 通信安全](#73-通信安全)
- [8. 性能设计](#8-性能设计)
    - [8.1 性能优化策略](#81-性能优化策略)
    - [8.2 缓存设计](#82-缓存设计)
    - [8.3 并发处理](#83-并发处理)
- [9. 部署设计](#9-部署设计)
    - [9.1 部署架构](#91-部署架构)
    - [9.2 环境配置](#92-环境配置)
    - [9.3 监控与日志](#93-监控与日志)
- [10. 开发指南](#10-开发指南)
    - [10.1 开发规范](#101-开发规范)
    - [10.2 工具与环境](#102-工具与环境)
    - [10.3 调试与测试](#103-调试与测试)

---

## 1. 文档信息

### 1.1 版本历史

| 版本号 | 日期       | 作者     | 变更说明         |
| ------ | ---------- | -------- | ---------------- |
| 0.1.0  | 2025-05-15 | Cion Nieh | 初稿创建，定义基本结构 |
| 0.2.0  | 2025-05-15 | Cion Nieh | 更新文档，准备填充详细设计内容 |
| 0.2.1  | 2025-05-16 | Trae AI  | 修复章节编号错误，确保与目录一致性，补充数据设计章节内容框架。 |

### 1.2 文档目的

本文档旨在详细描述亘安网站内容管理系统 (GACMS) 的系统设计，包括架构设计、模块设计、接口设计、数据设计等各个方面。它是指导系统开发和实现的重要依据，确保系统的设计满足需求规格说明书中定义的各项要求。

### 1.3 相关文档引用

- [需求规格说明书 (RSD) - GACMS](RSD.md)
- [系统架构设计文档 (SADD) - GACMS](SADD.md)
- [技术选型文档 (Technology Selection) - GACMS](Technology_Selection.md)
- [接口设计文档 (Interface Design) - GACMS](Interface_Design.md)
- [数据模型设计文档 (Data Model Design) - GACMS](Data_Model_Design.md)
- [部署架构文档 (Deployment Architecture) - GACMS](Deployment_Architecture.md)
- [性能设计文档 (Performance Design) - GACMS](Performance_Design.md)
- [安全设计文档 (Security Design) - GACMS](Security_Design.md)
- [技术风险评估文档 (Technical Risk Assessment) - GACMS](Technical_Risk_Assessment.md)
- [技术规范文档 (Technical Specification) - GACMS](Technical_Specification.md)

---

## 2. 系统概述

### 2.1 系统目标

本系统设计旨在实现 <mcfile name="RSD.md" path="docs/RSD.md"></mcfile> 中定义的各项业务目标和技术目标。具体而言，系统设计将围绕以下核心目标展开：

-   **高效的内容管理与发布：** 通过灵活的内容模型、可视化的编辑器、强大的版本控制和多渠道发布能力，提升内容生产和管理的效率。
-   **卓越的用户体验：** 提供快速响应的界面、直观的操作流程和个性化的内容展示，满足不同用户角色的需求。
-   **强大的系统扩展性：** 采用模块化和插件化架构，方便未来功能的迭代和第三方服务的集成。
-   **坚实的安全保障：** 从架构层面融入安全设计理念，保障用户数据和系统运行的安全。
-   **优异的系统性能与可用性：** 通过合理的架构设计、缓存策略和负载均衡机制，确保系统在高并发场景下的稳定性和快速响应。
-   **便捷的二次开发与维护：** 提供清晰的API接口、完善的文档和标准化的开发规范，降低二次开发和系统维护的成本。

### 2.2 设计原则

为达成上述系统目标，GACMS的设计将遵循以下核心原则：

-   **模块化与高内聚低耦合 (Modularity & High Cohesion, Low Coupling):**
    -   系统将划分为一系列功能独立、接口清晰的模块。
    -   模块内部功能高度相关（高内聚），模块之间依赖关系最小化（低耦合）。
    -   便于独立开发、测试、部署和维护，提高系统的灵活性和可复用性。

-   **可扩展性与灵活性 (Scalability & Flexibility):**
    -   采用插件式架构，允许开发者方便地添加新功能或修改现有功能，而无需改动核心系统。
    -   API驱动设计，核心功能通过API暴露，方便不同客户端（Web、App、小程序等）的接入和第三方系统的集成。
    -   支持水平扩展和垂直扩展，以应对未来用户量和数据量的增长。

-   **安全性 (Security by Design):**
    -   遵循最小权限原则，对用户和系统组件进行严格的权限控制。
    -   对敏感数据进行加密存储和传输。
    -   内置常见的Web攻击防护机制（如XSS、CSRF、SQL注入防护）。
    -   提供详细的安全日志和审计功能。

-   **高性能与高可用性 (High Performance & High Availability):**
    -   采用多级缓存策略（CDN、应用层缓存、数据库查询缓存、对象缓存）以减少延迟。
    -   优化数据库查询，合理设计索引。
    -   支持负载均衡和故障转移机制，确保系统在部分组件失效时仍能提供服务。
    -   异步处理耗时任务，避免阻塞用户请求。

-   **可维护性与可测试性 (Maintainability & Testability):**
    -   清晰、一致的代码风格和命名规范。
    -   完善的开发文档、API文档和部署文档。
    -   提供全面的单元测试、集成测试和端到端测试支持。
    -   详细的日志记录，方便问题排查和系统监控。

-   **标准化与开放性 (Standardization & Openness):**
    -   遵循业界主流的技术标准和协议（如HTTP/2, RESTful API, OAuth 2.0）。
    -   优先选择成熟、稳定、社区活跃的开源技术和框架。
    -   提供标准化的接口和数据格式，便于与其他系统集成。

-   **用户中心 (User-Centric):**
    -   界面设计简洁直观，操作流程符合用户习惯。
    -   提供丰富的个性化配置选项。
    -   关注不同用户角色的需求和痛点，提供针对性的解决方案。

---

## 3. 架构设计

本章节简要介绍GACMS的架构设计理念。关于GACMS系统架构的详细阐述，包括总体架构风格、高层架构图、技术选型、模块划分、接口设计原则、数据模型以及部署策略等，请参阅 <mcfile name="SADD.md" path="docs/SADD.md"></mcfile> (系统架构设计文档)。

GACMS的核心架构设计原则与 <mcfile name="SADD.md" path="docs/SADD.md"></mcfile> 中描述的一致，旨在构建一个高内聚、低耦合、可扩展、安全可靠的系统。

主要的架构考虑点包括：

-   **分层与模块化:** 系统采用分层架构，清晰划分用户界面层、应用服务层、领域层和基础设施层。同时，通过模块化设计，将系统功能分解为独立的、可复用的模块，如内容管理、用户管理、主题管理等。
-   **技术栈选择:** 后端主要基于Gin (Go语言)，前端选用React。数据库采用PostgreSQL或MySQL，缓存使用Redis。详细的技术选型及其理由见 <mcfile name="Technology_Selection.md" path="docs/Technology_Selection.md"></mcfile>。
-   **应用部署:** 初期采用单体应用架构，并为未来向微服务架构演进预留可能性。
-   **数据管理:** 强调结构化数据、缓存数据和文件数据的分离存储与管理，并关注数据一致性与备份恢复策略。

更深入的架构细节，例如详细的组件交互、数据流图、API设计规范、以及针对非功能性需求的架构支持策略，请参考 <mcfile name="SADD.md" path="docs/SADD.md"></mcfile>。

---

## 4. 模块设计

本章节详细描述GACMS各个核心模块和扩展模块的设计。每个模块的描述将包括其主要职责、核心功能、关键类/组件（如果适用，可以使用简单的类图或组件图表示）、以及与其他模块的交互。

### 4.1 核心模块

核心模块是GACMS系统赖以运行的基础，提供了内容管理的核心能力。详细的模块划分和技术实现请参考 <mcfile name="SADD.md" path="docs/SADD.md"></mcfile> 中的"3.4 模块划分"部分。

本章节将从系统设计的角度描述各个模块的职责、功能和交互关系，与架构设计文档中的模块划分相互补充。

#### 4.1.1 内容管理模块

-   **职责:**
    -   负责内容的创建、编辑、存储、版本控制、发布和检索。
    -   管理内容模型（字段、类型等）、分类、标签等元数据。
    -   处理媒体资源（图片、视频、文件）的上传和管理。
    -   实现内容审核流程。

-   **核心功能:**
    -   **内容模型管理:** 允许管理员自定义内容类型及其字段（如文章、产品、页面）。
    -   **内容CRUD:** 提供内容的创建、读取、更新、删除操作界面和API。
    -   **富文本编辑器:** 集成强大的所见即所得编辑器，支持格式化、媒体插入等。
    -   **版本控制:** 自动记录内容的修改历史，支持版本比较和回滚。
    -   **分类与标签管理:** 允许创建和管理内容的分类体系和标签云。
    -   **媒体库:** 统一管理上传的图片、视频、附件等，支持文件夹管理和搜索。
    -   **内容发布与调度:** 支持立即发布、定时发布、草稿保存等状态。
    -   **内容审核:** 支持多级审核流程，确保内容质量。
    -   **内容搜索:** 提供基于标题、内容、标签等的搜索功能。

-   **关键类/组件 (示例):**
    -   `ContentController`: 处理内容相关的HTTP请求。
    -   `ContentService`: 封装内容管理的核心业务逻辑。
    -   `ContentRepository`: 负责内容数据的持久化和检索。
    -   `ContentModel` (Entity): 代表内容实体。
    -   `FieldType` (Enum/ValueObject): 定义不同类型的自定义字段。
    -   `MediaManager`: 处理媒体文件上传、存储和管理。
    -   `WorkflowEngine`: 驱动内容审核流程。

-   **交互说明:**
    -   与**用户管理模块**交互，获取内容作者信息和权限信息。
    -   与**主题管理模块**交互，将内容数据传递给主题进行渲染。
    -   与**API接口模块**交互，提供内容的CRUD API。
    -   与**基础设施层**交互，进行数据持久化（数据库）、文件存储和缓存操作。

#### 4.1.2 用户管理模块

根据系统设计，用户体系将严格区分后台管理用户和前台网站用户，以实现更精细化的权限控制和数据隔离。

##### 4.1.2.1 后台用户管理 (Backend Users)

-   **职责:**
    -   负责后台管理系统（Admin Panel）的用户账户创建、认证、授权和管理。
    -   管理后台用户的角色和权限体系，基于RBAC模型。
    -   确保后台操作的安全性与合规性。
    -   关联数据表: `backend_users`。

-   **核心功能:**
    -   **用户账户管理:** 管理员可以创建、编辑、禁用、删除后台用户账户。
    -   **登录认证:** 提供安全的后台登录机制，支持用户名/邮箱和密码认证，可集成2FA。
    -   **密码策略:** 强制密码复杂度、定期更换、历史密码限制等。
    -   **角色管理:** 管理员可以创建、编辑、删除后台用户角色（如超级管理员、编辑、运营）。
    -   **权限管理 (RBAC):** 基于角色的访问控制，管理员可以为角色分配细粒度的后台操作权限（如模块访问、功能操作权限）。
    -   **操作日志:** 记录后台用户的关键操作，用于审计和追踪。
    -   **用户会话管理:** 管理后台用户登录状态和会话。

-   **关键类/组件 (示例):**
    -   `AdminAuthController`: 处理后台用户认证相关的HTTP请求。
    -   `BackendUserController`: 处理后台用户管理相关的HTTP请求。
    -   `BackendUserService`: 封装后台用户管理的核心业务逻辑。
    -   `BackendUserRepository`: 负责`backend_users`表的数据持久化和检索。
    -   `BackendUser` (Entity): 代表后台用户实体。
    -   `BackendRole` (Entity): 代表后台角色实体。
    -   `BackendPermission` (Entity/ValueObject): 代表后台操作权限。
    -   `AdminAuthService`: 提供后台认证和授权服务。

-   **交互说明:**
    -   后台各模块（内容管理、主题管理、系统设置等）依赖此模块进行操作权限校验。
    -   与**API接口模块**交互，提供后台用户管理的API（供后台自身使用或特定管理工具）。

#### 4.1.5 开发者文档模块 (新增)
-   **职责:**
    -   提供一个结构化、易于访问的平台，用于展示和管理面向开发者的各类文档。
    -   该模块本质上是一个静态内容展示系统，但集成在后台统一框架下，以保持体验一致性。
-   **核心功能:**
    -   **文档渲染**: 负责解析Markdown或特定格式的文档源文件，并将其渲染为HTML页面。
    -   **导航生成**: 自动根据文档目录结构或配置文件生成多级导航菜单（左侧主导航、页面内导航）。
    -   **代码高亮**: 集成代码高亮库（如`highlight.js`），自动美化代码片段。
-   **关键类/组件 (示例):**
    -   `DocsController`: 处理文档页面的HTTP请求，根据路由加载相应的文档内容。
    -   `MarkdownParser`: 解析Markdown文件，转换为HTML。
    -   `NavGenerator`: 生成文档导航结构。
-   **交互说明:**
    -   与**主题管理模块**交互，使用后台的统一主题框架进行页面渲染。
    -   不直接与其他业务模块交互，是一个相对独立的展示模块。

##### 4.1.2.2 前台用户管理 (Frontend Users)

-   **职责:**
    -   负责网站前台用户的账户创建、认证、授权和管理。
    -   支持多种注册和登录方式，包括第三方社交平台登录。
    -   管理前台用户的基本资料和轻量级权限（如用户分组、内容访问权限）。
    -   关联数据表: `frontend_users`。

-   **核心功能:**
    -   **用户注册:** 提供用户友好的注册流程，支持邮箱、手机号注册。
    -   **用户登录:** 支持用户名/邮箱/手机号和密码登录，可集成2FA。
    -   **第三方登录:** 支持通过OAuth 2.0等协议集成第三方平台登录（如微信、GitHub、Google等）。
    -   **密码管理:** 支持密码加密存储、密码找回/重置、密码修改。
    -   **用户资料管理:** 允许前台用户查看和修改个人公开信息、头像、联系方式等。
    -   **轻量级角色/分组 (可选):** 支持将前台用户划分为不同组别（如普通用户、VIP用户），并赋予不同的内容访问权限或站点特权。
    -   **用户会话管理:** 管理前台用户登录状态和会话。
    -   **隐私设置:** 允许用户控制个人数据的可见性。

-   **关键类/组件 (示例):**
    -   `FrontendAuthController`: 处理前台用户认证相关的HTTP请求。
    -   `FrontendUserController`: 处理前台用户管理相关的HTTP请求。
    -   `FrontendUserService`: 封装前台用户管理的核心业务逻辑。
    -   `FrontendUserRepository`: 负责`frontend_users`表的数据持久化和检索。
    -   `FrontendUser` (Entity): 代表前台用户实体。
    -   `FrontendUserGroup` (Entity, 可选): 代表前台用户分组。
    -   `FrontendAuthService`: 提供前台认证和授权服务。

-   **交互说明:**
    -   网站前台功能（如评论、收藏、个性化推荐）依赖此模块进行用户身份识别和权限判断。
    -   与**内容管理模块**交互，根据用户身份控制内容可见性或提供个性化内容。
    -   与**API接口模块**交互，提供前台用户相关的API（供前端应用、移动端调用）。
    -   与**基础设施层**交互，进行数据持久化和缓存操作。
    -   与第三方认证服务（如OAuth提供商）集成。

#### 4.1.3 主题管理模块

-   **职责:**
    -   负责网站前端界面的展示和定制。
    -   管理主题的安装、切换、配置和开发。

-   **核心功能:**
    -   **主题发现与安装:** 允许管理员浏览、上传、安装新主题。
    -   **主题切换:** 支持一键切换网站当前使用的主题。
    -   **主题配置:** 提供主题自定义选项，如颜色、布局、Logo等。
    -   **模板引擎:** 解析主题模板文件，渲染动态内容。
    -   **静态资源管理:** 处理主题相关的CSS、JavaScript、图片等静态文件。
    -   **主题开发规范:** 提供主题开发的标准和API，方便开发者创建新主题。
    -   **小部件/区块管理 (可选):** 允许在主题的特定区域放置动态内容块。

-   **关键类/组件 (示例):**
    -   `ThemeController`: 处理主题管理相关的后台请求。
    -   `ThemeService`: 封装主题管理的核心业务逻辑。
    -   `ThemeManager`: 负责主题的加载、解析和渲染。
    -   `TemplateEngine`: (如 Block, Twig, Smarty) 模板解析和渲染核心。
    -   `AssetManager`: 管理主题的静态资源。

-   **交互说明:**
    -   与**内容管理模块**交互，获取需要在前端展示的内容数据。
    -   与**系统设置模块**交互，获取网站全局配置信息。
    -   与**用户界面层**紧密集成，负责最终页面的生成。

#### 4.1.4 系统设置模块

-   **职责:**
    -   负责管理GACMS系统的全局配置项。
    -   提供系统维护和诊断功能。

-   **核心功能:**
    -   **基本设置:** 网站名称、Logo、备案信息、默认语言、时区等。
    -   **内容设置:** 默认编辑器、评论设置、URL结构（固定链接）等。
    -   **邮件服务配置:** SMTP服务器设置，用于发送系统邮件。
    -   **缓存设置:** 缓存驱动选择和配置。
    -   **安全设置:** 如登录尝试限制、CSRF保护开关等。
    -   **备份与恢复配置:** 自动备份策略设置。
    -   **系统信息查看:** Go版本、数据库版本、服务器信息等。
    -   **日志管理:** 查看和管理系统运行日志、错误日志。
    -   **国际化与本地化设置:** 管理多语言支持和翻译。

-   **关键类/组件 (示例):**
    -   `SettingController`: 处理系统设置相关的HTTP请求。
    -   `SettingService`: 封装系统设置的业务逻辑。
    -   `SettingRepository`: 负责配置数据的持久化和检索。
    -   `ConfigManager`: 提供统一的配置读取和写入接口。

-   **交互说明:**
    -   几乎所有其他模块都会读取系统设置模块提供的配置信息。
    -   与**基础设施层**交互，存储和读取配置数据。

#### 4.1.6 专题内容管理模块 (新增)

-   **职责:**
    -   负责专题（Topic）的完整生命周期管理，包括创建、编辑、存储和删除。
    -   管理专题与内容实体（如文章、页面）之间的关联关系。
    -   为前端和API提供专题数据服务。

-   **核心功能:**
    -   **专题CRUD**: 提供后台界面和API，用于创建、读取、更新和删除专题。每个专题包含标题、描述、封面图、URL别名等元数据。
    -   **内容关联管理**: 提供一个多对多（M:N）的关联机制，允许将多个内容项（`contents`）添加到一个专题中，也允许一个内容项属于多个专题。提供后台界面用于管理这些关联。
    -   **专题排序与内容排序**: 支持对专题列表进行排序，并支持对专题内部的内容进行手动排序。
    -   **数据接口**: 提供RESTful API接口，用于获取专题列表（可分页、筛选）和单个专题的详细信息（包括其关联的内容列表）。

-   **关键类/组件 (示例):**
    -   `TopicController`: 处理专题管理相关的HTTP请求，负责输入验证和调用服务层。
    -   `TopicService`: 封装专题管理的核心业务逻辑，如创建专题、关联内容、处理排序等。
    -   `TopicRepository`: 负责专题数据及专题-内容关联数据的持久化和检索。
    -   `Topic` (Entity): 代表专题实体，对应 `topics` 表。
    -   `TopicContent` (Entity): 代表专题与内容的关联实体，对应 `topic_content_relations` 表，存储专题ID和内容ID的对应关系及排序信息。

-   **交互说明:**
    -   与**内容管理模块**交互，在关联内容时，需要查询内容模块以获取可选的内容列表。
    -   与**用户管理模块**交互，以确定当前操作者是否具有管理专题的权限。
    -   与**API接口模块**交互，暴露专题管理的RESTful API。
    -   与**数据库**交互，对 `topics` 和 `topic_content_relations` 表进行读写操作。
    -   **主题管理模块**将通过API获取专题数据，并在前端页面进行渲染。

### 4.2 扩展模块

扩展模块为GACMS提供了更丰富的功能和更强的适应性，它们通常是可选的，可以根据实际需求启用或禁用。

#### 4.2.1 插件管理模块

-   **职责:**
    -   提供一个标准的机制来扩展GACMS的核心功能。
    -   管理插件的生命周期（安装、启用、禁用、卸载、更新）。
    -   提供插件开发的API和规范。

-   **核心功能:**
    -   **插件发现与安装:** 允许管理员浏览官方或第三方插件市场，或上传插件包进行安装。
    -   **插件启用/禁用:** 管理员可以控制插件的激活状态。
    -   **插件配置:** 如果插件提供可配置项，管理员可以在此进行设置。
    -   **插件更新:** 检查并安装插件的新版本。
    -   **钩子 (Hooks) 和事件 (Events) 系统:** 插件通过监听系统定义的钩子和事件来注入自定义逻辑，实现对核心功能的扩展，而无需修改核心代码。
    -   **插件隔离:** 确保插件之间的运行相对独立，一个插件的错误不应影响核心系统或其他插件的稳定性（尽力而为）。
    -   **插件依赖管理:** 处理插件之间的依赖关系。

-   **关键类/组件 (示例):**
    -   `PluginController`: 处理插件管理相关的后台请求。
    -   `PluginManager`: 负责插件的加载、注册、生命周期管理。
    -   `HookRegistry`: 管理系统中所有可用的钩子点。
    -   `EventDispatcher`: 负责事件的派发和监听。
    -   `PluginManifest`: 定义插件的元数据（名称、版本、作者、依赖等）。

-   **交互说明:**
    -   与**应用服务层**和**领域层**交互，通过钩子和事件机制扩展其功能。
    -   可能与**用户界面层**交互，添加新的后台管理菜单或前端展示元素。
    -   可能与**数据库**交互，创建插件所需的数据表。

#### 4.2.2 多站点管理模块

-   **职责:**
    -   允许在单个GACMS实例下管理多个独立的网站。
    -   提供站点间的配置隔离和内容隔离（可选共享）。

-   **核心功能:**
    -   **站点创建与配置:** 管理员可以创建新的站点，并为每个站点配置独立的域名、主题、语言、插件等。
    -   **内容隔离与共享:** 默认情况下，各站点内容独立。可配置部分内容类型或分类在站点间共享。
    -   **用户与权限隔离:** 每个站点可以有独立的用户和权限体系，或者共享主站点的用户体系。
    -   **统一后台管理:** 管理员可以通过一个统一的后台界面管理所有站点，或切换到特定站点的管理视图。
    -   **资源分配与限制 (可选):** 为每个站点设置存储空间、带宽等资源限制。

-   **关键类/组件 (示例):**
    -   `SiteController`: 处理多站点管理相关的后台请求。
    -   `SiteManager`: 负责站点的创建、配置和上下文切换。
    -   `SiteContext`: 存储当前请求所属的站点信息。
    -   `Site` (Entity): 代表一个独立的站点。

-   **交互说明:**
    -   深刻影响**内容管理模块**、**用户管理模块**、**主题管理模块**和**系统设置模块**的行为，使其能够感知并适应多站点环境。
    -   需要修改路由机制，根据域名或路径前缀识别当前站点。

#### 4.2.3 API接口模块

-   **职责:**
    -   提供标准化的RESTful API接口，供前端应用、移动应用、第三方系统等消费GACMS的数据和功能。
    -   管理API的认证、授权和文档。

-   **核心功能:**
    -   **API路由:** API路由不进行版本控制，始终提供最新的API接口。
    -   **请求处理与响应格式化:** 统一处理API请求，返回标准格式的响应 (如JSON)。
    -   **API认证与授权:** 支持多种认证机制（如API Key, OAuth 2.0, JWT），并与用户管理模块集成进行权限校验。
    -   **API限流与监控:** 防止API滥用，监控API调用情况。
    -   **API文档生成:** 自动或半自动生成API文档 (如Swagger/OpenAPI规范)。
    -   **Webhooks支持:** 允许外部系统订阅GACMS中的特定事件，当事件发生时，GACMS通过HTTP POST请求通知外部系统。

-   **关键类/组件 (示例):**
    -   `ApiController` (Base Class): API控制器的基类，处理通用逻辑。
    -   `ApiAuthMiddleware`: 处理API认证和授权的中间件。
    -   `ApiRateLimiter`: 实现API请求限流。
    -   `ApiDocGenerator`: 生成API文档。
    -   `WebhookService`: 管理和触发Webhook事件。

-   **交互说明:**
    -   作为**应用服务层**的对外暴露方式，调用各个业务模块的服务。
    -   与**用户管理模块**紧密集成，进行API的认证和授权。
    -   详细接口设计见 <mcfile name="Interface_Design.md" path="docs/Interface_Design.md"></mcfile>。

#### 4.2.4 数据分析模块 (可选)

-   **职责:**
    -   收集和分析网站的访问数据、用户行为数据、内容互动数据等。
    -   为网站运营者提供数据洞察，辅助决策。

-   **核心功能:**
    -   **数据采集:** 收集页面浏览量 (PV)、独立访客数 (UV)、用户来源、停留时长、跳出率等基础数据。
    -   **内容分析:** 分析热门内容、内容互动情况（评论、分享、点赞）等。
    -   **用户分析:** 用户画像、用户留存分析、用户行为路径分析等。
    -   **数据可视化:** 通过图表、报表等形式展示分析结果。
    -   **自定义报表:** 允许用户根据需求创建自定义的数据报表。
    -   **数据导出:** 支持将分析数据导出为CSV、Excel等格式。
    -   **集成第三方分析工具 (可选):** 如 Google Analytics, Matomo。

-   **关键类/组件 (示例):**
    -   `AnalyticsController`: 处理数据分析相关的后台请求。
    -   `TrackingService`: 负责前端数据埋点和后端数据收集。
    -   `AnalyticsEngine`: 执行数据聚合和分析计算。
    -   `ReportGenerator`: 生成数据报表和图表。

-   **交互说明:**
    -   与**用户界面层**交互，通过JavaScript脚本进行前端数据采集。
    -   与**内容管理模块**和**用户管理模块**交互，获取分析所需的数据维度。
    -   可能需要独立的数据库或数据仓库来存储和处理大量的分析数据。

#### 4.2.5 焦点图管理模块 (新增)

-   **职责:**
    -   负责管理网站中用于展示的轮播焦点图（Slides/Banners）。
    -   提供后台界面，允许运营人员对焦点图进行增删改查及排序。

-   **核心功能:**
    -   **焦点图管理**:
        -   **CRUD**: 支持创建、读取、更新和删除焦点图条目。
        -   **内容字段**: 每个条目包含图片URL、标题、描述、跳转链接、显示顺序、状态（启用/禁用）等字段。
    -   **排序**: 支持通过拖拽或修改排序号的方式调整焦点图的显示顺序。
    -   **数据接口**: 提供API，供前端主题或其他模块调用，以获取焦点图数据列表。

-   **关键类/组件 (示例):**
    -   `SlideController`: 处理后台对焦点图的CRUD请求。
    -   `SlideService`: 封装焦点图管理的业务逻辑。
    -   `SlideRepository`: 负责焦点图数据在数据库中的持久化操作。
    -   `Slide` (Entity): 代表焦点图的数据实体。
    -   `SlideApiController`: 对外提供获取焦点图列表的API端点。

-   **交互说明:**
    -   与**内容管理模块**下的媒体库功能交互，允许用户从媒体库选择图片作为焦点图。
    -   与**API接口模块**紧密集成，暴露数据接口给前端。
    -   与**用户管理模块**交互，进行后台操作的权限验证。
    -   主题开发者将通过调用此模块提供的API来获取数据，并在前台页面上渲染出轮播图效果。

---

## 5. 接口设计

系统的接口设计是确保模块间有效通信、系统与外部世界顺畅交互的关键。GACMS的接口设计遵循清晰、一致、安全和易于使用的原则。

### 5.1 API 接口设计 (面向外部)

GACMS将提供一套标准的RESTful API接口，供前端应用（如SPA、移动App）、第三方系统集成、开发者进行二次开发等场景使用。

-   **设计原则:**
    -   **资源导向 (Resource-Oriented):** API围绕资源进行组织，如内容 (posts)、用户 (users)、分类 (categories) 等。
    -   **HTTP动词:** 正确使用HTTP动词表达操作：
        -   `GET`: 读取资源。
        -   `POST`: 创建新资源。
        -   `PUT`: 完整更新现有资源。
        -   `PATCH`: 部分更新现有资源。
        -   `DELETE`: 删除资源。
    -   **HTTP状态码:** 准确使用HTTP状态码表示请求结果 (如 `200 OK`, `201 Created`, `204 No Content`, `400 Bad Request`, `401 Unauthorized`, `403 Forbidden`, `404 Not Found`, `500 Internal Server Error`)。
    -   **统一数据格式:** 请求体和响应体主要使用JSON格式。

    -   **无状态性:** API请求应是无状态的，服务端不应依赖先前请求的状态。
    -   **幂等性:** 对于`GET`, `PUT`, `DELETE`等操作，应保证幂等性。
    -   **过滤、排序、分页:** 提供灵活的查询参数支持对资源列表进行过滤、排序和分页。
        -   过滤: `GET /api/posts?status=published&author_id=1`
        -   排序: `GET /api/posts?sort=-created_at` (降序), `sort=title` (升序)
        -   分页: `GET /api/posts?page=2&per_page=10`
    -   **HATEOAS (Hypermedia as the Engine of Application State) (可选):** 在响应中包含相关资源的链接，增强API的可发现性。

-   **认证与授权:**
    -   **API Keys:** 为第三方应用或开发者分配API Key进行认证。
    -   **OAuth 2.0 / JWT (JSON Web Tokens):** 为用户授权访问提供更安全的机制，适用于前端应用或移动应用。
    -   所有API请求都必须经过认证和授权检查，确保用户只能访问其有权限操作的资源。

-   **API文档:**
    -   提供详细、准确、易于理解的API文档。
    -   推荐使用Swagger/OpenAPI规范自动生成和维护API文档，方便开发者查阅和测试。

-   **速率限制 (Rate Limiting):** 对API请求进行速率限制，防止滥用和恶意攻击。

-   **错误处理:**
    -   返回统一的错误响应格式，包含错误码、错误信息和详细描述。
    ```json
    {
      "error": {
        "code": "INVALID_PARAMETER",
        "message": "The 'email' field is required.",
        "details": "Validation failed for field: email"
      }
    }
    ```

-   **关键API端点示例 (部分):**
    -   内容管理:
        -   `GET /posts`
        -   `POST /posts`
        -   `GET /posts/{id}`
        -   `PUT /posts/{id}`
        -   `DELETE /posts/{id}`
    -   用户管理:
        -   `POST /users/register`
        -   `POST /auth/login`
        -   `GET /users/me`
    -   分类管理:
        -   `GET /categories`

### 5.2 内部接口设计 (模块间)

模块间的交互是系统协同工作的核心。GACMS将采用灵活且高效的机制来管理模块间的通信和依赖关系，吸收了 `/e:/软件程序/软件仓库/GACMS/docs/old/GACMS系统架构设计文档.md` 中关于模块注册、依赖注入和事件系统的合理设计。

#### 5.2.1 模块注册与发现

-   **模块定义与结构:**
    -   每个模块是一个独立的功能单元，遵循预定义的目录结构，包含自身的控制器、模型、视图、服务、配置文件、路由、事件监听、数据库迁移和语言包等。
    -   模块根目录下包含 `module.json` 文件，定义模块的元数据，如名称、版本、供应商、依赖关系、Go及GACMS版本要求等。
    -   模块ID采用 `<Vendor>_<Name>` 格式 (例如 `Core_Admin`, `Gacms_Blog`, `ThirdParty_Analytics`)。
-   **模块注册表:**
    -   系统在安装或更新模块时，会生成或更新一个全局模块注册表 (例如 `config/modules.yaml` 或类似机制)。
    -   该注册表集中管理所有已安装模块的元数据、启用状态、路径、依赖关系等信息。
    -   系统启动时，会读取此注册表来加载和初始化启用的模块。
-   **模块生命周期管理:**
    -   **安装:** 扫描 `app/code/` 目录，解析 `module.json`，用户选择启用模块，生成注册表条目。
    -   **运行:** 读取注册表，根据依赖关系进行拓扑排序，依次加载和初始化启用的模块（注册路由、事件、服务等）。
    -   **管理 (CLI):** 提供命令行工具进行模块的列出、启用、禁用、安装、卸载等操作，这些操作会更新模块注册表。
-   **核心模块服务 (`ModuleService`):**
    -   负责模块注册表的读写、模块状态查询、依赖解析、模块加载和初始化等核心逻辑。

#### 5.2.2 依赖注入与服务容器

-   **服务注册:**
    -   模块可以定义自己的服务，并通过模块的配置文件或在模块初始化时动态注册到GACMS核心的服务容器中 (Go语言中通常通过依赖注入库或手动管理实现)。
    -   服务应遵循单一职责原则，封装特定的业务逻辑或功能。
-   **依赖注入:**
    -   系统广泛采用构造函数注入和方法注入来管理对象间的依赖关系。
    -   控制器、服务、监听器等组件的依赖项由服务容器自动解析和注入。
    -   这降低了组件间的耦合度，提高了代码的可测试性和可维护性。

#### 5.2.3 事件系统

-   **事件定义与发布:**
    -   模块可以定义和发布特定事件来通知系统其他部分发生了某个动作（例如 `UserRegisteredEvent`, `ContentPublishedEvent`）。
    -   事件本身是一个简单的数据对象，携带与事件相关的信息。
-   **事件监听与处理:**
    -   其他模块可以注册监听器来订阅感兴趣的事件。
    -   当事件被发布时，所有相关的监听器会被触发执行相应的处理逻辑。
    -   事件和监听器可以是同步的，也可以是异步的（通过队列处理）。
    -   事件系统有助于实现模块间的松散耦合和横切关注点的分离（如日志记录、通知发送）。

通过上述机制，GACMS能够构建一个灵活、可扩展的模块化系统，模块之间既能有效协作，又能保持相对独立。

模块间的交互主要通过Go的接口调用、事件驱动机制 (如使用Go的事件库或channel) 以及消息队列（针对异步任务）进行。

-   **设计原则:**
    -   **高内聚低耦合:** 模块应专注于自身职责，减少对其他模块的直接依赖。
    -   **面向接口编程:** 依赖抽象而非具体实现，使用接口定义模块间的契约。
    -   **清晰的职责划分:** 每个接口方法应有明确的单一职责。
    -   **参数对象/DTO (Data Transfer Objects):** 对于复杂参数，使用参数对象封装，提高可读性和可维护性。
    -   **异常处理:** 模块间的调用失败应通过抛出特定异常来通知调用方。

-   **交互方式:**
    -   **同步调用:** 对于需要立即得到结果的交互，直接调用服务类的方法。
        ```go
        // ContentService 调用 UserService
        $user = $this->userService->findUserById($authorId);
        ```
    -   **事件驱动:** 对于解耦合模块间的依赖，可以使用事件机制。一个模块发布事件，其他感兴趣的模块订阅并处理该事件。
        ```go
        // 用户注册成功后，发布 UserRegisteredEvent
        event(new UserRegisteredEvent($user));

        // NotificationService 监听 UserRegisteredEvent，发送欢迎邮件
        class SendWelcomeEmailListener implements ShouldQueue
        {
            public function handle(UserRegisteredEvent $event)
            {
                // send email logic
            }
        }
        ```
    -   **消息队列 (异步任务):** 对于耗时较长或不需要立即响应的操作（如发送邮件、生成报表、处理上传文件），应通过消息队列进行异步处理。
        ```go
        // 在控制器中分发一个处理上传视频的任务
        ProcessUploadedVideo::dispatch($videoPath);

        // ProcessUploadedVideo Job
        class ProcessUploadedVideo implements ShouldQueue
        {
            use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
            protected $videoPath;
            public function __construct($videoPath)
            {
                $this->videoPath = $videoPath;
            }
            public function handle()
            {
                // video processing logic
            }
        }
        ```

### 5.3 外部接口设计 (与第三方服务集成)

GACMS可能需要与多种第三方服务进行集成，如邮件服务、支付网关、社交媒体登录、云存储服务、CDN等。

-   **设计原则:**
    -   **适配器模式 (Adapter Pattern):** 为每个第三方服务创建一个适配器，封装其API调用细节，对系统内部提供统一的接口。
    -   **配置驱动:** 第三方服务的API密钥、端点等配置信息应通过配置文件或环境变量管理，而不是硬编码。
    -   **容错与重试:** 对第三方服务的调用应考虑网络延迟、服务不可用等情况，实现合理的超时、重试机制和熔断机制（可选）。
    -   **日志记录:** 记录与第三方服务交互的关键日志，便于问题排查。
    -   **安全性:** 保护好API密钥等敏感信息，确保与第三方服务通信的安全性（如使用HTTPS）。

-   **集成示例:**
    -   **邮件服务:** 集成如Mailgun, SendGrid, AWS SES。Go生态中有相应的SDK或库可以使用。
    -   **社交媒体登录:** 集成支持OAuth2的Go库，支持微信、GitHub、Google等平台登录。
    -   **云存储:** 集成如AWS S3, Aliyun OSS。Go生态中有相应的SDK或库可以使用。
    -   **支付网关 (如果需要电子商务功能):** 集成如Stripe, PayPal。需要特别关注支付安全和PCI DSS合规性。

详细的API端点定义、请求/响应示例以及数据模型将在 <mcfile name="Interface_Design.md" path="docs/Interface_Design.md"></mcfile> 文档中进一步阐述。

---

## 6. 数据设计

数据是GACMS系统的核心，数据设计的合理性直接影响系统的性能、可扩展性和可维护性。本章节将详细描述GACMS的数据模型、数据流以及数据库设计。

详细的数据模型、实体关系图、以及各模块的核心数据表结构，请参阅独立的 <mcfile name="Data_Model_Design.md" path="docs/Data_Model_Design.md"></mcfile> 文档。

### 6.1 数据模型

GACMS的数据模型将围绕内容、用户、主题、设置等核心概念构建。主要实体及其关系概述如下：

-   **用户 (User):** 包含用户基本信息、角色、权限等。
-   **内容 (Content):** 包含文章、页面、产品等不同类型的内容，支持自定义字段。
-   **分类 (Category):** 用于组织内容，支持层级结构。
-   **标签 (Tag):** 用于标记内容，支持多对多关系。
-   **媒体 (Media):** 存储图片、视频、附件等文件信息。
-   **主题 (Theme):** 包含主题信息、配置等。
-   **插件 (Plugin):** 包含插件信息、状态等。
-   **设置 (Setting):** 存储系统全局配置和模块配置。

更详细的实体属性和关系定义见 <mcfile name="Data_Model_Design.md" path="docs/Data_Model_Design.md"></mcfile>。

### 6.2 数据流

数据流描述了数据在系统中的产生、处理、存储和消费过程。例如：

-   **内容创建流程:** 用户在后台创建内容 -> 数据经过校验和处理 -> 存储到数据库 -> 内容发布后，前端读取数据进行展示。
-   **用户注册流程:** 用户提交注册信息 -> 系统校验数据 -> 创建用户记录并存储 -> 发送验证邮件。

关键数据流图将在 <mcfile name="Data_Model_Design.md" path="docs/Data_Model_Design.md"></mcfile> 中详细展示。

### 6.3 数据库设计

数据库设计包括选择合适的数据库管理系统 (DBMS)、设计表结构、定义索引、规划数据备份与恢复策略等。

-   **DBMS选择:** 优先考虑MySQL或PostgreSQL，具体选型依据见 <mcfile name="Technology_Selection.md" path="docs/Technology_Selection.md"></mcfile>。
-   **表结构设计:** 根据数据模型设计具体的数据库表，遵循数据库设计范式，平衡性能和冗余。
-   **索引策略:** 为常用查询字段和JOIN条件创建索引，优化查询性能。
-   **数据字典:** 详细描述每个表的字段、数据类型、约束、含义等。

详细的数据库表结构、ER图以及数据字典请参考 <mcfile name="Data_Model_Design.md" path="docs/Data_Model_Design.md"></mcfile>。

---

## 7. 安全设计

安全是GACMS设计的核心考量之一，旨在保护系统本身、用户数据以及通过系统管理的内容免受各种威胁。安全设计将贯穿于系统的整个生命周期，从架构设计、代码实现到部署运维。

### 7.1 安全目标

-   **机密性 (Confidentiality):** 确保敏感数据（如用户信息、未发布内容、系统配置）不被未授权访问。
-   **完整性 (Integrity):** 防止数据在存储、传输或处理过程中被未授权篡改。
-   **可用性 (Availability):** 确保系统及其功能在授权用户需要时可用，抵御拒绝服务攻击等威胁。
-   **可追溯性 (Accountability):** 记录关键操作和安全事件，以便审计和追踪。
-   **合规性 (Compliance):** 满足相关法律法规和行业标准的要求（如GDPR、OWASP Top 10）。

### 7.2 威胁模型与风险评估

我们将采用STRIDE模型（Spoofing, Tampering, Repudiation, Information Disclosure, Denial of Service, Elevation of Privilege）进行威胁建模，识别潜在的攻击向量和安全风险。针对识别的风险，进行评估并制定相应的缓解措施。

主要关注的威胁包括但不限于：

-   **Web应用漏洞:** SQL注入、跨站脚本 (XSS)、跨站请求伪造 (CSRF)、文件上传漏洞、不安全的反序列化等。
-   **身份认证与授权缺陷:**弱密码策略、会话管理不当、权限提升等。
-   **数据泄露:** 敏感数据明文存储或传输、访问控制不当等。
-   **拒绝服务攻击 (DoS/DDoS):** 耗尽系统资源，导致服务不可用。
-   **恶意插件/主题:** 第三方扩展可能引入安全漏洞。
-   **供应链攻击:** 依赖的第三方库或服务存在漏洞。

### 7.3 认证与授权 (Authentication & Authorization)

-   **强密码策略:** 要求用户设置复杂密码，定期提醒修改，限制密码重用。
-   **多因素认证 (MFA):** 为管理员和高权限用户提供MFA选项（如TOTP）。
-   **安全的会话管理:** 使用安全的会话Cookie（HttpOnly, Secure, SameSite），设置合理的会话超时时间，提供注销功能。
-   **基于角色的访问控制 (RBAC):** 严格遵循最小权限原则，为不同用户角色分配完成其工作所需的最小权限集。
-   **API认证:** API接口将使用安全的认证机制（如OAuth 2.0, JWT, API Keys），并对每次请求进行严格的授权检查。
-   **登录尝试限制与账户锁定:** 防止暴力破解密码，多次登录失败后临时锁定账户或启用验证码。

### 7.4 数据安全

-   **数据加密:**
    -   **传输中加密:** 全站强制使用HTTPS (TLS/SSL)。
    -   **静态加密:** 对敏感数据（如用户密码、API密钥）进行加密存储（如使用bcrypt进行哈希，对敏感配置使用对称或非对称加密）。
-   **数据备份与恢复:** 定期进行数据备份，并测试恢复流程，确保数据在发生意外时可以恢复。
-   **数据输入验证:** 对所有用户输入进行严格的验证、过滤和清理，防止注入类攻击。
-   **数据输出编码:** 在将数据显示到用户界面时，进行适当的HTML编码，防止XSS攻击。
-   **防止SQL注入:** 优先使用参数化查询或ORM（如GORM）来操作数据库。
-   **文件上传安全:** 限制上传文件的类型、大小，对上传的文件进行病毒扫描（如果可行），存储在非Web可直接访问的目录，并使用随机文件名。

### 7.5 输入验证与输出编码

-   **服务端验证:** 所有用户输入必须在服务端进行严格验证，不能仅依赖客户端验证。
-   **白名单验证:** 优先使用白名单验证输入数据的格式、类型、长度和范围。
-   **上下文相关的输出编码:** 根据数据输出的上下文（HTML body, HTML attribute, JavaScript, CSS, URL）选择合适的编码方法，防止XSS。

### 7.6 安全日志与监控

-   **全面的日志记录:** 记录关键系统事件、安全事件（如登录成功/失败、权限变更、重要配置修改、错误日志）。
-   **日志格式与存储:** 日志应包含时间戳、事件源、事件类型、用户身份等关键信息，并安全存储，防止篡改。
-   **实时监控与告警:** 对可疑活动和安全事件进行实时监控，并设置告警机制，及时通知管理员。
-   **定期审计:** 定期对安全日志进行审计，发现潜在的安全问题。

### 7.7 第三方库与依赖安全

-   **依赖管理:** 使用 Go Modules 管理项目依赖，定期更新依赖库到最新稳定版本。
-   **漏洞扫描:** 使用自动化工具（如NPM Audit, Snyk, Dependabot）扫描项目依赖中的已知漏洞。
-   **最小化依赖:** 仅引入必要的第三方库，减少攻击面。
-   **插件/主题安全审查:** 对引入的第三方插件和主题进行安全审查，评估其安全性。

### 7.8 Web服务器与基础设施安全

-   **安全的服务器配置:** 遵循服务器安全最佳实践，如禁用不必要的服务、最小化权限运行Web服务器进程、配置防火墙等。
-   **操作系统和软件更新:** 及时更新操作系统、Web服务器软件、Go版本、数据库等到最新安全补丁版本。
-   **DDoS防护:** 考虑使用CDN或专业的DDoS防护服务。
-   **Web应用防火墙 (WAF):** 部署WAF以检测和阻止常见的Web攻击。

### 7.9 安全开发生命周期 (SDL)

-   **安全培训:** 对开发团队进行安全意识和安全编码规范的培训。
-   **代码审查:** 将安全作为代码审查的重要环节，检查潜在的安全漏洞。
-   **安全测试:**
    -   **静态应用安全测试 (SAST):** 使用工具分析源代码，发现潜在漏洞。
    -   **动态应用安全测试 (DAST):** 在运行时测试Web应用程序，模拟攻击行为。
    -   **渗透测试:** 定期进行渗透测试，模拟黑客攻击，发现系统弱点。
-   **漏洞管理:**建立漏洞报告和处理流程，及时修复已发现的漏洞。

### 7.10 应急响应计划

-   制定安全事件应急响应计划，明确事件上报、处理、恢复和事后分析的流程和责任人。

---

## 8. 性能设计

性能是衡量GACMS用户体验和系统能力的关键指标。本节将详细描述系统的性能目标、关键性能指标 (KPIs)、性能优化策略以及性能测试计划，确保系统在高并发、大数据量场景下依然能够提供快速、稳定的服务。

### 8.1 性能目标

-   **快速响应:** 确保用户界面和API接口的快速响应，提升用户体验。
-   **高并发处理:** 系统能够稳定处理预期数量的并发用户请求。
-   **高吞吐量:** 系统能够在单位时间内处理大量的业务请求和数据。
-   **资源高效利用:** 合理利用服务器资源（CPU、内存、磁盘I/O、网络带宽），避免资源浪费和瓶颈。
-   **可伸缩性:** 系统架构支持水平和垂直扩展，以应对未来业务增长带来的性能压力。

### 8.2 关键性能指标 (KPIs)

| 指标类别         | KPI 名称                      | 目标值/描述                                       | 备注                                       |
| ---------------- | ----------------------------- | ------------------------------------------------- | ------------------------------------------ |
| **响应时间**     | 平均页面加载时间 (前端)       | < 2 秒 (核心页面)                                 | 影响用户体验的关键指标                     |
|                  | API 平均响应时间              | < 200毫秒 (95%的请求)                             | 影响前后端交互效率                         |
|                  | 后台任务平均处理时间          | 根据具体任务定义 (如：邮件发送 < 5秒)             | 影响系统后台处理效率                       |
| **并发能力**     | 最大并发用户数 (CUV)          | 根据项目初期预估 (如：1000 CUV)                   | 衡量系统在高压力下的稳定性                 |
|                  | 每秒请求数 (RPS) / QPS        | 根据CUV和用户行为模型估算 (如：500 RPS)           | 衡量系统处理请求的能力                     |
| **吞吐量**       | 内容发布吞吐量                | 例如：每分钟可发布100篇文章                       | 衡量核心业务处理能力                       |
|                  | 数据导入/导出速率             | 根据数据量和业务需求定义                          |                                            |
| **资源利用率**   | CPU 平均利用率                | < 70% (正常负载)                                  | 避免CPU成为瓶颈                            |
|                  | 内存平均利用率                | < 80% (正常负载)                                  | 避免内存溢出                               |
|                  | 磁盘I/O等待时间               | 尽可能低                                          | 影响数据读写性能                           |
|                  | 网络带宽利用率                | < 70% (正常负载)                                  | 避免网络拥塞                               |
| **数据库性能**   | 平均SQL查询时间               | < 50毫秒 (常用查询)                               | 慢查询是常见的性能瓶颈                     |
|                  | 数据库连接数                  | 在合理范围内                                      | 过多连接会消耗资源                         |
| **缓存性能**     | 缓存命中率                    | > 90% (对于可缓存数据)                            | 高命中率能显著提升性能                     |

### 8.3 性能优化策略

性能优化将从前端、后端、数据库、缓存、基础设施等多个层面进行。

-   **前端优化:**
    -   **资源压缩与合并:** 压缩HTML, CSS, JavaScript文件；合并CSS和JavaScript文件以减少HTTP请求数。
    -   **图片优化:** 使用合适的图片格式 (如WebP)，压缩图片大小，使用懒加载 (Lazy Loading)。
    -   **CDN加速:** 将静态资源（图片、CSS、JS）部署到CDN，加速用户访问。
    -   **浏览器缓存:** 合理利用HTTP缓存头 (如`Cache-Control`, `Expires`, `ETag`)。
    -   **代码优化:** 减少DOM操作，优化JavaScript执行效率，避免渲染阻塞。
    -   **服务端渲染 (SSR) 或预渲染 (Prerendering) (可选):** 对于内容型网站，可以提升首屏加载速度和SEO。
    -   **代码分割 (Code Splitting):** 按需加载JavaScript模块。

-   **后端优化 (应用层):**
    -   **代码优化:** 优化算法和数据结构，减少不必要的计算和循环，避免N+1查询问题。
    -   **异步处理:** 对于耗时操作（如邮件发送、复杂计算、第三方API调用），使用消息队列进行异步处理。
    -   **连接池:** 合理配置数据库连接池、Redis连接池等，提高连接复用率。
    -   **Go性能优化:** Go语言本身性能较高，编译为本地代码。关注并发模型 (goroutines, channels) 的合理使用，避免不必要的阻塞。
    -   **Gin框架优化:**
        -   Gin框架本身轻量，主要优化点在于业务逻辑和Go代码层面。
        -   (Gin的路由通常在启动时构建，无需额外缓存机制)
        -   (GACMS后端为API服务，不涉及视图编译)
        -   (Go中事件机制通常不需要显式缓存)
        -   使用Octane提升应用性能 (可选)。

-   **数据库优化:**
    -   **索引优化:** 为经常查询的字段创建合适的索引，避免全表扫描。
    -   **SQL查询优化:** 避免复杂的JOIN操作，使用EXPLAIN分析查询计划，优化慢查询。
    -   **读写分离 (可选):** 对于读多写少的场景，可以考虑主从复制和读写分离架构。
    -   **分库分表 (可选):** 对于超大数据量，可以考虑垂直或水平分片。
    -   **数据库连接优化:** 合理配置最大连接数。
    -   **定期维护:** 分析表、优化表、清理碎片。

-   **缓存策略:**
    -   **数据缓存:** 缓存热点数据、计算结果、配置信息等，减少数据库访问压力。使用Redis或Memcached。
    -   **页面缓存/片段缓存:** 缓存静态或不经常变化的页面内容或页面片段。
    -   **对象缓存:** 缓存Go对象，如通过Redis或Go内置缓存库。
    -   **缓存更新策略:** 采用合适的缓存失效和更新策略 (如TTL、主动更新、事件驱动更新)。
    -   **缓存预热:** 系统启动或低峰期预先加载热点数据到缓存。

-   **基础设施优化:**
    -   **负载均衡:** 使用负载均衡器 (如Nginx, HAProxy, ELB) 将流量分发到多个应用服务器实例。
    -   **服务器配置优化:** 优化操作系统内核参数、Web服务器 (Nginx/Apache) 配置。
    -   **水平扩展:** 根据负载情况动态增加或减少应用服务器实例。
    -   **垂直扩展:** 提升单个服务器的硬件配置 (CPU, 内存)。

### 8.4 性能测试计划

性能测试是确保系统满足性能目标的关键环节。

-   **测试类型:**
    -   **基准测试 (Benchmark Testing):** 针对特定模块或功能进行性能评估。
    -   **负载测试 (Load Testing):** 模拟预期用户负载，测试系统在正常负载下的性能表现。
    -   **压力测试 (Stress Testing):** 超出预期负载，测试系统的极限承载能力和稳定性，发现性能瓶颈。
    -   **并发测试 (Concurrency Testing):** 测试系统同时处理多个用户请求的能力。
    -   **稳定性测试/耐力测试 (Soak Testing):** 长时间运行高负载，测试系统的稳定性和是否存在内存泄漏等问题。

-   **测试环境:**
    -   尽可能与生产环境一致或按比例缩放。
    -   独立的性能测试环境，避免影响开发和测试环境。

-   **测试工具:**
    -   **前端性能测试:** Google PageSpeed Insights, WebPageTest, Lighthouse。
    -   **后端/API性能测试:** Apache JMeter, Locust, k6, Siege, wrk。
    -   **数据库性能测试:** sysbench, Percona Toolkit。

-   **测试场景:**
    -   模拟真实用户行为，覆盖核心业务流程。
    -   例如：用户登录、内容浏览、内容发布、后台管理操作等。

-   **测试流程:**
    1.  **定义性能目标和KPIs。**
    2.  **设计测试场景和测试用例。**
    3.  **准备测试环境和测试数据。**
    4.  **执行测试并收集监控数据 (CPU, 内存, 网络, I/O, 响应时间, 错误率等)。**
    5.  **分析测试结果，定位性能瓶颈。**
    6.  **进行性能调优。**
    7.  **重复测试，直到满足性能目标。**

-   **监控指标:**
    -   在性能测试过程中，需要全面监控应用服务器、数据库服务器、缓存服务器以及网络等各个环节的性能指标。
    -   使用APM (Application Performance Monitoring) 工具 (如New Relic, Datadog, SkyWalking, Pinpoint) 进行深度监控和诊断。

性能设计和优化是一个持续的过程，需要在系统开发、测试和运维的各个阶段不断进行。详细的性能测试报告和优化记录将作为项目文档的一部分。

---

## 9. 部署设计

本节将详细描述GACMS的部署架构、部署环境、部署流程、持续集成与持续部署 (CI/CD) 策略以及运维监控方案，确保系统能够稳定、高效地运行，并易于维护和扩展。详细的部署架构图和配置说明请参考 <mcfile name="Deployment_Architecture.md" path="docs/Deployment_Architecture.md"></mcfile>。

### 9.1 部署架构

GACMS的部署架构将根据项目的实际需求和预算，选择合适的方案，兼顾高可用性、可伸缩性和安全性。

-   **单服务器部署 (适用于小型项目或初期阶段):**
    -   Web服务器 (Nginx/Caddy)、Go应用可执行文件、数据库 (PostgreSQL/MySQL)、缓存服务 (Redis) 都部署在同一台物理或虚拟服务器上，或通过容器化部署。
    -   优点：简单、成本低。
    -   缺点：单点故障风险高，扩展性有限。

-   **多服务器分离部署 (推荐方案):**
    -   **Web服务器集群:** 使用多台服务器运行Nginx/Apache，通过负载均衡器 (如Nginx, HAProxy, ELB) 分发用户请求。
    -   **应用服务器集群:** 使用多台服务器运行GACMS的Go应用实例，同样通过负载均衡器分发请求。
    -   **数据库服务器:** 独立的数据库服务器或主从复制集群，实现数据冗余和读写分离。
    -   **缓存服务器集群:** 独立的Redis/Memcached集群，高缓存服务的可用性和性能。
    -   **文件存储:** 可以使用本地文件系统、NFS共享存储或对象存储服务 (如AWS S3, Aliyun OSS)。
    -   优点：高可用性、可伸缩性好、职责分离易于管理。
    -   缺点：架构相对复杂，成本较高。

-   **容器化部署 (推荐，基于Docker & Kubernetes):**
    -   将GACMS的各个组件（Web服务、应用服务、后台任务处理器等）打包成Docker镜像。
    -   使用Kubernetes (K8s) 或类似的容器编排平台进行部署和管理。
    -   数据库和缓存服务也可以容器化部署，或使用云服务商提供的托管服务。
    -   优点：环境一致性、快速部署与回滚、弹性伸缩、资源利用率高、易于实现微服务架构。
    -   缺点：学习曲线较陡峭，需要专业的运维知识。

### 9.2 部署环境

至少需要以下几种环境：

-   **开发环境 (Development):** 开发人员本地开发环境，通常使用Docker Compose或类似的工具快速搭建。
-   **测试环境 (Testing):** 用于功能测试、集成测试和UAT (用户验收测试)，环境配置应尽可能接近生产环境。
-   **预生产环境/演练环境 (Staging):** 与生产环境配置完全一致，用于部署前的最后验证和演练。
-   **生产环境 (Production):** 实际对外提供服务的环境，需要最高的稳定性和安全性保障。

### 9.3 部署流程

1.  **代码构建:**
    -   从版本控制系统 (Git) 拉取最新代码。
    -   构建Go应用 (`go build -o gacms_server .`)。
    -   安装前端依赖 (`npm install`)。
    -   编译前端资源 (`npm run build`)。
    -   (Go应用通常无需此类优化命令，编译即优化)
    -   打包应用 (例如，创建Docker镜像或压缩代码包)。
2.  **配置管理:**
    -   使用环境变量或配置文件管理不同环境的配置项 (数据库连接、API密钥等)。
    -   敏感配置信息应加密存储或使用专门的密钥管理服务。
3.  **数据库迁移:**
    -   在部署新版本前，执行数据库迁移脚本 (如使用 `migrate -path ./migrations -database "$DATABASE_URL" up`)。
4.  **应用部署:**
    -   将构建好的应用包或Docker镜像部署到目标服务器或容器编排平台。
    -   更新Web服务器配置，指向新的应用版本。
5.  **服务启动与健康检查:**
    -   启动应用服务和相关依赖服务。
    -   执行健康检查，确保应用正常运行。
6.  **流量切换 (蓝绿部署/金丝雀发布):**
    -   逐步将用户流量切换到新版本，监控系统稳定性。
    -   如果出现问题，可以快速回滚到旧版本。
7.  **部署后验证:**
    -   进行基本的功能验证和性能监控。

### 9.4 持续集成与持续部署 (CI/CD)

-   **CI (Continuous Integration):**
    -   **工具选择:** Jenkins, GitLab CI/CD, GitHub Actions, Travis CI等。
    -   **流程:** 开发人员提交代码到版本控制系统后，CI服务器自动执行代码构建 (`go build`)、单元测试 (`go test ./...`)、集成测试、代码质量检查 (如 `go vet`, `golint`, `staticcheck`)。
    -   **目标:** 快速发现和修复集成错误，保证代码质量。
-   **CD (Continuous Deployment/Delivery):**
    -   **流程:** CI流程通过后，自动或手动触发部署流程，将应用部署到测试环境、预生产环境，最终到生产环境。
    -   **目标:** 实现快速、可靠、频繁的应用交付。

### 9.5 运维与监控

-   **日志管理:**
    -   集中式日志收集与分析系统 (如ELK Stack - Elasticsearch, Logstash, Kibana; 或EFK Stack - Elasticsearch, Fluentd, Kibana; Grafana Loki)。
    -   记录应用日志、Web服务器日志、系统日志、安全日志等。
-   **应用性能监控 (APM):**
    -   使用APM工具 (如New Relic, Datadog, SkyWalking, Pinpoint, Tideways, Blackfire.io) 监控应用性能指标、追踪分布式请求、发现性能瓶颈。
-   **基础设施监控:**
    -   监控服务器的CPU、内存、磁盘、网络等资源使用情况 (如Prometheus + Grafana, Zabbix, Nagios)。
-   **错误追踪:**
    -   使用错误追踪服务 (如Sentry, Bugsnag) 实时捕获和报告应用错误。
-   **告警机制:**
    -   针对关键性能指标、错误率、资源阈值等设置告警规则，通过邮件、短信、Slack等方式及时通知运维人员。
-   **备份与恢复:**
    -   定期备份数据库和重要文件。
    -   制定并演练数据恢复计划。
-   **安全监控:**
    -   监控安全事件，定期进行安全扫描和审计。

部署设计需要根据项目的具体情况进行调整和优化，确保系统的稳定运行和高效运维。

---

## 10. 开发指南

本开发指南旨在为GACMS项目的开发团队提供一套统一的规范和最佳实践，以确保代码质量、提高开发效率、降低维护成本，并促进团队协作。所有开发人员应严格遵守本指南的要求。详细的技术规范和编码标准请参考 <mcfile name="Technical_Specification.md" path="docs/Technical_Specification.md"></mcfile>。

### 10.1 环境搭建

-   **操作系统:** 推荐使用Linux (如Ubuntu) 或 macOS进行开发，Windows用户可以使用WSL2。
-   **Go版本:** 严格按照项目要求的Go版本 (如Go 1.20+)。推荐使用`gvm`或`asdf`进行版本管理。
-   **Node.js版本:** 严格按照项目要求的前端Node.js版本 (如Node.js 18+)。推荐使用`nvm`或`asdf`进行版本管理。
-   **Go Modules:** 使用Go Modules进行依赖管理。
-   **NPM/Yarn:** 使用NPM或Yarn进行前端依赖管理。
-   **数据库:** 本地安装与生产环境一致的数据库 (MySQL/PostgreSQL)。
-   **缓存服务:** 本地安装Redis或Memcached。
-   **IDE/编辑器:** 推荐使用支持Go开发的IDE，如GoLand或VS Code (配合Go插件)。配置好代码风格检查 (`gofmt`, `goimports`) 和格式化工具。
-   **Docker (推荐):** 使用Docker和Docker Compose搭建统一的本地开发环境，确保环境一致性。项目应提供`docker-compose.yml`文件。

### 10.2 版本控制 (Git)

-   **分支策略:**
    -   `main` (或 `master`): 稳定的主分支，对应生产环境代码，只接受来自`develop`分支的合并 (通过Pull Request/Merge Request)。
    -   `develop`: 开发分支，集成所有已完成的功能和修复，是新功能分支的起点和合并目标。
    -   `feature/<feature-name>`: 功能开发分支，从`develop`创建，完成后合并回`develop`。
    -   `bugfix/<issue-id>`: Bug修复分支，从`develop`或`main`(用于Hotfix)创建，修复后合并回对应分支。
    -   `hotfix/<issue-id>`: 紧急生产Bug修复分支，从`main`创建，修复后同时合并回`main`和`develop`。
    -   `release/<version>`: 发布分支，从`develop`创建，用于准备版本发布（如最后的测试、文档更新），完成后合并到`main`和`develop`，并打上版本标签。
-   **提交规范 (Commit Message):**
    -   遵循Conventional Commits规范 (例如：`feat: add user login functionality`, `fix: resolve issue with image upload`, `docs: update README.md`)。
    -   每次提交应保持原子性，只包含相关的代码变更。
    -   提交前进行代码审查和测试。
-   **代码合并:**
    -   所有向`develop`和`main`分支的合并必须通过Pull Request/Merge Request (PR/MR)。
    -   PR/MR需要至少一名其他开发人员进行Code Review。
    -   PR/MR必须通过所有CI检查 (构建、测试、代码风格)。

### 10.3 编码规范

-   **Go编码规范:**
    -   遵循PSR-12 (Extended Coding Style) 和 PSR-4 (Autoloader) 规范。
    -   使用 `go vet` 进行基础静态代码分析。
    -   使用 `gofmt` 或 `goimports` 进行代码风格格式化。可以使用 `golangci-lint` 集成多种linter进行更全面的检查。
    -   **命名规范:**
        -   类名：大驼峰 (PascalCase)，如 `UserController`。
        -   方法名：小驼峰 (camelCase)，如 `getUserById`。
        -   变量名：小驼峰 (camelCase)，如 `$userName`。
        -   常量：全大写，下划线分隔，如 `MAX_USERS`。
        -   配置文件键名：小写，下划线分隔，如 `database_connection`。
    -   **注释规范:**
        -   函数、类型、重要代码块应有清晰的GoDoc注释。
        -   注释应解释"为什么"而不是"做什么"。
    -   **类型系统:** Go是静态强类型语言，充分利用其类型系统。
    -   **错误处理:** 使用异常处理机制，避免使用`@`抑制错误。
    -   **安全性:** 遵循安全编码实践，防止常见Web漏洞 (XSS, SQL注入, CSRF等)。
-   **前端编码规范 (JavaScript/TypeScript, CSS/SCSS):**
    -   **JavaScript/TypeScript:**
        -   遵循ESLint和Prettier的配置规范。
        -   推荐使用TypeScript以增强代码类型安全。
        -   模块化开发 (ES Modules)。
        -   命名规范：变量和函数使用小驼峰，类和接口使用大驼峰。
    -   **CSS/SCSS:**
        -   遵循Stylelint的配置规范。
        -   推荐使用SCSS/SASS等预处理器。
        -   使用BEM (Block, Element, Modifier) 或类似的CSS命名方法论，避免样式冲突。
        -   组件化CSS，样式应与组件绑定。

### 10.4 测试规范

-   **单元测试 (Unit Tests):**
    -   使用Go标准库的 `testing` 包进行单元测试。
    -   针对类的方法、函数的逻辑进行测试。
    -   测试应覆盖正常情况、边界情况和异常情况。
    -   保持单元测试的独立性和快速执行。
    -   目标代码覆盖率：核心模块 > 80%。
-   **集成测试 (Integration Tests):**
    -   测试模块之间、服务之间的交互。
    -   例如，测试Controller层与Service层、Service层与Repository层的交互。
    -   可以使用Go的HTTP测试工具 (如 `net/http/httptest`) 和数据库测试辅助库。
-   **功能测试/E2E测试 (End-to-End Tests):**
    -   (后端API测试不直接涉及浏览器自动化，前端E2E测试会使用Cypress, Playwright或Selenium等工具)
    -   模拟用户操作，测试完整的业务流程。
-   **API测试:**
    -   使用Postman, Insomnia或代码化的API测试框架 (如Go的 `testing` 包结合 `net/http/httptest`) 测试API接口的正确性、性能和安全性。
-   **测试数据:**
    -   可以使用Go代码或数据库脚本生成测试数据，或使用类似 `go-faker` 的库。
    -   测试完成后应清理测试数据，或使用独立的测试数据库。

### 10.5 代码审查 (Code Review)

-   **目的:** 提高代码质量、发现潜在Bug、知识共享、统一编码风格。
-   **流程:**
    -   开发者完成功能或修复Bug后，创建PR/MR。
    -   几乎一名其他开发者进行审查。
    -   审查者关注代码逻辑、可读性、可维护性、性能、安全性、测试覆盖率等方面。
    -   提出具体的修改建议和问题。
    -   开发者根据反馈修改代码，直至审查通过。
-   **审查清单 (Checklist):** 可以制定团队内部的Code Review Checklist。

### 10.6 文档规范

-   **代码注释:** 遵循GoDoc规范，清晰描述函数、类型、包的功能和用法。
-   **API文档:** 使用Swagger/OpenAPI规范编写和维护API文档。可以使用工具 (如`darkaonline/l5-swagger`) 自动生成。
-   **项目文档:** 及时更新项目相关的设计文档、用户手册、运维手册等 (存储在`docs`目录)。
-   **README.md:** 项目根目录的`README.md`应包含项目简介、环境搭建步骤、启动命令、主要贡献者等信息。

### 10.7 依赖管理

-   **Go依赖 (Go Modules):**
    -   `go.mod` 和 `go.sum` 文件必须纳入版本控制。
    -   定期执行 `go get -u ./...` 或针对特定模块更新依赖，并进行充分测试。
    -   谨慎引入新的依赖，评估其质量、维护性和安全性。
-   **前端依赖 (NPM/Yarn):**
    -   `package.json` 和 `package-lock.json` (或 `yarn.lock`) 文件必须纳入版本控制。
    -   定期更新前端依赖。

### 10.8 数据库变更

-   所有数据库结构变更必须通过数据库迁移工具 (如 `migrate`, `goose`, 或GORM的AutoMigrate) 实现。
-   迁移文件应清晰描述变更内容，并支持回滚 (`down`方法)。
-   避免在迁移文件中进行大量数据操作，如有需要，应使用Seeder或自定义Artisan命令。

### 10.9 安全最佳实践

-   遵循OWASP Top 10等安全指南。
-   对所有用户输入进行验证和清理。
-   使用参数化查询或ORM防止SQL注入。
-   对输出到HTML的内容进行编码，防止XSS。
-   实现CSRF保护。
-   安全处理文件上传。
-   使用HTTPS。
-   保护API密钥和敏感配置信息。
-   定期进行安全审计和漏洞扫描。

### 10.10 性能注意事项

-   避免N+1查询问题，合理使用Eloquent的预加载 (Eager Loading)。
-   优化数据库查询，使用索引。
-   合理使用缓存 (数据缓存、查询缓存、配置缓存、路由缓存、视图缓存)。
-   对于耗时操作，考虑使用队列进行异步处理。
-   优化前端资源加载 (压缩、合并、CDN、懒加载)。

本开发指南将随着项目的进展和团队的成长而不断完善和更新。

---

性能是衡量GACMS系统质量的关键指标，直接影响用户体验和系统的可扩展性。性能设计将关注系统的响应速度、吞吐量、并发处理能力和资源利用率。详细的性能设计方案请参考 <mcfile name="Performance_Design.md" path="docs/Performance_Design.md"></mcfile>。