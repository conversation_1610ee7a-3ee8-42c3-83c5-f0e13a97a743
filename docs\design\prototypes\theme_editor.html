<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 主题编辑器</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <!-- Code<PERSON><PERSON><PERSON><PERSON> CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.5/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.5/theme/monokai.min.css">
    <style>
        .CodeMirror {
            height: calc(100vh - 250px);
            border: 1px solid #374151;
            border-radius: 0.5rem;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: { 500: '#007bff', 600: '#0069d9' },
                        secondary: { 500: '#00c6ff' },
                        dark: { 800: '#1F2937', 900: '#1A1A1A' }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>

        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4 flex flex-col">
            <!-- 顶部操作区 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                 <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white">主题编辑器</h2>
                    <div class="flex items-center space-x-3">
                        <button class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-2 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30">
                            <i class="fas fa-save mr-2"></i>保存文件
                        </button>
                    </div>
                </div>
            </div>

            <div class="flex-1 grid grid-cols-12 gap-6">
                <!-- 文件树 -->
                <div class="col-span-3 bg-gray-800/10 border border-gray-700 rounded-xl p-4">
                    <h3 class="text-lg font-semibold mb-4">主题文件</h3>
                    <ul class="text-gray-300 space-y-2">
                        <li><i class="fas fa-folder text-yellow-500 mr-2"></i> assets</li>
                        <ul class="ml-6 space-y-1 text-sm">
                            <li><i class="fas fa-file-code text-blue-400 mr-2"></i> style.css</li>
                            <li><i class="fas fa-file-code text-yellow-400 mr-2"></i> script.js</li>
                        </ul>
                         <li><i class="fas fa-file-code text-gray-400 mr-2"></i> index.html</li>
                         <li><i class="fas fa-file-code text-gray-400 mr-2"></i> single.html</li>
                         <li><i class="fas fa-file-code text-gray-400 mr-2"></i> page.html</li>
                    </ul>
                </div>

                <!-- 编辑器 -->
                <div class="col-span-9">
                    <textarea id="code-editor"></textarea>
                </div>
            </div>

        </div>

        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <!-- CodeMirror JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.5/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.5/mode/xml/xml.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.5/mode/css/css.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.5/mode/javascript/javascript.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var editor = CodeMirror.fromTextArea(document.getElementById("code-editor"), {
                lineNumbers: true,
                theme: "monokai",
                mode: "xml"
            });
        });
    </script>
</body>
</html> 