/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/modules/user/events/UserEvents.go
 * @Description: 用户模块的事件定义
 * 
 * © 2025 GACMS. All rights reserved.
 */

package events

import (
	"time"

	"gacms/pkg/contract"
)

// 用户模块的事件名称常量
const (
	// UserRegistered 用户注册事件
	UserRegistered contract.EventName = "user.registered"

	// UserActivated 用户激活事件
	UserActivated contract.EventName = "user.activated"

	// UserLoggedIn 用户登录事件
	UserLoggedIn contract.EventName = "user.logged_in"

	// UserLoggedOut 用户登出事件
	UserLoggedOut contract.EventName = "user.logged_out"

	// UserProfileUpdated 用户资料更新事件
	UserProfileUpdated contract.EventName = "user.profile_updated"

	// UserPasswordChanged 用户密码修改事件
	UserPasswordChanged contract.EventName = "user.password_changed"

	// UserPasswordReset 用户密码重置事件
	UserPasswordReset contract.EventName = "user.password_reset"

	// UserDeleted 用户删除事件
	UserDeleted contract.EventName = "user.deleted"

	// UserRoleChanged 用户角色变更事件
	UserRoleChanged contract.EventName = "user.role_changed"
)

// UserRegisteredPayload 用户注册事件的载荷
type UserRegisteredPayload struct {
	UserID    string    `json:"userId"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	CreatedAt time.Time `json:"createdAt"`
	SiteID    string    `json:"siteId"`
}

// UserActivatedPayload 用户激活事件的载荷
type UserActivatedPayload struct {
	UserID       string    `json:"userId"`
	Username     string    `json:"username"`
	ActivatedAt  time.Time `json:"activatedAt"`
	ActivatedBy  string    `json:"activatedBy,omitempty"`
	SiteID       string    `json:"siteId"`
}

// UserLoggedInPayload 用户登录事件的载荷
type UserLoggedInPayload struct {
	UserID    string    `json:"userId"`
	Username  string    `json:"username"`
	LoginAt   time.Time `json:"loginAt"`
	IP        string    `json:"ip"`
	UserAgent string    `json:"userAgent"`
	SiteID    string    `json:"siteId"`
}

// UserLoggedOutPayload 用户登出事件的载荷
type UserLoggedOutPayload struct {
	UserID     string    `json:"userId"`
	Username   string    `json:"username"`
	LogoutAt   time.Time `json:"logoutAt"`
	SessionID  string    `json:"sessionId"`
	SiteID     string    `json:"siteId"`
}

// UserProfileUpdatedPayload 用户资料更新事件的载荷
type UserProfileUpdatedPayload struct {
	UserID      string                 `json:"userId"`
	Username    string                 `json:"username"`
	UpdatedAt   time.Time              `json:"updatedAt"`
	UpdatedBy   string                 `json:"updatedBy"`
	ChangedFields map[string]interface{} `json:"changedFields"`
	SiteID      string                 `json:"siteId"`
}

// UserPasswordChangedPayload 用户密码修改事件的载荷
type UserPasswordChangedPayload struct {
	UserID    string    `json:"userId"`
	Username  string    `json:"username"`
	ChangedAt time.Time `json:"changedAt"`
	ChangedBy string    `json:"changedBy"`
	SiteID    string    `json:"siteId"`
}

// UserPasswordResetPayload 用户密码重置事件的载荷
type UserPasswordResetPayload struct {
	UserID    string    `json:"userId"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	ResetAt   time.Time `json:"resetAt"`
	ResetBy   string    `json:"resetBy,omitempty"`
	ResetToken string   `json:"resetToken,omitempty"`
	SiteID    string    `json:"siteId"`
}

// UserDeletedPayload 用户删除事件的载荷
type UserDeletedPayload struct {
	UserID    string    `json:"userId"`
	Username  string    `json:"username"`
	DeletedAt time.Time `json:"deletedAt"`
	DeletedBy string    `json:"deletedBy"`
	Reason    string    `json:"reason,omitempty"`
	SiteID    string    `json:"siteId"`
}

// UserRoleChangedPayload 用户角色变更事件的载荷
type UserRoleChangedPayload struct {
	UserID     string    `json:"userId"`
	Username   string    `json:"username"`
	OldRoles   []string  `json:"oldRoles"`
	NewRoles   []string  `json:"newRoles"`
	ChangedAt  time.Time `json:"changedAt"`
	ChangedBy  string    `json:"changedBy"`
	SiteID     string    `json:"siteId"`
}