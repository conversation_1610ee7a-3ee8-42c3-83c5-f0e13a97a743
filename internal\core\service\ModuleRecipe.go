/*
 * @Author: <PERSON><PERSON>eh <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/ModuleRecipe.go
 * @Description: 统一的模块Recipe定义，整合状态管理和依赖关系
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"fmt"
	"time"

	"go.uber.org/fx"
)

// ModuleType 模块类型
type ModuleType string

const (
	// CoreModule 核心模块，免费使用，不可停用
	CoreModule ModuleType = "core"
	// ExtensionModule 官方扩展模块，商业授权，可启用停用
	ExtensionModule ModuleType = "extension"
	// ThirdPartyModule 第三方模块，独立许可证管理
	ThirdPartyModule ModuleType = "third_party"
	// PluginModule 插件模块，轻量级扩展
	PluginModule ModuleType = "plugin"
	// ThemeModule 主题模块，界面扩展
	ThemeModule ModuleType = "theme"
)

// ModuleStatus 模块状态
type ModuleStatus int

const (
	ModuleStatusDisabled ModuleStatus = iota // 禁用
	ModuleStatusEnabled                      // 启用但未激活
	ModuleStatusActive                       // 激活运行中
	ModuleStatusError                        // 错误状态
)

// ModuleRecipe 统一的模块Recipe定义
// 整合了原有的ModuleRecipe和ModuleMetadata功能，支持多租户
type ModuleRecipe struct {
	// 基本信息
	Name        string     `json:"name"`
	Version     string     `json:"version"`
	Description string     `json:"description"`
	Author      string     `json:"author"`
	Type        ModuleType `json:"type"`

	// 多租户支持
	SiteID      uint   `json:"site_id,omitempty"`      // 租户ID，0表示全局模块
	IsGlobal    bool   `json:"is_global"`              // 是否为全局模块（所有租户共享）
	TenantScope string `json:"tenant_scope,omitempty"` // 租户作用域：global/tenant/site

	// 依赖关系
	Dependencies []string `json:"dependencies"`

	// fx配置
	Options fx.Option `json:"-"`

	// 运行时状态
	Status    ModuleStatus `json:"status"`
	LoadedAt  *time.Time   `json:"loaded_at,omitempty"`
	ErrorMsg  string       `json:"error_msg,omitempty"`
	Path      string       `json:"path,omitempty"`

	// 许可证管理
	RequiresLicense   bool                   `json:"requires_license"`
	LicenseKey        string                 `json:"license_key,omitempty"`
	IsActivated       bool                   `json:"is_activated"`
	LicenseProvider   string                 `json:"license_provider,omitempty"`   // 许可证提供方：official/third_party
	LicenseValidator  string                 `json:"license_validator,omitempty"`  // 许可证验证器名称
	LicenseMetadata   map[string]interface{} `json:"license_metadata,omitempty"`   // 许可证元数据

	// 元数据
	Tags        []string               `json:"tags,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	ConfigPath  string                 `json:"config_path,omitempty"`
	ManifestURL string                 `json:"manifest_url,omitempty"`
}

// IsCore 判断是否为核心模块
func (r *ModuleRecipe) IsCore() bool {
	return r.Type == CoreModule
}

// IsThirdParty 判断是否为第三方模块
func (r *ModuleRecipe) IsThirdParty() bool {
	return r.Type == ThirdPartyModule || r.Type == PluginModule || r.Type == ThemeModule
}

// IsOfficial 判断是否为官方模块
func (r *ModuleRecipe) IsOfficial() bool {
	return r.Type == CoreModule || r.Type == ExtensionModule
}

// IsPlugin 判断是否为插件
func (r *ModuleRecipe) IsPlugin() bool {
	return r.Type == PluginModule
}

// IsTheme 判断是否为主题
func (r *ModuleRecipe) IsTheme() bool {
	return r.Type == ThemeModule
}

// IsTenantSpecific 判断是否为租户特定模块
func (r *ModuleRecipe) IsTenantSpecific() bool {
	return !r.IsGlobal && r.SiteID > 0
}

// IsGlobalModule 判断是否为全局模块
func (r *ModuleRecipe) IsGlobalModule() bool {
	return r.IsGlobal || r.SiteID == 0
}

// GetTenantScope 获取租户作用域
func (r *ModuleRecipe) GetTenantScope() string {
	if r.TenantScope != "" {
		return r.TenantScope
	}

	if r.IsGlobal {
		return "global"
	} else if r.SiteID > 0 {
		return "site"
	} else {
		return "tenant"
	}
}

// SetTenantScope 设置租户作用域
func (r *ModuleRecipe) SetTenantScope(siteID uint, isGlobal bool) {
	r.SiteID = siteID
	r.IsGlobal = isGlobal

	if isGlobal {
		r.TenantScope = "global"
	} else if siteID > 0 {
		r.TenantScope = "site"
	} else {
		r.TenantScope = "tenant"
	}
}

// BelongsToSite 判断模块是否属于指定站点
func (r *ModuleRecipe) BelongsToSite(siteID uint) bool {
	// 全局模块属于所有站点
	if r.IsGlobal {
		return true
	}
	// 站点特定模块只属于指定站点
	return r.SiteID == siteID
}

// IsEnabled 判断是否启用
func (r *ModuleRecipe) IsEnabled() bool {
	return r.Status == ModuleStatusEnabled || r.Status == ModuleStatusActive
}

// IsActive 判断是否激活
func (r *ModuleRecipe) IsActive() bool {
	return r.Status == ModuleStatusActive
}

// HasError 判断是否有错误
func (r *ModuleRecipe) HasError() bool {
	return r.Status == ModuleStatusError
}

// RequiresActivation 判断是否需要激活（商业授权）
func (r *ModuleRecipe) RequiresActivation() bool {
	return r.RequiresLicense
}

// IsLicenseActivated 判断许可证是否已激活
func (r *ModuleRecipe) IsLicenseActivated() bool {
	return r.IsActivated
}

// CanRun 判断模块是否可以运行
// 综合考虑启用状态、许可证要求和激活状态
func (r *ModuleRecipe) CanRun() bool {
	if !r.IsEnabled() {
		return false
	}

	// 核心模块总是可以运行
	if r.IsCore() {
		return true
	}

	// 如果需要许可证，必须已激活
	if r.RequiresLicense && !r.IsActivated {
		return false
	}

	return true
}

// GetLicenseProvider 获取许可证提供方
func (r *ModuleRecipe) GetLicenseProvider() string {
	if r.LicenseProvider != "" {
		return r.LicenseProvider
	}

	// 根据模块类型推断许可证提供方
	if r.IsOfficial() {
		return "official"
	} else if r.IsThirdParty() {
		return "third_party"
	}

	return "unknown"
}

// UsesOfficialLicense 判断是否使用官方许可证
func (r *ModuleRecipe) UsesOfficialLicense() bool {
	return r.GetLicenseProvider() == "official"
}

// UsesThirdPartyLicense 判断是否使用第三方许可证
func (r *ModuleRecipe) UsesThirdPartyLicense() bool {
	return r.GetLicenseProvider() == "third_party"
}

// StatusString 状态转字符串
func (r *ModuleRecipe) StatusString() string {
	switch r.Status {
	case ModuleStatusDisabled:
		return "disabled"
	case ModuleStatusEnabled:
		return "enabled"
	case ModuleStatusActive:
		return "active"
	case ModuleStatusError:
		return "error"
	default:
		return "unknown"
	}
}

// TypeString 类型转字符串
func (r *ModuleRecipe) TypeString() string {
	return string(r.Type)
}

// SetStatus 设置状态
func (r *ModuleRecipe) SetStatus(status ModuleStatus) {
	r.Status = status
	if status == ModuleStatusActive {
		now := time.Now()
		r.LoadedAt = &now
		r.ErrorMsg = ""
	} else if status != ModuleStatusError {
		r.LoadedAt = nil
		r.ErrorMsg = ""
	}
}

// SetError 设置错误状态
func (r *ModuleRecipe) SetError(err error) {
	r.Status = ModuleStatusError
	r.ErrorMsg = err.Error()
	r.LoadedAt = nil
}

// ClearError 清除错误状态
func (r *ModuleRecipe) ClearError() {
	if r.Status == ModuleStatusError {
		r.Status = ModuleStatusEnabled
		r.ErrorMsg = ""
	}
}

// SetLicense 设置许可证
func (r *ModuleRecipe) SetLicense(licenseKey string, activated bool) {
	r.LicenseKey = licenseKey
	r.IsActivated = activated
	if activated {
		r.ClearError()
	}
}

// SetLicenseWithProvider 设置许可证和提供方
func (r *ModuleRecipe) SetLicenseWithProvider(licenseKey, provider, validator string, activated bool) {
	r.LicenseKey = licenseKey
	r.LicenseProvider = provider
	r.LicenseValidator = validator
	r.IsActivated = activated
	if activated {
		r.ClearError()
	}
}

// ActivateLicense 激活许可证
func (r *ModuleRecipe) ActivateLicense(licenseKey string) {
	r.SetLicense(licenseKey, true)
}

// ActivateThirdPartyLicense 激活第三方许可证
func (r *ModuleRecipe) ActivateThirdPartyLicense(licenseKey, validator string, metadata map[string]interface{}) {
	r.LicenseKey = licenseKey
	r.LicenseProvider = "third_party"
	r.LicenseValidator = validator
	r.LicenseMetadata = metadata
	r.IsActivated = true
	r.ClearError()
}

// DeactivateLicense 停用许可证
func (r *ModuleRecipe) DeactivateLicense() {
	r.IsActivated = false
	// 保留许可证信息，只是标记为未激活
}

// ClearLicense 清除许可证信息
func (r *ModuleRecipe) ClearLicense() {
	r.LicenseKey = ""
	r.LicenseProvider = ""
	r.LicenseValidator = ""
	r.LicenseMetadata = nil
	r.IsActivated = false
}

// Clone 克隆Recipe（不包含fx.Option）
func (r *ModuleRecipe) Clone() *ModuleRecipe {
	clone := &ModuleRecipe{
		Name:         r.Name,
		Version:      r.Version,
		Description:  r.Description,
		Author:       r.Author,
		Type:         r.Type,
		Dependencies: make([]string, len(r.Dependencies)),
		Status:       r.Status,
		ErrorMsg:     r.ErrorMsg,
		Path:         r.Path,
		Tags:         make([]string, len(r.Tags)),
		ConfigPath:   r.ConfigPath,
		ManifestURL:  r.ManifestURL,
	}

	copy(clone.Dependencies, r.Dependencies)
	copy(clone.Tags, r.Tags)

	if r.LoadedAt != nil {
		loadedAt := *r.LoadedAt
		clone.LoadedAt = &loadedAt
	}

	if r.Metadata != nil {
		clone.Metadata = make(map[string]interface{})
		for k, v := range r.Metadata {
			clone.Metadata[k] = v
		}
	}

	return clone
}

// Validate 验证Recipe的有效性
func (r *ModuleRecipe) Validate() error {
	if r.Name == "" {
		return fmt.Errorf("module name is required")
	}
	if r.Version == "" {
		return fmt.Errorf("module version is required")
	}

	// 验证模块类型
	validTypes := []ModuleType{CoreModule, ExtensionModule, ThirdPartyModule, PluginModule, ThemeModule}
	isValidType := false
	for _, validType := range validTypes {
		if r.Type == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		return fmt.Errorf("invalid module type: %s", r.Type)
	}

	// 验证许可证配置
	if r.RequiresLicense {
		if r.LicenseProvider == "" {
			r.LicenseProvider = r.GetLicenseProvider() // 自动推断
		}

		// 第三方模块需要指定验证器
		if r.UsesThirdPartyLicense() && r.LicenseValidator == "" {
			return fmt.Errorf("third party module %s requires license validator", r.Name)
		}
	}

	return nil
}

// NewModuleRecipe 创建新的ModuleRecipe
func NewModuleRecipe(name, version string, moduleType ModuleType, options fx.Option) *ModuleRecipe {
	return &ModuleRecipe{
		Name:         name,
		Version:      version,
		Type:         moduleType,
		Options:      options,
		Status:       ModuleStatusEnabled,
		Dependencies: make([]string, 0),
		Tags:         make([]string, 0),
		Metadata:     make(map[string]interface{}),
	}
}

// NewCoreModuleRecipe 创建核心模块Recipe
func NewCoreModuleRecipe(name, version string, options fx.Option) *ModuleRecipe {
	return NewModuleRecipe(name, version, CoreModule, options)
}

// NewExtensionModuleRecipe 创建扩展模块Recipe
func NewExtensionModuleRecipe(name, version string, options fx.Option) *ModuleRecipe {
	recipe := NewModuleRecipe(name, version, ExtensionModule, options)
	recipe.RequiresLicense = true // 扩展模块默认需要许可证
	recipe.LicenseProvider = "official"
	return recipe
}

// NewThirdPartyModuleRecipe 创建第三方模块Recipe
func NewThirdPartyModuleRecipe(name, version string, options fx.Option) *ModuleRecipe {
	return NewModuleRecipe(name, version, ThirdPartyModule, options)
}

// NewPluginModuleRecipe 创建插件模块Recipe
func NewPluginModuleRecipe(name, version string, options fx.Option) *ModuleRecipe {
	return NewModuleRecipe(name, version, PluginModule, options)
}

// NewThemeModuleRecipe 创建主题模块Recipe
func NewThemeModuleRecipe(name, version string, options fx.Option) *ModuleRecipe {
	return NewModuleRecipe(name, version, ThemeModule, options)
}

// ModuleRecipeBuilder Recipe构建器
type ModuleRecipeBuilder struct {
	recipe *ModuleRecipe
}

// NewModuleRecipeBuilder 创建Recipe构建器
func NewModuleRecipeBuilder(name, version string) *ModuleRecipeBuilder {
	return &ModuleRecipeBuilder{
		recipe: &ModuleRecipe{
			Name:         name,
			Version:      version,
			Type:         ExtensionModule,
			Status:       ModuleStatusEnabled,
			Dependencies: make([]string, 0),
			Tags:         make([]string, 0),
			Metadata:     make(map[string]interface{}),
		},
	}
}

// WithType 设置模块类型
func (b *ModuleRecipeBuilder) WithType(moduleType ModuleType) *ModuleRecipeBuilder {
	b.recipe.Type = moduleType
	return b
}

// WithDescription 设置描述
func (b *ModuleRecipeBuilder) WithDescription(description string) *ModuleRecipeBuilder {
	b.recipe.Description = description
	return b
}

// WithAuthor 设置作者
func (b *ModuleRecipeBuilder) WithAuthor(author string) *ModuleRecipeBuilder {
	b.recipe.Author = author
	return b
}

// WithDependencies 设置依赖
func (b *ModuleRecipeBuilder) WithDependencies(deps ...string) *ModuleRecipeBuilder {
	b.recipe.Dependencies = append(b.recipe.Dependencies, deps...)
	return b
}

// WithTags 设置标签
func (b *ModuleRecipeBuilder) WithTags(tags ...string) *ModuleRecipeBuilder {
	b.recipe.Tags = append(b.recipe.Tags, tags...)
	return b
}

// WithOptions 设置fx选项
func (b *ModuleRecipeBuilder) WithOptions(options fx.Option) *ModuleRecipeBuilder {
	b.recipe.Options = options
	return b
}

// WithPath 设置路径
func (b *ModuleRecipeBuilder) WithPath(path string) *ModuleRecipeBuilder {
	b.recipe.Path = path
	return b
}

// WithMetadata 设置元数据
func (b *ModuleRecipeBuilder) WithMetadata(key string, value interface{}) *ModuleRecipeBuilder {
	if b.recipe.Metadata == nil {
		b.recipe.Metadata = make(map[string]interface{})
	}
	b.recipe.Metadata[key] = value
	return b
}

// Build 构建Recipe
func (b *ModuleRecipeBuilder) Build() *ModuleRecipe {
	return b.recipe
}
