<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

内容模型页面 - 用于管理系统中的内容模型结构
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 内容模型</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .model-card {
            transition: all 0.3s ease;
        }
        
        .model-card:hover {
            transform: translateY(-3px);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">内容模型</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">内容模型管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="addModelBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <i class="fas fa-plus mr-2"></i>
                            新增模型
                        </button>
                        <button class="flex items-center justify-center bg-gradient-to-r from-gray-600 to-gray-700 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg relative overflow-hidden action-button">
                            <i class="fas fa-cog mr-2"></i>
                            批量操作
                        </button>
                    </div>
                </div>
            </div>

            <!-- 内容模型统计卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-6">
                <!-- 总模型数 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-cubes text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">总模型数</div>
                            <div class="text-xl font-semibold text-white">8</div>
                            <div class="text-xs text-blue-400 mt-0.5">本月新增: 2</div>
                        </div>
                    </div>
                </div>
                
                <!-- 自定义模型 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-puzzle-piece text-purple-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">自定义模型</div>
                            <div class="text-xl font-semibold text-white">5</div>
                            <div class="text-xs text-purple-400 mt-0.5">可扩展</div>
                        </div>
                    </div>
                </div>
                
                <!-- 系统模型 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-shield-alt text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">系统模型</div>
                            <div class="text-xl font-semibold text-white">3</div>
                            <div class="text-xs text-green-400 mt-0.5">基础功能</div>
                        </div>
                    </div>
                </div>
                
                <!-- 已发布内容 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-file-alt text-yellow-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">已发布内容</div>
                            <div class="text-xl font-semibold text-white">245</div>
                            <div class="text-xs text-yellow-400 mt-0.5">基于模型</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 内容模型搜索和筛选 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap gap-4">
                    <div class="relative flex-grow sm:flex-grow-0">
                        <select class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white appearance-none pr-10 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all" selected>所有类型</option>
                            <option value="system">系统模型</option>
                            <option value="custom">自定义模型</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </div>
                    </div>
                    
                    <div class="relative flex-grow">
                        <input type="text" placeholder="搜索内容模型..." class="bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2 w-full text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>
            </div>

            <!-- 内容模型列表 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead class="text-gray-400 text-left">
                            <tr>
                                <th class="pb-4 px-2 w-14">
                                    <input type="checkbox" class="w-4 h-4 bg-gray-700 border-gray-600 rounded">
                                </th>
                                <th class="pb-4 px-2">模型名称</th>
                                <th class="pb-4 px-2">类型</th>
                                <th class="pb-4 px-2">标识符</th>
                                <th class="pb-4 px-2">字段数</th>
                                <th class="pb-4 px-2">内容量</th>
                                <th class="pb-4 px-2">创建时间</th>
                                <th class="pb-4 px-2 text-right">操作</th>
                            </tr>
                        </thead>
                        <tbody class="text-gray-300">
                            <!-- 文章模型 -->
                            <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-2">
                                    <input type="checkbox" class="w-4 h-4 bg-gray-700 border-gray-600 rounded">
                                </td>
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <span class="w-8 h-8 rounded bg-blue-500/20 text-blue-400 flex items-center justify-center mr-3">
                                            <i class="fas fa-file-alt"></i>
                                        </span>
                                        <span class="text-white font-medium">文章</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">
                                    <span class="px-2 py-1 rounded-md text-xs bg-green-900/30 text-green-400 border border-green-900">系统</span>
                                </td>
                                <td class="py-4 px-2">article</td>
                                <td class="py-4 px-2">12</td>
                                <td class="py-4 px-2">86</td>
                                <td class="py-4 px-2">2025-01-12</td>
                                <td class="py-4 px-2 text-right">
                                    <div class="flex items-center justify-end space-x-3">
                                        <button class="text-blue-400 hover:text-blue-300" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-yellow-400 hover:text-yellow-300" title="字段管理">
                                            <i class="fas fa-list"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-gray-300" title="复制">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <!-- 页面模型 -->
                            <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-2">
                                    <input type="checkbox" class="w-4 h-4 bg-gray-700 border-gray-600 rounded">
                                </td>
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <span class="w-8 h-8 rounded bg-yellow-500/20 text-yellow-400 flex items-center justify-center mr-3">
                                            <i class="fas fa-file"></i>
                                        </span>
                                        <span class="text-white font-medium">页面</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">
                                    <span class="px-2 py-1 rounded-md text-xs bg-green-900/30 text-green-400 border border-green-900">系统</span>
                                </td>
                                <td class="py-4 px-2">page</td>
                                <td class="py-4 px-2">10</td>
                                <td class="py-4 px-2">14</td>
                                <td class="py-4 px-2">2025-01-12</td>
                                <td class="py-4 px-2 text-right">
                                    <div class="flex items-center justify-end space-x-3">
                                        <button class="text-blue-400 hover:text-blue-300" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-yellow-400 hover:text-yellow-300" title="字段管理">
                                            <i class="fas fa-list"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-gray-300" title="复制">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <!-- 产品模型 -->
                            <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-2">
                                    <input type="checkbox" class="w-4 h-4 bg-gray-700 border-gray-600 rounded">
                                </td>
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <span class="w-8 h-8 rounded bg-purple-500/20 text-purple-400 flex items-center justify-center mr-3">
                                            <i class="fas fa-shopping-cart"></i>
                                        </span>
                                        <span class="text-white font-medium">产品</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">
                                    <span class="px-2 py-1 rounded-md text-xs bg-purple-900/30 text-purple-400 border border-purple-900">自定义</span>
                                </td>
                                <td class="py-4 px-2">product</td>
                                <td class="py-4 px-2">18</td>
                                <td class="py-4 px-2">45</td>
                                <td class="py-4 px-2">2025-02-15</td>
                                <td class="py-4 px-2 text-right">
                                    <div class="flex items-center justify-end space-x-3">
                                        <button class="text-blue-400 hover:text-blue-300" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-yellow-400 hover:text-yellow-300" title="字段管理">
                                            <i class="fas fa-list"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-gray-300" title="复制">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        <button class="text-red-400 hover:text-red-300" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <!-- 活动模型 -->
                            <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-2">
                                    <input type="checkbox" class="w-4 h-4 bg-gray-700 border-gray-600 rounded">
                                </td>
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <span class="w-8 h-8 rounded bg-red-500/20 text-red-400 flex items-center justify-center mr-3">
                                            <i class="fas fa-calendar-check"></i>
                                        </span>
                                        <span class="text-white font-medium">活动</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">
                                    <span class="px-2 py-1 rounded-md text-xs bg-purple-900/30 text-purple-400 border border-purple-900">自定义</span>
                                </td>
                                <td class="py-4 px-2">event</td>
                                <td class="py-4 px-2">16</td>
                                <td class="py-4 px-2">12</td>
                                <td class="py-4 px-2">2025-03-05</td>
                                <td class="py-4 px-2 text-right">
                                    <div class="flex items-center justify-end space-x-3">
                                        <button class="text-blue-400 hover:text-blue-300" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-yellow-400 hover:text-yellow-300" title="字段管理">
                                            <i class="fas fa-list"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-gray-300" title="复制">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        <button class="text-red-400 hover:text-red-300" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="mt-6 flex justify-between items-center flex-wrap">
                    <div class="text-sm text-gray-400 mb-4 md:mb-0">
                        显示 <b>1-4</b> 共 <b>8</b> 条
                    </div>
                    <div class="flex">
                        <a href="#" class="flex items-center justify-center px-3 py-2 bg-gray-700 text-gray-300 rounded-l-lg hover:bg-gray-600">
                            <i class="fas fa-chevron-left text-xs"></i>
                        </a>
                        <a href="#" class="flex items-center justify-center px-4 py-2 bg-blue-600 text-white">1</a>
                        <a href="#" class="flex items-center justify-center px-4 py-2 bg-gray-700 text-gray-300 hover:bg-gray-600">2</a>
                        <a href="#" class="flex items-center justify-center px-3 py-2 bg-gray-700 text-gray-300 rounded-r-lg hover:bg-gray-600">
                            <i class="fas fa-chevron-right text-xs"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 页面底部版权信息 -->
            <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
                © 2025 GACMS 后台管理系统 - 版本 v1.0.0
            </footer>
        </div>
    </main>

    <!-- JavaScript 导入区域 -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化事件
            document.getElementById('addModelBtn').addEventListener('click', function() {
                alert('新增内容模型功能将在此实现');
            });

            // 表格行复选框全选切换
            const mainCheckbox = document.querySelector('thead input[type="checkbox"]');
            const rowCheckboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            
            if (mainCheckbox && rowCheckboxes.length) {
                mainCheckbox.addEventListener('change', function() {
                    rowCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                });
            }
        });
    </script>
</body>
</html>
