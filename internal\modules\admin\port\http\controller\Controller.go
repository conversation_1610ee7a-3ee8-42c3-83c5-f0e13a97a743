/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/admin/port/http/controller/Controller.go
 * @Description: HTTP controller for the admin module.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package controller

import (
	"gacms/internal/modules/admin/application/service"
	coreSvc "gacms/internal/core/service"
	"gacms/pkg/contract"
	"net/http"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// AdminController handles HTTP requests for admin backend operations.
type AdminController struct {
	adminService          *service.Service
	menuController        *MenuController
	vendorsController     *VendorsController
	performanceController *PerformanceController
	licenseController     *LicenseController
	editionController     *EditionController
}

// New creates a new instance of AdminController.
func New(
	adminService *service.Service,
	permissionChecker contract.Permission<PERSON><PERSON><PERSON>,
	vendorsManager coreSvc.VendorsModuleManager,
	configManager coreSvc.ModuleConfigManager,
	perfManager coreSvc.ModulePerformanceManager,
	preloadManager coreSvc.PreloadStrategyManager,
	moduleFactory *coreSvc.ModuleProxyFactory,
	licenseManager contract.LicenseManager,
	editionManager coreSvc.EditionManager,
	logger *zap.Logger,
) *AdminController {
	return &AdminController{
		adminService:          adminService,
		menuController:        NewMenuController(permissionChecker),
		vendorsController:     NewVendorsController(vendorsManager, configManager, logger),
		performanceController: NewPerformanceController(perfManager, preloadManager, moduleFactory, logger),
		licenseController:     NewLicenseController(licenseManager, moduleFactory, logger),
		editionController:     NewEditionController(editionManager, logger),
	}
}

// RegisterRoutes implements the pkgContract.IRoutable interface.
// It registers the admin module's routes under the provided group.
func (c *AdminController) RegisterRoutes(group *gin.RouterGroup) {
	// The "/api/admin" prefix is handled by the core router.
	// This controller just needs to register its specific endpoints relative to that group.

	// 注册菜单管理路由
	c.menuController.RegisterRoutes(group)

	// 注册第三方模块管理路由
	c.vendorsController.RegisterRoutes(group)

	// 注册性能监控路由
	c.performanceController.RegisterRoutes(group)

	// 注册许可证管理路由
	c.licenseController.RegisterRoutes(group)

	// 注册版本管理路由
	c.editionController.RegisterRoutes(group)

	// 保留原有的仪表板路由
	group.GET("/dashboard", c.GetDashboardStats)
}

// GetDashboardStats handles the request for fetching dashboard statistics.
// @Summary Get Dashboard Statistics
// @Description Retrieves aggregated statistics for the admin dashboard.
// @Tags Admin
// @Produce  json
// @Success 200 {object} response.Response{data=map[string]interface{}}
// @Failure 500 {object} response.Response
// @Router /admin/dashboard [get]
func (c *AdminController) GetDashboardStats(ctx *gin.Context) {
	stats, err := c.adminService.GetDashboardStats()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get dashboard stats"})
		return
	}
	ctx.JSON(http.StatusOK, stats)
}