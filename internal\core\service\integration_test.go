/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: <PERSON><PERSON> Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/integration_test.go
 * @Description: 事件系统集成测试
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"testing"
	"time"

	"gacms/internal/core/bus"
	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// UserRegisteredEvent 用户注册事件
type UserRegisteredEvent struct {
	*bus.BaseEvent
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	Email    string `json:"email"`
}

// NewUserRegisteredEvent 创建用户注册事件
func NewUserRegisteredEvent(ctx context.Context, userID uint, username, email string) contract.Event {
	payload := map[string]interface{}{
		"user_id":  userID,
		"username": username,
		"email":    email,
	}
	
	baseEvent := bus.NewBaseEvent(ctx, "user.registered", payload)
	return &UserRegisteredEvent{
		BaseEvent: baseEvent.(*bus.BaseEvent),
		UserID:    userID,
		Username:  username,
		Email:     email,
	}
}

// EmailNotificationHandler 邮件通知处理器
type EmailNotificationHandler struct {
	sentEmails []string
}

// NewEmailNotificationHandler 创建邮件通知处理器
func NewEmailNotificationHandler() *EmailNotificationHandler {
	return &EmailNotificationHandler{
		sentEmails: make([]string, 0),
	}
}

// Handle 处理事件
func (h *EmailNotificationHandler) Handle(event contract.Event) error {
	if event.Name() == "user.registered" {
		// 模拟发送欢迎邮件
		if payload, ok := event.Payload().(map[string]interface{}); ok {
			if email, exists := payload["email"]; exists {
				h.sentEmails = append(h.sentEmails, email.(string))
			}
		}
	}
	return nil
}

// SupportedEvents 返回支持的事件类型
func (h *EmailNotificationHandler) SupportedEvents() []contract.EventName {
	return []contract.EventName{"user.registered"}
}

// HandlerName 返回处理器名称
func (h *EmailNotificationHandler) HandlerName() string {
	return "email-notification-handler"
}

// GetSentEmails 获取已发送的邮件
func (h *EmailNotificationHandler) GetSentEmails() []string {
	return h.sentEmails
}

// AuditLogHandler 审计日志处理器
type AuditLogHandler struct {
	logs []string
}

// NewAuditLogHandler 创建审计日志处理器
func NewAuditLogHandler() *AuditLogHandler {
	return &AuditLogHandler{
		logs: make([]string, 0),
	}
}

// Handle 处理事件
func (h *AuditLogHandler) Handle(event contract.Event) error {
	logEntry := "Event: " + string(event.Name()) + " at " + event.Timestamp().Format(time.RFC3339)
	h.logs = append(h.logs, logEntry)
	return nil
}

// SupportedEvents 返回支持的事件类型
func (h *AuditLogHandler) SupportedEvents() []contract.EventName {
	return []contract.EventName{"user.registered", "user.updated", "user.deleted"}
}

// HandlerName 返回处理器名称
func (h *AuditLogHandler) HandlerName() string {
	return "audit-log-handler"
}

// GetLogs 获取日志
func (h *AuditLogHandler) GetLogs() []string {
	return h.logs
}

// TestEventSystemIntegration 测试事件系统集成
func TestEventSystemIntegration(t *testing.T) {
	logger := zap.NewNop()

	// 创建所有组件
	eventBus := NewDefaultEventBus(DefaultEventBusParams{Logger: logger})
	handlerRegistry := NewDefaultEventHandlerRegistry(DefaultEventHandlerRegistryParams{Logger: logger})
	serializer := NewDefaultEventSerializer(DefaultEventSerializerParams{Logger: logger})
	eventStore := NewDefaultEventStore(DefaultEventStoreParams{
		Serializer: serializer,
		Logger:     logger,
	})
	eventDispatcher := NewDefaultEventDispatcher(DefaultEventDispatcherParams{Logger: logger})
	
	eventManager := NewDefaultEventManager(DefaultEventManagerParams{
		EventBus:        eventBus,
		HandlerRegistry: handlerRegistry,
		Logger:          logger,
	})

	// 创建事件处理器
	emailHandler := NewEmailNotificationHandler()
	auditHandler := NewAuditLogHandler()

	// 注册事件处理器
	err := eventManager.RegisterHandler(emailHandler)
	if err != nil {
		t.Fatalf("Failed to register email handler: %v", err)
	}

	err = eventManager.RegisterHandler(auditHandler)
	if err != nil {
		t.Fatalf("Failed to register audit handler: %v", err)
	}

	// 创建用户注册事件
	ctx := context.Background()
	userEvent := NewUserRegisteredEvent(ctx, 1, "testuser", "<EMAIL>")

	// 保存事件到存储
	eventID, err := eventStore.SaveEvent(ctx, userEvent)
	if err != nil {
		t.Fatalf("Failed to save event: %v", err)
	}

	// 发布事件
	err = eventManager.PublishEvent(userEvent)
	if err != nil {
		t.Fatalf("Failed to publish event: %v", err)
	}

	// 验证邮件处理器收到事件
	sentEmails := emailHandler.GetSentEmails()
	if len(sentEmails) != 1 {
		t.Errorf("Expected 1 sent email, got %d", len(sentEmails))
	}

	if len(sentEmails) > 0 && sentEmails[0] != "<EMAIL>" {
		t.Errorf("<NAME_EMAIL>, got %s", sentEmails[0])
	}

	// 验证审计处理器收到事件
	logs := auditHandler.GetLogs()
	if len(logs) != 1 {
		t.Errorf("Expected 1 audit log, got %d", len(logs))
	}

	// 验证事件存储
	retrievedEvent, err := eventStore.GetEventByID(ctx, eventID)
	if err != nil {
		t.Fatalf("Failed to retrieve event: %v", err)
	}

	if retrievedEvent.ID() != userEvent.ID() {
		t.Errorf("Retrieved event ID mismatch: expected %s, got %s", userEvent.ID(), retrievedEvent.ID())
	}

	// 测试事件分发器
	err = eventDispatcher.RegisterHandler(emailHandler)
	if err != nil {
		t.Fatalf("Failed to register handler to dispatcher: %v", err)
	}

	err = eventDispatcher.Dispatch(userEvent)
	if err != nil {
		t.Fatalf("Failed to dispatch event: %v", err)
	}

	// 验证分发器也能正确处理事件
	sentEmails = emailHandler.GetSentEmails()
	if len(sentEmails) != 2 { // 之前EventManager发布了一次，现在Dispatcher又发布了一次
		t.Errorf("Expected 2 sent emails after dispatcher, got %d", len(sentEmails))
	}
}

// TestEventSystemWithFx 测试事件系统与fx集成
func TestEventSystemWithFx(t *testing.T) {
	var eventManager contract.EventManager
	var emailHandler *EmailNotificationHandler

	app := fx.New(
		// 提供日志器
		fx.Provide(func() *zap.Logger {
			return zap.NewNop()
		}),

		// 提供事件系统组件
		fx.Provide(
			fx.Annotate(
				NewDefaultEventBus,
				fx.As(new(contract.EventBus)),
			),
		),
		fx.Provide(
			fx.Annotate(
				NewDefaultEventHandlerRegistry,
				fx.As(new(contract.EventHandlerRegistry)),
			),
		),
		fx.Provide(
			fx.Annotate(
				NewDefaultEventSerializer,
				fx.As(new(contract.EventSerializer)),
			),
		),
		fx.Provide(
			fx.Annotate(
				NewDefaultEventStore,
				fx.As(new(contract.EventStore)),
			),
		),
		fx.Provide(
			fx.Annotate(
				NewDefaultEventManager,
				fx.As(new(contract.EventManager)),
			),
		),

		// 提供事件处理器
		fx.Provide(NewEmailNotificationHandler),

		// 注册事件处理器
		fx.Invoke(func(manager contract.EventManager, handler *EmailNotificationHandler) {
			err := manager.RegisterHandler(handler)
			if err != nil {
				t.Errorf("Failed to register handler: %v", err)
			}
		}),

		// 获取组件引用用于测试
		fx.Populate(&eventManager, &emailHandler),
	)

	// 启动应用
	ctx := context.Background()
	if err := app.Start(ctx); err != nil {
		t.Fatalf("Failed to start fx app: %v", err)
	}
	defer app.Stop(ctx)

	// 测试事件发布
	userEvent := NewUserRegisteredEvent(ctx, 2, "fxuser", "<EMAIL>")
	err := eventManager.PublishEvent(userEvent)
	if err != nil {
		t.Fatalf("Failed to publish event through fx: %v", err)
	}

	// 验证处理器收到事件
	sentEmails := emailHandler.GetSentEmails()
	if len(sentEmails) != 1 {
		t.Errorf("Expected 1 sent email through fx, got %d", len(sentEmails))
	}

	if len(sentEmails) > 0 && sentEmails[0] != "<EMAIL>" {
		t.Errorf("<NAME_EMAIL>, got %s", sentEmails[0])
	}
}
