<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 系统更新</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .content-section-title::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 5rem;
            height: 3px;
            background: linear-gradient(to right, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            background: linear-gradient(145deg, #1e1e1e, #2a2a2a);
            border-left: 4px solid #007bff;
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            backdrop-filter: blur(20px);
            z-index: 1000;
            transform: translateX(120%);
            opacity: 0;
            transition: all 0.2s ease;
            max-width: 350px;
        }
        
        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }
        
        /* 更新进度条样式 */
        .progress-bar {
            height: 6px;
            border-radius: 3px;
            background: linear-gradient(to right, #3b82f6, #00c6ff);
            transition: width 0.3s ease;
        }
        
        /* 版本卡片样式 */
        .version-card {
            transition: all 0.3s ease;
        }
        
        .version-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
        }
        
        /* 更新日志时间线样式 */
        .timeline-container {
            position: relative;
        }
        
        .timeline-line {
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #3b82f6 0%, #00c6ff 100%);
        }
        
        .timeline-dot {
            position: absolute;
            left: 9px;
            top: 24px;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            background: #3b82f6;
            border: 3px solid #1f2937;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm text-gray-400 mb-4">
                <a href="dashboard.html" class="hover:text-blue-400">首页</a>
                <span class="mx-2">/</span>
                <a href="#" class="hover:text-blue-400">系统工具</a>
                <span class="mx-2">/</span>
                <span class="text-gray-200">系统更新</span>
            </div>
            
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">系统更新</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <a href="#" class="flex items-center justify-center bg-gray-700/50 border border-gray-600 text-white px-5 py-2 rounded-lg font-medium transition-all hover:bg-gray-700/70 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <i class="fas fa-history mr-2"></i>
                                更新历史
                            </span>
                        </a>
                        <a href="#" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-2 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <i class="fas fa-sync mr-2"></i>
                                检查更新
                            </span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 当前版本信息 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold text-white mb-4">当前版本信息</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-gray-800/20 rounded-xl p-5">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-gray-300 font-medium">系统版本</h4>
                            <span class="bg-blue-500/20 text-blue-400 text-xs px-2 py-1 rounded-full">当前</span>
                        </div>
                        <div class="text-2xl font-bold text-white mb-2">GACMS v1.0.0</div>
                        <div class="text-sm text-gray-400">发布日期: 2025-05-15</div>
                    </div>
                    <div class="bg-gray-800/20 rounded-xl p-5">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-gray-300 font-medium">核心框架</h4>
                            <span class="bg-green-500/20 text-green-400 text-xs px-2 py-1 rounded-full">稳定</span>
                        </div>
                        <div class="text-2xl font-bold text-white mb-2">Framework v2.5.3</div>
                        <div class="text-sm text-gray-400">更新日期: 2025-04-28</div>
                    </div>
                    <div class="bg-gray-800/20 rounded-xl p-5">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-gray-300 font-medium">数据库版本</h4>
                            <span class="bg-green-500/20 text-green-400 text-xs px-2 py-1 rounded-full">稳定</span>
                        </div>
                        <div class="text-2xl font-bold text-white mb-2">DB Schema v1.2.1</div>
                        <div class="text-sm text-gray-400">更新日期: 2025-05-10</div>
                    </div>
                </div>
            </div>

            <!-- 可用更新 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-bold text-white">可用更新</h3>
                    <div class="flex items-center text-sm text-gray-400">
                        <i class="fas fa-sync-alt mr-2"></i>
                        最后检查: 2025-05-25 10:30
                    </div>
                </div>
                
                <div class="bg-blue-500/10 border border-blue-500/30 rounded-xl p-5 mb-6">
                    <div class="flex flex-wrap items-start justify-between">
                        <div>
                            <div class="flex items-center mb-2">
                                <h4 class="text-xl font-bold text-white">GACMS v1.1.0</h4>
                                <span class="ml-3 bg-blue-500/20 text-blue-400 text-xs px-2 py-1 rounded-full">推荐更新</span>
                            </div>
                            <p class="text-gray-300 mb-4">此更新包含重要功能改进和安全修复，建议尽快更新。</p>
                            <div class="mb-4">
                                <div class="flex justify-between text-sm mb-1">
                                    <span class="text-gray-400">更新大小: 15.8 MB</span>
                                    <span class="text-blue-400">预计时间: 2-3分钟</span>
                                </div>
                                <div class="w-full bg-gray-700/50 rounded-full h-1.5">
                                    <div class="progress-bar w-0" id="updateProgress"></div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4 sm:mt-0">
                            <button id="updateButton" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-2 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                                <span class="relative flex items-center">
                                    <i class="fas fa-download mr-2"></i>
                                    立即更新
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 框架更新 -->
                    <div class="version-card bg-gray-800/20 rounded-xl p-5 border border-gray-700">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-gray-300 font-medium">核心框架更新</h4>
                            <span class="bg-yellow-500/20 text-yellow-400 text-xs px-2 py-1 rounded-full">可选</span>
                        </div>
                        <div class="text-xl font-bold text-white mb-2">Framework v2.6.0</div>
                        <p class="text-sm text-gray-400 mb-4">包含性能优化和新特性，与当前系统兼容。</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-400">发布日期: 2025-05-20</span>
                            <button class="text-blue-400 hover:text-blue-300 text-sm flex items-center">
                                查看详情 <i class="fas fa-chevron-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 插件更新 -->
                    <div class="version-card bg-gray-800/20 rounded-xl p-5 border border-gray-700">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-gray-300 font-medium">插件更新</h4>
                            <div class="flex">
                                <span class="bg-blue-500/20 text-blue-400 text-xs px-2 py-1 rounded-full mr-2">3个更新</span>
                                <span class="bg-red-500/20 text-red-400 text-xs px-2 py-1 rounded-full">1个安全修复</span>
                            </div>
                        </div>
                        <div class="text-xl font-bold text-white mb-2">多个插件可更新</div>
                        <p class="text-sm text-gray-400 mb-4">SEO优化、内容编辑器和安全插件有可用更新。</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-400">最近更新: 2025-05-22</span>
                            <button class="text-blue-400 hover:text-blue-300 text-sm flex items-center">
                                查看详情 <i class="fas fa-chevron-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 更新日志 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold text-white mb-4">更新日志</h3>
                
                <div class="timeline-container pl-10 relative">
                    <!-- 时间线背景 -->
                    <div class="timeline-line"></div>
                    
                    <!-- 更新项 1 -->
                    <div class="mb-8 relative">
                        <div class="timeline-dot"></div>
                        <div class="bg-gray-800/20 rounded-xl p-5 border border-gray-700">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="text-lg font-bold text-white">GACMS v1.0.0 正式版</h4>
                                <span class="bg-green-500/20 text-green-400 text-xs px-2 py-1 rounded-full">当前版本</span>
                            </div>
                            <div class="text-sm text-gray-400 mb-4">发布日期: 2025-05-15</div>
                            <div class="space-y-3">
                                <div>
                                    <h5 class="text-blue-400 font-medium mb-2">新功能</h5>
                                    <ul class="list-disc list-inside text-gray-300 space-y-1">
                                        <li>全新的响应式管理界面</li>
                                        <li>增强的内容编辑器，支持更多媒体类型</li>
                                        <li>新增多站点管理功能</li>
                                        <li>集成AI辅助内容创作工具</li>
                                    </ul>
                                </div>
                                <div>
                                    <h5 class="text-green-400 font-medium mb-2">改进</h5>
                                    <ul class="list-disc list-inside text-gray-300 space-y-1">
                                        <li>优化数据库查询性能，提升页面加载速度</li>
                                        <li>改进用户权限管理系统</li>
                                        <li>增强缓存机制，减少服务器负载</li>
                                    </ul>
                                </div>
                                <div>
                                    <h5 class="text-red-400 font-medium mb-2">修复</h5>
                                    <ul class="list-disc list-inside text-gray-300 space-y-1">
                                        <li>修复媒体库在特定条件下无法上传的问题</li>
                                        <li>解决移动端菜单显示异常的问题</li>
                                        <li>修复多个安全漏洞</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 更新项 2 -->
                    <div class="mb-8 relative">
                        <div class="timeline-dot"></div>
                        <div class="bg-gray-800/20 rounded-xl p-5 border border-gray-700">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="text-lg font-bold text-white">GACMS v0.9.5 Beta</h4>
                                <span class="bg-gray-500/20 text-gray-400 text-xs px-2 py-1 rounded-full">历史版本</span>
                            </div>
                            <div class="text-sm text-gray-400 mb-4">发布日期: 2025-04-02</div>
                            <div class="space-y-3">
                                <div>
                                    <h5 class="text-blue-400 font-medium mb-2">新功能</h5>
                                    <ul class="list-disc list-inside text-gray-300 space-y-1">
                                        <li>新增插件市场</li>
                                        <li>添加自动备份功能</li>
                                        <li>集成第三方登录支持</li>
                                    </ul>
                                </div>
                                <div>
                                    <h5 class="text-green-400 font-medium mb-2">改进</h5>
                                    <ul class="list-disc list-inside text-gray-300 space-y-1">
                                        <li>优化移动端响应式布局</li>
                                        <li>提升媒体库性能</li>
                                    </ul>
                                </div>
                                <div>
                                    <h5 class="text-red-400 font-medium mb-2">修复</h5>
                                    <ul class="list-disc list-inside text-gray-300 space-y-1">
                                        <li>修复多个UI显示问题</li>
                                        <li>解决内容导入功能的错误</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 更新项 3 -->
                    <div class="relative">
                        <div class="timeline-dot"></div>
                        <div class="bg-gray-800/20 rounded-xl p-5 border border-gray-700">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="text-lg font-bold text-white">GACMS v0.9.0 Beta</h4>
                                <span class="bg-gray-500/20 text-gray-400 text-xs px-2 py-1 rounded-full">历史版本</span>
                            </div>
                            <div class="text-sm text-gray-400 mb-4">发布日期: 2025-03-10</div>
                            <div class="space-y-3">
                                <div>
                                    <h5 class="text-blue-400 font-medium mb-2">新功能</h5>
                                    <ul class="list-disc list-inside text-gray-300 space-y-1">
                                        <li>首个公开测试版本</li>
                                        <li>核心内容管理功能</li>
                                        <li>基础主题系统</li>
                                        <li>用户权限管理</li>
                                    </ul>
                                </div>
                                <div>
                                    <h5 class="text-red-400 font-medium mb-2">已知问题</h5>
                                    <ul class="list-disc list-inside text-gray-300 space-y-1">
                                        <li>部分浏览器兼容性问题</li>
                                        <li>媒体库性能待优化</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 text-center">
                    <button class="text-blue-400 hover:text-blue-300 inline-flex items-center">
                        查看更多历史版本 <i class="fas fa-chevron-down ml-2"></i>
                    </button>
                </div>
            </div>

            <!-- 自动更新设置 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold text-white mb-4">更新设置</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-gray-200 font-medium">自动检查更新</h4>
                            <p class="text-sm text-gray-400">系统将定期检查新版本</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-700 rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-gray-300 after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-gray-200 font-medium">自动下载更新</h4>
                            <p class="text-sm text-gray-400">发现更新时自动下载但不安装</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-700 rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-gray-300 after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-gray-200 font-medium">自动安装安全更新</h4>
                            <p class="text-sm text-gray-400">自动安装关键安全修复</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-700 rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-gray-300 after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-gray-200 font-medium">更新前自动备份</h4>
                            <p class="text-sm text-gray-400">在安装更新前创建系统备份</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-700 rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-gray-300 after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <!-- 更新确认模态框 -->
    <div id="updateModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center hidden">
        <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 max-w-md w-full mx-4">
            <div class="flex justify-between items-start mb-4">
                <h3 class="text-lg font-bold text-white">确认更新</h3>
                <button id="closeModal" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mb-6">
                <p class="text-gray-300 mb-4">您确定要更新到 GACMS v1.1.0 吗？</p>
                <div class="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4 text-sm text-gray-300">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-info-circle text-blue-400 mr-2"></i>
                        <span class="font-medium">更新注意事项</span>
                    </div>
                    <ul class="list-disc list-inside space-y-1 ml-1 text-gray-400">
                        <li>更新过程中请勿关闭浏览器或断开连接</li>
                        <li>更新完成后系统将自动重启</li>
                        <li>建议在更新前备份您的数据</li>
                    </ul>
                </div>
            </div>
            <div class="flex space-x-3">
                <button id="cancelUpdate" class="flex-1 py-2 px-4 bg-gray-700 hover:bg-gray-600 text-gray-200 rounded-lg transition-colors">
                    取消
                </button>
                <button id="confirmUpdate" class="flex-1 py-2 px-4 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg transition-colors">
                    确认更新
                </button>
            </div>
        </div>
    </div>

    <!-- 通知组件 -->
    <div class="notification" id="notification">
        <div class="mr-3">
            <i class="fas fa-info-circle text-blue-400 text-xl"></i>
        </div>
        <div class="flex-1">
            <h4 class="text-white text-sm font-bold">检查更新完成</h4>
            <p class="text-gray-300 text-xs">发现1个系统更新和4个组件更新。</p>
        </div>
    </div>

    <!-- 加载必要的JS -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 显示通知
            setTimeout(() => {
                document.getElementById('notification').classList.add('show');
                setTimeout(() => {
                    document.getElementById('notification').classList.remove('show');
                }, 3000);
            }, 1000);
            
            // 更新按钮点击事件
            const updateButton = document.getElementById('updateButton');
            const updateModal = document.getElementById('updateModal');
            const closeModal = document.getElementById('closeModal');
            const cancelUpdate = document.getElementById('cancelUpdate');
            const confirmUpdate = document.getElementById('confirmUpdate');
            const updateProgress = document.getElementById('updateProgress');
            
            updateButton.addEventListener('click', function() {
                updateModal.classList.remove('hidden');
            });
            
            closeModal.addEventListener('click', function() {
                updateModal.classList.add('hidden');
            });
            
            cancelUpdate.addEventListener('click', function() {
                updateModal.classList.add('hidden');
            });
            
            confirmUpdate.addEventListener('click', function() {
                updateModal.classList.add('hidden');
                simulateUpdate();
            });
            
            // 模拟更新进度
            function simulateUpdate() {
                let progress = 0;
                updateButton.disabled = true;
                updateButton.classList.add('opacity-70');
                updateButton.innerHTML = '<span class="relative flex items-center"><i class="fas fa-spinner fa-spin mr-2"></i>更新中...</span>';
                
                const interval = setInterval(() => {
                    progress += 5;
                    updateProgress.style.width = `${progress}%`;
                    
                    if (progress >= 100) {
                        clearInterval(interval);
                        updateButton.innerHTML = '<span class="relative flex items-center"><i class="fas fa-check mr-2"></i>更新完成</span>';
                        updateButton.classList.remove('opacity-70');
                        updateButton.classList.remove('from-blue-500', 'to-blue-600');
                        updateButton.classList.add('from-green-500', 'to-green-600');
                        
                        // 显示更新完成通知
                        setTimeout(() => {
                            const notification = document.getElementById('notification');
                            notification.querySelector('i').className = 'fas fa-check-circle text-green-400 text-xl';
                            notification.querySelector('h4').textContent = '更新完成';
                            notification.querySelector('p').textContent = 'GACMS已成功更新到v1.1.0版本。';
                            notification.classList.add('show');
                            
                            setTimeout(() => {
                                notification.classList.remove('show');
                            }, 3000);
                        }, 1000);
                    }
                }, 200);
            }
        });
    </script>
</body>
</html>