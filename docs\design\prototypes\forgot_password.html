<!--
© 2025 Clion Nieh. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 找回密码</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1a1c23 0%, #121317 100%);
            font-family: 'Inter', sans-serif;
        }
        
        .login-card {
            background: rgba(30, 32, 40, 0.6);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
        
        .input-group {
            position: relative;
        }
        
        .input-icon {
            position: absolute;
            top: 50%;
            left: 1rem;
            transform: translateY(-50%);
            color: #6b7280;
        }
        
        .input-field {
            background-color: rgba(30, 32, 40, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #e5e7eb;
            padding-left: 2.5rem;
            transition: all 0.3s ease;
        }
        
        .input-field:focus {
            background-color: rgba(30, 32, 40, 0.8);
            border-color: rgba(59, 130, 246, 0.5);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
        }
        
        .submit-btn {
            background: linear-gradient(90deg, #3b82f6, #2563eb);
            transition: all 0.3s ease;
        }
        
        .submit-btn:hover {
            background: linear-gradient(90deg, #2563eb, #1d4ed8);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
            transform: translateY(-1px);
        }
        
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }
        
        .shape {
            position: absolute;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 50%;
            animation: float 15s infinite ease-in-out;
        }
        
        .shape:nth-child(1) {
            width: 400px;
            height: 400px;
            top: -200px;
            left: -200px;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 300px;
            height: 300px;
            top: 60%;
            right: -150px;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 200px;
            height: 200px;
            bottom: -100px;
            left: 30%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0% {
                transform: translateY(0) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(5deg);
            }
            100% {
                transform: translateY(0) rotate(0deg);
            }
        }
        
        .recovery-step {
            display: none;
        }
        
        .recovery-step.active {
            display: block;
        }
        
        .verification-code-group {
            display: flex;
            justify-content: space-between;
            gap: 8px;
        }
        
        .verification-code-input {
            width: 50px;
            height: 60px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            border-radius: 8px;
            background-color: rgba(30, 32, 40, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #e5e7eb;
            transition: all 0.3s ease;
        }
        
        .verification-code-input:focus {
            background-color: rgba(30, 32, 40, 0.8);
            border-color: rgba(59, 130, 246, 0.5);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
            outline: none;
        }
        
        .progress-dots {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        
        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #4b5563;
            margin: 0 4px;
            transition: all 0.3s ease;
        }
        
        .dot.active {
            background-color: #3b82f6;
            transform: scale(1.2);
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="login-card w-full max-w-md rounded-xl p-8 md:p-10">
        <div class="text-center mb-8">
            <img src="./assets/images/logo.svg" alt="GACMS Logo" class="h-12 mx-auto mb-2">
            <h1 class="text-2xl font-bold text-white">找回密码</h1>
            <p class="text-gray-400 mt-2" id="stepDescription">请输入您的账户邮箱，我们会发送重置密码的链接</p>
        </div>
        
        <!-- 步骤1：输入邮箱 -->
        <div class="recovery-step active" id="step1">
            <form id="emailForm">
                <div class="space-y-6">
                    <div class="input-group">
                        <span class="input-icon">
                            <i class="fas fa-envelope"></i>
                        </span>
                        <input type="email" placeholder="您的邮箱地址" id="email" class="input-field w-full px-4 py-3 rounded-lg focus:outline-none" required>
                    </div>
                    
                    <button type="submit" class="submit-btn w-full py-3 rounded-lg text-white font-medium focus:outline-none">
                        发送验证码 <i class="fas fa-paper-plane ml-2"></i>
                    </button>
                    
                    <div class="text-center mt-6">
                        <p class="text-gray-400">
                            返回 
                            <a href="login.html" class="text-blue-400 hover:text-blue-300 transition-colors">登录</a>
                        </p>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 步骤2：输入验证码 -->
        <div class="recovery-step" id="step2">
            <form id="verificationForm">
                <div class="space-y-6">
                    <div class="text-center mb-4">
                        <p class="text-gray-300">我们已向 <span id="userEmail" class="text-blue-400"><EMAIL></span> 发送了验证码</p>
                    </div>
                    
                    <div class="verification-code-group">
                        <input type="text" maxlength="1" class="verification-code-input" data-index="1" required>
                        <input type="text" maxlength="1" class="verification-code-input" data-index="2" required>
                        <input type="text" maxlength="1" class="verification-code-input" data-index="3" required>
                        <input type="text" maxlength="1" class="verification-code-input" data-index="4" required>
                        <input type="text" maxlength="1" class="verification-code-input" data-index="5" required>
                        <input type="text" maxlength="1" class="verification-code-input" data-index="6" required>
                    </div>
                    
                    <div class="text-center">
                        <p class="text-sm text-gray-400">
                            没收到验证码？<button type="button" id="resendCode" class="text-blue-400 hover:text-blue-300 transition-colors">重新发送</button>
                        </p>
                        <p class="text-sm text-gray-500 mt-1">
                            <span id="countdown">60</span> 秒后可重新发送
                        </p>
                    </div>
                    
                    <button type="submit" class="submit-btn w-full py-3 rounded-lg text-white font-medium focus:outline-none">
                        验证 <i class="fas fa-check ml-2"></i>
                    </button>
                    
                    <button type="button" id="backToStep1" class="bg-gray-700 hover:bg-gray-600 transition-colors w-full py-3 rounded-lg text-white font-medium focus:outline-none">
                        <i class="fas fa-arrow-left mr-2"></i> 返回修改邮箱
                    </button>
                </div>
            </form>
        </div>
        
        <!-- 步骤3：设置新密码 -->
        <div class="recovery-step" id="step3">
            <form id="resetPasswordForm">
                <div class="space-y-6">
                    <div class="input-group">
                        <span class="input-icon">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" id="newPassword" placeholder="新密码" class="input-field w-full px-4 py-3 rounded-lg focus:outline-none" required>
                    </div>
                    
                    <div class="input-group">
                        <span class="input-icon">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" id="confirmPassword" placeholder="确认新密码" class="input-field w-full px-4 py-3 rounded-lg focus:outline-none" required>
                    </div>
                    
                    <div class="text-xs text-gray-400">
                        <p>密码要求：</p>
                        <ul class="list-disc ml-5 mt-1 space-y-1">
                            <li>至少8个字符</li>
                            <li>包含字母和数字</li>
                            <li>包含至少一个特殊字符</li>
                        </ul>
                    </div>
                    
                    <button type="submit" class="submit-btn w-full py-3 rounded-lg text-white font-medium focus:outline-none">
                        重置密码 <i class="fas fa-key ml-2"></i>
                    </button>
                </div>
            </form>
        </div>
        
        <!-- 步骤4：重置成功 -->
        <div class="recovery-step" id="step4">
            <div class="text-center">
                <div class="w-20 h-20 mx-auto bg-green-500 rounded-full flex items-center justify-center">
                    <i class="fas fa-check text-white text-3xl"></i>
                </div>
                
                <h2 class="text-xl font-bold text-white mt-6">密码重置成功</h2>
                <p class="text-gray-400 mt-2">您的密码已成功重置，现在可以使用新密码登录</p>
                
                <a href="login.html" class="submit-btn w-full py-3 rounded-lg text-white font-medium focus:outline-none mt-6 inline-block">
                    前往登录 <i class="fas fa-sign-in-alt ml-2"></i>
                </a>
            </div>
        </div>
        
        <!-- 步骤指示点 -->
        <div class="progress-dots">
            <div class="dot active" data-step="1"></div>
            <div class="dot" data-step="2"></div>
            <div class="dot" data-step="3"></div>
            <div class="dot" data-step="4"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取步骤元素
            const step1 = document.getElementById('step1');
            const step2 = document.getElementById('step2');
            const step3 = document.getElementById('step3');
            const step4 = document.getElementById('step4');
            const stepDescription = document.getElementById('stepDescription');
            const userEmail = document.getElementById('userEmail');
            
            // 获取表单
            const emailForm = document.getElementById('emailForm');
            const verificationForm = document.getElementById('verificationForm');
            const resetPasswordForm = document.getElementById('resetPasswordForm');
            const backToStep1 = document.getElementById('backToStep1');
            
            // 步骤描述文本
            const descriptions = [
                "请输入您的账户邮箱，我们会发送重置密码的链接",
                "请输入您收到的验证码",
                "请设置您的新密码",
                "您的密码已成功重置"
            ];
            
            // 更新当前步骤
            function updateStep(stepNumber) {
                // 隐藏所有步骤
                [step1, step2, step3, step4].forEach(step => {
                    step.classList.remove('active');
                });
                
                // 显示当前步骤
                document.getElementById(`step${stepNumber}`).classList.add('active');
                
                // 更新描述文本
                stepDescription.textContent = descriptions[stepNumber - 1];
                
                // 更新进度点
                const dots = document.querySelectorAll('.dot');
                dots.forEach(dot => {
                    dot.classList.remove('active');
                    if (parseInt(dot.getAttribute('data-step')) <= stepNumber) {
                        dot.classList.add('active');
                    }
                });
            }
            
            // 步骤1表单提交
            emailForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const email = document.getElementById('email').value;
                if (email) {
                    // 在实际应用中，这里会发送API请求发送验证码
                    userEmail.textContent = email;
                    updateStep(2);
                    startCountdown();
                }
            });
            
            // 返回步骤1
            backToStep1.addEventListener('click', function() {
                updateStep(1);
            });
            
            // 步骤2表单提交
            verificationForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // 收集验证码
                const inputs = document.querySelectorAll('.verification-code-input');
                let code = '';
                inputs.forEach(input => {
                    code += input.value;
                });
                
                if (code.length === 6) {
                    // 在实际应用中，这里会发送API请求验证验证码
                    updateStep(3);
                }
            });
            
            // 步骤3表单提交
            resetPasswordForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const newPassword = document.getElementById('newPassword').value;
                const confirmPassword = document.getElementById('confirmPassword').value;
                
                if (newPassword && confirmPassword) {
                    if (newPassword !== confirmPassword) {
                        alert('两次输入的密码不一致，请重新输入');
                        return;
                    }
                    
                    // 在实际应用中，这里会发送API请求重置密码
                    updateStep(4);
                }
            });
            
            // 验证码输入框处理
            const verificationInputs = document.querySelectorAll('.verification-code-input');
            
            verificationInputs.forEach((input, index) => {
                // 自动聚焦到下一个输入框
                input.addEventListener('input', function() {
                    if (this.value.length === 1) {
                        const nextIndex = index + 1;
                        if (nextIndex < verificationInputs.length) {
                            verificationInputs[nextIndex].focus();
                        }
                    }
                });
                
                // 允许退格键返回到上一个输入框
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Backspace' && this.value === '') {
                        const prevIndex = index - 1;
                        if (prevIndex >= 0) {
                            verificationInputs[prevIndex].focus();
                        }
                    }
                });
            });
            
            // 倒计时功能
            let countdownInterval;
            function startCountdown() {
                const countdownElement = document.getElementById('countdown');
                const resendButton = document.getElementById('resendCode');
                let seconds = 60;
                
                resendButton.disabled = true;
                resendButton.classList.add('text-gray-500');
                resendButton.classList.remove('text-blue-400', 'hover:text-blue-300');
                
                countdownElement.textContent = seconds;
                
                countdownInterval = setInterval(function() {
                    seconds--;
                    countdownElement.textContent = seconds;
                    
                    if (seconds <= 0) {
                        clearInterval(countdownInterval);
                        resendButton.disabled = false;
                        resendButton.classList.remove('text-gray-500');
                        resendButton.classList.add('text-blue-400', 'hover:text-blue-300');
                    }
                }, 1000);
            }
            
            // 重新发送验证码
            document.getElementById('resendCode').addEventListener('click', function() {
                if (!this.disabled) {
                    // 在实际应用中，这里会发送API请求重新发送验证码
                    clearInterval(countdownInterval);
                    startCountdown();
                }
            });
        });
    </script>
</body>
</html> 