/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/system/contract/SystemRepository.go
 * @Description: 系统设置基础设施仓储接口定义
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"gacms/internal/core/system/model"
)

// SiteRepository 站点仓储接口
type SiteRepository interface {
	// 基础CRUD
	Create(site *model.Site) error
	Update(site *model.Site) error
	Delete(id uint) error
	GetByID(id uint) (*model.Site, error)
	GetByDomain(domain string) (*model.Site, error)
	
	// 查询方法
	ListAll(page, pageSize int) ([]*model.Site, int64, error)
	ListActive() ([]*model.Site, error)
	Exists(domain string) (bool, error)
	
	// 配置相关
	UpdateConfig(siteID uint, config *model.SiteConfig) error
	GetConfig(siteID uint) (*model.SiteConfig, error)
}

// DomainBindingRepository 域名绑定仓储接口
type DomainBindingRepository interface {
	// 基础CRUD
	Create(binding *model.DomainBinding) error
	Update(binding *model.DomainBinding) error
	Delete(id uint) error
	GetByID(id uint) (*model.DomainBinding, error)
	GetByDomain(domain string) (*model.DomainBinding, error)
	GetByDomainWithRules(domain string) (*model.DomainBinding, error)
	
	// 查询方法
	ListBySiteID(siteID uint, page, pageSize int) ([]*model.DomainBinding, int64, error)
	ListByType(bindingType model.BindingType) ([]*model.DomainBinding, error)
	ListByModule(moduleSlug string) ([]*model.DomainBinding, error)
	
	// URL重写规则相关
	CreateURLRule(rule *model.URLRewriteRule) error
	UpdateURLRule(rule *model.URLRewriteRule) error
	DeleteURLRule(id uint) error
	GetURLRulesByDomainBinding(domainBindingID uint) ([]*model.URLRewriteRule, error)
	GetURLRuleByID(id uint) (*model.URLRewriteRule, error)
}

// SystemConfigRepository 系统配置仓储接口
type SystemConfigRepository interface {
	// 配置管理
	Set(key, value string) error
	Get(key string) (string, error)
	GetWithDefault(key, defaultValue string) string
	Delete(key string) error
	
	// 批量操作
	SetMultiple(configs map[string]string) error
	GetMultiple(keys []string) (map[string]string, error)
	GetByPrefix(prefix string) (map[string]string, error)
	
	// 配置分组
	GetGroup(group string) (map[string]string, error)
	SetGroup(group string, configs map[string]string) error
	DeleteGroup(group string) error
}

// PlatformUserRepository 平台用户仓储接口（超级管理员）
type PlatformUserRepository interface {
	// 基础CRUD
	Create(user *model.PlatformUser) error
	Update(user *model.PlatformUser) error
	Delete(id uint) error
	GetByID(id uint) (*model.PlatformUser, error)
	GetByEmail(email string) (*model.PlatformUser, error)
	GetByUsername(username string) (*model.PlatformUser, error)
	
	// 查询方法
	ListAll(page, pageSize int) ([]*model.PlatformUser, int64, error)
	ListActive() ([]*model.PlatformUser, error)
	
	// 认证相关
	ValidateCredentials(username, password string) (*model.PlatformUser, error)
	UpdatePassword(userID uint, hashedPassword string) error
	UpdateLastLogin(userID uint) error
}

// SystemLogRepository 系统日志仓储接口
type SystemLogRepository interface {
	// 日志记录
	CreateLog(log *model.SystemLog) error
	
	// 查询方法
	ListByLevel(level string, page, pageSize int) ([]*model.SystemLog, int64, error)
	ListByModule(module string, page, pageSize int) ([]*model.SystemLog, int64, error)
	ListByUser(userID uint, page, pageSize int) ([]*model.SystemLog, int64, error)
	ListByDateRange(startDate, endDate string, page, pageSize int) ([]*model.SystemLog, int64, error)
	
	// 统计方法
	CountByLevel(level string) (int64, error)
	CountByModule(module string) (int64, error)
	CountByDateRange(startDate, endDate string) (int64, error)
	
	// 清理方法
	DeleteOldLogs(days int) error
	DeleteByLevel(level string) error
}
