<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 评论管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        /* 状态标签样式 */
        .status-badge {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .status-badge.pending {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
            border: 1px solid rgba(245, 158, 11, 0.3);
        }
        
        .status-badge.approved {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }
        
        .status-badge.rejected {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        
        .status-badge.spam {
            background: rgba(108, 117, 125, 0.2);
            color: #6c757d;
            border: 1px solid rgba(108, 117, 125, 0.3);
        }
        
        /* 评论卡片悬停效果 */
        .comment-item {
            transition: all 0.3s ease;
        }
        
        .comment-item:hover {
            background-color: rgba(50, 50, 50, 0.3);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">评论管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-check text-white"></i>
                                </span>
                                批量审核
                            </span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 统计概览 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- 评论总数 -->
                <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-comments text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">评论总数</div>
                            <div class="text-xl font-semibold text-white">1,256</div>
                            <div class="text-xs text-gray-400 mt-1">今日 +14 条</div>
                        </div>
                    </div>
                </div>

                <!-- 待审核 -->
                <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-clock text-yellow-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">待审核</div>
                            <div class="text-xl font-semibold text-white">32</div>
                            <div class="text-xs text-yellow-400 mt-1">需要审核</div>
                        </div>
                    </div>
                </div>

                <!-- 已通过 -->
                <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-check-circle text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">已通过</div>
                            <div class="text-xl font-semibold text-white">1,193</div>
                            <div class="text-xs text-gray-400 mt-1">昨日 +25 条</div>
                        </div>
                    </div>
                </div>

                <!-- 垃圾评论 -->
                <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-red-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-ban text-red-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">垃圾评论</div>
                            <div class="text-xl font-semibold text-white">31</div>
                            <div class="text-xs text-gray-400 mt-1">自动过滤</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 评论管理主体内容 -->
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- 左侧筛选面板 -->
                <div class="lg:col-span-1">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold text-white mb-4">筛选条件</h3>
                        
                        <!-- 搜索框 -->
                        <div class="mb-6">
                            <label class="block text-gray-400 mb-2 text-sm">关键字搜索</label>
                            <div class="relative">
                                <input type="text" placeholder="搜索评论内容或作者..." 
                                       class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent pl-10">
                                <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                    <i class="fas fa-search"></i>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 评论状态 -->
                        <div class="mb-6">
                            <h4 class="font-medium text-white mb-3 text-sm">评论状态</h4>
                            <div class="space-y-2">
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" class="form-checkbox rounded text-blue-500 border-gray-600 bg-gray-700 mr-2">
                                    <span class="text-gray-300">待审核</span>
                                    <span class="ml-auto text-sm text-gray-500">32</span>
                                </label>
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" class="form-checkbox rounded text-blue-500 border-gray-600 bg-gray-700 mr-2" checked>
                                    <span class="text-gray-300">已通过</span>
                                    <span class="ml-auto text-sm text-gray-500">1,193</span>
                                </label>
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" class="form-checkbox rounded text-blue-500 border-gray-600 bg-gray-700 mr-2">
                                    <span class="text-gray-300">已拒绝</span>
                                    <span class="ml-auto text-sm text-gray-500">18</span>
                                </label>
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" class="form-checkbox rounded text-blue-500 border-gray-600 bg-gray-700 mr-2">
                                    <span class="text-gray-300">垃圾评论</span>
                                    <span class="ml-auto text-sm text-gray-500">13</span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- 评论时间 -->
                        <div class="mb-6">
                            <h4 class="font-medium text-white mb-3 text-sm">评论时间</h4>
                            <div class="space-y-2">
                                <label class="flex items-center cursor-pointer">
                                    <input type="radio" name="time-filter" class="form-radio text-blue-500 border-gray-600 bg-gray-700 mr-2" checked>
                                    <span class="text-gray-300">全部时间</span>
                                </label>
                                <label class="flex items-center cursor-pointer">
                                    <input type="radio" name="time-filter" class="form-radio text-blue-500 border-gray-600 bg-gray-700 mr-2">
                                    <span class="text-gray-300">最近一天</span>
                                </label>
                                <label class="flex items-center cursor-pointer">
                                    <input type="radio" name="time-filter" class="form-radio text-blue-500 border-gray-600 bg-gray-700 mr-2">
                                    <span class="text-gray-300">最近一周</span>
                                </label>
                                <label class="flex items-center cursor-pointer">
                                    <input type="radio" name="time-filter" class="form-radio text-blue-500 border-gray-600 bg-gray-700 mr-2">
                                    <span class="text-gray-300">最近一月</span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- 评论类型 -->
                        <div class="mb-6">
                            <h4 class="font-medium text-white mb-3 text-sm">评论类型</h4>
                            <div class="space-y-2">
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" class="form-checkbox rounded text-blue-500 border-gray-600 bg-gray-700 mr-2" checked>
                                    <span class="text-gray-300">文章评论</span>
                                    <span class="ml-auto text-sm text-gray-500">1,124</span>
                                </label>
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" class="form-checkbox rounded text-blue-500 border-gray-600 bg-gray-700 mr-2">
                                    <span class="text-gray-300">页面评论</span>
                                    <span class="ml-auto text-sm text-gray-500">132</span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- 筛选按钮 -->
                        <div class="flex gap-3">
                            <button class="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30">
                                应用筛选
                            </button>
                            <button class="flex-1 bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-all hover:bg-gray-600">
                                重置
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧评论列表 -->
                <div class="lg:col-span-3">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <div class="flex flex-wrap justify-between items-center mb-6">
                            <h3 class="text-lg font-semibold text-white">评论列表</h3>
                            
                            <!-- 批量操作和排序 -->
                            <div class="flex flex-wrap gap-3 mt-2 sm:mt-0">
                                <select class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-gray-300 focus:ring-2 focus:ring-blue-500">
                                    <option value="newest">最新评论</option>
                                    <option value="oldest">最早评论</option>
                                    <option value="likes">点赞数</option>
                                </select>
                                <button class="px-4 py-2 bg-red-600/30 text-red-400 border border-red-500/30 rounded-lg hover:bg-red-600/40">
                                    <i class="fas fa-trash-alt mr-1"></i> 批量删除
                                </button>
                            </div>
                        </div>
                        
                        <!-- 评论列表 -->
                        <div class="space-y-5">
                            <!-- 评论项 1 (待审核) -->
                            <div class="comment-item bg-gray-800/20 border border-l-4 border-yellow-500/50 rounded-lg p-5">
                                <div class="flex items-start gap-4">
                                    <div class="flex-shrink-0">
                                        <img src="./assets/images/avatar-1.jpg" alt="用户头像" class="w-10 h-10 rounded-full">
                                        <label class="block mt-2 mx-auto text-center">
                                            <input type="checkbox" class="form-checkbox w-4 h-4">
                                        </label>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex flex-wrap justify-between items-center mb-2">
                                            <div>
                                                <span class="font-medium text-white">李华</span>
                                                <span class="text-gray-400 text-sm ml-2">2025-06-15 14:30</span>
                                                <span class="status-badge pending ml-2">待审核</span>
                                            </div>
                                            <div class="flex space-x-3">
                                                <button title="通过" class="text-gray-400 hover:text-green-500">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button title="拒绝" class="text-gray-400 hover:text-red-500">
                                                    <i class="fas fa-ban"></i>
                                                </button>
                                                <button title="删除" class="text-gray-400 hover:text-red-500">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <button title="回复" class="text-gray-400 hover:text-blue-500">
                                                    <i class="fas fa-reply"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <p class="text-gray-300">这篇文章写得非常好，对我帮助很大！希望能有更多类似的内容分享。</p>
                                        </div>
                                        <div class="flex justify-between items-center text-sm">
                                            <div class="text-gray-400">
                                                评论于：<a href="#" class="text-blue-400 hover:underline">《Go语言高性能编程实践指南》</a>
                                            </div>
                                            <div class="flex items-center space-x-3">
                                                <span class="text-gray-400"><i class="far fa-eye mr-1"></i> 123</span>
                                                <span class="text-gray-400"><i class="far fa-thumbs-up mr-1"></i> 5</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 评论项 2 (已通过) -->
                            <div class="comment-item bg-gray-800/20 border border-l-4 border-green-500/50 rounded-lg p-5">
                                <div class="flex items-start gap-4">
                                    <div class="flex-shrink-0">
                                        <img src="./assets/images/avatar-2.jpg" alt="用户头像" class="w-10 h-10 rounded-full">
                                        <label class="block mt-2 mx-auto text-center">
                                            <input type="checkbox" class="form-checkbox w-4 h-4">
                                        </label>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex flex-wrap justify-between items-center mb-2">
                                            <div>
                                                <span class="font-medium text-white">王小明</span>
                                                <span class="text-gray-400 text-sm ml-2">2025-06-14 09:15</span>
                                                <span class="status-badge approved ml-2">已通过</span>
                                            </div>
                                            <div class="flex space-x-3">
                                                <button title="删除" class="text-gray-400 hover:text-red-500">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <button title="回复" class="text-gray-400 hover:text-blue-500">
                                                    <i class="fas fa-reply"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <p class="text-gray-300">文章中关于并发控制的部分讲得很清楚，但是我认为在实际应用中，还需要考虑更多的边界情况。</p>
                                        </div>
                                        <div class="flex justify-between items-center text-sm">
                                            <div class="text-gray-400">
                                                评论于：<a href="#" class="text-blue-400 hover:underline">《Go语言高性能编程实践指南》</a>
                                            </div>
                                            <div class="flex items-center space-x-3">
                                                <span class="text-gray-400"><i class="far fa-eye mr-1"></i> 98</span>
                                                <span class="text-gray-400"><i class="far fa-thumbs-up mr-1"></i> 12</span>
                                            </div>
                                        </div>
                                        
                                        <!-- 回复区域 -->
                                        <div class="mt-3 bg-gray-900/30 rounded-lg p-3 border-l-2 border-blue-500/50">
                                            <div class="flex items-start gap-3">
                                                <img src="./assets/images/avatar-admin.jpg" alt="管理员头像" class="w-8 h-8 rounded-full">
                                                <div>
                                                    <div class="flex items-center">
                                                        <span class="text-blue-400 font-medium">管理员</span>
                                                        <span class="text-gray-500 text-xs ml-2">2025-06-14 11:30</span>
                                                    </div>
                                                    <p class="text-gray-400 mt-1 text-sm">感谢您的建议，您提到的边界情况确实很重要，我们会在后续的文章中详细讨论这些问题。</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 评论项 3 (已拒绝) -->
                            <div class="comment-item bg-gray-800/20 border border-l-4 border-red-500/50 rounded-lg p-5">
                                <div class="flex items-start gap-4">
                                    <div class="flex-shrink-0">
                                        <img src="./assets/images/avatar-3.jpg" alt="用户头像" class="w-10 h-10 rounded-full">
                                        <label class="block mt-2 mx-auto text-center">
                                            <input type="checkbox" class="form-checkbox w-4 h-4">
                                        </label>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex flex-wrap justify-between items-center mb-2">
                                            <div>
                                                <span class="font-medium text-white">张雨琪</span>
                                                <span class="text-gray-400 text-sm ml-2">2025-06-13 16:45</span>
                                                <span class="status-badge rejected ml-2">已拒绝</span>
                                            </div>
                                            <div class="flex space-x-3">
                                                <button title="通过" class="text-gray-400 hover:text-green-500">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button title="删除" class="text-gray-400 hover:text-red-500">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <button title="回复" class="text-gray-400 hover:text-blue-500">
                                                    <i class="fas fa-reply"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <p class="text-gray-300">这篇文章有点浪费时间，都是基础内容，没什么新意。</p>
                                        </div>
                                        <div class="flex justify-between items-center text-sm">
                                            <div class="text-gray-400">
                                                评论于：<a href="#" class="text-blue-400 hover:underline">《Web前端开发入门指南》</a>
                                            </div>
                                            <div class="flex items-center space-x-3">
                                                <span class="text-gray-400"><i class="far fa-eye mr-1"></i> 45</span>
                                                <span class="text-gray-400"><i class="far fa-thumbs-up mr-1"></i> 0</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 分页 -->
                        <div class="flex justify-between items-center mt-6">
                            <div class="text-sm text-gray-400">
                                显示 1 至 3，共 1,256 条
                            </div>
                            <div class="flex space-x-2">
                                <button disabled class="bg-gray-800 text-gray-500 px-4 py-2 rounded-lg">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <button class="bg-blue-500 text-white px-4 py-2 rounded-lg">1</button>
                                <button class="bg-gray-800 text-white hover:bg-gray-700 px-4 py-2 rounded-lg">2</button>
                                <button class="bg-gray-800 text-white hover:bg-gray-700 px-4 py-2 rounded-lg">3</button>
                                <button class="bg-gray-800 text-white hover:bg-gray-700 px-4 py-2 rounded-lg">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            
            // 加载顶部导航栏
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
        });
    </script>
</body>
</html> 