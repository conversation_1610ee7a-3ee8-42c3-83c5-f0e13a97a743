/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: pkg/contract/Auth.go
 * @Description: JWT认证接口定义，扩展原有功能
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"context"
	"github.com/gin-gonic/gin"
)

// Auth JWT认证接口（原有接口）
type Auth interface {
	// Generate 生成JWT令牌
	Generate(claims map[string]interface{}) (string, error)
	
	// Parse 解析JWT令牌
	Parse(tokenString string) (map[string]interface{}, error)
	
	// 扩展方法：增强认证功能
	
	// GenerateToken 生成令牌（别名方法，保持兼容性）
	GenerateToken(claims map[string]interface{}) (string, error)
	
	// ParseToken 解析令牌（别名方法，保持兼容性）
	ParseToken(tokenString string) (map[string]interface{}, error)
	
	// ValidateToken 验证令牌有效性
	ValidateToken(ctx context.Context, tokenString string) (map[string]interface{}, error)
	
	// RefreshToken 刷新令牌
	RefreshToken(ctx context.Context, refreshToken string) (string, error)
	
	// RevokeToken 撤销令牌
	RevokeToken(ctx context.Context, tokenString string) error
	
	// GetUserIDFromToken 从令牌中获取用户ID
	GetUserIDFromToken(tokenString string) (uint, error)
	
	// GetSiteIDFromToken 从令牌中获取站点ID
	GetSiteIDFromToken(tokenString string) (uint, error)
	
	// GetUserTypeFromToken 从令牌中获取用户类型
	GetUserTypeFromToken(tokenString string) (string, error)
	
	// SiteIDFrom 从上下文获取站点ID（保持现有方法）
	SiteIDFrom(ctx context.Context) (uint, error)
}

// AuthProvider 认证提供者接口（扩展功能）
type AuthProvider interface {
	// GetName 获取提供者名称
	GetName() string
	
	// Authenticate 执行认证
	Authenticate(ctx context.Context, credentials map[string]interface{}) (*AuthResult, error)
	
	// Validate 验证令牌
	Validate(ctx context.Context, token string) (*AuthResult, error)
	
	// Refresh 刷新令牌
	Refresh(ctx context.Context, refreshToken string) (*AuthResult, error)
	
	// Revoke 撤销令牌
	Revoke(ctx context.Context, token string) error
}

// AuthResult 认证结果
type AuthResult struct {
	Token        string                 `json:"token"`
	RefreshToken string                 `json:"refresh_token,omitempty"`
	ExpiresIn    int64                  `json:"expires_in"`
	TokenType    string                 `json:"token_type"`
	User         map[string]interface{} `json:"user"`
	Claims       map[string]interface{} `json:"claims"`
}

// AuthError 认证错误
type AuthError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func (e *AuthError) Error() string {
	return e.Message
}

// 认证错误代码
const (
	AuthErrInvalidCredentials = "INVALID_CREDENTIALS"
	AuthErrTokenExpired       = "TOKEN_EXPIRED"
	AuthErrTokenInvalid       = "TOKEN_INVALID"
	AuthErrTokenRevoked       = "TOKEN_REVOKED"
	AuthErrUserNotFound       = "USER_NOT_FOUND"
	AuthErrUserInactive       = "USER_INACTIVE"
	AuthErrSiteNotFound       = "SITE_NOT_FOUND"
	AuthErrPermissionDenied   = "PERMISSION_DENIED"
)

// TokenClaims 令牌声明结构
type TokenClaims struct {
	UserID    uint   `json:"user_id"`
	Username  string `json:"username"`
	UserType  string `json:"user_type"`
	SiteID    uint   `json:"site_id"`
	Email     string `json:"email,omitempty"`
	ExpiresAt int64  `json:"exp"`
	IssuedAt  int64  `json:"iat"`
	Issuer    string `json:"iss"`
	Subject   string `json:"sub"`
}

// AuthContext 认证上下文接口
type AuthContext interface {
	// GetCurrentUser 获取当前认证用户
	GetCurrentUser(ctx context.Context) (*TokenClaims, error)
	
	// GetUserID 获取当前用户ID
	GetUserID(ctx context.Context) (uint, error)
	
	// GetSiteID 获取当前站点ID
	GetSiteID(ctx context.Context) (uint, error)
	
	// GetUserType 获取当前用户类型
	GetUserType(ctx context.Context) (string, error)
	
	// IsAuthenticated 检查是否已认证
	IsAuthenticated(ctx context.Context) bool
	
	// HasRole 检查是否有指定角色
	HasRole(ctx context.Context, role string) bool
	
	// SetCurrentUser 设置当前用户到上下文
	SetCurrentUser(ctx context.Context, claims *TokenClaims) context.Context
}

// AuthMiddleware 认证中间件接口
type AuthMiddleware interface {
	// RequireAuth 要求认证
	RequireAuth() func(c *gin.Context)
	
	// RequireRole 要求指定角色
	RequireRole(role string) func(c *gin.Context)
	
	// RequirePermission 要求指定权限
	RequirePermission(permission string) func(c *gin.Context)
	
	// OptionalAuth 可选认证
	OptionalAuth() func(c *gin.Context)
}

// SessionManager 会话管理接口（扩展）
type SessionManager interface {
	// CreateSession 创建会话
	CreateSession(ctx context.Context, userID uint, userType string, siteID uint) (string, error)
	
	// GetSession 获取会话
	GetSession(ctx context.Context, sessionID string) (*TokenClaims, error)
	
	// UpdateSession 更新会话
	UpdateSession(ctx context.Context, sessionID string, data map[string]interface{}) error
	
	// DestroySession 销毁会话
	DestroySession(ctx context.Context, sessionID string) error
	
	// CleanupExpiredSessions 清理过期会话
	CleanupExpiredSessions(ctx context.Context) error
	
	// GetActiveSessions 获取用户的活跃会话
	GetActiveSessions(ctx context.Context, userID uint) ([]string, error)
	
	// DestroyAllUserSessions 销毁用户的所有会话
	DestroyAllUserSessions(ctx context.Context, userID uint) error
}
