/*
Author: <PERSON><PERSON>eh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package service

import (
	"context"
	"sync"
	"time"

	"go.uber.org/fx"
	"go.uber.org/zap"
)

// ModulePerformanceManager 模块性能管理器接口
type ModulePerformanceManager interface {
	// 性能监控
	StartLoadTimer(moduleName string) *LoadTimer
	RecordLoadTime(moduleName string, duration time.Duration)
	GetLoadStats(moduleName string) *LoadStats
	GetOverallStats() *OverallStats
	
	// 缓存管理
	SetCacheEnabled(enabled bool)
	IsCacheEnabled() bool
	ClearCache()
	GetCacheStats() *CacheStats
	
	// 预加载管理
	AddToPreloadList(moduleName string, priority int)
	RemoveFromPreloadList(moduleName string)
	GetPreloadList() []PreloadItem
	ExecutePreload(ctx context.Context) error
	
	// 性能优化建议
	GetPerformanceRecommendations() []PerformanceRecommendation
}

// LoadTimer 加载计时器
type LoadTimer struct {
	ModuleName string
	StartTime  time.Time
	manager    *DefaultModulePerformanceManager
}

// Stop 停止计时并记录
func (t *LoadTimer) Stop() {
	duration := time.Since(t.StartTime)
	t.manager.RecordLoadTime(t.ModuleName, duration)
}

// LoadStats 模块加载统计
type LoadStats struct {
	ModuleName    string        `json:"module_name"`
	LoadCount     int64         `json:"load_count"`
	TotalTime     time.Duration `json:"total_time"`
	AverageTime   time.Duration `json:"average_time"`
	MinTime       time.Duration `json:"min_time"`
	MaxTime       time.Duration `json:"max_time"`
	LastLoadTime  time.Time     `json:"last_load_time"`
	CacheHits     int64         `json:"cache_hits"`
	CacheMisses   int64         `json:"cache_misses"`
}

// OverallStats 整体统计
type OverallStats struct {
	TotalModules     int                    `json:"total_modules"`
	LoadedModules    int                    `json:"loaded_modules"`
	CachedModules    int                    `json:"cached_modules"`
	TotalLoadTime    time.Duration          `json:"total_load_time"`
	AverageLoadTime  time.Duration          `json:"average_load_time"`
	CacheHitRate     float64                `json:"cache_hit_rate"`
	ModuleStats      map[string]*LoadStats  `json:"module_stats"`
}

// CacheStats 缓存统计
type CacheStats struct {
	Enabled       bool    `json:"enabled"`
	Size          int     `json:"size"`
	HitRate       float64 `json:"hit_rate"`
	TotalHits     int64   `json:"total_hits"`
	TotalMisses   int64   `json:"total_misses"`
	TotalRequests int64   `json:"total_requests"`
}

// PreloadItem 预加载项
type PreloadItem struct {
	ModuleName string `json:"module_name"`
	Priority   int    `json:"priority"` // 数字越小优先级越高
}

// PerformanceRecommendation 性能优化建议
type PerformanceRecommendation struct {
	Type        string `json:"type"`
	ModuleName  string `json:"module_name,omitempty"`
	Description string `json:"description"`
	Impact      string `json:"impact"` // high, medium, low
	Action      string `json:"action"`
}

// DefaultModulePerformanceManager 默认模块性能管理器实现
type DefaultModulePerformanceManager struct {
	// 统计数据
	moduleStats map[string]*LoadStats
	statsMu     sync.RWMutex
	
	// 缓存配置
	cacheEnabled bool
	cacheMu      sync.RWMutex
	
	// 预加载配置
	preloadList []PreloadItem
	preloadMu   sync.RWMutex
	
	// 依赖
	moduleFactory *ModuleProxyFactory
	logger        *zap.Logger
}

// ModulePerformanceManagerParams fx依赖注入参数
type ModulePerformanceManagerParams struct {
	fx.In

	ModuleFactory *ModuleProxyFactory
	Logger        *zap.Logger
}

// NewDefaultModulePerformanceManager 创建默认模块性能管理器
func NewDefaultModulePerformanceManager(params ModulePerformanceManagerParams) ModulePerformanceManager {
	return &DefaultModulePerformanceManager{
		moduleStats:   make(map[string]*LoadStats),
		cacheEnabled:  true, // 默认启用缓存
		preloadList:   make([]PreloadItem, 0),
		moduleFactory: params.ModuleFactory,
		logger:        params.Logger,
	}
}

// StartLoadTimer 开始加载计时
func (m *DefaultModulePerformanceManager) StartLoadTimer(moduleName string) *LoadTimer {
	return &LoadTimer{
		ModuleName: moduleName,
		StartTime:  time.Now(),
		manager:    m,
	}
}

// RecordLoadTime 记录加载时间
func (m *DefaultModulePerformanceManager) RecordLoadTime(moduleName string, duration time.Duration) {
	m.statsMu.Lock()
	defer m.statsMu.Unlock()
	
	stats, exists := m.moduleStats[moduleName]
	if !exists {
		stats = &LoadStats{
			ModuleName: moduleName,
			MinTime:    duration,
			MaxTime:    duration,
		}
		m.moduleStats[moduleName] = stats
	}
	
	// 更新统计
	stats.LoadCount++
	stats.TotalTime += duration
	stats.AverageTime = stats.TotalTime / time.Duration(stats.LoadCount)
	stats.LastLoadTime = time.Now()
	
	if duration < stats.MinTime {
		stats.MinTime = duration
	}
	if duration > stats.MaxTime {
		stats.MaxTime = duration
	}
	
	m.logger.Debug("Module load time recorded",
		zap.String("module", moduleName),
		zap.Duration("duration", duration),
		zap.Duration("average", stats.AverageTime),
	)
}

// GetLoadStats 获取模块加载统计
func (m *DefaultModulePerformanceManager) GetLoadStats(moduleName string) *LoadStats {
	m.statsMu.RLock()
	defer m.statsMu.RUnlock()
	
	if stats, exists := m.moduleStats[moduleName]; exists {
		// 返回副本
		statsCopy := *stats
		return &statsCopy
	}
	
	return nil
}

// GetOverallStats 获取整体统计
func (m *DefaultModulePerformanceManager) GetOverallStats() *OverallStats {
	m.statsMu.RLock()
	defer m.statsMu.RUnlock()
	
	stats := &OverallStats{
		TotalModules:  len(m.moduleStats),
		LoadedModules: 0,
		CachedModules: 0,
		ModuleStats:   make(map[string]*LoadStats),
	}
	
	var totalLoadTime time.Duration
	var totalHits, totalMisses int64
	
	for name, moduleStats := range m.moduleStats {
		// 复制统计数据
		statsCopy := *moduleStats
		stats.ModuleStats[name] = &statsCopy
		
		if moduleStats.LoadCount > 0 {
			stats.LoadedModules++
		}
		
		totalLoadTime += moduleStats.TotalTime
		totalHits += moduleStats.CacheHits
		totalMisses += moduleStats.CacheMisses
	}
	
	stats.TotalLoadTime = totalLoadTime
	if stats.LoadedModules > 0 {
		stats.AverageLoadTime = totalLoadTime / time.Duration(stats.LoadedModules)
	}
	
	totalRequests := totalHits + totalMisses
	if totalRequests > 0 {
		stats.CacheHitRate = float64(totalHits) / float64(totalRequests)
	}
	
	return stats
}

// SetCacheEnabled 设置缓存启用状态
func (m *DefaultModulePerformanceManager) SetCacheEnabled(enabled bool) {
	m.cacheMu.Lock()
	defer m.cacheMu.Unlock()
	
	m.cacheEnabled = enabled
	
	m.logger.Info("Module cache status changed",
		zap.Bool("enabled", enabled),
	)
}

// IsCacheEnabled 检查缓存是否启用
func (m *DefaultModulePerformanceManager) IsCacheEnabled() bool {
	m.cacheMu.RLock()
	defer m.cacheMu.RUnlock()
	
	return m.cacheEnabled
}

// ClearCache 清空缓存
func (m *DefaultModulePerformanceManager) ClearCache() {
	// 这里应该调用ModuleProxyFactory的清空缓存方法
	// 暂时记录日志
	m.logger.Info("Module cache cleared")
}

// GetCacheStats 获取缓存统计
func (m *DefaultModulePerformanceManager) GetCacheStats() *CacheStats {
	m.statsMu.RLock()
	defer m.statsMu.RUnlock()
	
	var totalHits, totalMisses int64
	for _, stats := range m.moduleStats {
		totalHits += stats.CacheHits
		totalMisses += stats.CacheMisses
	}
	
	totalRequests := totalHits + totalMisses
	hitRate := 0.0
	if totalRequests > 0 {
		hitRate = float64(totalHits) / float64(totalRequests)
	}
	
	return &CacheStats{
		Enabled:       m.cacheEnabled,
		Size:          len(m.moduleStats),
		HitRate:       hitRate,
		TotalHits:     totalHits,
		TotalMisses:   totalMisses,
		TotalRequests: totalRequests,
	}
}

// AddToPreloadList 添加到预加载列表
func (m *DefaultModulePerformanceManager) AddToPreloadList(moduleName string, priority int) {
	m.preloadMu.Lock()
	defer m.preloadMu.Unlock()
	
	// 检查是否已存在
	for i, item := range m.preloadList {
		if item.ModuleName == moduleName {
			// 更新优先级
			m.preloadList[i].Priority = priority
			return
		}
	}
	
	// 添加新项
	m.preloadList = append(m.preloadList, PreloadItem{
		ModuleName: moduleName,
		Priority:   priority,
	})
	
	m.logger.Debug("Module added to preload list",
		zap.String("module", moduleName),
		zap.Int("priority", priority),
	)
}

// RemoveFromPreloadList 从预加载列表移除
func (m *DefaultModulePerformanceManager) RemoveFromPreloadList(moduleName string) {
	m.preloadMu.Lock()
	defer m.preloadMu.Unlock()
	
	for i, item := range m.preloadList {
		if item.ModuleName == moduleName {
			// 移除项
			m.preloadList = append(m.preloadList[:i], m.preloadList[i+1:]...)
			
			m.logger.Debug("Module removed from preload list",
				zap.String("module", moduleName),
			)
			return
		}
	}
}

// GetPreloadList 获取预加载列表
func (m *DefaultModulePerformanceManager) GetPreloadList() []PreloadItem {
	m.preloadMu.RLock()
	defer m.preloadMu.RUnlock()
	
	// 返回副本
	result := make([]PreloadItem, len(m.preloadList))
	copy(result, m.preloadList)
	
	return result
}

// ExecutePreload 执行预加载
func (m *DefaultModulePerformanceManager) ExecutePreload(ctx context.Context) error {
	preloadList := m.GetPreloadList()
	if len(preloadList) == 0 {
		return nil
	}

	// 按优先级排序（数字越小优先级越高）
	for i := 0; i < len(preloadList)-1; i++ {
		for j := i + 1; j < len(preloadList); j++ {
			if preloadList[i].Priority > preloadList[j].Priority {
				preloadList[i], preloadList[j] = preloadList[j], preloadList[i]
			}
		}
	}

	m.logger.Info("Starting module preload",
		zap.Int("count", len(preloadList)),
	)

	startTime := time.Now()
	successCount := 0

	for _, item := range preloadList {
		timer := m.StartLoadTimer(item.ModuleName)

		_, err := m.moduleFactory.GetModule(ctx, item.ModuleName)
		timer.Stop()

		if err != nil {
			m.logger.Warn("Failed to preload module",
				zap.String("module", item.ModuleName),
				zap.Error(err),
			)
		} else {
			successCount++
			m.logger.Debug("Module preloaded successfully",
				zap.String("module", item.ModuleName),
			)
		}
	}

	totalTime := time.Since(startTime)
	m.logger.Info("Module preload completed",
		zap.Int("total", len(preloadList)),
		zap.Int("success", successCount),
		zap.Int("failed", len(preloadList)-successCount),
		zap.Duration("total_time", totalTime),
	)

	return nil
}

// GetPerformanceRecommendations 获取性能优化建议
func (m *DefaultModulePerformanceManager) GetPerformanceRecommendations() []PerformanceRecommendation {
	var recommendations []PerformanceRecommendation

	stats := m.GetOverallStats()

	// 检查缓存命中率
	if stats.CacheHitRate < 0.8 {
		recommendations = append(recommendations, PerformanceRecommendation{
			Type:        "cache",
			Description: "Cache hit rate is low, consider enabling cache or reviewing cache strategy",
			Impact:      "high",
			Action:      "Enable module caching and review frequently accessed modules",
		})
	}

	// 检查慢加载模块
	for name, moduleStats := range stats.ModuleStats {
		if moduleStats.AverageTime > 100*time.Millisecond {
			recommendations = append(recommendations, PerformanceRecommendation{
				Type:        "slow_load",
				ModuleName:  name,
				Description: "Module has slow average load time",
				Impact:      "medium",
				Action:      "Consider adding to preload list or optimizing module initialization",
			})
		}

		// 检查频繁加载的模块
		if moduleStats.LoadCount > 10 && moduleStats.CacheHits == 0 {
			recommendations = append(recommendations, PerformanceRecommendation{
				Type:        "frequent_load",
				ModuleName:  name,
				Description: "Module is loaded frequently but not cached",
				Impact:      "medium",
				Action:      "Add to preload list or ensure caching is enabled",
			})
		}
	}

	// 检查预加载建议
	if len(m.GetPreloadList()) == 0 && stats.LoadedModules > 3 {
		recommendations = append(recommendations, PerformanceRecommendation{
			Type:        "preload",
			Description: "No modules in preload list, consider preloading frequently used modules",
			Impact:      "low",
			Action:      "Add core modules to preload list",
		})
	}

	return recommendations
}

// RecordCacheHit 记录缓存命中
func (m *DefaultModulePerformanceManager) RecordCacheHit(moduleName string) {
	m.statsMu.Lock()
	defer m.statsMu.Unlock()

	stats, exists := m.moduleStats[moduleName]
	if !exists {
		stats = &LoadStats{
			ModuleName: moduleName,
		}
		m.moduleStats[moduleName] = stats
	}

	stats.CacheHits++
}

// RecordCacheMiss 记录缓存未命中
func (m *DefaultModulePerformanceManager) RecordCacheMiss(moduleName string) {
	m.statsMu.Lock()
	defer m.statsMu.Unlock()

	stats, exists := m.moduleStats[moduleName]
	if !exists {
		stats = &LoadStats{
			ModuleName: moduleName,
		}
		m.moduleStats[moduleName] = stats
	}

	stats.CacheMisses++
}
