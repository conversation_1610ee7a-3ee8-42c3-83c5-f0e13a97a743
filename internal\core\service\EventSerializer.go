/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/EventSerializer.go
 * @Description: 事件序列化器实现
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"sync"
	"time"

	"gacms/internal/core/bus"
	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// SerializedEvent 表示序列化后的事件
type SerializedEvent struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	Payload   interface{}            `json:"payload"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// DefaultEventSerializer 是 contract.EventSerializer 接口的默认实现
type DefaultEventSerializer struct {
	typeRegistry map[string]reflect.Type
	mu           sync.RWMutex
	logger       *zap.Logger
}

// DefaultEventSerializerParams 定义了创建 DefaultEventSerializer 所需的参数
type DefaultEventSerializerParams struct {
	fx.In

	Logger *zap.Logger
}

// NewDefaultEventSerializer 创建一个新的 DefaultEventSerializer 实例
func NewDefaultEventSerializer(params DefaultEventSerializerParams) contract.EventSerializer {
	return &DefaultEventSerializer{
		typeRegistry: make(map[string]reflect.Type),
		logger:       params.Logger,
	}
}

// Serialize 将事件序列化为字节数组
func (s *DefaultEventSerializer) Serialize(event contract.Event) ([]byte, error) {
	if event == nil {
		return nil, errors.New("event cannot be nil")
	}

	serialized := SerializedEvent{
		ID:        event.ID(),
		Name:      string(event.Name()),
		Payload:   event.Payload(),
		Timestamp: event.Timestamp(),
		Metadata:  event.AllMetadata(),
	}

	s.logger.Debug("Serializing event",
		zap.String("id", event.ID()),
		zap.String("name", string(event.Name())),
	)

	return json.Marshal(serialized)
}

// Deserialize 将字节数组反序列化为事件
func (s *DefaultEventSerializer) Deserialize(data []byte, eventType contract.EventName) (contract.Event, error) {
	if len(data) == 0 {
		return nil, errors.New("data cannot be empty")
	}

	var serialized SerializedEvent
	if err := json.Unmarshal(data, &serialized); err != nil {
		return nil, fmt.Errorf("failed to unmarshal event: %w", err)
	}

	// 验证事件类型是否匹配
	if eventType != "" && serialized.Name != string(eventType) {
		return nil, fmt.Errorf("event type mismatch: expected %s, got %s", eventType, serialized.Name)
	}

	// 创建基础事件
	baseEvent := &bus.BaseEvent{
		Id:        serialized.ID,
		Name:      contract.EventName(serialized.Name),
		Timestamp: serialized.Timestamp,
		Metadata:  serialized.Metadata,
	}

	// 处理载荷
	if serialized.Payload != nil {
		s.mu.RLock()
		payloadType, exists := s.typeRegistry[serialized.Name]
		s.mu.RUnlock()

		if exists {
			// 如果载荷类型已注册，尝试将载荷转换为正确的类型
			payloadBytes, err := json.Marshal(serialized.Payload)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal payload: %w", err)
			}

			payload := reflect.New(payloadType).Interface()
			if err := json.Unmarshal(payloadBytes, payload); err != nil {
				return nil, fmt.Errorf("failed to unmarshal payload: %w", err)
			}

			// 获取指针指向的值
			baseEvent.Payload = reflect.ValueOf(payload).Elem().Interface()
		} else {
			// 如果载荷类型未注册，保留原始载荷
			baseEvent.Payload = serialized.Payload
		}
	}

	s.logger.Debug("Deserialized event",
		zap.String("id", baseEvent.Id),
		zap.String("name", string(baseEvent.Name)),
	)

	return baseEvent, nil
}

// RegisterPayloadType 注册事件载荷类型
func (s *DefaultEventSerializer) RegisterPayloadType(eventName contract.EventName, payloadType interface{}) error {
	if eventName == "" {
		return errors.New("event name cannot be empty")
	}
	if payloadType == nil {
		return errors.New("payload type cannot be nil")
	}

	t := reflect.TypeOf(payloadType)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	s.typeRegistry[string(eventName)] = t
	s.logger.Debug("Registered payload type",
		zap.String("event", string(eventName)),
		zap.String("type", t.String()),
	)

	return nil
}

// RegisterEventType 注册事件类型
func (s *DefaultEventSerializer) RegisterEventType(eventType contract.EventName, factory func() contract.Event) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 创建一个示例事件来获取其类型信息
	sampleEvent := factory()
	if sampleEvent != nil && sampleEvent.Payload() != nil {
		payloadType := reflect.TypeOf(sampleEvent.Payload())
		if payloadType.Kind() == reflect.Ptr {
			payloadType = payloadType.Elem()
		}
		s.typeRegistry[string(eventType)] = payloadType

		s.logger.Debug("Registered event type",
			zap.String("event", string(eventType)),
			zap.String("type", payloadType.String()),
		)
	}
}

// GetRegisteredTypes 获取所有注册的载荷类型
func (s *DefaultEventSerializer) GetRegisteredTypes() map[contract.EventName]reflect.Type {
	s.mu.RLock()
	defer s.mu.RUnlock()

	result := make(map[contract.EventName]reflect.Type, len(s.typeRegistry))
	for name, t := range s.typeRegistry {
		result[contract.EventName(name)] = t
	}

	return result
} 