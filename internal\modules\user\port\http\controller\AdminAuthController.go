/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: {current_date}
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: {current_date}
 * @FilePath: internal/modules/user/port/http/controller/AdminAuthController.go
 * @Description: HTTP controller for backend user authentication endpoints.
 *
 * © {current_year} GACMS. All rights reserved.
 */
package controller

import (
	"gacms/internal/modules/user/application/service"
	pkgContract "gacms/pkg/contract"
	"gacms/pkg/response"
	"net/http"

	"github.com/gin-gonic/gin"
)

// AdminAuthController handles HTTP requests related to admin authentication.
type AdminAuthController struct {
	authService *service.AdminAuthService
	appCtx      pkgContract.AppContext
	permission  pkgContract.PermissionChecker
}

// NewAdminAuthController creates a new instance of AdminAuthController.
func NewAdminAuthController(
	authService *service.AdminAuthService,
	appCtx pkgContract.AppContext,
	permission pkgContract.PermissionChecker,
) *AdminAuthController {
	return &AdminAuthController{
		authService: authService,
		appCtx:      appCtx,
		permission:  permission,
	}
}

// RegisterRoutes sets up the routing for the admin authentication endpoints.
func (c *AdminAuthController) RegisterRoutes(rg *gin.RouterGroup) {
	// All routes are prefixed with /admin (or the configured admin_path) by the router manager.
	authGroup := rg.Group("/auth")
	{
		authGroup.POST("/login", c.login)
		authGroup.POST("/register", c.register)
		// This route is protected by a permission check.
		authGroup.GET("/me", c.permission.Wrap("user.profile.read", c.getMe))
	}
}

func (c *AdminAuthController) getMe(ctx *gin.Context) {
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Error(ctx, http.StatusUnauthorized, "User not found in context", nil)
		return
	}
	
	// In a real app, you'd fetch the user's details from a service.
	// For this example, we just return the ID from the context.
	response.Success(ctx, "Current user retrieved", gin.H{"user_id": userID})
}

func (c *AdminAuthController) login(ctx *gin.Context) {
	var dto service.LoginDTO
	if err := ctx.ShouldBindJSON(&dto); err != nil {
		response.Error(ctx, http.StatusBadRequest, "Invalid credentials", err.Error())
		return
	}

	token, err := c.authService.Login(ctx.Request.Context(), dto)
	if err != nil {
		response.Error(ctx, http.StatusUnauthorized, err.Error(), nil)
		return
	}

	response.Success(ctx, "Login successful", gin.H{"token": token})
}

func (c *AdminAuthController) register(ctx *gin.Context) {
	var dto service.RegisterDTO
	if err := ctx.ShouldBindJSON(&dto); err != nil {
		response.Error(ctx, http.StatusBadRequest, "Invalid registration data", err.Error())
		return
	}

	admin, err := c.authService.Register(ctx.Request.Context(), dto)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	response.Success(ctx, "Admin user created successfully", gin.H{"user_id": admin.ID})
} 