/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/LicenseManager.go
 * @Description: 统一许可证管理器实现，管理系统级许可证和模块级许可证
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"crypto/rsa"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"time"

	"gacms/internal/core/constants"
	"gacms/internal/infrastructure/database"
	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// SystemLicense 系统许可证（部署方）
type SystemLicense struct {
	LicenseID         string                 `json:"license_id"`
	LicenseType       string                 `json:"license_type"`       // "system"
	Licensee          string                 `json:"licensee"`           // 部署方名称
	Edition           contract.Edition       `json:"edition"`            // 系统最高版本
	MaxTenants        int                    `json:"max_tenants"`        // 默认租户数量
	AdditionalTenants int                    `json:"additional_tenants"` // 额外购买的租户数量
	IssuedAt          time.Time              `json:"issued_at"`
	ExpiresAt         time.Time              `json:"expires_at"`
	Features          []string               `json:"features"`
	Metadata          map[string]interface{} `json:"metadata,omitempty"`
	Signature         string                 `json:"signature"`
}

// UsageLicense 使用许可证（租户）
type UsageLicense struct {
	LicenseID        string                 `json:"license_id"`
	LicenseType      string                 `json:"license_type"`      // "usage"
	TenantDomain     string                 `json:"tenant_domain"`     // 租户域名
	Edition          contract.Edition       `json:"edition"`           // 租户版本
	SystemLicenseID  string                 `json:"system_license_id"` // 关联的系统许可证ID
	IssuedAt         time.Time              `json:"issued_at"`
	ExpiresAt        time.Time              `json:"expires_at"`
	Features         []string               `json:"features"`
	Limits           map[string]interface{} `json:"limits,omitempty"`
	DomainValidation string                 `json:"domain_validation"` // DNS TXT记录值
	Metadata         map[string]interface{} `json:"metadata,omitempty"`
	Signature        string                 `json:"signature"`
}

// SystemLicenseConfig 系统许可证配置
type SystemLicenseConfig struct {
	// 商业授权开关
	Enabled         bool   `yaml:"enabled"`
	DevelopmentMode bool   `yaml:"development_mode"`

	// 许可证文件路径
	SystemLicensePath string `yaml:"system_license_path"`
	UsageLicenseDir   string `yaml:"usage_license_dir"`

	// 验证配置
	ValidationInterval time.Duration `yaml:"validation_interval"`
	OfflineMode        bool          `yaml:"offline_mode"`
	LenientMode        bool          `yaml:"lenient_mode"`
	CacheDuration      time.Duration `yaml:"cache_duration"`

	// 域名验证
	DomainValidationEnabled bool `yaml:"domain_validation_enabled"`

	// 公钥文件路径
	PublicKeyPath string `yaml:"public_key_path"`
}

// DefaultLicenseManager 默认许可证管理器
// 统一管理系统级许可证和模块级许可证
type DefaultLicenseManager struct {
	// 模块许可证相关（保持不变）
	registry      contract.LicenseValidatorRegistry
	store         contract.LicenseStore
	eventManager  contract.EventManager
	moduleManager *ModuleManager
	logger        *zap.Logger
	config        *contract.LicenseConfig

	// 系统级许可证相关（新增）
	editionManager    EditionManager
	systemLicense     *SystemLicense
	usageLicenses     map[string]*UsageLicense // domain -> license
	systemConfig      *SystemLicenseConfig
	licenseCache      map[string]interface{}
	publicKey         *rsa.PublicKey
	personalProvider  constants.PersonalEditionProvider
}

// DefaultLicenseManagerParams 许可证管理器参数
type DefaultLicenseManagerParams struct {
	fx.In

	Registry       contract.LicenseValidatorRegistry
	Store          contract.LicenseStore
	EventManager   contract.EventManager
	ModuleManager  *ModuleManager
	Logger         *zap.Logger
	Config         *contract.LicenseConfig `optional:"true"`
	EditionManager EditionManager          `optional:"true"`
	SystemConfig   *SystemLicenseConfig    `optional:"true"`
}

// NewDefaultLicenseManager 创建默认许可证管理器
func NewDefaultLicenseManager(params DefaultLicenseManagerParams) contract.LicenseManager {
	config := params.Config
	if config == nil {
		// 默认配置
		config = &contract.LicenseConfig{
			ValidateOnStartup:      true,
			ValidationInterval:     24 * time.Hour,
			OfflineGracePeriod:     7 * 24 * time.Hour,
			CacheLicenseInfo:       true,
			CacheExpiration:        1 * time.Hour,
			AllowThirdPartyLicense: true,
		}
	}

	systemConfig := params.SystemConfig
	if systemConfig == nil {
		// 默认系统配置
		systemConfig = &SystemLicenseConfig{
			Enabled:                 false, // 默认关闭
			DevelopmentMode:         true,
			SystemLicensePath:       "./licenses/system_license.json",
			UsageLicenseDir:         "./licenses/usage/",
			ValidationInterval:      24 * time.Hour,
			OfflineMode:             true,
			LenientMode:             true,
			CacheDuration:           1 * time.Hour,
			DomainValidationEnabled: false,
			PublicKeyPath:           "./keys/gacms_public.pem",
		}
	}

	manager := &DefaultLicenseManager{
		registry:         params.Registry,
		store:            params.Store,
		eventManager:     params.EventManager,
		moduleManager:    params.ModuleManager,
		logger:           params.Logger,
		config:           config,
		editionManager:   params.EditionManager,
		systemConfig:     systemConfig,
		usageLicenses:    make(map[string]*UsageLicense),
		licenseCache:     make(map[string]interface{}),
		personalProvider: constants.GetPersonalEditionProvider(),
	}

	// 加载系统级许可证
	if err := manager.initializeSystemLicenses(); err != nil {
		params.Logger.Warn("Failed to initialize system licenses", zap.Error(err))
	}

	return manager
}

// ActivateModuleLicense 激活模块许可证（多租户支持）
func (m *DefaultLicenseManager) ActivateModuleLicense(ctx context.Context, moduleName, licenseKey string) error {
	// 获取租户信息
	siteID, hasSiteID := database.SiteIDFrom(ctx)

	m.logger.Info("Activating module license",
		zap.String("module", moduleName),
		zap.String("license_key", licenseKey[:min(8, len(licenseKey))]+"..."),
		zap.Uint("site_id", siteID),
		zap.Bool("has_site_id", hasSiteID),
	)

	// 获取模块信息（租户感知）
	var moduleInfo *ModuleRecipe
	var err error

	if hasSiteID {
		moduleInfo, err = m.moduleManager.GetModuleInfoForSite(ctx, moduleName)
	} else {
		moduleInfo, err = m.moduleManager.GetModuleInfo(moduleName)
	}

	if err != nil {
		return fmt.Errorf("failed to get module info: %w", err)
	}

	// 检查模块是否需要许可证
	if !moduleInfo.RequiresActivation() {
		return fmt.Errorf("module %s does not require license activation", moduleName)
	}

	// 确定验证器
	validatorName := moduleInfo.LicenseValidator
	if validatorName == "" {
		validatorName = moduleInfo.GetLicenseProvider()
	}

	// 验证许可证
	licenseInfo, err := m.registry.ValidateModuleLicense(ctx, moduleName, licenseKey, validatorName)
	if err != nil {
		m.publishLicenseEventWithContext(ctx, contract.LicenseInvalid, moduleName, licenseKey, validatorName, false, err.Error())
		return fmt.Errorf("license validation failed: %w", err)
	}

	if !licenseInfo.IsValid {
		m.publishLicenseEventWithContext(ctx, contract.LicenseInvalid, moduleName, licenseKey, validatorName, false, licenseInfo.ErrorMsg)
		return fmt.Errorf("invalid license: %s", licenseInfo.ErrorMsg)
	}

	// 保存许可证信息
	if err := m.store.SaveLicenseInfo(ctx, moduleName, licenseInfo); err != nil {
		m.logger.Error("Failed to save license info", zap.Error(err))
		// 不返回错误，因为验证已经成功
	}

	// 激活模块许可证
	if moduleInfo.UsesOfficialLicense() {
		officialValidator, err := m.registry.GetOfficialValidator()
		if err == nil {
			if err := officialValidator.ActivateLicense(ctx, licenseKey, moduleName); err != nil {
				m.logger.Error("Failed to activate official license", zap.Error(err))
			}
		}
	}

	// 更新模块状态
	if moduleInfo.UsesThirdPartyLicense() {
		moduleInfo.ActivateThirdPartyLicense(licenseKey, validatorName, licenseInfo.Metadata)
	} else {
		moduleInfo.ActivateLicense(licenseKey)
	}

	// 发布许可证激活事件
	m.publishLicenseEventWithContext(ctx, contract.LicenseActivated, moduleName, licenseKey, validatorName, true, "")

	m.logger.Info("Module license activated successfully",
		zap.String("module", moduleName),
		zap.String("validator", validatorName),
	)

	return nil
}

// DeactivateModuleLicense 停用模块许可证（多租户支持）
func (m *DefaultLicenseManager) DeactivateModuleLicense(ctx context.Context, moduleName string) error {
	// 获取租户信息
	siteID, hasSiteID := database.SiteIDFrom(ctx)

	m.logger.Info("Deactivating module license",
		zap.String("module", moduleName),
		zap.Uint("site_id", siteID),
		zap.Bool("has_site_id", hasSiteID),
	)

	// 获取模块信息（租户感知）
	var moduleInfo *ModuleRecipe
	var err error

	if hasSiteID {
		moduleInfo, err = m.moduleManager.GetModuleInfoForSite(ctx, moduleName)
	} else {
		moduleInfo, err = m.moduleManager.GetModuleInfo(moduleName)
	}

	if err != nil {
		return fmt.Errorf("failed to get module info: %w", err)
	}

	if !moduleInfo.RequiresActivation() {
		return fmt.Errorf("module %s does not require license activation", moduleName)
	}

	// 停用官方许可证
	if moduleInfo.UsesOfficialLicense() && moduleInfo.LicenseKey != "" {
		officialValidator, err := m.registry.GetOfficialValidator()
		if err == nil {
			if err := officialValidator.DeactivateLicense(ctx, moduleInfo.LicenseKey, moduleName); err != nil {
				m.logger.Error("Failed to deactivate official license", zap.Error(err))
			}
		}
	}

	// 删除许可证信息
	if err := m.store.DeleteLicenseInfo(ctx, moduleName); err != nil {
		m.logger.Error("Failed to delete license info", zap.Error(err))
	}

	// 更新模块状态
	licenseKey := moduleInfo.LicenseKey
	validatorName := moduleInfo.LicenseValidator
	moduleInfo.DeactivateLicense()

	// 发布许可证停用事件
	m.publishLicenseEventWithContext(ctx, contract.LicenseDeactivated, moduleName, licenseKey, validatorName, true, "")

	m.logger.Info("Module license deactivated successfully", zap.String("module", moduleName))

	return nil
}

// ValidateModuleLicense 验证模块许可证（多租户支持）
func (m *DefaultLicenseManager) ValidateModuleLicense(ctx context.Context, moduleName string) (*contract.LicenseInfo, error) {
	// 获取租户信息
	siteID, hasSiteID := database.SiteIDFrom(ctx)

	// 获取模块信息（租户感知）
	var moduleInfo *ModuleRecipe
	var err error

	if hasSiteID {
		moduleInfo, err = m.moduleManager.GetModuleInfoForSite(ctx, moduleName)
	} else {
		moduleInfo, err = m.moduleManager.GetModuleInfo(moduleName)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to get module info: %w", err)
	}

	if !moduleInfo.RequiresActivation() {
		// 不需要许可证的模块，返回默认有效信息
		return &contract.LicenseInfo{
			ModuleName: moduleName,
			Type:       "none",
			IsValid:    true,
		}, nil
	}

	if !moduleInfo.IsLicenseActivated() {
		return &contract.LicenseInfo{
			ModuleName: moduleName,
			IsValid:    false,
			ErrorMsg:   "license not activated",
		}, nil
	}

	// 从缓存获取许可证信息
	if m.config.CacheLicenseInfo {
		licenseInfo, err := m.store.GetLicenseInfo(ctx, moduleName)
		if err == nil && licenseInfo != nil {
			// 检查缓存是否过期
			if time.Since(licenseInfo.IssuedAt) < m.config.CacheExpiration {
				return licenseInfo, nil
			}
		}
	}

	// 重新验证许可证
	validatorName := moduleInfo.LicenseValidator
	if validatorName == "" {
		validatorName = moduleInfo.GetLicenseProvider()
	}

	licenseInfo, err := m.registry.ValidateModuleLicense(ctx, moduleName, moduleInfo.LicenseKey, validatorName)
	if err != nil {
		m.publishLicenseEventWithContext(ctx, contract.LicenseInvalid, moduleName, moduleInfo.LicenseKey, validatorName, false, err.Error())
		return nil, fmt.Errorf("license validation failed: %w", err)
	}

	// 更新缓存
	if m.config.CacheLicenseInfo {
		if err := m.store.SaveLicenseInfo(ctx, moduleName, licenseInfo); err != nil {
			m.logger.Error("Failed to update license cache", zap.Error(err))
		}
	}

	// 发布验证事件
	eventType := contract.LicenseValidated
	if !licenseInfo.IsValid {
		eventType = contract.LicenseInvalid
	}
	m.publishLicenseEventWithContext(ctx, eventType, moduleName, moduleInfo.LicenseKey, validatorName, licenseInfo.IsValid, licenseInfo.ErrorMsg)

	return licenseInfo, nil
}

// GetModuleLicenseInfo 获取模块许可证信息
func (m *DefaultLicenseManager) GetModuleLicenseInfo(ctx context.Context, moduleName string) (*contract.LicenseInfo, error) {
	return m.store.GetLicenseInfo(ctx, moduleName)
}

// ListLicensedModules 列出已授权的模块
func (m *DefaultLicenseManager) ListLicensedModules(ctx context.Context) ([]string, error) {
	return m.store.ListLicensedModules(ctx)
}

// RegisterThirdPartyValidator 注册第三方验证器
func (m *DefaultLicenseManager) RegisterThirdPartyValidator(validator contract.ThirdPartyLicenseValidator) error {
	if !m.config.AllowThirdPartyLicense {
		return fmt.Errorf("third party license validation is disabled")
	}

	return m.registry.RegisterValidator(validator)
}

// SetModuleLicenseValidator 设置模块的许可证验证器
func (m *DefaultLicenseManager) SetModuleLicenseValidator(moduleName, validatorName string) error {
	moduleInfo, err := m.moduleManager.GetModuleInfo(moduleName)
	if err != nil {
		return fmt.Errorf("failed to get module info: %w", err)
	}

	// 验证验证器是否存在
	_, err = m.registry.GetValidator(validatorName)
	if err != nil {
		return fmt.Errorf("validator not found: %w", err)
	}

	moduleInfo.LicenseValidator = validatorName
	m.logger.Info("Module license validator set",
		zap.String("module", moduleName),
		zap.String("validator", validatorName),
	)

	return nil
}

// publishLicenseEvent 发布许可证事件（多租户支持）
func (m *DefaultLicenseManager) publishLicenseEvent(eventType contract.LicenseEventType, moduleName, licenseKey, validator string, success bool, errorMsg string) {
	m.publishLicenseEventWithContext(context.Background(), eventType, moduleName, licenseKey, validator, success, errorMsg)
}

// publishLicenseEventWithContext 发布带上下文的许可证事件
func (m *DefaultLicenseManager) publishLicenseEventWithContext(ctx context.Context, eventType contract.LicenseEventType, moduleName, licenseKey, validator string, success bool, errorMsg string) {
	// 获取租户信息
	siteID, hasSiteID := database.SiteIDFrom(ctx)

	event := m.eventManager.CreateEvent(ctx, "license."+string(eventType), contract.LicenseEvent{
		Type:       eventType,
		ModuleName: moduleName,
		LicenseKey: licenseKey,
		Validator:  validator,
		Timestamp:  time.Now(),
		Success:    success,
		ErrorMsg:   errorMsg,
	})

	if err := m.eventManager.PublishEvent(event); err != nil {
		m.logger.Error("Failed to publish license event",
			zap.String("event_type", string(eventType)),
			zap.String("module", moduleName),
			zap.Uint("site_id", siteID),
			zap.Bool("has_site_id", hasSiteID),
			zap.Error(err),
		)
	}
}

// ActivateModuleLicenseForSite 为指定站点激活模块许可证
func (m *DefaultLicenseManager) ActivateModuleLicenseForSite(siteID uint, moduleName, licenseKey string) error {
	ctx := database.WithSiteID(context.Background(), siteID)
	return m.ActivateModuleLicense(ctx, moduleName, licenseKey)
}

// DeactivateModuleLicenseForSite 为指定站点停用模块许可证
func (m *DefaultLicenseManager) DeactivateModuleLicenseForSite(siteID uint, moduleName string) error {
	ctx := database.WithSiteID(context.Background(), siteID)
	return m.DeactivateModuleLicense(ctx, moduleName)
}

// ValidateModuleLicenseForSite 为指定站点验证模块许可证
func (m *DefaultLicenseManager) ValidateModuleLicenseForSite(siteID uint, moduleName string) (*contract.LicenseInfo, error) {
	ctx := database.WithSiteID(context.Background(), siteID)
	return m.ValidateModuleLicense(ctx, moduleName)
}

// GetModuleLicenseInfoForSite 获取指定站点的模块许可证信息
func (m *DefaultLicenseManager) GetModuleLicenseInfoForSite(siteID uint, moduleName string) (*contract.LicenseInfo, error) {
	ctx := database.WithSiteID(context.Background(), siteID)
	return m.GetModuleLicenseInfo(ctx, moduleName)
}

// ListLicensedModulesForSite 列出指定站点的已授权模块
func (m *DefaultLicenseManager) ListLicensedModulesForSite(siteID uint) ([]string, error) {
	ctx := database.WithSiteID(context.Background(), siteID)
	return m.ListLicensedModules(ctx)
}

// SetModuleLicenseValidatorForSite 为指定站点设置模块的许可证验证器
func (m *DefaultLicenseManager) SetModuleLicenseValidatorForSite(siteID uint, moduleName, validatorName string) error {
	// 获取站点特定的模块信息
	ctx := database.WithSiteID(context.Background(), siteID)
	moduleInfo, err := m.moduleManager.GetModuleInfoForSite(ctx, moduleName)
	if err != nil {
		return fmt.Errorf("failed to get module info for site %d: %w", siteID, err)
	}

	// 验证验证器是否存在
	_, err = m.registry.GetValidator(validatorName)
	if err != nil {
		return fmt.Errorf("validator not found: %w", err)
	}

	moduleInfo.LicenseValidator = validatorName
	m.logger.Info("Module license validator set for site",
		zap.Uint("site_id", siteID),
		zap.String("module", moduleName),
		zap.String("validator", validatorName),
	)

	return nil
}

// ValidateLicense 验证许可证（全局）
func (m *DefaultLicenseManager) ValidateLicense(ctx context.Context) (*contract.LicenseInfo, error) {
	// 如果商业授权未启用，返回开发版信息
	if !m.isCommercialAuthEnabled() {
		return &contract.LicenseInfo{
			ModuleName: "system",
			Type:       "development",
			Edition:    contract.EditionBusiness, // 开发模式默认商业版
			IsValid:    true,
		}, nil
	}

	// 验证系统许可证
	if m.systemLicense == nil {
		return &contract.LicenseInfo{
			ModuleName: "system",
			Type:       "none",
			Edition:    contract.EditionPersonal,
			IsValid:    false,
			ErrorMsg:   "system license not found",
		}, nil
	}

	// 检查系统许可证是否过期
	if time.Now().After(m.systemLicense.ExpiresAt) {
		return &contract.LicenseInfo{
			ModuleName: "system",
			Type:       "system",
			Edition:    m.systemLicense.Edition,
			IsValid:    false,
			ErrorMsg:   "system license expired",
		}, nil
	}

	// 返回有效的系统许可证信息
	return &contract.LicenseInfo{
		ModuleName: "system",
		Type:       "system",
		Edition:    m.systemLicense.Edition,
		IssuedAt:   m.systemLicense.IssuedAt,
		ExpiresAt:  &m.systemLicense.ExpiresAt,
		Features:   m.systemLicense.Features,
		IsValid:    true,
	}, nil
}

// GetLicenseInfo 获取许可证信息（全局）
func (m *DefaultLicenseManager) GetLicenseInfo() *contract.LicenseInfo {
	// 如果商业授权未启用，返回开发版信息
	if !m.isCommercialAuthEnabled() {
		return &contract.LicenseInfo{
			ModuleName: "system",
			Type:       "development",
			Edition:    contract.EditionBusiness, // 开发模式默认商业版
			IsValid:    true,
		}
	}

	// 如果没有系统许可证，返回个人版信息
	if m.systemLicense == nil {
		return &contract.LicenseInfo{
			ModuleName: "system",
			Type:       "none",
			Edition:    contract.EditionPersonal,
			IsValid:    false,
		}
	}

	// 返回系统许可证信息
	return &contract.LicenseInfo{
		ModuleName: "system",
		Type:       "system",
		Edition:    m.systemLicense.Edition,
		IssuedAt:   m.systemLicense.IssuedAt,
		ExpiresAt:  &m.systemLicense.ExpiresAt,
		Features:   m.systemLicense.Features,
		IsValid:    time.Now().Before(m.systemLicense.ExpiresAt),
	}
}

// GetLicenseStatus 获取许可证状态
func (m *DefaultLicenseManager) GetLicenseStatus() contract.LicenseStatus {
	return contract.LicenseStatusValid
}

// InstallLicense 安装许可证
func (m *DefaultLicenseManager) InstallLicense(licenseData string) error {
	m.logger.Info("Installing license...")

	// 尝试解析为系统许可证
	var systemLicense SystemLicense
	if err := json.Unmarshal([]byte(licenseData), &systemLicense); err == nil {
		if systemLicense.LicenseType == "system" {
			return m.installSystemLicense(&systemLicense)
		}
	}

	// 尝试解析为使用许可证
	var usageLicense UsageLicense
	if err := json.Unmarshal([]byte(licenseData), &usageLicense); err == nil {
		if usageLicense.LicenseType == "usage" {
			return m.installUsageLicense(&usageLicense)
		}
	}

	return fmt.Errorf("invalid license format or type")
}

// RefreshLicense 刷新许可证
func (m *DefaultLicenseManager) RefreshLicense() error {
	m.logger.Info("Refreshing licenses...")

	// 清空缓存
	m.licenseCache = make(map[string]interface{})

	// 重新加载系统级许可证
	if err := m.initializeSystemLicenses(); err != nil {
		m.logger.Error("Failed to refresh system licenses", zap.Error(err))
		return fmt.Errorf("failed to refresh system licenses: %w", err)
	}

	// 刷新模块许可证缓存（如果有的话）
	// 这里可以调用现有的模块许可证刷新逻辑

	m.logger.Info("Licenses refreshed successfully")
	return nil
}

// IsModuleAuthorized 检查模块是否授权
func (m *DefaultLicenseManager) IsModuleAuthorized(ctx context.Context, moduleName string) bool {
	licenseInfo, err := m.ValidateModuleLicense(ctx, moduleName)
	return err == nil && licenseInfo != nil && licenseInfo.IsValid
}

// IsFeatureAuthorized 检查功能是否授权
func (m *DefaultLicenseManager) IsFeatureAuthorized(ctx context.Context, featureName string) bool {
	// 如果商业授权未启用，允许所有功能
	if !m.isCommercialAuthEnabled() {
		return true
	}

	// 从上下文获取租户域名
	tenantDomain := m.getTenantDomainFromContext(ctx)

	// 检查系统级功能授权
	if err := m.checkSystemFeatureAccess(ctx, tenantDomain, featureName); err != nil {
		// 系统级授权失败，尝试优雅降级到个人版
		if m.isPersonalFeature(featureName) {
			m.logger.Info("Feature authorized through personal edition degradation",
				zap.String("feature", featureName),
				zap.String("tenant", tenantDomain),
				zap.Error(err))
			return true
		}

		m.logger.Debug("System feature access denied",
			zap.String("feature", featureName),
			zap.String("tenant", tenantDomain),
			zap.Error(err))
		return false
	}



	// 检查模块级功能授权（如果是模块功能）
	if m.isModuleFeature(featureName) {
		return m.IsModuleAuthorized(ctx, m.getModuleNameFromFeature(featureName))
	}

	return true
}

// GetCacheStats 获取缓存统计
func (m *DefaultLicenseManager) GetCacheStats() *contract.LicenseCacheStats {
	return &contract.LicenseCacheStats{
		CacheEnabled:    m.config.CacheLicenseInfo,
		LastValidation:  time.Now(),
		ValidationCount: 0,
		CacheHits:       0,
		CacheMisses:     0,
	}
}

// ClearCache 清空缓存
func (m *DefaultLicenseManager) ClearCache() {
	// 清空系统级许可证缓存
	m.licenseCache = make(map[string]interface{})

	// 清空模块许可证缓存（如果有的话）
	// 这里可以调用现有的模块许可证缓存清空逻辑

	m.logger.Info("License cache cleared")
}

// ========== 系统级许可证公开API ==========

// GetSystemLicenseInfo 获取系统许可证信息
func (m *DefaultLicenseManager) GetSystemLicenseInfo() *SystemLicense {
	if !m.isCommercialAuthEnabled() {
		return nil
	}
	return m.systemLicense
}

// GetUsageLicenseInfo 获取使用许可证信息
func (m *DefaultLicenseManager) GetUsageLicenseInfo(tenantDomain string) *UsageLicense {
	if !m.isCommercialAuthEnabled() {
		return nil
	}

	rootDomain := m.extractRootDomain(tenantDomain)
	return m.usageLicenses[rootDomain]
}



// GetSystemEdition 获取系统版本
func (m *DefaultLicenseManager) GetSystemEdition() contract.Edition {
	if !m.isCommercialAuthEnabled() {
		return contract.EditionBusiness // 开发模式默认商业版
	}

	if m.systemLicense == nil {
		return contract.EditionPersonal // 默认个人版
	}

	return m.systemLicense.Edition
}

// GetTenantEdition 获取租户版本
func (m *DefaultLicenseManager) GetTenantEdition(tenantDomain string) contract.Edition {
	if !m.isCommercialAuthEnabled() {
		return contract.EditionBusiness // 开发模式默认商业版
	}

	// 个人版免费使用
	if m.isPersonalEditionFree(tenantDomain) {
		return contract.EditionPersonal
	}

	rootDomain := m.extractRootDomain(tenantDomain)
	if usageLicense, exists := m.usageLicenses[rootDomain]; exists {
		return usageLicense.Edition
	}

	return contract.EditionPersonal // 默认个人版
}

// IsPersonalEditionFree 检查个人版是否免费
func (m *DefaultLicenseManager) IsPersonalEditionFree(tenantDomain string) bool {
	// 个人版始终免费
	return true
}

// CheckTenantAccess 检查租户访问权限
func (m *DefaultLicenseManager) CheckTenantAccess(ctx context.Context, tenantDomain string, featureName string) error {
	// 如果商业授权未启用，允许所有访问
	if !m.isCommercialAuthEnabled() {
		return nil
	}

	// 1. 验证系统授权
	if m.systemLicense == nil {
		return fmt.Errorf("system license not found")
	}

	if time.Now().After(m.systemLicense.ExpiresAt) {
		return fmt.Errorf("system license expired")
	}

	// 2. 检查租户数量限制
	currentTenantCount := len(m.usageLicenses)
	maxTenants := m.systemLicense.MaxTenants + m.systemLicense.AdditionalTenants
	if maxTenants > 0 && currentTenantCount > maxTenants {
		return fmt.Errorf("tenant count (%d) exceeds system license limit (%d)", currentTenantCount, maxTenants)
	}

	// 3. 验证使用授权
	rootDomain := m.extractRootDomain(tenantDomain)
	usageLicense, exists := m.usageLicenses[rootDomain]
	if !exists {
		// 个人版免费使用
		if m.IsPersonalEditionFree(tenantDomain) {
			return m.checkPersonalEditionFeature(featureName)
		}
		return fmt.Errorf("usage license not found for domain: %s", rootDomain)
	}

	// 检查使用许可证是否过期
	if time.Now().After(usageLicense.ExpiresAt) {
		return fmt.Errorf("usage license expired for domain: %s", rootDomain)
	}

	// 4. 检查功能权限
	return m.checkFeatureAccess(usageLicense.Edition, featureName)
}

// GetMaxTenants 获取最大租户数量
func (m *DefaultLicenseManager) GetMaxTenants() int {
	if !m.isCommercialAuthEnabled() {
		return -1 // 无限制
	}

	if m.systemLicense == nil {
		return 1 // 默认1个租户
	}

	return m.systemLicense.MaxTenants + m.systemLicense.AdditionalTenants
}

// isPersonalEditionFree 检查个人版是否免费（内部方法）
func (m *DefaultLicenseManager) isPersonalEditionFree(tenantDomain string) bool {
	// 个人版始终免费
	return true
}

// checkPersonalEditionFeature 检查个人版功能权限
func (m *DefaultLicenseManager) checkPersonalEditionFeature(featureName string) error {
	if m.editionManager != nil {
		return m.editionManager.CheckFeatureAccess(contract.EditionPersonal, featureName)
	}
	return nil
}

// isPersonalFeature 检查是否是个人版支持的功能
func (m *DefaultLicenseManager) isPersonalFeature(featureName string) bool {
	return m.personalProvider.IsFeature(featureName)
}



// checkFeatureAccess 检查功能访问权限
func (m *DefaultLicenseManager) checkFeatureAccess(edition contract.Edition, featureName string) error {
	if m.editionManager != nil {
		return m.editionManager.CheckFeatureAccess(edition, featureName)
	}
	return nil
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// ========== 系统级许可证辅助方法 ==========

// isCommercialAuthEnabled 检查商业授权是否启用
func (m *DefaultLicenseManager) isCommercialAuthEnabled() bool {
	return m.systemConfig.Enabled && !m.systemConfig.DevelopmentMode
}

// initializeSystemLicenses 初始化系统级许可证
func (m *DefaultLicenseManager) initializeSystemLicenses() error {
	// 如果商业授权未启用，跳过加载
	if !m.isCommercialAuthEnabled() {
		return nil
	}

	// 加载系统许可证
	if err := m.loadSystemLicenseFromFile(); err != nil {
		return fmt.Errorf("failed to load system license: %w", err)
	}

	// 加载使用许可证
	if err := m.loadUsageLicensesFromDir(); err != nil {
		m.logger.Warn("Failed to load usage licenses", zap.Error(err))
		// 不返回错误，使用许可证是可选的
	}

	return nil
}

// getTenantDomainFromContext 从上下文获取租户域名
func (m *DefaultLicenseManager) getTenantDomainFromContext(ctx context.Context) string {
	// 这里可以从上下文中提取域名信息
	// 暂时返回默认值
	return "localhost"
}

// isModuleFeature 检查是否是模块功能
func (m *DefaultLicenseManager) isModuleFeature(featureName string) bool {
	// 检查功能名是否以模块前缀开头
	return strings.HasPrefix(featureName, "module.") || strings.HasPrefix(featureName, "vendor.")
}

// getModuleNameFromFeature 从功能名获取模块名
func (m *DefaultLicenseManager) getModuleNameFromFeature(featureName string) string {
	if strings.HasPrefix(featureName, "module.") {
		return strings.TrimPrefix(featureName, "module.")
	}
	if strings.HasPrefix(featureName, "vendor.") {
		return strings.TrimPrefix(featureName, "vendor.")
	}
	return featureName
}

// checkSystemFeatureAccess 检查系统级功能访问权限（简化版本）
func (m *DefaultLicenseManager) checkSystemFeatureAccess(ctx context.Context, tenantDomain string, featureName string) error {
	// 如果商业授权未启用，允许所有访问
	if !m.isCommercialAuthEnabled() {
		return nil
	}

	// 检查系统许可证是否有效
	if m.systemLicense == nil || time.Now().After(m.systemLicense.ExpiresAt) {
		return fmt.Errorf("system license invalid or expired")
	}

	// 检查功能是否在系统许可证中
	if !m.isFeatureInSystemLicense(featureName) {
		return fmt.Errorf("feature not authorized")
	}

	return nil
}



// isFeatureInSystemLicense 检查功能是否在系统许可证中
func (m *DefaultLicenseManager) isFeatureInSystemLicense(featureName string) bool {
	if m.systemLicense == nil {
		return false
	}

	// 检查功能列表
	for _, feature := range m.systemLicense.Features {
		if feature == featureName || feature == "*" {
			return true
		}
	}

	return false
}

// loadSystemLicenseFromFile 从文件加载系统许可证
func (m *DefaultLicenseManager) loadSystemLicenseFromFile() error {
	if m.systemConfig.SystemLicensePath == "" {
		return fmt.Errorf("system license path not configured")
	}

	data, err := ioutil.ReadFile(m.systemConfig.SystemLicensePath)
	if err != nil {
		if os.IsNotExist(err) {
			m.logger.Info("System license file not found, using default configuration")
			return nil
		}
		return fmt.Errorf("failed to read system license file: %w", err)
	}

	var license SystemLicense
	if err := json.Unmarshal(data, &license); err != nil {
		return fmt.Errorf("failed to parse system license: %w", err)
	}

	// 验证许可证格式
	if license.LicenseType != "system" {
		return fmt.Errorf("invalid license type: %s", license.LicenseType)
	}

	m.systemLicense = &license
	m.logger.Info("System license loaded successfully", zap.String("license_id", license.LicenseID))

	return nil
}

// loadUsageLicensesFromDir 从目录加载使用许可证
func (m *DefaultLicenseManager) loadUsageLicensesFromDir() error {
	if m.systemConfig.UsageLicenseDir == "" {
		return nil
	}

	// 检查目录是否存在
	if _, err := os.Stat(m.systemConfig.UsageLicenseDir); os.IsNotExist(err) {
		m.logger.Info("Usage license directory not found, skipping")
		return nil
	}

	// 读取目录中的所有.json文件
	files, err := filepath.Glob(filepath.Join(m.systemConfig.UsageLicenseDir, "*.json"))
	if err != nil {
		return fmt.Errorf("failed to list usage license files: %w", err)
	}

	for _, file := range files {
		if err := m.loadUsageLicenseFromFile(file); err != nil {
			m.logger.Warn("Failed to load usage license", zap.String("file", file), zap.Error(err))
		}
	}

	return nil
}

// loadUsageLicenseFromFile 从文件加载使用许可证
func (m *DefaultLicenseManager) loadUsageLicenseFromFile(filePath string) error {
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read usage license file: %w", err)
	}

	var license UsageLicense
	if err := json.Unmarshal(data, &license); err != nil {
		return fmt.Errorf("failed to parse usage license: %w", err)
	}

	// 验证许可证格式
	if license.LicenseType != "usage" {
		return fmt.Errorf("invalid license type: %s", license.LicenseType)
	}

	// 提取根域名
	rootDomain := m.extractRootDomain(license.TenantDomain)
	m.usageLicenses[rootDomain] = &license

	m.logger.Info("Usage license loaded successfully",
		zap.String("license_id", license.LicenseID),
		zap.String("domain", rootDomain))

	return nil
}

// extractRootDomain 提取根域名
func (m *DefaultLicenseManager) extractRootDomain(domain string) string {
	// 简单实现：如果是子域名，提取根域名
	parts := strings.Split(domain, ".")
	if len(parts) >= 2 {
		return strings.Join(parts[len(parts)-2:], ".")
	}
	return domain
}

// installSystemLicense 安装系统许可证
func (m *DefaultLicenseManager) installSystemLicense(license *SystemLicense) error {
	// 验证许可证
	if err := m.validateSystemLicense(license); err != nil {
		return fmt.Errorf("system license validation failed: %w", err)
	}

	// 保存到内存
	m.systemLicense = license

	// 保存到文件
	if err := m.saveSystemLicenseToFile(license); err != nil {
		m.logger.Warn("Failed to save system license to file", zap.Error(err))
	}

	m.logger.Info("System license installed successfully", zap.String("license_id", license.LicenseID))
	return nil
}

// installUsageLicense 安装使用许可证
func (m *DefaultLicenseManager) installUsageLicense(license *UsageLicense) error {
	// 验证许可证
	if err := m.validateUsageLicense(license); err != nil {
		return fmt.Errorf("usage license validation failed: %w", err)
	}

	// 保存到内存
	rootDomain := m.extractRootDomain(license.TenantDomain)
	m.usageLicenses[rootDomain] = license

	// 保存到文件
	if err := m.saveUsageLicenseToFile(license); err != nil {
		m.logger.Warn("Failed to save usage license to file", zap.Error(err))
	}

	m.logger.Info("Usage license installed successfully",
		zap.String("license_id", license.LicenseID),
		zap.String("domain", rootDomain))
	return nil
}

// validateSystemLicense 验证系统许可证（简化版本）
func (m *DefaultLicenseManager) validateSystemLicense(license *SystemLicense) error {
	// 基本验证
	if license.LicenseID == "" || license.LicenseType != "system" {
		return fmt.Errorf("invalid system license")
	}

	if time.Now().After(license.ExpiresAt) {
		return fmt.Errorf("license expired")
	}

	return nil
}

// validateUsageLicense 验证使用许可证（简化版本）
func (m *DefaultLicenseManager) validateUsageLicense(license *UsageLicense) error {
	// 基本验证
	if license.LicenseID == "" || license.LicenseType != "usage" || license.TenantDomain == "" {
		return fmt.Errorf("invalid usage license")
	}

	if time.Now().After(license.ExpiresAt) {
		return fmt.Errorf("license expired")
	}

	// 验证与系统许可证的关联
	if m.systemLicense != nil && license.SystemLicenseID != m.systemLicense.LicenseID {
		return fmt.Errorf("usage license not associated with current system license")
	}

	return nil
}

// saveSystemLicenseToFile 保存系统许可证到文件
func (m *DefaultLicenseManager) saveSystemLicenseToFile(license *SystemLicense) error {
	if m.systemConfig.SystemLicensePath == "" {
		return fmt.Errorf("system license path not configured")
	}

	data, err := json.MarshalIndent(license, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal system license: %w", err)
	}

	// 确保目录存在
	dir := filepath.Dir(m.systemConfig.SystemLicensePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}

	if err := ioutil.WriteFile(m.systemConfig.SystemLicensePath, data, 0600); err != nil {
		return fmt.Errorf("failed to write system license file: %w", err)
	}

	return nil
}

// saveUsageLicenseToFile 保存使用许可证到文件
func (m *DefaultLicenseManager) saveUsageLicenseToFile(license *UsageLicense) error {
	if m.systemConfig.UsageLicenseDir == "" {
		return fmt.Errorf("usage license directory not configured")
	}

	data, err := json.MarshalIndent(license, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal usage license: %w", err)
	}

	// 确保目录存在
	if err := os.MkdirAll(m.systemConfig.UsageLicenseDir, 0755); err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}

	// 使用域名作为文件名
	rootDomain := m.extractRootDomain(license.TenantDomain)
	filename := fmt.Sprintf("%s.json", rootDomain)
	filePath := filepath.Join(m.systemConfig.UsageLicenseDir, filename)

	if err := ioutil.WriteFile(filePath, data, 0600); err != nil {
		return fmt.Errorf("failed to write usage license file: %w", err)
	}

	return nil
}

// ========== 降级状态管理 ==========

// IsInDegradedMode 检查是否处于降级状态（简化版本）
func (m *DefaultLicenseManager) IsInDegradedMode() bool {
	// 如果商业授权未启用，不算降级
	if !m.isCommercialAuthEnabled() {
		return false
	}

	// 系统许可证无效或过期即为降级状态
	return m.systemLicense == nil || time.Now().After(m.systemLicense.ExpiresAt)
}

// GetDegradationReason 获取降级原因（简化版本）
func (m *DefaultLicenseManager) GetDegradationReason() string {
	if !m.IsInDegradedMode() {
		return ""
	}

	if m.systemLicense == nil {
		return "系统许可证未安装"
	}

	if time.Now().After(m.systemLicense.ExpiresAt) {
		return "系统许可证已过期"
	}

	return "许可证验证失败"
}

// GetDegradationInfo 获取降级状态信息
func (m *DefaultLicenseManager) GetDegradationInfo() *DegradationInfo {
	return &DegradationInfo{
		IsInDegradedMode: m.IsInDegradedMode(),
		Reason:          m.GetDegradationReason(),
		CurrentEdition:  m.GetSystemEdition(),
		AvailableFeatures: m.getPersonalFeatureList(),
		UpgradeRequired: m.IsInDegradedMode(),
	}
}

// getPersonalFeatureList 获取个人版功能列表（简化版本）
func (m *DefaultLicenseManager) getPersonalFeatureList() []string {
	return m.personalProvider.GetFeatures()
}

// DegradationInfo 降级状态信息
type DegradationInfo struct {
	IsInDegradedMode  bool              `json:"is_in_degraded_mode"`
	Reason           string            `json:"reason"`
	CurrentEdition   contract.Edition  `json:"current_edition"`
	AvailableFeatures []string         `json:"available_features"`
	UpgradeRequired  bool              `json:"upgrade_required"`
}
