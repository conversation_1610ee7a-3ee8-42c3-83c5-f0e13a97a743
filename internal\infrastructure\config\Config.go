/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/infrastructure/config/Config.go
 * @Description: Implements the three-tier configuration loading mechanism for GACMS.
 *
 * © 2025 GACMS. All rights reserved.
 */

package config

import (
	"fmt"
	"gacms/pkg/contract"
	"github.com/spf13/viper"
	"os"
	"strings"
)

// Config implements the contract.Config interface using the Viper library.
type Config struct {
	viper *viper.Viper
}

// New creates and loads the configuration based on the three-tier model.
// This constructor is designed to be flexible. In the main application container,
// it can be called without a siteID to load global/env config. In a request-scoped
// context, it could be called with a siteID to get site-specific configuration.
// For simplicity in the initial FX container, we load only the global config.
func New() (contract.Config, error) {
	return LoadConfigForSite("")
}

// LoadConfigForSite provides a way to load configuration for a specific site,
// adhering to the three-tier model.
func LoadConfigForSite(siteID string) (contract.Config, error) {
	v := viper.New()

	// 1. Load Global Configuration
	v.SetConfigName("system")
	v.SetConfigType("yaml")
	v.AddConfigPath("configs/global")
	if err := v.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read global config file: %w", err)
		}
		// It's okay if the global config is not found, we can rely on defaults/env.
	}

	// 2. Merge Site-Specific Configuration if siteID is provided
	if siteID != "" {
		v.SetConfigName("config")
		v.AddConfigPath(fmt.Sprintf("configs/sites/%s", siteID))
		// MergeInConfig doesn't error out if the file is not found, which is desired.
		_ = v.MergeInConfig()
	}

	// 3. Set up environment variable overrides
	v.SetEnvPrefix("GACMS")
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	v.AutomaticEnv()

	// Set some sensible defaults
	v.SetDefault("server.port", "8080")
	v.SetDefault("log.level", "info")

	fmt.Println("Configuration loaded.")
	// For debugging: v.Debug()

	return &Config{viper: v}, nil
}

// Get retrieves a value from the configuration.
func (c *Config) Get(key string) (interface{}, bool) {
	if c.viper.IsSet(key) {
		return c.viper.Get(key), true
	}
	return nil, false
}

// GetString retrieves a string value.
func (c *Config) GetString(key string) string {
	return c.viper.GetString(key)
}

// GetInt retrieves an integer value.
func (c *Config) GetInt(key string) int {
	return c.viper.GetInt(key)
}

// GetBool retrieves a boolean value.
func (c *Config) GetBool(key string) bool {
	return c.viper.GetBool(key)
}

// GetStringMap retrieves a map[string]interface{} value.
func (c *Config) GetStringMap(key string) map[string]interface{} {
	return c.viper.GetStringMap(key)
}

// Helper function for creating dummy config files for testing/development.
func SetupDummyConfigFiles() {
	_ = os.MkdirAll("configs/global", os.ModePerm)
	_ = os.MkdirAll("configs/sites/site1", os.ModePerm)

	globalConfig := `
server:
  port: "8888"
database:
  dsn: "user:password@tcp(127.0.0.1:3306)/gacms_global?charset=utf8mb4&parseTime=True&loc=Local"
log:
  level: "debug"
`
	_ = os.WriteFile("configs/global/system.yaml", []byte(globalConfig), 0644)

	site1Config := `
theme: "cool-blue"
server:
  port: "9999" # This will override the global port for site1
`
	_ = os.WriteFile("configs/sites/site1/config.yaml", []byte(site1Config), 0644)
} 