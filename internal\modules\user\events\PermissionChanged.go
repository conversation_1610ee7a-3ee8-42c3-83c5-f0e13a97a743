/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/modules/user/events/PermissionChanged.go
 * @Description: Permission related event models for the RBAC system.
 *
 * © 2025 GACMS. All rights reserved.
 */
package events

import (
	"context"
	"gacms/internal/core/bus"
	"gacms/internal/modules/user/domain/model"
	"gacms/pkg/contract"
)

// 权限事件类型
const (
	PermissionCreatedEventName contract.EventName = "permission.created"
	PermissionUpdatedEventName contract.EventName = "permission.updated"
	PermissionDeletedEventName contract.EventName = "permission.deleted"
)

// PermissionCreatedEvent 权限创建事件
type PermissionCreatedEvent struct {
	bus.BaseEvent
	Permission *model.Permission `json:"permission"`
	OperatedBy uint              `json:"operated_by"`
	SiteID     uint              `json:"site_id"`
}

// PermissionUpdatedEvent 权限更新事件
type PermissionUpdatedEvent struct {
	bus.BaseEvent
	Permission *model.Permission `json:"permission"`
	OldSlug    string            `json:"old_slug"` // 记录原始slug以便接收者可以处理更改
	OperatedBy uint              `json:"operated_by"`
	SiteID     uint              `json:"site_id"`
}

// PermissionDeletedEvent 权限删除事件
type PermissionDeletedEvent struct {
	bus.BaseEvent
	PermissionID uint   `json:"permission_id"`
	Slug         string `json:"slug"` // 保存删除的权限标识，便于接收者识别
	OperatedBy   uint   `json:"operated_by"`
	SiteID       uint   `json:"site_id"`
}

// 事件创建函数

// NewPermissionCreatedEvent 创建权限创建事件
func NewPermissionCreatedEvent(ctx context.Context, permission *model.Permission, operatedBy uint, siteID uint) *PermissionCreatedEvent {
	payload := map[string]interface{}{
		"permission": permission,
		"operated_by": operatedBy,
		"site_id": siteID,
	}
	baseEvent := bus.NewBaseEvent(ctx, PermissionCreatedEventName, payload).(*bus.BaseEvent)
	return &PermissionCreatedEvent{
		BaseEvent:  *baseEvent,
		Permission: permission,
		OperatedBy: operatedBy,
		SiteID:     siteID,
	}
}

// NewPermissionUpdatedEvent 创建权限更新事件
func NewPermissionUpdatedEvent(ctx context.Context, permission *model.Permission, operatedBy uint, siteID uint, oldSlug string) *PermissionUpdatedEvent {
	payload := map[string]interface{}{
		"permission": permission,
		"old_slug": oldSlug,
		"operated_by": operatedBy,
		"site_id": siteID,
	}
	baseEvent := bus.NewBaseEvent(ctx, PermissionUpdatedEventName, payload).(*bus.BaseEvent)
	return &PermissionUpdatedEvent{
		BaseEvent:  *baseEvent,
		Permission: permission,
		OldSlug:    oldSlug,
		OperatedBy: operatedBy,
		SiteID:     siteID,
	}
}

// NewPermissionDeletedEvent 创建权限删除事件
func NewPermissionDeletedEvent(ctx context.Context, permissionID uint, operatedBy uint, siteID uint, slug string) *PermissionDeletedEvent {
	payload := map[string]interface{}{
		"permission_id": permissionID,
		"slug": slug,
		"operated_by": operatedBy,
		"site_id": siteID,
	}
	baseEvent := bus.NewBaseEvent(ctx, PermissionDeletedEventName, payload).(*bus.BaseEvent)
	return &PermissionDeletedEvent{
		BaseEvent:    *baseEvent,
		PermissionID: permissionID,
		Slug:         slug,
		OperatedBy:   operatedBy,
		SiteID:       siteID,
	}
} 