/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/category/infrastructure/persistence/CategoryGormRepository.go
 * @Description: GORM implementation of the CategoryRepository, fully integrated with multi-tenancy context.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package persistence

import (
	"context"
	"gacms/internal/modules/category/domain/contract"
	"gacms/internal/modules/category/domain/model"
	dbContract "gacms/pkg/contract"
)

type CategoryGormRepository struct {
	dbService dbContract.Database
}

func NewCategoryGormRepository(dbService dbContract.Database) contract.CategoryRepository {
	return &CategoryGormRepository{dbService: dbService}
}

func (r *CategoryGormRepository) Create(ctx context.Context, category *model.Category) error {
	return r.dbService.DB(ctx).Create(category).Error
}

func (r *CategoryGormRepository) Update(ctx context.Context, category *model.Category) error {
	return r.dbService.DB(ctx).Save(category).Error
}

func (r *CategoryGormRepository) Delete(ctx context.Context, id uint) error {
	return r.dbService.DB(ctx).Delete(&model.Category{}, id).Error
}

func (r *CategoryGormRepository) GetByID(ctx context.Context, id uint) (*model.Category, error) {
	var category model.Category
	err := r.dbService.DB(ctx).First(&category, id).Error
	return &category, err
}

func (r *CategoryGormRepository) GetAll(ctx context.Context) ([]*model.Category, error) {
	var categories []*model.Category
	err := r.dbService.DB(ctx).Find(&categories).Error
	return categories, err
} 