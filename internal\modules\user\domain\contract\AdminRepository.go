/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/user/domain/contract/AdminRepository.go
 * @Description: Defines the repository interface for admin users.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package contract

import (
	"context"
	"gacms/internal/modules/user/domain/model"
	"github.com/google/uuid"
)

// AdminRepository defines the persistence methods for Admin entities.
type AdminRepository interface {
	GetAdminByUsername(ctx context.Context, username string) (*model.Admin, error)
	CreateAdmin(ctx context.Context, admin *model.Admin) error
	ListAdmins(ctx context.Context, page, pageSize int) ([]*model.Admin, int64, error)
	GetAdminByID(ctx context.Context, id uuid.UUID) (*model.Admin, error)
	UpdateAdmin(ctx context.Context, admin *model.Admin) error
	DeleteAdmin(ctx context.Context, id uuid.UUID) error
	AssignRolesToAdmin(ctx context.Context, adminID uuid.UUID, roleIDs []uint) error
	// CountAdmins returns the total number of admin users.
	CountAdmins(ctx context.Context) (int64, error)
	GetByIDWithRoles(ctx context.Context, userID uint) (*model.Admin, error)
	FindByIDWithRoles(ctx context.Context, adminID uint) (*model.Admin, error)
	FindAll(ctx context.Context, page int, pageSize int) ([]*model.Admin, int64, error)
}