/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/theme/module.go
 * @Description: Defines the theme module for GACMS, including DI and route registration with permissions.
 *
 * © 2025 GACMS. All rights reserved.
 */

package theme

import (
	"context"
	"gacms/internal/core/contract"
	"gacms/internal/modules/extension/application/service"
	themeContract "gacms/internal/modules/theme/domain/contract"
	"gacms/internal/modules/theme/application/service"
	"gacms/internal/modules/theme/domain/model"
	"gacms/internal/modules/theme/infrastructure/persistence"
	"gacms/internal/modules/theme/port/http/controller"
	"gacms/internal/port/http/middleware"

	"github.com/gin-gonic/gin"
	"go.uber.org/fx"
	"gorm.io/gorm"
)

type ThemeModule struct{}

func (m *ThemeModule) GetName() string {
	return "theme"
}

func (m_ *ThemeModule) GetVersion() string {
	return "1.0.0"
}

func (m *ThemeModule) GetModels() []interface{} {
	return []interface{}{&model.ThemeSetting{}}
}

// ExposePermissions declares the permissions this module uses.
func (m *ThemeModule) ExposePermissions() []coreContract.PermissionInfo {
	return []coreContract.PermissionInfo{
		{Name: contract.PermissionManageThemes, Slug: contract.PermissionManageThemes, Description: "General permission for theme management access"},
		{Name: contract.PermissionCreateTheme,  Slug: contract.PermissionCreateTheme,  Description: "Permission to create a child theme for a site"},
		{Name: contract.PermissionEditTheme,    Slug: contract.PermissionEditTheme,    Description: "Permission to edit a theme's settings or files for a site"},
		{Name: contract.PermissionActivateTheme,Slug: contract.PermissionActivateTheme,Description: "Permission to activate or deactivate a theme for a site"},
	}
}

var Module = fx.Module("theme",
	fx.Provide(
		func(db *gorm.DB) themeContract.ThemeSettingRepository {
			return persistence.NewThemeSettingGormRepository(db)
		},
		service.NewThemeService,
		controller.NewThemeController,
	),
	fx.Invoke(RegisterRoutes),
)

func RegisterRoutes(
	router *gin.Engine,
	authMiddleware *middleware.AuthMiddleware,
	themeController *controller.ThemeController,
) {
	// Create a base group for all admin APIs
	adminAPIBase := router.Group("/api/admin")

	// Group for theme routes that require at least general management permission
	themesGuard := authMiddleware.Handle(contract.PermissionManageThemes)
	themesGroup := adminAPIBase.Group("/themes", themesGuard)
	{
		// Routes that only require the general 'themes:manage' permission
		themeController.RegisterGeneralRoutes(themesGroup)

		// Create subgroups for more specific permissions
		// This approach allows for cleaner, role-based access control.
		
		// Group for routes requiring activation permission
		activateGuard := authMiddleware.Handle(contract.PermissionActivateTheme)
		activateGroup := themesGroup.Group("", activateGuard)
		{
			activateGroup.POST("/activate", themeController.Activate)
			activateGroup.POST("/deactivate", themeController.Deactivate)
		}

		// Group for routes requiring creation permission
		createGuard := authMiddleware.Handle(contract.PermissionCreateTheme)
		createGroup := themesGroup.Group("", createGuard)
		{
			createGroup.POST("/create-child", themeController.CreateChildTheme)
		}

		// Group for routes requiring editing permission
		editGuard := authMiddleware.Handle(contract.PermissionEditTheme)
		editGroup := themesGroup.Group("", editGuard)
		{
			editGroup.GET("/settings", themeController.GetThemeSettings)
			editGroup.POST("/settings", themeController.UpdateThemeSettings)
			editGroup.GET("/files", themeController.GetThemeFile)
			editGroup.POST("/files", themeController.UpdateThemeFile)
		}
	}
} 