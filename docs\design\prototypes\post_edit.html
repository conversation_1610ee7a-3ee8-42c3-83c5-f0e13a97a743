<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 文章编辑</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .editor-container {
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .editor-toolbar {
            background-color: rgba(31, 41, 55, 0.5);
            border-bottom: 1px solid rgba(75, 85, 99, 0.5);
        }
        
        .editor-toolbar button {
            margin: 0;
            padding: 8px 12px;
            background: transparent;
            border: none;
            color: #e0e0e0;
            cursor: pointer;
            border-right: 1px solid rgba(75, 85, 99, 0.3);
        }
        
        .editor-toolbar button:hover {
            background-color: rgba(75, 85, 99, 0.3);
        }
        
        .editor-content {
            min-height: 300px;
            padding: 16px;
            background-color: rgba(31, 41, 55, 0.3);
        }
        
        .active-tab {
            background-color: rgba(59, 130, 246, 0.2);
            border-bottom: 2px solid #3b82f6;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="content.html" class="hover:text-white">内容管理</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="posts.html" class="hover:text-white">文章管理</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">编辑文章</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">编辑文章</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <a href="post_preview.html" target="_blank" class="flex items-center justify-center bg-gradient-to-r from-purple-500 to-purple-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-purple-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-eye text-white"></i>
                                </span>
                                预览
                            </span>
                        </a>
                        <button id="saveBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-save text-white"></i>
                                </span>
                                保存
                            </span>
                        </button>
                        <a href="posts.html" class="flex items-center justify-center bg-gradient-to-r from-gray-600 to-gray-700 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-gray-600/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-times text-white"></i>
                                </span>
                                取消
                            </span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 文章编辑区域 -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                <!-- 左侧：文章内容 -->
                <div class="lg:col-span-2">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">文章标题 <span class="text-red-500">*</span></label>
                            <input type="text" id="articleTitle" value="深入解析云原生架构的优势与挑战" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">文章别名（URL）</label>
                            <div class="flex">
                                <span class="inline-flex items-center px-3 bg-gray-800 border border-r-0 border-gray-600 rounded-l-lg text-gray-400">
                                    /article/
                                </span>
                                <input type="text" id="articleSlug" value="cloud-native-architecture-analysis" class="flex-1 bg-gray-700 border border-gray-600 rounded-r-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <p class="text-gray-400 text-sm mt-1">生成的URL: https://www.example.com/article/cloud-native-architecture-analysis</p>
                        </div>
                        
                        <!-- 内容编辑器标签 -->
                        <div class="mb-4 border-b border-gray-700">
                            <ul class="flex flex-wrap -mb-px">
                                <li class="mr-2">
                                    <button class="tab-btn active-tab inline-block p-4 rounded-t-lg border-b-2 border-blue-500" data-tab="visual">
                                        可视化编辑器
                                    </button>
                                </li>
                                <li class="mr-2">
                                    <button class="tab-btn inline-block p-4 rounded-t-lg" data-tab="code">
                                        HTML代码
                                    </button>
                                </li>
                                <li class="mr-2">
                                    <button class="tab-btn inline-block p-4 rounded-t-lg" data-tab="blocks">
                                        区块编辑
                                    </button>
                                </li>
                            </ul>
                        </div>
                        
                        <!-- 编辑器 -->
                        <div class="editor-container mb-4">
                            <div class="editor-toolbar flex flex-wrap">
                                <button><i class="fas fa-paragraph"></i></button>
                                <button><i class="fas fa-heading"></i></button>
                                <button><i class="fas fa-bold"></i></button>
                                <button><i class="fas fa-italic"></i></button>
                                <button><i class="fas fa-underline"></i></button>
                                <button><i class="fas fa-link"></i></button>
                                <button><i class="fas fa-image"></i></button>
                                <button><i class="fas fa-list-ul"></i></button>
                                <button><i class="fas fa-list-ol"></i></button>
                                <button><i class="fas fa-quote-right"></i></button>
                                <button><i class="fas fa-code"></i></button>
                            </div>
                            <div class="editor-content" contenteditable="true">
                                <h2>云原生架构概述</h2>
                                <p>云原生架构是一种设计和构建应用程序的方法，它利用云计算模型的优势，使应用程序能够在现代、动态环境中扩展和运行。这种架构风格强调使用微服务、容器和DevOps实践，以实现高度可扩展、弹性和敏捷的应用系统。</p>
                                
                                <h3>核心优势</h3>
                                <ul>
                                    <li><strong>可扩展性</strong>：云原生应用可以根据需求自动扩展，有效应对流量波动。</li>
                                    <li><strong>弹性</strong>：通过分布式设计，系统可以在部分组件失败时继续运行。</li>
                                    <li><strong>敏捷性</strong>：支持快速迭代和持续部署，显著缩短上市时间。</li>
                                </ul>
                                
                                <h3>面临的挑战</h3>
                                <p>尽管云原生架构提供了许多优势，但在实施过程中也面临一些挑战：</p>
                                <ol>
                                    <li>复杂的分布式系统管理</li>
                                    <li>微服务之间的安全通信</li>
                                    <li>监控和调试的难度增加</li>
                                </ol>
                                
                                <blockquote>
                                    "云原生不仅是技术选择，更是一种思维方式的转变。"
                                </blockquote>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">文章摘要</label>
                            <textarea class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" rows="3">本文深入分析了云原生架构的优势与挑战，探讨了如何利用微服务、容器和DevOps实践构建高弹性、可扩展的现代应用系统。</textarea>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：发布选项 -->
                <div class="lg:col-span-1">
                    <!-- 发布设置 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold text-white mb-4">发布设置</h3>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">状态</label>
                            <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="publish">已发布</option>
                                <option value="draft">草稿</option>
                                <option value="pending">待审核</option>
                                <option value="scheduled">定时发布</option>
                            </select>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">发布时间</label>
                            <input type="datetime-local" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" value="2025-05-28T10:30">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">作者</label>
                            <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="1">管理员 (admin)</option>
                                <option value="2">张小明 (xiaoming)</option>
                                <option value="3">李华 (lihua)</option>
                            </select>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">访问权限</label>
                            <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="public">公开</option>
                                <option value="private">私有</option>
                                <option value="password">密码保护</option>
                            </select>
                        </div>
                        
                        <div class="flex space-x-3 mt-6">
                            <button class="w-full py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg font-medium hover:shadow-lg hover:shadow-green-500/30 transition-all">
                                <i class="fas fa-paper-plane mr-2"></i>发布
                            </button>
                            <button class="w-full py-3 bg-gray-700 text-white rounded-lg font-medium hover:bg-gray-600 transition-all">
                                <i class="fas fa-save mr-2"></i>存草稿
                            </button>
                        </div>
                    </div>
                    
                    <!-- 分类 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold text-white mb-4">分类</h3>
                        
                        <div class="max-h-48 overflow-y-auto">
                            <div class="mb-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded bg-gray-700 border-gray-600 text-blue-500 mr-2" checked>
                                    <span>技术架构</span>
                                </label>
                            </div>
                            <div class="mb-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded bg-gray-700 border-gray-600 text-blue-500 mr-2" checked>
                                    <span>云计算</span>
                                </label>
                            </div>
                            <div class="mb-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded bg-gray-700 border-gray-600 text-blue-500 mr-2">
                                    <span>微服务</span>
                                </label>
                            </div>
                            <div class="mb-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded bg-gray-700 border-gray-600 text-blue-500 mr-2">
                                    <span>容器化</span>
                                </label>
                            </div>
                            <div class="mb-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded bg-gray-700 border-gray-600 text-blue-500 mr-2">
                                    <span>DevOps</span>
                                </label>
                            </div>
                        </div>
                        
                        <button class="w-full py-2 bg-gray-700 text-gray-300 rounded-lg font-medium mt-3 hover:bg-gray-600 transition-all flex items-center justify-center">
                            <i class="fas fa-plus-circle mr-2"></i>添加新分类
                        </button>
                    </div>
                    
                    <!-- 标签 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold text-white mb-4">标签</h3>
                        
                        <div class="mb-4">
                            <input type="text" placeholder="输入标签，按回车添加" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-blue-500/20 text-blue-400 px-3 py-1 rounded-full text-sm flex items-center">
                                云原生
                                <button class="ml-2 text-xs hover:text-white"><i class="fas fa-times"></i></button>
                            </span>
                            <span class="bg-blue-500/20 text-blue-400 px-3 py-1 rounded-full text-sm flex items-center">
                                Kubernetes
                                <button class="ml-2 text-xs hover:text-white"><i class="fas fa-times"></i></button>
                            </span>
                            <span class="bg-blue-500/20 text-blue-400 px-3 py-1 rounded-full text-sm flex items-center">
                                微服务架构
                                <button class="ml-2 text-xs hover:text-white"><i class="fas fa-times"></i></button>
                            </span>
                            <span class="bg-blue-500/20 text-blue-400 px-3 py-1 rounded-full text-sm flex items-center">
                                Docker
                                <button class="ml-2 text-xs hover:text-white"><i class="fas fa-times"></i></button>
                            </span>
                        </div>
                    </div>
                    
                    <!-- 特色图片 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold text-white mb-4">特色图片</h3>
                        
                        <div class="border-2 border-dashed border-gray-600 rounded-lg p-4 text-center">
                            <img src="./assets/images/cloud-native.jpg" alt="云原生架构" class="w-full h-auto rounded-lg mb-3">
                            <div class="flex space-x-3">
                                <button class="flex-1 bg-gray-700 text-gray-300 px-4 py-2 rounded hover:bg-gray-600 transition-all text-sm">
                                    <i class="fas fa-exchange-alt mr-1"></i>更换
                                </button>
                                <button class="flex-1 bg-gray-700 text-gray-300 px-4 py-2 rounded hover:bg-gray-600 transition-all text-sm">
                                    <i class="fas fa-trash-alt mr-1"></i>删除
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签页切换功能
            const tabButtons = document.querySelectorAll('.tab-btn');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active-tab');
                    });
                    this.classList.add('active-tab');
                });
            });
            
            // 保存按钮点击事件
            document.getElementById('saveBtn').addEventListener('click', function() {
                showNotification('文章已成功保存！', 'success');
            });
        });
        
        // 显示通知
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = 'notification';
            
            // 设置图标
            let icon = 'info-circle';
            let iconColor = 'text-blue-500';
            
            if (type === 'success') {
                icon = 'check-circle';
                iconColor = 'text-green-500';
            } else if (type === 'error') {
                icon = 'exclamation-circle';
                iconColor = 'text-red-500';
            } else if (type === 'warning') {
                icon = 'exclamation-triangle';
                iconColor = 'text-yellow-500';
            }
            
            notification.innerHTML = `
                <div class="mr-3">
                    <i class="fas fa-${icon} ${iconColor} text-2xl"></i>
                </div>
                <div class="flex-1">
                    <p class="text-white">${message}</p>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // 显示通知
            setTimeout(() => notification.classList.add('show'), 10);
            
            // 3秒后隐藏通知
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }
    </script>
</body>
</html> 
