/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/infrastructure/database/Service.go
 * @Description: GORM implementation of the Database contract.
 *
 * © 2025 GACMS. All rights reserved.
 */

package database

import (
	"context"
	"fmt"
	"gacms/pkg/contract"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// Service implements the contract.Database interface.
type Service struct {
	db *gorm.DB
}

// NewService creates a new database service.
func NewService(config contract.Config) (contract.Database, error) {
	dsn := config.GetString("database.dsn")
	if dsn == "" {
		return nil, fmt.Errorf("database dsn is not configured")
	}

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	return &Service{db: db}, nil
}

// GetDB returns the underlying GORM database instance.
func (s *Service) GetDB() *gorm.DB {
	return s.db
}

// DB returns a GORM database instance scoped to the given context.
// If the context contains a siteID, the returned instance will be automatically
// filtered for that site using the SiteScope.
func (s *Service) DB(ctx context.Context) *gorm.DB {
	if siteID, ok := SiteIDFrom(ctx); ok {
		return s.db.Scopes(SiteScope(siteID))
	}
	// Return the unscoped DB instance if no siteID is in the context.
	// This is useful for global operations during startup or in admin contexts.
	return s.db
}

// AutoMigrate runs the auto-migration for the given models.
func (s *Service) AutoMigrate(models ...interface{}) error {
	return s.db.AutoMigrate(models...)
} 