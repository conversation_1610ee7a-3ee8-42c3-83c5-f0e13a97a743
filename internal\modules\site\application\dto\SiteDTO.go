/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/site/application/dto/SiteDTO.go
 * @Description: Defines DTOs for site operations.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package dto

// SiteDTO is used for displaying site information.
type SiteDTO struct {
	ID             uint   `json:"id"`
	Name           string `json:"name"`
	Domain         string `json:"domain"`
	IsActive       bool   `json:"is_active"`
	FrontendTheme  string `json:"frontend_theme"`
	BackendTheme   string `json:"backend_theme"`
	DefaultLang    string `json:"default_lang"`
	AvailableLangs string `json:"available_langs"`
	CreatedAt      string `json:"created_at"`
	UpdatedAt      string `json:"updated_at"`
}

// SiteCreateDTO is used for creating a new site.
type SiteCreateDTO struct {
	Name           string `json:"name" binding:"required"`
	Domain         string `json:"domain" binding:"required"`
	IsActive       bool   `json:"is_active"`
	FrontendTheme  string `json:"frontend_theme"`
	BackendTheme   string `json:"backend_theme"`
	DefaultLang    string `json:"default_lang"`
	AvailableLangs string `json:"available_langs"`
}

// SiteUpdateDTO is used for updating an existing site.
type SiteUpdateDTO struct {
	Name           string `json:"name"`
	Domain         string `json:"domain"`
	IsActive       *bool  `json:"is_active"`
	FrontendTheme  string `json:"frontend_theme"`
	BackendTheme   string `json:"backend_theme"`
	DefaultLang    string `json:"default_lang"`
	AvailableLangs string `json:"available_langs"`
} 