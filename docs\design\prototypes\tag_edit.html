<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 标签编辑</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .color-ball {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }
        
        .color-ball:hover {
            transform: scale(1.1);
        }
        
        .color-ball.selected::after {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border: 2px solid #fff;
            border-radius: 50%;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="content.html" class="hover:text-white">内容管理</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="tags.html" class="hover:text-white">标签管理</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">编辑标签</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">编辑标签</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="saveBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-save text-white"></i>
                                </span>
                                保存
                            </span>
                        </button>
                        <a href="tags.html" class="flex items-center justify-center bg-gradient-to-r from-gray-600 to-gray-700 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-gray-600/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-times text-white"></i>
                                </span>
                                取消
                            </span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 标签编辑表单 -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                <!-- 左侧：基本信息 -->
                <div class="lg:col-span-2">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">基本信息</h3>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">标签名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="tagName" value="Web开发" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">标签别名（用于URL）</label>
                            <div class="flex">
                                <span class="inline-flex items-center px-3 bg-gray-800 border border-r-0 border-gray-600 rounded-l-lg text-gray-400">
                                    /tag/
                                </span>
                                <input type="text" id="tagSlug" value="web-development" class="flex-1 bg-gray-700 border border-gray-600 rounded-r-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <p class="text-gray-400 text-sm mt-1">仅限使用小写字母、数字和连字符</p>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">标签描述</label>
                            <textarea id="tagDescription" rows="4" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">Web开发相关的技术话题，包括前端开发和后端开发等内容。</textarea>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">标签颜色</label>
                            <div class="flex flex-wrap gap-3 mt-2">
                                <div class="color-ball selected" style="background-color: #3B82F6;"></div>
                                <div class="color-ball" style="background-color: #10B981;"></div>
                                <div class="color-ball" style="background-color: #F59E0B;"></div>
                                <div class="color-ball" style="background-color: #EF4444;"></div>
                                <div class="color-ball" style="background-color: #8B5CF6;"></div>
                                <div class="color-ball" style="background-color: #EC4899;"></div>
                                <div class="color-ball" style="background-color: #6366F1;"></div>
                                <div class="color-ball" style="background-color: #14B8A6;"></div>
                                <div class="color-ball" style="background-color: #F97316;"></div>
                                <div class="color-ball" style="background-color: #A855F7;"></div>
                            </div>
                            <div class="flex mt-3">
                                <span class="inline-flex items-center px-3 bg-gray-800 border border-r-0 border-gray-600 rounded-l-lg text-gray-400">
                                    HEX
                                </span>
                                <input type="text" id="colorHex" value="#3B82F6" class="w-32 bg-gray-700 border border-gray-600 rounded-r-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">标签图标</label>
                            <div class="flex">
                                <span class="inline-flex items-center px-3 bg-gray-800 border border-r-0 border-gray-600 rounded-l-lg text-gray-400">
                                    <i class="fas fa-code"></i>
                                </span>
                                <input type="text" id="iconClass" value="fas fa-code" class="flex-1 bg-gray-700 border border-gray-600 rounded-r-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <p class="text-gray-400 text-sm mt-1">输入FontAwesome图标类（例如：fas fa-code）</p>
                        </div>
                    </div>
                    
                    <!-- SEO设置 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">SEO设置</h3>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">SEO标题</label>
                            <input type="text" id="seoTitle" value="Web开发技术与资源 | GACMS官方" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <p class="text-gray-400 text-sm mt-1">留空则使用标签名称，建议不超过60个字符</p>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">SEO描述</label>
                            <textarea id="seoDescription" rows="3" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">探索最新的Web开发技术、工具和最佳实践。包括前端开发、后端开发、全栈应用等内容，助您提升开发技能。</textarea>
                            <p class="text-gray-400 text-sm mt-1">留空则使用标签描述，建议不超过160个字符</p>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">SEO关键词</label>
                            <input type="text" id="seoKeywords" value="Web开发, 前端开发, 后端开发, 编程技术, HTML, CSS, JavaScript" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <p class="text-gray-400 text-sm mt-1">多个关键词用逗号分隔</p>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：附加设置 -->
                <div class="lg:col-span-1">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">附加设置</h3>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">排序</label>
                            <input type="number" id="tagOrder" value="10" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <p class="text-gray-400 text-sm mt-1">数字越小排序越靠前</p>
                        </div>
                        
                        <div class="mb-4">
                            <label class="flex items-center">
                                <input type="checkbox" id="isRecommend" checked class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-900 w-5 h-5">
                                <span class="ml-2 text-gray-300">标记为推荐</span>
                            </label>
                            <p class="text-gray-400 text-sm mt-1">推荐标签会在前台显示更突出</p>
                        </div>
                        
                        <div class="mb-4">
                            <label class="flex items-center">
                                <input type="checkbox" id="showFrontend" checked class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-900 w-5 h-5">
                                <span class="ml-2 text-gray-300">在前台显示</span>
                            </label>
                            <p class="text-gray-400 text-sm mt-1">取消勾选则前台不显示此标签</p>
                        </div>
                    </div>
                    
                    <!-- 标签统计 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">标签统计</h3>
                        
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="text-gray-400">文章数量：</span>
                                <span class="text-white font-medium">32</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">访问次数：</span>
                                <span class="text-white font-medium">5,628</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">创建时间：</span>
                                <span class="text-white font-medium">2025-01-15</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">最后更新：</span>
                                <span class="text-white font-medium">2025-04-25</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 危险操作区 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <h3 class="text-lg font-semibold mb-4 text-white text-red-400">危险操作</h3>
                        
                        <button class="flex items-center justify-center bg-red-500/20 hover:bg-red-500/30 text-red-400 w-full py-3 rounded-lg transition-colors">
                            <i class="fas fa-trash-alt mr-2"></i> 删除此标签
                        </button>
                        <p class="text-gray-400 text-sm mt-2">删除后将无法恢复，关联的文章将失去此标签</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 颜色选择器功能
            const colorBalls = document.querySelectorAll('.color-ball');
            const colorHexInput = document.getElementById('colorHex');
            
            colorBalls.forEach(ball => {
                ball.addEventListener('click', function() {
                    // 移除所有选中状态
                    colorBalls.forEach(b => b.classList.remove('selected'));
                    
                    // 为当前颜色添加选中状态
                    ball.classList.add('selected');
                    
                    // 更新颜色输入框
                    const bgColor = getComputedStyle(ball).backgroundColor;
                    const hexColor = rgbToHex(bgColor);
                    colorHexInput.value = hexColor;
                });
            });
            
            // RGB转HEX颜色
            function rgbToHex(rgb) {
                // 从rgb(r, g, b)字符串中提取r, g, b值
                const rgbArray = rgb.match(/\d+/g);
                if (rgbArray) {
                    return '#' + rgbArray.map(x => {
                        const hex = parseInt(x).toString(16);
                        return hex.length === 1 ? '0' + hex : hex;
                    }).join('');
                }
                return '#3B82F6'; // 默认颜色
            }
            
            // 手动输入颜色时更新选中状态
            colorHexInput.addEventListener('blur', function() {
                const inputColor = colorHexInput.value;
                
                // 取消所有选中状态
                colorBalls.forEach(ball => {
                    ball.classList.remove('selected');
                    
                    // 如果有球的颜色与输入匹配，则选中
                    const bgColor = getComputedStyle(ball).backgroundColor;
                    const hexColor = rgbToHex(bgColor);
                    
                    if (hexColor.toLowerCase() === inputColor.toLowerCase()) {
                        ball.classList.add('selected');
                    }
                });
            });
            
            // 表单保存功能
            document.getElementById('saveBtn').addEventListener('click', function() {
                alert('标签保存成功！');
                // 在实际场景中，这里会有AJAX请求保存表单数据
            });
        });
    </script>
</body>
</html> 