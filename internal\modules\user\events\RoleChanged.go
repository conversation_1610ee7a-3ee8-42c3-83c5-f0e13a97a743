/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/modules/user/events/RoleChanged.go
 * @Description: Role related event models for the RBAC system.
 *
 * © 2025 GACMS. All rights reserved.
 */
package events

import (
	"context"
	"gacms/internal/core/bus"
	"gacms/internal/modules/user/domain/model"
	"gacms/pkg/contract"
)

// 角色事件类型
const (
	RoleCreatedEventName          contract.EventName = "role.created"
	RoleUpdatedEventName          contract.EventName = "role.updated"
	RoleDeletedEventName          contract.EventName = "role.deleted"
	RolePermissionsChangedEventName contract.EventName = "role.permissions.changed"
)

// RoleCreatedEvent 角色创建事件
type RoleCreatedEvent struct {
	bus.BaseEvent
	Role       *model.Role `json:"role"`
	OperatedBy uint        `json:"operated_by"`
	SiteID     uint        `json:"site_id"`
}

// RoleUpdatedEvent 角色更新事件
type RoleUpdatedEvent struct {
	bus.BaseEvent
	Role       *model.Role `json:"role"`
	OldName    string      `json:"old_name,omitempty"`
	OldLevel   uint        `json:"old_level,omitempty"`
	OperatedBy uint        `json:"operated_by"`
	SiteID     uint        `json:"site_id"`
}

// RoleDeletedEvent 角色删除事件
type RoleDeletedEvent struct {
	bus.BaseEvent
	RoleID     uint   `json:"role_id"`
	RoleName   string `json:"role_name"`
	OperatedBy uint   `json:"operated_by"`
	SiteID     uint   `json:"site_id"`
}

// RolePermissionsChangedEvent 角色权限变更事件
type RolePermissionsChangedEvent struct {
	bus.BaseEvent
	RoleID             uint     `json:"role_id"`
	RoleName           string   `json:"role_name"`
	AddedPermissions   []string `json:"added_permissions,omitempty"`
	RemovedPermissions []string `json:"removed_permissions,omitempty"`
	OperatedBy         uint     `json:"operated_by"`
	SiteID             uint     `json:"site_id"`
}

// 事件创建函数

// NewRoleCreatedEvent 创建角色创建事件
func NewRoleCreatedEvent(ctx context.Context, role *model.Role, operatedBy uint, siteID uint) *RoleCreatedEvent {
	payload := map[string]interface{}{
		"role": role,
		"operated_by": operatedBy,
		"site_id": siteID,
	}
	baseEvent := bus.NewBaseEvent(ctx, RoleCreatedEventName, payload).(*bus.BaseEvent)
	return &RoleCreatedEvent{
		BaseEvent:  *baseEvent,
		Role:       role,
		OperatedBy: operatedBy,
		SiteID:     siteID,
	}
}

// NewRoleUpdatedEvent 创建角色更新事件
func NewRoleUpdatedEvent(ctx context.Context, role *model.Role, operatedBy uint, siteID uint, oldName string, oldLevel uint) *RoleUpdatedEvent {
	payload := map[string]interface{}{
		"role": role,
		"old_name": oldName,
		"old_level": oldLevel,
		"operated_by": operatedBy,
		"site_id": siteID,
	}
	baseEvent := bus.NewBaseEvent(ctx, RoleUpdatedEventName, payload).(*bus.BaseEvent)
	return &RoleUpdatedEvent{
		BaseEvent:  *baseEvent,
		Role:       role,
		OldName:    oldName,
		OldLevel:   oldLevel,
		OperatedBy: operatedBy,
		SiteID:     siteID,
	}
}

// NewRoleDeletedEvent 创建角色删除事件
func NewRoleDeletedEvent(ctx context.Context, roleID uint, roleName string, operatedBy uint, siteID uint) *RoleDeletedEvent {
	payload := map[string]interface{}{
		"role_id": roleID,
		"role_name": roleName,
		"operated_by": operatedBy,
		"site_id": siteID,
	}
	baseEvent := bus.NewBaseEvent(ctx, RoleDeletedEventName, payload).(*bus.BaseEvent)
	return &RoleDeletedEvent{
		BaseEvent:  *baseEvent,
		RoleID:     roleID,
		RoleName:   roleName,
		OperatedBy: operatedBy,
		SiteID:     siteID,
	}
}

// NewRolePermissionsChangedEvent 创建角色权限变更事件
func NewRolePermissionsChangedEvent(ctx context.Context, roleID uint, roleName string, operatedBy uint, siteID uint, added []string, removed []string) *RolePermissionsChangedEvent {
	payload := map[string]interface{}{
		"role_id": roleID,
		"role_name": roleName,
		"added_permissions": added,
		"removed_permissions": removed,
		"operated_by": operatedBy,
		"site_id": siteID,
	}
	baseEvent := bus.NewBaseEvent(ctx, RolePermissionsChangedEventName, payload).(*bus.BaseEvent)
	return &RolePermissionsChangedEvent{
		BaseEvent:          *baseEvent,
		RoleID:             roleID,
		RoleName:           roleName,
		AddedPermissions:   added,
		RemovedPermissions: removed,
		OperatedBy:         operatedBy,
		SiteID:             siteID,
	}
} 