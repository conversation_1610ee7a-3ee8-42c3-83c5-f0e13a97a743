/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/ModuleDiscovery.go
 * @Description: 模块自发现服务
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"fmt"
	"io/fs"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"go.uber.org/fx"
	"go.uber.org/zap"
)

// ModuleChangeType 模块变化类型
type ModuleChangeType int

const (
	ModuleDiscovered ModuleChangeType = iota // 发现新模块
	ModuleEnabled                            // 模块启用
	ModuleDisabled                           // 模块禁用
	ModuleActivated                          // 模块激活
	ModuleDeactivated                        // 模块停用
	ModuleError                              // 模块错误
	ModuleUpdated                            // 模块更新
)

// ModuleChangeEvent 模块变化事件
type ModuleChangeEvent struct {
	Type       ModuleChangeType
	ModuleName string
	Recipe     *ModuleRecipe
	Timestamp  time.Time
}

// ModuleChangeCallback 模块变化回调函数
type ModuleChangeCallback func(event ModuleChangeEvent) error

// ModuleWatcher 模块监控器接口
type ModuleWatcher interface {
	Start() error
	Stop() error
	AddCallback(callback ModuleChangeCallback)
}

// ModuleStatus 模块状态
type ModuleStatus int

const (
	ModuleStatusDisabled ModuleStatus = iota // 禁用
	ModuleStatusEnabled                      // 启用但未激活
	ModuleStatusActive                       // 激活运行中
	ModuleStatusError                        // 错误状态
)

// ModuleMetadata 模块元数据（已废弃，使用ModuleRecipe替代）
// 保留此类型以兼容现有代码，将逐步迁移到ModuleRecipe
type ModuleMetadata = ModuleRecipe

// ModuleDiscovery 模块发现服务（多租户支持）
type ModuleDiscovery struct {
	logger      *zap.Logger
	modulesPath string
	// 全局模块：所有租户共享
	globalModules map[string]*ModuleRecipe
	// 租户模块：按站点ID分组
	tenantModules map[uint]map[string]*ModuleRecipe
	mu            sync.RWMutex
	watchers      []ModuleWatcher
	callbacks     []ModuleChangeCallback
}

// ModuleDiscoveryParams 模块发现服务参数
type ModuleDiscoveryParams struct {
	fx.In

	Logger *zap.Logger
}

// NewModuleDiscovery 创建模块发现服务（多租户支持）
func NewModuleDiscovery(params ModuleDiscoveryParams) *ModuleDiscovery {
	return &ModuleDiscovery{
		logger:        params.Logger,
		modulesPath:   "internal/modules",
		globalModules: make(map[string]*ModuleRecipe),
		tenantModules: make(map[uint]map[string]*ModuleRecipe),
		watchers:      make([]ModuleWatcher, 0),
		callbacks:     make([]ModuleChangeCallback, 0),
	}
}

// DiscoverModules 发现所有模块
func (d *ModuleDiscovery) DiscoverModules() ([]*ModuleRecipe, error) {
	d.logger.Info("Starting module discovery", zap.String("path", d.modulesPath))

	err := filepath.WalkDir(d.modulesPath, func(path string, entry fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		// 只处理目录
		if !entry.IsDir() {
			return nil
		}

		// 跳过根目录
		if path == d.modulesPath {
			return nil
		}

		// 检查是否是模块目录（包含module.go文件）
		modulePath := filepath.Join(path, "module.go")
		if !d.fileExists(modulePath) {
			return nil
		}

		// 解析模块名称
		moduleName := d.extractModuleName(path)
		if moduleName == "" {
			return nil
		}

		// 创建模块Recipe（默认为全局模块）
		recipe := NewExtensionModuleRecipe(moduleName, "1.0.0", fx.Options())
		recipe.Path = path
		recipe.Status = ModuleStatusEnabled // 默认启用但未激活
		recipe.SetTenantScope(0, true)      // 默认为全局模块

		// 注册为全局模块
		d.globalModules[moduleName] = recipe
		d.logger.Debug("Discovered global module",
			zap.String("name", moduleName),
			zap.String("path", path),
		)

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to discover modules: %w", err)
	}

	// 转换为切片（只返回全局模块）
	modules := make([]*ModuleRecipe, 0, len(d.globalModules))
	for _, module := range d.globalModules {
		modules = append(modules, module)
	}

	d.logger.Info("Module discovery completed",
		zap.Int("global_modules", len(d.globalModules)),
		zap.Int("total_tenants", len(d.tenantModules)),
	)
	return modules, nil
}

// GetModule 获取指定模块（多租户支持）
func (d *ModuleDiscovery) GetModule(name string) (*ModuleRecipe, bool) {
	d.mu.RLock()
	defer d.mu.RUnlock()

	// 先查找全局模块
	if module, exists := d.globalModules[name]; exists {
		return module, true
	}

	// 再查找当前租户的模块（需要从上下文获取）
	// 这里暂时返回全局模块，具体实现需要上下文
	return nil, false
}

// GetModuleForSite 获取指定站点的模块
func (d *ModuleDiscovery) GetModuleForSite(siteID uint, name string) (*ModuleRecipe, bool) {
	d.mu.RLock()
	defer d.mu.RUnlock()

	// 先查找站点特定模块
	if siteModules, exists := d.tenantModules[siteID]; exists {
		if module, exists := siteModules[name]; exists {
			return module, true
		}
	}

	// 再查找全局模块
	if module, exists := d.globalModules[name]; exists {
		return module, true
	}

	return nil, false
}

// GetAllModules 获取所有模块（已废弃，使用GetAllModulesForSite）
func (d *ModuleDiscovery) GetAllModules() map[string]*ModuleRecipe {
	d.mu.RLock()
	defer d.mu.RUnlock()

	// 只返回全局模块
	result := make(map[string]*ModuleRecipe)
	for name, module := range d.globalModules {
		result[name] = module
	}
	return result
}

// GetAllModulesForSite 获取指定站点的所有模块
func (d *ModuleDiscovery) GetAllModulesForSite(siteID uint) map[string]*ModuleRecipe {
	d.mu.RLock()
	defer d.mu.RUnlock()

	result := make(map[string]*ModuleRecipe)

	// 添加全局模块
	for name, module := range d.globalModules {
		result[name] = module
	}

	// 添加站点特定模块
	if siteModules, exists := d.tenantModules[siteID]; exists {
		for name, module := range siteModules {
			result[name] = module
		}
	}

	return result
}

// RegisterGlobalModule 注册全局模块
func (d *ModuleDiscovery) RegisterGlobalModule(recipe *ModuleRecipe) {
	d.mu.Lock()
	defer d.mu.Unlock()

	recipe.SetTenantScope(0, true)
	d.globalModules[recipe.Name] = recipe

	d.logger.Info("Global module registered",
		zap.String("name", recipe.Name),
		zap.String("version", recipe.Version),
	)
}

// RegisterTenantModule 注册租户模块
func (d *ModuleDiscovery) RegisterTenantModule(siteID uint, recipe *ModuleRecipe) {
	d.mu.Lock()
	defer d.mu.Unlock()

	recipe.SetTenantScope(siteID, false)

	if d.tenantModules[siteID] == nil {
		d.tenantModules[siteID] = make(map[string]*ModuleRecipe)
	}

	d.tenantModules[siteID][recipe.Name] = recipe

	d.logger.Info("Tenant module registered",
		zap.Uint("site_id", siteID),
		zap.String("name", recipe.Name),
		zap.String("version", recipe.Version),
	)
}

// SortModulesByDependencies 按依赖关系排序模块（多租户支持）
func (d *ModuleDiscovery) SortModulesByDependencies(modules []*ModuleRecipe) ([]*ModuleRecipe, error) {
	// 使用拓扑排序算法
	visited := make(map[string]bool)
	visiting := make(map[string]bool)
	sorted := make([]*ModuleRecipe, 0, len(modules))
	moduleMap := make(map[string]*ModuleRecipe)

	// 构建模块映射
	for _, module := range modules {
		moduleMap[module.Name] = module
	}

	// 深度优先搜索进行拓扑排序
	var visit func(string) error
	visit = func(name string) error {
		if visiting[name] {
			return fmt.Errorf("circular dependency detected involving module: %s", name)
		}
		if visited[name] {
			return nil
		}

		visiting[name] = true
		module, exists := moduleMap[name]
		if !exists {
			return fmt.Errorf("module not found: %s", name)
		}

		// 先访问所有依赖
		for _, dep := range module.Dependencies {
			if err := visit(dep); err != nil {
				return err
			}
		}

		visiting[name] = false
		visited[name] = true
		sorted = append(sorted, module)
		return nil
	}

	// 访问所有模块
	for _, module := range modules {
		if !visited[module.Name] {
			if err := visit(module.Name); err != nil {
				return nil, err
			}
		}
	}

	d.logger.Info("Modules sorted by dependencies", zap.Int("count", len(sorted)))
	return sorted, nil
}

// LoadModule 加载指定模块（多租户支持）
func (d *ModuleDiscovery) LoadModule(name string) (fx.Option, error) {
	return d.LoadModuleForSite(0, name) // 默认加载全局模块
}

// LoadModuleForSite 为指定站点加载模块
func (d *ModuleDiscovery) LoadModuleForSite(siteID uint, name string) (fx.Option, error) {
	d.mu.RLock()
	module, exists := d.findModuleForSite(siteID, name)
	d.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("module not found: %s for site %d", name, siteID)
	}

	if !module.CanRun() {
		return nil, fmt.Errorf("module cannot run: %s (status: %s)", name, module.StatusString())
	}

	// TODO: 动态加载模块的fx.Option
	// 这里需要使用反射或插件机制来动态加载模块
	d.logger.Info("Loading module",
		zap.Uint("site_id", siteID),
		zap.String("name", name),
		zap.String("scope", module.GetTenantScope()),
	)

	return module.Options, nil // 返回模块的fx.Option
}

// EnableModule 启用模块（多租户支持）
func (d *ModuleDiscovery) EnableModule(name string) error {
	return d.EnableModuleForSite(0, name) // 默认全局操作
}

// EnableModuleForSite 为指定站点启用模块
func (d *ModuleDiscovery) EnableModuleForSite(siteID uint, name string) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	module, exists := d.findModuleForSite(siteID, name)
	if !exists {
		return fmt.Errorf("module not found: %s for site %d", name, siteID)
	}

	if module.Status == ModuleStatusEnabled || module.Status == ModuleStatusActive {
		return nil // 已经启用
	}

	oldStatus := module.Status
	module.Status = ModuleStatusEnabled
	module.ErrorMsg = ""

	d.logger.Info("Module enabled",
		zap.Uint("site_id", siteID),
		zap.String("name", name),
		zap.String("old_status", d.statusString(oldStatus)),
		zap.String("new_status", d.statusString(module.Status)),
	)

	// 触发模块启用事件
	d.notifyChange(ModuleEnabled, name, module)
	return nil
}

// DisableModule 禁用模块（多租户支持）
func (d *ModuleDiscovery) DisableModule(name string) error {
	return d.DisableModuleForSite(0, name) // 默认全局操作
}

// DisableModuleForSite 为指定站点禁用模块
func (d *ModuleDiscovery) DisableModuleForSite(siteID uint, name string) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	module, exists := d.findModuleForSite(siteID, name)
	if !exists {
		return fmt.Errorf("module not found: %s for site %d", name, siteID)
	}

	if module.Status == ModuleStatusDisabled {
		return nil // 已经禁用
	}

	// 如果模块正在运行，先停用
	if module.Status == ModuleStatusActive {
		if err := d.deactivateModuleInternal(name, module); err != nil {
			return fmt.Errorf("failed to deactivate module before disabling: %w", err)
		}
	}

	oldStatus := module.Status
	module.Status = ModuleStatusDisabled
	module.LoadedAt = nil

	d.logger.Info("Module disabled",
		zap.Uint("site_id", siteID),
		zap.String("name", name),
		zap.String("old_status", d.statusString(oldStatus)),
		zap.String("new_status", d.statusString(module.Status)),
	)

	// 触发模块禁用事件
	d.notifyChange(ModuleDisabled, name, module)
	return nil
}

// ActivateModule 激活模块
func (d *ModuleDiscovery) ActivateModule(name string) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	module, exists := d.modules[name]
	if !exists {
		return fmt.Errorf("module not found: %s", name)
	}

	if module.Status == ModuleStatusActive {
		return nil // 已经激活
	}

	if module.Status != ModuleStatusEnabled {
		return fmt.Errorf("module must be enabled before activation, current status: %s", d.statusString(module.Status))
	}

	// 检查依赖是否满足
	if err := d.checkDependencies(module); err != nil {
		module.Status = ModuleStatusError
		module.ErrorMsg = err.Error()
		d.notifyChange(ModuleError, name, module)
		return fmt.Errorf("dependency check failed: %w", err)
	}

	// 激活模块
	if err := d.activateModuleInternal(name, module); err != nil {
		module.Status = ModuleStatusError
		module.ErrorMsg = err.Error()
		d.notifyChange(ModuleError, name, module)
		return fmt.Errorf("failed to activate module: %w", err)
	}

	now := time.Now()
	module.Status = ModuleStatusActive
	module.LoadedAt = &now
	module.ErrorMsg = ""

	d.logger.Info("Module activated",
		zap.String("name", name),
		zap.Time("loaded_at", now),
	)

	// 触发模块激活事件
	d.notifyChange(ModuleActivated, name, module)
	return nil
}

// DeactivateModule 停用模块
func (d *ModuleDiscovery) DeactivateModule(name string) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	module, exists := d.modules[name]
	if !exists {
		return fmt.Errorf("module not found: %s", name)
	}

	if module.Status != ModuleStatusActive {
		return nil // 不是激活状态
	}

	if err := d.deactivateModuleInternal(name, module); err != nil {
		return fmt.Errorf("failed to deactivate module: %w", err)
	}

	module.Status = ModuleStatusEnabled
	module.LoadedAt = nil

	d.logger.Info("Module deactivated", zap.String("name", name))

	// 触发模块停用事件
	d.notifyChange(ModuleDeactivated, name, module)
	return nil
}

// fileExists 检查文件是否存在
func (d *ModuleDiscovery) fileExists(path string) bool {
	// TODO: 实现文件存在检查
	// 由于无法直接访问文件系统，这里返回true作为示例
	return true
}

// extractModuleName 从路径提取模块名称
func (d *ModuleDiscovery) extractModuleName(path string) string {
	// 从路径中提取最后一个目录名作为模块名
	parts := strings.Split(filepath.ToSlash(path), "/")
	if len(parts) == 0 {
		return ""
	}
	return parts[len(parts)-1]
}

// GetEnabledModules 获取所有启用的模块（已废弃，使用GetEnabledModulesForSite）
func (d *ModuleDiscovery) GetEnabledModules() []*ModuleRecipe {
	return d.GetEnabledModulesForSite(0) // 只返回全局模块
}

// GetEnabledModulesForSite 获取指定站点的所有启用模块
func (d *ModuleDiscovery) GetEnabledModulesForSite(siteID uint) []*ModuleRecipe {
	d.mu.RLock()
	defer d.mu.RUnlock()

	enabled := make([]*ModuleRecipe, 0)

	// 添加启用的全局模块
	for _, module := range d.globalModules {
		if module.IsEnabled() {
			enabled = append(enabled, module)
		}
	}

	// 添加启用的站点特定模块
	if siteModules, exists := d.tenantModules[siteID]; exists {
		for _, module := range siteModules {
			if module.IsEnabled() {
				enabled = append(enabled, module)
			}
		}
	}

	return enabled
}

// GetActiveModules 获取所有激活的模块（已废弃，使用GetActiveModulesForSite）
func (d *ModuleDiscovery) GetActiveModules() []*ModuleRecipe {
	return d.GetActiveModulesForSite(0) // 只返回全局模块
}

// GetActiveModulesForSite 获取指定站点的所有激活模块
func (d *ModuleDiscovery) GetActiveModulesForSite(siteID uint) []*ModuleRecipe {
	d.mu.RLock()
	defer d.mu.RUnlock()

	active := make([]*ModuleRecipe, 0)

	// 添加激活的全局模块
	for _, module := range d.globalModules {
		if module.IsActive() {
			active = append(active, module)
		}
	}

	// 添加激活的站点特定模块
	if siteModules, exists := d.tenantModules[siteID]; exists {
		for _, module := range siteModules {
			if module.IsActive() {
				active = append(active, module)
			}
		}
	}

	return active
}

// GetRunnableModules 获取所有可运行的模块（已废弃，使用GetRunnableModulesForSite）
func (d *ModuleDiscovery) GetRunnableModules() []*ModuleRecipe {
	return d.GetRunnableModulesForSite(0) // 只返回全局模块
}

// GetRunnableModulesForSite 获取指定站点的所有可运行模块
func (d *ModuleDiscovery) GetRunnableModulesForSite(siteID uint) []*ModuleRecipe {
	d.mu.RLock()
	defer d.mu.RUnlock()

	runnable := make([]*ModuleRecipe, 0)

	// 添加可运行的全局模块
	for _, module := range d.globalModules {
		if module.CanRun() {
			runnable = append(runnable, module)
		}
	}

	// 添加可运行的站点特定模块
	if siteModules, exists := d.tenantModules[siteID]; exists {
		for _, module := range siteModules {
			if module.CanRun() {
				runnable = append(runnable, module)
			}
		}
	}

	return runnable
}

// GetModulesByStatus 按状态获取模块
func (d *ModuleDiscovery) GetModulesByStatus(status ModuleStatus) []*ModuleRecipe {
	d.mu.RLock()
	defer d.mu.RUnlock()

	modules := make([]*ModuleRecipe, 0)
	for _, module := range d.modules {
		if module.Status == status {
			modules = append(modules, module)
		}
	}
	return modules
}

// statusString 状态转字符串
func (d *ModuleDiscovery) statusString(status ModuleStatus) string {
	switch status {
	case ModuleStatusDisabled:
		return "disabled"
	case ModuleStatusEnabled:
		return "enabled"
	case ModuleStatusActive:
		return "active"
	case ModuleStatusError:
		return "error"
	default:
		return "unknown"
	}
}

// checkDependencies 检查模块依赖
func (d *ModuleDiscovery) checkDependencies(module *ModuleRecipe) error {
	for _, dep := range module.Dependencies {
		depModule, exists := d.modules[dep]
		if !exists {
			return fmt.Errorf("dependency not found: %s", dep)
		}
		// 依赖模块必须可运行（启用且已激活许可证）
		if !depModule.CanRun() {
			return fmt.Errorf("dependency %s cannot run (status: %s, activated: %v)",
				dep, depModule.StatusString(), depModule.IsLicenseActivated())
		}
	}
	return nil
}

// activateModuleInternal 内部激活模块
func (d *ModuleDiscovery) activateModuleInternal(name string, module *ModuleRecipe) error {
	// 检查是否需要许可证激活
	if module.RequiresActivation() && !module.IsLicenseActivated() {
		return fmt.Errorf("module %s requires license activation", name)
	}

	// TODO: 实际的模块激活逻辑
	// 这里应该加载模块的fx.Option，注册服务等
	d.logger.Debug("Activating module internally",
		zap.String("name", name),
		zap.Bool("requires_license", module.RequiresActivation()),
		zap.Bool("is_activated", module.IsLicenseActivated()),
	)
	return nil
}

// deactivateModuleInternal 内部停用模块
func (d *ModuleDiscovery) deactivateModuleInternal(name string, module *ModuleRecipe) error {
	// TODO: 实际的模块停用逻辑
	// 这里应该清理模块资源，取消注册服务等
	d.logger.Debug("Deactivating module internally", zap.String("name", name))
	return nil
}

// notifyChange 通知模块变化
func (d *ModuleDiscovery) notifyChange(changeType ModuleChangeType, moduleName string, recipe *ModuleRecipe) {
	event := ModuleChangeEvent{
		Type:       changeType,
		ModuleName: moduleName,
		Recipe:     recipe,
		Timestamp:  time.Now(),
	}

	for _, callback := range d.callbacks {
		if err := callback(event); err != nil {
			d.logger.Error("Module change callback failed",
				zap.String("module", moduleName),
				zap.String("change_type", d.changeTypeString(changeType)),
				zap.Error(err),
			)
		}
	}
}

// changeTypeString 变化类型转字符串
func (d *ModuleDiscovery) changeTypeString(changeType ModuleChangeType) string {
	switch changeType {
	case ModuleDiscovered:
		return "discovered"
	case ModuleEnabled:
		return "enabled"
	case ModuleDisabled:
		return "disabled"
	case ModuleActivated:
		return "activated"
	case ModuleDeactivated:
		return "deactivated"
	case ModuleError:
		return "error"
	case ModuleUpdated:
		return "updated"
	default:
		return "unknown"
	}
}

// ValidateModuleDependencies 验证模块依赖关系
func (d *ModuleDiscovery) ValidateModuleDependencies() error {
	for _, module := range d.modules {
		for _, dep := range module.Dependencies {
			if _, exists := d.modules[dep]; !exists {
				return fmt.Errorf("module %s depends on non-existent module: %s", module.Name, dep)
			}
		}
	}
	return nil
}

// StartHotPlugMonitoring 启动热插拔监控
func (d *ModuleDiscovery) StartHotPlugMonitoring() error {
	d.mu.Lock()
	defer d.mu.Unlock()

	// 创建文件系统监控器
	watcher := NewFileSystemWatcher(d.modulesPath, d.logger)
	watcher.AddCallback(d.handleModuleChange)

	if err := watcher.Start(); err != nil {
		return fmt.Errorf("failed to start hot plug monitoring: %w", err)
	}

	d.watchers = append(d.watchers, watcher)
	d.logger.Info("Hot plug monitoring started", zap.String("path", d.modulesPath))
	return nil
}

// StopHotPlugMonitoring 停止热插拔监控
func (d *ModuleDiscovery) StopHotPlugMonitoring() error {
	d.mu.Lock()
	defer d.mu.Unlock()

	for _, watcher := range d.watchers {
		if err := watcher.Stop(); err != nil {
			d.logger.Error("Failed to stop watcher", zap.Error(err))
		}
	}

	d.watchers = d.watchers[:0]
	d.logger.Info("Hot plug monitoring stopped")
	return nil
}

// AddChangeCallback 添加模块变化回调
func (d *ModuleDiscovery) AddChangeCallback(callback ModuleChangeCallback) {
	d.mu.Lock()
	defer d.mu.Unlock()
	d.callbacks = append(d.callbacks, callback)
}

// handleModuleChange 处理模块变化
func (d *ModuleDiscovery) handleModuleChange(event ModuleChangeEvent) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	switch event.Type {
	case ModuleDiscovered:
		d.modules[event.ModuleName] = event.Recipe
		d.logger.Info("Module discovered", zap.String("name", event.ModuleName))

	case ModuleEnabled:
		if existing, exists := d.modules[event.ModuleName]; exists {
			existing.SetStatus(ModuleStatusEnabled)
			d.logger.Info("Module enabled", zap.String("name", event.ModuleName))
		}

	case ModuleDisabled:
		if existing, exists := d.modules[event.ModuleName]; exists {
			existing.SetStatus(ModuleStatusDisabled)
			d.logger.Info("Module disabled", zap.String("name", event.ModuleName))
		}

	case ModuleActivated:
		if existing, exists := d.modules[event.ModuleName]; exists {
			existing.SetStatus(ModuleStatusActive)
			d.logger.Info("Module activated", zap.String("name", event.ModuleName))
		}

	case ModuleDeactivated:
		if existing, exists := d.modules[event.ModuleName]; exists {
			existing.SetStatus(ModuleStatusEnabled)
			d.logger.Info("Module deactivated", zap.String("name", event.ModuleName))
		}

	case ModuleError:
		if existing, exists := d.modules[event.ModuleName]; exists {
			existing.SetStatus(ModuleStatusError)
			d.logger.Error("Module error",
				zap.String("name", event.ModuleName),
				zap.String("error", existing.ErrorMsg),
			)
		}

	case ModuleUpdated:
		if existing, exists := d.modules[event.ModuleName]; exists {
			// 更新模块元数据
			existing.Dependencies = event.Recipe.Dependencies
			existing.Version = event.Recipe.Version
			d.logger.Info("Module updated", zap.String("name", event.ModuleName))
		}
	}

	// 通知所有回调
	for _, callback := range d.callbacks {
		if err := callback(event); err != nil {
			d.logger.Error("Module change callback failed",
				zap.String("module", event.ModuleName),
				zap.Error(err),
			)
		}
	}

	return nil
}

// RescanModules 重新扫描模块（手动触发）
func (d *ModuleDiscovery) RescanModules() ([]*ModuleRecipe, error) {
	d.logger.Info("Rescanning modules")

	// 保存当前模块列表
	d.mu.RLock()
	oldModules := make(map[string]*ModuleRecipe)
	for k, v := range d.modules {
		oldModules[k] = v
	}
	d.mu.RUnlock()

	// 重新发现模块
	newModules, err := d.DiscoverModules()
	if err != nil {
		return nil, err
	}

	// 比较变化并触发事件
	d.detectChanges(oldModules, d.modules)

	return newModules, nil
}

// detectChanges 检测模块变化
func (d *ModuleDiscovery) detectChanges(oldModules, newModules map[string]*ModuleRecipe) {
	now := time.Now()

	// 检测新增和修改的模块
	for name, newModule := range newModules {
		if oldModule, exists := oldModules[name]; exists {
			// 检查是否有修改
			if !d.moduleEquals(oldModule, newModule) {
				event := ModuleChangeEvent{
					Type:       ModuleUpdated,
					ModuleName: name,
					Recipe:     newModule,
					Timestamp:  now,
				}
				d.handleModuleChange(event)
			}
		} else {
			// 新增模块
			event := ModuleChangeEvent{
				Type:       ModuleDiscovered,
				ModuleName: name,
				Recipe:     newModule,
				Timestamp:  now,
			}
			d.handleModuleChange(event)
		}
	}

	// 检测删除的模块
	for name, oldModule := range oldModules {
		if _, exists := newModules[name]; !exists {
			event := ModuleChangeEvent{
				Type:       ModuleDisabled, // 将删除视为禁用
				ModuleName: name,
				Recipe:     oldModule,
				Timestamp:  now,
			}
			d.handleModuleChange(event)
		}
	}
}

// moduleEquals 比较两个模块是否相等
func (d *ModuleDiscovery) moduleEquals(a, b *ModuleRecipe) bool {
	if a.Name != b.Name || a.Path != b.Path || a.Version != b.Version {
		return false
	}

	if len(a.Dependencies) != len(b.Dependencies) {
		return false
	}

	for i, dep := range a.Dependencies {
		if dep != b.Dependencies[i] {
			return false
		}
	}

	return true
}

// ActivateModuleLicense 激活模块许可证
func (d *ModuleDiscovery) ActivateModuleLicense(name, licenseKey string) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	module, exists := d.modules[name]
	if !exists {
		return fmt.Errorf("module not found: %s", name)
	}

	if !module.RequiresActivation() {
		return fmt.Errorf("module %s does not require license activation", name)
	}

	// TODO: 验证许可证密钥的有效性
	// 这里应该调用许可证验证服务

	module.ActivateLicense(licenseKey)
	d.logger.Info("Module license activated",
		zap.String("name", name),
		zap.String("license_key", licenseKey[:min(8, len(licenseKey))]+"..."), // 只记录前8位
	)

	// 触发许可证激活事件
	d.notifyChange(ModuleActivated, name, module)
	return nil
}

// DeactivateModuleLicense 停用模块许可证
func (d *ModuleDiscovery) DeactivateModuleLicense(name string) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	module, exists := d.modules[name]
	if !exists {
		return fmt.Errorf("module not found: %s", name)
	}

	if !module.RequiresActivation() {
		return fmt.Errorf("module %s does not require license activation", name)
	}

	module.DeactivateLicense()
	d.logger.Info("Module license deactivated", zap.String("name", name))

	// 如果模块正在运行，需要停用
	if module.IsActive() {
		if err := d.deactivateModuleInternal(name, module); err != nil {
			d.logger.Error("Failed to deactivate module after license deactivation",
				zap.String("name", name),
				zap.Error(err),
			)
		}
		module.SetStatus(ModuleStatusEnabled)
	}

	// 触发许可证停用事件
	d.notifyChange(ModuleDeactivated, name, module)
	return nil
}

// GetLicensedModules 获取需要许可证的模块
func (d *ModuleDiscovery) GetLicensedModules() []*ModuleRecipe {
	d.mu.RLock()
	defer d.mu.RUnlock()

	licensed := make([]*ModuleRecipe, 0)
	for _, module := range d.modules {
		if module.RequiresActivation() {
			licensed = append(licensed, module)
		}
	}
	return licensed
}

// GetActivatedModules 获取已激活许可证的模块
func (d *ModuleDiscovery) GetActivatedModules() []*ModuleRecipe {
	d.mu.RLock()
	defer d.mu.RUnlock()

	activated := make([]*ModuleRecipe, 0)
	for _, module := range d.modules {
		if module.RequiresActivation() && module.IsLicenseActivated() {
			activated = append(activated, module)
		}
	}
	return activated
}

// findModuleForSite 查找指定站点的模块（内部方法，需要锁）
func (d *ModuleDiscovery) findModuleForSite(siteID uint, name string) (*ModuleRecipe, bool) {
	// 先查找站点特定模块
	if siteModules, exists := d.tenantModules[siteID]; exists {
		if module, exists := siteModules[name]; exists {
			return module, true
		}
	}

	// 再查找全局模块
	if module, exists := d.globalModules[name]; exists {
		return module, true
	}

	return nil, false
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
