<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 菜单管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        /* 树形菜单样式 */
        .menu-tree {
            margin: 0;
            padding: 0;
            list-style: none;
        }
        
        .menu-tree-item {
            position: relative;
            margin-bottom: 8px;
        }
        
        .menu-tree-content {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            background-color: rgba(42, 42, 42, 0.5);
            border: 1px solid rgba(255,255,255,0.05);
            border-radius: 8px;
            transition: all 0.2s ease;
        }
        
        .menu-tree-content:hover {
            background-color: rgba(51, 51, 51, 0.5);
            border-color: rgba(0,123,255,0.3);
        }
        
        .menu-tree-content.active {
            background-color: rgba(0, 123, 255, 0.1);
            border-color: rgba(0,123,255,0.5);
        }
        
        .menu-tree-toggle {
            margin-right: 10px;
            cursor: pointer;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #aaa;
        }
        
        .menu-tree-icon {
            margin-right: 10px;
            width: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .menu-tree-title {
            flex: 1;
            color: #e0e0e0;
        }
        
        .menu-tree-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .menu-tree-action {
            background: transparent;
            border: none;
            color: #aaa;
            cursor: pointer;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        
        .menu-tree-action:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
        }
        
        .menu-tree-action.edit:hover {
            color: #007bff;
        }
        
        .menu-tree-action.delete:hover {
            color: #dc3545;
        }
        
        .menu-tree-action.add:hover {
            color: #28a745;
        }
        
        .menu-tree-children {
            padding-left: 30px;
            margin-top: 8px;
            display: none;
        }
        
        .menu-tree-children.expanded {
            display: block;
        }
        
        .menu-tree-item.has-children > .menu-tree-content .menu-tree-toggle {
            color: #007bff;
        }
        
        /* 表单样式 */
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #e0e0e0;
        }
        
        .form-control {
            display: block;
            width: 100%;
            padding: 0.75rem 1rem;
            background-color: rgba(42, 42, 42, 0.5);
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 0.375rem;
            color: #e0e0e0;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        
        .form-control:focus {
            border-color: rgba(0,123,255,0.5);
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">菜单管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <div class="relative">
                            <select id="menuTypeSelect" class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 pr-8 text-white appearance-none">
                                <option value="main">主导航菜单</option>
                                <option value="footer">底部菜单</option>
                                <option value="user">用户中心菜单</option>
                                <option value="mobile">移动端菜单</option>
                            </select>
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                        <button id="addRootMenuBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-plus text-white"></i>
                                </span>
                                添加顶级菜单
                            </span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 主要内容区域 -->
            <div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
                <!-- 左侧菜单树 -->
                <div class="xl:col-span-2">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-white">菜单结构</h3>
                            <div class="flex space-x-2">
                                <button id="expandAllBtn" class="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm">
                                    <i class="fas fa-expand-alt mr-1"></i> 展开全部
                                </button>
                                <button id="collapseAllBtn" class="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm">
                                    <i class="fas fa-compress-alt mr-1"></i> 折叠全部
                                </button>
                            </div>
                        </div>
                        
                        <ul class="menu-tree">
                            <!-- 菜单项 1 -->
                            <li class="menu-tree-item has-children">
                                <div class="menu-tree-content active">
                                    <div class="menu-tree-toggle">
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                    <div class="menu-tree-icon">
                                        <i class="fas fa-home text-blue-400"></i>
                                    </div>
                                    <div class="menu-tree-title">首页</div>
                                    <div class="menu-tree-actions">
                                        <button class="menu-tree-action add" title="添加子菜单">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button class="menu-tree-action edit" title="编辑菜单">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="menu-tree-action delete" title="删除菜单">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </div>
                                <ul class="menu-tree-children expanded">
                                    <li class="menu-tree-item">
                                        <div class="menu-tree-content">
                                            <div class="menu-tree-toggle">
                                                <i class="fas fa-chevron-right"></i>
                                            </div>
                                            <div class="menu-tree-icon">
                                                <i class="fas fa-chart-line text-green-400"></i>
                                            </div>
                                            <div class="menu-tree-title">数据概览</div>
                                            <div class="menu-tree-actions">
                                                <button class="menu-tree-action add" title="添加子菜单">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                                <button class="menu-tree-action edit" title="编辑菜单">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="menu-tree-action delete" title="删除菜单">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </li>
                                    <li class="menu-tree-item">
                                        <div class="menu-tree-content">
                                            <div class="menu-tree-toggle">
                                                <i class="fas fa-chevron-right"></i>
                                            </div>
                                            <div class="menu-tree-icon">
                                                <i class="fas fa-bell text-yellow-400"></i>
                                            </div>
                                            <div class="menu-tree-title">消息中心</div>
                                            <div class="menu-tree-actions">
                                                <button class="menu-tree-action add" title="添加子菜单">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                                <button class="menu-tree-action edit" title="编辑菜单">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="menu-tree-action delete" title="删除菜单">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </li>
                            
                            <!-- 菜单项 2 -->
                            <li class="menu-tree-item has-children">
                                <div class="menu-tree-content">
                                    <div class="menu-tree-toggle">
                                        <i class="fas fa-chevron-right"></i>
                                    </div>
                                    <div class="menu-tree-icon">
                                        <i class="fas fa-newspaper text-purple-400"></i>
                                    </div>
                                    <div class="menu-tree-title">内容管理</div>
                                    <div class="menu-tree-actions">
                                        <button class="menu-tree-action add" title="添加子菜单">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button class="menu-tree-action edit" title="编辑菜单">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="menu-tree-action delete" title="删除菜单">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </div>
                                <ul class="menu-tree-children">
                                    <li class="menu-tree-item">
                                        <div class="menu-tree-content">
                                            <div class="menu-tree-toggle">
                                                <i class="fas fa-chevron-right"></i>
                                            </div>
                                            <div class="menu-tree-icon">
                                                <i class="fas fa-file-alt text-blue-400"></i>
                                            </div>
                                            <div class="menu-tree-title">文章管理</div>
                                            <div class="menu-tree-actions">
                                                <button class="menu-tree-action add" title="添加子菜单">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                                <button class="menu-tree-action edit" title="编辑菜单">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="menu-tree-action delete" title="删除菜单">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </li>
                                    <li class="menu-tree-item">
                                        <div class="menu-tree-content">
                                            <div class="menu-tree-toggle">
                                                <i class="fas fa-chevron-right"></i>
                                            </div>
                                            <div class="menu-tree-icon">
                                                <i class="fas fa-folder text-yellow-400"></i>
                                            </div>
                                            <div class="menu-tree-title">栏目管理</div>
                                            <div class="menu-tree-actions">
                                                <button class="menu-tree-action add" title="添加子菜单">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                                <button class="menu-tree-action edit" title="编辑菜单">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="menu-tree-action delete" title="删除菜单">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </li>
                            
                            <!-- 菜单项 3 -->
                            <li class="menu-tree-item">
                                <div class="menu-tree-content">
                                    <div class="menu-tree-toggle">
                                        <i class="fas fa-chevron-right"></i>
                                    </div>
                                    <div class="menu-tree-icon">
                                        <i class="fas fa-user text-green-400"></i>
                                    </div>
                                    <div class="menu-tree-title">用户中心</div>
                                    <div class="menu-tree-actions">
                                        <button class="menu-tree-action add" title="添加子菜单">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button class="menu-tree-action edit" title="编辑菜单">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="menu-tree-action delete" title="删除菜单">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </div>
                            </li>
                            
                            <!-- 菜单项 4 -->
                            <li class="menu-tree-item">
                                <div class="menu-tree-content">
                                    <div class="menu-tree-toggle">
                                        <i class="fas fa-chevron-right"></i>
                                    </div>
                                    <div class="menu-tree-icon">
                                        <i class="fas fa-cog text-gray-400"></i>
                                    </div>
                                    <div class="menu-tree-title">系统设置</div>
                                    <div class="menu-tree-actions">
                                        <button class="menu-tree-action add" title="添加子菜单">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button class="menu-tree-action edit" title="编辑菜单">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="menu-tree-action delete" title="删除菜单">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 菜单预览 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-lg font-semibold text-white">菜单预览</h3>
                            <div>
                                <select class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white">
                                    <option>桌面视图</option>
                                    <option>平板视图</option>
                                    <option>手机视图</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 桌面菜单预览 -->
                        <div class="bg-gray-900/50 rounded-lg p-4 mb-4">
                            <div class="flex justify-between items-center border-b border-gray-700 pb-4 mb-4">
                                <div class="flex items-center">
                                    <div class="text-xl font-bold text-white mr-8">LOGO</div>
                                    <div class="flex space-x-6">
                                        <div class="text-blue-400 font-medium">首页</div>
                                        <div class="text-white font-medium">内容管理</div>
                                        <div class="text-white font-medium">用户中心</div>
                                        <div class="text-white font-medium">系统设置</div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <div class="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center">
                                        <i class="fas fa-search text-gray-400"></i>
                                    </div>
                                    <div class="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center">
                                        <i class="fas fa-bell text-gray-400"></i>
                                    </div>
                                    <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="flex">
                                <div class="text-blue-400 font-medium mr-6">数据概览</div>
                                <div class="text-white font-medium">消息中心</div>
                            </div>
                        </div>
                        
                        <p class="text-gray-400 text-sm">此预览展示了当前菜单的桌面端展示效果。切换视图可查看不同设备上的菜单布局。</p>
                    </div>
                </div>
                
                <!-- 右侧编辑区域 -->
                <div class="xl:col-span-1">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold text-white mb-6">编辑菜单项</h3>
                        
                        <form>
                            <div class="form-group">
                                <label class="form-label">菜单名称</label>
                                <input type="text" class="form-control" value="首页">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">菜单图标</label>
                                <div class="flex">
                                    <div class="w-10 h-10 bg-gray-700 rounded flex items-center justify-center mr-3">
                                        <i class="fas fa-home text-blue-400 text-xl"></i>
                                    </div>
                                    <button type="button" class="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-white rounded text-sm">
                                        选择图标
                                    </button>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">链接类型</label>
                                <select class="form-control">
                                    <option>网站页面</option>
                                    <option>外部链接</option>
                                    <option>自定义</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">选择页面</label>
                                <select class="form-control">
                                    <option>首页</option>
                                    <option>仪表盘</option>
                                    <option>文章列表</option>
                                    <option>关于我们</option>
                                    <option>联系方式</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">URL 路径</label>
                                <input type="text" class="form-control" value="/" readonly>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">打开方式</label>
                                <div class="flex items-center space-x-4">
                                    <div class="flex items-center">
                                        <input type="radio" id="target_self" name="target" value="_self" checked class="mr-2">
                                        <label for="target_self">当前窗口</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="radio" id="target_blank" name="target" value="_blank" class="mr-2">
                                        <label for="target_blank">新窗口</label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">高级选项</label>
                                <div class="bg-gray-800/20 p-4 rounded-lg">
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" class="mr-2">
                                            <span>仅限已登录用户可见</span>
                                        </label>
                                    </div>
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" class="mr-2">
                                            <span>添加徽章提示</span>
                                        </label>
                                    </div>
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" class="mr-2" checked>
                                            <span>显示在移动端</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex justify-between mt-8">
                                <button type="button" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded">
                                    取消
                                </button>
                                <button type="button" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded">
                                    保存更改
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 菜单树交互
            const toggleButtons = document.querySelectorAll('.menu-tree-toggle');
            toggleButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const menuItem = this.closest('.menu-tree-item');
                    const childrenContainer = menuItem.querySelector('.menu-tree-children');
                    
                    if (childrenContainer) {
                        childrenContainer.classList.toggle('expanded');
                        
                        if (childrenContainer.classList.contains('expanded')) {
                            this.innerHTML = '<i class="fas fa-chevron-down"></i>';
                        } else {
                            this.innerHTML = '<i class="fas fa-chevron-right"></i>';
                        }
                    }
                });
            });
            
            // 菜单内容点击事件
            const menuContents = document.querySelectorAll('.menu-tree-content');
            menuContents.forEach(content => {
                content.addEventListener('click', function(e) {
                    // 忽略切换按钮和操作按钮的点击
                    if (e.target.closest('.menu-tree-toggle') || e.target.closest('.menu-tree-actions')) {
                        return;
                    }
                    
                    // 清除其他菜单的激活状态
                    menuContents.forEach(c => c.classList.remove('active'));
                    
                    // 设置当前菜单的激活状态
                    this.classList.add('active');
                });
            });
            
            // 展开全部按钮
            document.getElementById('expandAllBtn').addEventListener('click', function() {
                const childrenContainers = document.querySelectorAll('.menu-tree-children');
                const toggles = document.querySelectorAll('.menu-tree-toggle');
                
                childrenContainers.forEach(container => {
                    container.classList.add('expanded');
                });
                
                toggles.forEach(toggle => {
                    toggle.innerHTML = '<i class="fas fa-chevron-down"></i>';
                });
            });
            
            // 折叠全部按钮
            document.getElementById('collapseAllBtn').addEventListener('click', function() {
                const childrenContainers = document.querySelectorAll('.menu-tree-children');
                const toggles = document.querySelectorAll('.menu-tree-toggle');
                
                childrenContainers.forEach(container => {
                    container.classList.remove('expanded');
                });
                
                toggles.forEach(toggle => {
                    toggle.innerHTML = '<i class="fas fa-chevron-right"></i>';
                });
            });
            
            // 添加顶级菜单按钮
            document.getElementById('addRootMenuBtn').addEventListener('click', function() {
                // 这里可以添加逻辑，比如显示添加菜单的表单
                const menuTree = document.querySelector('.menu-tree');
                
                // 创建一个新的菜单项
                const newMenuItem = document.createElement('li');
                newMenuItem.className = 'menu-tree-item';
                newMenuItem.innerHTML = `
                    <div class="menu-tree-content">
                        <div class="menu-tree-toggle">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                        <div class="menu-tree-icon">
                            <i class="fas fa-link text-blue-400"></i>
                        </div>
                        <div class="menu-tree-title">新菜单项</div>
                        <div class="menu-tree-actions">
                            <button class="menu-tree-action add" title="添加子菜单">
                                <i class="fas fa-plus"></i>
                            </button>
                            <button class="menu-tree-action edit" title="编辑菜单">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="menu-tree-action delete" title="删除菜单">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                `;
                
                menuTree.appendChild(newMenuItem);
                
                // 为新添加的菜单项添加事件
                const newToggle = newMenuItem.querySelector('.menu-tree-toggle');
                const newContent = newMenuItem.querySelector('.menu-tree-content');
                
                newToggle.addEventListener('click', function() {
                    const childrenContainer = this.closest('.menu-tree-item').querySelector('.menu-tree-children');
                    
                    if (childrenContainer) {
                        childrenContainer.classList.toggle('expanded');
                        
                        if (childrenContainer.classList.contains('expanded')) {
                            this.innerHTML = '<i class="fas fa-chevron-down"></i>';
                        } else {
                            this.innerHTML = '<i class="fas fa-chevron-right"></i>';
                        }
                    }
                });
                
                newContent.addEventListener('click', function(e) {
                    if (e.target.closest('.menu-tree-toggle') || e.target.closest('.menu-tree-actions')) {
                        return;
                    }
                    
                    menuContents.forEach(c => c.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html> 