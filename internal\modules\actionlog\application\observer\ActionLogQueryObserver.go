/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/modules/actionlog/application/observer/ActionLogQueryObserver.go
 * @Description: 操作日志模块的查询观察者，遵循统一的目录结构和命名规范
 * 
 * © 2025 GACMS. All rights reserved.
 */

package observer

import (
	"context"
	"time"

	actionLogService "gacms/internal/modules/actionlog/application/service"
	"gacms/pkg/contract"
	"go.uber.org/zap"
)

// ActionLogQueryObserver 操作日志模块的查询观察者
// 处理操作日志查询相关的业务事件，如仪表板统计查询等
type ActionLogQueryObserver struct {
	service *actionLogService.ActionLogService
	logger  *zap.Logger
}

// NewActionLogQueryObserver 创建操作日志查询观察者
func NewActionLogQueryObserver(
	service *actionLogService.ActionLogService,
	logger *zap.Logger,
) *ActionLogQueryObserver {
	return &ActionLogQueryObserver{
		service: service,
		logger:  logger,
	}
}

// Handle 处理查询事件（统一的观察者模式）
func (o *ActionLogQueryObserver) Handle(event contract.Event) error {
	switch event.Name() {
	case "dashboard.stats.query":
		return o.handleDashboardStatsQuery(event)
	case "operation.log.recent.query":
		return o.handleRecentLogsQuery(event)
	default:
		// 不处理不支持的事件
		return nil
	}
}

// handleDashboardStatsQuery 处理仪表板统计查询事件
func (o *ActionLogQueryObserver) handleDashboardStatsQuery(event contract.Event) error {
	// 获取事件数据
	eventData := event.GetData()
	if eventData == nil {
		o.logger.Error("ActionLogQuery observer received nil event data for dashboard stats")
		return nil
	}

	// 异步处理统计查询
	go o.processDashboardStats(event)

	return nil
}

// handleRecentLogsQuery 处理最近日志查询事件
func (o *ActionLogQueryObserver) handleRecentLogsQuery(event contract.Event) error {
	// 获取事件数据
	eventData := event.GetData()
	if eventData == nil {
		o.logger.Error("ActionLogQuery observer received nil event data for recent logs")
		return nil
	}

	// 异步处理最近日志查询
	go o.processRecentLogs(event)

	return nil
}

// processDashboardStats 处理仪表板统计（异步）
func (o *ActionLogQueryObserver) processDashboardStats(event contract.Event) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 获取操作日志统计数据
	stats, err := o.service.GetDashboardStats(ctx)
	if err != nil {
		o.logger.Error("ActionLogQuery observer failed to get dashboard stats",
			zap.Error(err),
		)
		return
	}

	// 设置响应数据（如果事件支持响应）
	if responsiveEvent, ok := event.(contract.ResponsiveEvent); ok {
		responseData := map[string]interface{}{
			"total_logs":    stats.TotalLogs,
			"today_logs":    stats.TodayLogs,
			"recent_users":  stats.RecentUsers,
			"top_actions":   stats.TopActions,
		}
		responsiveEvent.SetResponse(responseData)
	}

	o.logger.Debug("ActionLogQuery observer processed dashboard stats successfully")
}

// processRecentLogs 处理最近日志查询（异步）
func (o *ActionLogQueryObserver) processRecentLogs(event contract.Event) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 提取查询参数
	eventData := event.GetData()
	limit := o.extractInt(eventData["limit"], 10)
	offset := o.extractInt(eventData["offset"], 0)

	// 获取最近的操作日志
	logs, err := o.service.GetRecentLogs(ctx, limit, offset)
	if err != nil {
		o.logger.Error("ActionLogQuery observer failed to get recent logs",
			zap.Error(err),
			zap.Int("limit", limit),
			zap.Int("offset", offset),
		)
		return
	}

	// 设置响应数据（如果事件支持响应）
	if responsiveEvent, ok := event.(contract.ResponsiveEvent); ok {
		responseData := map[string]interface{}{
			"activities": logs,
			"limit":      limit,
			"offset":     offset,
		}
		responsiveEvent.SetResponse(responseData)
	}

	o.logger.Debug("ActionLogQuery observer processed recent logs successfully",
		zap.Int("count", len(logs)),
		zap.Int("limit", limit),
		zap.Int("offset", offset),
	)
}

// HandlerName 返回处理器名称
func (o *ActionLogQueryObserver) HandlerName() string {
	return "actionlog.query.observer"
}

// SupportedEvents 返回支持的事件列表
func (o *ActionLogQueryObserver) SupportedEvents() []contract.EventName {
	return []contract.EventName{
		"dashboard.stats.query",
		"operation.log.recent.query",
	}
}

// extractInt 从事件数据中提取int值
func (o *ActionLogQueryObserver) extractInt(value interface{}, defaultValue int) int {
	switch v := value.(type) {
	case int:
		return v
	case int32:
		return int(v)
	case int64:
		return int(v)
	case uint:
		return int(v)
	case uint32:
		return int(v)
	case uint64:
		return int(v)
	case float64:
		return int(v)
	default:
		return defaultValue
	}
}

// Priority 返回事件处理器的优先级
func (o *ActionLogQueryObserver) Priority() int {
	// 查询操作的优先级中等
	return 50
}

// IsAsync 返回是否异步处理
func (o *ActionLogQueryObserver) IsAsync() bool {
	// 查询操作异步处理，不阻塞主业务流程
	return true
}
