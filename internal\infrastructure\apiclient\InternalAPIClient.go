/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-13
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-13
 * @FilePath: internal/infrastructure/apiclient/InternalAPIClient.go
 * @Description: Implements a generic client for internal service-to-service API calls.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package apiclient

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"gacms/pkg/contract"
	"io"
	"net/http"
	"net/url"
	"time"
)

// ensure InternalAPIClient implements the APIClient interface.
var _ contract.APIClient = (*InternalAPIClient)(nil)

// InternalAPIClient provides a concrete implementation for the APIClient contract.
// It handles making HTTP requests between services within the application.
type InternalAPIClient struct {
	client  *http.Client
	baseURL string // Base URL for the internal API gateway, e.g., "http://127.0.0.1:8080"
	auth    contract.Auth
}

// NewInternalAPIClient creates a new client for internal API communications.
// The baseURL should be sourced from configuration.
func NewInternalAPIClient(baseURL string, auth contract.Auth) contract.APIClient {
	return &InternalAPIClient{
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
		baseURL: baseURL,
		auth:    auth,
	}
}

// Post sends a POST request to a full internal path.
func (c *InternalAPIClient) Post(ctx context.Context, path string, payload interface{}) (*contract.APIResponse, error) {
	return c.doRequestWithBody(ctx, "POST", path, payload)
}

// Get sends a GET request to a full internal path.
func (c *InternalAPIClient) Get(ctx context.Context, path string, params map[string]string) (*contract.APIResponse, error) {
	fullURL := c.baseURL + path

	req, err := http.NewRequestWithContext(ctx, "GET", fullURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create GET request: %w", err)
	}

	// Add query parameters
	q := req.URL.Query()
	for key, value := range params {
		q.Add(key, value)
	}
	req.URL.RawQuery = q.Encode()

	// Propagate headers
	c.propagateHeaders(ctx, req)

	// Execute the request
	return c.doRequest(req)
}

// Put sends a PUT request with a JSON body to a full internal path.
func (c *InternalAPIClient) Put(ctx context.Context, path string, payload interface{}) (*contract.APIResponse, error) {
	return c.doRequestWithBody(ctx, "PUT", path, payload)
}

// Patch sends a PATCH request with a JSON body to a full internal path.
func (c *InternalAPIClient) Patch(ctx context.Context, path string, payload interface{}) (*contract.APIResponse, error) {
	return c.doRequestWithBody(ctx, "PATCH", path, payload)
}

// Delete sends a DELETE request to a full internal path.
func (c *InternalAPIClient) Delete(ctx context.Context, path string) (*contract.APIResponse, error) {
	fullURL := c.baseURL + path

	req, err := http.NewRequestWithContext(ctx, "DELETE", fullURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create DELETE request: %w", err)
	}

	c.propagateHeaders(ctx, req)
	return c.doRequest(req)
}

// propagateHeaders extracts relevant headers from the incoming context and adds them to the outgoing request.
// This is crucial for propagating authentication, tracing, and other contextual information.
func (c *InternalAPIClient) propagateHeaders(ctx context.Context, req *http.Request) {
	req.Header.Set("Content-Type", "application/json")

	// Propagate the authorization token
	if token, ok := c.auth.TokenFrom(ctx); ok {
		req.Header.Set("Authorization", "Bearer "+token)
	}
	
	// TODO: Propagate other headers like trace IDs (e.g., X-Request-ID)
}

func (c *InternalAPIClient) doRequestWithBody(ctx context.Context, method, path string, payload interface{}) (*contract.APIResponse, error) {
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payload: %w", err)
	}

	fullURL := c.baseURL + path

	req, err := http.NewRequestWithContext(ctx, method, fullURL, bytes.NewBuffer(jsonPayload))
	if err != nil {
		return nil, fmt.Errorf("failed to create %s request: %w", method, err)
	}

	c.propagateHeaders(ctx, req)
	return c.doRequest(req)
}

// doRequest executes the http request and handles the response.
func (c *InternalAPIClient) doRequest(req *http.Request) (*contract.APIResponse, error) {
	resp, err := c.client.Do(req)
	if err != nil {
		if err, ok := err.(*url.Error); ok && err.Timeout() {
			return nil, fmt.Errorf("request to %s timed out", req.URL.String())
		}
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	apiResponse := &contract.APIResponse{
		StatusCode: resp.StatusCode,
		Body:       body,
		Header:     resp.Header,
	}

	if resp.StatusCode >= 400 {
		return apiResponse, fmt.Errorf("api call to %s failed with status %d: %s", req.URL.String(), resp.StatusCode, string(body))
	}

	return apiResponse, nil
} 