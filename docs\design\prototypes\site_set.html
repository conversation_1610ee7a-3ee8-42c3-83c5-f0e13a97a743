<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 站点设置</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        /* 站点设置页面特定样式 */
        .tabs {
            display: flex;
            border-bottom: 1px solid #333;
            margin-bottom: 25px;
            overflow-x: auto;
            scrollbar-width: thin;
            scrollbar-color: #555 #1e1e1e;
        }
        
        .tabs::-webkit-scrollbar {
            height: 6px;
        }
        
        .tabs::-webkit-scrollbar-track {
            background: #1e1e1e;
        }
        
        .tabs::-webkit-scrollbar-thumb {
            background-color: #555;
            border-radius: 6px;
        }
        
        .tab-button {
            padding: 12px 20px;
            background: none;
            border: none;
            color: #a0a0a0;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
            position: relative;
        }
        
        .tab-button:hover {
            color: #fff;
        }
        
        .tab-button.active {
            color: #007bff;
        }
        
        .tab-button.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: #007bff;
            border-radius: 3px 3px 0 0;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .image-preview {
            width: 100%;
            max-width: 300px;
            height: auto;
            border-radius: 6px;
            margin-top: 10px;
            border: 1px solid #444;
        }
        
        .file-upload {
            position: relative;
            overflow: hidden;
            display: inline-block;
            cursor: pointer;
        }
        
        .file-upload input[type=file] {
            position: absolute;
            font-size: 100px;
            opacity: 0;
            right: 0;
            top: 0;
            cursor: pointer;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10">
        <!-- 侧边栏内容将通过JavaScript从模板加载 -->
    </aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">站点设置</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="saveSettings" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-save text-white"></i>
                                </span>
                                保存设置
                            </span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 设置标签页 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <!-- 标签导航 -->
                <div class="tabs">
                    <button class="tab-button active" data-tab="general">常规设置</button>
                    <button class="tab-button" data-tab="appearance">外观设置</button>
                    <button class="tab-button" data-tab="seo">SEO 设置</button>
                    <button class="tab-button" data-tab="comments">评论设置</button>
                    <button class="tab-button" data-tab="permalinks">固定链接</button>
                    <button class="tab-button" data-tab="advanced">高级设置</button>
                </div>
                
                <!-- 常规设置 -->
                <div class="tab-content active" id="general">
                    <form>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- 站点标题 -->
                            <div class="mb-4">
                                <label class="block text-gray-400 text-sm font-medium mb-2">站点标题</label>
                                <input type="text" value="GACMS 官方演示站" 
                                       class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <p class="text-gray-400 text-xs mt-1">您的站点标题将显示在浏览器标签和搜索引擎结果中。</p>
                            </div>
                            
                            <!-- 站点副标题 -->
                            <div class="mb-4">
                                <label class="block text-gray-400 text-sm font-medium mb-2">站点副标题</label>
                                <input type="text" value="专业的内容管理系统" 
                                       class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <p class="text-gray-400 text-xs mt-1">简短描述您的站点，通常显示在站点标题旁边。</p>
                            </div>
                            
                            <!-- 站点地址 -->
                            <div class="mb-4">
                                <label class="block text-gray-400 text-sm font-medium mb-2">站点地址 (URL)</label>
                                <input type="url" value="https://www.example.com" 
                                       class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <p class="text-gray-400 text-xs mt-1">您的网站访问地址，包括 http:// 或 https://。</p>
                            </div>
                            
                            <!-- 管理员邮箱 -->
                            <div class="mb-4">
                                <label class="block text-gray-400 text-sm font-medium mb-2">管理员邮箱</label>
                                <input type="email" value="<EMAIL>" 
                                       class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <p class="text-gray-400 text-xs mt-1">此邮箱用于接收系统通知和恢复密码。</p>
                            </div>
                            
                            <!-- 时区设置 -->
                            <div class="mb-4">
                                <label class="block text-gray-400 text-sm font-medium mb-2">时区设置</label>
                                <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="UTC+8" selected>北京时间 (UTC+8)</option>
                                    <option value="UTC+0">格林威治标准时间 (UTC+0)</option>
                                    <option value="UTC-5">美国东部时间 (UTC-5)</option>
                                    <option value="UTC-8">美国太平洋时间 (UTC-8)</option>
                                    <option value="UTC+1">中欧时间 (UTC+1)</option>
                                    <option value="UTC+9">日本标准时间 (UTC+9)</option>
                                </select>
                                <p class="text-gray-400 text-xs mt-1">选择与您所在地匹配的时区。</p>
                            </div>
                            
                            <!-- 日期格式 -->
                            <div class="mb-4">
                                <label class="block text-gray-400 text-sm font-medium mb-2">日期格式</label>
                                <div class="space-y-2">
                                    <div class="flex items-center">
                                        <input type="radio" id="date-format-1" name="date-format" checked class="mr-2 text-blue-500 focus:ring-blue-500">
                                        <label for="date-format-1">2025-06-05</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="radio" id="date-format-2" name="date-format" class="mr-2 text-blue-500 focus:ring-blue-500">
                                        <label for="date-format-2">05/06/2025</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="radio" id="date-format-3" name="date-format" class="mr-2 text-blue-500 focus:ring-blue-500">
                                        <label for="date-format-3">2025年06月05日</label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 首页显示 -->
                            <div class="mb-4 col-span-1 md:col-span-2">
                                <label class="block text-gray-400 text-sm font-medium mb-2">首页显示</label>
                                <div class="space-y-2">
                                    <div class="flex items-center">
                                        <input type="radio" id="home-latest" name="home-display" checked class="mr-2 text-blue-500 focus:ring-blue-500">
                                        <label for="home-latest">最新文章</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="radio" id="home-page" name="home-display" class="mr-2 text-blue-500 focus:ring-blue-500">
                                        <label for="home-page">静态页面</label>
                                    </div>
                                </div>
                                <p class="text-gray-400 text-xs mt-1">选择访问网站首页时显示的内容。</p>
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- 外观设置 -->
                <div class="tab-content" id="appearance">
                    <form>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Logo上传 -->
                            <div class="mb-4">
                                <label class="block text-gray-400 text-sm font-medium mb-2">站点Logo</label>
                                <div>
                                    <img src="./assets/images/logo.svg" alt="当前Logo" class="image-preview mb-3">
                                    <div class="file-upload">
                                        <button type="button" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                                            <i class="fas fa-upload mr-2"></i> 更换Logo
                                        </button>
                                        <input type="file" accept="image/*" />
                                    </div>
                                    <p class="text-gray-400 text-xs mt-2">推荐尺寸: 200×50 像素, 支持 PNG, SVG 格式</p>
                                </div>
                            </div>
                            
                            <!-- 网站图标 -->
                            <div class="mb-4">
                                <label class="block text-gray-400 text-sm font-medium mb-2">网站图标 (Favicon)</label>
                                <div>
                                    <img src="./assets/images/favicon.ico" alt="当前网站图标" class="w-16 h-16 object-contain border border-gray-600 rounded p-1 mb-3">
                                    <div class="file-upload">
                                        <button type="button" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                                            <i class="fas fa-upload mr-2"></i> 更换图标
                                        </button>
                                        <input type="file" accept="image/x-icon,image/png" />
                                    </div>
                                    <p class="text-gray-400 text-xs mt-2">推荐尺寸: 32×32 像素, 支持 ICO, PNG 格式</p>
                                </div>
                            </div>
                            
                            <!-- 主题选择 -->
                            <div class="mb-4 col-span-1 md:col-span-2">
                                <label class="block text-gray-400 text-sm font-medium mb-2">网站主题</label>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    <div class="theme-item bg-gray-700/50 border border-blue-500 p-3 rounded-lg text-center">
                                        <img src="./assets/images/theme-default.jpg" alt="默认主题" class="w-full h-32 object-cover rounded-lg mb-2">
                                        <div class="text-sm font-medium text-white">默认主题</div>
                                        <div class="mt-2"><span class="text-xs bg-blue-500 text-white px-2 py-1 rounded">当前使用</span></div>
                                    </div>
                                    <div class="theme-item bg-gray-700/50 border border-gray-600 p-3 rounded-lg text-center">
                                        <img src="./assets/images/theme-business.jpg" alt="商务主题" class="w-full h-32 object-cover rounded-lg mb-2">
                                        <div class="text-sm font-medium text-white">商务主题</div>
                                        <div class="mt-2"><button class="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-500">启用</button></div>
                                    </div>
                                    <div class="theme-item bg-gray-700/50 border border-gray-600 p-3 rounded-lg text-center">
                                        <img src="./assets/images/theme-blog.jpg" alt="博客主题" class="w-full h-32 object-cover rounded-lg mb-2">
                                        <div class="text-sm font-medium text-white">博客主题</div>
                                        <div class="mt-2"><button class="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-500">启用</button></div>
                                    </div>
                                    <div class="theme-item bg-gray-700/50 border border-gray-600 p-3 rounded-lg text-center">
                                        <img src="./assets/images/theme-magazine.jpg" alt="杂志主题" class="w-full h-32 object-cover rounded-lg mb-2">
                                        <div class="text-sm font-medium text-white">杂志主题</div>
                                        <div class="mt-2"><button class="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-500">启用</button></div>
                                    </div>
                                </div>
                                <p class="text-gray-400 text-xs mt-2">
                                    <a href="themes.html" class="text-blue-400 hover:text-blue-300">浏览更多主题</a> 或 
                                    <a href="theme_market.html" class="text-blue-400 hover:text-blue-300">访问主题市场</a>
                                </p>
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- 其他标签内容可以类似添加... -->
                
                <!-- 暂时只显示前两个标签页的内容，其他标签可以后续添加 -->
                <div class="tab-content" id="seo">
                    <div class="p-4 text-center text-gray-400">
                        SEO 设置内容将在后续实现
                    </div>
                </div>
                
                <div class="tab-content" id="comments">
                    <div class="p-4 text-center text-gray-400">
                        评论设置内容将在后续实现
                    </div>
                </div>
                
                <div class="tab-content" id="permalinks">
                    <div class="p-4 text-center text-gray-400">
                        固定链接设置内容将在后续实现
                    </div>
                </div>
                
                <div class="tab-content" id="advanced">
                    <div class="p-4 text-center text-gray-400">
                        高级设置内容将在后续实现
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签页切换功能
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tabId = button.getAttribute('data-tab');
                    
                    // 移除所有标签的活动状态
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));
                    
                    // 为当前点击的标签设置活动状态
                    button.classList.add('active');
                    document.getElementById(tabId).classList.add('active');
                });
            });
            
            // 保存按钮点击事件
            document.getElementById('saveSettings').addEventListener('click', function() {
                // 这里可以添加保存设置的逻辑
                // 显示保存成功的通知
                alert('设置已成功保存！');
            });
        });
    </script>
</body>
</html>