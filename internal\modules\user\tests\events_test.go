/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/user/tests/events_test.go
 * @Description: RBAC权限相关事件测试
 * 
 * © 2025 GACMS. All rights reserved.
 */
package tests

import (
	"gacms/internal/modules/user/events"
	"gacms/internal/modules/user/domain/model"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// 测试权限创建事件
func TestPermissionCreatedEvent(t *testing.T) {
	// 准备测试数据
	now := time.Now()
	permission := &model.Permission{
		ID:          1,
		Slug:        "content.create",
		Description: "Allow content creation",
		SiteID:      2,
		CreatedAt:   now,
		CreatedBy:   3,
	}

	// 创建事件
	event := events.NewPermissionCreatedEvent(permission)

	// 验证事件内容
	assert.Equal(t, events.PermissionCreatedEventName, event.Name())
	assert.Equal(t, permission, event.Permission)
	assert.Equal(t, permission.ID, event.PermissionID)
	assert.Equal(t, permission.SiteID, event.SiteID)
	assert.Equal(t, permission.CreatedBy, event.OperatedBy)

	// 测试事件数据转换
	data := event.Data()
	assert.Equal(t, permission.ID, data["permission_id"])
	assert.Equal(t, permission.Slug, data["slug"])
	assert.Equal(t, permission.SiteID, data["site_id"])
	assert.Equal(t, permission.CreatedBy, data["operated_by"])
}

// 测试权限更新事件
func TestPermissionUpdatedEvent(t *testing.T) {
	// 准备测试数据
	now := time.Now()
	oldPermission := &model.Permission{
		ID:          1,
		Slug:        "content.edit",
		Description: "Allow content editing",
		SiteID:      2,
		UpdatedAt:   now.Add(-24 * time.Hour),
	}

	newPermission := &model.Permission{
		ID:          1,
		Slug:        "content.modify",
		Description: "Allow content modification",
		SiteID:      2,
		UpdatedAt:   now,
		UpdatedBy:   3,
	}

	// 创建事件
	event := events.NewPermissionUpdatedEvent(oldPermission, newPermission)

	// 验证事件内容
	assert.Equal(t, events.PermissionUpdatedEventName, event.Name())
	assert.Equal(t, newPermission, event.NewPermission)
	assert.Equal(t, oldPermission, event.OldPermission)
	assert.Equal(t, newPermission.ID, event.PermissionID)
	assert.Equal(t, newPermission.SiteID, event.SiteID)
	assert.Equal(t, newPermission.UpdatedBy, event.OperatedBy)

	// 测试事件数据转换
	data := event.Data()
	assert.Equal(t, newPermission.ID, data["permission_id"])
	assert.Equal(t, oldPermission.Slug, data["old_slug"])
	assert.Equal(t, newPermission.Slug, data["new_slug"])
	assert.Equal(t, newPermission.SiteID, data["site_id"])
	assert.Equal(t, newPermission.UpdatedBy, data["operated_by"])
}

// 测试权限删除事件
func TestPermissionDeletedEvent(t *testing.T) {
	// 准备测试数据
	now := time.Now()
	permission := &model.Permission{
		ID:        1,
		Slug:      "content.delete",
		SiteID:    2,
		DeletedAt: &now,
		DeletedBy: 3,
	}

	// 创建事件
	event := events.NewPermissionDeletedEvent(permission)

	// 验证事件内容
	assert.Equal(t, events.PermissionDeletedEventName, event.Name())
	assert.Equal(t, permission, event.Permission)
	assert.Equal(t, permission.ID, event.PermissionID)
	assert.Equal(t, permission.Slug, event.Slug)
	assert.Equal(t, permission.SiteID, event.SiteID)
	assert.Equal(t, permission.DeletedBy, event.OperatedBy)

	// 测试事件数据转换
	data := event.Data()
	assert.Equal(t, permission.ID, data["permission_id"])
	assert.Equal(t, permission.Slug, data["slug"])
	assert.Equal(t, permission.SiteID, data["site_id"])
	assert.Equal(t, permission.DeletedBy, data["operated_by"])
}

// 测试角色创建事件
func TestRoleCreatedEvent(t *testing.T) {
	// 准备测试数据
	now := time.Now()
	role := &model.Role{
		ID:          1,
		Name:        "Editor",
		Description: "Content editor role",
		Type:        model.AdminRole,
		Level:       2,
		SiteID:      3,
		CreatedAt:   now,
		CreatedBy:   4,
	}

	// 创建事件
	event := events.NewRoleCreatedEvent(role)

	// 验证事件内容
	assert.Equal(t, events.RoleCreatedEventName, event.Name())
	assert.Equal(t, role, event.Role)
	assert.Equal(t, role.ID, event.RoleID)
	assert.Equal(t, role.Name, event.RoleName)
	assert.Equal(t, role.Type, event.RoleType)
	assert.Equal(t, role.SiteID, event.SiteID)
	assert.Equal(t, role.CreatedBy, event.OperatedBy)

	// 测试事件数据转换
	data := event.Data()
	assert.Equal(t, role.ID, data["role_id"])
	assert.Equal(t, role.Name, data["role_name"])
	assert.Equal(t, role.Type, data["role_type"])
	assert.Equal(t, role.SiteID, data["site_id"])
	assert.Equal(t, role.CreatedBy, data["operated_by"])
}

// 测试角色更新事件
func TestRoleUpdatedEvent(t *testing.T) {
	// 准备测试数据
	now := time.Now()
	oldRole := &model.Role{
		ID:          1,
		Name:        "Editor",
		Description: "Content editor role",
		Type:        model.AdminRole,
		Level:       2,
		SiteID:      3,
		UpdatedAt:   now.Add(-24 * time.Hour),
	}

	newRole := &model.Role{
		ID:          1,
		Name:        "Senior Editor",
		Description: "Senior content editor role",
		Type:        model.AdminRole,
		Level:       3,
		SiteID:      3,
		UpdatedAt:   now,
		UpdatedBy:   4,
	}

	// 创建事件
	event := events.NewRoleUpdatedEvent(oldRole, newRole)

	// 验证事件内容
	assert.Equal(t, events.RoleUpdatedEventName, event.Name())
	assert.Equal(t, oldRole, event.OldRole)
	assert.Equal(t, newRole, event.NewRole)
	assert.Equal(t, newRole.ID, event.RoleID)
	assert.Equal(t, oldRole.Name, event.OldRoleName)
	assert.Equal(t, newRole.Name, event.NewRoleName)
	assert.Equal(t, oldRole.Level, event.OldLevel)
	assert.Equal(t, newRole.Level, event.NewLevel)
	assert.Equal(t, newRole.SiteID, event.SiteID)
	assert.Equal(t, newRole.UpdatedBy, event.OperatedBy)

	// 测试事件数据转换
	data := event.Data()
	assert.Equal(t, newRole.ID, data["role_id"])
	assert.Equal(t, oldRole.Name, data["old_role_name"])
	assert.Equal(t, newRole.Name, data["new_role_name"])
	assert.Equal(t, oldRole.Level, data["old_level"])
	assert.Equal(t, newRole.Level, data["new_level"])
	assert.Equal(t, newRole.SiteID, data["site_id"])
	assert.Equal(t, newRole.UpdatedBy, data["operated_by"])
}

// 测试角色删除事件
func TestRoleDeletedEvent(t *testing.T) {
	// 准备测试数据
	now := time.Now()
	role := &model.Role{
		ID:        1,
		Name:      "Temporary",
		Type:      model.AdminRole,
		SiteID:    2,
		DeletedAt: &now,
		DeletedBy: 3,
	}

	// 创建事件
	event := events.NewRoleDeletedEvent(role)

	// 验证事件内容
	assert.Equal(t, events.RoleDeletedEventName, event.Name())
	assert.Equal(t, role, event.Role)
	assert.Equal(t, role.ID, event.RoleID)
	assert.Equal(t, role.Name, event.RoleName)
	assert.Equal(t, role.Type, event.RoleType)
	assert.Equal(t, role.SiteID, event.SiteID)
	assert.Equal(t, role.DeletedBy, event.OperatedBy)

	// 测试事件数据转换
	data := event.Data()
	assert.Equal(t, role.ID, data["role_id"])
	assert.Equal(t, role.Name, data["role_name"])
	assert.Equal(t, role.Type, data["role_type"])
	assert.Equal(t, role.SiteID, data["site_id"])
	assert.Equal(t, role.DeletedBy, data["operated_by"])
}

// 测试角色权限变更事件
func TestRolePermissionsChangedEvent(t *testing.T) {
	// 准备测试数据
	role := &model.Role{
		ID:     1,
		Name:   "Editor",
		Type:   model.AdminRole,
		SiteID: 2,
	}

	oldPermissions := []*model.Permission{
		{ID: 1, Slug: "content.view"},
		{ID: 2, Slug: "content.edit"},
	}

	newPermissions := []*model.Permission{
		{ID: 1, Slug: "content.view"},
		{ID: 2, Slug: "content.edit"},
		{ID: 3, Slug: "content.create"},
		{ID: 4, Slug: "content.delete"},
	}

	operatedBy := uint(3)

	// 创建事件
	event := events.NewRolePermissionsChangedEvent(role, oldPermissions, newPermissions, operatedBy)

	// 验证事件内容
	assert.Equal(t, events.RolePermissionsChangedEventName, event.Name())
	assert.Equal(t, role, event.Role)
	assert.Equal(t, oldPermissions, event.OldPermissions)
	assert.Equal(t, newPermissions, event.NewPermissions)
	assert.Equal(t, role.ID, event.RoleID)
	assert.Equal(t, role.Name, event.RoleName)
	assert.Equal(t, role.SiteID, event.SiteID)
	assert.Equal(t, operatedBy, event.OperatedBy)

	// 验证权限ID列表
	oldIDs := []uint{1, 2}
	newIDs := []uint{1, 2, 3, 4}

	assert.ElementsMatch(t, oldIDs, event.OldPermissionIDs)
	assert.ElementsMatch(t, newIDs, event.NewPermissionIDs)

	// 验证新增的权限
	addedPermissions := []*model.Permission{
		{ID: 3, Slug: "content.create"},
		{ID: 4, Slug: "content.delete"},
	}
	assert.Len(t, event.AddedPermissions, 2)
	assert.ElementsMatch(t, []uint{3, 4}, event.AddedPermissionIDs)
	for i, p := range addedPermissions {
		assert.Equal(t, p.ID, event.AddedPermissions[i].ID)
		assert.Equal(t, p.Slug, event.AddedPermissions[i].Slug)
	}

	// 验证移除的权限
	assert.Len(t, event.RemovedPermissions, 0)
	assert.Len(t, event.RemovedPermissionIDs, 0)

	// 测试事件数据转换
	data := event.Data()
	assert.Equal(t, role.ID, data["role_id"])
	assert.Equal(t, role.Name, data["role_name"])
	assert.Equal(t, role.SiteID, data["site_id"])
	assert.Equal(t, operatedBy, data["operated_by"])
	assert.ElementsMatch(t, oldIDs, data["old_permission_ids"].([]uint))
	assert.ElementsMatch(t, newIDs, data["new_permission_ids"].([]uint))
	assert.ElementsMatch(t, []uint{3, 4}, data["added_permission_ids"].([]uint))
	assert.ElementsMatch(t, []uint{}, data["removed_permission_ids"].([]uint))
}

// 测试用户角色变更事件
func TestUserRoleChangedEvent(t *testing.T) {
	// 准备测试数据
	userID := uint(1)
	userType := model.AdminUser
	siteID := uint(2)
	operatedBy := uint(3)

	oldRoles := []*model.Role{
		{ID: 1, Name: "Editor", Type: model.AdminRole},
	}

	newRoles := []*model.Role{
		{ID: 1, Name: "Editor", Type: model.AdminRole},
		{ID: 2, Name: "Manager", Type: model.AdminRole},
	}

	// 创建事件
	event := events.NewUserRoleChangedEvent(userID, userType, siteID, oldRoles, newRoles, operatedBy)

	// 验证事件内容
	assert.Equal(t, events.UserRoleChangedEventName, event.Name())
	assert.Equal(t, userID, event.UserID)
	assert.Equal(t, userType, event.UserType)
	assert.Equal(t, siteID, event.SiteID)
	assert.Equal(t, oldRoles, event.OldRoles)
	assert.Equal(t, newRoles, event.NewRoles)
	assert.Equal(t, operatedBy, event.OperatedBy)

	// 验证角色ID列表
	oldIDs := []uint{1}
	newIDs := []uint{1, 2}

	assert.ElementsMatch(t, oldIDs, event.OldRoleIDs)
	assert.ElementsMatch(t, newIDs, event.NewRoleIDs)

	// 验证新增的角色
	addedRoles := []*model.Role{
		{ID: 2, Name: "Manager", Type: model.AdminRole},
	}
	assert.Len(t, event.AddedRoles, 1)
	assert.ElementsMatch(t, []uint{2}, event.AddedRoleIDs)
	assert.Equal(t, addedRoles[0].ID, event.AddedRoles[0].ID)
	assert.Equal(t, addedRoles[0].Name, event.AddedRoles[0].Name)

	// 验证移除的角色
	assert.Len(t, event.RemovedRoles, 0)
	assert.Len(t, event.RemovedRoleIDs, 0)

	// 测试事件数据转换
	data := event.Data()
	assert.Equal(t, userID, data["user_id"])
	assert.Equal(t, userType, data["user_type"])
	assert.Equal(t, siteID, data["site_id"])
	assert.Equal(t, operatedBy, data["operated_by"])
	assert.ElementsMatch(t, oldIDs, data["old_role_ids"].([]uint))
	assert.ElementsMatch(t, newIDs, data["new_role_ids"].([]uint))
	assert.ElementsMatch(t, []uint{2}, data["added_role_ids"].([]uint))
	assert.ElementsMatch(t, []uint{}, data["removed_role_ids"].([]uint))
}