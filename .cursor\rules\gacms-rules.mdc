---
description: 
globs: 
alwaysApply: true
---
---
description: "GACMS项目开发需要遵循的基本原则和规范"
globs: ["**/*"]
alwaysApply: true
---

# 亘安内容管理系统（GACMS）开发规范

## 项目信息
- **项目名称**：亘安内容管理系统（GACMS）  
- **项目代号**：GACMS  
- **负责人**：Clion Nieh  
- **开发环境**：Windows 11  
- **生产环境**：Linux
- **架构设计**：完全前后端分离，后端仅通过API提供数据  

## 通用原则
### 设计原则
```yaml
核心原则：
  - 开闭原则：对扩展开放，对修改关闭
  - 单一职责：每个模块/组件/函数/方法都专注单一功能
  - 模块化原则：功能边界清晰，高内聚低耦合
  - 扩展性原则：通过标准接口支持灵活扩展
  - 隔离性原则：租户/模块/插件资源隔离
  - 最小化原则：按需加载，资源高效利用
  - 显式声明原则：依赖/权限/配置显式声明
  - 渐进增强原则：核心精简，按需增强复杂度

ISP: "接口隔离原则：为每个功能定义独立接口，避免'胖接口'"
DIP: "依赖倒置原则：控制器应依赖接口而非具体实现"
LSP: "里氏替换原则：子类可以替换父类"
LoD: "迪米特法则：对象间保持最小了解，避免高耦合"
```

### 开发实践
```yaml
DDD: "文档驱动开发：所有开发基于书面规范"
AT: "自动化测试：开发过程必备自动化测试"
VC: "版本控制：不使用URL或目录区分版本，只通过release控制软件版本"
CR: "代码重构：持续进行代码重构"
CO: "代码优化：持续进行代码优化"
CPD: "契约优先开发：先定义接口再实现"
MPD: "模型优先原则：先定义领域模型再实现业务"
约定优于配置：通过目录结构约定职责
显式优于隐式：依赖关系明确声明
```

### 代码质量
```yaml
CS: "代码规范：严格遵守规范"
CC: "代码注释：只使用英文注释"
CR: "代码可读性：确保代码易读"
CM: "代码可维护性：确保代码易维护"
CE: "代码可扩展性：确保代码易扩展"
CT: "代码可测试性：确保代码易测试"
CP: "代码可移植性：确保代码可移植"
CD: "代码可调试性：确保代码易调试"
```

### 系统设计原则
```yaml
微核心+模块化平台设计，模块功能完整独立，共享核心能力，文件结构独立
多站点支持设计，前后台统一认证，独立用户管理设计
LAP: "分层架构：每层只处理职责相关逻辑"
DDD: "领域驱动设计：业务逻辑与领域模型分离"
松耦合高内聚: "层间通过接口通信，组件功能高度相关"
统一响应格式: "所有接口返回相同数据格式"
全局异常处理: "捕获并处理所有异常"
统一日志管理: "使用文件日志而非数据库日志"
多语言支持: "URL中不使用语言标识"
API版本: "始终使用最新API，不在URL中使用版本，不用目录控制版本，不同时存在多个版本"
用户管理: "前后台用户及各站点用户需要完全隔离的管理能力，包括模块的设计、数据库的设计等都应该物理隔离"
```

## 通用规范
### 代码风格
```yaml
缩进: "2个空格"
命名:
  变量: "小驼峰命名法"
  函数: "小驼峰命名法"
  类: "大驼峰命名法（与文件名一致）"
  常量: "全大写"
  路径: "小驼峰命名法"
  文件: "大驼峰命名法（优先增加目录层级而非使用下划线）"
  命名空间: "大驼峰命名法"
```

### 注释规范
```yaml
语言: "只使用英文注释"
函数注释:
  - "@param: 输入参数"
  - "@return: 输出参数"
  - "@throws: 异常说明"
类注释: "@class"
方法注释: "@method"
变量注释: "@var"
常量注释: "@const"
```

## 技术规范
### 前端技术栈
```yaml
框架: "React"
组件库: "Ant Design"
状态管理: "Redux"
路由: "React Router"
国际化: "i18n"
表单验证: "Formik"
图表: "ECharts"
编辑器: "TinyMCE"
文件上传:
  图片: "Cloudinary"
  视频: "Vimeo"
  音频: "SoundCloud"
  文档: "Google Drive"
数据可视化: "D3.js"
```

### 后端技术栈
```yaml
框架: "Gin"
数据库: "MySQL"
缓存: "Redis"
负载均衡: "Nginx"
容器化: "Docker"
部署: "Kubernetes"
监控: "Prometheus"
```

## 约束规则
1. **版权信息**：所有文件必须添加以下头部注释：
```go
/*
 * @Author: Clion Nieh <<EMAIL>>
 * @Date: {current_date}
 * @LastEditors: Clion Nieh
 * @LastEditTime: {current_date}
 * @FilePath: {file_path}
 * @Description: {file_description}
 * 
 * © {current_year} GACMS. All rights reserved.
 */
```
> `current_date` 和 `current_year` 由 MCP 动态生成

2. **Agent 工作模式**：
```yaml
时间管理: "始终调用MCP获取当前时间"
决策过程: 
  - "调用MCP进行深度思考"
  - "搜索网络资源验证假设"
  - "发散思维多方案评估"
  - "每步决策后反思并记录依据"
任务管理:
  - "调用MCP读取操作记录和任务进度"
  - "修改后记录到长期记忆"
  - "确保多次操作的一致性和连贯性"
执行原则:
  - "功能模块修改或创建前应该首先检查系统设计与原型设计的要求和规范"
  - "用最少步骤完成任务"
  - "避免过度拆分任务"
  - "始终通过MCP计划、分解、执行、记录"
  - "通过MCP进行信息反馈与交互"
  - "文件操作总是成功的，不必重试和确认"
```

3. **设计约束**：
```yaml
模块化: "一个模块只负责一项功能"
文件命名: 
  - "保持简洁（如users而非users_list）"
  - "不使用下划线，优先增加路径层级"
多站点支持: "所有逻辑需支持多站点内容管理"
术语统一: "文章相关统一使用post而非article"
依赖注入: "所有逻辑遵循依赖注入原则"
通信规范: "层间通信始终使用interface"
API版本: "始终使用最新API，不维护多版本"
```

## 执行检查项
1. [ ] 所有文件包含动态生成的版权信息
2. [ ] 代码符合命名规范和注释标准
3. [ ] 层间通信通过interface实现
4. [ ] 异常处理全局统一
5. [ ] 模块满足单一职责原则
6. [ ] 多站点支持逻辑已实现
7. [ ] 无API版本控制痕迹
8. [ ] 所有决策通过MCP记录和验证