/*
 * @Author: <PERSON><PERSON>eh <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: pkg/contract/EventSerializer.go
 * @Description: 定义事件序列化接口，用于事件的持久化和传输
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

// EventSerializer 定义了事件序列化器的接口
// 负责在事件对象和字节数组之间进行转换
type EventSerializer interface {
	// Serialize 将事件序列化为字节数组
	// 返回序列化后的字节数组和可能的错误
	Serialize(event Event) ([]byte, error)

	// Deserialize 将字节数组反序列化为事件
	// 需要提供事件类型名称以确定目标类型
	Deserialize(data []byte, eventType EventName) (Event, error)

	// RegisterEventType 注册事件类型
	// 将事件类型名称与事件类型的构造函数关联
	RegisterEventType(eventType EventName, factory func() Event)
}

// EventSerializerFactory 定义了创建EventSerializer实例的工厂接口
type EventSerializerFactory interface {
	// CreateEventSerializer 创建一个新的EventSerializer实例
	CreateEventSerializer() EventSerializer
}

// SerializationFormat 定义了序列化格式的类型
type SerializationFormat string

const (
	// JSON 表示JSON序列化格式
	JSON SerializationFormat = "json"

	// ProtoBuf 表示Protocol Buffers序列化格式
	ProtoBuf SerializationFormat = "protobuf"

	// MsgPack 表示MessagePack序列化格式
	MsgPack SerializationFormat = "msgpack"
)

// FormatAwareEventSerializer 定义了支持多种序列化格式的事件序列化器接口
type FormatAwareEventSerializer interface {
	EventSerializer

	// SerializeWithFormat 使用指定格式将事件序列化为字节数组
	SerializeWithFormat(event Event, format SerializationFormat) ([]byte, error)

	// DeserializeWithFormat 使用指定格式将字节数组反序列化为事件
	DeserializeWithFormat(data []byte, eventType EventName, format SerializationFormat) (Event, error)

	// GetSupportedFormats 获取支持的序列化格式列表
	GetSupportedFormats() []SerializationFormat
} 