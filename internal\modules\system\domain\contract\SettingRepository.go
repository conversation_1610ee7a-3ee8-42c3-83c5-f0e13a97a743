/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/system/domain/contract/SettingRepository.go
 * @Description: Defines the repository contract for system settings.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package contract

import "gacms/internal/modules/system/domain/model"

// SettingRepository defines the persistence operations for settings.
type SettingRepository interface {
	// Get retrieves a setting by its group and key, for a specific site or globally.
	// siteID = 0 means platform-level setting.
	Get(siteID *uint, group, key string) (*model.Setting, error)

	// GetByGroup retrieves all settings for a specific group, for a specific site or globally.
	GetByGroup(siteID *uint, group string) ([]*model.Setting, error)

	// Create creates a new setting.
	Create(setting *model.Setting) error

	// Update updates an existing setting.
	Update(setting *model.Setting) error

	// Upsert creates a setting if it doesn't exist, or updates it if it does.
	Upsert(setting *model.Setting) error
} 