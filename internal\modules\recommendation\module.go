/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/recommendation/module.go
 * @Description: Defines the recommendation module for dependency injection.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package recommendation

import (
	"gacms/internal/modules/recommendation/application/service"
	"gacms/internal/modules/recommendation/domain/contract"
	"gacms/internal/modules/recommendation/infrastructure/strategy"
	"gacms/internal/modules/recommendation/port/http/controller"

	"go.uber.org/fx"
)

var Module = fx.Options(
	// Provide services and controllers
	fx.Provide(
		service.NewRecommendationService,
		controller.NewRecommendationController,
	),

	// Provide the concrete strategy and bind it to the interface
	fx.Provide(
		fx.Annotate(
			strategy.NewTagBasedStrategy,
			fx.As(new(contract.RecommendationStrategy)),
		),
	),
) 