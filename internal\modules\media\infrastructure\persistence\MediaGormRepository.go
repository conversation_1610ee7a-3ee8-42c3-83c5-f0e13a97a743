/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/media/infrastructure/persistence/MediaGormRepository.go
 * @Description: GORM implementation of the media repository, with multi-tenancy support.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package persistence

import (
	"context"
	"gacms/internal/modules/media/domain/contract"
	"gacms/internal/modules/media/domain/model"
	dbContract "gacms/pkg/contract"
)

type gormMediaRepository struct {
	dbService dbContract.Database
}

func NewMediaGormRepository(dbService dbContract.Database) contract.MediaRepository {
	return &gormMediaRepository{dbService: dbService}
}

func (r *gormMediaRepository) Create(ctx context.Context, media *model.Media) error {
	return r.dbService.DB(ctx).Create(media).Error
}

func (r *gormMediaRepository) GetByID(ctx context.Context, id uint) (*model.Media, error) {
	var media model.Media
	err := r.dbService.DB(ctx).First(&media, id).Error
	return &media, err
}

func (r *gormMediaRepository) List(ctx context.Context, page int, pageSize int, filters map[string]interface{}) ([]*model.Media, int64, error) {
	var mediaList []*model.Media
	var total int64

	query := r.dbService.DB(ctx).Model(&model.Media{})
	if filters != nil {
		query = query.Where(filters)
	}

	query.Count(&total)

	offset := (page - 1) * pageSize
	err := query.Offset(offset).Limit(pageSize).Order("created_at desc").Find(&mediaList).Error

	return mediaList, total, err
}

func (r *gormMediaRepository) Delete(ctx context.Context, id uint) error {
	return r.dbService.DB(ctx).Delete(&model.Media{}, id).Error
}

func (r *gormMediaRepository) Update(ctx context.Context, media *model.Media) error {
	return r.dbService.DB(ctx).Save(media).Error
} 