# GACMS 品牌识别规范

## 1. LOGO 设计

### 1.1 设计理念

GACMS 的 LOGO 设计旨在体现其作为一款**高效、安全、可扩展、现代化**的内容管理系统的核心特性，同时融入“亘安”的中文寓意——永恒的连接与坚固的守护。

LOGO 主体将采用抽象几何图形，灵感来源于以下几个方面：

*   **连接与无限 (亘):** 通过流畅的线条或交织的元素，象征GACMS连接不同内容、平台和用户的能力，以及其持续发展的无限可能性。
*   **守护与安全 (安):** 图形结构将体现稳固和保护感，呼应GACMS对数据安全和系统稳定性的承诺。
*   **科技与效率:** 采用简洁、锐利的线条和现代几何形态，传递科技感和高效能的印象。
*   **模块化与扩展性:** 图形中可能包含可组合或可延伸的元素，暗示GACMS的模块化架构和强大的扩展能力。

整体风格追求**简约、现代、科技感**，易于识别和记忆，并能在不同尺寸和媒介上保持清晰的视觉效果。

### 2.1. LOGO 图形方案

根据用户反馈和进一步的优化，我们提出了以下核心的 LOGO 图形设计概念方案：

**新LOGO概念方案 V2：“G-Nexus Core”**

*   **设计理念：**
    *   旨在体现GACMS作为内容管理系统的核心地位，连接各个内容节点，并展现其现代化、科技感和专业可靠的特性。
    *   “Nexus”意为连接点、核心，强调系统的整合与枢纽作用。
    *   “Core”再次突出其核心引擎的定位。

*   **图形构成：**
    *   **主图形：** 以GACMS的首字母“G”为基础进行抽象和变形。字母“G”的开口部分向内旋转、汇聚，形成一个动态的、向心的视觉焦点。这个汇聚的形态象征着GACMS将分散的内容和功能聚合为一体，是系统的核心引擎。
    *   **核心元素：** 在“G”图形汇聚的中心，设想一个小的、明亮的几何图形（例如一个 estilized 的六边形或一个发光的圆点）。这个元素代表“数据”、“智能”、“创新”或“激活的能量”，是GACMS驱动内容价值的核心动力。
    *   **整体风格：** 线条力求简洁、流畅、现代，可以带有轻微的扁平化拟物或光影效果以增强科技感和质感，但避免过度复杂。

*   **颜色方案建议：**
    *   **主色调 (Primary):**
        *   **科技蓝 (Tech Blue):** 例如 `#0A7AFF` (近似 iOS 蓝) 或 Tailwind CSS 的 `blue-600` (`#2563EB`)。代表专业、可靠、稳定和科技感。
    *   **辅助色/点缀色 (Accent/Highlight):**
        *   **活力青 (Vibrant Teal/Green):** 例如 `#30D158` (近似 iOS 绿) 或 Tailwind CSS 的 `teal-500` (`#14B8A6`) 或 `green-500` (`#22C55E`)。用于核心发光元素或渐变，增加活力、创新和现代感。
        *   **亮银灰 (Light Silver/Gray):** 例如 `#E5E7EB` (Tailwind `gray-200`) 或 `#D1D5DB` (Tailwind `gray-300`)。可用于LOGO在深色背景下的纯色版本，或作为辅助线条、高光。
    *   **背景适应性：**
        *   **浅色背景：** 主要使用科技蓝作为主图形颜色，活力青/绿作为点缀。
        *   **深色背景：** 主图形可使用亮银灰或白色版本，或者科技蓝本身在某些深色背景下也有足够的对比度。核心发光元素保持明亮。
    *   **渐变 (Optional):**
        *   可以考虑在主图形或核心元素上使用从科技蓝到活力青/绿的平滑渐变，以增强视觉的丰富性和现代感。

*   **字体搭配 (LOGO文字标识部分，如果包含文字):**
    *   选用现代、简洁、易读的无衬线字体，例如 Montserrat, Open Sans, Lato, Inter 或类似的开源字体。字母“GACMS”或“亘安”将与图形部分协调搭配。

*   **应用效果预期：**
    *   该LOGO设计旨在在各种尺寸和媒介上（PC网站、移动端应用图标、微信公众号头像、小程序图标等）均具有良好的识别度和视觉效果。
    *   整体上传递出专业、创新、高效、安全可靠的品牌形象。

**(注意：以上为设计概念描述，具体的视觉实现将通过SVG草图和后续细化完成。)**

#### LOGO 方案：科技G盾

*   **设计理念:** 融合 GACMS 的首字母“G”与盾牌的意象，传递安全、可靠、专业、现代的品牌特性。盾牌象征守护与安全，抽象化的“G”字母融入其中，增强品牌识别性，同时通过凌厉的线条和现代的渐变色体现科技感与高效性。
*   **设计说明:** LOGO 主体为一个风格化的盾牌轮廓，内部巧妙地融入了抽象变形的字母“G”。盾牌采用现代、硬朗的线条勾勒，而非传统的古典盾牌样式。字母“G”的设计简洁有力，与盾牌形态自然结合。整体色彩采用科技蓝和青蓝色的渐变，营造出深度和光泽感，辅以细微的阴影效果增加立体感。一个小的动态光点装饰，寓意创新与活力。
*   **关键词:** 安全、可靠、科技、现代、专业、守护、高效、创新。
*   **SVG 实现 (位于 `design/prototypes/assets/images/logo.svg`):**
    ```svg
    <!--
      @file: logo.svg
      @description: GACMS 优化后的 LOGO 设计 - 科技G盾方案
      @author: Cion Nieh
      @email: <EMAIL>
      @copyright: Copyright (c) 2025 Cion Nieh
    -->
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 120" width="120" height="120">
      <defs>
        <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#0A58CA;stop-opacity:1" /> <!-- 深科技蓝 -->
          <stop offset="100%" style="stop-color:#0DCAF0;stop-opacity:1" /> <!-- 亮青蓝 -->
        </linearGradient>
        <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
          <feGaussianBlur in="SourceAlpha" stdDeviation="2"/>
          <feOffset dx="2" dy="2" result="offsetblur"/>
          <feComponentTransfer>
            <feFuncA type="linear" slope="0.5"/>
          </feComponentTransfer>
          <feMerge>
            <feMergeNode/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>

      <!-- 盾牌背景 -->
      <path fill="url(#shieldGradient)" filter="url(#dropShadow)" d="M60,5 C95,5 115,30 115,60 C115,90 95,115 60,115 C25,115 5,90 5,60 C5,30 25,5 60,5 Z M60,10 C28.2,10 10,33.2 10,60 C10,86.8 28.2,110 60,110 C91.8,110 110,86.8 110,60 C110,33.2 91.8,10 60,10 Z"/>

      <!-- 字母 G 的抽象形态 -->
      <path fill="#FFFFFF" d="M85,40 C85,26.2 73.8,15 60,15 C46.2,15 35,26.2 35,40 L35,80 C35,93.8 46.2,105 60,105 C73.8,105 85,93.8 85,80 L85,65 L60,65 L60,80 C60,85.5 55.5,90 50,90 C44.5,90 40,85.5 40,80 L40,40 C40,34.5 44.5,30 50,30 C55.5,30 60,34.5 60,40 L60,55 L85,55 L85,40 Z"/>

      <!-- 可选：小的装饰性元素或高光 -->
      <circle cx="60" cy="30" r="3" fill="#FFFFFF" opacity="0.7">
        <animate attributeName="opacity" values="0.7;0.3;0.7" dur="2s" repeatCount="indefinite" />
      </circle>
    </svg>
    ```

### 2.2. LOGO 方案选择建议

**状态：重新设计中**

*   **说明：**
    *   根据用户最新反馈，之前的LOGO方案未能满足期望。
    *   目前正在基于GACMS项目定位（现代、简洁、科技感、专业、可靠）以及目标平台（Web PC端、Web 移动端、微信公众号、微信小程序）的要求，重新构思和设计新的LOGO方案。
    *   新的设计将重点关注图形的原创性、美感，并确保在不同背景和尺寸下的良好显示效果。
    *   后续将提供新的LOGO概念方案供用户选择。

### 1.3 LOGO 文字标识

*   **主标识:** GACMS
*   **辅助中文标识 (可选):** 亘安
*   字体将选用现代、简洁、易读的无衬线字体，例如 Montserrat, Open Sans, Lato 或类似的开源字体。
*   字母间距和行距将经过精心调整，以确保最佳的视觉平衡和可读性。

## 2. 颜色规范

### 2.1 主色调

*   **科技蓝 (Primary Blue):**
    *   HEX: `#007BFF` (示例)
    *   RGB: `0, 123, 255`
    *   CMYK: `100, 52, 0, 0`
    *   寓意：科技、专业、信任、稳定、高效。作为LOGO和品牌视觉的主要识别色。

### 2.2 辅助色

*   **深灰蓝 (Dark Blue-Gray):**
    *   HEX: `#343A40` (示例)
    *   RGB: `52, 58, 64`
    *   CMYK: `67, 56, 50, 75`
    *   寓意：沉稳、可靠、专业。用于文字、背景或辅助图形元素，增强对比度和层次感。
*   **活力青 (Vibrant Cyan/Teal):**
    *   HEX: `#17A2B8` (示例) 或 `#20C997` (示例)
    *   RGB: `23, 162, 184` 或 `32, 201, 151`
    *   CMYK: `87, 12, 0, 28` 或 `84, 0, 25, 21`
    *   寓意：创新、活力、增长。用于强调特定元素、行动号召或作为点缀色，增加视觉的现代感和吸引力。
*   **浅灰色 (Light Gray):**
    *   HEX: `#F8F9FA` (示例)
    *   RGB: `248, 249, 250`
    *   CMYK: `1, 0, 0, 2`
    *   寓意：简洁、干净、背景。用于背景或次要文字，提供清晰的阅读体验。

### 2.3 渐变色 (可选)

*   可以考虑在某些场景下使用从主色调科技蓝到活力青的平滑渐变，以增加LOGO的现代感和视觉丰富性。

## 3. 字体规范

### 3.1 LOGO 字体

*   **GACMS (主英文标识):**
    *   字体家族: Montserrat (或其他选定的现代无衬线字体)
    *   字重: Medium 或 SemiBold
    *   特点: 几何感强，现代，清晰易读。

*   **亘安 (辅助中文标识):**
    *   字体家族: 思源黑体 (Source Han Sans CN) 或类似的现代无衬线中文字体
    *   字重: Regular 或 Medium
    *   特点: 简洁大方，与英文字体风格协调。

### 3.2 品牌标准字体 (用于网站、宣传材料等)

*   **英文:** Open Sans 或 Lato
    *   字重: Regular (正文), SemiBold (标题), Bold (强调)
    *   特点: 高度可读，适用于屏幕和印刷，拥有多种字重选择。
*   **中文:** 思源黑体 (Source Han Sans CN)
    *   字重: Regular (正文), Medium (标题), Bold (强调)
    *   特点: 现代、清晰，适合各种应用场景。

## 4. LOGO 应用规范

### 4.1 标准组合

*   图形LOGO + GACMS 文字标识 (首选)
*   图形LOGO + GACMS 文字标识 + 亘安中文标识 (次选，用于特定市场或强调中文品牌)
*   仅图形LOGO (用于空间有限或作为图标使用)
*   仅 GACMS 文字标识 (用于纯文本环境)

### 4.2 最小尺寸与安全空间

*   规定LOGO在不同应用中的最小显示尺寸，确保清晰可辨。
*   规定LOGO周围必须保留的最小安全空间（留白区域），避免与其他元素过于接近而影响视觉效果。

### 4.3 背景颜色使用

*   **优先背景:** 白色或浅灰色背景。
*   **深色背景:** 在深色背景上使用时，LOGO文字部分应使用反白处理（如浅灰色或白色）。
*   **复杂背景:** 避免在过于复杂或与LOGO颜色冲突的背景上使用LOGO。如果必须使用，应考虑为LOGO添加纯色描边或背景板以保证其清晰度。

### 4.4 错误使用示例

*   禁止拉伸、压缩或不成比例地缩放LOGO。
*   禁止修改LOGO的颜色组合（除单色或反白应用外）。
*   禁止在LOGO上添加额外的阴影、发光等效果（除非有特定设计需求并经过审核）。
*   禁止将LOGO与其他图形或文字过于紧密地组合，影响其独立性。

## 5. 视觉风格关键词

*   现代 (Modern)
*   简洁 (Minimalist)
*   科技 (Tech-savvy)
*   专业 (Professional)
*   可靠 (Reliable)
*   高效 (Efficient)
*   灵活 (Flexible)
*   安全 (Secure)

*(此文档为初步规范，LOGO图形的SVG实现将在后续步骤中完成)*