<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

内容分析页面 - 用于分析网站内容数据和文章表现
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 内容分析</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <!-- 引入图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment"></script>
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .stat-card {
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-3px);
        }

        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }

        .progress-ring-container {
            position: relative;
            display: inline-block;
            width: 120px;
            height: 120px;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="data_stats.html" class="hover:text-white">数据报告</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">内容分析</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">内容分析</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="exportBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <i class="fas fa-download mr-2"></i>
                            导出报告
                        </button>
                        <button class="flex items-center justify-center bg-gradient-to-r from-gray-600 to-gray-700 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg relative overflow-hidden action-button">
                            <i class="fas fa-cog mr-2"></i>
                            设置
                        </button>
                    </div>
                </div>
            </div>

            <!-- 日期和内容类型筛选 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap gap-4 items-center">
                    <div class="flex items-center">
                        <span class="text-gray-400 mr-2">时间范围:</span>
                        <div class="relative">
                            <select id="dateRange" class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white appearance-none pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="today">今天</option>
                                <option value="yesterday">昨天</option>
                                <option value="7days" selected>最近7天</option>
                                <option value="30days">最近30天</option>
                                <option value="90days">最近90天</option>
                                <option value="custom">自定义</option>
                            </select>
                            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                                <i class="fas fa-chevron-down text-xs"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <span class="text-gray-400 mr-2">内容类型:</span>
                        <div class="relative">
                            <select id="contentType" class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white appearance-none pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="all" selected>所有内容</option>
                                <option value="article">文章</option>
                                <option value="page">页面</option>
                                <option value="product">产品</option>
                                <option value="event">活动</option>
                            </select>
                            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                                <i class="fas fa-chevron-down text-xs"></i>
                            </div>
                        </div>
                    </div>

                    <div id="customDateRange" class="flex items-center space-x-2 hidden">
                        <input type="date" class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="text-gray-400">至</span>
                        <input type="date" class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div class="ml-auto">
                        <button id="refreshBtn" class="flex items-center justify-center bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-all">
                            <i class="fas fa-sync-alt mr-2"></i>
                            刷新数据
                        </button>
                    </div>
                </div>
            </div>

            <!-- 内容概览卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-6">
                <!-- 总内容数 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 stat-card">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-file-alt text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">总内容数</div>
                            <div class="text-xl font-semibold text-white">245</div>
                            <div class="text-xs text-green-400 mt-0.5">
                                <i class="fas fa-arrow-up mr-1"></i>
                                15 篇较上周
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 已发布内容 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 stat-card">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-check-circle text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">已发布内容</div>
                            <div class="text-xl font-semibold text-white">182</div>
                            <div class="text-xs text-green-400 mt-0.5">
                                <i class="fas fa-arrow-up mr-1"></i>
                                9 篇较上周
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 草稿内容 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 stat-card">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-edit text-yellow-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">草稿内容</div>
                            <div class="text-xl font-semibold text-white">63</div>
                            <div class="text-xs text-green-400 mt-0.5">
                                <i class="fas fa-arrow-up mr-1"></i>
                                6 篇较上周
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 平均互动量 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 stat-card">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-comments text-purple-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">平均互动量</div>
                            <div class="text-xl font-semibold text-white">26.4</div>
                            <div class="text-xs text-red-400 mt-0.5">
                                <i class="fas fa-arrow-down mr-1"></i>
                                3.2% 较上周
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 内容发布和互动趋势 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold text-white mb-4">内容发布和互动趋势</h3>
                <div class="chart-container">
                    <canvas id="contentTrendChart"></canvas>
                </div>
            </div>

            <!-- 内容分类分析和热门内容 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- 内容分类分析 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">内容分类分析</h3>
                    <div class="chart-container" style="height: 250px;">
                        <canvas id="contentCategoryChart"></canvas>
                    </div>
                    <div class="grid grid-cols-2 gap-4 mt-4">
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                            <span class="text-sm text-gray-400">文章 (45%)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-purple-500 mr-2"></span>
                            <span class="text-sm text-gray-400">产品 (25%)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                            <span class="text-sm text-gray-400">页面 (18%)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></span>
                            <span class="text-sm text-gray-400">活动 (12%)</span>
                        </div>
                    </div>
                </div>
                
                <!-- 内容长度分布 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">内容长度分布</h3>
                    <div class="chart-container" style="height: 250px;">
                        <canvas id="contentLengthChart"></canvas>
                    </div>
                    <div class="grid grid-cols-2 gap-4 mt-4">
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                            <span class="text-sm text-gray-400">短内容 (<500字) (22%)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                            <span class="text-sm text-gray-400">中内容 (500-2000字) (48%)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></span>
                            <span class="text-sm text-gray-400">长内容 (2000-5000字) (25%)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-purple-500 mr-2"></span>
                            <span class="text-sm text-gray-400">超长内容 (>5000字) (5%)</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 内容质量分析 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold text-white mb-4">内容质量分析</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- SEO评分 -->
                    <div class="bg-gray-800/20 p-5 rounded-xl flex flex-col items-center">
                        <h4 class="text-white text-center mb-2">SEO评分</h4>
                        <div class="progress-ring-container mb-3">
                            <canvas id="seoScoreChart"></canvas>
                            <div class="absolute inset-0 flex items-center justify-center text-2xl font-bold text-blue-500">86</div>
                        </div>
                        <div class="text-sm text-gray-400">优秀</div>
                    </div>
                    
                    <!-- 可读性 -->
                    <div class="bg-gray-800/20 p-5 rounded-xl flex flex-col items-center">
                        <h4 class="text-white text-center mb-2">可读性</h4>
                        <div class="progress-ring-container mb-3">
                            <canvas id="readabilityScoreChart"></canvas>
                            <div class="absolute inset-0 flex items-center justify-center text-2xl font-bold text-green-500">78</div>
                        </div>
                        <div class="text-sm text-gray-400">良好</div>
                    </div>
                    
                    <!-- 媒体丰富度 -->
                    <div class="bg-gray-800/20 p-5 rounded-xl flex flex-col items-center">
                        <h4 class="text-white text-center mb-2">媒体丰富度</h4>
                        <div class="progress-ring-container mb-3">
                            <canvas id="mediaScoreChart"></canvas>
                            <div class="absolute inset-0 flex items-center justify-center text-2xl font-bold text-yellow-500">64</div>
                        </div>
                        <div class="text-sm text-gray-400">一般</div>
                    </div>
                    
                    <!-- 互动活跃度 -->
                    <div class="bg-gray-800/20 p-5 rounded-xl flex flex-col items-center">
                        <h4 class="text-white text-center mb-2">互动活跃度</h4>
                        <div class="progress-ring-container mb-3">
                            <canvas id="engagementScoreChart"></canvas>
                            <div class="absolute inset-0 flex items-center justify-center text-2xl font-bold text-purple-500">92</div>
                        </div>
                        <div class="text-sm text-gray-400">优秀</div>
                    </div>
                </div>
            </div>

            <!-- 热门内容排行 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold text-white mb-4">热门内容排行</h3>
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead class="text-gray-400 text-left">
                            <tr>
                                <th class="pb-4 px-2">内容标题</th>
                                <th class="pb-4 px-2">类型</th>
                                <th class="pb-4 px-2">阅读量</th>
                                <th class="pb-4 px-2">评论数</th>
                                <th class="pb-4 px-2">平均停留时间</th>
                                <th class="pb-4 px-2">转化率</th>
                            </tr>
                        </thead>
                        <tbody class="text-gray-300">
                            <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <i class="fas fa-file-alt text-blue-400 mr-2"></i>
                                        <span>2025年前端开发趋势解析</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">
                                    <span class="px-2 py-1 rounded-md text-xs bg-blue-900/30 text-blue-400 border border-blue-900">文章</span>
                                </td>
                                <td class="py-4 px-2">12,546</td>
                                <td class="py-4 px-2">145</td>
                                <td class="py-4 px-2">5分32秒</td>
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <span class="mr-2">8.2%</span>
                                        <div class="w-16 h-1.5 bg-gray-700 rounded-full overflow-hidden">
                                            <div class="h-full bg-blue-500 rounded-full" style="width: 82%"></div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <i class="fas fa-box text-purple-400 mr-2"></i>
                                        <span>全新旗舰产品发布介绍</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">
                                    <span class="px-2 py-1 rounded-md text-xs bg-purple-900/30 text-purple-400 border border-purple-900">产品</span>
                                </td>
                                <td class="py-4 px-2">9,832</td>
                                <td class="py-4 px-2">98</td>
                                <td class="py-4 px-2">4分14秒</td>
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <span class="mr-2">12.5%</span>
                                        <div class="w-16 h-1.5 bg-gray-700 rounded-full overflow-hidden">
                                            <div class="h-full bg-blue-500 rounded-full" style="width: 100%"></div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <i class="fas fa-file text-green-400 mr-2"></i>
                                        <span>关于我们</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">
                                    <span class="px-2 py-1 rounded-md text-xs bg-green-900/30 text-green-400 border border-green-900">页面</span>
                                </td>
                                <td class="py-4 px-2">8,432</td>
                                <td class="py-4 px-2">12</td>
                                <td class="py-4 px-2">1分53秒</td>
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <span class="mr-2">3.1%</span>
                                        <div class="w-16 h-1.5 bg-gray-700 rounded-full overflow-hidden">
                                            <div class="h-full bg-blue-500 rounded-full" style="width: 31%"></div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <i class="fas fa-calendar-check text-yellow-400 mr-2"></i>
                                        <span>2025年度开发者大会</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">
                                    <span class="px-2 py-1 rounded-md text-xs bg-yellow-900/30 text-yellow-400 border border-yellow-900">活动</span>
                                </td>
                                <td class="py-4 px-2">7,265</td>
                                <td class="py-4 px-2">87</td>
                                <td class="py-4 px-2">3分45秒</td>
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <span class="mr-2">9.8%</span>
                                        <div class="w-16 h-1.5 bg-gray-700 rounded-full overflow-hidden">
                                            <div class="h-full bg-blue-500 rounded-full" style="width: 98%"></div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <i class="fas fa-file-alt text-blue-400 mr-2"></i>
                                        <span>内容营销实战指南</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">
                                    <span class="px-2 py-1 rounded-md text-xs bg-blue-900/30 text-blue-400 border border-blue-900">文章</span>
                                </td>
                                <td class="py-4 px-2">6,876</td>
                                <td class="py-4 px-2">76</td>
                                <td class="py-4 px-2">6分12秒</td>
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <span class="mr-2">7.2%</span>
                                        <div class="w-16 h-1.5 bg-gray-700 rounded-full overflow-hidden">
                                            <div class="h-full bg-blue-500 rounded-full" style="width: 72%"></div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 页面底部版权信息 -->
            <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
                © 2025 GACMS 后台管理系统 - 版本 v1.0.0
            </footer>
        </div>
    </main>

    <!-- JavaScript 导入区域 -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <script>
        /**
         * @function initCharts
         * @description 初始化所有图表
         */
        function initCharts() {
            // 内容发布和互动趋势图表
            const contentTrendCtx = document.getElementById('contentTrendChart').getContext('2d');
            const contentTrendChart = new Chart(contentTrendCtx, {
                type: 'line',
                data: {
                    labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                    datasets: [
                        {
                            label: '发布内容数',
                            data: [5, 8, 12, 6, 9, 4, 7],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.3,
                            fill: true
                        },
                        {
                            label: '互动总量',
                            data: [120, 145, 210, 180, 230, 170, 190],
                            borderColor: '#a855f7',
                            backgroundColor: 'rgba(168, 85, 247, 0.1)',
                            tension: 0.3,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                color: '#e0e0e0'
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#9CA3AF'
                            }
                        },
                        y: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#9CA3AF'
                            }
                        }
                    }
                }
            });

            // 内容分类饼图
            const contentCategoryCtx = document.getElementById('contentCategoryChart').getContext('2d');
            const contentCategoryChart = new Chart(contentCategoryCtx, {
                type: 'doughnut',
                data: {
                    labels: ['文章', '产品', '页面', '活动'],
                    datasets: [{
                        data: [45, 25, 18, 12],
                        backgroundColor: [
                            '#3b82f6',
                            '#a855f7',
                            '#22c55e',
                            '#eab308'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    cutout: '65%'
                }
            });

            // 内容长度分布图
            const contentLengthCtx = document.getElementById('contentLengthChart').getContext('2d');
            const contentLengthChart = new Chart(contentLengthCtx, {
                type: 'doughnut',
                data: {
                    labels: ['短内容 (<500字)', '中内容 (500-2000字)', '长内容 (2000-5000字)', '超长内容 (>5000字)'],
                    datasets: [{
                        data: [22, 48, 25, 5],
                        backgroundColor: [
                            '#3b82f6',
                            '#22c55e',
                            '#eab308',
                            '#a855f7'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    cutout: '65%'
                }
            });

            // 创建环形进度图表
            function createProgressRingChart(elementId, value, color) {
                const ctx = document.getElementById(elementId).getContext('2d');
                const remainderColor = 'rgba(255, 255, 255, 0.1)';
                
                return new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        datasets: [{
                            data: [value, 100-value],
                            backgroundColor: [color, remainderColor],
                            borderWidth: 0,
                            cutout: '80%'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: true,
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                enabled: false
                            }
                        },
                        animation: {
                            animateRotate: true,
                            animateScale: true
                        }
                    }
                });
            }

            // 创建所有环形进度图表
            createProgressRingChart('seoScoreChart', 86, '#3b82f6');
            createProgressRingChart('readabilityScoreChart', 78, '#22c55e');
            createProgressRingChart('mediaScoreChart', 64, '#eab308');
            createProgressRingChart('engagementScoreChart', 92, '#a855f7');
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initCharts();

            // 日期范围选择器逻辑
            const dateRangeSelect = document.getElementById('dateRange');
            const customDateRange = document.getElementById('customDateRange');
            
            if (dateRangeSelect && customDateRange) {
                dateRangeSelect.addEventListener('change', function() {
                    if (this.value === 'custom') {
                        customDateRange.classList.remove('hidden');
                    } else {
                        customDateRange.classList.add('hidden');
                    }
                });
            }

            // 刷新按钮事件
            const refreshBtn = document.getElementById('refreshBtn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function() {
                    // 模拟刷新数据
                    const btn = this;
                    btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> 刷新中...';
                    btn.disabled = true;
                    
                    setTimeout(() => {
                        btn.innerHTML = '<i class="fas fa-sync-alt mr-2"></i> 刷新数据';
                        btn.disabled = false;
                        
                        // 可以在这里重新加载图表数据
                        initCharts();
                    }, 1500);
                });
            }

            // 导出按钮事件
            const exportBtn = document.getElementById('exportBtn');
            if (exportBtn) {
                exportBtn.addEventListener('click', function() {
                    alert('报告导出功能将在此实现');
                });
            }
        });
    </script>
</body>
</html> 