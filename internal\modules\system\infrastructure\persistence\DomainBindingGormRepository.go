/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON> <<EMAIL>>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/system/infrastructure/persistence/DomainBindingGormRepository.go
 * @Description: GORM implementation for the DomainBindingRepository.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package persistence

import (
	"errors"
	"gacms/internal/modules/system/domain/contract"
	"gacms/internal/modules/system/domain/model"
	"gorm.io/gorm"
)

type domainBindingGormRepository struct {
	db *gorm.DB
}

func NewDomainBindingGormRepository(db *gorm.DB) contract.DomainBindingRepository {
	return &domainBindingGormRepository{db: db}
}

func (r *domainBindingGormRepository) Create(binding *model.DomainBinding) error {
	return r.db.Create(binding).Error
}

func (r *domainBindingGormRepository) Delete(id uint) error {
	return r.db.Delete(&model.DomainBinding{}, id).Error
}

func (r *domainBindingGormRepository) GetByID(id uint) (*model.DomainBinding, error) {
	var binding model.DomainBinding
	err := r.db.First(&binding, id).Error
	return &binding, err
}

func (r *domainBindingGormRepository) GetByDomain(domain string) (*model.DomainBinding, error) {
	var binding model.DomainBinding
	err := r.db.Where("domain = ?", domain).First(&binding).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil // Not a fatal error, just not found
	}
	return &binding, err
}

func (r *domainBindingGormRepository) GetByDomainWithRules(domain string) (*model.DomainBinding, error) {
	var binding model.DomainBinding
	err := r.db.Preload("URLRules", "is_active = ?", true).
		Where("domain = ?", domain).
		First(&binding).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil // Not a fatal error, just not found
	}
	return &binding, err
}

func (r *domainBindingGormRepository) ListBySiteID(siteID uint, page, pageSize int) ([]*model.DomainBinding, int64, error) {
	var bindings []*model.DomainBinding
	var total int64
	query := r.db.Model(&model.DomainBinding{}).Where("site_id = ?", siteID)
	
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	err = query.Preload("URLRules").Limit(pageSize).Offset(offset).Find(&bindings).Error

	return bindings, total, err
}

// URL重写规则相关方法

func (r *domainBindingGormRepository) CreateURLRule(rule *model.URLRewriteRule) error {
	return r.db.Create(rule).Error
}

func (r *domainBindingGormRepository) UpdateURLRule(rule *model.URLRewriteRule) error {
	return r.db.Save(rule).Error
}

func (r *domainBindingGormRepository) DeleteURLRule(id uint) error {
	return r.db.Delete(&model.URLRewriteRule{}, id).Error
}

func (r *domainBindingGormRepository) GetURLRulesByDomainBinding(domainBindingID uint) ([]*model.URLRewriteRule, error) {
	var rules []*model.URLRewriteRule
	err := r.db.Where("domain_binding_id = ? AND is_active = ?", domainBindingID, true).
		Order("priority DESC, id ASC").
		Find(&rules).Error
	return rules, err
}