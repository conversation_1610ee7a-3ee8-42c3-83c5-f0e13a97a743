<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 在线客服聊天</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .chat-container {
            height: calc(100vh - 240px);
        }
        
        .chat-sidebar {
            width: 280px;
            border-right: 1px solid rgba(75, 85, 99, 0.3);
        }
        
        .chat-messages {
            flex: 1;
        }
        
        .chat-input {
            border-top: 1px solid rgba(75, 85, 99, 0.3);
        }
        
        .chat-item {
            transition: all 0.2s ease;
        }
        
        .chat-item:hover, .chat-item.active {
            background-color: rgba(75, 85, 99, 0.2);
        }
        
        .message {
            max-width: 80%;
            margin-bottom: 1rem;
            padding: 0.75rem 1rem;
            border-radius: 1rem;
            position: relative;
        }
        
        .message.customer {
            background-color: rgba(75, 85, 99, 0.3);
            border-bottom-left-radius: 0.25rem;
            margin-right: auto;
        }
        
        .message.agent {
            background-color: rgba(59, 130, 246, 0.3);
            border-bottom-right-radius: 0.25rem;
            margin-left: auto;
        }
        
        .typing-indicator span {
            width: 8px;
            height: 8px;
            background-color: rgba(156, 163, 175, 0.7);
            border-radius: 50%;
            display: inline-block;
            margin: 0 1px;
            animation: typing 1.4s infinite both;
        }
        
        .typing-indicator span:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .typing-indicator span:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        @keyframes typing {
            0% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-5px);
            }
            100% {
                transform: translateY(0);
            }
        }
        
        .status-badge {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
        }
        
        .status-online {
            background-color: #10B981;
        }
        
        .status-away {
            background-color: #F59E0B;
        }
        
        .status-offline {
            background-color: #6B7280;
        }
        
        .emoji-picker {
            position: absolute;
            bottom: 60px;
            right: 0;
            width: 300px;
            background-color: #1F2937;
            border: 1px solid #374151;
            border-radius: 0.5rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            z-index: 10;
        }
        
        .emoji-category {
            display: flex;
            border-bottom: 1px solid #374151;
        }
        
        .emoji-category button {
            flex: 1;
            padding: 0.5rem;
            background: none;
            border: none;
            color: #9CA3AF;
            cursor: pointer;
        }
        
        .emoji-category button.active {
            color: #3B82F6;
            border-bottom: 2px solid #3B82F6;
        }
        
        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 0.25rem;
            padding: 0.5rem;
        }
        
        .emoji-item {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            padding: 0.25rem;
            cursor: pointer;
            border-radius: 0.25rem;
        }
        
        .emoji-item:hover {
            background-color: #374151;
        }
        
        /* 快捷回复样式 */
        .quick-reply {
            padding: 0.5rem 1rem;
            background-color: rgba(75, 85, 99, 0.2);
            border-radius: 1rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }
        
        .quick-reply:hover {
            background-color: rgba(59, 130, 246, 0.3);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="customer_service.html" class="hover:text-white">客户服务</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">在线客服聊天</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white">在线客服聊天</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <div class="relative">
                            <select class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="online">在线</option>
                                <option value="away">离开</option>
                                <option value="offline">离线</option>
                            </select>
                            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                                <i class="fas fa-chevron-down text-xs"></i>
                            </div>
                        </div>
                        <button class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30">
                            <i class="fas fa-cog mr-2"></i>
                            设置
                        </button>
                    </div>
                </div>
            </div>

            <!-- 聊天统计卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <!-- 当前在线会话 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-comments text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">当前在线会话</h3>
                            <div class="text-2xl font-bold text-white mt-1">12</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-green-400">
                            <i class="fas fa-arrow-up mr-1"></i>3
                        </span>
                        <span class="text-gray-400 ml-2">较昨日</span>
                    </div>
                </div>
                
                <!-- 平均响应时间 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-clock text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">平均响应时间</h3>
                            <div class="text-2xl font-bold text-white mt-1">45秒</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-green-400">
                            <i class="fas fa-arrow-down mr-1"></i>12秒
                        </span>
                        <span class="text-gray-400 ml-2">较上周</span>
                    </div>
                </div>
                
                <!-- 今日已处理 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-check-circle text-purple-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">今日已处理</h3>
                            <div class="text-2xl font-bold text-white mt-1">78</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-green-400">
                            <i class="fas fa-arrow-up mr-1"></i>15%
                        </span>
                        <span class="text-gray-400 ml-2">较昨日</span>
                    </div>
                </div>
                
                <!-- 客户满意度 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-star text-yellow-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">客户满意度</h3>
                            <div class="text-2xl font-bold text-white mt-1">4.8/5</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-green-400">
                            <i class="fas fa-arrow-up mr-1"></i>0.2
                        </span>
                        <span class="text-gray-400 ml-2">较上月</span>
                    </div>
                </div>
            </div>

            <!-- 聊天主界面 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl mb-6 overflow-hidden">
                <div class="flex h-full chat-container">
                    <!-- 聊天侧边栏 -->
                    <div class="chat-sidebar">
                        <!-- 搜索框 -->
                        <div class="p-4 border-b border-gray-700">
                            <div class="relative">
                                <input type="text" placeholder="搜索客户..." class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <div class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-400">
                                    <i class="fas fa-search"></i>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 聊天列表 -->
                        <div class="overflow-y-auto" style="height: calc(100% - 70px);">
                            <!-- 活跃聊天 -->
                            <div class="p-3 border-b border-gray-700">
                                <h3 class="text-xs font-medium text-gray-400 uppercase mb-2">活跃聊天 (8)</h3>
                                
                                <!-- 聊天项 1 - 当前活跃 -->
                                <div class="chat-item active p-2 rounded-lg mb-2">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80" alt="用户头像" class="w-10 h-10 rounded-full object-cover">
                                            <span class="status-badge status-online absolute bottom-0 right-0 border-2 border-gray-800"></span>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <div class="flex justify-between items-center">
                                                <h4 class="text-sm font-medium text-white">李小华</h4>
                                                <span class="text-xs text-gray-400">刚刚</span>
                                            </div>
                                            <p class="text-xs text-gray-400 truncate">我想咨询一下关于退款的问题...</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 聊天项 2 -->
                                <div class="chat-item p-2 rounded-lg mb-2">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <img src="https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80" alt="用户头像" class="w-10 h-10 rounded-full object-cover">
                                            <span class="status-badge status-online absolute bottom-0 right-0 border-2 border-gray-800"></span>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <div class="flex justify-between items-center">
                                                <h4 class="text-sm font-medium text-white">张明</h4>
                                                <span class="text-xs text-gray-400">2分钟前</span>
                                            </div>
                                            <p class="text-xs text-gray-400 truncate">请问这个商品什么时候发货？</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 聊天项 3 -->
                                <div class="chat-item p-2 rounded-lg mb-2">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80" alt="用户头像" class="w-10 h-10 rounded-full object-cover">
                                            <span class="status-badge status-online absolute bottom-0 right-0 border-2 border-gray-800"></span>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <div class="flex justify-between items-center">
                                                <h4 class="text-sm font-medium text-white">王丽</h4>
                                                <span class="text-xs text-gray-400">5分钟前</span>
                                            </div>
                                            <p class="text-xs text-gray-400 truncate">我需要修改我的订单地址</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 聊天项 4 -->
                                <div class="chat-item p-2 rounded-lg mb-2">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80" alt="用户头像" class="w-10 h-10 rounded-full object-cover">
                                            <span class="status-badge status-online absolute bottom-0 right-0 border-2 border-gray-800"></span>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <div class="flex justify-between items-center">
                                                <h4 class="text-sm font-medium text-white">陈强</h4>
                                                <span class="text-xs text-gray-400">8分钟前</span>
                                            </div>
                                            <p class="text-xs text-gray-400 truncate">这个产品有其他颜色吗？</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 等待中 -->
                            <div class="p-3 border-b border-gray-700">
                                <h3 class="text-xs font-medium text-gray-400 uppercase mb-2">等待中 (3)</h3>
                                
                                <!-- 等待聊天项 1 -->
                                <div class="chat-item p-2 rounded-lg mb-2">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80" alt="用户头像" class="w-10 h-10 rounded-full object-cover">
                                            <span class="status-badge status-away absolute bottom-0 right-0 border-2 border-gray-800"></span>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <div class="flex justify-between items-center">
                                                <h4 class="text-sm font-medium text-white">赵伟</h4>
                                                <span class="text-xs text-gray-400">12分钟前</span>
                                            </div>
                                            <p class="text-xs text-gray-400 truncate">等待客服回复...</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 等待聊天项 2 -->
                                <div class="chat-item p-2 rounded-lg mb-2">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80" alt="用户头像" class="w-10 h-10 rounded-full object-cover">
                                            <span class="status-badge status-away absolute bottom-0 right-0 border-2 border-gray-800"></span>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <div class="flex justify-between items-center">
                                                <h4 class="text-sm font-medium text-white">林小美</h4>
                                                <span class="text-xs text-gray-400">15分钟前</span>
                                            </div>
                                            <p class="text-xs text-gray-400 truncate">等待客服回复...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 最近聊天 -->
                            <div class="p-3">
                                <h3 class="text-xs font-medium text-gray-400 uppercase mb-2">最近聊天</h3>
                                
                                <!-- 最近聊天项 1 -->
                                <div class="chat-item p-2 rounded-lg mb-2">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80" alt="用户头像" class="w-10 h-10 rounded-full object-cover">
                                            <span class="status-badge status-offline absolute bottom-0 right-0 border-2 border-gray-800"></span>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <div class="flex justify-between items-center">
                                                <h4 class="text-sm font-medium text-white">刘洋</h4>
                                                <span class="text-xs text-gray-400">昨天</span>
                                            </div>
                                            <p class="text-xs text-gray-400 truncate">谢谢您的帮助！</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 最近聊天项 2 -->
                                <div class="chat-item p-2 rounded-lg mb-2">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80" alt="用户头像" class="w-10 h-10 rounded-full object-cover">
                                            <span class="status-badge status-offline absolute bottom-0 right-0 border-2 border-gray-800"></span>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <div class="flex justify-between items-center">
                                                <h4 class="text-sm font-medium text-white">周静</h4>
                                                <span class="text-xs text-gray-400">昨天</span>
                                            </div>
                                            <p class="text-xs text-gray-400 truncate">问题已解决，感谢！</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 聊天主区域 -->
                    <div class="chat-messages flex flex-col">
                        <!-- 聊天头部 -->
                        <div class="p-4 border-b border-gray-700 flex justify-between items-center">
                            <div class="flex items-center">
                                <div class="relative">
                                    <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80" alt="用户头像" class="w-10 h-10 rounded-full object-cover">
                                    <span class="status-badge status-online absolute bottom-0 right-0 border-2 border-gray-800"></span>
                                </div>
                                <div class="ml-3">
                                    <h4 class="text-sm font-medium text-white">李小华</h4>
                                    <p class="text-xs text-gray-400">正在输入...</p>
                                </div>
                            </div>
                            <div class="flex space-x-3">
                                <button class="text-gray-400 hover:text-white" title="查看客户资料">
                                    <i class="fas fa-user"></i>
                                </button>
                                <button class="text-gray-400 hover:text-white" title="查看订单历史">
                                    <i class="fas fa-shopping-bag"></i>
                                </button>
                                <button class="text-gray-400 hover:text-white" title="转接会话">
                                    <i class="fas fa-random"></i>
                                </button>
                                <button class="text-gray-400 hover:text-white" title="更多操作">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 聊天消息区域 -->
                        <div class="flex-1 p-4 overflow-y-auto">
                            <!-- 时间分割线 -->
                            <div class="flex justify-center mb-4">
                                <span class="text-xs text-gray-400 bg-gray-800 px-3 py-1 rounded-full">今天 14:30</span>
                            </div>
                            
                            <!-- 客户消息 -->
                            <div class="message customer">
                                <p>您好，我想咨询一下关于我最近购买的产品退款问题</p>
                            </div>
                            
                            <!-- 客服消息 -->
                            <div class="message agent">
                                <p>您好，很高兴为您服务！请问是哪个订单需要退款呢？可以提供一下订单号吗？</p>
                            </div>
                            
                            <!-- 客户消息 -->
                            <div class="message customer">
                                <p>订单号是 GA20250415-7823，我购买的是一件衬衫，但是收到后发现尺寸不合适</p>
                            </div>
                            
                            <!-- 客服消息 -->
                            <div class="message agent">
                                <p>感谢您提供订单信息，我已经查询到您的订单。根据我们的退换货政策，您可以在收到商品的7天内申请退款或换货。</p>
                            </div>
                            
                            <!-- 客户消息 -->
                            <div class="message customer">
                                <p>是的，我是3天前收到的，现在想申请退款</p>
                            </div>
                            
                            <!-- 客服消息 -->
                            <div class="message agent">
                                <p>好的，没有问题。请问您是希望直接退款还是换成合适的尺寸呢？</p>
                            </div>
                            
                            <!-- 客户消息 -->
                            <div class="message customer">
                                <p>我想直接退款，因为我已经在别处购买了合适的尺寸</p>
                            </div>
                            
                            <!-- 客服消息 -->
                            <div class="message agent">
                                <p>明白了。我现在就为您处理退款申请。请问商品的包装还在吗？是否已经使用过？</p>
                            </div>
                            
                            <!-- 客户消息 -->
                            <div class="message customer">
                                <p>包装还在，我只是试穿了一下，没有使用过</p>
                            </div>
                            
                            <!-- 客服消息 -->
                            <div class="message agent">
                                <p>非常好！那么我已经为您创建了退款申请，编号是 RF-20250415-342。您只需要将商品连同原包装一起寄回我们的退货地址：北京市朝阳区建国路88号电子商务产业园3号楼，收件人：GACMS退货部，电话：400-123-4567。</p>
                            </div>
                            
                            <!-- 客户消息 -->
                            <div class="message customer">
                                <p>好的，请问运费需要我自己承担吗？</p>
                            </div>
                            
                            <!-- 客户正在输入 -->
                            <div class="flex items-center mt-4 ml-2">
                                <div class="typing-indicator bg-gray-800/30 px-4 py-2 rounded-full">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 快捷回复区域 -->
                        <div class="px-4 py-2 border-t border-gray-700 overflow-x-auto whitespace-nowrap">
                            <div class="inline-flex">
                                <div class="quick-reply">您好，很高兴为您服务！</div>
                                <div class="quick-reply">请提供您的订单号</div>
                                <div class="quick-reply">感谢您的耐心等待</div>
                                <div class="quick-reply">请问还有其他问题吗？</div>
                                <div class="quick-reply">稍等，我正在查询</div>
                                <div class="quick-reply">已为您处理完成</div>
                                <div class="quick-reply">祝您购物愉快！</div>
                            </div>
                        </div>
                        
                        <!-- 聊天输入区域 -->
                        <div class="chat-input p-4 relative">
                            <div class="flex">
                                <div class="flex space-x-2 mr-2">
                                    <button id="emojiBtn" class="text-gray-400 hover:text-white p-2">
                                        <i class="far fa-smile"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-white p-2">
                                        <i class="fas fa-paperclip"></i>
                                    </button>
                                </div>
                                <div class="flex-1 relative">
                                    <textarea placeholder="输入消息..." class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none" rows="2"></textarea>
                                </div>
                                <div class="flex items-end ml-2">
                                    <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                                        <i class="fas fa-paper-plane mr-1"></i>
                                        发送
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 表情选择器 (默认隐藏) -->
                            <div id="emojiPicker" class="emoji-picker hidden">
                                <div class="emoji-category">
                                    <button class="active"><i class="far fa-smile"></i></button>
                                    <button><i class="far fa-hand-peace"></i></button>
                                    <button><i class="fas fa-utensils"></i></button>
                                    <button><i class="fas fa-car"></i></button>
                                    <button><i class="far fa-bell"></i></button>
                                </div>
                                <div class="emoji-grid">
                                    <div class="emoji-item">😀</div>
                                    <div class="emoji-item">😁</div>
                                    <div class="emoji-item">😂</div>
                                    <div class="emoji-item">🤣</div>
                                    <div class="emoji-item">😃</div>
                                    <div class="emoji-item">😄</div>
                                    <div class="emoji-item">😅</div>
                                    <div class="emoji-item">😆</div>
                                    <div class="emoji-item">😉</div>
                                    <div class="emoji-item">😊</div>
                                    <div class="emoji-item">😋</div>
                                    <div class="emoji-item">😎</div>
                                    <div class="emoji-item">😍</div>
                                    <div class="emoji-item">😘</div>
                                    <div class="emoji-item">🙂</div>
                                    <div class="emoji-item">🤔</div>
                                    <div class="emoji-item">🤨</div>
                                    <div class="emoji-item">😐</div>
                                    <div class="emoji-item">😑</div>
                                    <div class="emoji-item">😶</div>
                                    <div class="emoji-item">🙄</div>
                                    <div class="emoji-item">😏</div>
                                    <div class="emoji-item">😣</div>
                                    <div class="emoji-item">😥</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 页面底部版权信息 -->
            <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
                © 2025 GACMS 后台管理系统 - 版本 v1.0.0
            </footer>
        </div>
    </main>

    <!-- 必要的JS脚本 -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 聊天项点击事件
            const chatItems = document.querySelectorAll('.chat-item');
            
            chatItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 移除所有聊天项的活动状态
                    chatItems.forEach(chat => chat.classList.remove('active'));
                    
                    // 设置当前聊天项为活动状态
                    this.classList.add('active');
                });
            });
            
            // 表情选择器控制
            const emojiBtn = document.getElementById('emojiBtn');
            const emojiPicker = document.getElementById('emojiPicker');
            
            emojiBtn.addEventListener('click', function() {
                emojiPicker.classList.toggle('hidden');
            });
            
            // 点击表情选择器外部关闭
            document.addEventListener('click', function(e) {
                if (!emojiBtn.contains(e.target) && !emojiPicker.contains(e.target)) {
                    emojiPicker.classList.add('hidden');
                }
            });
            
            // 表情类别切换
            const emojiCategoryBtns = document.querySelectorAll('.emoji-category button');
            
            emojiCategoryBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有类别按钮的活动状态
                    emojiCategoryBtns.forEach(b => b.classList.remove('active'));
                    
                    // 设置当前类别按钮为活动状态
                    this.classList.add('active');
                });
            });
            
            // 表情点击事件
            const emojiItems = document.querySelectorAll('.emoji-item');
            const messageInput = document.querySelector('textarea');
            
            emojiItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 将表情添加到输入框
                    messageInput.value += this.textContent;
                    messageInput.focus();
                });
            });
            
            // 快捷回复点击事件
            const quickReplies = document.querySelectorAll('.quick-reply');
            
            quickReplies.forEach(reply => {
                reply.addEventListener('click', function() {
                    // 将快捷回复添加到输入框
                    messageInput.value = this.textContent;
                    messageInput.focus();
                });
            });
        });
    </script>
</body>
</html>