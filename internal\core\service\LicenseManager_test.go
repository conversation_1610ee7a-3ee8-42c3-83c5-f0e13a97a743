/*
 * @Author: <PERSON><PERSON>
 * @Email: <EMAIL>
 * @Date: 2025-01-21 17:00:00
 * @LastEditTime: 2025-01-21 17:00:00
 * @FilePath: internal/core/service/LicenseManager_test.go
 * @Description: 统一许可证管理器测试
 * @Copyright (c) 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"testing"

	"gacms/pkg/contract"

	"go.uber.org/zap"
)

// MockLicenseStore 模拟许可证存储
type MockLicenseStore struct{}

func (m *MockLicenseStore) SaveLicense(ctx context.Context, license *contract.LicenseInfo) error {
	return nil
}

func (m *MockLicenseStore) GetLicense(ctx context.Context, moduleID string) (*contract.LicenseInfo, error) {
	return nil, nil
}

func (m *MockLicenseStore) DeleteLicense(ctx context.Context, moduleID string) error {
	return nil
}

func (m *MockLicenseStore) ListLicenses(ctx context.Context) ([]*contract.LicenseInfo, error) {
	return nil, nil
}

// MockLicenseValidatorRegistry 模拟验证器注册表
type MockLicenseValidatorRegistry struct{}

func (m *MockLicenseValidatorRegistry) RegisterValidator(name string, validator contract.LicenseValidator) error {
	return nil
}

func (m *MockLicenseValidatorRegistry) GetValidator(name string) (contract.LicenseValidator, error) {
	return nil, nil
}

func (m *MockLicenseValidatorRegistry) ListValidators() []string {
	return []string{}
}

// MockEventManager 模拟事件管理器
type MockEventManager struct{}

func (m *MockEventManager) Publish(ctx context.Context, event contract.Event) error {
	return nil
}

func (m *MockEventManager) Subscribe(eventType string, handler contract.EventHandler) error {
	return nil
}

func (m *MockEventManager) RegisterHandler(handler contract.EventHandler) error {
	return nil
}

// MockEditionManager 模拟版本管理器
type MockEditionManager struct{}

func (m *MockEditionManager) CheckFeatureAccess(edition contract.Edition, featureName string) error {
	return nil
}

func (m *MockEditionManager) GetEditionFeatures(edition contract.Edition) []string {
	return []string{}
}

func (m *MockEditionManager) IsFeatureAvailable(edition contract.Edition, featureName string) bool {
	return true
}

// TestLicenseManagerIntegration 测试许可证管理器整合
func TestLicenseManagerIntegration(t *testing.T) {
	// 创建模拟依赖
	mockStore := &MockLicenseStore{}
	mockRegistry := &MockLicenseValidatorRegistry{}
	mockEventManager := &MockEventManager{}
	mockEditionManager := &MockEditionManager{}
	logger := zap.NewNop()

	// 创建许可证管理器
	params := DefaultLicenseManagerParams{
		Registry:       mockRegistry,
		Store:          mockStore,
		EventManager:   mockEventManager,
		ModuleManager:  nil, // 简化测试
		Logger:         logger,
		Config:         nil, // 使用默认配置
		EditionManager: mockEditionManager,
		SystemConfig:   nil, // 使用默认配置
	}

	manager := NewDefaultLicenseManager(params)
	if manager == nil {
		t.Fatal("Failed to create LicenseManager")
	}

	// 测试基本功能
	t.Run("GetLicenseInfo", func(t *testing.T) {
		licenseInfo := manager.GetLicenseInfo()
		if licenseInfo == nil {
			t.Error("GetLicenseInfo returned nil")
			return
		}
		if licenseInfo.ModuleName != "system" {
			t.Errorf("Expected ModuleName 'system', got '%s'", licenseInfo.ModuleName)
		}
		if licenseInfo.Type != "development" {
			t.Errorf("Expected Type 'development', got '%s'", licenseInfo.Type)
		}
		if licenseInfo.Edition != contract.EditionBusiness {
			t.Errorf("Expected Edition '%s', got '%s'", contract.EditionBusiness, licenseInfo.Edition)
		}
		if !licenseInfo.IsValid {
			t.Error("Expected IsValid to be true")
		}
	})

	t.Run("ValidateLicense", func(t *testing.T) {
		ctx := context.Background()
		licenseInfo, err := manager.ValidateLicense(ctx)
		if err != nil {
			t.Errorf("ValidateLicense returned error: %v", err)
			return
		}
		if licenseInfo == nil {
			t.Error("ValidateLicense returned nil")
			return
		}
		if licenseInfo.ModuleName != "system" {
			t.Errorf("Expected ModuleName 'system', got '%s'", licenseInfo.ModuleName)
		}
		if !licenseInfo.IsValid {
			t.Error("Expected IsValid to be true")
		}
	})

	t.Run("IsFeatureAuthorized", func(t *testing.T) {
		ctx := context.Background()

		// 在开发模式下，所有功能都应该被授权
		authorized := manager.IsFeatureAuthorized(ctx, "advanced_seo")
		if !authorized {
			t.Error("Expected advanced_seo to be authorized in development mode")
		}

		authorized = manager.IsFeatureAuthorized(ctx, "enterprise_api")
		if !authorized {
			t.Error("Expected enterprise_api to be authorized in development mode")
		}
	})

	t.Run("RefreshLicense", func(t *testing.T) {
		err := manager.RefreshLicense()
		if err != nil {
			t.Errorf("RefreshLicense returned error: %v", err)
		}
	})

	t.Run("ClearCache", func(t *testing.T) {
		// 应该不会panic
		defer func() {
			if r := recover(); r != nil {
				t.Errorf("ClearCache panicked: %v", r)
			}
		}()
		manager.ClearCache()
	})
}

// TestLicenseServiceIntegration 测试许可证服务整合
func TestLicenseServiceIntegration(t *testing.T) {
	// 创建模拟许可证管理器
	mockLicenseManager := &MockLicenseManager{}
	logger := zap.NewNop()

	// 创建许可证服务
	params := DefaultLicenseServiceParams{
		LicenseManager: mockLicenseManager,
		Logger:         logger,
	}

	service := NewDefaultLicenseService(params)
	if service == nil {
		t.Fatal("Failed to create LicenseService")
	}

	// 测试服务功能
	t.Run("GetSystemInfo", func(t *testing.T) {
		systemInfo := service.GetSystemInfo()
		if systemInfo == nil {
			t.Error("GetSystemInfo returned nil")
			return
		}
		if systemInfo.Edition != contract.EditionBusiness {
			t.Errorf("Expected Edition '%s', got '%s'", contract.EditionBusiness, systemInfo.Edition)
		}
		if !systemInfo.IsValid {
			t.Error("Expected IsValid to be true")
		}
		if systemInfo.MaxTenants != -1 {
			t.Errorf("Expected MaxTenants -1 (unlimited), got %d", systemInfo.MaxTenants)
		}
	})

	t.Run("CheckFeatureAccess", func(t *testing.T) {
		ctx := context.Background()
		result := service.CheckFeatureAccess(ctx, "advanced_seo")
		if result == nil {
			t.Error("CheckFeatureAccess returned nil")
			return
		}
		if !result.Allowed {
			t.Error("Expected feature to be allowed")
		}
		if result.Edition != contract.EditionBusiness {
			t.Errorf("Expected Edition '%s', got '%s'", contract.EditionBusiness, result.Edition)
		}
	})

	t.Run("RefreshLicenses", func(t *testing.T) {
		err := service.RefreshLicenses()
		if err != nil {
			t.Errorf("RefreshLicenses returned error: %v", err)
		}
	})

	t.Run("GetLicenseStatus", func(t *testing.T) {
		status := service.GetLicenseStatus()
		if status == nil {
			t.Error("GetLicenseStatus returned nil")
			return
		}
		if status.SystemLicense == nil {
			t.Error("SystemLicense is nil")
		}
		if status.OverallStatus != "valid" {
			t.Errorf("Expected OverallStatus 'valid', got '%s'", status.OverallStatus)
		}
	})
}

// MockLicenseManager 模拟许可证管理器
type MockLicenseManager struct{}

func (m *MockLicenseManager) GetLicenseInfo() *contract.LicenseInfo {
	return &contract.LicenseInfo{
		ModuleName: "system",
		Type:       "development",
		Edition:    contract.EditionBusiness,
		IsValid:    true,
	}
}

func (m *MockLicenseManager) ValidateLicense(ctx context.Context) (*contract.LicenseInfo, error) {
	return &contract.LicenseInfo{
		ModuleName: "system",
		Type:       "development",
		Edition:    contract.EditionBusiness,
		IsValid:    true,
	}, nil
}

func (m *MockLicenseManager) IsFeatureAuthorized(ctx context.Context, featureName string) bool {
	return true
}

func (m *MockLicenseManager) RefreshLicense() error {
	return nil
}

func (m *MockLicenseManager) InstallLicense(licenseData string) error {
	return nil
}

func (m *MockLicenseManager) ClearCache() {
	// Do nothing
}

func (m *MockLicenseManager) GetCacheStats() map[string]interface{} {
	return make(map[string]interface{})
}

func (m *MockLicenseManager) GetLicenseStatus() string {
	return "valid"
}

// 实现其他必需的方法（简化版本）
func (m *MockLicenseManager) ActivateModuleLicense(ctx context.Context, moduleID string, licenseData string) error {
	return nil
}

func (m *MockLicenseManager) DeactivateModuleLicense(ctx context.Context, moduleID string) error {
	return nil
}

func (m *MockLicenseManager) ValidateModuleLicense(ctx context.Context, moduleID string) (*contract.LicenseInfo, error) {
	return nil, nil
}

func (m *MockLicenseManager) GetModuleLicenseInfo(ctx context.Context, moduleID string) (*contract.LicenseInfo, error) {
	return nil, nil
}

func (m *MockLicenseManager) ListLicensedModules(ctx context.Context) ([]string, error) {
	return nil, nil
}

func (m *MockLicenseManager) IsModuleAuthorized(ctx context.Context, moduleID string) bool {
	return true
}

func (m *MockLicenseManager) SetModuleLicenseValidator(moduleID string, validator contract.LicenseValidator) error {
	return nil
}

// 多租户方法
func (m *MockLicenseManager) ActivateModuleLicenseForSite(ctx context.Context, siteID, moduleID string, licenseData string) error {
	return nil
}

func (m *MockLicenseManager) DeactivateModuleLicenseForSite(ctx context.Context, siteID, moduleID string) error {
	return nil
}

func (m *MockLicenseManager) ValidateModuleLicenseForSite(ctx context.Context, siteID, moduleID string) (*contract.LicenseInfo, error) {
	return nil, nil
}

func (m *MockLicenseManager) GetModuleLicenseInfoForSite(ctx context.Context, siteID, moduleID string) (*contract.LicenseInfo, error) {
	return nil, nil
}

func (m *MockLicenseManager) ListLicensedModulesForSite(ctx context.Context, siteID string) ([]string, error) {
	return nil, nil
}

func (m *MockLicenseManager) SetModuleLicenseValidatorForSite(siteID, moduleID string, validator contract.LicenseValidator) error {
	return nil
}
