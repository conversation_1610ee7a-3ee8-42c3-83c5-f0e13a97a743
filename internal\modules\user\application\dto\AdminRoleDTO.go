/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/user/application/dto/AdminRoleDTO.go
 * @Description: DTOs for Admin Role operations.
 *
 * © 2025 GACMS. All rights reserved.
 */
package dto

// AdminRoleCreateDTO is used for creating a new admin role.
type AdminRoleCreateDTO struct {
	Name        string `json:"name" binding:"required,min=2,max=50"`
	Slug        string `json:"slug" binding:"required,min=2,max=50"`
	Description string `json:"description" binding:"max=255"`
	SiteID      *uint  `json:"siteId"`
}

// AdminRoleUpdateDTO is used for updating an existing admin role.
type AdminRoleUpdateDTO struct {
	Name        *string `json:"name"`
	Description *string `json:"description"`
}

// AdminRolePermissionsUpdateDTO is used to update the permissions for a role.
type AdminRolePermissionsUpdateDTO struct {
	PermissionIDs []uint `json:"permissionIds" binding:"required"`
}

// AssignPermissionsDTO is used to assign permissions to a role.
type AssignPermissionsDTO struct {
	PermissionIDs []uint `json:"permissionIds" binding:"required,gt=0"`
} 