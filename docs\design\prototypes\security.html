<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 安全设置</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .security-log {
            background-color: rgba(30, 32, 40, 0.6);
            border-radius: 8px;
            padding: 10px 15px;
            margin-bottom: 10px;
            border-left: 4px solid #6B7280;
            transition: all 0.2s ease;
        }
        
        .security-log:hover {
            background-color: rgba(30, 32, 40, 0.8);
        }
        
        .security-log.warning {
            border-left-color: #F59E0B;
        }
        
        .security-log.danger {
            border-left-color: #EF4444;
        }
        
        .security-log.success {
            border-left-color: #10B981;
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #555;
            transition: .4s;
            border-radius: 24px;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .toggle-slider {
            background-color: #007bff;
        }
        
        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">安全设置</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="saveSecuritySettings" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-save text-white"></i>
                                </span>
                                保存设置
                            </span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 安全状态概览 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold mb-4 text-white">安全状态概览</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                                <i class="fas fa-shield-alt text-green-500 text-xl"></i>
                            </div>
                            <div>
                                <div class="text-sm text-gray-400">总体安全评分</div>
                                <div class="text-xl font-semibold text-white">92<span class="text-sm text-gray-400">/100</span></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                                <i class="fas fa-clock text-blue-500 text-xl"></i>
                            </div>
                            <div>
                                <div class="text-sm text-gray-400">上次系统更新</div>
                                <div class="text-xl font-semibold text-white">2天前</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center mr-4">
                                <i class="fas fa-exclamation-triangle text-yellow-500 text-xl"></i>
                            </div>
                            <div>
                                <div class="text-sm text-gray-400">未解决安全问题</div>
                                <div class="text-xl font-semibold text-white">2</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-red-500/20 flex items-center justify-center mr-4">
                                <i class="fas fa-user-shield text-red-500 text-xl"></i>
                            </div>
                            <div>
                                <div class="text-sm text-gray-400">失败登录尝试</div>
                                <div class="text-xl font-semibold text-white">5 <span class="text-xs text-gray-400">本周</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主设置区域 -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 左侧安全设置 -->
                <div class="lg:col-span-2">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-6 text-white">核心安全设置</h3>
                        
                        <div class="space-y-6">
                            <!-- 登录安全 -->
                            <div class="p-4 bg-gray-800/20 rounded-lg border border-gray-700">
                                <h4 class="text-white font-medium mb-4">登录安全</h4>
                                
                                <div class="space-y-4">
                                    <!-- 双因素认证 -->
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <div class="text-white">双因素认证</div>
                                            <div class="text-sm text-gray-400">要求用户使用 App 或短信验证码进行二次验证</div>
                                        </div>
                                        <label class="toggle-switch">
                                            <input type="checkbox" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    
                                    <!-- 限制登录尝试 -->
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <div class="text-white">限制登录尝试</div>
                                            <div class="text-sm text-gray-400">连续失败5次后锁定账户30分钟</div>
                                        </div>
                                        <label class="toggle-switch">
                                            <input type="checkbox" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    
                                    <!-- 强制密码复杂度 -->
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <div class="text-white">强制密码复杂度</div>
                                            <div class="text-sm text-gray-400">密码必须包含字母、数字和特殊字符</div>
                                        </div>
                                        <label class="toggle-switch">
                                            <input type="checkbox" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    
                                    <!-- 强制定期修改密码 -->
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <div class="text-white">强制定期修改密码</div>
                                            <div class="text-sm text-gray-400">每90天提示修改密码</div>
                                        </div>
                                        <label class="toggle-switch">
                                            <input type="checkbox">
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 数据安全 -->
                            <div class="p-4 bg-gray-800/20 rounded-lg border border-gray-700">
                                <h4 class="text-white font-medium mb-4">数据安全</h4>
                                
                                <div class="space-y-4">
                                    <!-- 自动备份 -->
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <div class="text-white">自动备份</div>
                                            <div class="text-sm text-gray-400">每日自动备份网站数据</div>
                                        </div>
                                        <label class="toggle-switch">
                                            <input type="checkbox" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    
                                    <!-- 数据加密存储 -->
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <div class="text-white">数据加密存储</div>
                                            <div class="text-sm text-gray-400">敏感数据使用AES-256加密存储</div>
                                        </div>
                                        <label class="toggle-switch">
                                            <input type="checkbox" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    
                                    <!-- SQL注入防护 -->
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <div class="text-white">SQL注入防护</div>
                                            <div class="text-sm text-gray-400">对所有数据库操作进行参数化处理</div>
                                        </div>
                                        <label class="toggle-switch">
                                            <input type="checkbox" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 访问限制 -->
                            <div class="p-4 bg-gray-800/20 rounded-lg border border-gray-700">
                                <h4 class="text-white font-medium mb-4">访问限制</h4>
                                
                                <div class="space-y-4">
                                    <!-- IP白名单 -->
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <div class="text-white">启用IP白名单</div>
                                            <div class="text-sm text-gray-400">仅允许指定IP访问管理后台</div>
                                        </div>
                                        <label class="toggle-switch">
                                            <input type="checkbox">
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    
                                    <!-- IP白名单设置 -->
                                    <div>
                                        <label class="block text-sm text-gray-400 mb-1">IP白名单设置（每行一个IP地址）</label>
                                        <textarea class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent" rows="4" placeholder="例如: ***********&#10;********"></textarea>
                                    </div>
                                    
                                    <!-- 禁止API访问 -->
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <div class="text-white">限制API访问</div>
                                            <div class="text-sm text-gray-400">仅允许授权应用访问API</div>
                                        </div>
                                        <label class="toggle-switch">
                                            <input type="checkbox" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧安全日志 -->
                <div class="lg:col-span-1">
                    <!-- 安全日志 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">安全日志</h3>
                        
                        <div class="space-y-4">
                            <div class="security-log danger">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="font-medium text-white">登录失败</span>
                                    <span class="text-xs text-gray-400">今天 09:45</span>
                                </div>
                                <p class="text-sm text-gray-300">IP: ************* - 用户: admin</p>
                            </div>
                            
                            <div class="security-log warning">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="font-medium text-white">页面权限不足</span>
                                    <span class="text-xs text-gray-400">今天 08:32</span>
                                </div>
                                <p class="text-sm text-gray-300">用户: editor - 页面: /admin/settings</p>
                            </div>
                            
                            <div class="security-log success">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="font-medium text-white">密码已更改</span>
                                    <span class="text-xs text-gray-400">昨天 15:20</span>
                                </div>
                                <p class="text-sm text-gray-300">用户: admin - IP: *************</p>
                            </div>
                            
                            <div class="security-log">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="font-medium text-white">成功登录</span>
                                    <span class="text-xs text-gray-400">昨天 15:05</span>
                                </div>
                                <p class="text-sm text-gray-300">用户: admin - IP: *************</p>
                            </div>
                            
                            <div class="security-log warning">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="font-medium text-white">文件权限变更</span>
                                    <span class="text-xs text-gray-400">3天前 11:24</span>
                                </div>
                                <p class="text-sm text-gray-300">用户: admin - 文件: /config/database.php</p>
                            </div>
                        </div>
                        
                        <div class="mt-6 text-center">
                            <a href="#" class="text-blue-400 hover:text-blue-300 text-sm">查看全部日志</a>
                        </div>
                    </div>
                    
                    <!-- 安全建议 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">安全建议</h3>
                        
                        <div class="space-y-4">
                            <div class="p-4 bg-yellow-500/20 rounded-lg border border-yellow-500/30">
                                <div class="flex items-start">
                                    <div class="mr-3 mt-1">
                                        <i class="fas fa-exclamation-triangle text-yellow-500"></i>
                                    </div>
                                    <div>
                                        <h5 class="text-white font-medium mb-1">更新您的PHP版本</h5>
                                        <p class="text-sm text-gray-300">您当前使用的PHP版本 (7.4.3) 即将停止支持。建议升级到PHP 8.2或更高版本。</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="p-4 bg-red-500/20 rounded-lg border border-red-500/30">
                                <div class="flex items-start">
                                    <div class="mr-3 mt-1">
                                        <i class="fas fa-times-circle text-red-500"></i>
                                    </div>
                                    <div>
                                        <h5 class="text-white font-medium mb-1">有5个插件需要更新</h5>
                                        <p class="text-sm text-gray-300">这些插件包含已知安全漏洞，请立即更新以保护您的网站。</p>
                                        <a href="#" class="text-blue-400 hover:text-blue-300 text-xs">查看详情</a>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="p-4 bg-gray-700/20 rounded-lg border border-gray-600/30">
                                <div class="flex items-start">
                                    <div class="mr-3 mt-1">
                                        <i class="fas fa-info-circle text-blue-500"></i>
                                    </div>
                                    <div>
                                        <h5 class="text-white font-medium mb-1">启用HTTPS</h5>
                                        <p class="text-sm text-gray-300">建议为您的网站配置SSL证书并强制使用HTTPS。</p>
                                        <a href="#" class="text-blue-400 hover:text-blue-300 text-xs">如何设置</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            
            // 加载顶部导航栏
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 保存按钮事件
            document.getElementById('saveSecuritySettings').addEventListener('click', function() {
                // 在这里添加保存设置的逻辑
                showNotification('设置已保存', 'success');
            });
            
            // 显示通知的辅助函数
            function showNotification(message, type = 'info') {
                // 在实际应用中实现通知显示逻辑
                console.log(`${type.toUpperCase()}: ${message}`);
            }
        });
    </script>
</body>
</html> 