/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/topic/application/service/TopicService.go
 * @Description: Application service for topic-related business logic.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package service

import (
	"context"
	"gacms/internal/modules/topic/domain/contract"
	"gacms/internal/modules/topic/domain/model"
	// We might need post service in the future to get related posts,
	// but the dependency on contenttype service is now removed.
)

// TopicWithPosts is a DTO that combines a topic's data with its related posts.
type TopicWithPosts struct {
	Topic *model.Topic `json:"topic"`
	// Posts will be handled separately, perhaps by a post service.
	// Posts []*postModel.Post `json:"posts"`
}

type TopicService struct {
	topicRepo contract.TopicRepository
	// postRepo postContract.PostRepository // This would be the correct dependency for posts
}

func NewTopicService(topicRepo contract.TopicRepository) *TopicService {
	return &TopicService{topicRepo: topicRepo}
}

// GetTopicBySlug fetches a topic by its slug.
// The siteID is implicitly handled by the context via the repository.
func (s *TopicService) GetTopicBySlug(ctx context.Context, topicSlug string) (*model.Topic, error) {
	return s.topicRepo.GetBySlug(ctx, topicSlug)
} 