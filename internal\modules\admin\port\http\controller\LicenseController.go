/*
Author: <PERSON><PERSON> Nieh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package controller

import (
	"net/http"

	"gacms/internal/core/service"
	"gacms/pkg/contract"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// LicenseController 许可证管理控制器
type LicenseController struct {
	licenseManager contract.LicenseManager
	moduleFactory  *service.ModuleProxyFactory
	logger         *zap.Logger
}

// NewLicenseController 创建许可证管理控制器
func NewLicenseController(
	licenseManager contract.LicenseManager,
	moduleFactory *service.ModuleProxyFactory,
	logger *zap.Logger,
) *LicenseController {
	return &LicenseController{
		licenseManager: licenseManager,
		moduleFactory:  moduleFactory,
		logger:         logger,
	}
}

// RegisterRoutes 注册路由
func (c *LicenseController) RegisterRoutes(group *gin.RouterGroup) {
	license := group.Group("/license")
	{
		// 许可证信息
		license.GET("/info", c.GetLicenseInfo)
		license.GET("/status", c.GetLicenseStatus)
		license.POST("/validate", c.ValidateLicense)
		
		// 许可证管理
		license.POST("/install", c.InstallLicense)
		license.POST("/refresh", c.RefreshLicense)
		
		// 授权检查
		license.GET("/modules", c.GetAuthorizedModules)
		license.GET("/modules/unauthorized", c.GetUnauthorizedModules)
		license.POST("/modules/:name/check", c.CheckModuleAuthorization)
		license.POST("/features/:name/check", c.CheckFeatureAuthorization)
		
		// 缓存管理
		license.GET("/cache", c.GetCacheStats)
		license.POST("/cache/clear", c.ClearCache)
	}
}

// GetLicenseInfo 获取许可证信息
func (c *LicenseController) GetLicenseInfo(ctx *gin.Context) {
	info := c.licenseManager.GetLicenseInfo()
	
	if info == nil {
		ctx.JSON(http.StatusOK, gin.H{
			"status": "success",
			"data": gin.H{
				"edition": "community",
				"status":  "missing",
				"message": "No license installed, running in community edition",
			},
		})
		return
	}
	
	// 隐藏敏感信息
	safeInfo := gin.H{
		"customer_name":       info.CustomerName,
		"customer_email":      info.CustomerEmail,
		"edition":             info.Edition,
		"version":             info.Version,
		"issued_at":           info.IssuedAt,
		"expires_at":          info.ExpiresAt,
		"max_sites":           info.MaxSites,
		"max_users":           info.MaxUsers,
		"authorized_modules":  info.AuthorizedModules,
		"authorized_features": info.AuthorizedFeatures,
		"restrictions":        info.Restrictions,
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   safeInfo,
	})
}

// GetLicenseStatus 获取许可证状态
func (c *LicenseController) GetLicenseStatus(ctx *gin.Context) {
	status := c.licenseManager.GetLicenseStatus()
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"license_status": status,
		},
	})
}

// ValidateLicense 验证许可证
func (c *LicenseController) ValidateLicense(ctx *gin.Context) {
	info, err := c.licenseManager.ValidateLicense(ctx)
	
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":   "License validation failed",
			"details": err.Error(),
		})
		return
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "License validation successful",
		"data": gin.H{
			"edition":    info.Edition,
			"expires_at": info.ExpiresAt,
		},
	})
}

// InstallLicense 安装许可证
func (c *LicenseController) InstallLicense(ctx *gin.Context) {
	var req struct {
		LicenseData string `json:"license_data" binding:"required"`
	}
	
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}
	
	err := c.licenseManager.InstallLicense(req.LicenseData)
	if err != nil {
		c.logger.Error("Failed to install license", zap.Error(err))
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":   "Failed to install license",
			"details": err.Error(),
		})
		return
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "License installed successfully",
	})
}

// RefreshLicense 刷新许可证
func (c *LicenseController) RefreshLicense(ctx *gin.Context) {
	err := c.licenseManager.RefreshLicense()
	if err != nil {
		c.logger.Error("Failed to refresh license", zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to refresh license",
			"details": err.Error(),
		})
		return
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "License refreshed successfully",
	})
}

// GetAuthorizedModules 获取授权的模块列表
func (c *LicenseController) GetAuthorizedModules(ctx *gin.Context) {
	authorizedModules := c.moduleFactory.GetAuthorizedModules(ctx)
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"authorized_modules": authorizedModules,
			"count":              len(authorizedModules),
		},
	})
}

// GetUnauthorizedModules 获取未授权的模块列表
func (c *LicenseController) GetUnauthorizedModules(ctx *gin.Context) {
	unauthorizedModules := c.moduleFactory.GetUnauthorizedModules(ctx)
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"unauthorized_modules": unauthorizedModules,
			"count":                len(unauthorizedModules),
		},
	})
}

// CheckModuleAuthorization 检查模块授权
func (c *LicenseController) CheckModuleAuthorization(ctx *gin.Context) {
	moduleName := ctx.Param("name")

	// 使用ValidateModuleLicense方法
	licenseInfo, err := c.licenseManager.ValidateModuleLicense(ctx, moduleName)
	authorized := err == nil && licenseInfo != nil && licenseInfo.IsValid

	response := gin.H{
		"module":     moduleName,
		"authorized": authorized,
	}

	if licenseInfo != nil {
		response["license_type"] = licenseInfo.Type
		if !authorized && licenseInfo.ErrorMsg != "" {
			response["error"] = licenseInfo.ErrorMsg
		}
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   response,
	})
}

// CheckFeatureAuthorization 检查功能授权
func (c *LicenseController) CheckFeatureAuthorization(ctx *gin.Context) {
	featureName := ctx.Param("name")

	authorized := c.licenseManager.IsFeatureAuthorized(ctx, featureName)

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"feature":    featureName,
			"authorized": authorized,
		},
	})
}

// GetCacheStats 获取缓存统计
func (c *LicenseController) GetCacheStats(ctx *gin.Context) {
	stats := c.licenseManager.GetCacheStats()
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   stats,
	})
}

// ClearCache 清空缓存
func (c *LicenseController) ClearCache(ctx *gin.Context) {
	c.licenseManager.ClearCache()
	
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "License cache cleared successfully",
	})
}
