<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 前台预览</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        /* 前台预览样式 */
        :root {
            --primary-color: #3B82F6;
            --primary-dark: #2563EB;
            --secondary-color: #10B981;
            --background-light: #F9FAFB;
            --text-color: #1F2937;
            --text-light: #6B7280;
            --border-color: #E5E7EB;
        }
        
        .navbar {
            background-color: #fff;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .nav-link {
            padding: 0.5rem 1rem;
            color: var(--text-color);
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .nav-link:hover {
            color: var(--primary-color);
        }
        
        .nav-link.active {
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-color);
        }
        
        .hero {
            background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
            color: white;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            transition: all 0.2s ease;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
        }
        
        .btn-outline {
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
            background-color: transparent;
            transition: all 0.2s ease;
        }
        
        .btn-outline:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        .card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            font-size: 1.5rem;
            background-color: rgba(59, 130, 246, 0.1);
            color: var(--primary-color);
        }
        
        .article-card {
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .article-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }
        
        .article-image {
            height: 200px;
            object-fit: cover;
        }
        
        .category-badge {
            background-color: rgba(59, 130, 246, 0.1);
            color: var(--primary-color);
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .footer {
            background-color: #1F2937;
            color: #E5E7EB;
        }
        
        .footer-link {
            color: #D1D5DB;
            transition: all 0.2s ease;
        }
        
        .footer-link:hover {
            color: #F9FAFB;
        }
        
        .social-icon {
            width: 36px;
            height: 36px;
            border-radius: 9999px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            transition: all 0.2s ease;
        }
        
        .social-icon:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        /* Admin toolbar */
        .admin-toolbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background-color: #1F2937;
            color: white;
            z-index: 1000;
            padding: 0.5rem 1rem;
        }
        
        .preview-badge {
            background-color: #EF4444;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            letter-spacing: 0.025em;
            text-transform: uppercase;
        }
        
        /* With admin toolbar */
        body.with-admin-toolbar .frontend-preview {
            padding-top: 3rem;
        }
        
        .device-frame {
            border: 12px solid #2c2c2c;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            margin: 0 auto;
            transition: all 0.3s ease;
        }
        
        .device-mobile {
            width: 375px;
            height: 667px;
            border-radius: 36px;
        }
        
        .device-tablet {
            width: 768px;
            height: 1024px;
        }
        
        .device-desktop {
            width: 100%;
            max-width: 1200px;
            height: 800px;
            border-top-width: 24px;
        }
        
        .device-desktop::before {
            content: '';
            position: absolute;
            top: -18px;
            left: 15px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #ff6058;
            box-shadow: 20px 0 0 #ffbd2e, 40px 0 0 #2acb42;
            z-index: 10;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">前台预览</h2>
                    <div class="flex items-center gap-4">
                        <select id="deviceSelect" class="bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="desktop">桌面端</option>
                            <option value="tablet">平板端</option>
                            <option value="mobile">移动端</option>
                        </select>
                        <select id="themeSelect" class="bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="default">默认主题</option>
                            <option value="dark">深色主题</option>
                            <option value="business">商务主题</option>
                        </select>
                        <a href="#" target="_blank" class="flex items-center justify-center bg-gradient-to-r from-green-500 to-green-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-green-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <i class="fas fa-external-link-alt mr-2"></i> 新窗口打开
                            </span>
                        </a>
                    </div>
                </div>
                <div class="mt-4">
                    <p class="text-gray-400">预览模式允许您查看前台页面在不同设备和主题下的显示效果，而无需离开管理界面。您可以选择不同的设备尺寸和主题来测试网站的响应式设计。</p>
                </div>
            </div>
            
            <!-- 预览容器 -->
            <div class="mb-6 relative">
                <div class="device-frame device-desktop relative">
                    <div class="with-admin-toolbar">
                        <!-- 管理员工具栏 -->
                        <div class="admin-toolbar flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="preview-badge mr-3">预览模式</span>
                                <select class="bg-gray-700 text-white text-sm rounded-lg px-2 py-1 border-none focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option>默认主题</option>
                                    <option>深色主题</option>
                                    <option>商务主题</option>
                                </select>
                            </div>
                            <div class="flex items-center space-x-4">
                                <select class="bg-gray-700 text-white text-sm rounded-lg px-2 py-1 border-none focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option>中文 (简体)</option>
                                    <option>English</option>
                                </select>
                                <div class="flex items-center">
                                    <label class="mr-2 text-sm">编辑模式</label>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer" checked>
                                        <div class="w-9 h-5 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="text-white bg-blue-600 hover:bg-blue-700 rounded px-3 py-1 text-sm">保存</button>
                                    <button class="text-white bg-gray-600 hover:bg-gray-700 rounded px-3 py-1 text-sm">返回管理</button>
                                </div>
                            </div>
                        </div>

                        <!-- 前台页面内容 -->
                        <div class="frontend-preview" style="font-family: 'Inter', sans-serif; color: var(--text-color); background-color: var(--background-light);">
                            <!-- 导航栏 -->
                            <nav class="navbar py-4">
                                <div class="container mx-auto px-4 flex items-center justify-between">
                                    <a href="#" class="flex items-center space-x-2">
                                        <img src="https://images.unsplash.com/photo-1563906267088-b029e7101114?ixlib=rb-1.2.1&auto=format&fit=crop&w=40&h=40&q=80" alt="Logo" class="h-10">
                                        <span class="text-xl font-bold text-gray-900">GACMS</span>
                                    </a>
                                    <div class="hidden md:flex space-x-1">
                                        <a href="#" class="nav-link active">首页</a>
                                        <a href="#" class="nav-link">新闻</a>
                                        <a href="#" class="nav-link">产品</a>
                                        <a href="#" class="nav-link">关于我们</a>
                                        <a href="#" class="nav-link">联系我们</a>
                                    </div>
                                    <div class="flex items-center space-x-4">
                                        <div class="hidden md:block">
                                            <button class="bg-transparent hover:bg-gray-100 text-gray-700 px-4 py-2 rounded-lg transition-colors">登录</button>
                                            <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">注册</button>
                                        </div>
                                        <button class="md:hidden text-gray-700">
                                            <i class="fas fa-bars text-xl"></i>
                                        </button>
                                    </div>
                                </div>
                            </nav>

                            <!-- 英雄区 -->
                            <section class="hero py-16">
                                <div class="container mx-auto px-4">
                                    <div class="max-w-3xl mx-auto text-center">
                                        <h1 class="text-4xl md:text-5xl font-bold mb-6">构建现代化、高性能的内容管理系统</h1>
                                        <p class="text-xl mb-8 opacity-90">GACMS 提供灵活且强大的内容管理解决方案，助您打造出色的数字体验</p>
                                        <div class="flex flex-col sm:flex-row justify-center gap-4">
                                            <button class="btn-primary px-8 py-3 rounded-lg font-medium">立即开始</button>
                                            <button class="btn-outline px-8 py-3 rounded-lg font-medium">了解更多</button>
                                        </div>
                                    </div>
                                </div>
                            </section>

                            <!-- 特性区 -->
                            <section class="py-16 bg-white">
                                <div class="container mx-auto px-4">
                                    <div class="text-center mb-12">
                                        <h2 class="text-3xl font-bold mb-4">为什么选择 GACMS?</h2>
                                        <p class="text-lg text-gray-600 max-w-2xl mx-auto">我们提供现代化的内容管理功能，帮助您更高效地管理网站内容</p>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                                        <div class="card p-6">
                                            <div class="feature-icon">
                                                <i class="fas fa-bolt"></i>
                                            </div>
                                            <h3 class="text-xl font-bold mb-2">高性能</h3>
                                            <p class="text-gray-600">优化的代码和缓存机制确保您的网站加载速度快如闪电</p>
                                        </div>

                                        <div class="card p-6">
                                            <div class="feature-icon">
                                                <i class="fas fa-shield-alt"></i>
                                            </div>
                                            <h3 class="text-xl font-bold mb-2">安全可靠</h3>
                                            <p class="text-gray-600">内置安全机制，保护您的网站免受常见威胁</p>
                                        </div>

                                        <div class="card p-6">
                                            <div class="feature-icon">
                                                <i class="fas fa-th-large"></i>
                                            </div>
                                            <h3 class="text-xl font-bold mb-2">模块化设计</h3>
                                            <p class="text-gray-600">灵活的插件系统，按需扩展您的网站功能</p>
                                        </div>
                                    </div>
                                </div>
                            </section>

                            <!-- 文章区 -->
                            <section class="py-16 bg-gray-50">
                                <div class="container mx-auto px-4">
                                    <div class="flex justify-between items-center mb-8">
                                        <h2 class="text-2xl font-bold">最新文章</h2>
                                        <a href="#" class="text-blue-600 hover:text-blue-700 flex items-center">
                                            查看全部 <i class="fas fa-arrow-right ml-2"></i>
                                        </a>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                                        <article class="article-card bg-white overflow-hidden">
                                            <img src="https://images.unsplash.com/photo-1581287053822-fd7bf4f4bfec?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="Article Image" class="article-image w-full">
                                            <div class="p-6">
                                                <div class="flex items-center mb-3">
                                                    <span class="category-badge">技术</span>
                                                    <span class="text-gray-500 text-sm ml-auto">2025-03-10</span>
                                                </div>
                                                <h3 class="text-xl font-bold mb-3">如何优化您的网站性能</h3>
                                                <p class="text-gray-600 mb-4">了解提升网站速度和用户体验的关键技巧和最佳实践...</p>
                                                <a href="#" class="text-blue-600 hover:text-blue-700 font-medium">阅读更多</a>
                                            </div>
                                        </article>

                                        <article class="article-card bg-white overflow-hidden">
                                            <img src="https://images.unsplash.com/photo-1542744173-8659b8e77b29?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="Article Image" class="article-image w-full">
                                            <div class="p-6">
                                                <div class="flex items-center mb-3">
                                                    <span class="category-badge">设计</span>
                                                    <span class="text-gray-500 text-sm ml-auto">2025-03-08</span>
                                                </div>
                                                <h3 class="text-xl font-bold mb-3">2025年网页设计趋势预测</h3>
                                                <p class="text-gray-600 mb-4">发现未来一年将主导网页设计的最新趋势和创新技术...</p>
                                                <a href="#" class="text-blue-600 hover:text-blue-700 font-medium">阅读更多</a>
                                            </div>
                                        </article>

                                        <article class="article-card bg-white overflow-hidden">
                                            <img src="https://images.unsplash.com/photo-1616469829941-c7200edec809?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="Article Image" class="article-image w-full">
                                            <div class="p-6">
                                                <div class="flex items-center mb-3">
                                                    <span class="category-badge">营销</span>
                                                    <span class="text-gray-500 text-sm ml-auto">2025-03-05</span>
                                                </div>
                                                <h3 class="text-xl font-bold mb-3">内容营销策略完全指南</h3>
                                                <p class="text-gray-600 mb-4">探索有效的内容营销策略，吸引更多目标受众并提升转化率...</p>
                                                <a href="#" class="text-blue-600 hover:text-blue-700 font-medium">阅读更多</a>
                                            </div>
                                        </article>
                                    </div>
                                </div>
                            </section>

                            <!-- 页脚 -->
                            <footer class="footer py-12">
                                <div class="container mx-auto px-4">
                                    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                                        <div>
                                            <div class="flex items-center space-x-2 mb-6">
                                                <img src="https://images.unsplash.com/photo-1563906267088-b029e7101114?ixlib=rb-1.2.1&auto=format&fit=crop&w=40&h=40&q=80" alt="Logo" class="h-10">
                                                <span class="text-xl font-bold text-white">GACMS</span>
                                            </div>
                                            <p class="mb-6">现代化、高性能的内容管理解决方案，为您的数字体验提供强大支持。</p>
                                            <div class="flex space-x-3">
                                                <a href="#" class="social-icon">
                                                    <i class="fab fa-twitter"></i>
                                                </a>
                                                <a href="#" class="social-icon">
                                                    <i class="fab fa-facebook-f"></i>
                                                </a>
                                                <a href="#" class="social-icon">
                                                    <i class="fab fa-instagram"></i>
                                                </a>
                                                <a href="#" class="social-icon">
                                                    <i class="fab fa-github"></i>
                                                </a>
                                            </div>
                                        </div>

                                        <div>
                                            <h4 class="text-lg font-semibold mb-6">导航</h4>
                                            <ul class="space-y-3">
                                                <li><a href="#" class="footer-link">首页</a></li>
                                                <li><a href="#" class="footer-link">新闻</a></li>
                                                <li><a href="#" class="footer-link">产品</a></li>
                                                <li><a href="#" class="footer-link">关于我们</a></li>
                                                <li><a href="#" class="footer-link">联系我们</a></li>
                                            </ul>
                                        </div>

                                        <div>
                                            <h4 class="text-lg font-semibold mb-6">资源</h4>
                                            <ul class="space-y-3">
                                                <li><a href="#" class="footer-link">文档</a></li>
                                                <li><a href="#" class="footer-link">博客</a></li>
                                                <li><a href="#" class="footer-link">API 参考</a></li>
                                                <li><a href="#" class="footer-link">支持中心</a></li>
                                                <li><a href="#" class="footer-link">常见问题</a></li>
                                            </ul>
                                        </div>

                                        <div>
                                            <h4 class="text-lg font-semibold mb-6">联系我们</h4>
                                            <ul class="space-y-3">
                                                <li class="flex items-center">
                                                    <i class="fas fa-map-marker-alt mr-3 text-gray-400"></i>
                                                    <span>北京市朝阳区科技园区</span>
                                                </li>
                                                <li class="flex items-center">
                                                    <i class="fas fa-phone-alt mr-3 text-gray-400"></i>
                                                    <span>+86 10 1234 5678</span>
                                                </li>
                                                <li class="flex items-center">
                                                    <i class="fas fa-envelope mr-3 text-gray-400"></i>
                                                    <span><EMAIL></span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="border-t border-gray-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
                                        <p>© 2025 GACMS. 保留所有权利。</p>
                                        <div class="flex space-x-6 mt-4 md:mt-0">
                                            <a href="#" class="footer-link">隐私政策</a>
                                            <a href="#" class="footer-link">服务条款</a>
                                            <a href="#" class="footer-link">Cookie 设置</a>
                                        </div>
                                    </div>
                                </div>
                            </footer>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 设备切换
            const deviceSelect = document.getElementById('deviceSelect');
            const deviceFrame = document.querySelector('.device-frame');
            
            if(deviceSelect && deviceFrame) {
                deviceSelect.addEventListener('change', function() {
                    // 移除所有设备类名
                    deviceFrame.classList.remove('device-desktop', 'device-tablet', 'device-mobile');
                    
                    // 添加选中的设备类名
                    deviceFrame.classList.add(`device-${this.value}`);
                });
            }
            
            // 主题切换
            const themeSelect = document.getElementById('themeSelect');
            const previewThemeSelect = document.querySelector('.admin-toolbar select');
            
            if(themeSelect && previewThemeSelect) {
                themeSelect.addEventListener('change', function() {
                    // 同步更改预览工具栏中的主题选择
                    previewThemeSelect.value = this.options[this.selectedIndex].text;
                });
            }
        });
    </script>
</body>
</html> 