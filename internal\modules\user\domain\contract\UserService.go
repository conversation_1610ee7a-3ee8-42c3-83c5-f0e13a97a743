/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/modules/user/domain/contract/UserService.go
 * @Description: 用户服务接口定义
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"context"

	"gacms/internal/modules/user/domain/model"
)

// RegisterUserInput 注册用户的输入参数
type RegisterUserInput struct {
	Username string
	Email    string
	Password string
	SiteID   string
}

// LoginUserInput 用户登录的输入参数
type LoginUserInput struct {
	Username  string
	Password  string
	IP        string
	UserAgent string
}

// UpdateUserProfileInput 更新用户资料的输入参数
type UpdateUserProfileInput struct {
	UserID    string
	Nickname  string
	Avatar    string
	Bio       string
	UpdatedBy string
}

// UserService 用户服务接口
type UserService interface {
	// Register 注册新用户
	Register(ctx context.Context, input RegisterUserInput) (*model.User, error)

	// Login 用户登录
	Login(ctx context.Context, input LoginUserInput) (*model.User, error)

	// UpdateProfile 更新用户资料
	UpdateProfile(ctx context.Context, input UpdateUserProfileInput) (*model.User, error)
} 