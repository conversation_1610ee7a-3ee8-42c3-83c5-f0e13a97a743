/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/site/module.go
 * @Description: Defines the site module for GACMS.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package site

import (
	"context"

	"gacms/internal/core/contract"
	systemService "gacms/internal/core/system/service"
	"gacms/internal/modules/site/application/service"
	"gacms/internal/modules/site/domain/model"
	"gacms/internal/modules/site/infrastructure/persistence"
	"gacms/internal/modules/site/port/http/controller"
	themeController "gacms/internal/modules/theme/port/http/controller"
	"gacms/internal/port/http/middleware"
	pkgContract "gacms/pkg/contract"

	"github.com/gin-gonic/gin"
	"go.uber.org/fx"
	"gorm.io/gorm"
)

type SiteModule struct{}

func (m *SiteModule) Name() string {
	return "Site"
}

func (m *SiteModule) GetModels() []interface{} {
	return []interface{}{
		&model.Site{},
	}
}

// Module is the standard fx dependency injection module for the site package.
var Module = fx.Options(
	// 仓储层提供者
	fx.Provide(
		fx.Annotate(
			func(db *gorm.DB) contract.SiteRepository {
				return persistence.NewSiteGormRepository(db)
			},
			fx.As(new(contract.SiteRepository)),
		),
	),

	// 服务层提供者
	fx.Provide(
		fx.Annotate(
			service.NewSiteService,
			fx.As(new(*service.SiteService)),
		),
	),

	// 控制器层提供者
	fx.Provide(controller.NewSiteController),

	// Routable Registration using standard pattern
	fx.Provide(
		fx.Annotate(
			func(c *controller.SiteController) pkgContract.RoutableRegistration {
				return pkgContract.RoutableRegistration{
					Target:   pkgContract.AdminRoute,
					Routable: c,
				}
			},
			fx.ResultTags(`group:"routables"`),
		),
	),
)

// ExposePermissions 显式声明站点模块所需的权限
func (m *SiteModule) ExposePermissions() []contract.PermissionInfo {
	return []contract.PermissionInfo{
		{
			Name:        "system:sites:manage",
			Description: "站点管理权限（包括站点CRUD、域名绑定和URL重写管理）",
			Category:    "system",
			Module:      "site",
		},
		{
			Name:        "system:sites:view",
			Description: "站点查看权限",
			Category:    "system",
			Module:      "site",
		},
	}
}

// ExposeEvents 显式声明站点模块发布的事件
func (m *SiteModule) ExposeEvents() []contract.EventInfo {
	return []contract.EventInfo{
		{
			Name:        "audit.operation.create",
			Description: "操作日志创建事件",
			Module:      "site",
			EventType:   "audit",
		},
		{
			Name:        "domain.binding.created",
			Description: "域名绑定创建事件",
			Module:      "site",
			EventType:   "domain",
		},
		{
			Name:        "domain.binding.updated",
			Description: "域名绑定更新事件",
			Module:      "site",
			EventType:   "domain",
		},
		{
			Name:        "domain.binding.deleted",
			Description: "域名绑定删除事件",
			Module:      "site",
			EventType:   "domain",
		},
		{
			Name:        "url_rule.created",
			Description: "URL重写规则创建事件",
			Module:      "site",
			EventType:   "url_rule",
		},
		{
			Name:        "url_rule.updated",
			Description: "URL重写规则更新事件",
			Module:      "site",
			EventType:   "url_rule",
		},
		{
			Name:        "url_rule.deleted",
			Description: "URL重写规则删除事件",
			Module:      "site",
			EventType:   "url_rule",
		},
	}
}

// ExposeEventListeners 显式声明站点模块监听的事件
func (m *SiteModule) ExposeEventListeners() []contract.EventListenerInfo {
	return []contract.EventListenerInfo{
		// 站点模块目前不监听其他模块的事件
		// 如果需要监听，在这里声明
	}
}

func NewModule() contract.Module {
	return &SiteModule{}
}

func RegisterRoutes(
	router *gin.Engine,
	authMiddleware *middleware.AdminAuthMiddleware,
	siteController *controller.SiteController,
) {
	guard := authMiddleware.Handle("sites:manage")
	adminGroup := router.Group("/api/admin", guard)
	siteController.RegisterRoutes(adminGroup)
} 