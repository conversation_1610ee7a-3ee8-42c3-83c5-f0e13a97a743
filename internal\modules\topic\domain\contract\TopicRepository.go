/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/topic/domain/contract/TopicRepository.go
 * @Description: Defines the contract for topic data access.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"context"
	"gacms/internal/modules/topic/domain/model"
)

// TopicRepository defines the interface for topic data operations.
type TopicRepository interface {
	Create(ctx context.Context, topic *model.Topic) error
	GetBySlug(ctx context.Context, slug string) (*model.Topic, error)
	// In a full implementation, you'd also have List, Update, Delete, etc.
} 