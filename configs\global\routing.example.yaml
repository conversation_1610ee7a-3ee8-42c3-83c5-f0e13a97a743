# GACMS Routing Configuration Example
# This file shows various routing configuration options for enhanced security and customization.

# Entry points configuration
routing:
  entry_points:
    # Admin entry point - CHANGE THIS for production security!
    # Examples: "management", "backend", "secure-admin", "control-panel"
    # Never use predictable names like "admin" in production
    admin: "secure-management-2024"
    
    # API entry point - usually kept as "api" but can be changed
    # Examples: "api", "rest", "v1", "services"
    api: "api"
    
    # Public entry point - usually empty (root path)
    # Can be set to a prefix if needed, e.g., "public", "www"
    public: ""
  
  # Security settings
  security:
    # Hide admin path in error messages and logs for security
    hide_admin_path: true
    
    # Require HTTPS for admin routes (recommended for production)
    admin_require_https: true
    
    # Rate limiting for different entry points
    rate_limiting:
      admin:
        enabled: true
        requests_per_minute: 60
      api:
        enabled: true
        requests_per_minute: 1000
      public:
        enabled: false
        requests_per_minute: 0
    
    # IP whitelist for admin access (optional)
    admin_ip_whitelist:
      enabled: false
      allowed_ips:
        - "127.0.0.1"
        - "::1"
        - "***********/24"
    
    # Admin session settings
    admin_session:
      timeout_minutes: 30
      require_2fa: false
      max_concurrent_sessions: 3

# Route caching settings
cache:
  enabled: true
  ttl_seconds: 300
  
# Logging settings for routing
logging:
  # Log all route access attempts
  log_access: true
  
  # Log failed route attempts
  log_failures: true
  
  # Include request details in logs
  include_request_details: false

# Development settings (disable in production)
development:
  # Show detailed error messages
  show_debug_info: false
  
  # Allow route introspection endpoints
  enable_route_debug: false
  
  # Disable security restrictions for development
  disable_security_checks: false

# Examples of different admin entry configurations:

# High Security Example:
# admin: "mgmt-x7k9p2"  # Random, unpredictable path

# Corporate Example:
# admin: "management"   # Professional but still secure

# Branded Example:
# admin: "control-center"  # Branded but not obvious

# Multi-language Example:
# admin: "administracion"  # Non-English to reduce automated attacks

# Date-based Example (rotate periodically):
# admin: "admin-2024-q4"   # Include date for periodic rotation
