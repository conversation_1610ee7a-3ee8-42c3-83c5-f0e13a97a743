/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/admin/port/http/controller/AdminMenuController.go
 * @Description: Controller for admin menus, delegating logic to AdminService.
 *
 * © 2025 GACMS. All rights reserved.
 */

package controller

import (
	"gacms/internal/modules/admin/application/service"
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
)

type AdminMenuController struct {
	service *service.AdminService
}

func NewAdminMenuController(service *service.AdminService) *AdminMenuController {
	return &AdminMenuController{service: service}
}

// RegisterRoutes registers the routes for admin menus.
func (c *AdminMenuController) RegisterRoutes(rg *gin.RouterGroup) {
	rg.GET("/menus", c.GetAdminMenus)
}

// GetAdminMenus handles the request to get the menus for the admin panel.
// It delegates the actual data fetching to the AdminService, which acts as an API client.
func (c *AdminMenuController) GetAdminMenus(ctx *gin.Context) {
	siteIDStr := ctx.Query("site_id")
	if siteIDStr == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "site_id query parameter is required"})
		return
	}
	siteID, err := strconv.ParseUint(siteIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid site_id format"})
		return
	}

	menus, err := c.service.GetAdminMenus(ctx.Request.Context(), uint(siteID))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve menus"})
		return
	}

	if menus == nil {
		menus = make([]interface{}, 0) // Return empty array instead of null
	}
	
	ctx.JSON(http.StatusOK, gin.H{"data": menus})
} 