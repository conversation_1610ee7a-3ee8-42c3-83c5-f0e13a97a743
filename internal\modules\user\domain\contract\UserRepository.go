/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/modules/user/domain/contract/UserRepository.go
 * @Description: 用户仓储接口定义
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"context"

	"gacms/internal/modules/user/domain/model"
)

// UserRepository 用户仓储接口
type UserRepository interface {
	// Create 创建用户
	Create(ctx context.Context, user *model.User) error

	// Update 更新用户
	Update(ctx context.Context, user *model.User) error

	// Delete 删除用户
	Delete(ctx context.Context, userID string) error

	// FindByID 根据ID查找用户
	FindByID(ctx context.Context, userID string) (*model.User, error)

	// FindByUsername 根据用户名查找用户
	FindByUsername(ctx context.Context, username string) (*model.User, error)

	// FindByEmail 根据邮箱查找用户
	FindByEmail(ctx context.Context, email string) (*model.User, error)

	// ExistsByUsername 检查用户名是否存在
	ExistsByUsername(ctx context.Context, username string) (bool, error)

	// ExistsByEmail 检查邮箱是否存在
	ExistsByEmail(ctx context.Context, email string) (bool, error)

	// FindBySiteID 查找指定站点的所有用户
	FindBySiteID(ctx context.Context, siteID string, offset, limit int) ([]*model.User, int, error)
} 