/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/infrastructure/database/DB.go
 * @Description: 数据库连接
 * 
 * © 2025 GACMS. All rights reserved.
 */

package database

import (
	"context"
	"time"

	"go.uber.org/fx"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Config 数据库配置
type Config struct {
	Driver   string `json:"driver"`
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Database string `json:"database"`
	Username string `json:"username"`
	Password string `json:"password"`
	Charset  string `json:"charset"`
	Debug    bool   `json:"debug"`
}

// DB 数据库连接
type DB struct {
	*gorm.DB
	config *Config
	logger *zap.Logger
}

// DBParams 定义了创建 DB 所需的参数
type DBParams struct {
	fx.In

	Config *Config
	Logger *zap.Logger
}

// NewDB 创建一个新的数据库连接
func NewDB(params DBParams) (*DB, error) {
	// 创建 GORM 日志配置
	gormLogger := logger.New(
		&zapGormWriter{logger: params.Logger},
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Warn,
			IgnoreRecordNotFoundError: true,
			Colorful:                  false,
		},
	)

	// 如果开启调试模式，设置日志级别为 Info
	if params.Config.Debug {
		gormLogger.LogMode(logger.Info)
	}

	// 构建 DSN
	dsn := buildDSN(params.Config)

	// 连接数据库
	gormDB, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		return nil, err
	}

	// 获取底层 SQL DB
	sqlDB, err := gormDB.DB()
	if err != nil {
		return nil, err
	}

	// 设置连接池
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	return &DB{
		DB:     gormDB,
		config: params.Config,
		logger: params.Logger,
	}, nil
}

// buildDSN 构建数据库连接字符串
func buildDSN(config *Config) string {
	return config.Username + ":" + config.Password + "@tcp(" + config.Host + ":" + string(config.Port) + ")/" + config.Database + "?charset=" + config.Charset + "&parseTime=True&loc=Local"
}

// RegisterHooks 注册数据库生命周期钩子
func RegisterHooks(lifecycle fx.Lifecycle, db *DB, logger *zap.Logger) {
	lifecycle.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			logger.Info("Database connected")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			sqlDB, err := db.DB.DB()
			if err != nil {
				return err
			}
			logger.Info("Closing database connection")
			return sqlDB.Close()
		},
	})
}

// zapGormWriter 实现 gorm logger.Writer 接口
type zapGormWriter struct {
	logger *zap.Logger
}

// Printf 实现 gorm logger.Writer 接口
func (w *zapGormWriter) Printf(format string, args ...interface{}) {
	w.logger.Sugar().Debugf(format, args...)
} 