/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/infrastructure/auth/Context.go
 * @Description: Provides context keys and helpers for authentication data.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package auth

import "context"

// contextKey is a private type to prevent collisions with other context keys.
type contextKey string

const (
	// userIDKey is the context key for the authenticated user's ID.
	userIDKey contextKey = "userID"
	// userRolesKey is the context key for the authenticated user's roles.
	userRolesKey contextKey = "userRoles"
	// siteIDKey is the context key for the current site's ID.
	siteIDKey contextKey = "siteID"
)

// With<PERSON>ser sets the user ID and roles in the context.
func WithUser(ctx context.Context, id uint, roles []string) context.Context {
	ctx = context.WithValue(ctx, userIDKey, id)
	ctx = context.WithValue(ctx, userRolesKey, roles)
	return ctx
}

// WithSiteID sets the site ID in the context.
func WithSiteID(ctx context.Context, id uint) context.Context {
	return context.WithValue(ctx, siteIDKey, id)
}

// UserIDFrom returns the user ID from the context, if it exists.
func UserIDFrom(ctx context.Context) (uint, bool) {
	id, ok := ctx.Value(userIDKey).(uint)
	return id, ok
}

// SiteIDFrom returns the site ID from the context, if it exists.
func SiteIDFrom(ctx context.Context) (uint, bool) {
	id, ok := ctx.Value(siteIDKey).(uint)
	return id, ok
}

// UserRolesFrom returns the user roles from the context, if they exist.
func UserRolesFrom(ctx context.Context) ([]string, bool) {
	roles, ok := ctx.Value(userRolesKey).([]string)
	return roles, ok
}
