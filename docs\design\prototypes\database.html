<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 数据库管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button {
            transition: all 0.3s ease;
        }
        
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .log-viewer {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        
        .log-viewer .log-line {
            padding: 2px 8px;
            border-bottom: 1px solid rgba(75, 85, 99, 0.2);
        }
        
        .log-viewer .log-line:hover {
            background-color: rgba(75, 85, 99, 0.1);
        }
        
        .log-viewer .log-error {
            color: #ef4444;
        }
        
        .log-viewer .log-warning {
            color: #f59e0b;
        }
        
        .log-viewer .log-info {
            color: #3b82f6;
        }
        
        .log-viewer .log-debug {
            color: #10b981;
        }
        
        .tab-active {
            border-bottom: 2px solid #3b82f6;
            color: #3b82f6;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-4">
                <a href="dashboard.html" class="text-gray-400 hover:text-white">首页</a>
                <span class="mx-2 text-gray-600">/</span>
                <a href="#" class="text-gray-400 hover:text-white">系统</a>
                <span class="mx-2 text-gray-600">/</span>
                <span class="text-white">数据库管理</span>
            </div>
            
            <!-- 页面标题 -->
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold text-white relative pl-3 section-title">数据库管理</h2>
                <div class="flex space-x-3">
                    <button class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center action-button">
                        <i class="fas fa-database mr-2"></i> 备份数据库
                    </button>
                    <button class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center action-button">
                        <i class="fas fa-cog mr-2"></i> 数据库设置
                    </button>
                </div>
            </div>
            
            <!-- 数据库状态概览 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                    <div class="flex items-center">
                        <div class="rounded-full bg-blue-500/10 p-3 mr-4">
                            <i class="fas fa-server text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">数据库服务器</h3>
                            <p class="text-lg font-bold">MySQL 8.0.28</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                    <div class="flex items-center">
                        <div class="rounded-full bg-green-500/10 p-3 mr-4">
                            <i class="fas fa-check-circle text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">连接状态</h3>
                            <p class="text-lg font-bold text-green-400">正常</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                    <div class="flex items-center">
                        <div class="rounded-full bg-purple-500/10 p-3 mr-4">
                            <i class="fas fa-table text-purple-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">表数量</h3>
                            <p class="text-lg font-bold">42</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                    <div class="flex items-center">
                        <div class="rounded-full bg-yellow-500/10 p-3 mr-4">
                            <i class="fas fa-hdd text-yellow-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">数据库大小</h3>
                            <p class="text-lg font-bold">256.8 MB</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 标签页导航 -->
            <div class="bg-gray-800/20 border border-gray-700 rounded-xl overflow-hidden mb-6">
                <div class="border-b border-gray-700">
                    <div class="flex">
                        <button class="tab-button px-6 py-4 text-blue-400 tab-active" data-tab="tables">
                            <i class="fas fa-table mr-2"></i> 数据表
                        </button>
                        <button class="tab-button px-6 py-4 text-gray-400 hover:text-white" data-tab="query">
                            <i class="fas fa-terminal mr-2"></i> SQL 查询
                        </button>
                        <button class="tab-button px-6 py-4 text-gray-400 hover:text-white" data-tab="backup">
                            <i class="fas fa-save mr-2"></i> 备份与恢复
                        </button>
                        <button class="tab-button px-6 py-4 text-gray-400 hover:text-white" data-tab="optimize">
                            <i class="fas fa-tachometer-alt mr-2"></i> 优化
                        </button>
                    </div>
                </div>
                
                <!-- 数据表标签内容 -->
                <div id="tables" class="tab-content active p-6">
                    <div class="flex justify-between items-center mb-4">
                        <div class="relative">
                            <input type="text" placeholder="搜索表..." class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 pl-10 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        <div class="flex space-x-2">
                            <select class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option>所有表</option>
                                <option>系统表</option>
                                <option>内容表</option>
                                <option>用户表</option>
                                <option>配置表</option>
                            </select>
                            <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                                <i class="fas fa-plus mr-2"></i> 新建表
                            </button>
                        </div>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="bg-gray-800/30 text-left">
                                    <th class="px-4 py-3 font-medium">表名</th>
                                    <th class="px-4 py-3 font-medium">引擎</th>
                                    <th class="px-4 py-3 font-medium">行数</th>
                                    <th class="px-4 py-3 font-medium">数据大小</th>
                                    <th class="px-4 py-3 font-medium">索引大小</th>
                                    <th class="px-4 py-3 font-medium">创建时间</th>
                                    <th class="px-4 py-3 font-medium">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                    <td class="px-4 py-3 text-blue-400">ga_users</td>
                                    <td class="px-4 py-3">InnoDB</td>
                                    <td class="px-4 py-3">1,245</td>
                                    <td class="px-4 py-3">2.4 MB</td>
                                    <td class="px-4 py-3">1.2 MB</td>
                                    <td class="px-4 py-3">2023-06-15</td>
                                    <td class="px-4 py-3">
                                        <div class="flex space-x-2">
                                            <button class="text-blue-400 hover:text-blue-300" title="浏览">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-yellow-400 hover:text-yellow-300" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-green-400 hover:text-green-300" title="优化">
                                                <i class="fas fa-wrench"></i>
                                            </button>
                                            <button class="text-red-400 hover:text-red-300" title="删除">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                    <td class="px-4 py-3 text-blue-400">ga_posts</td>
                                    <td class="px-4 py-3">InnoDB</td>
                                    <td class="px-4 py-3">8,762</td>
                                    <td class="px-4 py-3">45.6 MB</td>
                                    <td class="px-4 py-3">12.8 MB</td>
                                    <td class="px-4 py-3">2023-06-15</td>
                                    <td class="px-4 py-3">
                                        <div class="flex space-x-2">
                                            <button class="text-blue-400 hover:text-blue-300" title="浏览">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-yellow-400 hover:text-yellow-300" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-green-400 hover:text-green-300" title="优化">
                                                <i class="fas fa-wrench"></i>
                                            </button>
                                            <button class="text-red-400 hover:text-red-300" title="删除">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                    <td class="px-4 py-3 text-blue-400">ga_comments</td>
                                    <td class="px-4 py-3">InnoDB</td>
                                    <td class="px-4 py-3">15,432</td>
                                    <td class="px-4 py-3">28.7 MB</td>
                                    <td class="px-4 py-3">8.4 MB</td>
                                    <td class="px-4 py-3">2023-06-15</td>
                                    <td class="px-4 py-3">
                                        <div class="flex space-x-2">
                                            <button class="text-blue-400 hover:text-blue-300" title="浏览">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-yellow-400 hover:text-yellow-300" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-green-400 hover:text-green-300" title="优化">
                                                <i class="fas fa-wrench"></i>
                                            </button>
                                            <button class="text-red-400 hover:text-red-300" title="删除">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                    <td class="px-4 py-3 text-blue-400">ga_media</td>
                                    <td class="px-4 py-3">InnoDB</td>
                                    <td class="px-4 py-3">3,567</td>
                                    <td class="px-4 py-3">120.5 MB</td>
                                    <td class="px-4 py-3">4.2 MB</td>
                                    <td class="px-4 py-3">2023-06-15</td>
                                    <td class="px-4 py-3">
                                        <div class="flex space-x-2">
                                            <button class="text-blue-400 hover:text-blue-300" title="浏览">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-yellow-400 hover:text-yellow-300" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-green-400 hover:text-green-300" title="优化">
                                                <i class="fas fa-wrench"></i>
                                            </button>
                                            <button class="text-red-400 hover:text-red-300" title="删除">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                    <td class="px-4 py-3 text-blue-400">ga_options</td>
                                    <td class="px-4 py-3">InnoDB</td>
                                    <td class="px-4 py-3">245</td>
                                    <td class="px-4 py-3">0.8 MB</td>
                                    <td class="px-4 py-3">0.3 MB</td>
                                    <td class="px-4 py-3">2023-06-15</td>
                                    <td class="px-4 py-3">
                                        <div class="flex space-x-2">
                                            <button class="text-blue-400 hover:text-blue-300" title="浏览">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-yellow-400 hover:text-yellow-300" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-green-400 hover:text-green-300" title="优化">
                                                <i class="fas fa-wrench"></i>
                                            </button>
                                            <button class="text-red-400 hover:text-red-300" title="删除">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页控制 -->
                    <div class="flex justify-between items-center mt-6">
                        <div class="text-sm text-gray-400">
                            显示 1-5 / 共 42 个表
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="px-3 py-1 bg-blue-600 text-white rounded-md">1</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">2</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">3</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">4</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- SQL 查询标签内容 -->
                <div id="query" class="tab-content p-6">
                    <div class="mb-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold">SQL 查询工具</h3>
                            <div class="flex space-x-2">
                                <button class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded-lg flex items-center text-sm">
                                    <i class="fas fa-folder-open mr-2"></i> 加载查询
                                </button>
                                <button class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded-lg flex items-center text-sm">
                                    <i class="fas fa-save mr-2"></i> 保存查询
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <textarea class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent h-40 font-mono" placeholder="输入 SQL 查询语句...">SELECT * FROM ga_users WHERE user_status = 'active' LIMIT 10;</textarea>
                        </div>
                        
                        <div class="flex justify-between">
                            <div>
                                <select class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option>选择数据库</option>
                                    <option selected>gacms_main</option>
                                    <option>gacms_test</option>
                                </select>
                            </div>
                            <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg flex items-center">
                                <i class="fas fa-play mr-2"></i> 执行查询
                            </button>
                        </div>
                    </div>
                    
                    <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5 mb-6">
                        <h3 class="text-lg font-semibold mb-4">查询结果</h3>
                        
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="bg-gray-800/30 text-left">
                                        <th class="px-4 py-3 font-medium">id</th>
                                        <th class="px-4 py-3 font-medium">username</th>
                                        <th class="px-4 py-3 font-medium">email</th>
                                        <th class="px-4 py-3 font-medium">role</th>
                                        <th class="px-4 py-3 font-medium">created_at</th>
                                        <th class="px-4 py-3 font-medium">last_login</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                        <td class="px-4 py-3">1</td>
                                        <td class="px-4 py-3">admin</td>
                                        <td class="px-4 py-3"><EMAIL></td>
                                        <td class="px-4 py-3"><span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full text-xs">管理员</span></td>
                                        <td class="px-4 py-3">2023-01-15 08:30:45</td>
                                        <td class="px-4 py-3">2023-06-20 14:25:12</td>
                                    </tr>
                                    <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                        <td class="px-4 py-3">2</td>
                                        <td class="px-4 py-3">editor</td>
                                        <td class="px-4 py-3"><EMAIL></td>
                                        <td class="px-4 py-3"><span class="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs">编辑</span></td>
                                        <td class="px-4 py-3">2023-02-10 10:15:22</td>
                                        <td class="px-4 py-3">2023-06-19 09:45:30</td>
                                    </tr>
                                    <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                        <td class="px-4 py-3">3</td>
                                        <td class="px-4 py-3">author</td>
                                        <td class="px-4 py-3"><EMAIL></td>
                                        <td class="px-4 py-3"><span class="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded-full text-xs">作者</span></td>
                                        <td class="px-4 py-3">2023-03-05 15:40:18</td>
                                        <td class="px-4 py-3">2023-06-18 16:20:45</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="mt-4 text-sm text-gray-400">
                            查询执行时间: 0.0032 秒 | 返回行数: 3
                        </div>
                    </div>
                    
                    <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                        <h3 class="text-lg font-semibold mb-4">查询历史</h3>
                        
                        <div class="space-y-3">
                            <div class="p-3 bg-gray-800/30 rounded-lg hover:bg-gray-800/40 cursor-pointer">
                                <div class="flex justify-between items-center">
                                    <div class="font-mono text-sm text-blue-400 truncate pr-4">SELECT * FROM ga_users WHERE user_status = 'active' LIMIT 10;</div>
                                    <div class="text-xs text-gray-400">2023-06-20 15:30</div>
                                </div>
                            </div>
                            <div class="p-3 bg-gray-800/30 rounded-lg hover:bg-gray-800/40 cursor-pointer">
                                <div class="flex justify-between items-center">
                                    <div class="font-mono text-sm text-blue-400 truncate pr-4">SELECT post_title, post_date FROM ga_posts ORDER BY post_date DESC LIMIT 20;</div>
                                    <div class="text-xs text-gray-400">2023-06-20 14:45</div>
                                </div>
                            </div>
                            <div class="p-3 bg-gray-800/30 rounded-lg hover:bg-gray-800/40 cursor-pointer">
                                <div class="flex justify-between items-center">
                                    <div class="font-mono text-sm text-blue-400 truncate pr-4">UPDATE ga_options SET option_value = 'new_value' WHERE option_name = 'site_title';</div>
                                    <div class="text-xs text-gray-400">2023-06-19 11:20</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 备份与恢复标签内容 -->
                <div id="backup" class="tab-content p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 备份部分 -->
                        <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                            <h3 class="text-lg font-semibold mb-4">创建备份</h3>
                            
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-gray-300 mb-2">备份名称</label>
                                    <input type="text" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="例如：每周备份-2023-06-20">
                                </div>
                                
                                <div>
                                    <label class="block text-gray-300 mb-2">备份内容</label>
                                    <div class="space-y-2">
                                        <label class="flex items-center">
                                            <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                            <span class="ml-2 text-gray-300">数据库结构</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                            <span class="ml-2 text-gray-300">数据库数据</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600">
                                            <span class="ml-2 text-gray-300">媒体文件</span>
                                        </label>
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-gray-300 mb-2">压缩选项</label>
                                    <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option>不压缩</option>
                                        <option selected>GZIP 压缩</option>
                                        <option>ZIP 压缩</option>
                                    </select>
                                </div>
                                
                                <div class="pt-2">
                                    <button class="w-full bg-gradient-to-r from-blue-500 to-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center action-button">
                                        <i class="fas fa-download mr-2"></i> 创建备份
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 恢复部分 -->
                        <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                            <h3 class="text-lg font-semibold mb-4">恢复备份</h3>
                            
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">选择备份文件</label>
                                <div class="relative">
                                    <input type="file" class="hidden" id="backupFile">
                                    <label for="backupFile" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-gray-300 flex items-center justify-between cursor-pointer hover:bg-gray-600">
                                        <span>选择备份文件...</span>
                                        <i class="fas fa-upload"></i>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">恢复选项</label>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                        <span class="ml-2 text-gray-300">恢复前备份当前数据</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600">
                                        <span class="ml-2 text-gray-300">覆盖现有表</span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="pt-2">
                                <button class="w-full bg-gradient-to-r from-yellow-500 to-yellow-700 text-white px-4 py-2 rounded-lg flex items-center justify-center action-button">
                                    <i class="fas fa-sync-alt mr-2"></i> 恢复备份
                                </button>
                            </div>
                            
                            <div class="mt-6">
                                <h4 class="font-medium text-gray-300 mb-3">最近备份</h4>
                                <div class="space-y-2">
                                    <div class="flex justify-between items-center p-2 bg-gray-800/30 rounded-lg">
                                        <div>
                                            <div class="text-sm font-medium">每周备份-2023-06-15</div>
                                            <div class="text-xs text-gray-400">大小: 45.2 MB | 2023-06-15 03:00</div>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-400 hover:text-blue-300" title="下载">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="text-yellow-400 hover:text-yellow-300" title="恢复">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                            <button class="text-red-400 hover:text-red-300" title="删除">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="flex justify-between items-center p-2 bg-gray-800/30 rounded-lg">
                                        <div>
                                            <div class="text-sm font-medium">每周备份-2023-06-08</div>
                                            <div class="text-xs text-gray-400">大小: 44.8 MB | 2023-06-08 03:00</div>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-400 hover:text-blue-300" title="下载">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="text-yellow-400 hover:text-yellow-300" title="恢复">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                            <button class="text-red-400 hover:text-red-300" title="删除">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 优化标签内容 -->
                <div id="optimize" class="tab-content p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 数据库优化 -->
                        <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                            <h3 class="text-lg font-semibold mb-4">数据库优化</h3>
                            
                            <div class="space-y-4">
                                <div class="flex justify-between items-center p-3 bg-gray-800/30 rounded-lg">
                                    <div>
                                        <div class="font-medium">优化表</div>
                                        <div class="text-sm text-gray-400">整理表空间，减少碎片</div>
                                    </div>
                                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                                        开始优化
                                    </button>
                                </div>
                                
                                <div class="flex justify-between items-center p-3 bg-gray-800/30 rounded-lg">
                                    <div>
                                        <div class="font-medium">修复表</div>
                                        <div class="text-sm text-gray-400">检查并修复损坏的表</div>
                                    </div>
                                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                                        开始修复
                                    </button>
                                </div>
                                
                                <div class="flex justify-between items-center p-3 bg-gray-800/30 rounded-lg">
                                    <div>
                                        <div class="font-medium">分析表</div>
                                        <div class="text-sm text-gray-400">更新表统计信息以优化查询</div>
                                    </div>
                                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                                        开始分析
                                    </button>
                                </div>
                                
                                <div class="flex justify-between items-center p-3 bg-gray-800/30 rounded-lg">
                                    <div>
                                        <div class="font-medium">清理冗余数据</div>
                                        <div class="text-sm text-gray-400">删除过期的临时数据和日志</div>
                                    </div>
                                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                                        开始清理
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 性能监控 -->
                        <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                            <h3 class="text-lg font-semibold mb-4">性能监控</h3>
                            
                            <div class="space-y-4">
                                <div>
                                    <div class="flex justify-between mb-2">
                                        <span class="text-gray-300">查询缓存命中率</span>
                                        <span class="text-blue-400">78%</span>
                                    </div>
                                    <div class="w-full bg-gray-700 rounded-full h-2">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: 78%"></div>
                                    </div>
                                </div>
                                
                                <div>
                                    <div class="flex justify-between mb-2">
                                        <span class="text-gray-300">索引使用率</span>
                                        <span class="text-green-400">92%</span>
                                    </div>
                                    <div class="w-full bg-gray-700 rounded-full h-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: 92%"></div>
                                    </div>
                                </div>
                                
                                <div>
                                    <div class="flex justify-between mb-2">
                                        <span class="text-gray-300">连接使用率</span>
                                        <span class="text-yellow-400">45%</span>
                                    </div>
                                    <div class="w-full bg-gray-700 rounded-full h-2">
                                        <div class="bg-yellow-500 h-2 rounded-full" style="width: 45%"></div>
                                    </div>
                                </div>
                                
                                <div>
                                    <div class="flex justify-between mb-2">
                                        <span class="text-gray-300">临时表使用</span>
                                        <span class="text-red-400">28%</span>
                                    </div>
                                    <div class="w-full bg-gray-700 rounded-full h-2">
                                        <div class="bg-red-500 h-2 rounded-full" style="width: 28%"></div>
                                    </div>
                                </div>
                                
                                <div class="pt-2">
                                    <button class="w-full bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center justify-center action-button">
                                        <i class="fas fa-chart-line mr-2"></i> 查看详细性能报告
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 标签页切换功能
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tabId = button.getAttribute('data-tab');
                    
                    // 移除所有标签按钮的活动状态
                    tabButtons.forEach(btn => {
                        btn.classList.remove('tab-active');
                        btn.classList.add('text-gray-400');
                        btn.classList.remove('text-blue-400');
                    });
                    
                    // 添加当前标签按钮的活动状态
                    button.classList.add('tab-active');
                    button.classList.remove('text-gray-400');
                    button.classList.add('text-blue-400');
                    
                    // 隐藏所有标签内容
                    tabContents.forEach(content => {
                        content.classList.remove('active');
                    });
                    
                    // 显示当前标签内容
                    document.getElementById(tabId).classList.add('active');
                });
            });
        });
    </script>
</body>
</html>