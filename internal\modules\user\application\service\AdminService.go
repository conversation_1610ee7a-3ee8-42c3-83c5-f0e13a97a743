/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/user/application/service/AdminService.go
 * @Description: Service for managing admin users, with domain error handling.
 *
 * © 2025 GACMS. All rights reserved.
 */
package service

import (
	"context"
	"errors"
	"fmt"
	"gacms/internal/modules/user/application/dto"
	"gacms/internal/modules/user/domain"
	"gacms/internal/modules/user/domain/contract"
	"gacms/internal/modules/user/domain/model"
	pkgContract "gacms/pkg/contract"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

// AdminService provides business logic for admin users.
type AdminService struct {
	repo   contract.AdminRepository
	appCtx pkgContract.AppContext
}

// NewAdminService creates a new AdminService.
func NewAdminService(repo contract.AdminRepository, appCtx pkgContract.AppContext) *AdminService {
	return &AdminService{
		repo:   repo,
		appCtx: appCtx,
	}
}

// Login handles administrator login.
func (s *AdminService) Login(ctx context.Context, input *dto.AdminLoginDTO) (string, error) {
	// SiteID must be present in the context, placed by SiteResolver middleware.
	siteID, ok := s.appCtx.Auth().SiteIDFrom(ctx)
	if !ok {
		// This indicates a server-side misconfiguration, as SiteResolver should always run first.
		return "", errors.New("could not determine site context")
	}

	admin, err := s.repo.GetAdminByUsername(ctx, input.Username)
	if err != nil {
		if errors.Is(err, domain.ErrNotFound) {
			return "", domain.ErrAuthenticationFailed
		}
		return "", err // Propagate other errors
	}

	// Compare the provided password with the stored hash
	err = bcrypt.CompareHashAndPassword([]byte(admin.Password), []byte(input.Password))
	if err != nil {
		return "", domain.ErrAuthenticationFailed
	}

	if !admin.IsActive {
		return "", errors.New("user account is inactive")
	}

	// TODO: Replace with an API call to the Admin module via AppContext
	// For now, assuming AppContext can provide a way to check this.
	// This functionality might need to be added to the AppContext contract.
	// isAuthorized, err := s.appCtx.SiteChecker().IsAdminAuthorizedForSite(ctx, admin.ID, siteID)
	// if err != nil {
	// 	return "", errors.New("failed to check site authorization")
	// }
	// if !isAuthorized {
	// 	return "", errors.New("user not authorized for this site")
	// }

	// Prepare claims for JWT
	var roleSlugs []string
	for _, role := range admin.Roles {
		roleSlugs = append(roleSlugs, role.Slug)
	}
	claims := map[string]interface{}{
		"user_id": admin.ID,
		"email":   admin.Email,
		"roles":   roleSlugs,
		"site_id": siteID, // Add siteID to claims for context propagation
	}

	// Generate JWT using the token processor from AppContext
	token, err := s.appCtx.Auth().GenerateToken(claims)
	if err != nil {
		return "", errors.New("failed to generate token")
	}

	s.recordActionLog(ctx, fmt.Sprintf("Admin user '%s' logged in successfully.", admin.Username), "success")

	return token, nil
}

// CreateAdmin creates a new administrator.
func (s *AdminService) CreateAdmin(ctx context.Context, input *dto.AdminCreateDTO) (*model.Admin, error) {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(input.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	admin := &model.Admin{
		Username: input.Username,
		Email:    input.Email,
		Password: string(hashedPassword),
		IsActive: true,
	}

	err = s.repo.CreateAdmin(ctx, admin)
	if err != nil {
		return nil, err
	}

	if len(input.RoleIDs) > 0 {
		if err := s.repo.AssignRolesToAdmin(ctx, admin.UUID, input.RoleIDs); err != nil {
			// Handle case where admin is created but role assignment fails
			return admin, err
		}
	}

	// Refetch admin to include roles
	createdAdmin, err := s.repo.GetAdminByID(ctx, admin.UUID)
	if err != nil {
		return admin, nil // Return admin without roles if refetch fails
	}

	s.recordActionLog(ctx, fmt.Sprintf("New admin user '%s' created successfully.", createdAdmin.Username), "success")

	return createdAdmin, nil
}

// ListAdmins retrieves a paginated list of administrators.
func (s *AdminService) ListAdmins(ctx context.Context, page, pageSize int) ([]*model.Admin, int64, error) {
	return s.repo.ListAdmins(ctx, page, pageSize)
}

// GetAdminByID retrieves an admin by their UUID.
func (s *AdminService) GetAdminByID(ctx context.Context, id uuid.UUID) (*model.Admin, error) {
	return s.repo.GetAdminByID(ctx, id)
}

// UpdateAdmin updates an existing administrator.
func (s *AdminService) UpdateAdmin(ctx context.Context, id uuid.UUID, input *dto.AdminUpdateDTO) (*model.Admin, error) {
	admin, err := s.repo.GetAdminByID(ctx, id)
	if err != nil {
		// The service layer can decide to return the domain error directly,
		// as it's part of the contract with the presentation layer.
		return nil, err
	}

	if input.Email != nil {
		admin.Email = *input.Email
	}
	if input.IsActive != nil {
		admin.IsActive = *input.IsActive
	}
	if input.Password != nil && *input.Password != "" {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(*input.Password), bcrypt.DefaultCost)
		if err != nil {
			return nil, err
		}
		admin.Password = string(hashedPassword)
	}

	if err := s.repo.UpdateAdmin(ctx, admin); err != nil {
		return nil, err
	}

	s.recordActionLog(ctx, fmt.Sprintf("Admin user '%s' (ID: %s) updated successfully.", admin.Username, id.String()), "success")

	return admin, nil
}

// DeleteAdmin deletes an administrator.
func (s *AdminService) DeleteAdmin(ctx context.Context, id uuid.UUID) error {
	// It's good practice to fetch the entity before deleting to know what was deleted.
	admin, err := s.repo.GetAdminByID(ctx, id)
	if err != nil {
		// If it's already not found, we can consider the deletion "successful" idempotently.
		if errors.Is(err, domain.ErrNotFound) {
			return nil
		}
		return err
	}

	if err := s.repo.DeleteAdmin(ctx, id); err != nil {
		return err
	}

	s.recordActionLog(ctx, fmt.Sprintf("Admin user '%s' (ID: %s) deleted successfully.", admin.Username, id.String()), "success")

	return nil
}

// AssignRolesToAdmin assigns a set of roles to an administrator.
func (s *AdminService) AssignRolesToAdmin(ctx context.Context, adminID uuid.UUID, roleIDs []uint) error {
	if err := s.repo.AssignRolesToAdmin(ctx, adminID, roleIDs); err != nil {
		return err
	}

	s.recordActionLog(ctx, fmt.Sprintf("Roles assigned to admin (ID: %s) successfully.", adminID.String()), "success")

	return nil
}

// recordActionLog is a helper to asynchronously record an action to the actionlog service.
func (s *AdminService) recordActionLog(ctx context.Context, description string, status string) {
	go func() {
		// We create a new context because the original request's context might be cancelled
		// after the HTTP response is sent. We still need to propagate the auth info.
		bgCtx := context.Background()
		if token, ok := s.appCtx.Auth().TokenFrom(ctx); ok {
			bgCtx = s.appCtx.Auth().ContextWithToken(bgCtx, token)
		}

		payload := map[string]string{
			"description": description,
			"status":      status,
		}
		_, err := s.appCtx.APIClient().Post(bgCtx, "actionlog", "/logs", payload)
		if err != nil {
			// Use the system logger to record the failure of the API call.
			s.appCtx.Logger().Error(context.Background(), "Failed to record action log via API", "error", err)
		}
	}()
}

func (s *AdminService) GetUserPermissions(ctx context.Context, userID uint) ([]model.AdminPermission, error) {
	user, err := s.repo.GetByIDWithRoles(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Use a map to collect unique permissions
	permissionMap := make(map[uint]model.AdminPermission)

	for _, role := range user.Roles {
		// In a more complex setup, you might need to load permissions for each role
		// if they are not preloaded. Here we assume they are.
		for _, perm := range role.Permissions {
			permissionMap[perm.ID] = *perm
		}
	}

	// Convert map to slice
	permissions := make([]model.AdminPermission, 0, len(permissionMap))
	for _, perm := range permissionMap {
		permissions = append(permissions, perm)
	}

	return permissions, nil
}

func (s *AdminService) GetAdminWithRoles(ctx context.Context, adminID uint) (*model.Admin, error) {
	return s.repo.FindByIDWithRoles(ctx, adminID)
}

func (s *AdminService) GetAdmins(ctx context.Context, page, pageSize int) ([]*model.Admin, int64, error) {
	return s.repo.FindAll(ctx, page, pageSize)
}

// GetAdminStats retrieves statistics about admin users.
func (s *AdminService) GetAdminStats(ctx context.Context) (map[string]interface{}, error) {
	total, err := s.repo.CountAdmins(ctx)
	if err != nil {
		return nil, err
	}
	stats := map[string]interface{}{
		"total": total,
	}
	return stats, nil
} 