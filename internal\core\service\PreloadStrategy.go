/*
Author: <PERSON><PERSON>eh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package service

import (
	"context"
	"time"

	"gacms/internal/core/domain/model"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// PreloadStrategy 预加载策略接口
type PreloadStrategy interface {
	// 策略名称
	Name() string
	
	// 是否应该预加载指定模块
	ShouldPreload(moduleName string, moduleType model.ModuleType) bool
	
	// 获取预加载优先级
	GetPriority(moduleName string) int
	
	// 获取预加载时机
	GetTiming() PreloadTiming
}

// PreloadTiming 预加载时机
type PreloadTiming string

const (
	PreloadOnStartup    PreloadTiming = "startup"     // 系统启动时
	PreloadOnFirstUse   PreloadTiming = "first_use"   // 首次使用时
	PreloadOnSchedule   PreloadTiming = "schedule"    // 定时预加载
	PreloadOnDemand     PreloadTiming = "demand"      // 按需预加载
)

// PreloadStrategyManager 预加载策略管理器
type PreloadStrategyManager interface {
	// 注册策略
	RegisterStrategy(strategy PreloadStrategy)
	
	// 获取所有策略
	GetStrategies() []PreloadStrategy
	
	// 执行预加载
	ExecutePreload(ctx context.Context, timing PreloadTiming) error
	
	// 获取预加载建议
	GetPreloadRecommendations() []PreloadRecommendation
}

// PreloadRecommendation 预加载建议
type PreloadRecommendation struct {
	ModuleName  string        `json:"module_name"`
	Reason      string        `json:"reason"`
	Priority    int           `json:"priority"`
	Timing      PreloadTiming `json:"timing"`
	EstimatedTime time.Duration `json:"estimated_time"`
}

// DefaultPreloadStrategyManager 默认预加载策略管理器
type DefaultPreloadStrategyManager struct {
	strategies      []PreloadStrategy
	perfManager     ModulePerformanceManager
	configManager   ModuleConfigManager
	moduleFactory   *ModuleProxyFactory
	logger          *zap.Logger
}

// PreloadStrategyManagerParams fx依赖注入参数
type PreloadStrategyManagerParams struct {
	fx.In

	PerfManager   ModulePerformanceManager
	ConfigManager ModuleConfigManager
	ModuleFactory *ModuleProxyFactory
	Logger        *zap.Logger
}

// NewDefaultPreloadStrategyManager 创建默认预加载策略管理器
func NewDefaultPreloadStrategyManager(params PreloadStrategyManagerParams) PreloadStrategyManager {
	manager := &DefaultPreloadStrategyManager{
		strategies:    make([]PreloadStrategy, 0),
		perfManager:   params.PerfManager,
		configManager: params.ConfigManager,
		moduleFactory: params.ModuleFactory,
		logger:        params.Logger,
	}
	
	// 注册默认策略
	manager.registerDefaultStrategies()
	
	return manager
}

// RegisterStrategy 注册策略
func (m *DefaultPreloadStrategyManager) RegisterStrategy(strategy PreloadStrategy) {
	m.strategies = append(m.strategies, strategy)
	m.logger.Debug("Preload strategy registered", zap.String("strategy", strategy.Name()))
}

// GetStrategies 获取所有策略
func (m *DefaultPreloadStrategyManager) GetStrategies() []PreloadStrategy {
	return m.strategies
}

// ExecutePreload 执行预加载
func (m *DefaultPreloadStrategyManager) ExecutePreload(ctx context.Context, timing PreloadTiming) error {
	m.logger.Info("Executing preload", zap.String("timing", string(timing)))
	
	// 获取所有模块配置
	configs, err := m.configManager.GetAllModuleConfigs()
	if err != nil {
		return err
	}
	
	// 收集需要预加载的模块
	var preloadItems []PreloadItem
	
	for _, config := range configs {
		if !config.Enabled {
			continue
		}
		
		// 检查所有策略
		shouldPreload := false
		priority := 100 // 默认优先级
		
		for _, strategy := range m.strategies {
			if strategy.GetTiming() == timing && strategy.ShouldPreload(config.ModuleName, config.ModuleType) {
				shouldPreload = true
				strategyPriority := strategy.GetPriority(config.ModuleName)
				if strategyPriority < priority {
					priority = strategyPriority
				}
			}
		}
		
		if shouldPreload {
			preloadItems = append(preloadItems, PreloadItem{
				ModuleName: config.ModuleName,
				Priority:   priority,
			})
		}
	}
	
	// 执行预加载
	if len(preloadItems) > 0 {
		// 添加到性能管理器的预加载列表
		for _, item := range preloadItems {
			m.perfManager.AddToPreloadList(item.ModuleName, item.Priority)
		}
		
		// 执行预加载
		return m.perfManager.ExecutePreload(ctx)
	}
	
	return nil
}

// GetPreloadRecommendations 获取预加载建议
func (m *DefaultPreloadStrategyManager) GetPreloadRecommendations() []PreloadRecommendation {
	var recommendations []PreloadRecommendation
	
	// 获取性能统计
	stats := m.perfManager.GetOverallStats()
	
	// 基于使用频率推荐
	for name, moduleStats := range stats.ModuleStats {
		if moduleStats.LoadCount > 5 && moduleStats.AverageTime > 50*time.Millisecond {
			recommendations = append(recommendations, PreloadRecommendation{
				ModuleName:    name,
				Reason:        "Frequently used module with slow load time",
				Priority:      10,
				Timing:        PreloadOnStartup,
				EstimatedTime: moduleStats.AverageTime,
			})
		}
	}
	
	return recommendations
}

// registerDefaultStrategies 注册默认策略
func (m *DefaultPreloadStrategyManager) registerDefaultStrategies() {
	// 核心模块策略
	m.RegisterStrategy(&CoreModuleStrategy{})
	
	// 频繁使用策略
	m.RegisterStrategy(&FrequentUseStrategy{perfManager: m.perfManager})
	
	// 慢加载策略
	m.RegisterStrategy(&SlowLoadStrategy{perfManager: m.perfManager})
}

// CoreModuleStrategy 核心模块预加载策略
type CoreModuleStrategy struct{}

func (s *CoreModuleStrategy) Name() string {
	return "core_module"
}

func (s *CoreModuleStrategy) ShouldPreload(moduleName string, moduleType model.ModuleType) bool {
	return moduleType == model.ModuleTypeCore
}

func (s *CoreModuleStrategy) GetPriority(moduleName string) int {
	// 核心模块按重要性排序
	priorities := map[string]int{
		"user":      1,
		"site":      2,
		"actionlog": 3,
		"admin":     4,
		"content":   5,
	}
	
	if priority, exists := priorities[moduleName]; exists {
		return priority
	}
	
	return 10 // 默认核心模块优先级
}

func (s *CoreModuleStrategy) GetTiming() PreloadTiming {
	return PreloadOnStartup
}

// FrequentUseStrategy 频繁使用模块预加载策略
type FrequentUseStrategy struct {
	perfManager ModulePerformanceManager
}

func (s *FrequentUseStrategy) Name() string {
	return "frequent_use"
}

func (s *FrequentUseStrategy) ShouldPreload(moduleName string, moduleType model.ModuleType) bool {
	stats := s.perfManager.GetLoadStats(moduleName)
	return stats != nil && stats.LoadCount > 10
}

func (s *FrequentUseStrategy) GetPriority(moduleName string) int {
	stats := s.perfManager.GetLoadStats(moduleName)
	if stats == nil {
		return 50
	}
	
	// 使用次数越多，优先级越高
	if stats.LoadCount > 50 {
		return 20
	} else if stats.LoadCount > 20 {
		return 30
	} else {
		return 40
	}
}

func (s *FrequentUseStrategy) GetTiming() PreloadTiming {
	return PreloadOnStartup
}

// SlowLoadStrategy 慢加载模块预加载策略
type SlowLoadStrategy struct {
	perfManager ModulePerformanceManager
}

func (s *SlowLoadStrategy) Name() string {
	return "slow_load"
}

func (s *SlowLoadStrategy) ShouldPreload(moduleName string, moduleType model.ModuleType) bool {
	stats := s.perfManager.GetLoadStats(moduleName)
	return stats != nil && stats.AverageTime > 100*time.Millisecond
}

func (s *SlowLoadStrategy) GetPriority(moduleName string) int {
	stats := s.perfManager.GetLoadStats(moduleName)
	if stats == nil {
		return 50
	}
	
	// 加载时间越长，优先级越高
	if stats.AverageTime > 500*time.Millisecond {
		return 15
	} else if stats.AverageTime > 200*time.Millisecond {
		return 25
	} else {
		return 35
	}
}

func (s *SlowLoadStrategy) GetTiming() PreloadTiming {
	return PreloadOnStartup
}
