<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 仪表盘</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .content-section-title::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 5rem;
            height: 3px;
            background: linear-gradient(to right, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .user-link::after, .article-link::after, .version-link::after, .plugin-link::after, .comment-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 1px;
            background: linear-gradient(to right, #007bff, #00c6ff);
            transition: width 0.2s ease;
        }
        
        .user-link:hover, .article-link:hover, .version-link:hover, .plugin-link:hover, .comment-link:hover {
            color: #00c6ff;
        }
        
        .user-link:hover::after, .article-link:hover::after, .version-link:hover::after, .plugin-link:hover::after, .comment-link:hover::after {
            width: 100%;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            background: linear-gradient(145deg, #1e1e1e, #2a2a2a);
            border-left: 4px solid #007bff;
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            backdrop-filter: blur(20px);
            z-index: 1000;
            transform: translateX(120%);
            opacity: 0;
            transition: all 0.2s ease;
            max-width: 350px;
        }
        
        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10">
        <!-- 侧边栏内容将通过JavaScript从模板加载 -->
    </aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- Admin Info and System Info Cards -->
            <div class="content-section bg-gray-800/10 flex flex-wrap justify-between border border-gray-700 rounded-xl p-6 mb-6">
                <div class="w-full lg:w-1/2 lg:pr-4">
                    <h3 class="text-xl font-bold text-white mb-4 relative pl-3">快捷操作</h3>
                    <div class="flex flex-wrap gap-3">
                        <a href="post_edit.html" class="flex items-center justify-center bg-gradient-to-r text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-plus-circle text-white"></i>
                                </span>
                                新建文章
                            </span>
                        </a>
                        <a href="category_new.html" class="flex items-center justify-center bg-gradient-to-r text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-folder-plus text-white"></i>
                                </span>
                                新建栏目
                            </span>
                        </a>
                        <a href="system_set.html" class="flex items-center justify-center bg-gradient-to-r text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-cog text-white"></i>
                                </span>
                                系统设置
                            </span>
                        </a>
                        <a href="#" id="clearCacheBtn" class="flex items-center justify-center bg-gradient-to-r text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-broom text-white"></i>
                                </span>
                                清理缓存
                            </span>
                        </a>
                    </div>
                </div>
                <!-- Admin Info Card -->
                <div class="w-full lg:w-1/2 mt-6 lg:mt-0">
                    <div class="border border-gray-700 rounded-xl shadow-xl hover:shadow-2xl transition-shadow duration-300 p-4">
                        <div class="flex items-center py-2">
                            <img class="w-16 h-16 rounded-full object-cover border-2 border-white" src="./assets/images/avatar.jpg" alt="Admin Avatar">
                            <div class="ml-4">
                                <h3 class="text-xl font-semibold text-green-400">Sherlly</h3>
                                <p class="text-sm text-gray-400">超级管理员</p>
                                <p class="text-xs text-gray-500 mt-1">登录 IP: ***************</p>
                                <p class="text-xs text-gray-500 mt-1">上次登录: 2025-05-29 10:00</p>
                            </div>
                        </div>
                        <ul class="flex gap-2 pl-2 space-y-1 text-sm border-t border-gray-700 pt-3">
                            <h3 class="font-medium">运行环境：</h3>
                            <li class="text-xs text-gray-300"><span class="font-medium">Ubuntu 22.04</span></li>
                            <li class="text-xs text-gray-300"><span class="font-medium">Nginx 1.21.0</span></li>
                            <li class="text-xs text-gray-300"><span class="font-medium">MySQL 8.0.32</span></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 统计卡片区域 -->
            <div class="flex flex-wrap gap-6 mb-8">
                <!-- 文章统计卡片 -->
                <div class="bg-gray-800/10 min-w-[260px] flex-1 border border-gray-700 rounded-xl px-6 py-5 flex items-center transition-all hover:shadow-lg hover:border-blue-500/30 relative overflow-hidden">
                    <div class="text-3xl text-blue-500 mr-6">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-bold text-white mb-2">总文章数</h3>
                        <div class="flex items-end">
                            <span class="text-2xl font-bold">128</span>
                            <span class="ml-2 text-sm text-green-400">↑12%</span>
                        </div>
                    </div>
                </div>
                <!-- 评论统计卡片 -->
                <div class="bg-gray-800/10 min-w-[260px] flex-1 border border-gray-700 rounded-xl px-6 py-5 flex items-center transition-all hover:shadow-lg hover:border-purple-500/30 relative overflow-hidden">
                    <div class="text-3xl text-purple-500 mr-6">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-bold text-white mb-2">待审评论</h3>
                        <div class="flex items-end">
                            <span class="text-2xl font-bold">356</span>
                            <span class="ml-2 text-sm text-green-400">↑8%</span>
                        </div>
                    </div>
                </div>
                <!-- 用户统计卡片 -->
                <div class="bg-gray-800/10 min-w-[260px] flex-1 border border-gray-700 rounded-xl px-6 py-5 flex items-center transition-all hover:shadow-lg hover:border-yellow-500/30 relative overflow-hidden">
                    <div class="text-3xl text-yellow-500 mr-6">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-bold text-white mb-2">注册用户</h3>
                        <div class="flex items-end">
                            <span class="text-2xl font-bold">1,247</span>
                            <span class="ml-2 text-sm text-green-400">↑15%</span>
                        </div>
                    </div>
                </div>
                
                <!-- 安全状态卡片 -->
                <div class="bg-gray-800/10 min-w-[260px] flex-1 border border-gray-700 rounded-xl px-6 py-5 flex items-center transition-all hover:shadow-lg hover:border-green-500/30 relative overflow-hidden">
                    <div class="text-3xl text-green-500 mr-6">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-bold text-white mb-2">安全状态</h3>
                        <div class="flex items-end">
                            <span class="text-2xl font-bold text-green-400">正常</span>
                        </div>
                        <p class="text-sm text-gray-400 mt-1">最后备份: 2天前</p>
                    </div>
                </div>
            </div>

            <!-- 访问统计图表 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-white relative pl-3">访问统计</h2>
                </div>
                <!-- Flex container for Chart and Region Stats -->
                <div class="flex flex-col lg:flex-row gap-4">
                    <!-- Left Column: Existing Visit Chart -->
                    <div class="w-full lg:w-1/2">
                        <div class="flex items-center justify-between px-4">
                            <div class="flex text-sm text-gray-400 space-x-4">
                                <span id="chartTodayStats">今日: 1,000</span>
                                <span id="chartMonthStats">本月: 46,500</span>
                                <span id="chartYearStats">本年: 567,000</span>
                            </div>
                            <div class="flex rounded-lg p-1">
                                <button class="period-tab px-3 py-1.5 rounded-md text-sm font-medium hover:text-white active:bg-blue-500 bg-gray-700 active" data-period="7">7天</button>
                                <button class="period-tab px-3 py-1.5 rounded-md text-sm font-medium text-gray-300 hover:text-white active:bg-blue-500 bg-gray-700 ml-1" data-period="30">30天</button>
                                <button class="period-tab px-3 py-1.5 rounded-md text-sm font-medium text-gray-300 hover:text-white active:bg-blue-500 bg-gray-700 ml-1" data-period="90">90天</button>
                                <button class="period-tab px-3 py-1.5 rounded-md text-sm font-medium text-gray-300 hover:text-white active:bg-blue-500 bg-gray-700 ml-1" data-period="365">1年</button>
                            </div>
                        </div>
                        <div class="w-full h-72 rounded-lg p-4">
                            <!-- 图表占位 -->
                            <div class="w-full h-full flex items-center justify-center text-gray-500">
                                <canvas id="visitChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <!-- Right Column: New IP Region Stats -->
                    <div class="w-full lg:w-1/2">
                        <div class="flex justify-end px-4">
                            <div class="flex rounded-md">
                                <button id="tab-china-stats" role="tab" aria-selected="true" aria-controls="panel-china-stats" class="tab-button px-4 py-2 text-sm font-medium bg-gray-700 hover:text-white active:bg-blue-500 rounded-l active">中国</button>
                                <button id="tab-world-stats" role="tab" aria-selected="false" aria-controls="panel-world-stats" class="tab-button px-4 py-2 text-sm font-medium text-gray-300 bg-gray-700 hover:text-white active:bg-blue-500 rounded-r">世界</button>
                            </div>
                        </div>
                        <!-- Tab Content for Region Stats -->
                        <div class="w-full rounded-lg h-72 p-4">
                            <div id="panel-china-stats" class="tab-panel" role="tabpanel" tabindex="0" aria-labelledby="tab-china-stats">
                                <div class="flex flex-col xl:flex-row">
                                    <div class="xl:w-4/5">
                                        <div id="china-map-container" class="w-full h-auto min-h-[281px] sm:h-64 rounded flex items-center justify-center text-gray-400 text-sm">
                                            <noscript>请启用JavaScript以查看地图。</noscript>
                                        </div>
                                    </div>
                                    <div class="xl:w-1/5 flex justify-center">
                                        <ul id="china-top-provinces" class="flex flex-row flex-wrap items-center xl:flex-col xl:space-y-2 text-xs sm:text-sm text-gray-300">
                                            <li class="mx-2 my-1 xl:w-full xl:m-0"><span class="font-medium">广东:</span> 9.2k</li>
                                            <li class="mx-2 my-1 xl:w-full xl:m-0"><span class="font-medium">上海:</span> 8.6k</li>
                                            <li class="mx-2 my-1 xl:w-full xl:m-0"><span class="font-medium">河南:</span> 8.6k</li>
                                            <li class="mx-2 my-1 xl:w-full xl:m-0"><span class="font-medium">山东:</span> 8.4k</li>
                                            <li class="mx-2 my-1 xl:w-full xl:m-0"><span class="font-medium">北京:</span> 7.8k</li>
                                            <li class="mx-2 my-1 xl:w-full xl:m-0"><span class="font-medium">四川:</span> 7.8k</li>
                                            <li class="mx-2 my-1 xl:w-full xl:m-0"><span class="font-medium">浙江:</span> 6.4k</li>
                                            <li class="mx-2 my-1 xl:w-full xl:m-0"><span class="font-medium">江苏:</span> 5.1k</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <!-- World Stats Panel -->
                            <div id="panel-world-stats" class="tab-panel hidden" role="tabpanel" tabindex="-1" aria-labelledby="tab-world-stats">
                                <div class="flex flex-col xl:flex-row gap-4">
                                    <div class="xl:w-4/5">
                                        <div id="world-map-container" class="w-full h-auto min-h-[281px] sm:h-64 rounded flex items-center justify-center text-gray-400 text-sm">
                                            <noscript>请启用JavaScript以查看地图。</noscript>
                                        </div>
                                    </div>
                                    <div class="xl:w-1/5 flex justify-center">
                                        <ul id="world-top-countries" class="flex flex-row flex-wrap items-center xl:flex-col xl:space-y-2 text-xs sm:text-sm text-gray-300">
                                            <li class="mx-2 my-1 xl:w-full xl:m-0"><span class="font-medium">中国:</span> 56.0k</li>
                                            <li class="mx-2 my-1 xl:w-full xl:m-0"><span class="font-medium">美国:</span> 22.0k</li>
                                            <li class="mx-2 my-1 xl:w-full xl:m-0"><span class="font-medium">德国:</span> 18.0k</li>
                                            <li class="mx-2 my-1 xl:w-full xl:m-0"><span class="font-medium">日本:</span> 15.0k</li>
                                            <li class="mx-2 my-1 xl:w-full xl:m-0"><span class="font-medium">英国:</span> 12.0k</li>
                                            <li class="mx-2 my-1 xl:w-full xl:m-0"><span class="font-medium">加拿大:</span> 10.0k</li>
                                            <li class="mx-2 my-1 xl:w-full xl:m-0"><span class="font-medium">法国:</span> 9.0k</li>
                                            <li class="mx-2 my-1 xl:w-full xl:m-0"><span class="font-medium">巴西:</span> 8.0k</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 设备分布图 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-gray-300 font-medium">设备分布</h3>
                    <span class="text-xs text-gray-500">最近30天</span>
                </div>
                <div class="w-full h-56">
                    <div class="w-full h-full flex items-center justify-center text-gray-500 rounded-lg">
                        <canvas id="deviceChart"></canvas>
                    </div>
                </div>
            </div>
        
            <!-- SEO健康度分析 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold text-white relative pl-3">健康度分析</h2>
                    <a href="seo_stats.html" class="text-blue-500 text-sm flex items-center hover:text-blue-400">
                        详细报告 <i class="fas fa-chevron-right text-xs ml-1"></i>
                    </a>
                </div>
                <div class="flex flex-wrap gap-4 mt-4">
                    <div class="flex flex-col items-center rounded-lg p-4 w-full sm:w-[calc(25%-0.75rem)] transition-all hover:bg-gray-800/40">
                        <i class="fas fa-tag text-blue-400 text-xl"></i>
                        <div class="text-2xl font-bold my-2 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-blue-600">78%</div>
                        <span class="text-xs text-gray-400">元标签完整率</span>
                    </div>
                    <div class="flex flex-col items-center rounded-lg p-4 w-full sm:w-[calc(25%-0.75rem)] transition-all hover:bg-gray-800/40">
                        <i class="fas fa-image text-green-400 text-xl"></i>
                        <div class="text-2xl font-bold my-2 text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-green-600">65%</div>
                        <span class="text-xs text-gray-400">图片ALT标签</span>
                    </div>
                    <div class="flex flex-col items-center rounded-lg p-4 w-full sm:w-[calc(25%-0.75rem)] transition-all hover:bg-gray-800/40">
                        <i class="fas fa-tachometer-alt text-yellow-400 text-xl"></i>
                        <div class="text-2xl font-bold my-2 text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-yellow-600">92%</div>
                        <span class="text-xs text-gray-400">页面加载速度</span>
                    </div>
                    <div class="flex flex-col items-center rounded-lg p-4 w-full sm:w-[calc(25%-0.75rem)] transition-all hover:bg-gray-800/40">
                        <i class="fas fa-mobile-alt text-purple-400 text-xl"></i>
                        <div class="text-2xl font-bold my-2 text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-purple-600">85%</div>
                        <span class="text-xs text-gray-400">移动适配度</span>
                    </div>
                </div>
            </div>

            <!-- 内容分析区域 -->
            <div class="bg-gray-800/10 mb-6 border border-gray-700 rounded-xl p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold">内容分析</h2>
                    <div class="flex rounded-lg p-1">
                        <button class="px-4 py-2 rounded-md text-sm font-medium hover:text-white active:bg-blue-500 bg-gray-700 active" data-tab="content-performance">内容表现</button>
                        <button class="px-4 py-2 rounded-md text-sm font-medium text-gray-300 hover:text-white active:bg-blue-500 bg-gray-700 ml-1" data-tab="content-activity">内容活动</button>
                    </div>
                </div>
                
                <div class="flex flex-col lg:flex-row gap-6">
                    <div class="bg-gray-800/20 lg:w-2/3 border border-gray-700 rounded-xl p-5">
                        <div id="contentPerformance">
                            <h3 class="text-lg font-semibold mb-4">热门内容</h3>
                            <div class="flex flex-wrap gap-4">
                                <div class="w-full md:w-[calc(50%-0.5rem)]">
                                    <div class="flex justify-between mb-1 text-sm">
                                        <span>技术文章</span>
                                        <span>42%</span>
                                    </div>
                                    <div class="w-full h-2 rounded-full overflow-hidden">
                                        <div class="h-full bg-blue-500 rounded-full" style="width: 42%"></div>
                                    </div>
                                </div>
                                <div class="w-full md:w-[calc(50%-0.5rem)]">
                                    <div class="flex justify-between mb-1 text-sm">
                                        <span>产品评测</span>
                                        <span>28%</span>
                                    </div>
                                    <div class="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
                                        <div class="h-full bg-purple-500 rounded-full" style="width: 28%"></div>
                                    </div>
                                </div>
                                <div class="w-full md:w-[calc(50%-0.5rem)]">
                                    <div class="flex justify-between mb-1 text-sm">
                                        <span>行业新闻</span>
                                        <span>18%</span>
                                    </div>
                                    <div class="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
                                        <div class="h-full bg-green-500 rounded-full" style="width: 18%"></div>
                                    </div>
                                </div>
                                <div class="w-full md:w-[calc(50%-0.5rem)]">
                                    <div class="flex justify-between mb-1 text-sm">
                                        <span>教程指南</span>
                                        <span>12%</span>
                                    </div>
                                    <div class="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
                                        <div class="h-full bg-yellow-500 rounded-full" style="width: 12%"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6">
                                <h3 class="text-lg font-semibold mb-4">最佳内容</h3>
                                <div class="space-y-3">
                                    <a href="#" class="flex justify-between items-center p-3 rounded-lg hover:bg-gray-800">
                                        <span>如何有效提升网站SEO排名</span>
                                        <span class="text-blue-400">阅读量: 15,230</span>
                                    </a>
                                    <a href="#" class="flex justify-between items-center p-3 rounded-lg hover:bg-gray-800">
                                        <span>Tailwind CSS 深度实践指南</span>
                                        <span class="text-blue-400">阅读量: 9,856</span>
                                    </a>
                                    <a href="#" class="flex justify-between items-center p-3 rounded-lg hover:bg-gray-800">
                                        <span>2025年内容营销新趋势</span>
                                        <span class="text-blue-400">阅读量: 7,342</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div id="contentActivity" class="hidden">
                            <h3 class="text-lg font-semibold mb-3">内容活动</h3>
                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <div class="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center mr-3">
                                        <i class="fas fa-plus text-xs"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium">新内容添加</p>
                                        <p class="text-sm text-gray-400">2025-05-28 14:30</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="w-10 h-10 rounded-full bg-green-500 flex items-center justify-center mr-3">
                                        <i class="fas fa-check text-xs"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium">内容审核通过</p>
                                        <p class="text-sm text-gray-400">2025-05-27 10:15</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center mr-3">
                                        <i class="fas fa-sync text-xs"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium">内容更新</p>
                                        <p class="text-sm text-gray-400">2025-05-26 16:45</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-800/20 lg:w-1/3 border border-gray-700 rounded-xl p-5">
                        <h3 class="text-lg font-semibold mb-4">发布趋势</h3>
                        <div class="w-full h-60 flex items-center justify-center text-gray-500">
                            <canvas id="contentTrendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 自动化工作流摘要 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold text-white relative pl-3">自动工作流</h2>
                    <a href="workflows.html" class="text-blue-500 text-sm flex items-center hover:text-blue-400">
                        查看全部 <i class="fas fa-chevron-right text-xs ml-1"></i>
                    </a>
                </div>
                <div class="mt-4 space-y-3">
                    <div class="bg-gray-800/20 rounded-xl p-4 border-l-4 border-green-500">
                        <div class="flex justify-between">
                            <h3 class="font-medium text-white">新用户欢迎邮件</h3>
                            <span class="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-full">运行中</span>
                        </div>
                        <p class="text-sm text-gray-400 mt-2">24小时内发送了128封欢迎邮件</p>
                    </div>
                    <div class="bg-gray-800/20 rounded-xl p-4 border-l-4 border-yellow-500">
                        <div class="flex justify-between">
                            <h3 class="font-medium text-white">内容更新通知</h3>
                            <span class="text-xs bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded-full">待处理</span>
                        </div>
                        <p class="text-sm text-gray-400 mt-2">3篇文章等待发送更新通知</p>
                    </div>
                    <div class="bg-gray-800/20 rounded-xl p-4 border-l-4 border-blue-500">
                        <div class="flex justify-between">
                            <h3 class="font-medium text-white">每周数据报告</h3>
                            <span class="text-xs bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full">已安排</span>
                        </div>
                        <p class="text-sm text-gray-400 mt-2">下次运行: 2025-06-02 08:00</p>
                    </div>
                </div>
            </div>

            <!-- 最新动态 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold text-white relative pl-3">最新动态</h2>
                    <a href="activities.html" class="text-blue-500 text-sm flex items-center hover:text-blue-400">
                        查看全部 <i class="fas fa-chevron-right text-xs ml-1"></i>
                    </a>
                </div>
                <div class="flex flex-wrap -mx-2">
                    <!-- 动态项 1 -->
                    <div class="w-full lg:w-1/2 px-2 mb-4">
                        <div class="flex items-center bg-gray-800/20 border border-gray-700 rounded-xl p-4">
                            <div class="w-14 h-14 rounded-full flex items-center justify-center text-xl bg-gradient-to-r from-blue-500 to-blue-600">
                                <i class="fas fa-file-alt text-white"></i>
                            </div>
                            <div class="ml-4 flex-1">
                                <h3 class="font-semibold">新内容提交</h3>
                                <p class="text-gray-300 my-2">
                                    <a href="user_profile.html" class="text-blue-400 hover:underline">张小明</a> 
                                    提交了新文章 <a href="content_reviews.html" class="text-blue-400 hover:underline">《如何优化网站SEO》</a>
                                    <span class="text-xs bg-yellow-500/20 text-yellow-400 px-2 py-0.5 rounded-full ml-2">等待审核</span>
                                </p>
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-500"><i class="far fa-clock mr-1"></i> 10分钟前</span>
                                    <a href="content_reviews.html" class="text-blue-400 hover:text-blue-300">立即审核 <i class="fas fa-arrow-right ml-1 text-xs"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 动态项 2 -->
                    <div class="w-full lg:w-1/2 px-2 mb-4">
                        <div class="flex items-center bg-gray-800/20 border border-gray-700 rounded-xl p-4">
                            <div class="w-14 h-14 rounded-full flex items-center justify-center text-xl bg-gradient-to-r from-purple-500 to-purple-600">
                                <i class="fas fa-sync-alt text-white"></i>
                            </div>
                            <div class="ml-4 flex-1">
                                <h3 class="font-semibold">系统更新</h3>
                                <p class="text-gray-300 my-2">
                                    系统更新至版本 <a href="update_log.html" class="text-blue-400 hover:underline">v2.5.3,包含多项安全修复</a>
                                    <span class="text-xs bg-green-500/20 text-green-400 px-2 py-0.5 rounded-full ml-2">已完成</span>
                                </p>
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-500"><i class="far fa-clock mr-1"></i> 2小时前</span>
                                    <a href="update_log.html" class="text-blue-400 hover:text-blue-300">更新日志 <i class="fas fa-arrow-right ml-1 text-xs"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 动态项 3 -->
                    <div class="w-full lg:w-1/2 px-2 mb-4">
                        <div class="flex items-center bg-gray-800/20 border border-gray-700 rounded-xl p-4">
                            <div class="w-14 h-14 rounded-full flex items-center justify-center text-xl bg-gradient-to-r from-indigo-500 to-indigo-600">
                                <i class="fas fa-puzzle-piece text-white"></i>
                            </div>
                            <div class="ml-4 flex-1">
                                <h3 class="font-semibold">插件更新</h3>
                                <p class="text-gray-300 my-2">
                                    插件 <a href="plugin_detail.html" class="text-blue-400 hover:underline">高级编辑器</a> 
                                    有新版本 v3.2.1 可用
                                    <span class="text-xs bg-blue-500/20 text-blue-400 px-2 py-0.5 rounded-full ml-2">可更新</span>
                                </p>
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-500"><i class="far fa-clock mr-1"></i> 昨天</span>
                                    <a href="plugin_update.html" class="text-blue-400 hover:text-blue-300">更新插件 <i class="fas fa-arrow-right ml-1 text-xs"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 动态项 4 -->
                    <div class="w-full lg:w-1/2 px-2 mb-4">
                        <div class="flex items-center bg-gray-800/20 border border-gray-700 rounded-xl p-4">
                            <div class="w-14 h-14 rounded-full flex items-center justify-center text-xl bg-gradient-to-r from-amber-500 to-amber-600">
                                <i class="fas fa-comment text-white"></i>
                            </div>
                            <div class="ml-4 flex-1">
                                <h3 class="font-semibold">发表了评论</h3>
                                <p class="text-gray-300 my-2">
                                    <a href="user_profile.html" class="text-blue-400 hover:underline">李华</a> 
                                    在文章 <a href="post_edit.html" class="text-blue-400 hover:underline">《2023年内容营销趋势》</a> 
                                    发表了评论
                                    <span class="text-xs bg-red-500/20 text-red-400 px-2 py-0.5 rounded-full ml-2">待审核</span>
                                </p>
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-500"><i class="far fa-clock mr-1"></i> 2天前</span>
                                    <a href="comment_review.html" class="text-blue-400 hover:text-blue-300">查看评论 <i class="fas fa-arrow-right ml-1 text-xs"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer border-t border-gray-700 mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <!-- Chart.js库 -->
    <script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/Chart.js/3.7.1/chart.js"></script>
    <script src="./assets/js/echarts.5.4.1.min.js"></script>
    <script src="./assets/js/china.js"></script>
    <script src="./assets/js/world.js"></script>
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <script>
    /**
     * 初始化访问统计图表
     * <AUTHOR> Nieh <<EMAIL>>
     */
    /**
     * 创建线性渐变供图表使用
     * @param {CanvasRenderingContext2D} ctx - 画布的2D渲染上下文
     * @param {string} colorStart - 渐变起始颜色
     * @param {string} colorEnd - 渐变结束颜色
     * @returns {CanvasGradient}
     * <AUTHOR> Nieh <<EMAIL>>
     */
    function createGradient(ctx, colorStart, colorEnd) {
        const gradient = ctx.createLinearGradient(0, 0, 0, ctx.canvas.height * 0.7); // Adjust gradient height if needed
        gradient.addColorStop(0, colorStart);
        gradient.addColorStop(1, colorEnd);
        return gradient;
    }

    /**
     * 生成带趋势的随机数据
     * @param {number} days - 周期天数
     * @param {number} baseValue - 基础值
     * @param {string} trend - 趋势 ('up', 'down', 'stable')
     * @param {number} volatility - 波动性 (0-1)
     * @param {boolean} isYearly - 是否为年度数据 (按月生成标签)
     * @returns {{labels: string[], data: number[]}}
     * <AUTHOR> Nieh <<EMAIL>>
     */
    function generateRandomData(days, baseValue, trend, volatility, isYearly = false) {
        const labels = [];
        const data = [];
        const today = new Date();

        if (isYearly) { // For 1-year view, labels are months
            const monthNames = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"];
            for (let i = 0; i < 12; i++) {
                labels.push(monthNames[i]);
            }
        } else if (days === 90) { // For 90-day view, show 12 weekly points
             for (let i = 11; i >= 0; i--) { // 12 weeks (labels from oldest to newest)
                const date = new Date(today);
                date.setDate(today.getDate() - i * 7);
                labels.push(`${date.getMonth() + 1}/${date.getDate()}`);
            }
        } else { // For 7-day and 30-day views
            for (let i = 0; i < days; i++) { // labels from (days-1) ago to today
                const date = new Date(today);
                date.setDate(today.getDate() - (days - 1 - i));
                if (days === 7) {
                    const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
                    labels.push(dayNames[date.getDay()]); 
                } else { // 30 days
                    labels.push(`${date.getMonth() + 1}/${date.getDate()}`);
                }
            }
        }
        
        let currentValue = baseValue;
        // Ensure data array matches label array length
        const numPoints = labels.length;

        for (let i = 0; i < numPoints; i++) {
            let changeFactor = (Math.random() - 0.5) * 2 * volatility; // Fluctuation percentage
            let trendFactor = 0;
            if (numPoints > 1) { // Avoid division by zero if numPoints is 1 or 0
                if (trend === 'up') {
                    trendFactor = 0.1 * (i / (numPoints -1)); // Gradual increase, up to 10% of base
                } else if (trend === 'down') {
                    trendFactor = -0.1 * (i / (numPoints -1)); // Gradual decrease
                }
            }
            currentValue = baseValue * (1 + trendFactor + changeFactor);
            data.push(Math.max(0, Math.round(currentValue))); // Ensure non-negative
        }
        return { labels, data };
    }

    function initVisitChart() {
        const ctx = document.getElementById('visitChart').getContext('2d');
        const visitChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                datasets: [{
                    label: ' 页面访问量',
                    data: [1024, 1239, 1032, 1547, 1823, 2156, 2583, 2234, 1987, 2345, 2678, 2890],
                    borderColor: 'rgba(59, 130, 246, 1)',
                    backgroundColor: function(context) { 
                        const chart = context.chart;
                        const {ctx, chartArea} = chart;
                        if (!chartArea) return null;
                        return createGradient(ctx, 'rgba(59, 130, 246, 0.4)', 'rgba(59, 130, 246, 0.05)');
                    },
                    borderWidth: 2.5,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgba(59, 130, 246, 1)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    pointHoverRadius: 7,
                    pointHitRadius: 10
                }, {
                    label: ' 独立访客',
                    data: [856, 1023, 876, 1234, 1456, 1789, 2134, 1876, 1654, 1987, 2234, 2456],
                    borderColor: 'rgba(16, 185, 129, 1)',
                    backgroundColor: function(context) { 
                        const chart = context.chart;
                        const {ctx, chartArea} = chart;
                        if (!chartArea) return null;
                        return createGradient(ctx, 'rgba(16, 185, 129, 0.4)', 'rgba(16, 185, 129, 0.05)');
                    },
                    borderWidth: 2.5,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgba(16, 185, 129, 1)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    pointHoverRadius: 7,
                    pointHitRadius: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                aspectRatio: 2, // 设置宽高比为2:1
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                        align: 'end',
                        labels: {
                            color: '#D1D5DB', // text-gray-300
                            font: { size: 12 },
                            usePointStyle: true,
                            boxWidth: 8,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.85)',
                        titleColor: '#E5E7EB',
                        bodyColor: '#D1D5DB',
                        borderColor: 'rgba(255,255,255,0.1)',
                        borderWidth: 1,
                        padding: 12,
                        cornerRadius: 8,
                        displayColors: true,
                        boxPadding: 4,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += new Intl.NumberFormat('zh-CN').format(context.parsed.y);
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#9CA3AF', font: { size: 11 } },
                        grid: { color: 'rgba(255, 255, 255, 0.05)', drawBorder: false }
                    },
                    y: { // Y 轴配置
                        ticks: { 
                            color: '#9CA3AF', 
                            font: { size: 11 }, 
                            callback: function(value) { return new Intl.NumberFormat('zh-CN').format(value); },
                            stepSize: 250 // 减少纵轴间隔高度
                        },
                        grid: { color: 'rgba(255, 255, 255, 0.05)', drawBorder: false },
                        beginAtZero: true
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
        
        // 图表周期选择事件 - 按钮组
        document.querySelectorAll('.period-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有按钮的active状态
                document.querySelectorAll('.period-tab').forEach(t => t.classList.remove('active'));
                // 为当前点击的按钮添加active状态
                this.classList.add('active');
                
                const period = this.getAttribute('data-period');
                updateChartData(visitChart, period);
            });
        });
        
        // 图表区域滚轮切换周期
        document.getElementById('visitChart').addEventListener('wheel', function(e) {
            e.preventDefault();
            
            const currentActive = document.querySelector('.period-tab.active');
            const allTabs = Array.from(document.querySelectorAll('.period-tab'));
            const currentIndex = allTabs.indexOf(currentActive);
            
            let nextIndex;
            if (e.deltaY > 0) {
                // 向下滚动，切换到下一个周期
                nextIndex = (currentIndex + 1) % allTabs.length;
            } else {
                // 向上滚动，切换到上一个周期
                nextIndex = (currentIndex - 1 + allTabs.length) % allTabs.length;
            }
            
            // 触发点击事件
            allTabs[nextIndex].click();
        });
    }
    
    /**
     * 更新图表数据和统计卡片
     * @param {Chart} chart - Chart.js实例
     * @param {string|number} period - 时间周期 (7, 30, 90, 365)
     * <AUTHOR> Nieh <<EMAIL>>
     */
    function updateChartData(chart, period) {
        let chartData1, chartData2;
        let todayValText = '', monthValText = '', yearValText = '';

        const today = new Date();
        const currentMonth = today.getMonth(); // 0-11

        let currentVisits = 0;

        switch(String(period)) {
            case '7':
                chartData1 = generateRandomData(7, 2500, 'up', 0.2);
                chartData2 = generateRandomData(7, 1800, 'up', 0.25);
                if (chartData1.data && chartData1.data.length > 0) {
                    currentVisits = chartData1.data[chartData1.data.length - 1] || 0;
                    todayValText = `今日: ${currentVisits.toLocaleString()}`;
                }
                break;
            case '30': // Default case handles 30 days
                chartData1 = generateRandomData(30, 80000, 'stable', 0.2); // Page views for month
                chartData2 = generateRandomData(30, 60000, 'stable', 0.25); // Unique visitors for month
                if (chartData1.data && chartData1.data.length > 0) {
                    const monthTotal = chartData1.data.reduce((a, b) => a + b, 0);
                    monthValText = `本月: ${monthTotal.toLocaleString()}`;
                    currentVisits = chartData1.data[chartData1.data.length - 1] || 0;
                }
                break;
            case '90':
                chartData1 = generateRandomData(90, 75000, 'stable', 0.15, false); // Page views (90 days, 12 weekly points)
                chartData2 = generateRandomData(90, 55000, 'stable', 0.2, false); // Unique visitors
                if (chartData1.data && chartData1.data.length > 0) {
                    const lastQuarterApprox = chartData1.data.reduce((sum, val) => sum + val, 0) ; // Sum of all weekly points for the quarter
                    monthValText = `本季: ${Math.round(lastQuarterApprox).toLocaleString()}`; // Changed label to 本季
                    currentVisits = chartData1.data[chartData1.data.length -1] || 0;
                }
                break;
            case '365':
                chartData1 = generateRandomData(365, 900000, 'up', 0.1, true); // Page views (yearly, 12 monthly points)
                chartData2 = generateRandomData(365, 600000, 'up', 0.15, true); // Unique visitors
                if (chartData1.data && chartData1.data.length > 0) {
                    const yearTotal = chartData1.data.reduce((a, b) => a + b, 0);
                    yearValText = `本年: ${yearTotal.toLocaleString()}`;
                }
                break;
        }
        
        if (chartData1 && chartData1.labels && chartData1.data) {
            chart.data.labels = chartData1.labels;
            chart.data.datasets[0].data = chartData1.data;
        } else {
            chart.data.labels = [];
            chart.data.datasets[0].data = [];
        }
        if (chartData2 && chartData2.data) {
            chart.data.datasets[1].data = chartData2.data;
        } else {
            chart.data.datasets[1].data = [];
        }
        chart.update();

        const chartTodayStats = document.getElementById('chartTodayStats');
        const chartMonthStats = document.getElementById('chartMonthStats');
        const chartYearStats = document.getElementById('chartYearStats');
        
        if (chartTodayStats) {
            chartTodayStats.textContent = todayValText;
            chartTodayStats.style.display = todayValText ? '' : 'none';
        }
        
        if (chartMonthStats) {
            chartMonthStats.textContent = monthValText;
            chartMonthStats.style.display = monthValText ? '' : 'none';
        }
        
        if (chartYearStats) {
            chartYearStats.textContent = yearValText;
            chartYearStats.style.display = yearValText ? '' : 'none';
        }
    }
    
    /**
     * 切换侧边栏显示模式（完整显示/图标模式）
     * <AUTHOR> Nieh <<EMAIL>>
     */
    function toggleSidebarMode() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.querySelector('.main-content');
        
        if (sidebar && mainContent) {
            sidebar.classList.toggle('sidebar-collapsed');
            mainContent.classList.toggle('content-expanded');
            
            // 保存用户偏好到localStorage
            const isCollapsed = sidebar.classList.contains('sidebar-collapsed');
            localStorage.setItem('sidebarCollapsed', isCollapsed);
        }
    }
    
    // 设备分布图初始化
    function initDeviceChart() {
        const ctx = document.getElementById('deviceChart').getContext('2d');
        const deviceChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['桌面设备', '移动设备', '平板设备'],
                datasets: [{
                    data: [58, 35, 7],
                    backgroundColor: [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(16, 185, 129, 0.8)',
                        'rgba(245, 158, 11, 0.8)'
                    ],
                    borderColor: [
                        'rgba(59, 130, 246, 1)',
                        'rgba(16, 185, 129, 1)',
                        'rgba(245, 158, 11, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#D1D5DB',
                            font: {
                                size: 12
                            },
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.85)',
                        titleColor: '#E5E7EB',
                        bodyColor: '#D1D5DB',
                        borderColor: 'rgba(255,255,255,0.1)',
                        borderWidth: 1,
                        padding: 12,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed || 0;
                                return `${label}: ${value}%`;
                            }
                        }
                    }
                },
                cutout: '65%'
            }
        });
    }
        
    // 初始化内容趋势图表
    function initContentTrendChart() {
        const ctx = document.getElementById('contentTrendChart').getContext('2d');
        const chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [
                    {
                        label: '新增内容',
                        data: [18, 22, 24, 30, 28, 35],
                        backgroundColor: 'rgba(59, 130, 246, 0.7)',
                        borderRadius: 4
                    },
                    {
                        label: '内容更新',
                        data: [12, 15, 18, 20, 22, 25],
                        backgroundColor: 'rgba(124, 58, 237, 0.7)',
                        borderRadius: 4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            color: '#D1D5DB'
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#94A3B8'
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.05)'
                        },
                        ticks: {
                            color: '#94A3B8'
                        },
                        beginAtZero: true
                    }
                }
            }
        });
    }
        
    // 初始化交互功能
    function initInteractions() {
        // 标签切换
        const tabButtons = document.querySelectorAll('[data-tab]');
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                const tab = this.getAttribute('data-tab');
                
                // 移除所有激活状态
                tabButtons.forEach(btn => btn.classList.remove('active'));
                // 添加当前激活状态
                this.classList.add('active');
                
                // 内容切换
                document.getElementById('contentPerformance').classList.add('hidden');
                document.getElementById('contentActivity').classList.add('hidden');
                
                if (tab === 'content-performance') {
                    document.getElementById('contentPerformance').classList.remove('hidden');
                } else if (tab === 'content-activity') {
                    document.getElementById('contentActivity').classList.remove('hidden');
                }
            });
        });
        
        // 清理缓存按钮
        document.getElementById('clearCacheBtn').addEventListener('click', function(e) {
            e.preventDefault();
            showNotification('缓存清理完成！', 'success');
        });
    }
        
    // 显示通知
    function showNotification(message, type) {
        // 简化版的提示函数
        alert(message);
    }
        
    // 页面加载时恢复用户的侧边栏偏好设置
    document.addEventListener('DOMContentLoaded', function() {
        const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        if (isCollapsed) {
            setTimeout(() => {
                toggleSidebarMode();
            }, 100); // 延迟执行确保DOM已加载
        }        
        
        // 初始化图表
        initVisitChart();
        initContentTrendChart();
        initDeviceChart();

        // 初始化交互功能
        initInteractions();
    });
    </script>
    <script>
        /**
         * @function initTabs
         * @description Initializes the tab functionality for switching between content panels.
         *              It finds all tab buttons and their corresponding panels, then sets up
         *              event listeners to handle tab switching on click.
         * <AUTHOR> Nieh <<EMAIL>>
         */
        /**
         * @function initChinaMap
         * @description Initializes the ECharts map for China statistics.
         * <AUTHOR> Nieh <<EMAIL>>
         */
        function initChinaMap() {
            const chartDom = document.getElementById('china-map-container');
            if (!chartDom) {
                console.error('GACMS Maps: China map container not found.');
                return;
            }
            // 确保容器有最小高度，以便 ECharts 正确渲染
            chartDom.style.minHeight = '281px'; 
            try {
                const myChart = echarts.init(chartDom);
                const option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: '{b}<br/>访问量: {c}'
                    },
                    visualMap: {
                        min: 0,
                        max: 100000, // Adjusted max value based on reference image (e.g. 8.6k)
                        left: '5%', // Position to the left
                        bottom: '5%', // Position to the bottom
                        orient: 'horizontal', // Horizontal orientation
                        itemWidth: 15, // Width of each color block in visualMap
                        itemHeight: 80, // Height of the visualMap bar (when horizontal)
                        text: ['高', '低'],
                        calculable: true,
                        inRange: {
                            color: ['#E0F7FA', '#4DD0E1', '#00ACC1', '#00838F'] // Cyan color scheme from light to dark
                        },
                        textStyle: {
                            color: '#E5E7EB'
                        }
                    },
                    series: [
                        {
                            name: '中国访问量',
                            type: 'map',
                            map: 'china',
                            roam: true, // Enable zooming and panning
                            label: {
                                show: false // Do not show province names by default
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    color: '#FFFFFF'
                                },
                                itemStyle: {
                                    areaColor: '#F59E0B' // Highlight color on hover
                                }
                            },
                            // Example data - replace with actual data from your backend
                            data: [
                                {name: '河南', value: 8600},
                                {name: '北京', value: 7800},
                                {name: '四川', value: 7800},
                                {name: '浙江', value: 6400},
                                {name: '江苏', value: 5100},
                                {name: '上海', value: 3400},
                                {name: '河北', value: 2600},
                                // Add other provinces with random data or 0 for a complete map
                                {name: '天津', value: Math.round(Math.random()*1000)},
                                {name: '重庆', value: Math.round(Math.random()*1000)},
                                {name: '云南', value: Math.round(Math.random()*1000)},
                                {name: '辽宁', value: Math.round(Math.random()*1000)},
                                {name: '黑龙江', value: Math.round(Math.random()*1000)},
                                {name: '湖南', value: Math.round(Math.random()*1000)},
                                {name: '安徽', value: Math.round(Math.random()*1000)},
                                {name: '山东', value: Math.round(Math.random()*1000)},
                                {name: '新疆', value: Math.round(Math.random()*1000)},
                                {name: '江西', value: Math.round(Math.random()*1000)},
                                {name: '湖北', value: Math.round(Math.random()*1000)},
                                {name: '广西', value: Math.round(Math.random()*1000)},
                                {name: '甘肃', value: Math.round(Math.random()*1000)},
                                {name: '山西', value: Math.round(Math.random()*1000)},
                                {name: '内蒙古', value: Math.round(Math.random()*1000)},
                                {name: '陕西', value: Math.round(Math.random()*1000)},
                                {name: '吉林', value: Math.round(Math.random()*1000)},
                                {name: '福建', value: Math.round(Math.random()*1000)},
                                {name: '贵州', value: Math.round(Math.random()*1000)},
                                {name: '广东', value: Math.round(Math.random()*2000)},
                                {name: '青海', value: Math.round(Math.random()*500)},
                                {name: '西藏', value: Math.round(Math.random()*500)},
                                {name: '宁夏', value: Math.round(Math.random()*500)},
                                {name: '海南', value: Math.round(Math.random()*1000)},
                                {name: '台湾', value: Math.round(Math.random()*500)},
                                {name: '香港', value: Math.round(Math.random()*500)},
                                {name: '澳门', value: Math.round(Math.random()*500)}
                            ]
                        }
                    ]
                };
                myChart.setOption(option);
                myChart.getDom().style.width = '100%';
                myChart.getDom().style.height = '100%';
                myChart.resize();
            } catch (error) {
                console.error('GACMS Maps: Error initializing China map:', error);
                chartDom.innerHTML = '<p class="text-red-400">中国地图加载失败，请检查控制台。</p>';
            }
        }

        /**
         * @function initWorldMap
         * @description Initializes the ECharts map for World statistics.
         * <AUTHOR> Nieh <<EMAIL>>
         */
        function initWorldMap() {
            const chartDom = document.getElementById('world-map-container');
            if (!chartDom) {
                console.error('GACMS Maps: World map container not found.');
                return;
            }
            // 确保容器有最小高度，以便 ECharts 正确渲染
            chartDom.style.minHeight = '281px';
            try {
                const myChart = echarts.init(chartDom);
                const option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: '{b}<br/>访问量: {c}'
                    },
                    visualMap: {
                        min: 0,
                        max: 1000000, // Adjusted max value (e.g. 22.0k)
                        left: '5%',
                        bottom: '5%',
                        orient: 'horizontal',
                        itemWidth: 15,
                        itemHeight: 80,
                        text: ['高', '低'],
                        calculable: true,
                        inRange: {
                            color: ['#E0F7FA', '#4DD0E1', '#00ACC1', '#00838F'] // Cyan color scheme
                        },
                        textStyle: {
                            color: '#E5E7EB'
                        }
                    },
                    series: [
                        {
                            name: '世界访问量',
                            type: 'map',
                            map: 'world',
                            roam: true,
                            label: {
                                show: false
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    color: '#FFFFFF'
                                },
                                itemStyle: {
                                    areaColor: '#F59E0B'
                                }
                            },
                            data: [
                                {name: 'China', value: 560000},
                                {name: 'United States', value: 22000},
                                {name: 'Germany', value: 18000},
                                {name: 'Japan', value: 15000},
                                {name: 'United Kingdom', value: 12000},
                                {name: 'Canada', value: 10000},
                                {name: 'France', value: 9000},
                                {name: 'Australia', value: 8000},
                                // Add other countries with random data or 0 for a complete map
                                {name: 'Brazil', value: Math.round(Math.random()*5000)},
                                {name: 'Russia', value: Math.round(Math.random()*5000)}
                            ]
                        }
                    ]
                };
                myChart.setOption(option);
                myChart.getDom().style.width = '100%';
                myChart.getDom().style.height = '100%';
                myChart.resize();
            } catch (error) {
                console.error('GACMS Maps: Error initializing World map:', error);
                chartDom.innerHTML = '<p class="text-red-400">世界地图加载失败，请检查控制台。</p>';
            }
        }

        function initTabs() {
            const tabButtons = document.querySelectorAll('[role="tab"][aria-controls]');
            const tabPanels = document.querySelectorAll('[role="tabpanel"]');

            if (tabButtons.length === 0 || tabPanels.length === 0) {
                // console.warn('GACMS Tabs: No tab buttons or panels found to initialize.');
                return;
            }

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Deactivate all tabs and hide all panels
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active');
                        btn.setAttribute('aria-selected', 'false');
                        btn.setAttribute('tabindex', '-1'); 
                    });
                    tabPanels.forEach(panel => {
                        panel.classList.add('hidden');
                    });

                    // Activate the clicked tab and show its panel
                    button.classList.add('active');
                    button.setAttribute('aria-selected', 'true');
                    button.setAttribute('tabindex', '0');
                    const controlledPanelId = button.getAttribute('aria-controls');
                    const activePanel = document.getElementById(controlledPanelId);
                    if (activePanel) {
                        activePanel.classList.remove('hidden');
                        // Initialize map if the activated panel is a map panel
                        if (controlledPanelId === 'panel-china-stats') {
                            setTimeout(initChinaMap, 0); // Use setTimeout to ensure panel is visible for ECharts
                        } else if (controlledPanelId === 'panel-world-stats') {
                            setTimeout(initWorldMap, 0);
                        }
                    }
                });
            });

            // Ensure the initially active tab's panel is visible
            const initiallyActiveButton = document.querySelector('.tab-button.active');
            if (initiallyActiveButton) {
                const activePanelId = initiallyActiveButton.getAttribute('aria-controls');
                const activePanel = document.getElementById(activePanelId);
                if (activePanel) {
                    activePanel.classList.remove('hidden');
                }
            }
        }

        // Initialize tabs when the DOM is fully loaded and all resources are available
        window.onload = () => {
            initTabs();
            // After tabs are initialized, check if the default active tab is a map tab and initialize it.
            const initiallyActiveButton = document.querySelector('.tab-button.active');
            if (initiallyActiveButton) {
                const activePanelId = initiallyActiveButton.getAttribute('aria-controls');
                const activePanel = document.getElementById(activePanelId); 
                if (activePanel) { 
                    // Check if panel exists
                    if (activePanelId === 'panel-china-stats') {
                        if (!activePanel.classList.contains('hidden')) {
                            // console.log('GACMS Maps: Initializing China map on load.');
                            setTimeout(initChinaMap, 500); // Delay initialization slightly
                        }
                    } else if (activePanelId === 'panel-world-stats') {
                        if (!activePanel.classList.contains('hidden')) {
                            // console.log('GACMS Maps: Initializing World map on load.');
                            setTimeout(initWorldMap, 500); // Delay initialization slightly
                        }
                    }
                }
            }
        };
    </script>
</body>
</html>