/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/post/infrastructure/persistence/PostGormRepository.go
 * @Description: GORM implementation of the PostRepository.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package persistence

import (
	"context"
	"gacms/internal/modules/post/domain/contract"
	"gacms/internal/modules/post/domain/model"
	dbContract "gacms/pkg/contract"
)

type PostGormRepository struct {
	dbService dbContract.Database
}

func NewPostGormRepository(dbService dbContract.Database) contract.PostRepository {
	return &PostGormRepository{dbService: dbService}
}

func (r *PostGormRepository) Create(ctx context.Context, post *model.Post) error {
	return r.dbService.DB(ctx).Create(post).Error
}

func (r *PostGormRepository) GetByID(ctx context.Context, id uint) (*model.Post, error) {
	var post model.Post
	err := r.dbService.DB(ctx).First(&post, id).Error
	return &post, err
}

func (r *PostGormRepository) GetBySlug(ctx context.Context, slug string) (*model.Post, error) {
	var post model.Post
	err := r.dbService.DB(ctx).Where("slug = ?", slug).First(&post).Error
	return &post, err
} 