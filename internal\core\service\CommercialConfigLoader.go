/*
Author: <PERSON><PERSON> Nieh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package service

import (
	"fmt"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"gopkg.in/yaml.v3"
)

// CommercialConfigLoader 商业授权配置加载器
type CommercialConfigLoader struct {
	logger *zap.Logger
}

// NewCommercialConfigLoader 创建配置加载器
func NewCommercialConfigLoader(logger *zap.Logger) *CommercialConfigLoader {
	return &CommercialConfigLoader{
		logger: logger,
	}
}

// LoadConfig 加载商业授权配置
func (l *CommercialConfigLoader) LoadConfig() (*SystemLicenseConfig, error) {
	config := &SystemLicenseConfig{}
	
	// 1. 加载默认配置
	l.setDefaultConfig(config)
	
	// 2. 从配置文件加载
	if err := l.loadFromFile(config); err != nil {
		l.logger.Warn("Failed to load config from file, using defaults", zap.Error(err))
	}
	
	// 3. 从环境变量覆盖
	l.loadFromEnv(config)
	
	// 4. 验证配置
	if err := l.validateConfig(config); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}
	
	l.logger.Info("Commercial license config loaded",
		zap.Bool("enabled", config.Enabled),
		zap.Bool("development_mode", config.DevelopmentMode),
		zap.String("system_license_path", config.SystemLicensePath),
		zap.String("usage_license_dir", config.UsageLicenseDir))
	
	return config, nil
}

// setDefaultConfig 设置默认配置
func (l *CommercialConfigLoader) setDefaultConfig(config *SystemLicenseConfig) {
	config.Enabled = false
	config.DevelopmentMode = true
	config.SystemLicensePath = "./licenses/system_license.json"
	config.UsageLicenseDir = "./licenses/usage/"
	config.ValidationInterval = 24 * time.Hour
	config.OfflineMode = true
	config.LenientMode = true
	config.CacheDuration = 1 * time.Hour
	config.DomainValidationEnabled = false
	config.PublicKeyPath = "./keys/gacms_public.pem"
}

// loadFromFile 从配置文件加载
func (l *CommercialConfigLoader) loadFromFile(config *SystemLicenseConfig) error {
	configPath := "configs/commercial.yaml"
	
	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return fmt.Errorf("config file not found: %s", configPath)
	}
	
	// 读取文件内容
	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}
	
	// 解析YAML配置
	var yamlConfig struct {
		CommercialAuthorization struct {
			Enabled         bool   `yaml:"enabled"`
			DevelopmentMode bool   `yaml:"development_mode"`
			LicenseFile     struct {
				SystemLicensePath string `yaml:"system_license_path"`
				UsageLicenseDir   string `yaml:"usage_license_dir"`
			} `yaml:"license_file"`
			Validation struct {
				IntervalHours      int  `yaml:"interval_hours"`
				OfflineMode        bool `yaml:"offline_mode"`
				LenientMode        bool `yaml:"lenient_mode"`
				CacheDurationMins  int  `yaml:"cache_duration_minutes"`
			} `yaml:"validation"`
		} `yaml:"commercial_authorization"`
		DomainValidation struct {
			Enabled bool `yaml:"enabled"`
		} `yaml:"domain_validation"`
		LicenseManagement struct {
			OfflineLicense struct {
				PublicKeyPath string `yaml:"public_key_path"`
			} `yaml:"offline_license"`
		} `yaml:"license_management"`
	}
	
	if err := yaml.Unmarshal(data, &yamlConfig); err != nil {
		return fmt.Errorf("failed to parse config file: %w", err)
	}
	
	// 映射配置值
	ca := yamlConfig.CommercialAuthorization
	config.Enabled = ca.Enabled
	config.DevelopmentMode = ca.DevelopmentMode
	
	if ca.LicenseFile.SystemLicensePath != "" {
		config.SystemLicensePath = ca.LicenseFile.SystemLicensePath
	}
	if ca.LicenseFile.UsageLicenseDir != "" {
		config.UsageLicenseDir = ca.LicenseFile.UsageLicenseDir
	}
	
	if ca.Validation.IntervalHours > 0 {
		config.ValidationInterval = time.Duration(ca.Validation.IntervalHours) * time.Hour
	}
	config.OfflineMode = ca.Validation.OfflineMode
	config.LenientMode = ca.Validation.LenientMode
	
	if ca.Validation.CacheDurationMins > 0 {
		config.CacheDuration = time.Duration(ca.Validation.CacheDurationMins) * time.Minute
	}
	
	config.DomainValidationEnabled = yamlConfig.DomainValidation.Enabled
	
	if yamlConfig.LicenseManagement.OfflineLicense.PublicKeyPath != "" {
		config.PublicKeyPath = yamlConfig.LicenseManagement.OfflineLicense.PublicKeyPath
	}
	
	return nil
}

// loadFromEnv 从环境变量加载
func (l *CommercialConfigLoader) loadFromEnv(config *SystemLicenseConfig) {
	// 商业授权开关
	if enabled := os.Getenv("GACMS_COMMERCIAL_AUTH_ENABLED"); enabled != "" {
		if val, err := strconv.ParseBool(enabled); err == nil {
			config.Enabled = val
		}
	}
	
	// 开发模式
	if devMode := os.Getenv("GACMS_DEVELOPMENT_MODE"); devMode != "" {
		if val, err := strconv.ParseBool(devMode); err == nil {
			config.DevelopmentMode = val
		}
	}
	
	// 许可证文件路径
	if path := os.Getenv("GACMS_SYSTEM_LICENSE_PATH"); path != "" {
		config.SystemLicensePath = path
	}
	
	if dir := os.Getenv("GACMS_USAGE_LICENSE_DIR"); dir != "" {
		config.UsageLicenseDir = dir
	}
	
	// 域名验证
	if domainValidation := os.Getenv("GACMS_DOMAIN_VALIDATION_ENABLED"); domainValidation != "" {
		if val, err := strconv.ParseBool(domainValidation); err == nil {
			config.DomainValidationEnabled = val
		}
	}
	
	// 公钥文件路径
	if keyPath := os.Getenv("GACMS_PUBLIC_KEY_PATH"); keyPath != "" {
		config.PublicKeyPath = keyPath
	}
	
	// 验证间隔
	if interval := os.Getenv("GACMS_LICENSE_VALIDATION_INTERVAL"); interval != "" {
		if duration, err := time.ParseDuration(interval); err == nil {
			config.ValidationInterval = duration
		}
	}
	
	// 缓存时间
	if cacheDuration := os.Getenv("GACMS_LICENSE_CACHE_DURATION"); cacheDuration != "" {
		if duration, err := time.ParseDuration(cacheDuration); err == nil {
			config.CacheDuration = duration
		}
	}
}

// validateConfig 验证配置
func (l *CommercialConfigLoader) validateConfig(config *SystemLicenseConfig) error {
	// 验证路径
	if config.SystemLicensePath == "" {
		return fmt.Errorf("system license path cannot be empty")
	}
	
	if config.UsageLicenseDir == "" {
		return fmt.Errorf("usage license directory cannot be empty")
	}
	
	// 验证时间间隔
	if config.ValidationInterval <= 0 {
		return fmt.Errorf("validation interval must be positive")
	}
	
	if config.CacheDuration <= 0 {
		return fmt.Errorf("cache duration must be positive")
	}
	
	// 如果启用商业授权，验证必需的文件路径
	if config.Enabled && !config.DevelopmentMode {
		// 检查系统许可证文件是否存在
		if _, err := os.Stat(config.SystemLicensePath); os.IsNotExist(err) {
			l.logger.Warn("System license file not found", 
				zap.String("path", config.SystemLicensePath))
		}
		
		// 检查使用许可证目录是否存在
		if _, err := os.Stat(config.UsageLicenseDir); os.IsNotExist(err) {
			// 尝试创建目录
			if err := os.MkdirAll(config.UsageLicenseDir, 0755); err != nil {
				l.logger.Warn("Failed to create usage license directory", 
					zap.String("dir", config.UsageLicenseDir), zap.Error(err))
			}
		}
		
		// 检查公钥文件是否存在
		if _, err := os.Stat(config.PublicKeyPath); os.IsNotExist(err) {
			l.logger.Warn("Public key file not found", 
				zap.String("path", config.PublicKeyPath))
		}
	}
	
	return nil
}

// GetConfigSummary 获取配置摘要
func (l *CommercialConfigLoader) GetConfigSummary(config *SystemLicenseConfig) map[string]interface{} {
	return map[string]interface{}{
		"enabled":                     config.Enabled,
		"development_mode":            config.DevelopmentMode,
		"system_license_path":         config.SystemLicensePath,
		"usage_license_dir":           config.UsageLicenseDir,
		"validation_interval":         config.ValidationInterval.String(),
		"offline_mode":                config.OfflineMode,
		"lenient_mode":                config.LenientMode,
		"cache_duration":              config.CacheDuration.String(),
		"domain_validation_enabled":   config.DomainValidationEnabled,
		"public_key_path":             config.PublicKeyPath,
	}
}

// SaveConfig 保存配置到文件
func (l *CommercialConfigLoader) SaveConfig(config *SystemLicenseConfig) error {
	configPath := "configs/commercial.yaml"
	
	// 创建配置目录
	if err := os.MkdirAll("configs", 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}
	
	// 构建YAML配置
	yamlConfig := map[string]interface{}{
		"commercial_authorization": map[string]interface{}{
			"enabled":          config.Enabled,
			"development_mode": config.DevelopmentMode,
			"license_file": map[string]interface{}{
				"system_license_path": config.SystemLicensePath,
				"usage_license_dir":   config.UsageLicenseDir,
			},
			"validation": map[string]interface{}{
				"interval_hours":         int(config.ValidationInterval.Hours()),
				"offline_mode":           config.OfflineMode,
				"lenient_mode":           config.LenientMode,
				"cache_duration_minutes": int(config.CacheDuration.Minutes()),
			},
		},
		"domain_validation": map[string]interface{}{
			"enabled": config.DomainValidationEnabled,
		},
		"license_management": map[string]interface{}{
			"offline_license": map[string]interface{}{
				"public_key_path": config.PublicKeyPath,
			},
		},
	}
	
	// 序列化为YAML
	data, err := yaml.Marshal(yamlConfig)
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}
	
	// 写入文件
	if err := ioutil.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}
	
	l.logger.Info("Commercial license config saved", zap.String("path", configPath))
	return nil
}

// ReloadConfig 重新加载配置
func (l *CommercialConfigLoader) ReloadConfig() (*SystemLicenseConfig, error) {
	l.logger.Info("Reloading commercial license config")
	return l.LoadConfig()
}

// IsConfigValid 检查配置是否有效
func (l *CommercialConfigLoader) IsConfigValid(config *SystemLicenseConfig) bool {
	return l.validateConfig(config) == nil
}

// GetEnvironmentVariables 获取相关的环境变量列表
func (l *CommercialConfigLoader) GetEnvironmentVariables() []string {
	return []string{
		"GACMS_COMMERCIAL_AUTH_ENABLED",
		"GACMS_DEVELOPMENT_MODE",
		"GACMS_SYSTEM_LICENSE_PATH",
		"GACMS_USAGE_LICENSE_DIR",
		"GACMS_DOMAIN_VALIDATION_ENABLED",
		"GACMS_PUBLIC_KEY_PATH",
		"GACMS_LICENSE_VALIDATION_INTERVAL",
		"GACMS_LICENSE_CACHE_DURATION",
	}
}

// PrintConfigHelp 打印配置帮助信息
func (l *CommercialConfigLoader) PrintConfigHelp() {
	help := `
GACMS Commercial License Configuration Help:

Environment Variables:
  GACMS_COMMERCIAL_AUTH_ENABLED     - Enable/disable commercial authorization (true/false)
  GACMS_DEVELOPMENT_MODE            - Enable development mode (true/false)
  GACMS_SYSTEM_LICENSE_PATH         - Path to system license file
  GACMS_USAGE_LICENSE_DIR           - Directory for usage license files
  GACMS_DOMAIN_VALIDATION_ENABLED   - Enable domain validation (true/false)
  GACMS_PUBLIC_KEY_PATH             - Path to public key file for signature verification
  GACMS_LICENSE_VALIDATION_INTERVAL - License validation interval (e.g., "24h", "1h30m")
  GACMS_LICENSE_CACHE_DURATION      - License cache duration (e.g., "1h", "30m")

Configuration File:
  configs/commercial.yaml - YAML configuration file

Example:
  export GACMS_COMMERCIAL_AUTH_ENABLED=true
  export GACMS_DEVELOPMENT_MODE=false
  export GACMS_SYSTEM_LICENSE_PATH="./licenses/system.json"
`
	
	fmt.Println(strings.TrimSpace(help))
}
