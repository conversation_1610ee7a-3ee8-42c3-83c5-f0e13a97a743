# ADR-001: 路由架构和系统设置定位决策

## 状态
已接受 (2025-06-14)

## 背景
在GACMS架构设计过程中，需要确定以下关键架构决策：
1. HTTP路由框架的选择和架构设计
2. 系统设置和站点管理的架构定位
3. URL优化和二级域名绑定的实现方案
4. 多租户架构中的权限层级设计

## 决策

### 1. HTTP路由架构决策

#### 1.1 框架选择：Gin + 约定路由处理器
**决策**：采用Gin框架作为HTTP基础，结合自定义约定路由处理器实现懒加载。

**理由**：
- Gin提供高性能的HTTP处理和丰富的中间件生态
- 约定路由处理器实现模块懒加载和三入口支持
- 保持架构的性能优势和开发效率

#### 1.2 路由处理流程
```
HTTP请求 → Gin引擎 → 
基础中间件（日志、恢复）→ 
SiteResolver中间件（域名→站点ID）→ 
URLRewrite中间件（URL重写）→ 
约定路由处理器（模块懒加载）→ 
控制器调用 → JSON响应
```

#### 1.3 三入口路由系统
- **管理员入口**：`/{admin_entry}/*` - 可配置的管理员路径
- **API入口**：`/{api_entry}/*` - 第三方开发接口
- **公共入口**：`/*` - 前台用户访问

#### 1.4 约定路由规则
```
路径格式：/{entry_point}/{module}/{controller}/{action}/{params}
示例：
- /admin/user/list → user.UserController.AdminList()
- /api/user/123 → user.UserController.ApiShow(123)
- /user/profile → user.UserController.Profile()
```

### 2. 系统设置定位决策

#### 2.1 系统设置作为基础设施
**决策**：系统设置定位为基础设施，不是业务模块。

**理由**：
- 系统设置是所有模块的基础依赖
- 必须在应用启动时预加载，不能懒加载
- 需要超越租户边界的权限管理
- 避免循环依赖和复杂的启动顺序

#### 2.2 架构层次
```
GACMS架构层次：
├── 核心基础设施（Core Infrastructure）
│   ├── 数据库连接池
│   ├── 事件管理器
│   ├── 配置管理器
│   ├── 日志系统
│   └── 系统设置服务（站点管理、域名绑定）
├── 核心服务（Core Services）
│   ├── 模块代理工厂
│   ├── 约定路由处理器
│   └── 事件总线
├── 业务模块（Business Modules）
│   ├── user模块（用户管理）
│   ├── content模块（内容管理）
│   └── 其他业务模块
```

### 3. 站点管理归属决策

#### 3.1 站点管理属于系统设置基础设施
**决策**：站点管理功能归属于系统设置基础设施。

**理由**：
- 站点是多租户的顶层概念，属于系统级配置
- 站点管理需要超级管理员权限，不是普通租户功能
- 站点配置影响整个租户的数据隔离策略
- 其他模块都依赖站点配置，站点应该是基础设施

#### 3.2 权限层级模型
```
1. 平台层（Platform Level）
   - 管理者：超级管理员
   - 功能：站点管理、域名绑定、全局配置
   - 路由：/platform/admin/*
   - 权限：超越租户边界

2. 租户层（Tenant Level）
   - 管理者：站点管理员
   - 功能：内容管理、用户管理、站点配置
   - 路由：/admin/*（带站点隔离）
   - 权限：租户内部管理

3. 用户层（User Level）
   - 访问者：普通用户
   - 功能：浏览内容、用户注册等
   - 路由：/*（公共访问）
   - 权限：基础用户权限
```

### 4. URL优化和域名绑定决策

#### 4.1 基于现有架构扩展
**决策**：基于现有的域名绑定系统扩展URL重写功能。

**实现方案**：
- 扩展DomainBinding模型，添加URL重写配置
- 创建URLRewriteRule模型，支持复杂重写规则
- 实现URLRewrite中间件，处理URL重写逻辑
- 保持向后兼容，可选择性启用URL重写

#### 4.2 支持的绑定场景
- **模块绑定**：`blog.example.com` → 绑定到blog模块
- **栏目绑定**：`news.example.com` → 绑定到content模块的news栏目
- **自定义规则**：`/p/{id}` → `/content/post/show/{id}`

### 5. APIClient重构决策

#### 5.1 重构为ExternalAPIClient
**决策**：将APIClient重构为专门负责外部API调用的ExternalAPIClient。

**理由**：
- 模块间通信应该使用事件系统，不是HTTP调用
- APIClient应该专注于外部第三方服务集成
- 简化架构，明确职责边界

## 实施计划

### 阶段1：基础设施重构
1. 将系统设置相关代码移动到`internal/core/system/`
2. 更新依赖注入配置，将系统设置作为基础设施加载
3. 实现平台管理路由，不经过模块系统

### 阶段2：URL重写功能
1. 扩展DomainBinding和URLRewriteRule模型
2. 实现URLRewrite中间件
3. 集成到Gin中间件链

### 阶段3：APIClient重构
1. 重命名为ExternalAPIClient
2. 移除内部调用功能
3. 保持向后兼容性

## 影响

### 正面影响
- **架构清晰**：基础设施与业务模块明确分离
- **性能优化**：基础设施预加载，业务模块懒加载
- **权限清晰**：平台权限与租户权限明确分离
- **扩展性强**：支持复杂的URL重写和域名绑定

### 风险缓解
- **向后兼容**：保持现有API的兼容性
- **渐进迁移**：可以逐步迁移现有功能
- **文档完善**：提供详细的迁移指南

## 相关决策
- 遵循GACMS的14项核心设计原则
- 符合微核心+模块的架构设计
- 支持事件驱动的模块间通信

## 备注
本决策基于2025-06-14的架构讨论和分析，所有实施都需要经过用户确认和feedback MCP调用。
