/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/admin/port/http/controller/AppearanceController.go
 * @Description: Controller for admin appearance settings, delegating logic to AdminService.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package controller

import (
	"gacms/internal/modules/admin/application/service"
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
)

type AppearanceController struct {
	service *service.AdminService
}

func NewAppearanceController(service *service.AdminService) *AppearanceController {
	return &AppearanceController{
		service: service,
	}
}

func (c *AppearanceController) RegisterRoutes(rg *gin.RouterGroup) {
	appearanceGroup := rg.Group("/appearance")
	{
		appearanceGroup.GET("/settings", c.GetAppearanceSettings)
	}
}

// GetAppearanceSettings handles the GET /api/admin/appearance/settings endpoint.
// It delegates the actual data fetching to the AdminService.
func (c *AppearanceController) GetAppearanceSettings(ctx *gin.Context) {
	siteIDStr := ctx.Query("site_id")
	if siteIDStr == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "site_id query parameter is required"})
		return
	}
	siteID, err := strconv.ParseUint(siteIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid site_id format"})
		return
	}

	themeName := ctx.Query("theme_name")
	if themeName == "" {
		// Assuming a default backend theme name if not provided.
		themeName = "default_admin"
	}

	settings, err := c.service.GetAppearanceSettings(ctx.Request.Context(), uint(siteID), themeName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": settings})
} 