/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-09
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/contenttype/port/http/controller/ContentItemController.go
 * @Description: Data-driven controller for managing content item entries.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package controller

import (
	"gacms/internal/modules/contenttype/application/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type ContentItemController struct {
	svc *service.ContentItemService
}

func NewContentItemController(svc *service.ContentItemService) *ContentItemController {
	return &ContentItemController{svc: svc}
}

func (c *ContentItemController) CreateContentItem(ctx *gin.Context) {
	siteID, err := strconv.ParseUint(ctx.Param("siteId"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid site ID"})
		return
	}
	contentTypeSlug := ctx.Param("contentTypeSlug")

	var data map[string]interface{}
	if err := ctx.ShouldBindJSON(&data); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	item, err := c.svc.CreateContentItem(uint(siteID), contentTypeSlug, data)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, item)
}

func (c *ContentItemController) GetContentItems(ctx *gin.Context) {
	siteID, err := strconv.ParseUint(ctx.Param("siteId"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid site ID"})
		return
	}
	contentTypeSlug := ctx.Param("contentTypeSlug")

	items, err := c.svc.GetItemsByContentTypeSlug(uint(siteID), contentTypeSlug)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, items)
}

func (c *ContentItemController) GetContentItem(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid content item ID"})
		return
	}

	item, err := c.svc.GetItemByID(uint(id))
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Content item not found"})
		return
	}

	ctx.JSON(http.StatusOK, item)
}
