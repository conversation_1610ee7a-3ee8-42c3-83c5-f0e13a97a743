# GACMS双重许可证机制技术实现文档

## 概述

本文档详细描述GACMS双重许可证机制的技术实现，包括架构设计、核心组件、实现细节和使用方法。

## 架构设计

### 核心架构原则遵循

严格遵循GACMS的14条核心架构原则：

1. **React+Gin无头架构**: 许可证验证集成到Gin后端API服务
2. **多租户支持**: 系统许可证控制租户配额，使用许可证按租户分配
3. **模块核心通信**: 模块通过公共接口使用许可证验证服务
4. **事件观察者模式**: 许可证状态变化通过事件通知相关模块
5. **fx依赖注入**: 许可证管理器通过fx.Option声明，服务代理工厂懒加载
6. **路由规范**: 许可证API遵循/admin、/、/api路由规范
7. **CQRS/事件总线**: 许可证操作通过事件总线驱动
8. **懒加载**: 许可证验证在首次请求时自动加载
9. **领域驱动分层**: 许可证模块采用分层设计，接口通信
10. **显式声明**: 依赖、权限、配置都显式声明

### 双重许可证架构

```
┌─────────────────────────────────────────────────────────────┐
│                    GACMS系统                                │
├─────────────────────────────────────────────────────────────┤
│  系统许可证 (System License)                                │
│  ├─ 控制部署权限和租户配额                                   │
│  ├─ 决定系统最高可用版本                                     │
│  └─ 过期后整个系统降级到个人版                               │
├─────────────────────────────────────────────────────────────┤
│  使用许可证 (Usage License)                                 │
│  ├─ 租户A: 专业版许可证                                      │
│  ├─ 租户B: 商业版许可证                                      │
│  ├─ 租户C: 个人版(无需许可证)                                │
│  └─ 过期后对应租户降级到个人版                               │
└─────────────────────────────────────────────────────────────┘
```

### 三层功能检查机制

```
用户功能请求
    ↓
1. 编译边界检查
   ├─ 功能是否编译到系统中？
   ├─ 通过: 继续下一层检查
   └─ 失败: 尝试降级到个人版功能
    ↓
2. 安装配置检查
   ├─ 功能是否在安装时启用？
   ├─ 通过: 继续下一层检查
   └─ 失败: 尝试降级到个人版功能
    ↓
3. 许可证验证
   ├─ 功能是否有许可证授权？
   ├─ 通过: 允许访问
   └─ 失败: 尝试降级到个人版功能
    ↓
4. 个人版功能检查
   ├─ 是否为个人版基础功能？
   ├─ 是: 允许访问（降级成功）
   └─ 否: 拒绝访问（降级失败）
```

## 核心组件

### 1. LicenseManager - 许可证管理器

**职责**: 双重许可证的加载、验证、管理

**核心方法**:
```go
// 系统许可证管理
func (m *DefaultLicenseManager) LoadSystemLicense() error
func (m *DefaultLicenseManager) ValidateSystemLicense() error
func (m *DefaultLicenseManager) GetSystemLicenseInfo() *SystemLicense

// 使用许可证管理
func (m *DefaultLicenseManager) LoadUsageLicense(tenantDomain string) error
func (m *DefaultLicenseManager) ValidateUsageLicense(tenantDomain string) error
func (m *DefaultLicenseManager) GetUsageLicenseInfo(tenantDomain string) *UsageLicense

// 租户管理
func (m *DefaultLicenseManager) GetMaxTenants() int
func (m *DefaultLicenseManager) GetCurrentTenantCount() int
func (m *DefaultLicenseManager) CheckTenantAccess(ctx context.Context, tenantDomain string, featureName string) error

// 降级状态管理
func (m *DefaultLicenseManager) IsInDegradedMode() bool
func (m *DefaultLicenseManager) GetDegradationInfo() *DegradationInfo
```

### 2. FeatureGuard - 功能守卫

**职责**: 三层功能检查和降级机制

**核心方法**:
```go
// 功能访问检查
func (g *DefaultFeatureGuard) CheckFeatureAccess(ctx context.Context, tenantDomain string, featureName string) error
func (g *DefaultFeatureGuard) IsFeatureAvailable(ctx context.Context, tenantDomain string, featureName string) bool

// 限制检查
func (g *DefaultFeatureGuard) IsLimitExceeded(ctx context.Context, tenantDomain string, limitType string, currentValue int) bool
func (g *DefaultFeatureGuard) GetLimit(ctx context.Context, tenantDomain string, limitType string) int

// 降级处理
func (g *DefaultFeatureGuard) HandleFeatureDegradation(ctx context.Context, tenantDomain string, featureName string, err error) error
func (g *DefaultFeatureGuard) GetAvailableFeatures(ctx context.Context, tenantDomain string) []string
```

### 3. LicenseService - 许可证服务层

**职责**: 统一的业务接口，隐藏底层复杂性

**核心方法**:
```go
// 系统信息
func (s *DefaultLicenseService) GetSystemInfo() *SystemInfo
func (s *DefaultLicenseService) GetTenantInfo(tenantDomain string) *TenantInfo

// 双重许可证管理
func (s *DefaultLicenseService) InstallSystemLicense(licenseData string) *InstallResult
func (s *DefaultLicenseService) InstallUsageLicense(tenantDomain string, licenseData string) *InstallResult
func (s *DefaultLicenseService) GetSystemLicenseInfo() *SystemLicenseInfo
func (s *DefaultLicenseService) GetUsageLicenseInfo(tenantDomain string) *UsageLicenseInfo

// 功能访问检查
func (s *DefaultLicenseService) CheckFeatureAccess(ctx context.Context, featureName string) *FeatureAccessResult
func (s *DefaultLicenseService) CheckTenantFeatureAccess(ctx context.Context, tenantDomain string, featureName string) *FeatureAccessResult

// 降级状态管理
func (s *DefaultLicenseService) GetDegradationStatus() *DegradationStatus
func (s *DefaultLicenseService) ShouldShowUpgradePrompt() bool
func (s *DefaultLicenseService) GetUpgradeRecommendation() *UpgradeRecommendation
```

## 版本实现机制

### 个人版 - 系统固化
```go
// 硬编码在系统中，无需配置文件
type PersonalEditionProvider struct{}

func (p *PersonalEditionProvider) GetFeatures() []string {
    return []string{
        "basic_content",
        "basic_theme", 
        "basic_seo",
        "basic_user",
        "api_access",
    }
}

func (p *PersonalEditionProvider) GetLimit(limitType string) int {
    limits := map[string]int{
        "sites":     1,
        "users":     3,
        "api_calls": 1000,
    }
    return limits[limitType]
}
```

### 专业版 - 编译时固化
```go
// 从配置文件读取并编译时固化
type ProfessionalEditionProvider struct {
    features []string
    limits   map[string]int
}

// 编译时从configs/editions.yaml读取配置
func NewProfessionalEditionProvider() *ProfessionalEditionProvider {
    config := loadEditionConfig("professional")
    return &ProfessionalEditionProvider{
        features: config.Features,
        limits:   config.Limits,
    }
}
```

### 商业版 - 全能版跳过检查
```go
// 检测到商业版许可证后直接跳过所有检查
func (g *DefaultFeatureGuard) CheckFeatureAccess(ctx context.Context, tenantDomain string, featureName string) error {
    // 商业版跳过所有检查
    if g.isBusinessEdition(tenantDomain) {
        return nil // 直接通过
    }
    
    // 其他版本执行正常检查流程
    return g.performNormalCheck(ctx, tenantDomain, featureName)
}
```

## fx依赖注入集成

### 模块声明
```go
// internal/core/di/LicenseModule.go
var LicenseModule = fx.Options(
    // 配置提供者
    fx.Provide(NewDefaultLicenseConfig),
    fx.Provide(NewDefaultSystemLicenseConfig),
    
    // 核心服务
    fx.Provide(NewDefaultLicenseManager),
    fx.Provide(NewDefaultFeatureGuard),
    fx.Provide(NewDefaultLicenseService),
    
    // 版本提供者
    fx.Provide(NewPersonalEditionProvider),
    fx.Provide(NewProfessionalEditionProvider),
    
    // 编译时管理器
    fx.Provide(NewCompileTimeEditionManager),
)
```

### 模块使用
```go
// 模块通过fx.Option声明依赖
type ModuleParams struct {
    fx.In
    
    LicenseService service.LicenseService
    FeatureGuard   service.FeatureGuard
}

func NewModule(params ModuleParams) *Module {
    return &Module{
        licenseService: params.LicenseService,
        featureGuard:   params.FeatureGuard,
    }
}
```

## 事件观察者模式

### 许可证事件定义
```go
// 许可证状态变化事件
type LicenseStatusChangedEvent struct {
    TenantDomain string
    OldStatus    string
    NewStatus    string
    Reason       string
}

// 降级事件
type SystemDegradedEvent struct {
    Reason           string
    DegradedFeatures []string
    AvailableFeatures []string
}
```

### 事件观察者
```go
// 模块观察者
type ModuleObserver struct {
    eventBus EventBus
}

func (o *ModuleObserver) OnLicenseStatusChanged(event *LicenseStatusChangedEvent) {
    // 处理许可证状态变化
    if event.NewStatus == "expired" {
        o.handleLicenseExpired(event.TenantDomain)
    }
}

func (o *ModuleObserver) OnSystemDegraded(event *SystemDegradedEvent) {
    // 处理系统降级
    o.disableAdvancedFeatures(event.DegradedFeatures)
}
```

## 配置管理

### 商业授权配置 (configs/commercial.yaml)
```yaml
# 商业授权总开关
commercial_authorization:
  enabled: false  # 开发阶段默认关闭
  
# 系统许可证配置
system_license:
  path: "./licenses/system_license.json"
  validation_interval: 24h
  offline_mode: true
  
# 使用许可证配置  
usage_license:
  directory: "./licenses/usage/"
  validation_interval: 1h
  
# 降级配置
degradation:
  strategy: "graceful"
  show_upgrade_hints: true
  log_degradation: true
```

### 版本配置 (configs/editions.yaml)
```yaml
# 专业版配置（编译时读取）
professional:
  features:
    - basic_content
    - basic_theme
    - basic_seo
    - basic_user
    - advanced_theme
    - advanced_seo
    - workflow
    - api_access
  limits:
    sites: 5
    users: 20
    api_calls: 10000
```

## 使用示例

### 模块中使用许可证验证
```go
// 在模块控制器中
func (c *ContentController) CreatePost(ctx *gin.Context) {
    tenantDomain := c.getTenantDomain(ctx)
    
    // 检查功能权限
    if err := c.featureGuard.CheckFeatureAccess(ctx, tenantDomain, "basic_content"); err != nil {
        ctx.JSON(403, gin.H{"error": "Feature not available", "reason": err.Error()})
        return
    }
    
    // 检查限制
    currentPosts := c.getPostCount(tenantDomain)
    if c.featureGuard.IsLimitExceeded(ctx, tenantDomain, "posts", currentPosts) {
        ctx.JSON(403, gin.H{"error": "Post limit exceeded"})
        return
    }
    
    // 执行业务逻辑
    c.createPost(ctx)
}
```

### 中间件集成
```go
// 功能访问中间件
func FeatureAccessMiddleware(featureGuard FeatureGuard, featureName string) gin.HandlerFunc {
    return func(ctx *gin.Context) {
        tenantDomain := getTenantDomain(ctx)
        
        if err := featureGuard.CheckFeatureAccess(ctx, tenantDomain, featureName); err != nil {
            ctx.JSON(403, gin.H{
                "error": "Feature access denied",
                "feature": featureName,
                "reason": err.Error(),
            })
            ctx.Abort()
            return
        }
        
        ctx.Next()
    }
}

// 路由中使用
router.POST("/api/posts", FeatureAccessMiddleware(featureGuard, "basic_content"), contentController.CreatePost)
```

## 错误处理和降级

### 降级流程
```go
func (g *DefaultFeatureGuard) HandleFeatureDegradation(ctx context.Context, tenantDomain string, featureName string, err error) error {
    // 记录降级日志
    g.logger.Warn("Feature access failed, attempting degradation",
        zap.String("tenant", tenantDomain),
        zap.String("feature", featureName),
        zap.Error(err))
    
    // 检查是否为个人版功能
    if g.personalProvider.IsFeature(featureName) {
        g.logger.Info("Feature degraded to personal edition",
            zap.String("tenant", tenantDomain),
            zap.String("feature", featureName))
        return nil // 降级成功
    }
    
    // 降级失败
    return fmt.Errorf("feature not available in personal edition: %s", featureName)
}
```

### 用户友好的错误信息
```go
type FeatureAccessResult struct {
    Allowed      bool     `json:"allowed"`
    Edition      string   `json:"edition"`
    Reason       string   `json:"reason,omitempty"`
    Suggestions  []string `json:"suggestions,omitempty"`
    
    // 降级状态信息
    IsInDegradedMode  bool   `json:"is_in_degraded_mode"`
    DegradationReason string `json:"degradation_reason,omitempty"`
}
```

## 性能优化

### 缓存机制
```go
type DefaultLicenseManager struct {
    licenseCache map[string]interface{}
    cacheMutex   sync.RWMutex
    cacheExpiry  time.Duration
}

func (m *DefaultLicenseManager) getCachedLicense(key string) (interface{}, bool) {
    m.cacheMutex.RLock()
    defer m.cacheMutex.RUnlock()
    
    value, exists := m.licenseCache[key]
    return value, exists
}
```

### 懒加载
```go
// 许可证在首次访问时加载
func (m *DefaultLicenseManager) getSystemLicense() *SystemLicense {
    if m.systemLicense == nil {
        m.loadSystemLicense()
    }
    return m.systemLicense
}
```

## 监控和日志

### 关键指标监控
- 许可证验证成功/失败率
- 功能访问请求量
- 降级事件频率
- 租户配额使用情况

### 日志记录
```go
// 许可证验证日志
g.logger.Info("License validation result",
    zap.String("tenant", tenantDomain),
    zap.String("feature", featureName),
    zap.Bool("allowed", allowed),
    zap.String("edition", edition))

// 降级事件日志
g.logger.Warn("System degraded",
    zap.String("reason", reason),
    zap.Strings("available_features", availableFeatures))
```

---

**文档版本**: 1.0  
**创建日期**: 2025-06-22  
**维护者**: GACMS开发团队  
**状态**: 当前版本
