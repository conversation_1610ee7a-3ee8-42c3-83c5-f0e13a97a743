<!--
Author: Cion Nieh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
-->
# ADR-005: 统一扩展系统设计 (Unified Extension System)

## 状态
已接受

## 上下文
随着GACMS功能的丰富，系统需要一种统一、健壮的方式来管理不同类型的可扩展组件，主要包括：
1.  **主题 (Themes)**: 控制网站的外观和感觉。
2.  **模块 (Modules)**: 提供独立的、大型的业务功能（如论坛、商城）。
3.  **插件 (Plugins)**: 对现有功能进行轻量级的修改或增强（通过钩子）。

在早期开发中，主题的管理逻辑被部分地实现在`theme`模块中，而一个独立的`installer`模块被初步创建用于处理文件上传。这种分离导致了功能重复和职责不清。此外，为了项目的长远发展，需要为未来可能的"线上应用市场"功能预留扩展点。

因此，我们需要一个统一的、高内-定的扩展管理中心，它能够处理所有类型扩展的完整生命周期，并为未来的功能演进提供清晰的架构支持。

## 决策
我们决定设计并实现一个统一的**扩展模块 (`extension` module)**，并采纳以下核心设计决策：

1.  **单一职责模块**: 创建一个名为`extension`的新核心模块。该模块将是系统中唯一负责管理所有类型扩展（主题、模块、插件）生命周期的单元。其职责包括：
    *   安装 (从本地上传或未来从应用市场下载)
    *   卸载
    *   列出已安装的扩展
    *   激活与停用
    *   读取扩展的元数据（manifest 文件）

2.  **采用策略模式 (Strategy Pattern) 处理差异化逻辑**:
    为了优雅地处理不同类型扩展在"激活/停用"等生命周期阶段的根本差异，我们引入策略模式。
    *   定义一个通用的`ActivationStrategy`接口，包含`Activate`和`Deactivate`等方法。
    *   为每种扩展类型（Theme, Module, Plugin）提供一个具体的策略实现。例如，`ThemeActivationStrategy`的逻辑是修改站点的数据库记录，而`ModuleActivationStrategy`的逻辑可能是动态加载或修改配置文件。
    *   `ExtensionService`将根据扩展类型，在运行时动态选择并执行正确的策略，从而将通用流程与具体实现解耦。

3.  **明确其他模块的职责**:
    *   原有的`theme`模块将被重构，移除所有管理和发现逻辑，使其只专注于**应用**主题的核心职责（例如，为渲染引擎提供当前激活主题的模板和资源）。

## 后果

### 积极
- **高内聚，低耦合**: 所有扩展管理的逻辑都集中在`extension`模块中，职责非常清晰。
- **遵循开闭原则**: 策略模式的引入使得未来增加新的扩展类型（如`widget`）变得非常容易，只需添加新的策略实现，而无需修改`ExtensionService`的核心代码，系统可扩展性极强。
- **消除代码冗余**: 彻底解决了`theme`模块和旧`installer`模块之间的功能重叠问题。
- **架构清晰**: 为未来的"应用市场"功能提供了清晰、无缝的集成点。

### 消极
- **增加了抽象层**: 引入策略模式会比简单的if-else判断增加一些代码量和抽象层级，需要开发者理解其设计模式。
- **前期设计成本**: 需要为每种扩展类型定义清晰的策略接口和实现，前期设计工作量稍大。

### 中性
- 模块、插件的激活/停用逻辑需要进一步的详细设计（例如，模块的动态加载机制）。

---

## 实施注意事项

### 1. 扩展元信息 (Manifest)
所有扩展包的根目录都必须包含一个`[type].json`文件（如`theme.json`, `module.json`），用于描述其元信息。
```json
{
  "name": "My Awesome Theme",
  "version": "1.0.0",
  "description": "A brief description of the theme.",
  "author": "GACMS Team"
  // ...other type-specific fields
}
```

### 2. 策略的依赖注入
不同的策略实现可能需要不同的依赖（例如，`ThemeActivationStrategy`需要`SiteRepository`）。这些依赖应该通过依赖注入容器（fx）提供给具体的策略实例。

### 3. API设计
`ExtensionController`将提供统一的API端点来管理所有类型的扩展，例如：
- `POST /api/admin/extensions/activate` - 请求体: `{ "type": "theme", "name": "my-theme", "siteId": 1 }`
- `DELETE /api/admin/extensions/{type}/{name}`

---

## 为未来预留的设计：应用市场 (Future Consideration: Extension Marketplace)

虽然本次重构不直接实现应用市场功能，但整体架构为其预留了清晰的扩展路径。以下是未来的设计构想，记录于此以便后续开发。

### 1. 核心流程
1.  **浏览**: 前端通过API (`GET /api/admin/extensions/market?type=...`) 从GACMS后端请求市场列表。GACMS后端作为代理，向官方或第三方市场服务器请求数据，然后返回给前端。
2.  **安装**: 前端请求安装某个市场扩展 (`POST /api/admin/extensions/market/install`)，提供扩展的唯一ID。
3.  **下载**: GACMS后端根据ID向市场服务器请求一个安全的、临时的下载链接。
4.  **安装**: GACMS后端从该链接下载zip包到本地临时目录。
5.  **复用**: 一旦zip包下载到本地，后续流程就**完全复用**现有的`InstallFromUpload`逻辑（解压 -> 验证 -> 移动到最终目录）。

### 2. 后端服务 (`ExtensionService`) 需扩展的方法
- `ListMarketExtensions(query MarketQuery) ([]MarketExtension, error)`
- `InstallFromMarket(packageID string, version string) error`

### 3. 未来所需的数据模型
```go
// MarketExtension represents an extension available on the marketplace.
type MarketExtension struct {
    ID          string   `json:"id"`
    Type        string   `json:"type"` // "theme", "module", "plugin"
    Name        string   `json:"name"`
    Description string   `json:"description"`
    Author      string   `json:"author"`
    Versions    []string `json:"versions"`
    Rating      float64  `json:"rating"`
    Price       float64  `json:"price"` // 0 for free
}

// Transaction might be needed for paid extensions.
type Transaction struct {
    ID           uint
    UserID       uint
    ExtensionID  string
    Amount       float64
    Status       string
    CreatedAt    time.Time
}
```

### 4. 安全考量
- **包签名**: 市场下载的扩展包应有数字签名，GACMS后端在安装前必须验证签名，确保其来源可靠且未被篡改。
- **依赖管理**: 市场需要明确声明扩展所依赖的GACMS核心版本。
- **API认证**: GACMS后端与市场服务器之间的通信需要安全的API密钥认证。
- **支付集成**: 如果支持付费扩展，需要与安全的支付网关集成。

通过这种设计，未来的应用市场功能可以作为对现有`extension`模块的一个自然扩展，而非颠覆性的重做，充分体现了当前架构决策的价值。 