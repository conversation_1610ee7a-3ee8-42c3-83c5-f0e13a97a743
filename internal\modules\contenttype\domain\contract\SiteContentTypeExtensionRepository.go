/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/contenttype/domain/contract/SiteContentTypeExtensionRepository.go
 * @Description: 
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import "gacms/internal/modules/contenttype/domain/model"

type SiteContentTypeExtensionRepository interface {
	FindBySiteAndContentType(siteID, contentTypeID uint) (*model.SiteContentTypeExtension, error)
	// We can add Create, Update, Delete methods later as needed.
} 