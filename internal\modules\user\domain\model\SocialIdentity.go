/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: {current_date}
 * @LastEditors: Clion Nieh
 * @LastEditTime: {current_date}
 * @FilePath: internal/modules/user/domain/model/SocialIdentity.go
 * @Description: Defines the model for a user's social login identity.
 *
 * © {current_year} GACMS. All rights reserved.
 */
package model

// SocialIdentity represents a link between a member account and a third-party social provider.
type SocialIdentity struct {
	ID             uint   `gorm:"primaryKey"`
	MemberID       uint   `gorm:"not null;uniqueIndex:idx_provider_user"`
	Provider       string `gorm:"not null;size:50;uniqueIndex:idx_provider_user"`
	ProviderUserID string `gorm:"not null;uniqueIndex:idx_provider_user"`
} 