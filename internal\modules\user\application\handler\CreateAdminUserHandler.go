/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-13
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-13
 * @FilePath: internal/modules/user/application/handler/CreateAdminUserHandler.go
 * @Description: Handles the creation of a new administrator user.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package handler

import (
	"fmt"

	"github.com/niecl/GACMS/internal/modules/user/application/service"
	"github.com/niecl/GACMS/internal/modules/user/events"
	"github.com/niecl/GACMS/pkg/contract"
)

// CreateAdminUserHandler handles the CreateAdminUserEvent.
// It orchestrates the user creation process.
type CreateAdminUserHandler struct {
	userService service.UserService
	eventBus    contract.EventBus
}

// NewCreateAdminUserHandler creates a new handler for creating admin users.
// It requires the UserService to perform the core logic and the EventBus
// to dispatch subsequent events.
func NewCreateAdminUserHandler(userService service.UserService, eventBus contract.EventBus) *CreateAdminUserHandler {
	return &CreateAdminUserHandler{
		userService: userService,
		eventBus:    eventBus,
	}
}

// Handle processes the CreateAdminUserEvent.
func (h *CreateAdminUserHandler) Handle(event contract.Event) error {
	// Type assertion to get the specific event type
	createEvent, ok := event.(*events.CreateAdminUserEvent)
	if !ok {
		return fmt.Errorf("invalid event type for CreateAdminUserHandler")
	}

	// 1. Call the user service to perform the core business logic
	createdUser, err := h.userService.CreateUser(createEvent.Context(), createEvent.DTO)
	if err != nil {
		return fmt.Errorf("failed to create user in service: %w", err)
	}

	// 2. If successful, dispatch a "Fact Event" (AdminUserCreatedEvent)
	// This allows other parts of the system (like logging) to react in a decoupled way.
	factEvent := events.NewAdminUserCreatedEvent(createEvent.Context(), createdUser)
	if err := h.eventBus.Dispatch(factEvent); err != nil {
		// Log the error but don't fail the whole operation, as the user was already created.
		// The caller should be aware that the primary operation succeeded.
		// In a more robust system, this might be queued for a retry.
		fmt.Printf("warning: user created but failed to dispatch AdminUserCreatedEvent: %v\n", err)
	}

	return nil
} 