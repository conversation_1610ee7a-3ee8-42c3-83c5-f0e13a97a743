/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/banner/port/http/controller/BannerController.go
 * @Description: Controller for the banner module API.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package controller

import (
	"gacms/internal/modules/banner/application/dto"
	"gacms/internal/modules/banner/application/service"
	"gacms/pkg/response"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type BannerController struct {
	service *service.BannerService
}

func NewBannerController(service *service.BannerService) *BannerController {
	return &BannerController{service: service}
}

func (con *BannerController) Create(c *gin.Context) {
	var input dto.BannerCreateDTO
	if err := c.ShouldBindJSON(&input); err != nil {
		response.Error(c, http.StatusBadRequest, err.Error())
		return
	}
	banner, err := con.service.Create(&input)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to create banner")
		return
	}
	response.Success(c, banner)
}

func (con *BannerController) Update(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "Invalid banner ID")
		return
	}
	var input dto.BannerUpdateDTO
	if err := c.ShouldBindJSON(&input); err != nil {
		response.Error(c, http.StatusBadRequest, err.Error())
		return
	}
	banner, err := con.service.Update(uint(id), &input)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to update banner")
		return
	}
	response.Success(c, banner)
}

func (con *BannerController) Delete(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "Invalid banner ID")
		return
	}
	err = con.service.Delete(uint(id))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to delete banner")
		return
	}
	response.Success(c, nil)
}

func (con *BannerController) Get(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "Invalid banner ID")
		return
	}
	banner, err := con.service.GetByID(uint(id))
	if err != nil {
		response.Error(c, http.StatusNotFound, "Banner not found")
		return
	}
	response.Success(c, banner)
}

func (con *BannerController) List(c *gin.Context) {
	siteIDStr := c.Query("site_id")
	if siteIDStr == "" {
		response.Error(c, http.StatusBadRequest, "site_id query parameter is required")
		return
	}
	siteID, err := strconv.ParseUint(siteIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "Invalid site_id")
		return
	}

	groupSlug := c.Query("group_slug")
	banners, err := con.service.List(uint(siteID), groupSlug)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to list banners")
		return
	}
	response.Success(c, banners)
}

func (con *BannerController) ListGroups(c *gin.Context) {
	siteIDStr := c.Query("site_id")
	if siteIDStr == "" {
		response.Error(c, http.StatusBadRequest, "site_id query parameter is required")
		return
	}
	siteID, err := strconv.ParseUint(siteIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "Invalid site_id")
		return
	}

	groups, err := con.service.ListGroups(uint(siteID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to list groups")
		return
	}
	response.Success(c, groups)
}

func (c *BannerController) RegisterRoutes(rg *gin.RouterGroup) {
	bannerRoutes := rg.Group("/banners")
	{
		bannerRoutes.GET("/:siteId/position/:positionSlug", c.getBannersByPosition)
	}
}

func (c *BannerController) getBannersByPosition(ctx *gin.Context) {
	siteID, err := strconv.ParseUint(ctx.Param("siteId"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid site ID"})
		return
	}
	positionSlug := ctx.Param("positionSlug")

	banners, err := c.service.GetBannersByPositionSlug(uint(siteID), positionSlug)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if banners == nil {
		banners = []*model.ContentItem{}
	}

	ctx.JSON(http.StatusOK, banners)
} 