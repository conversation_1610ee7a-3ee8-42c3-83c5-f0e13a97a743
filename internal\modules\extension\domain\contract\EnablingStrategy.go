/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/extension/domain/contract/EnablingStrategy.go
 * @Description: Defines the interface for extension enabling/disabling strategies.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

// EnablingStrategy defines the interface for different extension enabling logics.
type EnablingStrategy interface {
	// Enable enables an extension. For a module/plugin, this might mean adding it
	// to a config file. For a theme, it just means it's available to be activated.
	Enable(extName string, siteID uint) error

	// Disable deactivates an extension.
	Disable(extName string, siteID uint) error
} 