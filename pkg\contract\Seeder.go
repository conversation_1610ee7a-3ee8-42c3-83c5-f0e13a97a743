/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-13
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-13
 * @FilePath: pkg/contract/Seeder.go
 * @Description: Defines the public contract for database seeders.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

// Seeder defines the interface for a database seeder.
// Seeders are used to populate the database with initial or test data.
type Seeder interface {
	// Name returns a unique name for the seeder.
	Name() string
	// Run executes the seeder logic.
	Run() error
} 