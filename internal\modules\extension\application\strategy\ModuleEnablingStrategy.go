/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/extension/application/strategy/ModuleEnablingStrategy.go
 * @Description: Implements the enabling strategy for modules via config file.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package strategy

import (
	"encoding/json"
	"fmt"
	"gacms/internal/modules/extension/domain/contract"
	"os"
	"sync"
)

const extensionsConfigFile = "configs/extensions.json"

type enabledExtensions struct {
	Modules []string `json:"modules"`
	Plugins []string `json:"plugins"`
}

// ModuleEnablingStrategy handles module enabling/disabling by editing a config file.
type ModuleEnablingStrategy struct {
	// Mutex to handle concurrent read/write to the config file
	mu sync.Mutex
}

func NewModuleEnablingStrategy() contract.EnablingStrategy {
	return &ModuleEnablingStrategy{}
}

func (s *ModuleEnablingStrategy) readConfig() (*enabledExtensions, error) {
	data, err := os.ReadFile(extensionsConfigFile)
	if err != nil {
		if os.IsNotExist(err) {
			return &enabledExtensions{Modules: []string{}, Plugins: []string{}}, nil
		}
		return nil, err
	}
	var cfg enabledExtensions
	if err := json.Unmarshal(data, &cfg); err != nil {
		return nil, err
	}
	return &cfg, nil
}

func (s *ModuleEnablingStrategy) writeConfig(cfg *enabledExtensions) error {
	data, err := json.MarshalIndent(cfg, "", "  ")
	if err != nil {
		return err
	}
	return os.WriteFile(extensionsConfigFile, data, 0644)
}

func (s *ModuleEnablingStrategy) Enable(extName string, siteID uint) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	cfg, err := s.readConfig()
	if err != nil {
		return fmt.Errorf("failed to read extensions config: %w", err)
	}

	// Check if already enabled
	for _, mod := range cfg.Modules {
		if mod == extName {
			return nil // Already enabled
		}
	}

	cfg.Modules = append(cfg.Modules, extName)
	return s.writeConfig(cfg)
}

func (s *ModuleEnablingStrategy) Disable(extName string, siteID uint) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	cfg, err := s.readConfig()
	if err != nil {
		return fmt.Errorf("failed to read extensions config: %w", err)
	}

	found := false
	var updatedModules []string
	for _, mod := range cfg.Modules {
		if mod == extName {
			found = true
		} else {
			updatedModules = append(updatedModules, mod)
		}
	}

	if !found {
		return fmt.Errorf("module '%s' is not currently enabled", extName)
	}

	cfg.Modules = updatedModules
	return s.writeConfig(cfg)
} 