/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/banner/domain/contract/BannerRepository.go
 * @Description: Defines the contract for banner data access.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"context"
	"gacms/internal/modules/banner/domain/model"
)

// BannerRepository defines the interface for banner data operations.
type BannerRepository interface {
	Create(ctx context.Context, banner *model.Banner) error
	Update(ctx context.Context, banner *model.Banner) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*model.Banner, error)
	ListByPositionID(ctx context.Context, positionID uint) ([]*model.Banner, error)
} 