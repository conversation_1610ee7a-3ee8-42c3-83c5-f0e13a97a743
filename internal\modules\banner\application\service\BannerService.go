/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/banner/application/service/BannerService.go
 * @Description: 
 * 
 * © 2025 GACMS. All rights reserved.
 */
package service

import (
	"encoding/json"
	"fmt"
	"gacms/internal/modules/contenttype/application/service"
	"gacms/internal/modules/contenttype/domain/model"
)

type BannerService struct {
	itemSvc *service.ContentItemService
}

func NewBannerService(itemSvc *service.ContentItemService) *BannerService {
	return &BannerService{itemSvc: itemSvc}
}

// GetBannersByPositionSlug fetches all banners for a specific position.
func (s *BannerService) GetBannersByPositionSlug(siteID uint, positionSlug string) ([]*model.ContentItem, error) {
	// 1. Find the banner position content item.
	bannerPositions, err := s.itemSvc.GetItemsByContentTypeSlug(siteID, "banner_position")
	if err != nil {
		return nil, fmt.Errorf("could not fetch banner positions: %w", err)
	}

	var positionID uint
	for _, pos := range bannerPositions {
		var data struct {
			Slug string `json:"slug"`
		}
		if err := json.Unmarshal(pos.Data, &data); err == nil {
			if data.Slug == positionSlug {
				positionID = pos.ID
				break
			}
		}
	}

	if positionID == 0 {
		return nil, fmt.Errorf("banner position with slug '%s' not found for site %d", positionSlug, siteID)
	}

	// 2. Fetch all banner items.
	// As before, we filter in memory for simplicity. A real implementation
	// would support filtering relations in the database query itself.
	allBanners, err := s.itemSvc.GetItemsByContentTypeSlug(siteID, "banner")
	if err != nil {
		return nil, fmt.Errorf("could not fetch banners: %w", err)
	}

	// 3. Filter banners belonging to the correct position.
	var resultBanners []*model.ContentItem
	for _, banner := range allBanners {
		var data struct {
			Position *uint `json:"position"`
		}
		if err := json.Unmarshal(banner.Data, &data); err == nil {
			if data.Position != nil && *data.Position == positionID {
				resultBanners = append(resultBanners, banner)
			}
		}
	}
	
	// Optional: Sort banners by 'order' field.

	return resultBanners, nil
}
