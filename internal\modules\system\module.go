/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/system/module.go
 * @Description: Defines the system module for dependency injection.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package system

import (
	"gacms/internal/modules/system/application/service"
	"gacms/internal/modules/system/domain/contract"
	"gacms/internal/modules/system/infrastructure/persistence"
	"gacms/internal/modules/system/port/http/controller"

	"go.uber.org/fx"
)

var Module = fx.Module("system",
	fx.Provide(
		// Provide the repository implementation and bind it to the interface
		fx.Annotate(
			persistence.NewSettingRepository,
			fx.As(new(contract.SettingRepository)),
		),
		// Provide the service implementation
		service.NewSettingService,
		// Provide the controller
		controller.NewSettingController,
	),
) 