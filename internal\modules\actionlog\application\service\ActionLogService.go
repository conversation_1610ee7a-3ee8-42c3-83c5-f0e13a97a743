/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/actionlog/application/service/ActionLogService.go
 * @Description: Service for generic action log operations.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"gacms/internal/modules/actionlog/domain/contract"
	"gacms/internal/modules/actionlog/domain/model"
	pkgContract "gacms/pkg/contract"
)

type ActionLogService struct {
	repo   contract.ActionLogRepository
	appCtx pkgContract.AppContext
}

func NewActionLogService(repo contract.ActionLogRepository, appCtx pkgContract.AppContext) *ActionLogService {
	return &ActionLogService{repo: repo, appCtx: appCtx}
}

// CreateLog creates a generic action log from a DTO.
// This is intended to be called from the controller that handles the POST /logs API endpoint.
func (s *ActionLogService) CreateLog(ctx context.Context, logData *model.ActionLog) error {
	// Here you might add extra validation or processing if needed.
	if err := s.repo.Create(ctx, logData); err != nil {
		s.appCtx.Logger().Error(ctx, "Failed to create action log", "error", err, "userID", logData.UserID, "action", logData.Description)
		return err
	}
	return nil
}

// ListLogs retrieves a paginated and filtered list of action logs.
// The filters are passed down from the controller.
func (s *ActionLogService) ListLogs(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*model.ActionLog, int64, error) {
	// The service can add extra logic here, e.g., enforcing non-overrideable filters based on user context.
	// For example, if ctx contains a site_id, it could be added to the filters here.
	return s.repo.List(ctx, page, pageSize, filters)
} 