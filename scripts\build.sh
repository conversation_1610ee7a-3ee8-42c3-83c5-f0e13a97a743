#!/bin/bash

# GACMS 编译脚本
# 支持根据版本配置进行编译时功能控制

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# 默认配置
DEFAULT_EDITION="personal"
DEFAULT_OUTPUT_DIR="$PROJECT_ROOT/dist"
DEFAULT_CONFIG_FILE="$PROJECT_ROOT/configs/editions.yaml"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [[ "$DEBUG" == "true" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
GACMS Build Script

Usage: $0 [OPTIONS]

Options:
    -e, --edition EDITION       Build edition (personal|professional|business)
    -o, --output DIR            Output directory (default: $DEFAULT_OUTPUT_DIR)
    -c, --config FILE           Edition config file (default: $DEFAULT_CONFIG_FILE)
    -t, --target OS/ARCH        Target platform (e.g., linux/amd64, windows/amd64)
    -v, --version VERSION       Version string
    --ldflags FLAGS             Additional ldflags
    --tags TAGS                 Additional build tags
    --debug                     Enable debug output
    --clean                     Clean before build
    --docker                    Build Docker image
    --help                      Show this help message

Examples:
    $0 -e personal              # Build personal edition
    $0 -e professional -t linux/amd64  # Build professional edition for Linux
    $0 -e business --docker     # Build business edition Docker image
    $0 --clean -e personal      # Clean and build personal edition

Supported Editions:
    personal        - Personal edition (basic features)
    professional    - Professional edition (advanced features)
    business        - Business edition (all features)

EOF
}

# 解析命令行参数
parse_args() {
    EDITION="$DEFAULT_EDITION"
    OUTPUT_DIR="$DEFAULT_OUTPUT_DIR"
    CONFIG_FILE="$DEFAULT_CONFIG_FILE"
    TARGET=""
    VERSION=""
    LDFLAGS=""
    TAGS=""
    DEBUG="false"
    CLEAN="false"
    DOCKER="false"

    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--edition)
                EDITION="$2"
                shift 2
                ;;
            -o|--output)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            -c|--config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            -t|--target)
                TARGET="$2"
                shift 2
                ;;
            -v|--version)
                VERSION="$2"
                shift 2
                ;;
            --ldflags)
                LDFLAGS="$2"
                shift 2
                ;;
            --tags)
                TAGS="$2"
                shift 2
                ;;
            --debug)
                DEBUG="true"
                shift
                ;;
            --clean)
                CLEAN="true"
                shift
                ;;
            --docker)
                DOCKER="true"
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 验证版本
validate_edition() {
    case "$EDITION" in
        personal|professional|business)
            log_info "Building $EDITION edition"
            ;;
        *)
            log_error "Invalid edition: $EDITION"
            log_error "Supported editions: personal, professional, business"
            exit 1
            ;;
    esac
}

# 读取版本配置
read_edition_config() {
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_warn "Config file not found: $CONFIG_FILE"
        log_warn "Using default configuration"
        return
    fi

    log_info "Reading edition config from: $CONFIG_FILE"
    
    # 这里可以使用yq或其他工具解析YAML配置
    # 暂时使用简单的grep方式
    log_debug "Edition config loaded"
}

# 设置构建标签
setup_build_tags() {
    BUILD_TAGS="$EDITION"
    
    # 添加额外的构建标签
    if [[ -n "$TAGS" ]]; then
        BUILD_TAGS="$BUILD_TAGS,$TAGS"
    fi
    
    log_info "Build tags: $BUILD_TAGS"
}

# 设置版本信息
setup_version_info() {
    if [[ -z "$VERSION" ]]; then
        # 尝试从git获取版本
        if command -v git >/dev/null 2>&1 && [[ -d "$PROJECT_ROOT/.git" ]]; then
            VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo "dev")
        else
            VERSION="dev"
        fi
    fi
    
    BUILD_TIME=$(date -u '+%Y-%m-%d_%H:%M:%S_UTC')
    COMMIT_HASH=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    
    log_info "Version: $VERSION"
    log_info "Build time: $BUILD_TIME"
    log_info "Commit: $COMMIT_HASH"
}

# 设置ldflags
setup_ldflags() {
    BASE_LDFLAGS="-s -w"
    BASE_LDFLAGS="$BASE_LDFLAGS -X 'main.Version=$VERSION'"
    BASE_LDFLAGS="$BASE_LDFLAGS -X 'main.BuildTime=$BUILD_TIME'"
    BASE_LDFLAGS="$BASE_LDFLAGS -X 'main.CommitHash=$COMMIT_HASH'"
    BASE_LDFLAGS="$BASE_LDFLAGS -X 'main.Edition=$EDITION'"
    
    if [[ -n "$LDFLAGS" ]]; then
        BASE_LDFLAGS="$BASE_LDFLAGS $LDFLAGS"
    fi
    
    log_debug "LDFLAGS: $BASE_LDFLAGS"
}

# 清理构建目录
clean_build() {
    if [[ "$CLEAN" == "true" ]]; then
        log_info "Cleaning build directory: $OUTPUT_DIR"
        rm -rf "$OUTPUT_DIR"
    fi
    
    mkdir -p "$OUTPUT_DIR"
}

# 构建二进制文件
build_binary() {
    log_info "Building GACMS $EDITION edition..."
    
    cd "$PROJECT_ROOT"
    
    # 设置输出文件名
    BINARY_NAME="gacms-$EDITION"
    if [[ -n "$TARGET" ]]; then
        GOOS=$(echo "$TARGET" | cut -d'/' -f1)
        GOARCH=$(echo "$TARGET" | cut -d'/' -f2)
        BINARY_NAME="$BINARY_NAME-$GOOS-$GOARCH"
        
        if [[ "$GOOS" == "windows" ]]; then
            BINARY_NAME="$BINARY_NAME.exe"
        fi
        
        export GOOS GOARCH
        log_info "Target platform: $GOOS/$GOARCH"
    fi
    
    OUTPUT_PATH="$OUTPUT_DIR/$BINARY_NAME"
    
    # 执行构建
    log_info "Building binary: $OUTPUT_PATH"
    
    go build \
        -tags "$BUILD_TAGS" \
        -ldflags "$BASE_LDFLAGS" \
        -o "$OUTPUT_PATH" \
        ./cmd/gacms
    
    if [[ $? -eq 0 ]]; then
        log_info "Build successful: $OUTPUT_PATH"
        
        # 显示文件信息
        if command -v ls >/dev/null 2>&1; then
            ls -lh "$OUTPUT_PATH"
        fi
    else
        log_error "Build failed"
        exit 1
    fi
}

# 构建Docker镜像
build_docker() {
    if [[ "$DOCKER" != "true" ]]; then
        return
    fi
    
    log_info "Building Docker image for $EDITION edition..."
    
    cd "$PROJECT_ROOT"
    
    DOCKER_TAG="gacms:$EDITION-$VERSION"
    
    docker build \
        --build-arg EDITION="$EDITION" \
        --build-arg VERSION="$VERSION" \
        --build-arg BUILD_TIME="$BUILD_TIME" \
        --build-arg COMMIT_HASH="$COMMIT_HASH" \
        -t "$DOCKER_TAG" \
        -f Dockerfile \
        .
    
    if [[ $? -eq 0 ]]; then
        log_info "Docker image built: $DOCKER_TAG"
    else
        log_error "Docker build failed"
        exit 1
    fi
}

# 生成构建信息
generate_build_info() {
    BUILD_INFO_FILE="$OUTPUT_DIR/build-info.json"
    
    cat > "$BUILD_INFO_FILE" << EOF
{
    "edition": "$EDITION",
    "version": "$VERSION",
    "build_time": "$BUILD_TIME",
    "commit_hash": "$COMMIT_HASH",
    "build_tags": "$BUILD_TAGS",
    "target": "$TARGET",
    "go_version": "$(go version | cut -d' ' -f3)",
    "builder": "$(whoami)@$(hostname)"
}
EOF
    
    log_info "Build info saved: $BUILD_INFO_FILE"
}

# 验证构建结果
validate_build() {
    BINARY_NAME="gacms-$EDITION"
    if [[ -n "$TARGET" ]]; then
        GOOS=$(echo "$TARGET" | cut -d'/' -f1)
        GOARCH=$(echo "$TARGET" | cut -d'/' -f2)
        BINARY_NAME="$BINARY_NAME-$GOOS-$GOARCH"
        
        if [[ "$GOOS" == "windows" ]]; then
            BINARY_NAME="$BINARY_NAME.exe"
        fi
    fi
    
    OUTPUT_PATH="$OUTPUT_DIR/$BINARY_NAME"
    
    if [[ ! -f "$OUTPUT_PATH" ]]; then
        log_error "Binary not found: $OUTPUT_PATH"
        exit 1
    fi
    
    # 如果是本地平台，尝试运行版本检查
    if [[ -z "$TARGET" ]] || [[ "$TARGET" == "$(go env GOOS)/$(go env GOARCH)" ]]; then
        log_info "Validating binary..."
        if "$OUTPUT_PATH" --version >/dev/null 2>&1; then
            log_info "Binary validation successful"
        else
            log_warn "Binary validation failed (this may be normal if dependencies are missing)"
        fi
    fi
}

# 主函数
main() {
    log_info "GACMS Build Script"
    log_info "=================="
    
    parse_args "$@"
    validate_edition
    read_edition_config
    setup_build_tags
    setup_version_info
    setup_ldflags
    clean_build
    build_binary
    build_docker
    generate_build_info
    validate_build
    
    log_info "Build completed successfully!"
    log_info "Output directory: $OUTPUT_DIR"
}

# 执行主函数
main "$@"
