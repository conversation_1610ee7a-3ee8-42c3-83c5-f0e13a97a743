/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/media/domain/contract/MediaRepository.go
 * @Description: Defines the repository contract for media assets.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"gacms/internal/modules/media/domain/model"
)

// MediaRepository defines the persistence operations for Media assets.
type MediaRepository interface {
	Create(media *model.Media) error
	GetByID(id uint) (*model.Media, error)
	List(page int, pageSize int, filters map[string]interface{}) ([]*model.Media, int64, error)
	Delete(id uint) error
	Update(media *model.Media) error
} 