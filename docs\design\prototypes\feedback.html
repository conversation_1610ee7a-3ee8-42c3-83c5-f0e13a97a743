<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 用户反馈管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .feedback-card {
            transition: all 0.3s ease;
        }
        
        .feedback-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
        }
        
        .feedback-item {
            transition: all 0.3s ease;
        }
        
        .feedback-item:hover {
            background-color: rgba(75, 85, 99, 0.1);
        }
        
        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        /* 反馈详情样式 */
        .feedback-detail {
            border-left: 3px solid;
            padding-left: 1rem;
            margin-bottom: 1rem;
        }
        
        /* 回复框样式 */
        .reply-box {
            border: 1px solid rgba(75, 85, 99, 0.3);
            border-radius: 0.5rem;
            transition: all 0.2s ease;
        }
        
        .reply-box:focus-within {
            border-color: rgba(59, 130, 246, 0.5);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
        }
        
        /* 回复历史样式 */
        .reply-history-item {
            border-left: 2px solid rgba(75, 85, 99, 0.3);
            padding-left: 1rem;
            margin-bottom: 1rem;
        }
        
        .reply-history-item.staff {
            border-left-color: #3b82f6;
        }
        
        .reply-history-item.user {
            border-left-color: #10b981;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="marketing.html" class="hover:text-white">营销</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">用户反馈</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">用户反馈管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="exportFeedbackBtn" class="flex items-center justify-center bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-all">
                            <i class="fas fa-file-export mr-2"></i>
                            导出反馈
                        </button>
                        <button id="settingsBtn" class="flex items-center justify-center bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-all">
                            <i class="fas fa-cog mr-2"></i>
                            设置
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 反馈数据概览 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <!-- 待处理反馈 -->
                <div class="feedback-card bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-clock text-yellow-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">待处理反馈</h3>
                            <div class="text-2xl font-bold text-white mt-1">12</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-red-400">
                            <i class="fas fa-arrow-up mr-1"></i>3
                        </span>
                        <span class="text-gray-400 ml-2">较昨日</span>
                    </div>
                </div>
                
                <!-- 今日新增 -->
                <div class="feedback-card bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-plus-circle text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">今日新增</h3>
                            <div class="text-2xl font-bold text-white mt-1">8</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-green-400">
                            <i class="fas fa-arrow-up mr-1"></i>2
                        </span>
                        <span class="text-gray-400 ml-2">较昨日</span>
                    </div>
                </div>
                
                <!-- 平均响应时间 -->
                <div class="feedback-card bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-reply text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">平均响应时间</h3>
                            <div class="text-2xl font-bold text-white mt-1">4.2小时</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-green-400">
                            <i class="fas fa-arrow-down mr-1"></i>0.5小时
                        </span>
                        <span class="text-gray-400 ml-2">较上周</span>
                    </div>
                </div>
                
                <!-- 用户满意度 -->
                <div class="feedback-card bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-smile text-purple-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">用户满意度</h3>
                            <div class="text-2xl font-bold text-white mt-1">92%</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-green-400">
                            <i class="fas fa-arrow-up mr-1"></i>3%
                        </span>
                        <span class="text-gray-400 ml-2">较上月</span>
                    </div>
                </div>
            </div>
            
            <!-- 反馈管理主区域 -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 左侧反馈列表 -->
                <div class="lg:col-span-1">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <!-- 搜索和筛选 -->
                        <div class="mb-4">
                            <div class="relative">
                                <input type="text" placeholder="搜索反馈..." class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <div class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-400">
                                    <i class="fas fa-search"></i>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 筛选选项 -->
                        <div class="flex flex-wrap gap-2 mb-4">
                            <div class="relative">
                                <select class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="all">所有状态</option>
                                    <option value="pending">待处理</option>
                                    <option value="processing">处理中</option>
                                    <option value="resolved">已解决</option>
                                    <option value="closed">已关闭</option>
                                </select>
                                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                                    <i class="fas fa-chevron-down text-xs"></i>
                                </div>
                            </div>
                            
                            <div class="relative">
                                <select class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="all">所有类型</option>
                                    <option value="bug">问题反馈</option>
                                    <option value="feature">功能建议</option>
                                    <option value="complaint">投诉</option>
                                    <option value="praise">表扬</option>
                                </select>
                                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                                    <i class="fas fa-chevron-down text-xs"></i>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 反馈列表 -->
                        <div class="space-y-3">
                            <!-- 反馈项 1 - 当前选中 -->
                            <div class="feedback-item bg-blue-500/10 border border-blue-500/30 rounded-lg p-3 cursor-pointer">
                                <div class="flex justify-between items-start">
                                    <h4 class="text-white font-medium">登录页面响应缓慢</h4>
                                    <span class="status-badge bg-yellow-500/20 text-yellow-400">待处理</span>
                                </div>
                                <p class="text-gray-400 text-sm line-clamp-2 mt-1">在使用Chrome浏览器访问登录页面时，加载速度非常慢，有时需要等待10秒以上才能完全加载。</p>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="flex items-center text-sm text-gray-400">
                                        <i class="fas fa-user mr-1"></i>
                                        <span>张明</span>
                                    </div>
                                    <div class="text-sm text-gray-400">今天 09:45</div>
                                </div>
                            </div>
                            
                            <!-- 反馈项 2 -->
                            <div class="feedback-item bg-gray-800/30 border border-gray-700 rounded-lg p-3 cursor-pointer">
                                <div class="flex justify-between items-start">
                                    <h4 class="text-white font-medium">希望增加暗黑模式</h4>
                                    <span class="status-badge bg-blue-500/20 text-blue-400">处理中</span>
                                </div>
                                <p class="text-gray-400 text-sm line-clamp-2 mt-1">建议增加暗黑模式，在夜间使用时对眼睛更友好。希望能在下一版本中实现这个功能。</p>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="flex items-center text-sm text-gray-400">
                                        <i class="fas fa-user mr-1"></i>
                                        <span>李华</span>
                                    </div>
                                    <div class="text-sm text-gray-400">昨天 16:30</div>
                                </div>
                            </div>
                            
                            <!-- 反馈项 3 -->
                            <div class="feedback-item bg-gray-800/30 border border-gray-700 rounded-lg p-3 cursor-pointer">
                                <div class="flex justify-between items-start">
                                    <h4 class="text-white font-medium">导出功能异常</h4>
                                    <span class="status-badge bg-yellow-500/20 text-yellow-400">待处理</span>
                                </div>
                                <p class="text-gray-400 text-sm line-clamp-2 mt-1">尝试导出数据时出现错误，系统提示"导出失败"，但没有具体的错误信息。</p>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="flex items-center text-sm text-gray-400">
                                        <i class="fas fa-user mr-1"></i>
                                        <span>王强</span>
                                    </div>
                                    <div class="text-sm text-gray-400">昨天 14:15</div>
                                </div>
                            </div>
                            
                            <!-- 反馈项 4 -->
                            <div class="feedback-item bg-gray-800/30 border border-gray-700 rounded-lg p-3 cursor-pointer">
                                <div class="flex justify-between items-start">
                                    <h4 class="text-white font-medium">界面设计非常棒</h4>
                                    <span class="status-badge bg-green-500/20 text-green-400">已解决</span>
                                </div>
                                <p class="text-gray-400 text-sm line-clamp-2 mt-1">新版本的界面设计非常漂亮，操作也更加流畅了，感谢团队的努力！</p>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="flex items-center text-sm text-gray-400">
                                        <i class="fas fa-user mr-1"></i>
                                        <span>赵丽</span>
                                    </div>
                                    <div class="text-sm text-gray-400">2天前</div>
                                </div>
                            </div>
                            
                            <!-- 反馈项 5 -->
                            <div class="feedback-item bg-gray-800/30 border border-gray-700 rounded-lg p-3 cursor-pointer">
                                <div class="flex justify-between items-start">
                                    <h4 class="text-white font-medium">移动端适配问题</h4>
                                    <span class="status-badge bg-blue-500/20 text-blue-400">处理中</span>
                                </div>
                                <p class="text-gray-400 text-sm line-clamp-2 mt-1">在iPhone 13上使用时，部分按钮位置偏移，无法正常点击。</p>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="flex items-center text-sm text-gray-400">
                                        <i class="fas fa-user mr-1"></i>
                                        <span>陈明</span>
                                    </div>
                                    <div class="text-sm text-gray-400">3天前</div>
                                </div>
                            </div>
                            
                            <!-- 反馈项 6 -->
                            <div class="feedback-item bg-gray-800/30 border border-gray-700 rounded-lg p-3 cursor-pointer">
                                <div class="flex justify-between items-start">
                                    <h4 class="text-white font-medium">数据统计不准确</h4>
                                    <span class="status-badge bg-green-500/20 text-green-400">已解决</span>
                                </div>
                                <p class="text-gray-400 text-sm line-clamp-2 mt-1">仪表盘上显示的数据与实际导出的报表数据有差异，请核实。</p>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="flex items-center text-sm text-gray-400">
                                        <i class="fas fa-user mr-1"></i>
                                        <span>林小明</span>
                                    </div>
                                    <div class="text-sm text-gray-400">4天前</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 分页 -->
                        <div class="flex justify-between items-center mt-6">
                            <div class="text-sm text-gray-400">
                                显示 1-6 条，共 24 条
                            </div>
                            <div class="flex space-x-1">
                                <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <button class="px-3 py-1 bg-blue-500 text-white rounded-md">
                                    1
                                </button>
                                <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                                    2
                                </button>
                                <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                                    3
                                </button>
                                <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧反馈详情 -->
                <div class="lg:col-span-2">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <!-- 反馈详情头部 -->
                        <div class="flex justify-between items-start mb-6">
                            <div>
                                <h3 class="text-xl font-bold text-white mb-2">登录页面响应缓慢</h3>
                                <div class="flex flex-wrap items-center">
                                    <span class="status-badge bg-yellow-500/20 text-yellow-400 mr-3">待处理</span>
                                    <span class="text-gray-400 text-sm mr-3">
                                        <i class="fas fa-calendar-alt mr-1"></i>
                                        提交于：2025-04-15 09:45
                                    </span>
                                    <span class="text-gray-400 text-sm">
                                        <i class="fas fa-user mr-1"></i>
                                        提交人：张明
                                    </span>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <button class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded-lg text-sm">
                                    <i class="fas fa-user-plus mr-1"></i>
                                    分配
                                </button>
                                <button class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded-lg text-sm">
                                    <i class="fas fa-tag mr-1"></i>
                                    标签
                                </button>
                                <div class="relative">
                                    <button class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded-lg text-sm">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 反馈详情内容 -->
                        <div class="mb-6">
                            <div class="feedback-detail border-l-yellow-500 bg-gray-800/30 p-4 rounded-r-lg">
                                <p class="text-gray-200 mb-4">在使用Chrome浏览器访问登录页面时，加载速度非常慢，有时需要等待10秒以上才能完全加载。这个问题在Firefox浏览器上也存在，但在Edge浏览器上似乎正常。</p>
                                <p class="text-gray-200 mb-4">我的系统环境：</p>
                                <ul class="list-disc list-inside text-gray-300 mb-4 pl-4">
                                    <li>操作系统：Windows 11</li>
                                    <li>浏览器：Chrome 112.0.5615.138</li>
                                    <li>网络：100Mbps光纤</li>
                                </ul>
                                <p class="text-gray-200">希望能尽快解决这个问题，谢谢！</p>
                                
                                <!-- 附件 -->
                                <div class="mt-4 pt-4 border-t border-gray-700">
                                    <h5 class="text-white font-medium mb-2">附件</h5>
                                    <div class="flex items-center bg-gray-700/50 rounded-lg p-2">
                                        <i class="fas fa-file-image text-blue-400 text-xl mr-3"></i>
                                        <div class="flex-1">
                                            <div class="text-white text-sm">登录页面加载截图.png</div>
                                            <div class="text-gray-400 text-xs">2.3 MB</div>
                                        </div>
                                        <button class="text-gray-400 hover:text-white p-1">
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 标签 -->
                        <div class="mb-6">
                            <h5 class="text-white font-medium mb-2">标签</h5>
                            <div class="flex flex-wrap">
                                <span class="tag bg-red-500/20 text-red-400">Bug</span>
                                <span class="tag bg-blue-500/20 text-blue-400">性能问题</span>
                                <span class="tag bg-purple-500/20 text-purple-400">前端</span>
                                <span class="tag bg-gray-500/20 text-gray-400">登录页面</span>
                                <button class="tag bg-gray-700 text-gray-400 hover:text-white">
                                    <i class="fas fa-plus mr-1"></i>
                                    添加标签
                                </button>
                            </div>
                        </div>
                        
                        <!-- 处理状态 -->
                        <div class="mb-6">
                            <h5 class="text-white font-medium mb-2">处理状态</h5>
                            <div class="flex space-x-2">
                                <button class="px-3 py-2 bg-yellow-500/20 text-yellow-400 rounded-lg border border-yellow-500/30">
                                    待处理
                                </button>
                                <button class="px-3 py-2 bg-gray-700 text-gray-300 rounded-lg border border-gray-600 hover:bg-gray-600">
                                    处理中
                                </button>
                                <button class="px-3 py-2 bg-gray-700 text-gray-300 rounded-lg border border-gray-600 hover:bg-gray-600">
                                    已解决
                                </button>
                                <button class="px-3 py-2 bg-gray-700 text-gray-300 rounded-lg border border-gray-600 hover:bg-gray-600">
                                    已关闭
                                </button>
                            </div>
                        </div>
                        
                        <!-- 回复历史 -->
                        <div class="mb-6">
                            <h5 class="text-white font-medium mb-4">回复历史</h5>
                            <div class="space-y-4">
                                <!-- 系统自动回复 -->
                                <div class="reply-history-item system">
                                    <div class="flex justify-between items-start mb-2">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center mr-2">
                                                <i class="fas fa-robot text-gray-400"></i>
                                            </div>
                                            <span class="text-gray-300 font-medium">系统自动回复</span>
                                        </div>
                                        <span class="text-gray-400 text-sm">2025-04-15 09:46</span>
                                    </div>
                                    <p class="text-gray-300">感谢您的反馈！我们已收到您的问题，将尽快处理并回复您。</p>
                                </div>
                                
                                <!-- 技术支持回复 -->
                                <div class="reply-history-item staff">
                                    <div class="flex justify-between items-start mb-2">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 rounded-full bg-blue-500/20 flex items-center justify-center mr-2">
                                                <i class="fas fa-headset text-blue-400"></i>
                                            </div>
                                            <span class="text-blue-400 font-medium">技术支持 - 李工</span>
                                        </div>
                                        <span class="text-gray-400 text-sm">2025-04-15 10:30</span>
                                    </div>
                                    <p class="text-gray-300 mb-2">您好，感谢您的反馈。我们已经注意到登录页面在某些浏览器上加载缓慢的问题。请问您能否提供更多信息：</p>
                                    <ol class="list-decimal list-inside text-gray-300 mb-2 pl-4">
                                        <li>这个问题是最近才出现的吗？</li>
                                        <li>您是否清除过浏览器缓存后再尝试？</li>
                                        <li>是否在其他设备上也遇到同样的问题？</li>
                                    </ol>
                                    <p class="text-gray-300">我们会尽快调查并解决这个问题。</p>
                                </div>
                                
                                <!-- 用户回复 -->
                                <div class="reply-history-item user">
                                    <div class="flex justify-between items-start mb-2">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center mr-2">
                                                <i class="fas fa-user text-green-400"></i>
                                            </div>
                                            <span class="text-green-400 font-medium">张明</span>
                                        </div>
                                        <span class="text-gray-400 text-sm">2025-04-15 11:15</span>
                                    </div>
                                    <p class="text-gray-300 mb-2">您好，感谢您的回复。以下是更多信息：</p>
                                    <ol class="list-decimal list-inside text-gray-300 mb-2 pl-4">
                                        <li>这个问题是在最近的系统更新后出现的，大约一周前。</li>
                                        <li>我已经尝试过清除浏览器缓存，但问题依然存在。</li>
                                        <li>我在公司和家里的电脑上都遇到了这个问题，但手机App正常。</li>
                                    </ol>
                                    <p class="text-gray-300">希望这些信息对解决问题有所帮助。</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 回复框 -->
                        <div>
                            <h5 class="text-white font-medium mb-2">回复</h5>
                            <div class="reply-box bg-gray-800/30 rounded-lg p-2">
                                <textarea class="w-full bg-transparent border-0 text-white focus:outline-none resize-none" rows="4" placeholder="输入您的回复..."></textarea>
                                <div class="flex justify-between items-center pt-2 border-t border-gray-700">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1" title="添加表情">
                                            <i class="fas fa-smile"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-white p-1" title="添加附件">
                                            <i class="fas fa-paperclip"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-white p-1" title="添加图片">
                                            <i class="fas fa-image"></i>
                                        </button>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                                            保存为模板
                                        </button>
                                        <button class="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                                            发送回复
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 页面底部版权信息 -->
            <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
                © 2025 GACMS 后台管理系统 - 版本 v1.0.0
            </footer>
        </div>
    </main>

    <!-- 必要的JS脚本 -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 反馈项点击事件
            const feedbackItems = document.querySelectorAll('.feedback-item');
            
            feedbackItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 移除所有项的选中状态
                    feedbackItems.forEach(fi => {
                        fi.classList.remove('bg-blue-500/10', 'border-blue-500/30');
                        fi.classList.add('bg-gray-800/30', 'border-gray-700');
                    });
                    
                    // 设置当前项为选中状态
                    this.classList.remove('bg-gray-800/30', 'border-gray-700');
                    this.classList.add('bg-blue-500/10', 'border-blue-500/30');
                });
            });
            
            // 处理状态按钮点击事件
            const statusButtons = document.querySelectorAll('.mb-6:nth-child(3) button');
            
            statusButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有按钮的选中状态
                    statusButtons.forEach(btn => {
                        btn.classList.remove('bg-yellow-500/20', 'text-yellow-400', 'border-yellow-500/30');
                        btn.classList.add('bg-gray-700', 'text-gray-300', 'border-gray-600');
                    });
                    
                    // 设置当前按钮为选中状态
                    this.classList.remove('bg-gray-700', 'text-gray-300', 'border-gray-600');
                    
                    // 根据按钮文本设置不同的颜色
                    const text = this.textContent.trim();
                    if (text === '待处理') {
                        this.classList.add('bg-yellow-500/20', 'text-yellow-400', 'border-yellow-500/30');
                    } else if (text === '处理中') {
                        this.classList.add('bg-blue-500/20', 'text-blue-400', 'border-blue-500/30');
                    } else if (text === '已解决') {
                        this.classList.add('bg-green-500/20', 'text-green-400', 'border-green-500/30');
                    } else if (text === '已关闭') {
                        this.classList.add('bg-gray-500/20', 'text-gray-400', 'border-gray-500/30');
                    }
                });
            });
            
            // 导出按钮点击事件
            document.getElementById('exportFeedbackBtn').addEventListener('click', function() {
                alert('反馈数据导出功能将在新窗口打开！');
            });
            
            // 设置按钮点击事件
            document.getElementById('settingsBtn').addEventListener('click', function() {
                alert('反馈管理设置功能将在新窗口打开！');
            });
        });
    </script>
</body>
</html>