/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/user/application/service/AdminRoleService.go
 * @Description: Service for managing admin roles and permissions.
 *
 * © 2025 GACMS. All rights reserved.
 */
package service

import (
	"context"
	"gacms/internal/modules/user/application/dto"
	"gacms/internal/modules/user/domain/contract"
	"gacms/internal/modules/user/domain/model"
)

// AdminRoleService provides business logic for admin roles and permissions.
type AdminRoleService struct {
	repo         contract.AdminRoleRepository
	permissionRepo contract.AdminPermissionRepository
}

// NewAdminRoleService creates a new AdminRoleService.
func NewAdminRoleService(repo contract.AdminRoleRepository, permissionRepo contract.AdminPermissionRepository) *AdminRoleService {
	return &AdminRoleService{repo: repo, permissionRepo: permissionRepo}
}

// CreateRole creates a new admin role.
func (s *AdminRoleService) CreateRole(ctx context.Context, input *dto.AdminRoleCreateDTO) (*model.AdminRole, error) {
	// Here you would typically add business logic,
	// e.g., check if a role with the same slug already exists for the given SiteID.
	
	role := &model.AdminRole{
		Name:        input.Name,
		Slug:        input.Slug,
		Description: input.Description,
		SiteID:      input.SiteID,
	}

	err := s.repo.Create(role)
	if err != nil {
		return nil, err
	}

	return role, nil
}

// GetRoleByID retrieves a role by its ID.
func (s *AdminRoleService) GetRoleByID(ctx context.Context, id uint) (*model.AdminRole, error) {
	return s.repo.GetByID(id)
}

// ListRoles lists all roles for a given site.
func (s *AdminRoleService) ListRoles(ctx context.Context, siteID uint, page, pageSize int) ([]*model.AdminRole, int64, error) {
	return s.repo.List(page, pageSize, siteID)
}

// UpdateRole updates an existing role.
func (s *AdminRoleService) UpdateRole(ctx context.Context, id uint, input *dto.AdminRoleUpdateDTO) (*model.AdminRole, error) {
	role, err := s.repo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// Update fields from DTO
	if input.Name != "" {
		role.Name = input.Name
	}
	if input.Description != nil {
		role.Description = *input.Description
	}

	err = s.repo.Update(role)
	return role, err
}

// DeleteRole deletes a role by its ID.
func (s *AdminRoleService) DeleteRole(ctx context.Context, id uint) error {
	return s.repo.Delete(id)
}

// AssignPermissionsToRole assigns a set of permissions to a role.
func (s *AdminRoleService) AssignPermissionsToRole(ctx context.Context, roleID uint, permissionIDs []uint) error {
	// Here you could add logic to check if the current user has permission to assign all these permissions.
	return s.repo.AssignPermissionsToRole(roleID, permissionIDs)
}

// SyncPermissions synchronizes the permissions from all modules into the database.
func (s *AdminRoleService) SyncPermissions(ctx context.Context, permissions []*model.AdminPermission) error {
	return s.repo.SyncPermissions(permissions)
}

func (s *AdminRoleService) GetRolePermissions(roleID uint) ([]model.AdminPermission, error) {
	return s.repo.GetPermissionsByRoleID(roleID)
}

func (s *AdminRoleService) UpdateRolePermissions(roleID uint, permissionIDs []uint) error {
	role, err := s.repo.GetByID(roleID)
	if err != nil {
		return err
	}

	var permissions []model.AdminPermission
	if len(permissionIDs) > 0 {
		// In a real application, you should also check if these permission IDs are valid.
		if err := s.permissionRepo.GetDB().Where("id IN ?", permissionIDs).Find(&permissions).Error; err != nil {
			return err
		}
	}
	
	return s.repo.UpdateRolePermissions(role, permissions)
} 