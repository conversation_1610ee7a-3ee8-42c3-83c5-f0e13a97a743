# GACMS Global System Configuration
# This file contains default settings for the entire platform.
# Values here can be overridden by site-specific configs or environment variables.

# Server configuration
server:
  port: "8080"
  timeout: 30 # in seconds

# Routing configuration
routing:
  # Entry points configuration for enhanced security
  entry_points:
    # Admin entry point (configurable for security)
    # Can be changed to any custom path like "management", "backend", "secure-admin", etc.
    admin: "admin"

    # API entry point (usually fixed but configurable)
    api: "api"

    # Public entry point (root path, usually not changed)
    public: ""

  # Route security settings
  security:
    # Whether to hide admin entry point in error messages
    hide_admin_path: true

    # Whether to require HTTPS for admin routes
    admin_require_https: false

# Logging configuration
log:
  level: "info" # Log level: debug, info, warn, error
  encoding: "json" # Log encoding: "json" or "console"

# Database configuration
database:
  # Data Source Name (DSN) for the primary database connection.
  # WARNING: Replace 'password' with your actual database password.
  dsn: "user:password@tcp(127.0.0.1:3306)/gacms?charset=utf8mb4&parseTime=True&loc=Local"