/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/site/application/service/SiteService.go
 * @Description: Service for site operations.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"fmt"
	"gacms/internal/modules/site/application/dto"
	"gacms/internal/modules/site/domain/contract"
	"gacms/internal/modules/site/domain/model"
	themeContract "gacms/internal/modules/theme/domain/contract"
	"gacms/pkg/jwt"
)

type SiteService struct {
	repo          contract.SiteRepository
	themeRepo     themeContract.ThemeRepository
	permissionSvc *jwt.PermissionService
}

func NewSiteService(repo contract.SiteRepository, themeRepo themeContract.ThemeRepository, permissionSvc *jwt.PermissionService) *SiteService {
	return &SiteService{
		repo:          repo,
		themeRepo:     themeRepo,
		permissionSvc: permissionSvc,
	}
}

func (s *SiteService) CreateSite(input *dto.SiteCreateDTO) (*model.Site, error) {
	isActive := true
	if input.IsActive != nil {
		isActive = *input.IsActive
	}

	site := &model.Site{
		Name:        input.Name,
		Domain:      input.Domain,
		IsActive:    isActive,
		Description: input.Description,
	}
	err := s.repo.Create(site)
	return site, err
}

func (s *SiteService) GetSite(id uint) (*model.Site, error) {
	return s.repo.GetByID(id)
}

func (s *SiteService) ListSites(page, pageSize int) ([]*model.Site, int64, error) {
	return s.repo.List(page, pageSize)
}

func (s *SiteService) UpdateSite(id uint, input *dto.SiteUpdateDTO, user *userModel.Admin) (*model.Site, error) {
	site, err := s.repo.GetByID(id)
	if err != nil {
		return nil, err
	}

	isSuperAdmin := user.IsSuperAdmin()

	// Theme validation for non-super-admins
	if !isSuperAdmin {
		if input.FrontendTheme != "" {
			installed, err := s.themeRepo.IsThemeInstalledForSite(id, input.FrontendTheme)
			if err != nil {
				return nil, err
			}
			if !installed {
				return nil, fmt.Errorf("frontend theme '%s' is not installed for this site", input.FrontendTheme)
			}
		}
		if input.BackendTheme != "" {
			installed, err := s.themeRepo.IsThemeInstalledForSite(id, input.BackendTheme)
			if err != nil {
				return nil, err
			}
			if !installed {
				return nil, fmt.Errorf("backend theme '%s' is not installed for this site", input.BackendTheme)
			}
		}
	}

	if input.Name != "" {
		site.Name = input.Name
	}
	if input.Domain != "" {
		site.Domain = input.Domain
	}
	if input.IsActive != nil {
		site.IsActive = *input.IsActive
	}
	if input.FrontendTheme != "" {
		site.FrontendTheme = input.FrontendTheme
	}
	if input.BackendTheme != "" {
		site.BackendTheme = input.BackendTheme
	}
	if input.DefaultLang != "" {
		site.DefaultLang = input.DefaultLang
	}
	if input.AvailableLangs != "" {
		site.AvailableLangs = input.AvailableLangs
	}

	err = s.repo.Update(site)
	if err != nil {
		return nil, err
	}
	return site, nil
}

func (s *SiteService) DeleteSite(id uint) error {
	return s.repo.Delete(id)
} 