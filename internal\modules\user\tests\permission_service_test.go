/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/user/tests/permission_service_test.go
 * @Description: PermissionService单元测试
 * 
 * © 2025 GACMS. All rights reserved.
 */
package tests

import (
	"context"
	"errors"
	"gacms/internal/modules/user/application/service"
	"gacms/internal/modules/user/domain/model"
	"gacms/pkg/contract"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// 模拟依赖

// MockRoleRepository 模拟角色仓库
type MockRoleRepository struct {
	mock.Mock
}

func (m *MockRoleRepository) Create(ctx context.Context, role *model.Role) error {
	args := m.Called(ctx, role)
	return args.Error(0)
}

func (m *MockRoleRepository) Update(ctx context.Context, role *model.Role) error {
	args := m.Called(ctx, role)
	return args.Error(0)
}

func (m *MockRoleRepository) Delete(ctx context.Context, roleID uint) error {
	args := m.Called(ctx, roleID)
	return args.Error(0)
}

func (m *MockRoleRepository) FindByID(ctx context.Context, roleID uint) (*model.Role, error) {
	args := m.Called(ctx, roleID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Role), args.Error(1)
}

func (m *MockRoleRepository) FindAll(ctx context.Context, userType model.UserType, siteID uint) ([]*model.Role, error) {
	args := m.Called(ctx, userType, siteID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.Role), args.Error(1)
}

func (m *MockRoleRepository) FindRolesByUserID(ctx context.Context, userID uint, userType model.UserType) ([]*model.Role, error) {
	args := m.Called(ctx, userID, userType)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.Role), args.Error(1)
}

func (m *MockRoleRepository) FindPermissionsByRoleID(ctx context.Context, roleID uint) ([]*model.Permission, error) {
	args := m.Called(ctx, roleID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.Permission), args.Error(1)
}

func (m *MockRoleRepository) SyncPermissions(ctx context.Context, roleID uint, permissionIDs []uint) error {
	args := m.Called(ctx, roleID, permissionIDs)
	return args.Error(0)
}

// MockPermissionRepository 模拟权限仓库
type MockPermissionRepository struct {
	mock.Mock
}

func (m *MockPermissionRepository) Create(ctx context.Context, permission *model.Permission) error {
	args := m.Called(ctx, permission)
	return args.Error(0)
}

func (m *MockPermissionRepository) Update(ctx context.Context, permission *model.Permission) error {
	args := m.Called(ctx, permission)
	return args.Error(0)
}

func (m *MockPermissionRepository) Delete(ctx context.Context, permissionID uint) error {
	args := m.Called(ctx, permissionID)
	return args.Error(0)
}

func (m *MockPermissionRepository) GetByID(ctx context.Context, permissionID uint) (*model.Permission, error) {
	args := m.Called(ctx, permissionID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Permission), args.Error(1)
}

func (m *MockPermissionRepository) GetBySlug(ctx context.Context, slug string) (*model.Permission, error) {
	args := m.Called(ctx, slug)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Permission), args.Error(1)
}

func (m *MockPermissionRepository) List(ctx context.Context, options interface{}) ([]*model.Permission, int64, error) {
	args := m.Called(ctx, options)
	if args.Get(0) == nil {
		return nil, args.Int64(1), args.Error(2)
	}
	return args.Get(0).([]*model.Permission), args.Int64(1), args.Error(2)
}

func (m *MockPermissionRepository) FindByIDs(ctx context.Context, ids []uint) ([]*model.Permission, error) {
	args := m.Called(ctx, ids)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.Permission), args.Error(1)
}

// MockAdminRepository 模拟管理员仓库
type MockAdminRepository struct {
	mock.Mock
}

func (m *MockAdminRepository) FindByID(ctx context.Context, adminID uint) (*model.Admin, error) {
	args := m.Called(ctx, adminID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Admin), args.Error(1)
}

// MockEventManager 模拟事件管理器
type MockEventManager struct {
	mock.Mock
}

func (m *MockEventManager) Dispatch(event contract.IEvent) {
	m.Called(event)
}

// MockLogger 模拟日志器
type MockLogger struct {
	mock.Mock
}

func (m *MockLogger) Debug(ctx context.Context, msg string, keysAndValues ...interface{}) {
	m.Called(ctx, msg, keysAndValues)
}

func (m *MockLogger) Info(ctx context.Context, msg string, keysAndValues ...interface{}) {
	m.Called(ctx, msg, keysAndValues)
}

func (m *MockLogger) Warn(ctx context.Context, msg string, keysAndValues ...interface{}) {
	m.Called(ctx, msg, keysAndValues)
}

func (m *MockLogger) Error(ctx context.Context, msg string, keysAndValues ...interface{}) {
	m.Called(ctx, msg, keysAndValues)
}

// 测试用例

func TestPermissionService_Can(t *testing.T) {
	// 准备模拟对象
	mockRoleRepo := new(MockRoleRepository)
	mockAdminRepo := new(MockAdminRepository)
	mockPermRepo := new(MockPermissionRepository)
	mockLogger := new(MockLogger)
	mockEventManager := new(MockEventManager)

	// 创建服务实例
	permService := service.NewPermissionService(
		mockRoleRepo,
		mockAdminRepo,
		mockPermRepo,
		mockLogger,
		mockEventManager,
	)

	// 测试场景: 超级管理员始终有所有权限
	t.Run("SuperAdmin_AlwaysHasPermission", func(t *testing.T) {
		// 模拟超级管理员
		admin := &model.Admin{
			ID:          1,
			IsSuperAdmin: true,
		}
		mockAdminRepo.On("FindByID", mock.Anything, uint(1)).Return(admin, nil).Once()

		// 执行测试
		hasPermission, err := permService.Can(context.Background(), 1, model.AdminUser, "any.permission")

		// 验证结果
		assert.NoError(t, err)
		assert.True(t, hasPermission)
		mockAdminRepo.AssertExpectations(t)
	})

	// 测试场景: 用户有所需权限
	t.Run("User_HasPermission", func(t *testing.T) {
		// 模拟普通管理员
		admin := &model.Admin{
			ID:          2,
			IsSuperAdmin: false,
		}
		mockAdminRepo.On("FindByID", mock.Anything, uint(2)).Return(admin, nil).Once()

		// 模拟用户角色
		roles := []*model.Role{
			{ID: 1, Name: "Editor"},
		}
		mockRoleRepo.On("FindRolesByUserID", mock.Anything, uint(2), model.AdminUser).Return(roles, nil).Once()

		// 模拟角色权限
		permissions := []*model.Permission{
			{ID: 1, Slug: "content.edit"},
			{ID: 2, Slug: "content.view"},
		}
		mockRoleRepo.On("FindPermissionsByRoleID", mock.Anything, uint(1)).Return(permissions, nil).Once()

		// 执行测试
		hasPermission, err := permService.Can(context.Background(), 2, model.AdminUser, "content.edit")

		// 验证结果
		assert.NoError(t, err)
		assert.True(t, hasPermission)
		mockAdminRepo.AssertExpectations(t)
		mockRoleRepo.AssertExpectations(t)
	})

	// 测试场景: 用户没有所需权限
	t.Run("User_DoesNotHavePermission", func(t *testing.T) {
		// 模拟普通管理员
		admin := &model.Admin{
			ID:          3,
			IsSuperAdmin: false,
		}
		mockAdminRepo.On("FindByID", mock.Anything, uint(3)).Return(admin, nil).Once()

		// 模拟用户角色
		roles := []*model.Role{
			{ID: 2, Name: "Viewer"},
		}
		mockRoleRepo.On("FindRolesByUserID", mock.Anything, uint(3), model.AdminUser).Return(roles, nil).Once()

		// 模拟角色权限
		permissions := []*model.Permission{
			{ID: 2, Slug: "content.view"},
		}
		mockRoleRepo.On("FindPermissionsByRoleID", mock.Anything, uint(2)).Return(permissions, nil).Once()

		// 执行测试
		hasPermission, err := permService.Can(context.Background(), 3, model.AdminUser, "content.edit")

		// 验证结果
		assert.NoError(t, err)
		assert.False(t, hasPermission)
		mockAdminRepo.AssertExpectations(t)
		mockRoleRepo.AssertExpectations(t)
	})

	// 测试场景: 前台会员用户
	t.Run("MemberUser_Permission", func(t *testing.T) {
		// 模拟会员角色
		roles := []*model.Role{
			{ID: 3, Name: "Member"},
		}
		mockRoleRepo.On("FindRolesByUserID", mock.Anything, uint(10), model.MemberUser).Return(roles, nil).Once()

		// 模拟角色权限
		permissions := []*model.Permission{
			{ID: 5, Slug: "post.comment"},
		}
		mockRoleRepo.On("FindPermissionsByRoleID", mock.Anything, uint(3)).Return(permissions, nil).Once()

		// 执行测试
		hasPermission, err := permService.Can(context.Background(), 10, model.MemberUser, "post.comment")

		// 验证结果
		assert.NoError(t, err)
		assert.True(t, hasPermission)
		mockRoleRepo.AssertExpectations(t)
	})
}

func TestPermissionService_CreatePermission(t *testing.T) {
	// 准备模拟对象
	mockRoleRepo := new(MockRoleRepository)
	mockAdminRepo := new(MockAdminRepository)
	mockPermRepo := new(MockPermissionRepository)
	mockLogger := new(MockLogger)
	mockEventManager := new(MockEventManager)

	// 创建服务实例
	permService := service.NewPermissionService(
		mockRoleRepo,
		mockAdminRepo,
		mockPermRepo,
		mockLogger,
		mockEventManager,
	)

	// 测试场景: 成功创建权限
	t.Run("CreatePermission_Success", func(t *testing.T) {
		// 模拟Gin上下文
		ctx, _ := gin.CreateTestContext(nil)
		ctx.Request = &http.Request{
			Header: make(http.Header),
		}
		ctx.Request = ctx.Request.WithContext(context.Background())

		// 模拟不存在权限
		mockPermRepo.On("GetBySlug", mock.Anything, "posts.manage").Return(nil, errors.New("not found")).Once()

		// 模拟创建权限
		mockPermRepo.On("Create", mock.Anything, mock.AnythingOfType("*model.Permission")).Run(func(args mock.Arguments) {
			perm := args.Get(1).(*model.Permission)
			perm.ID = 1 // 模拟设置ID
		}).Return(nil).Once()

		// 模拟事件分发
		mockEventManager.On("Dispatch", mock.AnythingOfType("*events.PermissionCreatedEvent")).Return().Once()

		// 执行测试
		payload := &service.CreatePermissionPayload{
			Slug:        "posts.manage",
			Description: "Manage posts",
			SiteID:      1,
			CreatedBy:   2,
		}
		perm, err := permService.CreatePermission(ctx, payload)

		// 验证结果
		assert.NoError(t, err)
		assert.NotNil(t, perm)
		assert.Equal(t, "posts.manage", perm.Slug)
		assert.Equal(t, "Manage posts", perm.Description)
		mockPermRepo.AssertExpectations(t)
		mockEventManager.AssertExpectations(t)
	})

	// 测试场景: 创建已存在的权限
	t.Run("CreatePermission_AlreadyExists", func(t *testing.T) {
		// 模拟Gin上下文
		ctx, _ := gin.CreateTestContext(nil)
		ctx.Request = &http.Request{
			Header: make(http.Header),
		}
		ctx.Request = ctx.Request.WithContext(context.Background())

		// 模拟已存在权限
		existingPerm := &model.Permission{
			ID:          2,
			Slug:        "users.manage",
			Description: "Manage users",
		}
		mockPermRepo.On("GetBySlug", mock.Anything, "users.manage").Return(existingPerm, nil).Once()

		// 执行测试
		payload := &service.CreatePermissionPayload{
			Slug:        "users.manage",
			Description: "Manage users",
			SiteID:      1,
			CreatedBy:   2,
		}
		perm, err := permService.CreatePermission(ctx, payload)

		// 验证结果
		assert.Error(t, err)
		assert.Nil(t, perm)
		assert.Equal(t, service.ErrPermissionExists, err)
		mockPermRepo.AssertExpectations(t)
	})
}