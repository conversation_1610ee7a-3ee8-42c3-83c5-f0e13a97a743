/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/theme/domain/model/ThemeSetting.go
 * @Description: Defines the database model for storing theme setting overrides.
 *
 * © 2025 GACMS. All rights reserved.
 */

package model

import (
	"encoding/json"
	"gorm.io/gorm"
)

// ThemeSetting stores site-specific customizations for a particular theme as a JSON object.
type ThemeSetting struct {
	gorm.Model
	SiteID    uint   `gorm:"uniqueIndex:idx_site_theme;not null"`
	ThemeName string `gorm:"type:varchar(255);uniqueIndex:idx_site_theme;not null"`
	Settings  JSON   `gorm:"type:json"`
}

// JSON is a custom type for handling JSON data in GORM
type JSON json.RawMessage

// Scan implements the gorm.Scanner interface for JSON type
func (j *JSON) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return gorm.ErrInvalidData
	}
	*j = append((*j)[0:0], bytes...)
	return nil
}

// Value implements the driver.Valuer interface for JSON type
func (j JSON) Value() (interface{}, error) {
	if len(j) == 0 {
		return nil, nil
	}
	return json.RawMessage(j).MarshalJSON()
}

// MarshalJSON returns j as the JSON encoding of j.
func (j JSON) MarshalJSON() ([]byte, error) {
	if j == nil {
		return []byte("null"), nil
	}
	return j, nil
}

// UnmarshalJSON sets *j to a copy of data.
func (j *JSON) UnmarshalJSON(data []byte) error {
	if j == nil {
		return errors.New("json.RawMessage: UnmarshalJSON on nil pointer")
	}
	*j = append((*j)[0:0], data...)
	return nil
}

// GormDataType returns the data type of JSON for gorm
func (JSON) GormDataType() string {
	return "json"
}

func (ThemeSetting) TableName() string {
	return "theme_settings"
} 