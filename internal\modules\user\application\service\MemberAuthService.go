/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2024-07-16
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2024-07-16
 * @FilePath: internal/modules/user/application/service/MemberAuthService.go
 * @Description: Service for member authentication business logic.
 *
 * © 2024 GACMS. All rights reserved.
 */
package service

import (
	"context"
	"errors"
	"gacms/internal/modules/user/domain/contract"
	"gacms/internal/modules/user/domain/model"
	pkgContract "gacms/pkg/contract"
	"gacms/pkg/utils"

	"gorm.io/gorm"
)

var (
	ErrMemberExists         = errors.New("member with the same username, email, or phone already exists")
	ErrMemberNotFound       = errors.New("member not found")
	ErrInvalidPassword      = errors.New("invalid password")
	ErrSocialIdentityExists = errors.New("social identity already exists and is linked to another account")
)

type MemberAuthService struct {
	repo   contract.MemberRepository
	appCtx pkgContract.AppContext
}

func NewMemberAuthService(repo contract.MemberRepository, appCtx pkgContract.AppContext) *MemberAuthService {
	return &MemberAuthService{repo: repo, appCtx: appCtx}
}

// RegisterPayload defines the structure for member registration
type RegisterPayload struct {
	SiteID   uint64 `json:"site_id" binding:"required"`
	Username string `json:"username" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
	Phone    string `json:"phone"`
	Nickname string `json:"nickname"`
}

func (s *MemberAuthService) Register(ctx context.Context, payload *RegisterPayload) (*model.Member, error) {
	// Check if user already exists
	if _, err := s.repo.GetByUsername(ctx, payload.SiteID, payload.Username); !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, ErrMemberExists
	}
	if _, err := s.repo.GetByEmail(ctx, payload.SiteID, payload.Email); !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, ErrMemberExists
	}
	if payload.Phone != "" {
		if _, err := s.repo.GetByPhone(ctx, payload.SiteID, payload.Phone); !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrMemberExists
		}
	}

	hashedPassword, err := utils.HashPassword(payload.Password)
	if err != nil {
		s.appCtx.Logger().Error(ctx, "Failed to hash password", "error", err)
		return nil, err
	}

	member := &model.Member{
		SiteID:   payload.SiteID,
		Username: payload.Username,
		Email:    payload.Email,
		Password: hashedPassword,
		Phone:    payload.Phone,
		Nickname: payload.Nickname,
		Status:   model.UserStatusActive,
	}

	if err := s.repo.Create(ctx, member); err != nil {
		s.appCtx.Logger().Error(ctx, "Failed to create member", "error", err)
		return nil, err
	}

	return member, nil
}

// LoginPayload defines the structure for member login
type LoginPayload struct {
	SiteID   uint64 `json:"site_id" binding:"required"`
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

func (s *MemberAuthService) Login(ctx context.Context, payload *LoginPayload) (string, error) {
	member, err := s.repo.GetByUsername(ctx, payload.SiteID, payload.Username)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", ErrMemberNotFound
		}
		s.appCtx.Logger().Error(ctx, "Failed to get member by username", "error", err)
		return "", err
	}

	if !utils.CheckPasswordHash(payload.Password, member.Password) {
		return "", ErrInvalidPassword
	}

	claims := map[string]interface{}{
		"user_id":   member.ID,
		"username":  member.Username,
		"user_type": "member",
		"site_id":   member.SiteID,
	}

	token, err := s.appCtx.Auth().GenerateToken(claims)
	if err != nil {
		s.appCtx.Logger().Error(ctx, "Failed to generate token", "error", err)
		return "", err
	}

	return token, nil
}

// LinkSocialIdentityPayload defines the payload for linking a social account
type LinkSocialIdentityPayload struct {
	MemberID uint64 `json:"member_id" binding:"required"`
	Provider string `json:"provider" binding:"required"`
	OpenID   string `json:"open_id" binding:"required"`
	Token    string `json:"token"`
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
}

func (s *MemberAuthService) LinkSocialIdentity(ctx context.Context, payload *LinkSocialIdentityPayload) (*model.SocialIdentity, error) {
	// Check if the social identity is already linked to another account
	if _, err := s.repo.GetSocialIdentity(ctx, payload.Provider, payload.OpenID); !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, ErrSocialIdentityExists
	}
	
	// Check if the member exists
	if _, err := s.repo.GetByID(ctx, payload.MemberID); err != nil {
		return nil, ErrMemberNotFound
	}

	identity := &model.SocialIdentity{
		MemberID: payload.MemberID,
		Provider: payload.Provider,
		OpenID:   payload.OpenID,
		Token:    payload.Token,
		Nickname: payload.Nickname,
		Avatar:   payload.Avatar,
	}

	if err := s.repo.CreateSocialIdentity(ctx, identity); err != nil {
		s.appCtx.Logger().Error(ctx, "Failed to create social identity", "error", err)
		return nil, err
	}

	return identity, nil
} 