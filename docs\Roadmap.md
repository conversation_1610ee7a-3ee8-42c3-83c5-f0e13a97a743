<!--
Author: Cion Nieh
EMAIL: <EMAIL>
Copyright (c) 2025 Cion Nieh
-->
# 产品路线图 (Roadmap) - GACMS

## 目录

- [1. 路线图概述](#1-路线图概述)
- [2. 版本规划策略](#2-版本规划策略)
- [3. 详细版本规划](#3-详细版本规划)
  - [3.1 MVP (最小可行产品)](#31-mvp-最小可行产品)
  - [3.2 V1.1 (核心功能增强)](#32-v11-核心功能增强)
  - [3.3 V1.2 (多端与生态初步构建)](#33-v12-多端与生态初步构建)
  - [3.4 V2.0 (智能化与平台化)](#34-v20-智能化与平台化)
- [4. 功能优先级矩阵](#4-功能优先级矩阵)
- [5. 详细时间线计划 (里程碑)](#5-详细时间线计划-里程碑)
- [6. 资源规划 (初步建议)](#6-资源规划-初步建议)
- [7. 风险管理](#7-风险管理)

---

## 1. 路线图概述

本产品路线图旨在规划亘安网站内容管理系统 (GACMS) 从初期MVP版本到未来主要版本的演进路径。它将明确各阶段的核心目标、主要功能和预期交付时间，作为产品开发和迭代的战略指导。路线图将根据市场反馈、用户需求和技术发展进行动态调整。

**核心目标**: 快速推出具备核心内容管理能力的MVP版本，验证市场需求，并逐步迭代完善，最终打造一个功能强大、灵活易用、生态完善的内容管理平台。

---

## 2. 版本规划策略

- **MVP优先**: 快速迭代，小步快跑，优先实现核心价值，尽早获取用户反馈。
- **用户驱动**: 根据用户需求和痛点调整功能优先级和开发计划。
- **技术可行性**: 平衡功能创新与技术实现难度，确保版本按时交付。
- **模块化演进**: 各功能模块可独立开发和升级，降低版本迭代风险。
- **生态建设**:逐步开放API，鼓励第三方开发者参与，构建插件和主题生态。

---

## 3. 详细版本规划

### 3.1 GACMS 个人版 (MVP)

- **核心目标**: 提供最基础的个人网站或博客搭建和内容发布能力，开源核心。
- **主要功能 (P0)**:
    - **基础架构**: React + Gin (核心+按需模块)项目搭建，唯一入口，基本目录结构。
    - **核心内容管理**:
        - 栏目管理 (创建、编辑、删除、层级)。
        - 文章发布 (富文本编辑器，标题、内容、摘要、所属栏目、发布状态、封面图)。
        - 基础SEO (文章标题、关键词、描述自定义)。
    - **用户与权限**:
        - 用户注册与登录。
        - 单一管理员角色。
    - **系统设置**:
        - 站点基本信息配置 (名称、Logo、版权)。
    - **主题管理**:
        - 支持一套默认响应式前台主题。
        - 基础后台管理界面。
    - **文件管理**:
        - 图片上传与管理 (集成到文章发布)。
    - **安全性**: 基本的XSS、CSRF防护，SQL注入预防 (利用Gin框架特性及Go相关库实现)。
- **目标用户**: 个人站长、学生、开发者，用于快速搭建简单的个人网站、博客或小型项目演示站点。
- **预期交付**: 2025-08-14 (3个月后)

### 3.2 GACMS 专业版 (V1.1)

- **核心目标**: 在个人版基础上，增强内容管理能力，集成微信生态，提供更专业的主题与插件支持，以及基础的数据分析和SEO功能。
- **主要功能 (P0 & P1)**:
    - **包含个人版所有功能**。
    - **内容管理增强**:
        - 标签管理与文章关联。
        - 评论管理 (审核、回复)。
        - 内容静态化生成 (手动/自动触发，支持中英文)。
        - 专题管理 (聚合不同栏目内容)。
        - Markdown编辑器支持。
    - **用户与权限增强**:
        - 多角色权限系统 (管理员、编辑、贡献者等)。
    - **系统设置增强**:
        - 后台域名绑定。
        - 缓存机制可选 (文件缓存, Redis等)。
        - 邮件发送配置。
    - **主题管理增强**:
        - 提供更多官方高级主题选择。
        - 组件化模板系统 (完善)。
        - 主题设置选项 (更丰富的颜色、布局、字体调整)。
        - **站点级独立主题配置**: 支持为每个站点独立设置前后台主题。
    - **插件管理增强**:
        - 插件系统 (钩子和管理机制)。
        - 提供官方和第三方优质插件市场 (初步)。
    - **文件管理增强**:
        - 文件上传路径自定义。
        - (可选) 云存储对接 (如OSS, COS)。
    - **国际化与本地化**: 
        - 后台界面多语言支持 (中/英及更多)。
        - 前台内容多语言支持 (URL带语言标识，完善的切换机制)。
    - **API接口**: 
        - 提供核心内容读取API。
        - 微信公众号内容接口。
        - 微信小程序内容接口。
    - **数据分析模块**: 
        - 基础网站访问统计与报表 (PV, UV, 来源等)。
    - **SEO优化系统**: 
        - 自动生成Sitemap.xml。
        - 结构化数据支持 (文章、产品等)。
    - **多站点与域名绑定**: 
        - 支持创建和管理多个独立站点实例 (专业版核心功能)。
        - 每个站点可独立配置前台和后台域名。
        - 系统根据域名自动路由到对应站点。
        - 不同站点的内容、主题和用户设置可以独立管理。
    - **增强安全**: 
        - 登录尝试次数限制、验证码。
        - **2FA双因素认证**: 支持为前后台用户启用双因素认证，增强账户安全性。
- **目标用户**: 中小型企业、内容创作者、开发者，需要更专业的网站管理、微信生态整合、多站点管理和定制化能力。
- **预期交付**: 个人版MVP后3个月

### 3.3 GACMS 商业版 (V1.2)

- **核心目标**: 在专业版基础上，提供企业级特性，如高级工作流、合规性支持、高级安全套件、更全面的数据分析和定制开发服务。
- **主要功能 (P1 & P2)**:
    - **包含专业版所有功能**。
    - **高级用户与权限**: 
        - 更细粒度的权限控制 (如基于特定栏目或内容的权限)。
        - 支持对接企业SSO (如LDAP, OAuth2)。
    - **内容工作流**: 
        - 可自定义的多级内容审批流程 (草稿、待审核、已发布、驳回等)。
        - 定时发布功能。
        - 内容版本控制与回滚。
    - **合规性与审计**: 
        - 操作日志审计 (详细记录后台用户关键操作)。
        - 数据隐私功能 (如用户数据导出与删除，响应GDPR等)。
        - 辅助满足特定行业合规性要求 (如提供配置选项)。
    - **高级安全套件**: 
        - IP黑白名单管理。
        - 安全事件审计与告警。
        - WAF集成建议与支持。
        - 敏感数据加密存储。
    - **API接口增强**: 
        - 提供更全面的读写API。
        - API访问权限控制与流量限制。
    - **数据分析模块增强**: 
        - 高级用户行为路径分析。
        - 微信生态数据整合分析。
        - 自定义报表与数据导出。
    - **全文检索**: 
        - (可选) 集成更强大的搜索引擎 (如Elasticsearch, MeiliSearch)。
    - **多法人/多站点支持 (可选模块)**: 
        - 一套系统管理多个独立站点。
    - **定制开发与服务**: 
        - 提供针对商业版的定制开发和技术支持服务。
- **目标用户**: 中大型企业、对安全性、合规性、可扩展性和专业服务有较高要求的组织。
- **预期交付**: 专业版后4-6个月

### 3.4 GACMS 未来展望 (V2.0+)

- **核心目标**: 持续引入智能化、平台化特性，深化行业解决方案，打造领先的内容管理与数字体验平台。
- **主要方向 (P2及未来规划)**:
    - **智能化增强**:
        - AI辅助内容创作 (如内容生成、摘要提取、SEO优化建议)。
        - 个性化内容推荐引擎。
        - 智能内容审核。
    - **平台化与生态深化**:
        - 更开放和标准化的API体系。
        - 完善的开发者中心和社区支持。
        - 繁荣的插件和主题市场，引入付费和评价体系。
        - Serverless架构探索，提升弹性与效率。
    - **数据驱动决策强化**:
        - A/B测试框架。
        - 更高级的数据可视化与洞察分析。
        - 用户画像与精准营销工具集成。
    - **无头CMS (Headless CMS) 能力**: 
        - 强化API优先策略，支持将GACMS作为内容后端，赋能各种前端应用。
    - **行业解决方案**: 
        - 针对特定行业（如电商、教育、媒体）推出定制化的解决方案包。
    - **可视化与低代码**: 
        - 可视化页面设计器 (拖拽式)。
        - 低代码/无代码应用构建能力探索。
    - **持续的安全性与性能优化**。
- **目标用户**: 对内容管理有深度需求，追求创新、效率和业务增长的各类组织和开发者。
- **预期交付**: 商业版后持续迭代，每年1-2个大版本更新。

---

## 4. 功能优先级矩阵 (按版本划分)

| 优先级 | 功能描述                                       | 个人版 (MVP) | 专业版 (V1.1) | 商业版 (V1.2) | 备注                                         |
| ------ | ---------------------------------------------- | :----------: | :-----------: | :-----------: | -------------------------------------------- |
| **P0** | 基础架构搭建                                   |      ✅      |       ✅       |       ✅       | 系统运行的基石                               |
| P0     | 栏目管理                                       |      ✅      |       ✅       |       ✅       | 内容组织的基础                               |
| P0     | 文章发布 (富文本)                              |      ✅      |       ✅       |       ✅       | 核心内容生产功能                             |
| P0     | 用户注册与登录 (单管理员)                      |      ✅      |       ✅       |       ✅       | 个人版为单管理员，专业/商业版支持多角色      |
| P0     | 默认前后台主题                                 |      ✅      |       ✅       |       ✅       | 专业/商业版提供更优或更多选择                |
| P0     | 图片上传                                       |      ✅      |       ✅       |       ✅       | 内容编辑的必要支持                           |
| P0     | 基本安全防护 (XSS, CSRF, SQL注入预防)          |      ✅      |       ✅       |       ✅       | 利用Gin框架特性及Go相关库实现                                      |
| **P1** | 多角色权限系统                                 |              |       ✅       |       ✅       | 满足不同用户协作需求                         |
| P1     | 内容静态化生成                                 |              |       ✅       |       ✅       | 专业版手动/自动，商业版更优                  |
| P1     | 标签管理                                       |              |       ✅       |       ✅       |                                              |
| P1     | 评论管理                                       |              |       ✅       |       ✅       |                                              |
| P1     | 专题管理                                       |              |       ✅       |       ✅       | 灵活内容聚合                                 |
| P1     | Markdown编辑器支持                             |              |       ✅       |       ✅       |                                              |
| P1     | 后台域名绑定                                   |              |       ✅       |       ✅       | 增强安全性                                   |
| P1     | 2FA双因素认证                                  |              |       ✅       |       ✅       | 增强账户安全                                 |
| P1     | 站点级独立主题配置                             |              |       ✅       |       ✅       | 提升多站点灵活性                             |
| P1     | 缓存机制 (文件/Redis)                          |              |       ✅       |       ✅       |                                              |
| P1     | 邮件发送配置                                   |              |       ✅       |       ✅       |                                              |
| P1     | 组件化模板系统 (完善)                          |              |       ✅       |       ✅       | 提高主题开发效率和复用性                     |
| P1     | 插件系统 (基础)                                |              |       ✅       |       ✅       | 系统扩展性的关键                             |
| P1     | 国际化与本地化 (中/英+)                        |              |       ✅       |       ✅       | 专业版基础，商业版完善                       |
| P1     | 多站点与域名绑定                               |              |       ✅       |       ✅       | 专业版核心，商业版继承与增强                 |
| P1     | API接口 (核心内容读取, 微信)                   |              |       ✅       |       ✅       | 专业版基础，商业版增强                       |
| P1     | 基础数据分析 (PV, UV)                          |              |       ✅       |       ✅       |                                              |
| P1     | SEO优化 (Sitemap, 结构化数据)                  |              |       ✅       |       ✅       |                                              |
| P1     | 增强安全 (登录限制, 验证码)                    |              |       ✅       |       ✅       |                                              |
| **P1** | 内容工作流 (多级审批)                          |              |               |       ✅       | 规范内容生产流程                             |
| P1     | 定时发布                                       |              |               |       ✅       |                                              |
| P1     | 内容版本控制与回滚                             |              |               |       ✅       |                                              |
| P1     | 合规性与审计 (操作日志, 数据隐私基础)          |              |               |       ✅       |                                              |
| P1     | 高级安全 (IP黑白名单, WAF集成建议)             |              |               |       ✅       |                                              |
| P1     | API接口增强 (读写, 权限控制)                   |              |               |       ✅       |                                              |
| P1     | 高级数据分析 (用户行为, 微信整合)              |              |               |       ✅       |                                              |
| **P2** | 全文检索 (轻量级/集成高级引擎)                 |              |     (可选)     |       ✅       | 专业版可选基础，商业版可选高级               |
| P2     | 云存储对接                                     |              |     (可选)     |     (可选)     |                                              |
| P2     | 对接企业SSO                                    |              |               |     (可选)     |                                              |
| P2     | 多法人/多站点                                  |              |               |     (可选)     |                                              |

*(这是一个简化的优先级矩阵，实际项目中会更详细)*

---

## 5. 详细时间线计划 (里程碑)

| 里程碑                               | 计划完成日期        | 主要交付物                                                                                    | 负责人      |
| ------------------------------------ | ------------------- | ------------------------------------------------------------------------------------------- | ----------- |
| **项目启动与需求分析**               | 2025-05-14      | PRD文档 (含版本划分), Roadmap (含版本划分), 技术选型确认                                  | 产品经理    |
| **GACMS 个人版 (MVP) - 开发阶段1**   | +4周                | React + Gin (核心+按需模块)项目初始化, 核心目录结构, 数据库设计初稿 (个人版范围), 用户模型 (单管理员)                 | 开发团队    |
| **GACMS 个人版 (MVP) - 开发阶段2**   | +4周                | 个人版核心功能开发 (栏目, 文章, 基础SEO, 主题, 文件上传)                                      | 开发团队    |
| **GACMS 个人版 (MVP) - 测试与发布**  | +4周                | 个人版功能测试完成, Bug修复, 部署文档, 个人版 (MVP) 上线 (开源核心)                           | 测试/运维   |
| **GACMS 专业版 (V1.1) - 规划与设计** | 个人版MVP后 +2周    | 专业版详细需求确认, 架构升级方案 (如多角色, 插件机制)                                         | 产品/开发   |
| **GACMS 专业版 (V1.1) - 开发与集成** | 个人版MVP后 +8周    | 专业版功能开发 (多角色权限, 静态化, 微信接口, 插件基础, 基础数据分析, SEO增强等) 并集成        | 开发团队    |
| **GACMS 专业版 (V1.1) - 测试与发布** | 个人版MVP后 +3周    | 专业版功能测试完成, Bug修复, 专业版 (V1.1) 发布                                               | 测试/运维   |
| **GACMS 商业版 (V1.2) - 规划与设计** | 专业版V1.1后 +3周   | 商业版详细需求确认 (工作流, 高级安全, 合规性, 高级API, 企业级特性)                              | 产品/开发   |
| **GACMS 商业版 (V1.2) - 开发与集成** | 专业版V1.1后 +12周  | 商业版功能开发 (内容工作流, 合规审计, 高级安全套件, API增强, 高级数据分析等) 并集成             | 开发团队    |
| **GACMS 商业版 (V1.2) - 测试与发布** | 专业版V1.1后 +4周   | 商业版功能测试完成, Bug修复, 商业版 (V1.2) 发布                                               | 测试/运维   |
| **GACMS 未来展望 (V2.0+) - 规划**  | 商业版V1.2后 +4周   | V2.0+ 方向确认 (智能化, 平台化, Headless, 行业方案), 关键技术预研                             | 产品/架构师 |

*(时间为相对时间，具体日期需根据项目实际启动日确定)*

---

## 6. 资源规划 (初步建议)

- **产品团队**: 1名产品经理 (兼项目经理)。
- **设计团队**: 1名UI/UX设计师 (可兼职或按需投入)。
- **开发团队**:
    - 后端工程师: 1-2名 (熟悉Go/Gin者佳)。
    - 前端工程师: 1名 (熟悉HTML/CSS/JS，有响应式和组件化经验者佳)。
- **测试团队**: 1名测试工程师 (可由开发兼任初期测试，后期专职)。
- **运维支持**: (初期可由开发兼任，后期根据部署规模配置)。

---

## 7. 风险管理

| 风险描述                                 | 可能性 | 影响程度 | 应对措施                                                                                                | 负责人   |
| ---------------------------------------- | ------ | -------- | ------------------------------------------------------------------------------------------------------- | -------- |
| **核心开发人员流失**                       | 中     | 高       | 知识共享与文档建设, 培养备份人员, 保持良好团队氛围                                                            | 项目经理 |
| **需求频繁变更导致范围蔓延**               | 高     | 中       | 建立清晰的需求变更流程, 加强与用户的沟通确认, MVP阶段聚焦核心                                                 | 产品经理 |
| **技术难题攻关受阻**                       | 中     | 中       | 提前进行技术预研, 鼓励团队学习和知识分享, 必要时寻求外部专家支持                                              | 开发负责人 |
| **第三方服务依赖问题 (如API变更)**         | 低     | 中       | 选择稳定可靠的第三方服务, 做好接口封装和兼容性处理, 关注服务商通知                                              | 开发负责人 |
| **Gin框架本身出现重大Bug或限制**           | 低     | 高       | 关注Gin官方社区和更新, 积极参与社区讨论, 评估框架升级影响, 必要时寻找替代方案或自行修复 (贡献社区)             | 开发负责人 |
| **市场竞争激烈，产品缺乏吸引力**           | 中     | 高       | 持续进行市场和用户研究, 突出产品差异化优势 (功能丰富、安全、React + Gin 生态), 快速迭代响应用户反馈                        | 产品经理 |
| **安全性漏洞被利用**                       | 中     | 高       | 严格遵循安全开发规范, 定期进行安全审计和渗透测试, 及时更新依赖库, 快速响应安全事件                            | 安全负责人 |
| **项目延期交付**                           | 中     | 中       | 合理规划迭代周期, 每日站会跟踪进度, 及时识别和解决瓶颈问题, 预留缓冲时间                                      | 项目经理 |