/*
Author: <PERSON><PERSON>eh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package service

import (
	"strings"
	"sync"

	"gacms/pkg/contract"
	"go.uber.org/zap"
)

// ModuleEventMapper 负责管理事件名称到模块名称的映射
type ModuleEventMapper interface {
	// RegisterEventMapping 注册事件到模块的映射
	RegisterEventMapping(moduleName string, eventNames []string)
	
	// GetModuleForEvent 获取处理指定事件的模块名称
	GetModuleForEvent(eventName contract.EventName) (string, bool)
	
	// GetEventsForModule 获取指定模块处理的所有事件
	GetEventsForModule(moduleName string) []contract.EventName
	
	// RemoveModuleMapping 移除指定模块的所有事件映射
	RemoveModuleMapping(moduleName string)
}

// DefaultModuleEventMapper 是 ModuleEventMapper 的默认实现
type DefaultModuleEventMapper struct {
	// 事件名称到模块名称的映射
	eventToModule map[contract.EventName]string
	
	// 模块名称到事件列表的映射
	moduleToEvents map[string][]contract.EventName
	
	// 读写锁保护并发访问
	mu sync.RWMutex
	
	logger *zap.Logger
}

// NewDefaultModuleEventMapper 创建一个新的 DefaultModuleEventMapper 实例
func NewDefaultModuleEventMapper(logger *zap.Logger) ModuleEventMapper {
	mapper := &DefaultModuleEventMapper{
		eventToModule:  make(map[contract.EventName]string),
		moduleToEvents: make(map[string][]contract.EventName),
		logger:         logger,
	}
	
	// 初始化核心模块的事件映射
	mapper.initializeCoreModuleMappings()
	
	return mapper
}

// RegisterEventMapping 注册事件到模块的映射
func (m *DefaultModuleEventMapper) RegisterEventMapping(moduleName string, eventNames []string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	// 先移除该模块的旧映射
	m.removeModuleMappingUnsafe(moduleName)
	
	// 添加新映射
	events := make([]contract.EventName, 0, len(eventNames))
	for _, eventName := range eventNames {
		eventNameTyped := contract.EventName(eventName)
		m.eventToModule[eventNameTyped] = moduleName
		events = append(events, eventNameTyped)
	}
	
	m.moduleToEvents[moduleName] = events
	
	m.logger.Debug("Registered event mapping for module",
		zap.String("module", moduleName),
		zap.Strings("events", eventNames),
	)
}

// GetModuleForEvent 获取处理指定事件的模块名称
func (m *DefaultModuleEventMapper) GetModuleForEvent(eventName contract.EventName) (string, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	// 首先查找精确匹配
	if moduleName, exists := m.eventToModule[eventName]; exists {
		return moduleName, true
	}
	
	// 如果没有精确匹配，尝试从事件名称推断模块名称
	inferredModule := m.inferModuleFromEventName(eventName)
	if inferredModule != "" {
		// 动态注册推断的映射
		go func() {
			m.mu.Lock()
			defer m.mu.Unlock()
			m.eventToModule[eventName] = inferredModule
			
			// 更新模块到事件的映射
			if events, exists := m.moduleToEvents[inferredModule]; exists {
				m.moduleToEvents[inferredModule] = append(events, eventName)
			} else {
				m.moduleToEvents[inferredModule] = []contract.EventName{eventName}
			}
		}()
		
		return inferredModule, true
	}
	
	return "", false
}

// GetEventsForModule 获取指定模块处理的所有事件
func (m *DefaultModuleEventMapper) GetEventsForModule(moduleName string) []contract.EventName {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	if events, exists := m.moduleToEvents[moduleName]; exists {
		// 返回副本以避免并发修改
		result := make([]contract.EventName, len(events))
		copy(result, events)
		return result
	}
	
	return nil
}

// RemoveModuleMapping 移除指定模块的所有事件映射
func (m *DefaultModuleEventMapper) RemoveModuleMapping(moduleName string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.removeModuleMappingUnsafe(moduleName)
}

// removeModuleMappingUnsafe 移除指定模块的所有事件映射（不加锁版本）
func (m *DefaultModuleEventMapper) removeModuleMappingUnsafe(moduleName string) {
	// 移除事件到模块的映射
	if events, exists := m.moduleToEvents[moduleName]; exists {
		for _, eventName := range events {
			delete(m.eventToModule, eventName)
		}
	}
	
	// 移除模块到事件的映射
	delete(m.moduleToEvents, moduleName)
	
	m.logger.Debug("Removed event mapping for module",
		zap.String("module", moduleName),
	)
}

// initializeCoreModuleMappings 初始化核心模块的事件映射
func (m *DefaultModuleEventMapper) initializeCoreModuleMappings() {
	// 核心模块的事件映射
	coreModuleMappings := map[string][]string{
		"user": {
			"user.created", "user.updated", "user.deleted",
			"user.login", "user.logout", "user.password.changed",
			"content_type.created", "content_type.updated", "content_type.deleted",
		},
		"site": {
			"site.created", "site.updated", "site.deleted",
			"domain.binding.created", "domain.binding.updated", "domain.binding.deleted",
			"url_rule.created", "url_rule.updated", "url_rule.deleted",
		},
		"actionlog": {
			"audit.operation.create", "audit.operation.query",
			"actionlog.created", "actionlog.queried",
		},
		"admin": {
			"admin.menu.accessed", "admin.dashboard.viewed",
		},
		"content": {
			"content.created", "content.updated", "content.deleted",
			"content.published", "content.unpublished",
		},
	}
	
	// 注册核心模块映射
	for moduleName, events := range coreModuleMappings {
		m.RegisterEventMapping(moduleName, events)
	}
	
	m.logger.Info("Initialized core module event mappings",
		zap.Int("modules", len(coreModuleMappings)),
		zap.Int("total_events", len(m.eventToModule)),
	)
}

// inferModuleFromEventName 从事件名称推断模块名称
func (m *DefaultModuleEventMapper) inferModuleFromEventName(eventName contract.EventName) string {
	eventStr := string(eventName)
	
	// 尝试从事件名称的前缀推断模块名称
	// 格式：{module}.{action}.{object} 或 {module}.{object}.{action}
	parts := strings.Split(eventStr, ".")
	if len(parts) >= 2 {
		// 第一部分通常是模块名称
		possibleModule := parts[0]
		
		// 验证是否是已知的模块名称
		knownModules := []string{
			"user", "site", "actionlog", "admin", "content", 
			"menu", "theme", "seo", "analytics", "workflow",
		}
		
		for _, module := range knownModules {
			if possibleModule == module {
				m.logger.Debug("Inferred module from event name",
					zap.String("event", eventStr),
					zap.String("module", module),
				)
				return module
			}
		}
	}
	
	// 特殊情况处理
	if strings.Contains(eventStr, "content_type") {
		return "user" // content_type 事件由 user 模块处理
	}
	
	if strings.Contains(eventStr, "audit") {
		return "actionlog" // audit 事件由 actionlog 模块处理
	}
	
	return ""
}

// RegisterModuleEvents 注册模块的事件映射（从数据库配置）
func (m *DefaultModuleEventMapper) RegisterModuleEvents(moduleName string, publishes []string, listens []EventListenerConfig) {
	// 注册发布的事件
	m.RegisterEventMapping(moduleName, publishes)

	// 注册监听的事件（这些事件可能由其他模块发布）
	for _, listener := range listens {
		// 这里我们记录这个模块监听某个事件，但不建立反向映射
		// 因为事件的发布者可能是其他模块
		m.logger.Debug("Module listens to event",
			zap.String("module", moduleName),
			zap.String("event", listener.Event),
			zap.String("handler", listener.Handler),
			zap.Int("priority", listener.Priority),
		)
	}
}

// EventListenerConfig 事件监听器配置（与model包中的定义保持一致）
type EventListenerConfig struct {
	Event    string `json:"event"`
	Handler  string `json:"handler"`
	Priority int    `json:"priority"`
}
