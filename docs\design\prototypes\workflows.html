<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 工作流管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        /* 状态标签样式 */
        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-active {
            background-color: rgba(40, 167, 69, 0.2);
            color: #28a745;
        }
        .status-inactive {
            background-color: rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }

        /* 工作流节点样式 */
        .workflow-node {
            background-color: #333;
            border: 2px solid #444;
            border-radius: 8px;
            transition: all 0.3s ease;
            cursor: move;
        }
        .workflow-node:hover {
            border-color: #007bff;
            transform: scale(1.02);
        }
        .workflow-node.start {
            border-color: #28a745;
        }
        .workflow-node.end {
            border-color: #dc3545;
        }

        /* 连接线样式 */
        .workflow-connector {
            border-top: 2px dashed #444;
            position: relative;
        }
        .workflow-connector::after {
            content: '';
            position: absolute;
            right: -6px;
            top: -5px;
            width: 8px;
            height: 8px;
            border-top: 2px solid #444;
            border-right: 2px solid #444;
            transform: rotate(45deg);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">工作流管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-plus text-white"></i>
                                </span>
                                创建工作流
                            </span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 工作流统计 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- 活跃工作流 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-gray-400">活跃工作流</span>
                        <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center">
                            <i class="fas fa-play-circle text-green-500 text-xl"></i>
                        </div>
                    </div>
                    <div class="text-3xl font-bold mb-2 text-white">8</div>
                    <div class="text-sm text-gray-400">运行中的工作流</div>
                </div>

                <!-- 待处理任务 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-gray-400">待处理任务</span>
                        <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center">
                            <i class="fas fa-tasks text-blue-500 text-xl"></i>
                        </div>
                    </div>
                    <div class="text-3xl font-bold mb-2 text-white">24</div>
                    <div class="text-sm text-gray-400">需要审批的任务</div>
                </div>

                <!-- 完成率 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-gray-400">完成率</span>
                        <div class="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center">
                            <i class="fas fa-check-circle text-purple-500 text-xl"></i>
                        </div>
                    </div>
                    <div class="text-3xl font-bold mb-2 text-white">92%</div>
                    <div class="text-sm text-green-500">↑ 较上月提升 5%</div>
                </div>

                <!-- 平均处理时间 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-gray-400">平均处理时间</span>
                        <div class="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center">
                            <i class="fas fa-clock text-yellow-500 text-xl"></i>
                        </div>
                    </div>
                    <div class="text-3xl font-bold mb-2 text-white">2.5h</div>
                    <div class="text-sm text-green-500">↓ 较上月减少 30分钟</div>
                </div>
            </div>

            <!-- 工作流列表 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold text-white">工作流列表</h3>
                    <div class="flex gap-4">
                        <select class="bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option>全部类型</option>
                            <option>内容审核</option>
                            <option>用户审核</option>
                            <option>发布流程</option>
                        </select>
                        <select class="bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option>全部状态</option>
                            <option>运行中</option>
                            <option>已停止</option>
                        </select>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="text-left border-b border-gray-700">
                                <th class="pb-4 px-4 text-gray-300 font-medium">工作流名称</th>
                                <th class="pb-4 px-4 text-gray-300 font-medium">类型</th>
                                <th class="pb-4 px-4 text-gray-300 font-medium">创建者</th>
                                <th class="pb-4 px-4 text-gray-300 font-medium">状态</th>
                                <th class="pb-4 px-4 text-gray-300 font-medium">处理量</th>
                                <th class="pb-4 px-4 text-gray-300 font-medium">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 工作流1 -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-4">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 rounded bg-blue-500/20 flex items-center justify-center text-blue-500">
                                            <i class="fas fa-file-alt"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-white">内容审核流程</div>
                                            <div class="text-xs text-gray-400">文章、评论自动审核</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-4 px-4">
                                    <span class="text-gray-300">内容审核</span>
                                </td>
                                <td class="py-4 px-4">
                                    <div class="flex items-center gap-2">
                                        <div class="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center text-white">
                                            <i class="fas fa-user-check"></i>
                                        </div>
                                        <span class="text-gray-300">admin</span>
                                    </div>
                                </td>
                                <td class="py-4 px-4">
                                    <span class="status-badge status-active">运行中</span>
                                </td>
                                <td class="py-4 px-4">
                                    <div class="flex items-center">
                                        <div class="w-24 bg-gray-700 rounded-full h-2 mr-2">
                                            <div class="bg-blue-500 h-2 rounded-full" style="width: 75%"></div>
                                        </div>
                                        <span class="text-xs text-gray-400">75%</span>
                                    </div>
                                </td>
                                <td class="py-4 px-4">
                                    <div class="flex gap-2">
                                        <button class="p-2 text-blue-400 hover:text-blue-300" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="p-2 text-yellow-400 hover:text-yellow-300" title="暂停">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                        <button class="p-2 text-red-400 hover:text-red-300" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>

                            <!-- 工作流2 -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-4">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 rounded bg-green-500/20 flex items-center justify-center text-green-500">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-white">用户注册审核</div>
                                            <div class="text-xs text-gray-400">新用户注册验证流程</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-4 px-4">
                                    <span class="text-gray-300">用户审核</span>
                                </td>
                                <td class="py-4 px-4">
                                    <div class="flex items-center gap-2">
                                        <div class="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center text-white">
                                            <i class="fas fa-user-check"></i>
                                        </div>
                                        <span class="text-gray-300">admin</span>
                                    </div>
                                </td>
                                <td class="py-4 px-4">
                                    <span class="status-badge status-active">运行中</span>
                                </td>
                                <td class="py-4 px-4">
                                    <div class="flex items-center">
                                        <div class="w-24 bg-gray-700 rounded-full h-2 mr-2">
                                            <div class="bg-green-500 h-2 rounded-full" style="width: 90%"></div>
                                        </div>
                                        <span class="text-xs text-gray-400">90%</span>
                                    </div>
                                </td>
                                <td class="py-4 px-4">
                                    <div class="flex gap-2">
                                        <button class="p-2 text-blue-400 hover:text-blue-300" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="p-2 text-yellow-400 hover:text-yellow-300" title="暂停">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                        <button class="p-2 text-red-400 hover:text-red-300" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>

                            <!-- 工作流3 -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-4">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 rounded bg-purple-500/20 flex items-center justify-center text-purple-500">
                                            <i class="fas fa-rocket"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-white">内容发布流程</div>
                                            <div class="text-xs text-gray-400">文章定时发布管理</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-4 px-4">
                                    <span class="text-gray-300">发布流程</span>
                                </td>
                                <td class="py-4 px-4">
                                    <div class="flex items-center gap-2">
                                        <div class="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center text-white">
                                            <i class="fas fa-user-check"></i>
                                        </div>
                                        <span class="text-gray-300">editor</span>
                                    </div>
                                </td>
                                <td class="py-4 px-4">
                                    <span class="status-badge status-inactive">已停止</span>
                                </td>
                                <td class="py-4 px-4">
                                    <div class="flex items-center">
                                        <div class="w-24 bg-gray-700 rounded-full h-2 mr-2">
                                            <div class="bg-gray-500 h-2 rounded-full" style="width: 45%"></div>
                                        </div>
                                        <span class="text-xs text-gray-400">45%</span>
                                    </div>
                                </td>
                                <td class="py-4 px-4">
                                    <div class="flex gap-2">
                                        <button class="p-2 text-blue-400 hover:text-blue-300" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="p-2 text-green-400 hover:text-green-300" title="启动">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="p-2 text-red-400 hover:text-red-300" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="mt-6 flex justify-between items-center">
                    <div class="text-gray-400 text-sm">
                        显示 1-3 条，共 12 条
                    </div>
                    <div class="flex gap-2">
                        <button class="px-3 py-1 rounded border border-gray-600 text-gray-400 hover:bg-gray-700">上一页</button>
                        <button class="px-3 py-1 rounded border border-blue-500 bg-blue-500/20 text-blue-400">1</button>
                        <button class="px-3 py-1 rounded border border-gray-600 text-gray-400 hover:bg-gray-700">2</button>
                        <button class="px-3 py-1 rounded border border-gray-600 text-gray-400 hover:bg-gray-700">3</button>
                        <button class="px-3 py-1 rounded border border-gray-600 text-gray-400 hover:bg-gray-700">下一页</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
        });
    </script>
</body>
</html> 
</html> 