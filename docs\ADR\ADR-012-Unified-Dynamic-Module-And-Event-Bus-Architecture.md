# ADR-012: Unified Dynamic Module and Event Bus Architecture

## Status
Accepted

## Context
Through a series of intensive architectural reviews and iterative discussions, we identified fundamental limitations in our initial design. Key issues included:
1.  **Static Module Registration**: The application's entrypoint (`main.go`) required manual, hard-coded imports for every business module, violating the Open/Closed Principle and hindering true modularity.
2.  **Ambiguous Inter-Module Communication**: The mechanism for modules to interact was unclear, oscillating between direct API calls and a partially implemented event system, leading to inconsistent patterns.
3.  **Overly Complex Initial CQRS Proposal**: A proposal to implement a full Command-Query Responsibility Segregation (CQRS) pattern with separate `CommandBus` and `EventBus` was deemed potentially overly complex for the current project stage.
4.  **Unsafe Dependency Scope**: Placing the `EventManager` within the global `AppContext` was identified as a dangerous practice, granting excessive power to all services and obfuscating the application's data flow.

A final, unified, and pragmatic architecture was required to address all these issues cohesively.

## Decision
We have decided to implement a new, unified architecture based on **three** foundational pillars: **Dynamic Module Loading**, a **Simplified, Unified Event Bus**, and the **Abolition of a manual `AppContext` struct**. This architecture will govern the entire application's structure and data flow.

### 1. Dynamic Module Loading
The application will adopt a true plug-and-play module system.
*   **Module Self-Registration**: Each module (e.g., `user`, `actionlog`) must contain an `init()` function within its `module.go` file. In this function, it will register its `fx.Option` (the `Module` variable) to a global, singleton module registry.
*   **Core Module Loader**: A central `Module Loader` service, located in `internal/core/loader`, will be responsible for retrieving the list of all registered `fx.Option`s from the global registry.
*   **Decoupled Entrypoint**: The `main.go` file will no longer import any specific business modules. It will only call the `Module Loader` to get all modules and pass them to the `fx.New()` constructor. This makes the core application agnostic to the number and type of modules installed.

### 2. Simplified, Unified Event Bus
To achieve robust decoupling without excessive complexity, we will implement a single, unified `EventBus`.
*   **Single `EventBus`**: We will only create and manage one central message bus, the `EventBus`, instead of separate buses for commands and events.
*   **Intent-Based Naming Convention**: We will distinguish the purpose of an event by its name and verb tense, establishing a clear pattern:
    *   **Action Events (Commands)**: Represent an intent or a command to perform an action. They are named in the present tense (e.g., `CreateUserEvent`, `ProcessPaymentEvent`). By convention, an Action Event should only have **one** registered handler.
    *   **Fact Events (Events)**: Represent a fact that has already occurred. They are named in the past tense (e.g., `UserCreatedEvent`, `PaymentProcessedEvent`). A Fact Event can have **zero or more** listeners.
*   **Workflow**: The typical flow is `Controller -> ActionEvent -> Handler -> FactEvent -> Listeners`. This provides clear, debuggable, and decoupled business process orchestration.

### 3. Fine-Grained Dependency Injection
The `EventBus` will be an independent, injectable service managed by the `fx` container.
*   It will **NOT** be part of a manual `AppContext` struct.
*   It will be injected **only** into components that are explicitly allowed to dispatch events (primarily `Controllers` for Action Events and `Handlers` for Fact Events), thus enforcing architectural integrity at the code level.

### 4. Abolition of Manual `AppContext` Struct
To eliminate conceptual ambiguity and fully embrace dependency injection, we will abolish the manually-defined `AppContext` struct.
*   **`fx` as the Sole Container**: The `fx` framework is the one and only dependency injection container for the application. It is responsible for managing the lifecycle and injection of all application-level services (e.g., Logger, Database, etc.).
*   **Clear Separation of Concerns**: We will strictly distinguish between two types of "context":
    *   **Application-Level Dependencies**: Shared services like `*zap.Logger` or `*gorm.DB`. These are provided to the `fx` container at startup and **injected via constructors** into the services that need them.
    *   **Request-Level Context**: Per-request metadata like `siteID`, `userID`, or `traceID`. This is handled exclusively by the standard `context.Context` object, which is passed as the **first argument** to business logic methods.
*   **No "God" Context Object**: This change prevents the creation of a "God" object that holds all services, promoting cleaner, more explicit dependencies for each component.

## Consequences
### Positive
*   **True Modularity (Open/Closed Principle)**: New modules can be added or removed without ever touching the core application's entrypoint code.
*   **Simplified Complexity**: The unified `EventBus` इज easier to understand and manage than a full CQRS implementation, while still providing the core benefits of decoupling.
*   **Clear & Enforced Data Flow**: The "who can dispatch what" rule is enforced by DI, making the system's behavior predictable and robust.
*   **High Testability**: Decoupling via the event bus makes every component (`Controller`, `Handler`, `Listener`) independently testable.
*   **Resolves All Previous Contradictions**: This unified model solves the module communication debate, the DI container debate, and the `AppContext` debate cohesively.
*   **Conceptual Clarity**: By completely separating application-level dependency injection from request-level context passing, the cognitive load for developers is significantly reduced, and the architecture becomes much cleaner.

### Negative
*   **Reliance on `init()`**: The dynamic loading pattern relies on Go's `init()` function, which some developers view with caution. However, for this specific use case (module self-registration), it is a clean and widely accepted pattern.
*   **Convention over Strict Typing for Events**: The distinction between "Action" and "Fact" events is based on convention (naming and number of listeners). This requires developer discipline.
*   **`EventManager` in `AppContext`**: Rejected as it violates the Principle of Least Privilege and obfuscates the system's data flow. This concept is fully superseded by the abolition of the `AppContext` struct itself.

## Alternatives Considered
*   **Full CQRS with Separate Buses**: Rejected as potentially over-engineered for the current project phase. The simplified model provides a better balance of power and simplicity.
*   **API-based Inter-module Communication**: Rejected because it creates a spiderweb of direct dependencies, making the system rigid and hard to refactor. The event bus model provides superior decoupling.
*   **`EventManager` in `AppContext`**: Rejected as it violates the Principle of Least Privilege and obfuscates the system's data flow. 