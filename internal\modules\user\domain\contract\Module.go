/*
 * @Author: C<PERSON> Nieh <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: Clion Nieh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/user/domain/contract/Module.go
 * @Description: 定义模块接口和类型。
 * 
 * © 2025 GACMS. All rights reserved.
 */
package contract

// PermissionInfo 定义权限的详细信息
type PermissionInfo struct {
	Module      string // 所属模块
	Name        string // 权限名称
	Slug        string // 权限标识符
	Description string // 权限描述
}

// ModuleType 定义模块类型
type ModuleType string

const (
	// CoreModule 核心模块，不可停用
	CoreModule ModuleType = "core"
	
	// ExtensionModule 扩展模块，可以启用或停用
	ExtensionModule ModuleType = "extension"
)

// IModule 定义所有模块必须实现的接口
type IModule interface {
	// GetName 返回模块的唯一标识符
	GetName() string
	
	// GetVersion 返回模块的版本号
	GetVersion() string
	
	// GetType 返回模块类型
	GetType() ModuleType
	
	// GetModels 返回模块需要注册的数据模型
	GetModels() []interface{}
	
	// ExposePermissions 返回模块提供的所有权限
	ExposePermissions() []PermissionInfo
	
	// Initialize 初始化模块，如数据迁移、默认数据等
	Initialize() error
} 