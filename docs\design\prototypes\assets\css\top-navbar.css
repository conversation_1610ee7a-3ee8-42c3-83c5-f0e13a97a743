/* 
© 2025 Clion Nieh. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
*/

/* 顶部导航栏样式 */
.top-navbar {
    padding: 0 2rem;
    background: #161b22;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 搜索框样式 */
.search-container {
    position: relative;
    max-width: 300px;
}

.search-input {
    background: rgba(12, 12, 12, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5rem;
    color: #ffffff;
    transition: all 0.3s ease;
}

.search-input:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.2);
}

.search-btn {
    transition: all 0.3s ease;
}

.search-btn:hover {
    transform: translateY(-1px);
}

/* 导航按钮样式 */
.nav-btn {
    color: #ffffff;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-btn:hover {
    transform: translateY(-1px);
}

.nav-btn:active {
    transform: translateY(0);
}

/* 通知徽章 */
.notification-badge {
    font-size: 0.5rem;
    right: 5px;
    top: 5px;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    animation: pulse 2s infinite;
}

/* 用户头像下拉菜单 */
.submenu {
    background: #161b22;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dropdown-item {
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    transform: translateX(5px);
}

/* 动画效果 */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-enter {
    animation: slideDown 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-container {
        max-width: 200px;
    }
    
    .nav-btn {
        padding: 0.5rem;
    }
    
    .navbar-logo span {
        display: none;
    }
}

@media (max-width: 640px) {
    .search-container {
        display: none;
    }
}