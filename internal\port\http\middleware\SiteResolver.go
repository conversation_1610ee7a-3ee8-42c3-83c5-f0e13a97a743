/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/port/http/middleware/SiteResolver.go
 * @Description: Middleware to resolve the site ID from the request host and inject it into the context.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package middleware

import (
	"gacms/internal/infrastructure/database"
	siteContract "gacms/internal/modules/site/domain/contract"
	systemContract "gacms/internal/modules/system/domain/contract"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// NewSiteResolver creates a new middleware handler that resolves the site for the current request.
// It uses dependency injection to get the necessary repositories.
func NewSiteResolver(siteRepo siteContract.SiteRepository, bindingRepo systemContract.DomainBindingRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		host := c.Request.Host
		if strings.Contains(host, ":") {
			host = strings.Split(host, ":")[0]
		}

		var siteID uint

		// 1. Try to find a direct domain binding.
		binding, err := bindingRepo.GetByDomain(host)
		if err == nil && binding != nil {
			siteID = binding.SiteID
			// 将域名绑定信息存储到上下文中，供URL重写中间件使用
			c.Set("domain_binding", binding)
		} else {
			// 2. If no binding, try to find a site by its core domains.
			site, err := siteRepo.GetByDomain(host)
			if err == nil && site != nil {
				siteID = site.ID
			} else {
				// 3. If not found in primary domain, check backend domain.
				site, err = siteRepo.GetByBackendDomain(host)
				if err == nil && site != nil {
					siteID = site.ID
				}
			}
		}

		// If a site was found, inject its ID into the context.
		if siteID > 0 {
			// Use the standard context helpers we created.
			// This makes the siteID available to downstream handlers and services.
			ctx := database.WithSiteID(c.Request.Context(), siteID)
			c.Request = c.Request.WithContext(ctx)

			// Also, set it in the Gin context for easier access in handlers.
			c.Set("siteID", siteID)
		}

		c.Next()
	}
}