//go:build professional

/*
Author: <PERSON><PERSON> Nieh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package service

import "gacms/pkg/contract"

// ProfessionalEditionCompiler 专业版编译器
type ProfessionalEditionCompiler struct {
	BaseEditionCompiler
}

// 编译时返回专业版管理器
func getCompiledEditionManager() CompileTimeEditionManager {
	return &ProfessionalEditionCompiler{
		BaseEditionCompiler: BaseEditionCompiler{
			edition: contract.EditionProfessional,
			features: map[string]bool{
				// 专业版功能（编译时包含）
				"basic_content":    true,
				"basic_theme":      true,
				"basic_user":       true,
				"advanced_theme":   true,
				"seo_basic":        true,
				"seo_advanced":     true,
				"workflow":         true,
				"api_access":       true,
				"advanced_user":    true,
				
				// 商业级功能（编译时排除）
				"business_security": false,
				"custom_development":  false,
			},
			limits: &EditionLimits{
				MaxSites:     10,
				MaxUsers:     100,
				MaxStorage:   20,  // 20GB
				MaxBandwidth: 200, // 200GB
				MaxPages:     1000,
				MaxPosts:     2000,
			},
		},
	}
}

// ProfessionalFeatureGuard 专业版功能守卫
func ProfessionalFeatureGuard(featureName string) bool {
	compiler := GetCompiledEditionManager()
	return compiler.IsFeatureCompiledIn(featureName)
}

// ProfessionalLimitGuard 专业版限制守卫
func ProfessionalLimitGuard(limitType string, currentValue int) bool {
	compiler := GetCompiledEditionManager()
	limits := compiler.GetCompiledLimits()
	
	switch limitType {
	case "sites":
		return limits.MaxSites > 0 && currentValue >= limits.MaxSites
	case "users":
		return limits.MaxUsers > 0 && currentValue >= limits.MaxUsers
	case "storage":
		return limits.MaxStorage > 0 && currentValue >= limits.MaxStorage
	case "bandwidth":
		return limits.MaxBandwidth > 0 && currentValue >= limits.MaxBandwidth
	case "pages":
		return limits.MaxPages > 0 && currentValue >= limits.MaxPages
	case "posts":
		return limits.MaxPosts > 0 && currentValue >= limits.MaxPosts
	default:
		return false
	}
}
