/*
 * @Author: <PERSON><PERSON>eh <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: Clion Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: pkg/contract/EventHandler.go
 * @Description: 定义事件处理器接口
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import "context"

// EventHandler 定义了能够处理特定事件的组件的接口
// 事件处理器分为两类：
// 1. 命令处理器(Command Handlers)：执行业务逻辑
// 2. 事件观察者(Event Observers)：对事件做出反应
type EventHandler interface {
	// Handle 处理给定的事件
	// 返回错误表示处理失败
	Handle(event Event) error

	// SupportedEvents 返回此处理器支持的事件类型列表
	// 用于自动注册和事件路由
	SupportedEvents() []EventName

	// HandlerName 返回处理器的唯一名称
	// 用于日志记录和调试
	HandlerName() string
}

// AsyncEventHandler 定义了异步事件处理器接口
// 异步处理器可以在后台处理事件，不阻塞事件发布流程
type AsyncEventHandler interface {
	EventHandler

	// HandleAsync 异步处理给定的事件
	// 返回一个通道，当处理完成时关闭
	HandleAsync(ctx context.Context, event Event) <-chan error
}

// RetryableEventHandler 定义了支持重试的事件处理器接口
type RetryableEventHandler interface {
	EventHandler

	// MaxRetries 返回最大重试次数
	MaxRetries() int

	// RetryDelay 返回重试间隔时间（毫秒）
	RetryDelay() int
}

// PriorityEventHandler 定义了具有优先级的事件处理器接口
type PriorityEventHandler interface {
	EventHandler

	// Priority 返回处理器的优先级
	// 值越小优先级越高
	Priority() int
}

// TenantAwareEventHandler 定义了租户感知的事件处理器接口
// 实现此接口的处理器可以控制是否处理特定租户的事件
type TenantAwareEventHandler interface {
	EventHandler

	// ShouldHandleForTenant 判断是否应该为指定租户处理事件
	// tenantID 可能是 uint、string 或 nil（全局事件）
	ShouldHandleForTenant(tenantID interface{}) bool

	// GetSupportedTenants 返回此处理器支持的租户列表
	// 返回 nil 表示支持所有租户
	GetSupportedTenants() []interface{}
}

// GlobalEventHandler 定义了全局事件处理器接口
// 全局处理器处理所有租户的事件，不受租户隔离限制
type GlobalEventHandler interface {
	EventHandler

	// IsGlobal 标识这是一个全局处理器
	IsGlobal() bool
}