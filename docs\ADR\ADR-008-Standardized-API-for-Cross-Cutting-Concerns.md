# ADR-008: Standardized API for Cross-Cutting Concerns

## Status
Accepted

## Context
During the refactoring of the `actionlog` module, a critical architectural question arose: how should modules perform common, cross-cutting actions like logging?

The initial approach considered was for each business module to directly call a core `IActionLogger` interface via dependency injection. While this is architecturally sound in terms of type safety and avoiding HTTP overhead, it introduces a significant drawback: the logic for assembling the log data (e.g., fetching user ID, IP address from the request context) would be duplicated in every calling module. This violates the DRY (Don't Repeat Yourself) principle and increases the maintenance burden, as any change to the logging data model would require updates across multiple modules.

We needed a pattern that balanced architectural purity with development efficiency and long-term maintainability.

## Decision
We have decided to adopt a **Standardized API Gateway** pattern for specific, well-defined cross-cutting concerns, with logging as the primary example.

1.  **Centralized API Endpoint**: A dedicated module (e.g., `actionlog`) will expose a standardized, public API endpoint for the specific function (e.g., `POST /api/v1/actionlog/logs`).

2.  **Data Enrichment at the Gateway**: This API endpoint's controller is responsible for **enriching** the incoming request data. It extracts common, context-sensitive information (like `UserID`, `Username` from auth middleware, `ClientIP` from the request) and merges it with the core information provided by the calling module (like the `Description` of the action).

3.  **Simplified Caller Responsibility**: Calling modules are only responsible for providing the unique, business-specific details. They are freed from the repetitive task of assembling common contextual data.

4.  **Internal Core Interface Still Used**: The API endpoint controller, after enriching the data, will then call the internal core service (e.g., `ActionLogService`), which performs the final action. The service layer itself remains clean and testable, unaware that it's being triggered by an API call versus another internal process.

This creates a clear flow:
`Business Module -> HTTP Client -> POST /logs -> ActionLogController (Enriches Data) -> ActionLogService -> Repository -> Database`

## Alternatives Considered
### Alternative 1: Direct Core Interface Injection
- **Description**: Each module that needs to log an action would have the `IActionLogger` interface injected into its service. It would then assemble the `ActionLog` model manually and call the interface method.
- **Pros**: 
    - No HTTP overhead for internal communication.
    - Compile-time type safety.
- **Cons**: 
    - **High Repetition**: Logic for fetching user info, IP, etc., from the context is duplicated in every module.
    - **High Maintenance**: A change in the `ActionLog` model requires finding and updating every place the interface is called.
- **Why Not**: The cost of code duplication and higher maintenance outweighed the benefits of avoiding a negligible internal HTTP call.

## Consequences

### Positive
- **Reduced Code Duplication**: Logic for enriching log data is centralized in one place.
- **Improved Standardization**: All logs are guaranteed to have a consistent structure and data quality.
- **Simplified Development**: Developers in business modules can record logs with a simple API call, without needing to understand the complexities of context propagation.
- **Enhanced Decoupling**: Modules only need to know about a stable API endpoint, not the internal implementation, interfaces, or data models of the logging module.

### Negative
- **Minimal Performance Overhead**: Introduces a slight overhead of an internal HTTP request/response cycle compared to a direct function call. This is considered negligible in our architecture.
- **Runtime Errors**: Errors in API calls (e.g., incorrect DTO, endpoint down) are discovered at runtime, whereas interface misuse can be caught at compile time. This is mitigated by robust testing.

### Neutral
- This decision establishes a clear precedent for how future cross-cutting concerns (e.g., notifications, global caching) could be implemented.

## Implementation Notes
- **Authentication Propagation**: The internal HTTP client making the API call MUST be configured to propagate JWT/auth tokens from the original incoming request to the internal API request to ensure security.
- **Service Discovery**: In production, the hardcoded `localhost` URLs in services should be replaced by a proper service discovery mechanism or configuration provider.
- **DTOs for API Contracts**: Clear Data Transfer Objects (DTOs) should be defined for these public API endpoints to maintain a stable contract.

## References
- This decision directly impacts the implementation of the `admin` and `actionlog` modules.
- Relates to ADR-007 (Convention-Based Routing) as the API endpoint will be discovered automatically. 