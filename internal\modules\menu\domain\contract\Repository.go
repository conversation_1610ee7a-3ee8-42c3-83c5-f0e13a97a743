/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/menu/domain/contract/Repository.go
 * @Description: Defines repository contracts for the Menu module.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"gacms/internal/modules/menu/domain/model"
)

// MenuRepository defines the persistence operations for Menus.
type MenuRepository interface {
	Create(menu *model.Menu) error
	Update(menu *model.Menu) error
	Delete(id uint) error
	GetByID(id uint) (*model.Menu, error)
	GetBySlug(siteID uint, slug string) (*model.Menu, error)
	GetAllBySiteID(siteID uint) ([]*model.Menu, error)
}

// MenuItemRepository defines the persistence operations for MenuItems.
type MenuItemRepository interface {
	Create(item *model.MenuItem) error
	Update(item *model.MenuItem) error
	Delete(id uint) error
	GetByID(id uint) (*model.MenuItem, error)
	GetByMenuID(menuID uint) ([]*model.MenuItem, error)
	DeleteByMenuID(menuID uint) error
}
