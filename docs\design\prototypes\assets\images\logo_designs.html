<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS Logo设计方案</title>
    <style>
        /* 
         © 2025 Clion Nieh. All rights reserved.
         Author: <PERSON><PERSON> <<EMAIL>>
        */
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
            color: #333;
        }
        h1 {
            text-align: center;
            color: #2a4d69;
            margin-bottom: 40px;
        }
        .designs-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 30px;
        }
        .design-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .design-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.12);
        }
        .card-header {
            background-color: #2a4d69;
            color: white;
            padding: 15px;
            font-size: 18px;
            font-weight: bold;
        }
        .card-body {
            padding: 25px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .logo-display {
            width: 200px;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            background-color: white;
            border-radius: 8px;
        }
        .logo-display svg {
            max-width: 100%;
            max-height: 100%;
        }
        .description {
            margin-top: 15px;
            color: #555;
            font-size: 14px;
            line-height: 1.5;
        }
        .download-btn {
            margin-top: 20px;
            padding: 8px 16px;
            background-color: #4b86b4;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }
        .download-btn:hover {
            background-color: #2a4d69;
        }
        footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #777;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
    </style>
</head>
<body>
    <h1>亘安网站内容管理系统 (GACMS) Logo设计方案</h1>
    
    <div class="designs-container">
        <!-- 方案1：模块化安全盾牌 -->
        <div class="design-card">
            <div class="card-header">方案1：模块化安全盾牌</div>
            <div class="card-body">
                <div class="logo-display">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="180" height="180">
                        <defs>
                            <linearGradient id="shield-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" stop-color="#2A4D69" />
                                <stop offset="100%" stop-color="#4B86B4" />
                            </linearGradient>
                        </defs>
                        
                        <!-- 盾牌底座 -->
                        <path d="M100,10 L30,50 L30,110 C30,150 100,185 100,185 C100,185 170,150 170,110 L170,50 L100,10 Z" 
                              fill="url(#shield-gradient)" />
                        
                        <!-- 六边形蜂窝模块 - 装饰盾牌 -->
                        <g opacity="0.3" fill="#FFFFFF">
                            <polygon points="70,50 85,40 100,50 100,70 85,80 70,70" />
                            <polygon points="100,50 115,40 130,50 130,70 115,80 100,70" />
                            <polygon points="55,75 70,65 85,75 85,95 70,105 55,95" />
                            <polygon points="85,75 100,65 115,75 115,95 100,105 85,95" />
                            <polygon points="115,75 130,65 145,75 145,95 130,105 115,95" />
                            <polygon points="70,105 85,95 100,105 100,125 85,135 70,125" />
                            <polygon points="100,105 115,95 130,105 130,125 115,135 100,125" />
                        </g>
                        
                        <!-- 中心G字母 -->
                        <path d="M115,90 C115,77 105,67 90,67 C75,67 65,77 65,90 C65,103 75,113 90,113 L115,113 L115,100 L95,100 L95,90 L115,90 Z M90,100 C85,100 80,95 80,90 C80,85 85,80 90,80 C95,80 100,85 100,90 L100,100 L90,100 Z" 
                              fill="#FFFFFF" />
                    </svg>
                </div>
                <p class="description">
                    现代化盾牌形状，由六边形蜂窝状模块组成，象征系统的安全性和模块化特性。盾牌中心嵌入简化的"G"字母，传递"亘安"的核心价值——持久的安全保障。
                </p>
                <a class="download-btn" href="#" onclick="downloadSVG(this, 'shield-logo.svg', 0)">下载SVG</a>
            </div>
        </div>
        
        <!-- 方案2：亘字流线型 -->
        <div class="design-card">
            <div class="card-header">方案2：亘字流线型</div>
            <div class="card-body">
                <div class="logo-display">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="180" height="180">
                        <defs>
                            <linearGradient id="flow-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" stop-color="#1A237E" />
                                <stop offset="100%" stop-color="#4FC3F7" />
                            </linearGradient>
                        </defs>
                        
                        <!-- G字母底形 -->
                        <path d="M150,100 C150,65 125,40 90,40 C55,40 30,65 30,100 C30,135 55,160 90,160 L150,160 L150,130 L100,130 L100,100 L150,100 Z" 
                              fill="url(#flow-gradient)" opacity="0.8" />
                        
                        <!-- 亘字贯穿线条 -->
                        <path d="M30,85 L170,85 C165,85 160,90 160,95 C160,100 165,105 170,105 L30,105 C35,105 40,100 40,95 C40,90 35,85 30,85 Z" 
                              fill="#FFFFFF" opacity="0.9" />
                        
                        <!-- 流动线条装饰 -->
                        <g opacity="0.6">
                            <path d="M40,75 C70,65 100,75 130,65 L150,60" stroke="#4FC3F7" stroke-width="3" fill="none" />
                            <path d="M40,115 C80,125 120,115 160,125" stroke="#4FC3F7" stroke-width="3" fill="none" />
                            <path d="M160,70 C140,80 150,90 160,100" stroke="#4FC3F7" stroke-width="3" fill="none" />
                        </g>
                    </svg>
                </div>
                <p class="description">
                    将"亘"字简化为贯穿东西的水平线，向右扩展形成数据流的形态，巧妙融入"G"字母。展现内容的流动管理和系统的高效性，传递连接、贯通和高效的理念。
                </p>
                <a class="download-btn" href="#" onclick="downloadSVG(this, 'flow-logo.svg', 1)">下载SVG</a>
            </div>
        </div>
        
        <!-- 方案3：多端协同立方体 -->
        <div class="design-card">
            <div class="card-header">方案3：多端协同立方体</div>
            <div class="card-body">
                <div class="logo-display">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="180" height="180">
                        <defs>
                            <linearGradient id="cube-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" stop-color="#0277BD" />
                                <stop offset="100%" stop-color="#0277BD" stop-opacity="0.7" />
                            </linearGradient>
                        </defs>
                        
                        <!-- 立方体底面 -->
                        <polygon points="50,125 100,150 150,125 100,100" fill="#0277BD" />
                        
                        <!-- 立方体左面 -->
                        <polygon points="50,75 50,125 100,100 100,50" fill="url(#cube-gradient)" />
                        
                        <!-- 立方体右面，缺角 -->
                        <path d="M100,50 L100,100 L150,125 L150,75 L130,65 Z" fill="#0277BD" opacity="0.8" />
                        
                        <!-- 切角处高光 -->
                        <polygon points="100,50 130,65 150,75 120,40" fill="#FF8A65" />
                        
                        <!-- 中心连接线 -->
                        <g stroke="#FFFFFF" stroke-width="1.5" opacity="0.7">
                            <line x1="100" y1="100" x2="75" y2="75" />
                            <line x1="100" y1="100" x2="125" y2="75" />
                            <line x1="100" y1="100" x2="75" y2="125" />
                            <line x1="100" y1="100" x2="125" y2="125" />
                        </g>
                        
                        <!-- G标志 - 简化版 -->
                        <path d="M110,100 C110,95 105,90 100,90 C95,90 90,95 90,100 C90,105 95,110 100,110 L110,110 L110,105 L100,105 L100,100 L110,100 Z" 
                              fill="#FFFFFF" />
                    </svg>
                </div>
                <p class="description">
                    三维立方体设计，各面代表不同平台。立方体一角切除，从中心发出连接线延伸到各面。展示系统的完整性和多端内容协同管理能力，体现模块化和灵活性。
                </p>
                <a class="download-btn" href="#" onclick="downloadSVG(this, 'cube-logo.svg', 2)">下载SVG</a>
            </div>
        </div>
        
        <!-- 方案4：字母融合与图标元素 -->
        <div class="design-card">
            <div class="card-header">方案4：字母融合与图标元素</div>
            <div class="card-body">
                <div class="logo-display">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="180" height="180">
                        <defs>
                            <linearGradient id="letter-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" stop-color="#283593" />
                                <stop offset="100%" stop-color="#78909C" />
                            </linearGradient>
                        </defs>
                        
                        <!-- 字母G基础 -->
                        <path d="M145,100 C145,70 120,45 90,45 C60,45 35,70 35,100 C35,130 60,155 90,155 L145,155 L145,115 L105,115 L105,95 L145,95 L145,100 Z" 
                              fill="url(#letter-gradient)" />
                        
                        <!-- 字母A融入G中 -->
                        <path d="M90,45 L60,155 L80,155 L85,135 L110,135 L115,155 L135,155 L110,45 L90,45 Z M90,115 L97.5,80 L105,115 L90,115 Z" 
                              fill="#FFFFFF" opacity="0.15" />
                        
                        <!-- 锁图标 -->
                        <g transform="translate(97.5, 95) scale(0.4)">
                            <rect x="-25" y="-10" width="50" height="40" rx="5" fill="#FFFFFF" />
                            <path d="M-10,-10 L-10,-25 C-10,-35 10,-35 10,-25 L10,-10" stroke="#FFFFFF" stroke-width="5" fill="none" />
                        </g>
                    </svg>
                </div>
                <p class="description">
                    艺术化融合"G"和"A"字母，"G"设计为半圆形状，"A"形成其中的尖角部分。字母结合处添加锁图标，强调安全性，传达专业、安全和整合能力。
                </p>
                <a class="download-btn" href="#" onclick="downloadSVG(this, 'letter-logo.svg', 3)">下载SVG</a>
            </div>
        </div>
        
        <!-- 方案5：几何模块拼图 -->
        <div class="design-card">
            <div class="card-header">方案5：几何模块拼图</div>
            <div class="card-body">
                <div class="logo-display">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="180" height="180">
                        <defs>
                            <linearGradient id="module-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" stop-color="#1565C0" />
                                <stop offset="100%" stop-color="#4DD0E1" />
                            </linearGradient>
                        </defs>
                        
                        <!-- 模块G形状 -->
                        <g fill="url(#module-gradient)">
                            <!-- 横向模块 - 顶部 -->
                            <rect x="50" y="40" width="100" height="20" rx="2" />
                            
                            <!-- 纵向模块 - 左侧 -->
                            <rect x="50" y="40" width="20" height="120" rx="2" />
                            
                            <!-- 横向模块 - 底部 -->
                            <rect x="50" y="140" width="100" height="20" rx="2" />
                            
                            <!-- 纵向模块 - 右侧上半部分 -->
                            <rect x="130" y="40" width="20" height="50" rx="2" />
                            
                            <!-- 横向模块 - 中部 -->
                            <rect x="90" y="90" width="60" height="20" rx="2" />
                            
                            <!-- 纵向模块 - 右侧下半部分 -->
                            <rect x="130" y="110" width="20" height="50" rx="2" />
                        </g>
                        
                        <!-- 分离的模块 - 立体感 -->
                        <g opacity="0.7">
                            <rect x="85" y="65" width="30" height="15" rx="2" fill="#4DD0E1" transform="translate(2, -2)" />
                            <rect x="70" y="115" width="40" height="15" rx="2" fill="#4DD0E1" transform="translate(-2, 2)" />
                        </g>
                    </svg>
                </div>
                <p class="description">
                    多个几何形状组成拼图式样，形成"G"的轮廓。部分模块略微分离，创造空间感和层次感。体现系统的模块化和组件化特性，展现现代、技术和可定制特点。
                </p>
                <a class="download-btn" href="#" onclick="downloadSVG(this, 'module-logo.svg', 4)">下载SVG</a>
            </div>
        </div>
        
        <!-- 方案6：动态波浪数据流 -->
        <div class="design-card">
            <div class="card-header">方案6：动态波浪数据流</div>
            <div class="card-body">
                <div class="logo-display">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="180" height="180">
                        <defs>
                            <linearGradient id="wave-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" stop-color="#0D47A1" />
                                <stop offset="100%" stop-color="#00BCD4" />
                            </linearGradient>
                        </defs>
                        
                        <!-- 波浪线条集合 -->
                        <g fill="none" stroke="url(#wave-gradient)">
                            <path d="M30,60 C50,40 70,80 90,60 C110,40 130,80 150,60 C170,40 190,60 190,60" 
                                  stroke-width="6" opacity="0.2" />
                            <path d="M30,80 C50,60 70,100 90,80 C110,60 130,100 150,80 C170,60 190,80 190,80" 
                                  stroke-width="8" opacity="0.3" />
                            <path d="M30,100 C50,80 70,120 90,100 C110,80 130,120 150,100 C170,80 190,100 190,100" 
                                  stroke-width="10" opacity="0.4" />
                            <path d="M30,120 C50,100 70,140 90,120 C110,100 130,140 150,120 C170,100 190,120 190,120" 
                                  stroke-width="12" opacity="0.5" />
                            <path d="M30,140 C50,120 70,160 90,140 C110,120 130,160 150,140 C170,120 190,140 190,140" 
                                  stroke-width="14" opacity="0.6" />
                        </g>
                        
                        <!-- 收敛到G形状的蒙版 -->
                        <path d="M150,100 C150,70 125,45 90,45 C55,45 30,70 30,100 C30,130 55,155 90,155 L150,155 L150,125 L100,125 L100,100 L150,100 Z" 
                              fill="#FFFFFF" opacity="0.1" />
                        
                        <!-- G字母提示线 -->
                        <path d="M145,100 C145,75 125,55 95,55 C65,55 45,75 45,100 C45,125 65,145 95,145 L145,145 L145,120 L95,120 L95,100 L145,100 Z" 
                              stroke="#FFFFFF" stroke-width="2" fill="none" />
                    </svg>
                </div>
                <p class="description">
                    一系列平行波浪线条，逐渐收敛形成"G"轮廓。波浪有不同粗细和间距，创造动态和深度感。体现数据和内容的流动管理能力，展示系统的动态特性。
                </p>
                <a class="download-btn" href="#" onclick="downloadSVG(this, 'wave-logo.svg', 5)">下载SVG</a>
            </div>
        </div>
    </div>
    
    <footer>
        <p>© 2025 Clion Nieh. All rights reserved.</p>
        <p>这些SVG logo设计可以直接在网页中使用，也可以用Adobe Illustrator或Inkscape等工具进一步编辑。</p>
    </footer>

    <script>
        function downloadSVG(link, filename, index) {
            // 获取SVG元素
            const svgElement = document.querySelectorAll('.logo-display svg')[index];
            // 获取SVG代码
            const svgContent = new XMLSerializer().serializeToString(svgElement);
            // 创建Blob对象
            const blob = new Blob([svgContent], {type: 'image/svg+xml'});
            // 创建URL
            const url = URL.createObjectURL(blob);
            // 设置下载链接
            link.href = url;
            link.download = filename;
        }
    </script>
</body>
</html> 