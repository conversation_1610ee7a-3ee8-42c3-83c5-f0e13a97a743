/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/system/service/SystemService.go
 * @Description: 系统设置基础设施服务实现
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"fmt"

	"gacms/internal/core/system/contract"
	"gacms/internal/core/system/model"
	"gacms/pkg/contract"
	"go.uber.org/zap"
)

// SiteService 站点管理服务
type SiteService struct {
	repo     contract.SiteRepository
	eventMgr contract.EventManager
	logger   *zap.Logger
}

// NewSiteService 创建站点管理服务
func NewSiteService(repo contract.SiteRepository, eventMgr contract.EventManager, logger *zap.Logger) *SiteService {
	return &SiteService{
		repo:     repo,
		eventMgr: eventMgr,
		logger:   logger,
	}
}

// CreateSite 创建站点
func (s *SiteService) CreateSite(name, domain, description string) (*model.Site, error) {
	// 检查域名是否已存在
	exists, err := s.repo.Exists(domain)
	if err != nil {
		return nil, fmt.Errorf("failed to check domain existence: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("domain %s already exists", domain)
	}
	
	site := &model.Site{
		Name:        name,
		Domain:      domain,
		Description: description,
		IsActive:    true,
		Config: model.SiteConfig{
			Title:        name,
			SEOEnabled:   true,
			CacheEnabled: true,
			CacheTTL:     3600,
		},
	}
	
	if err := s.repo.Create(site); err != nil {
		return nil, fmt.Errorf("failed to create site: %w", err)
	}
	
	// 发布站点创建事件
	s.publishSiteEvent("site.created", site)
	
	s.logger.Info("Site created",
		zap.Uint("site_id", site.ID),
		zap.String("domain", domain),
		zap.String("name", name),
	)
	
	return site, nil
}

// UpdateSite 更新站点
func (s *SiteService) UpdateSite(siteID uint, name, description string) (*model.Site, error) {
	site, err := s.repo.GetByID(siteID)
	if err != nil {
		return nil, fmt.Errorf("failed to get site: %w", err)
	}
	if site == nil {
		return nil, fmt.Errorf("site not found")
	}
	
	site.Name = name
	site.Description = description
	
	if err := s.repo.Update(site); err != nil {
		return nil, fmt.Errorf("failed to update site: %w", err)
	}
	
	// 发布站点更新事件
	s.publishSiteEvent("site.updated", site)
	
	s.logger.Info("Site updated",
		zap.Uint("site_id", siteID),
		zap.String("name", name),
	)
	
	return site, nil
}

// DeleteSite 删除站点
func (s *SiteService) DeleteSite(siteID uint) error {
	site, err := s.repo.GetByID(siteID)
	if err != nil {
		return fmt.Errorf("failed to get site: %w", err)
	}
	if site == nil {
		return fmt.Errorf("site not found")
	}
	
	if err := s.repo.Delete(siteID); err != nil {
		return fmt.Errorf("failed to delete site: %w", err)
	}
	
	// 发布站点删除事件
	s.publishSiteEvent("site.deleted", site)
	
	s.logger.Info("Site deleted",
		zap.Uint("site_id", siteID),
		zap.String("domain", site.Domain),
	)
	
	return nil
}

// GetSite 获取站点
func (s *SiteService) GetSite(siteID uint) (*model.Site, error) {
	return s.repo.GetByID(siteID)
}

// GetSiteByDomain 根据域名获取站点
func (s *SiteService) GetSiteByDomain(domain string) (*model.Site, error) {
	return s.repo.GetByDomain(domain)
}

// ListSites 列出所有站点
func (s *SiteService) ListSites(page, pageSize int) ([]*model.Site, int64, error) {
	return s.repo.ListAll(page, pageSize)
}

// ListActiveSites 列出激活的站点
func (s *SiteService) ListActiveSites() ([]*model.Site, error) {
	return s.repo.ListActive()
}

// UpdateSiteConfig 更新站点配置
func (s *SiteService) UpdateSiteConfig(siteID uint, config *model.SiteConfig) error {
	if err := s.repo.UpdateConfig(siteID, config); err != nil {
		return fmt.Errorf("failed to update site config: %w", err)
	}
	
	// 发布配置更新事件
	eventData := map[string]interface{}{
		"site_id": siteID,
		"config":  config,
	}
	event := s.eventMgr.CreateEvent(nil, "site.config.updated", eventData)
	s.eventMgr.PublishEvent(event)
	
	s.logger.Info("Site config updated",
		zap.Uint("site_id", siteID),
	)
	
	return nil
}

// GetSiteConfig 获取站点配置
func (s *SiteService) GetSiteConfig(siteID uint) (*model.SiteConfig, error) {
	return s.repo.GetConfig(siteID)
}

// ActivateSite 激活站点
func (s *SiteService) ActivateSite(siteID uint) error {
	site, err := s.repo.GetByID(siteID)
	if err != nil {
		return fmt.Errorf("failed to get site: %w", err)
	}
	if site == nil {
		return fmt.Errorf("site not found")
	}
	
	site.IsActive = true
	if err := s.repo.Update(site); err != nil {
		return fmt.Errorf("failed to activate site: %w", err)
	}
	
	// 发布站点激活事件
	s.publishSiteEvent("site.activated", site)
	
	s.logger.Info("Site activated",
		zap.Uint("site_id", siteID),
	)
	
	return nil
}

// DeactivateSite 停用站点
func (s *SiteService) DeactivateSite(siteID uint) error {
	site, err := s.repo.GetByID(siteID)
	if err != nil {
		return fmt.Errorf("failed to get site: %w", err)
	}
	if site == nil {
		return fmt.Errorf("site not found")
	}
	
	site.IsActive = false
	if err := s.repo.Update(site); err != nil {
		return fmt.Errorf("failed to deactivate site: %w", err)
	}
	
	// 发布站点停用事件
	s.publishSiteEvent("site.deactivated", site)
	
	s.logger.Info("Site deactivated",
		zap.Uint("site_id", siteID),
	)
	
	return nil
}

// publishSiteEvent 发布站点事件
func (s *SiteService) publishSiteEvent(eventType string, site *model.Site) {
	eventData := map[string]interface{}{
		"site_id":     site.ID,
		"domain":      site.Domain,
		"name":        site.Name,
		"is_active":   site.IsActive,
		"description": site.Description,
	}
	
	event := s.eventMgr.CreateEvent(nil, eventType, eventData)
	if err := s.eventMgr.PublishEvent(event); err != nil {
		s.logger.Error("Failed to publish site event",
			zap.String("event_type", eventType),
			zap.Uint("site_id", site.ID),
			zap.Error(err),
		)
	}
}
