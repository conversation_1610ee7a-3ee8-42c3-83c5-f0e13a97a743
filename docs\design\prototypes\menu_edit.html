<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 菜单编辑</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .menu-item {
            transition: all 0.2s ease-in-out;
        }
        
        .menu-item:hover {
            background-color: rgba(75, 85, 99, 0.2);
        }
        
        .menu-item.active {
            background-color: rgba(59, 130, 246, 0.2);
            border-color: rgba(59, 130, 246, 0.5);
        }
        
        .sortable-ghost {
            opacity: 0.5;
            background-color: rgba(59, 130, 246, 0.3);
        }
        
        .menu-level-0 {
            border-left: 3px solid transparent;
        }
        
        .menu-level-1 {
            border-left: 3px solid rgba(59, 130, 246, 0.5);
            margin-left: 1.5rem;
        }
        
        .menu-level-2 {
            border-left: 3px solid rgba(139, 92, 246, 0.5);
            margin-left: 3rem;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="content.html" class="hover:text-white">内容管理</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="menus.html" class="hover:text-white">菜单管理</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">编辑菜单</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <div class="flex items-center">
                        <h2 class="text-xl font-bold text-white relative pl-3 section-title mr-4">编辑菜单: 主导航</h2>
                        <select id="menuSelect" class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="1" selected>主导航</option>
                            <option value="2">底部菜单</option>
                            <option value="3">用户中心菜单</option>
                            <option value="4">移动端菜单</option>
                        </select>
                    </div>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="saveBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-save text-white"></i>
                                </span>
                                保存菜单
                            </span>
                        </button>
                        <a href="menus.html" class="flex items-center justify-center bg-gradient-to-r from-gray-600 to-gray-700 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-gray-600/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-times text-white"></i>
                                </span>
                                取消
                            </span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 菜单编辑区域 -->
            <div class="grid grid-cols-1 xl:grid-cols-3 gap-6 mb-6">
                <!-- 左侧：菜单结构 -->
                <div class="xl:col-span-2">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white flex justify-between items-center">
                            <span>菜单结构</span>
                            <button id="expandCollapseBtn" class="text-xs bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded text-gray-300">
                                展开全部
                            </button>
                        </h3>
                        
                        <p class="text-gray-400 mb-4">拖动菜单项调整顺序，点击菜单项编辑详情</p>
                        
                        <!-- 菜单项列表 -->
                        <div id="menuItemsList" class="space-y-2">
                            <!-- 菜单项 1 -->
                            <div class="menu-item menu-level-0 border border-gray-700 rounded-lg p-3 bg-gray-800/10 cursor-move">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <i class="fas fa-grip-vertical text-gray-500 mr-3"></i>
                                        <span class="text-white font-medium">首页</span>
                                        <span class="ml-2 text-gray-400 text-sm">/</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white px-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-400 px-1">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                        <button class="toggle-children text-gray-400 hover:text-white px-1">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 菜单项 2 -->
                            <div class="menu-item menu-level-0 active border border-gray-700 rounded-lg p-3 cursor-move">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <i class="fas fa-grip-vertical text-gray-500 mr-3"></i>
                                        <span class="text-white font-medium">产品服务</span>
                                        <span class="ml-2 text-gray-400 text-sm">/products</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white px-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-400 px-1">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                        <button class="toggle-children text-blue-400 hover:text-white px-1">
                                            <i class="fas fa-chevron-up"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 子菜单项 2-1 -->
                            <div class="menu-item menu-level-1 border border-gray-700 rounded-lg p-3 bg-gray-800/10 cursor-move">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <i class="fas fa-grip-vertical text-gray-500 mr-3"></i>
                                        <span class="text-white font-medium">云服务器</span>
                                        <span class="ml-2 text-gray-400 text-sm">/products/cloud-server</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white px-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-400 px-1">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                        <button class="toggle-children text-gray-400 hover:text-white px-1">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 子菜单项 2-2 -->
                            <div class="menu-item menu-level-1 border border-gray-700 rounded-lg p-3 bg-gray-800/10 cursor-move">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <i class="fas fa-grip-vertical text-gray-500 mr-3"></i>
                                        <span class="text-white font-medium">存储解决方案</span>
                                        <span class="ml-2 text-gray-400 text-sm">/products/storage</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white px-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-400 px-1">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                        <button class="toggle-children text-gray-400 hover:text-white px-1">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 菜单项 3 -->
                            <div class="menu-item menu-level-0 border border-gray-700 rounded-lg p-3 bg-gray-800/10 cursor-move">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <i class="fas fa-grip-vertical text-gray-500 mr-3"></i>
                                        <span class="text-white font-medium">解决方案</span>
                                        <span class="ml-2 text-gray-400 text-sm">/solutions</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white px-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-400 px-1">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                        <button class="toggle-children text-gray-400 hover:text-white px-1">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 菜单项 4 -->
                            <div class="menu-item menu-level-0 border border-gray-700 rounded-lg p-3 bg-gray-800/10 cursor-move">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <i class="fas fa-grip-vertical text-gray-500 mr-3"></i>
                                        <span class="text-white font-medium">关于我们</span>
                                        <span class="ml-2 text-gray-400 text-sm">/about</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white px-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-400 px-1">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                        <button class="toggle-children text-gray-400 hover:text-white px-1">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 菜单项 5 -->
                            <div class="menu-item menu-level-0 border border-gray-700 rounded-lg p-3 bg-gray-800/10 cursor-move">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <i class="fas fa-grip-vertical text-gray-500 mr-3"></i>
                                        <span class="text-white font-medium">联系我们</span>
                                        <span class="ml-2 text-gray-400 text-sm">/contact</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white px-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-400 px-1">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                        <button class="toggle-children text-gray-400 hover:text-white px-1 invisible">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 添加菜单项按钮 -->
                        <div class="mt-4">
                            <button id="addMenuItemBtn" class="flex items-center text-blue-400 hover:text-blue-300 transition-colors">
                                <i class="fas fa-plus-circle mr-2"></i> 添加菜单项
                            </button>
                        </div>
                    </div>
                    
                    <!-- 菜单预览 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">菜单预览</h3>
                        
                        <div class="bg-gray-900 rounded-lg p-4 mb-4">
                            <div class="flex justify-between items-center text-sm">
                                <div class="flex space-x-6">
                                    <a href="#" class="text-white font-medium">首页</a>
                                    <div class="relative group">
                                        <a href="#" class="text-blue-400 font-medium flex items-center">
                                            产品服务
                                            <i class="fas fa-chevron-down text-xs ml-1"></i>
                                        </a>
                                        <div class="absolute top-full left-0 mt-2 bg-gray-800 border border-gray-700 rounded-lg shadow-lg p-2 w-40 hidden group-hover:block">
                                            <a href="#" class="block px-3 py-2 text-sm text-white hover:bg-gray-700 rounded">云服务器</a>
                                            <a href="#" class="block px-3 py-2 text-sm text-white hover:bg-gray-700 rounded">存储解决方案</a>
                                        </div>
                                    </div>
                                    <a href="#" class="text-gray-300 hover:text-white font-medium">解决方案</a>
                                    <a href="#" class="text-gray-300 hover:text-white font-medium">关于我们</a>
                                    <a href="#" class="text-gray-300 hover:text-white font-medium">联系我们</a>
                                </div>
                                <div>
                                    <a href="#" class="text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-user mr-1"></i> 登录
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <p class="text-gray-400 text-sm italic">注：菜单的实际外观将受主题样式影响，此处仅为示意</p>
                    </div>
                </div>
                
                <!-- 右侧：菜单项设置 -->
                <div class="xl:col-span-1">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">菜单项设置</h3>
                        
                        <div id="menuItemForm" class="space-y-4">
                            <div>
                                <label class="block text-gray-300 mb-2">标题 <span class="text-red-500">*</span></label>
                                <input type="text" id="menuItemTitle" value="产品服务" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">链接 URL</label>
                                <input type="text" id="menuItemUrl" value="/products" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">目标</label>
                                <select id="menuItemTarget" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="_self" selected>当前窗口</option>
                                    <option value="_blank">新窗口</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">图标</label>
                                <div class="flex">
                                    <span class="inline-flex items-center px-3 bg-gray-800 border border-r-0 border-gray-600 rounded-l-lg text-gray-400">
                                        <i class="fas fa-boxes"></i>
                                    </span>
                                    <input type="text" id="menuItemIcon" value="fas fa-boxes" class="flex-1 bg-gray-700 border border-gray-600 rounded-r-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                <p class="text-gray-400 text-sm mt-1">输入FontAwesome图标类（例如：fas fa-boxes）</p>
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">CSS类</label>
                                <input type="text" id="menuItemClass" value="featured-menu" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <p class="text-gray-400 text-sm mt-1">应用于菜单项的额外CSS类</p>
                            </div>
                            
                            <div class="pt-2">
                                <label class="flex items-center">
                                    <input type="checkbox" id="menuItemNofollow" class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-900 w-5 h-5">
                                    <span class="ml-2 text-gray-300">添加 rel="nofollow" 属性</span>
                                </label>
                            </div>
                            
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" id="menuItemHighlight" checked class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-900 w-5 h-5">
                                    <span class="ml-2 text-gray-300">高亮显示</span>
                                </label>
                            </div>
                            
                            <div class="pt-2">
                                <button id="updateMenuItemBtn" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg transition-colors">
                                    更新菜单项
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 菜单设置 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">菜单设置</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label class="block text-gray-300 mb-2">菜单名称</label>
                                <input type="text" value="主导航" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">菜单位置</label>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" checked class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-900 w-5 h-5">
                                        <span class="ml-2 text-gray-300">主导航区域</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-900 w-5 h-5">
                                        <span class="ml-2 text-gray-300">页脚菜单</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-900 w-5 h-5">
                                        <span class="ml-2 text-gray-300">移动端导航</span>
                                    </label>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 mb-2">默认缩进级别</label>
                                <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="0" selected>0级（无缩进）</option>
                                    <option value="1">1级缩进</option>
                                    <option value="2">2级缩进</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 常用链接 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">添加常用链接</h3>
                        
                        <div class="space-y-4">
                            <button class="flex items-center justify-between w-full bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors">
                                <span>首页</span>
                                <i class="fas fa-plus"></i>
                            </button>
                            <button class="flex items-center justify-between w-full bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors">
                                <span>关于我们</span>
                                <i class="fas fa-plus"></i>
                            </button>
                            <button class="flex items-center justify-between w-full bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors">
                                <span>文章分类</span>
                                <i class="fas fa-plus"></i>
                            </button>
                            <button class="flex items-center justify-between w-full bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors">
                                <span>联系我们</span>
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 菜单选择变更
            document.getElementById('menuSelect').addEventListener('change', function() {
                // 这里可以加载选定的菜单数据
                console.log('选择菜单ID: ' + this.value);
            });
            
            // 菜单展开/折叠按钮
            const toggleButtons = document.querySelectorAll('.toggle-children');
            toggleButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const icon = this.querySelector('i');
                    const menuItem = this.closest('.menu-item');
                    const isExpanded = icon.classList.contains('fa-chevron-up');
                    
                    // 切换图标
                    if (isExpanded) {
                        icon.classList.replace('fa-chevron-up', 'fa-chevron-down');
                        this.classList.remove('text-blue-400');
                        this.classList.add('text-gray-400');
                        
                        // 隐藏子菜单
                        let current = menuItem.nextElementSibling;
                        while (current && current.classList.contains('menu-level-1')) {
                            current.style.display = 'none';
                            current = current.nextElementSibling;
                        }
                    } else {
                        icon.classList.replace('fa-chevron-down', 'fa-chevron-up');
                        this.classList.remove('text-gray-400');
                        this.classList.add('text-blue-400');
                        
                        // 显示子菜单
                        let current = menuItem.nextElementSibling;
                        while (current && current.classList.contains('menu-level-1')) {
                            current.style.display = 'block';
                            current = current.nextElementSibling;
                        }
                    }
                });
            });
            
            // 展开/折叠所有按钮
            document.getElementById('expandCollapseBtn').addEventListener('click', function() {
                const isExpanded = this.textContent.trim() === '折叠全部';
                
                if (isExpanded) {
                    this.textContent = '展开全部';
                    
                    // 折叠所有菜单
                    document.querySelectorAll('.toggle-children').forEach(button => {
                        const icon = button.querySelector('i');
                        if (icon.classList.contains('fa-chevron-up')) {
                            button.click();
                        }
                    });
                } else {
                    this.textContent = '折叠全部';
                    
                    // 展开所有菜单
                    document.querySelectorAll('.toggle-children').forEach(button => {
                        const icon = button.querySelector('i');
                        if (icon.classList.contains('fa-chevron-down')) {
                            button.click();
                        }
                    });
                }
            });
            
            // 选择菜单项进行编辑
            const menuItems = document.querySelectorAll('.menu-item');
            menuItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // 阻止冒泡，避免点击按钮时触发
                    if (e.target.tagName === 'BUTTON' || e.target.tagName === 'I') {
                        return;
                    }
                    
                    // 移除其他项的选中状态
                    menuItems.forEach(i => i.classList.remove('active'));
                    
                    // 添加当前项的选中状态
                    this.classList.add('active');
                    
                    // 在实际应用中，这里会加载菜单项数据到表单
                    console.log('选中菜单项: ' + this.querySelector('.text-white').textContent);
                });
            });
            
            // 表单保存功能
            document.getElementById('saveBtn').addEventListener('click', function() {
                alert('菜单保存成功！');
                // 在实际场景中，这里会有AJAX请求保存表单数据
            });
            
            // 更新菜单项
            document.getElementById('updateMenuItemBtn').addEventListener('click', function() {
                const activeItem = document.querySelector('.menu-item.active');
                if (activeItem) {
                    const titleInput = document.getElementById('menuItemTitle');
                    const urlInput = document.getElementById('menuItemUrl');
                    
                    // 更新菜单项的标题和URL
                    activeItem.querySelector('.text-white').textContent = titleInput.value;
                    activeItem.querySelector('.text-gray-400').textContent = urlInput.value;
                    
                    alert('菜单项已更新！');
                } else {
                    alert('请先选择一个菜单项');
                }
            });
            
            // 添加菜单项按钮
            document.getElementById('addMenuItemBtn').addEventListener('click', function() {
                alert('在实际应用中，这里会打开添加菜单项的表单');
            });
        });
    </script>
</body>
</html> 