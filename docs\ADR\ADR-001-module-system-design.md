# ADR-001: 模块系统设计与核心/扩展模块的区分

## 状态
接受

## 背景
GACMS 作为一个内容管理系统，需要一个灵活、可扩展且结构清晰的模块系统。我们需要定义模块的概念、生命周期以及如何区分核心模块和扩展模块。用户模块作为最基础的模块，包含了 RBAC 权限管理等关键功能，不应该被停用，这促使我们思考如何设计模块系统。

核心问题包括：
- 如何定义模块接口
- 如何区分核心模块和可选模块
- 如何简化模块的注册和初始化
- 如何处理模块之间的依赖关系

## 决策
我们采用以下设计来实现 GACMS 的模块系统：

1. **模块类型的区分**：
   - 通过 `ModuleType` 类型明确区分核心模块和扩展模块
   - 核心模块 (`CoreModule`) 不可停用，在系统启动时必须加载
   - 扩展模块 (`ExtensionModule`) 可以根据需要启用或停用

2. **简化的模块接口**：
   ```go
   type IModule interface {
       // 返回模块的唯一标识符
       GetName() string
       
       // 返回模块的版本号
       GetVersion() string
       
       // 返回模块类型
       GetType() ModuleType
       
       // 返回模块需要注册的数据模型
       GetModels() []interface{}
       
       // 返回模块提供的所有权限
       ExposePermissions() []PermissionInfo
       
       // 初始化模块
       Initialize() error
   }
   ```

3. **模块注册机制**：
   - 使用依赖注入框架 (fx) 注册模块
   - 核心模块在应用启动时自动初始化
   - 扩展模块根据配置决定是否初始化

4. **权限声明式设计**：
   - 模块通过 `ExposePermissions()` 方法声明其提供的权限
   - 权限信息包含模块名、权限名、标识符和描述

## 替代方案

### 方案 1: 复杂的激活/停用逻辑
- **优点**：提供了更细粒度的模块生命周期控制
- **缺点**：实现复杂，代码冗余，易产生错误
- **为什么不选**：违背最小化原则，对核心模块增加了不必要的复杂性

### 方案 2: 使用注解而非代码标记核心模块
- **优点**：配置更灵活，可在外部文件中定义
- **缺点**：增加了配置复杂性，降低了代码可读性
- **为什么不选**：核心模块的身份不应是可配置的，应当在设计时明确

### 方案 3: 完全不区分核心和扩展模块
- **优点**：简化了模块接口，统一了处理方式
- **缺点**：无法保护核心功能，系统稳定性受影响
- **为什么不选**：某些模块（如用户模块）对系统至关重要，必须保证其不被停用

## 影响

### 积极影响
- 模块系统更加简洁明了，易于理解和实现
- 通过类型系统保证了核心模块的安全，而非复杂的运行时检查
- 减少了不必要的代码，提高了系统的可维护性
- 为未来的模块扩展提供了清晰的模板

### 消极影响
- 核心模块和扩展模块有不同的接口实现要求，需要开发者了解这一区别
- 系统启动时必须处理所有核心模块，无法实现真正的懒加载

### 中立影响
- 模块间的依赖关系需要在注册时显式声明
- 权限系统与模块系统紧密集成，模块变更可能影响权限管理

## 实施说明

### 技术详情
- 使用 Go 语言的接口机制和类型系统实现模块区分
- 通过 fx 依赖注入框架管理模块生命周期
- 在应用启动时初始化所有核心模块

### 集成点
- 模块需要实现 `IModule` 接口
- 模块注册到依赖注入容器
- 权限系统通过 `ExposePermissions()` 获取模块权限

### 成功指标
- 模块加载和初始化过程无错误
- 系统能正确区分和处理核心模块和扩展模块
- 权限系统能正确获取并管理各模块的权限

## 参考资料
- 领域驱动设计 (DDD) 原则
- Go 语言接口设计最佳实践
- uber-go/fx 依赖注入框架文档 