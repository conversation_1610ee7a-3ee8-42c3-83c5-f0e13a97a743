<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 页面管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .page-card {
            transition: all 0.3s ease;
        }
        
        .page-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .badge-published {
            background-color: rgba(16, 185, 129, 0.2);
            color: rgb(16, 185, 129);
        }
        
        .badge-draft {
            background-color: rgba(107, 114, 128, 0.2);
            color: rgb(107, 114, 128);
        }
        
        .badge-scheduled {
            background-color: rgba(59, 130, 246, 0.2);
            color: rgb(59, 130, 246);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">页面管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-plus text-white"></i>
                                </span>
                                创建页面
                            </span>
                        </button>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="relative">
                            <input type="text" placeholder="搜索页面..." 
                                class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent pl-10">
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                        
                        <div class="relative">
                            <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none">
                                <option>所有状态</option>
                                <option>已发布</option>
                                <option>草稿</option>
                                <option>定时发布</option>
                            </select>
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                        
                        <div class="relative">
                            <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none">
                                <option>最新创建</option>
                                <option>最近更新</option>
                                <option>按标题排序</option>
                                <option>访问最多</option>
                            </select>
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 页面列表 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                <!-- 页面卡片 1 -->
                <div class="page-card bg-gray-800/10 border border-gray-700 rounded-xl overflow-hidden">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1563089145-599997674d42?auto=format&fit=crop&w=800&h=400" alt="关于我们" class="w-full h-40 object-cover">
                        <span class="badge badge-published absolute top-3 right-3">已发布</span>
                    </div>
                    <div class="p-4">
                        <h3 class="text-lg font-semibold mb-2">关于我们</h3>
                        <p class="text-gray-400 text-sm mb-3 line-clamp-2">公司简介、团队成员、企业文化和发展历程介绍页面。</p>
                        <div class="flex justify-between items-center text-sm">
                            <span class="text-gray-500">更新于: 2025-05-20</span>
                            <div class="text-gray-500">
                                <i class="fas fa-eye mr-1"></i> 1,250
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-800/20 p-3 flex justify-between">
                        <div class="flex space-x-2">
                            <button class="text-gray-400 hover:text-white" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-gray-400 hover:text-white" title="预览">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-gray-400 hover:text-white" title="复制链接">
                                <i class="fas fa-link"></i>
                            </button>
                        </div>
                        <div class="flex space-x-2">
                            <button class="text-gray-400 hover:text-red-500" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 页面卡片 2 -->
                <div class="page-card bg-gray-800/10 border border-gray-700 rounded-xl overflow-hidden">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?auto=format&fit=crop&w=800&h=400" alt="联系我们" class="w-full h-40 object-cover">
                        <span class="badge badge-published absolute top-3 right-3">已发布</span>
                    </div>
                    <div class="p-4">
                        <h3 class="text-lg font-semibold mb-2">联系我们</h3>
                        <p class="text-gray-400 text-sm mb-3 line-clamp-2">企业联系方式、地址、联系表单和地图位置。</p>
                        <div class="flex justify-between items-center text-sm">
                            <span class="text-gray-500">更新于: 2025-05-18</span>
                            <div class="text-gray-500">
                                <i class="fas fa-eye mr-1"></i> 987
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-800/20 p-3 flex justify-between">
                        <div class="flex space-x-2">
                            <button class="text-gray-400 hover:text-white" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-gray-400 hover:text-white" title="预览">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-gray-400 hover:text-white" title="复制链接">
                                <i class="fas fa-link"></i>
                            </button>
                        </div>
                        <div class="flex space-x-2">
                            <button class="text-gray-400 hover:text-red-500" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 页面卡片 3 -->
                <div class="page-card bg-gray-800/10 border border-gray-700 rounded-xl overflow-hidden">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1576267423445-b2e0074d68a4?auto=format&fit=crop&w=800&h=400" alt="服务条款" class="w-full h-40 object-cover">
                        <span class="badge badge-published absolute top-3 right-3">已发布</span>
                    </div>
                    <div class="p-4">
                        <h3 class="text-lg font-semibold mb-2">服务条款</h3>
                        <p class="text-gray-400 text-sm mb-3 line-clamp-2">网站的使用条款、服务协议和法律声明。</p>
                        <div class="flex justify-between items-center text-sm">
                            <span class="text-gray-500">更新于: 2025-05-15</span>
                            <div class="text-gray-500">
                                <i class="fas fa-eye mr-1"></i> 456
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-800/20 p-3 flex justify-between">
                        <div class="flex space-x-2">
                            <button class="text-gray-400 hover:text-white" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-gray-400 hover:text-white" title="预览">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-gray-400 hover:text-white" title="复制链接">
                                <i class="fas fa-link"></i>
                            </button>
                        </div>
                        <div class="flex space-x-2">
                            <button class="text-gray-400 hover:text-red-500" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 页面卡片 4 -->
                <div class="page-card bg-gray-800/10 border border-gray-700 rounded-xl overflow-hidden">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1554224155-6726b3ff858f?auto=format&fit=crop&w=800&h=400" alt="隐私政策" class="w-full h-40 object-cover">
                        <span class="badge badge-published absolute top-3 right-3">已发布</span>
                    </div>
                    <div class="p-4">
                        <h3 class="text-lg font-semibold mb-2">隐私政策</h3>
                        <p class="text-gray-400 text-sm mb-3 line-clamp-2">用户隐私保护政策和数据收集使用说明。</p>
                        <div class="flex justify-between items-center text-sm">
                            <span class="text-gray-500">更新于: 2025-05-12</span>
                            <div class="text-gray-500">
                                <i class="fas fa-eye mr-1"></i> 382
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-800/20 p-3 flex justify-between">
                        <div class="flex space-x-2">
                            <button class="text-gray-400 hover:text-white" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-gray-400 hover:text-white" title="预览">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-gray-400 hover:text-white" title="复制链接">
                                <i class="fas fa-link"></i>
                            </button>
                        </div>
                        <div class="flex space-x-2">
                            <button class="text-gray-400 hover:text-red-500" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 页面卡片 5 -->
                <div class="page-card bg-gray-800/10 border border-gray-700 rounded-xl overflow-hidden">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?auto=format&fit=crop&w=800&h=400" alt="常见问题解答" class="w-full h-40 object-cover">
                        <span class="badge badge-draft absolute top-3 right-3">草稿</span>
                    </div>
                    <div class="p-4">
                        <h3 class="text-lg font-semibold mb-2">常见问题解答</h3>
                        <p class="text-gray-400 text-sm mb-3 line-clamp-2">用户常见问题解答和使用帮助指南。</p>
                        <div class="flex justify-between items-center text-sm">
                            <span class="text-gray-500">更新于: 2025-06-01</span>
                            <div class="text-gray-500">
                                <i class="fas fa-edit mr-1"></i> 草稿
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-800/20 p-3 flex justify-between">
                        <div class="flex space-x-2">
                            <button class="text-gray-400 hover:text-white" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-gray-400 hover:text-white" title="预览">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-gray-400 hover:text-white" title="发布">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                        <div class="flex space-x-2">
                            <button class="text-gray-400 hover:text-red-500" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 页面卡片 6 -->
                <div class="page-card bg-gray-800/10 border border-gray-700 rounded-xl overflow-hidden">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?auto=format&fit=crop&w=800&h=400" alt="团队介绍" class="w-full h-40 object-cover">
                        <span class="badge badge-scheduled absolute top-3 right-3">定时</span>
                    </div>
                    <div class="p-4">
                        <h3 class="text-lg font-semibold mb-2">团队介绍</h3>
                        <p class="text-gray-400 text-sm mb-3 line-clamp-2">核心团队成员介绍、专业背景和联系方式。</p>
                        <div class="flex justify-between items-center text-sm">
                            <span class="text-gray-500">发布于: 2025-06-15</span>
                            <div class="text-gray-500">
                                <i class="fas fa-clock mr-1"></i> 待发布
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-800/20 p-3 flex justify-between">
                        <div class="flex space-x-2">
                            <button class="text-gray-400 hover:text-white" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-gray-400 hover:text-white" title="预览">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-gray-400 hover:text-white" title="立即发布">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                        <div class="flex space-x-2">
                            <button class="text-gray-400 hover:text-red-500" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 分页控件 -->
            <div class="flex justify-between items-center mt-6 mb-6">
                <div class="text-sm text-gray-400">显示 1-6 项，共 14 项</div>
                <div class="flex space-x-1">
                    <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">上一页</button>
                    <button class="px-3 py-1 rounded bg-blue-600 text-white">1</button>
                    <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">2</button>
                    <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">3</button>
                    <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">下一页</button>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 其他初始化...
        });
    </script>
</body>
</html> 