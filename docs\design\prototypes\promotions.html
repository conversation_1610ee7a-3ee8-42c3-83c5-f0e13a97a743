<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 促销活动管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .promo-card {
            transition: all 0.3s ease;
        }
        
        .promo-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
        }
        
        .promo-item {
            transition: all 0.3s ease;
        }
        
        .promo-item:hover {
            background-color: rgba(75, 85, 99, 0.1);
        }
        
        /* 进度条样式 */
        .progress-bar {
            height: 6px;
            border-radius: 3px;
            overflow: hidden;
        }
        
        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        /* 日历样式 */
        .calendar-day {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .calendar-day:hover {
            background-color: rgba(59, 130, 246, 0.1);
        }
        
        .calendar-day.active {
            background-color: #3b82f6;
            color: white;
        }
        
        .calendar-day.has-event {
            position: relative;
        }
        
        .calendar-day.has-event::after {
            content: '';
            position: absolute;
            bottom: 2px;
            width: 4px;
            height: 4px;
            background-color: #3b82f6;
            border-radius: 50%;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="marketing.html" class="hover:text-white">营销</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">促销活动</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">促销活动管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="createPromoBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-plus text-white"></i>
                                </span>
                                创建促销
                            </span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 促销数据概览 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <!-- 活跃促销 -->
                <div class="promo-card bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-tag text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">活跃促销</h3>
                            <div class="text-2xl font-bold text-white mt-1">8</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-green-400">
                            <i class="fas fa-arrow-up mr-1"></i>2
                        </span>
                        <span class="text-gray-400 ml-2">较上月</span>
                    </div>
                </div>
                
                <!-- 总销售额 -->
                <div class="promo-card bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-shopping-cart text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">促销销售额</h3>
                            <div class="text-2xl font-bold text-white mt-1">¥128,459</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-green-400">
                            <i class="fas fa-arrow-up mr-1"></i>15.8%
                        </span>
                        <span class="text-gray-400 ml-2">较上月</span>
                    </div>
                </div>
                
                <!-- 优惠券使用 -->
                <div class="promo-card bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-ticket-alt text-yellow-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">优惠券使用</h3>
                            <div class="text-2xl font-bold text-white mt-1">2,845</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-green-400">
                            <i class="fas fa-arrow-up mr-1"></i>8.3%
                        </span>
                        <span class="text-gray-400 ml-2">较上周</span>
                    </div>
                </div>
                
                <!-- 转化率 -->
                <div class="promo-card bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-chart-line text-purple-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-400">平均转化率</h3>
                            <div class="text-2xl font-bold text-white mt-1">24.7%</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-red-400">
                            <i class="fas fa-arrow-down mr-1"></i>1.2%
                        </span>
                        <span class="text-gray-400 ml-2">较上月</span>
                    </div>
                </div>
            </div>
            
            <!-- 促销活动日历 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold text-white">促销活动日历</h3>
                    <div class="flex space-x-2">
                        <button class="p-2 bg-gray-700 rounded-lg hover:bg-gray-600">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <span class="py-2 px-4 bg-gray-700 rounded-lg">2025年4月</span>
                        <button class="p-2 bg-gray-700 rounded-lg hover:bg-gray-600">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 日历头部 -->
                <div class="grid grid-cols-7 gap-1 mb-2">
                    <div class="text-center text-gray-400 font-medium">周日</div>
                    <div class="text-center text-gray-400 font-medium">周一</div>
                    <div class="text-center text-gray-400 font-medium">周二</div>
                    <div class="text-center text-gray-400 font-medium">周三</div>
                    <div class="text-center text-gray-400 font-medium">周四</div>
                    <div class="text-center text-gray-400 font-medium">周五</div>
                    <div class="text-center text-gray-400 font-medium">周六</div>
                </div>
                
                <!-- 日历内容 -->
                <div class="grid grid-cols-7 gap-1">
                    <!-- 上个月的日期 -->
                    <div class="calendar-day text-gray-600">30</div>
                    <div class="calendar-day text-gray-600">31</div>
                    
                    <!-- 当月日期 -->
                    <div class="calendar-day">1</div>
                    <div class="calendar-day">2</div>
                    <div class="calendar-day">3</div>
                    <div class="calendar-day">4</div>
                    <div class="calendar-day">5</div>
                    <div class="calendar-day">6</div>
                    <div class="calendar-day">7</div>
                    <div class="calendar-day">8</div>
                    <div class="calendar-day has-event">9</div>
                    <div class="calendar-day">10</div>
                    <div class="calendar-day has-event">11</div>
                    <div class="calendar-day">12</div>
                    <div class="calendar-day">13</div>
                    <div class="calendar-day">14</div>
                    <div class="calendar-day active">15</div>
                    <div class="calendar-day has-event">16</div>
                    <div class="calendar-day">17</div>
                    <div class="calendar-day">18</div>
                    <div class="calendar-day">19</div>
                    <div class="calendar-day">20</div>
                    <div class="calendar-day">21</div>
                    <div class="calendar-day">22</div>
                    <div class="calendar-day">23</div>
                    <div class="calendar-day has-event">24</div>
                    <div class="calendar-day has-event">25</div>
                    <div class="calendar-day">26</div>
                    <div class="calendar-day">27</div>
                    <div class="calendar-day">28</div>
                    <div class="calendar-day">29</div>
                    <div class="calendar-day has-event">30</div>
                    
                    <!-- 下个月的日期 -->
                    <div class="calendar-day text-gray-600">1</div>
                    <div class="calendar-day text-gray-600">2</div>
                    <div class="calendar-day text-gray-600">3</div>
                </div>
                
                <!-- 当日活动 -->
                <div class="mt-6">
                    <h4 class="text-white font-medium mb-3">2025年4月15日活动</h4>
                    <div class="space-y-3">
                        <div class="flex items-center bg-gray-800/30 p-3 rounded-lg">
                            <div class="w-2 h-10 bg-green-500 rounded-full mr-3"></div>
                            <div class="flex-1">
                                <div class="text-white font-medium">春季特惠活动开始</div>
                                <div class="text-gray-400 text-sm">全场商品8折起</div>
                            </div>
                            <div class="text-gray-400 text-sm">09:00 - 23:59</div>
                        </div>
                        <div class="flex items-center bg-gray-800/30 p-3 rounded-lg">
                            <div class="w-2 h-10 bg-blue-500 rounded-full mr-3"></div>
                            <div class="flex-1">
                                <div class="text-white font-medium">新品发布优惠券</div>
                                <div class="text-gray-400 text-sm">满300减50</div>
                            </div>
                            <div class="text-gray-400 text-sm">全天</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 促销活动管理标签页 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="border-b border-gray-700 mb-6">
                    <ul class="flex flex-wrap -mb-px">
                        <li class="mr-2">
                            <a href="#" class="inline-block py-3 px-4 text-white border-b-2 border-blue-500 font-medium">全部促销</a>
                        </li>
                        <li class="mr-2">
                            <a href="#" class="inline-block py-3 px-4 text-gray-400 hover:text-white border-b-2 border-transparent hover:border-gray-400">折扣活动</a>
                        </li>
                        <li class="mr-2">
                            <a href="#" class="inline-block py-3 px-4 text-gray-400 hover:text-white border-b-2 border-transparent hover:border-gray-400">优惠券</a>
                        </li>
                        <li class="mr-2">
                            <a href="#" class="inline-block py-3 px-4 text-gray-400 hover:text-white border-b-2 border-transparent hover:border-gray-400">满减活动</a>
                        </li>
                        <li class="mr-2">
                            <a href="#" class="inline-block py-3 px-4 text-gray-400 hover:text-white border-b-2 border-transparent hover:border-gray-400">限时特价</a>
                        </li>
                    </ul>
                </div>
                
                <!-- 促销筛选 -->
                <div class="flex flex-wrap gap-4 mb-6">
                    <div class="relative">
                        <select class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all">所有状态</option>
                            <option value="active">进行中</option>
                            <option value="upcoming">即将开始</option>
                            <option value="ended">已结束</option>
                            <option value="draft">草稿</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </div>
                    </div>
                    
                    <div class="relative">
                        <select class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all">所有渠道</option>
                            <option value="online">线上商城</option>
                            <option value="offline">线下门店</option>
                            <option value="app">移动应用</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </div>
                    </div>
                    
                    <div class="flex-1"></div>
                    
                    <div class="relative">
                        <input type="text" placeholder="搜索促销活动" class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <div class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-400">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 促销列表 -->
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="py-3 text-left text-gray-400 font-medium">促销名称</th>
                                <th class="py-3 text-left text-gray-400 font-medium">类型</th>
                                <th class="py-3 text-left text-gray-400 font-medium">状态</th>
                                <th class="py-3 text-left text-gray-400 font-medium">时间范围</th>
                                <th class="py-3 text-left text-gray-400 font-medium">参与人数</th>
                                <th class="py-3 text-left text-gray-400 font-medium">转化率</th>
                                <th class="py-3 text-left text-gray-400 font-medium">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 促销项 1 -->
                            <tr class="promo-item border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded bg-green-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-percent text-green-500"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium">春季特惠活动</div>
                                            <div class="text-gray-400 text-sm">全场商品8折起</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-green-500/20 text-green-400">折扣活动</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-blue-500/20 text-blue-400">进行中</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">2025-04-15 ~ 2025-04-30</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-white">1,245</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <span class="text-white mr-2">32.5%</span>
                                        <div class="w-16 bg-gray-700 rounded-full h-1.5">
                                            <div class="bg-green-500 h-1.5 rounded-full" style="width: 32.5%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-white p-1" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 促销项 2 -->
                            <tr class="promo-item border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded bg-yellow-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-ticket-alt text-yellow-500"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium">新品发布优惠券</div>
                                            <div class="text-gray-400 text-sm">满300减50</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-yellow-500/20 text-yellow-400">优惠券</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-blue-500/20 text-blue-400">进行中</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">2025-04-15 ~ 2025-04-20</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-white">876</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <span class="text-white mr-2">28.3%</span>
                                        <div class="w-16 bg-gray-700 rounded-full h-1.5">
                                            <div class="bg-green-500 h-1.5 rounded-full" style="width: 28.3%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-white p-1" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 促销项 3 -->
                            <tr class="promo-item border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded bg-purple-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-gift text-purple-500"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium">满减活动</div>
                                            <div class="text-gray-400 text-sm">满1000减150</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-purple-500/20 text-purple-400">满减活动</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-yellow-500/20 text-yellow-400">即将开始</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">2025-04-24 ~ 2025-05-05</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-400">-</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-400">-</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-white p-1" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 促销项 4 -->
                            <tr class="promo-item border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded bg-red-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-clock text-red-500"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium">限时秒杀</div>
                                            <div class="text-gray-400 text-sm">精选商品5折起</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-red-500/20 text-red-400">限时特价</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-gray-500/20 text-gray-400">已结束</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">2025-04-09 ~ 2025-04-11</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-white">2,134</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <span class="text-white mr-2">45.2%</span>
                                        <div class="w-16 bg-gray-700 rounded-full h-1.5">
                                            <div class="bg-green-500 h-1.5 rounded-full" style="width: 45.2%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1" title="查看报告">
                                            <i class="fas fa-chart-bar"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-white p-1" title="复制活动">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 促销项 5 -->
                            <tr class="promo-item border-b border-gray-700">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded bg-blue-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-users text-blue-500"></i>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium">会员专享折扣</div>
                                            <div class="text-gray-400 text-sm">VIP会员额外95折</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-blue-500/20 text-blue-400">会员活动</span>
                                </td>
                                <td class="py-3">
                                    <span class="status-badge bg-blue-500/20 text-blue-400">进行中</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-300">2025-04-01 ~ 2025-04-30</span>
                                </td>
                                <td class="py-3">
                                    <span class="text-white">543</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <span class="text-white mr-2">18.7%</span>
                                        <div class="w-16 bg-gray-700 rounded-full h-1.5">
                                            <div class="bg-green-500 h-1.5 rounded-full" style="width: 18.7%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white p-1" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-white p-1" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-500 p-1" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div class="flex justify-between items-center mt-6">
                    <div class="text-sm text-gray-400">
                        显示 1-5 条，共 12 条
                    </div>
                    <div class="flex space-x-1">
                        <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="px-3 py-1 bg-blue-500 text-white rounded-md">
                            1
                        </button>
                        <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                            2
                        </button>
                        <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                            3
                        </button>
                        <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 页面底部版权信息 -->
            <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
                © 2025 GACMS 后台管理系统 - 版本 v1.0.0
            </footer>
        </div>
    </main>

    <!-- 创建促销模态框 -->
    <div id="createPromoModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 w-full max-w-2xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-white">创建促销活动</h3>
                <button id="closeModalBtn" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form>
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">促销名称</label>
                    <input type="text" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="输入促销活动名称">
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-gray-300 mb-2">促销类型</label>
                        <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="discount">折扣活动</option>
                            <option value="coupon">优惠券</option>
                            <option value="fullminus">满减活动</option>
                            <option value="flash">限时特价</option>
                            <option value="member">会员活动</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-gray-300 mb-2">适用渠道</label>
                        <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all">全部渠道</option>
                            <option value="online">线上商城</option>
                            <option value="offline">线下门店</option>
                            <option value="app">移动应用</option>
                        </select>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-gray-300 mb-2">开始时间</label>
                        <input type="datetime-local" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-gray-300 mb-2">结束时间</label>
                        <input type="datetime-local" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">促销规则</label>
                    <textarea class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3" placeholder="输入促销规则描述"></textarea>
                </div>
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">适用商品</label>
                    <div class="flex">
                        <select class="flex-1 bg-gray-700 border border-gray-600 rounded-l-lg px-3 py-2 text-white appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all">全部商品</option>
                            <option value="category">指定分类</option>
                            <option value="product">指定商品</option>
                        </select>
                        <button type="button" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-r-lg">
                            选择
                        </button>
                    </div>
                </div>
                <div class="mb-4 flex items-center">
                    <input type="checkbox" id="enablePromo" class="mr-2" checked>
                    <label for="enablePromo" class="text-gray-300">立即启用</label>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancelBtn" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg">取消</button>
                    <button type="button" class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 必要的JS脚本 -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签页切换
            const tabLinks = document.querySelectorAll('.border-b ul li a');
            
            tabLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 移除所有标签的活动状态
                    tabLinks.forEach(tab => {
                        tab.classList.remove('text-white', 'border-blue-500');
                        tab.classList.add('text-gray-400', 'border-transparent');
                    });
                    
                    // 设置当前标签为活动状态
                    this.classList.remove('text-gray-400', 'border-transparent');
                    this.classList.add('text-white', 'border-blue-500');
                });
            });
            
            // 日历日期点击
            const calendarDays = document.querySelectorAll('.calendar-day');
            
            calendarDays.forEach(day => {
                if (!day.classList.contains('text-gray-600')) { // 排除上个月和下个月的日期
                    day.addEventListener('click', function() {
                        // 移除所有日期的活动状态
                        calendarDays.forEach(d => d.classList.remove('active'));
                        
                        // 设置当前日期为活动状态
                        this.classList.add('active');
                    });
                }
            });
            
            // 模态框控制
            const createPromoBtn = document.getElementById('createPromoBtn');
            const createPromoModal = document.getElementById('createPromoModal');
            const closeModalBtn = document.getElementById('closeModalBtn');
            const cancelBtn = document.getElementById('cancelBtn');
            
            function openModal() {
                createPromoModal.classList.remove('hidden');
            }
            
            function closeModal() {
                createPromoModal.classList.add('hidden');
            }
            
            createPromoBtn.addEventListener('click', openModal);
            closeModalBtn.addEventListener('click', closeModal);
            cancelBtn.addEventListener('click', closeModal);
            
            // 点击模态框外部关闭
            createPromoModal.addEventListener('click', function(e) {
                if (e.target === createPromoModal) {
                    closeModal();
                }
            });
        });
    </script>
</body>
</html>