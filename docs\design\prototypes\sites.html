<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 多站点管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .site-card {
            transition: all 0.3s ease;
        }
        
        .site-card:hover {
            transform: translateY(-5px);
        }
        
        .site-status {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
        }
        
        .site-status.online {
            background-color: #10B981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
        }
        
        .site-status.offline {
            background-color: #EF4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
        }
        
        .site-status.maintenance {
            background-color: #F59E0B;
            box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.2);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">多站点管理</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">多站点管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="addSiteBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <i class="fas fa-plus mr-2"></i>
                            添加站点
                        </button>
                    </div>
                </div>
            </div>

            <!-- 多站点概览 -->
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-6">
                <!-- 总站点数 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-globe text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">总站点数</div>
                            <div class="text-xl font-semibold text-white">12</div>
                            <div class="text-xs text-blue-400 mt-0.5">本月新增: 1</div>
                        </div>
                    </div>
                </div>
                
                <!-- 在线站点 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-check-circle text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">在线站点</div>
                            <div class="text-xl font-semibold text-white">10</div>
                            <div class="text-xs text-green-400 mt-0.5">运行正常</div>
                        </div>
                    </div>
                </div>
                
                <!-- 问题站点 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-exclamation-triangle text-yellow-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">问题站点</div>
                            <div class="text-xl font-semibold text-white">1</div>
                            <div class="text-xs text-yellow-400 mt-0.5">需要维护</div>
                        </div>
                    </div>
                </div>
                
                <!-- 离线站点 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-red-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-times-circle text-red-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">离线站点</div>
                            <div class="text-xl font-semibold text-white">1</div>
                            <div class="text-xs text-red-400 mt-0.5">需要检查</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 站点筛选区 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap gap-4">
                    <div class="relative flex-grow sm:flex-grow-0">
                        <select class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white appearance-none pr-10 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all" selected>所有站点</option>
                            <option value="online">在线站点</option>
                            <option value="maintenance">维护站点</option>
                            <option value="offline">离线站点</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </div>
                    </div>
                    
                    <div class="relative flex-grow">
                        <input type="text" placeholder="搜索站点..." class="bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2 w-full text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                    
                    <div class="flex-shrink-0">
                        <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 px-4 py-2 rounded-lg transition-colors flex items-center">
                            <i class="fas fa-filter mr-2"></i> 筛选
                        </button>
                    </div>
                </div>
            </div>

            <!-- 站点列表 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                <!-- 站点 1 -->
                <div class="site-card bg-gray-800/10 border border-gray-700 rounded-xl overflow-hidden relative">
                    <div class="site-status online" title="在线"></div>
                    <img src="https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&h=200&q=80" alt="Main Company Website" class="w-full h-36 object-cover">
                    
                    <div class="p-5">
                        <h3 class="text-lg font-semibold text-white mb-1">主公司网站</h3>
                        <p class="text-gray-400 text-sm mb-3">https://example.com</p>
                        
                        <div class="space-y-2 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">服务器:</span>
                                <span class="text-white">AWS EC2</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">版本:</span>
                                <span class="text-white">GACMS v1.5.2</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">访问量:</span>
                                <span class="text-white">12,450 / 月</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">最后更新:</span>
                                <span class="text-white">2025-06-01</span>
                            </div>
                        </div>
                        
                        <div class="flex flex-wrap gap-2">
                            <a href="https://example.com" target="_blank" class="px-3 py-1.5 bg-blue-500/20 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-colors">
                                <i class="fas fa-external-link-alt mr-1"></i> 访问
                            </a>
                            <button class="px-3 py-1.5 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors">
                                <i class="fas fa-cog mr-1"></i> 管理
                            </button>
                            <button class="px-3 py-1.5 bg-purple-500/20 text-purple-400 rounded-lg hover:bg-purple-500/30 transition-colors">
                                <i class="fas fa-sync-alt mr-1"></i> 同步
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 站点 2 -->
                <div class="site-card bg-gray-800/10 border border-gray-700 rounded-xl overflow-hidden relative">
                    <div class="site-status online" title="在线"></div>
                    <img src="https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&h=200&q=80" alt="Blog Website" class="w-full h-36 object-cover">
                    
                    <div class="p-5">
                        <h3 class="text-lg font-semibold text-white mb-1">公司博客</h3>
                        <p class="text-gray-400 text-sm mb-3">https://blog.example.com</p>
                        
                        <div class="space-y-2 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">服务器:</span>
                                <span class="text-white">AWS EC2</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">版本:</span>
                                <span class="text-white">GACMS v1.5.2</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">访问量:</span>
                                <span class="text-white">8,320 / 月</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">最后更新:</span>
                                <span class="text-white">2025-05-28</span>
                            </div>
                        </div>
                        
                        <div class="flex flex-wrap gap-2">
                            <a href="https://blog.example.com" target="_blank" class="px-3 py-1.5 bg-blue-500/20 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-colors">
                                <i class="fas fa-external-link-alt mr-1"></i> 访问
                            </a>
                            <button class="px-3 py-1.5 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors">
                                <i class="fas fa-cog mr-1"></i> 管理
                            </button>
                            <button class="px-3 py-1.5 bg-purple-500/20 text-purple-400 rounded-lg hover:bg-purple-500/30 transition-colors">
                                <i class="fas fa-sync-alt mr-1"></i> 同步
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 站点 3 -->
                <div class="site-card bg-gray-800/10 border border-gray-700 rounded-xl overflow-hidden relative">
                    <div class="site-status online" title="在线"></div>
                    <img src="https://images.unsplash.com/photo-1577563908411-5077b6dc7624?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&h=200&q=80" alt="E-commerce Site" class="w-full h-36 object-cover">
                    
                    <div class="p-5">
                        <h3 class="text-lg font-semibold text-white mb-1">产品展示</h3>
                        <p class="text-gray-400 text-sm mb-3">https://products.example.com</p>
                        
                        <div class="space-y-2 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">服务器:</span>
                                <span class="text-white">阿里云 ECS</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">版本:</span>
                                <span class="text-white">GACMS v1.5.0</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">访问量:</span>
                                <span class="text-white">5,890 / 月</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">最后更新:</span>
                                <span class="text-white">2025-05-15</span>
                            </div>
                        </div>
                        
                        <div class="flex flex-wrap gap-2">
                            <a href="https://products.example.com" target="_blank" class="px-3 py-1.5 bg-blue-500/20 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-colors">
                                <i class="fas fa-external-link-alt mr-1"></i> 访问
                            </a>
                            <button class="px-3 py-1.5 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors">
                                <i class="fas fa-cog mr-1"></i> 管理
                            </button>
                            <button class="px-3 py-1.5 bg-purple-500/20 text-purple-400 rounded-lg hover:bg-purple-500/30 transition-colors">
                                <i class="fas fa-sync-alt mr-1"></i> 同步
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 站点 4 (维护中) -->
                <div class="site-card bg-gray-800/10 border border-gray-700 rounded-xl overflow-hidden relative">
                    <div class="site-status maintenance" title="维护中"></div>
                    <img src="https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&h=200&q=80" alt="Support Portal" class="w-full h-36 object-cover">
                    
                    <div class="p-5">
                        <h3 class="text-lg font-semibold text-white mb-1">客户支持门户</h3>
                        <p class="text-gray-400 text-sm mb-3">https://support.example.com</p>
                        
                        <div class="space-y-2 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">服务器:</span>
                                <span class="text-white">AWS EC2</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">版本:</span>
                                <span class="text-white">GACMS v1.4.8</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">访问量:</span>
                                <span class="text-white">3,210 / 月</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">状态:</span>
                                <span class="text-yellow-400">计划维护中</span>
                            </div>
                        </div>
                        
                        <div class="flex flex-wrap gap-2">
                            <a href="https://support.example.com" target="_blank" class="px-3 py-1.5 bg-blue-500/20 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-colors">
                                <i class="fas fa-external-link-alt mr-1"></i> 访问
                            </a>
                            <button class="px-3 py-1.5 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors">
                                <i class="fas fa-cog mr-1"></i> 管理
                            </button>
                            <button class="px-3 py-1.5 bg-yellow-500/20 text-yellow-400 rounded-lg hover:bg-yellow-500/30 transition-colors">
                                <i class="fas fa-tools mr-1"></i> 维护
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 站点 5 (离线) -->
                <div class="site-card bg-gray-800/10 border border-gray-700 rounded-xl overflow-hidden relative">
                    <div class="site-status offline" title="离线"></div>
                    <img src="https://images.unsplash.com/photo-1519389950473-47ba0277781c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&h=200&q=80" alt="Dev Portal" class="w-full h-36 object-cover grayscale">
                    
                    <div class="p-5">
                        <h3 class="text-lg font-semibold text-white mb-1">开发者门户</h3>
                        <p class="text-gray-400 text-sm mb-3">https://dev.example.com</p>
                        
                        <div class="space-y-2 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">服务器:</span>
                                <span class="text-white">腾讯云 CVM</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">版本:</span>
                                <span class="text-white">GACMS v1.4.5</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">访问量:</span>
                                <span class="text-white">1,280 / 月</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">状态:</span>
                                <span class="text-red-400">离线 (12小时)</span>
                            </div>
                        </div>
                        
                        <div class="flex flex-wrap gap-2">
                            <button class="px-3 py-1.5 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors">
                                <i class="fas fa-cog mr-1"></i> 管理
                            </button>
                            <button class="px-3 py-1.5 bg-red-500/20 text-red-400 rounded-lg hover:bg-red-500/30 transition-colors">
                                <i class="fas fa-power-off mr-1"></i> 重启
                            </button>
                            <button class="px-3 py-1.5 bg-purple-500/20 text-purple-400 rounded-lg hover:bg-purple-500/30 transition-colors">
                                <i class="fas fa-sync-alt mr-1"></i> 检查
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 站点 6 -->
                <div class="site-card bg-gray-800/10 border border-gray-700 rounded-xl overflow-hidden relative">
                    <div class="site-status online" title="在线"></div>
                    <img src="https://images.unsplash.com/photo-1600267175161-cfaa711b4a81?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&h=200&q=80" alt="Event Site" class="w-full h-36 object-cover">
                    
                    <div class="p-5">
                        <h3 class="text-lg font-semibold text-white mb-1">活动网站</h3>
                        <p class="text-gray-400 text-sm mb-3">https://events.example.com</p>
                        
                        <div class="space-y-2 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">服务器:</span>
                                <span class="text-white">AWS EC2</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">版本:</span>
                                <span class="text-white">GACMS v1.5.2</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">访问量:</span>
                                <span class="text-white">4,780 / 月</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">最后更新:</span>
                                <span class="text-white">2025-06-03</span>
                            </div>
                        </div>
                        
                        <div class="flex flex-wrap gap-2">
                            <a href="https://events.example.com" target="_blank" class="px-3 py-1.5 bg-blue-500/20 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-colors">
                                <i class="fas fa-external-link-alt mr-1"></i> 访问
                            </a>
                            <button class="px-3 py-1.5 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors">
                                <i class="fas fa-cog mr-1"></i> 管理
                            </button>
                            <button class="px-3 py-1.5 bg-purple-500/20 text-purple-400 rounded-lg hover:bg-purple-500/30 transition-colors">
                                <i class="fas fa-sync-alt mr-1"></i> 同步
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 分页 -->
            <div class="flex justify-between items-center mb-6">
                <div class="text-sm text-gray-400">
                    显示 <span class="text-white">1-6</span> 条，共 <span class="text-white">12</span> 条
                </div>
                <div class="flex space-x-1">
                    <a href="#" class="px-3 py-1 rounded border border-gray-700 bg-gray-800/20 text-gray-400 hover:bg-gray-800/40 hover:text-white transition-colors">
                        <i class="fas fa-chevron-left text-xs"></i>
                    </a>
                    <a href="#" class="px-3 py-1 rounded border border-blue-500 bg-blue-500/20 text-white">1</a>
                    <a href="#" class="px-3 py-1 rounded border border-gray-700 bg-gray-800/20 text-gray-400 hover:bg-gray-800/40 hover:text-white transition-colors">2</a>
                    <a href="#" class="px-3 py-1 rounded border border-gray-700 bg-gray-800/20 text-gray-400 hover:bg-gray-800/40 hover:text-white transition-colors">
                        <i class="fas fa-chevron-right text-xs"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <!-- 添加站点模态框 (默认隐藏) -->
    <div id="addSiteModal" class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 hidden">
        <div class="bg-gray-800 rounded-xl p-6 w-full max-w-lg">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold text-white">添加新站点</h3>
                <button id="closeModal" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <form>
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">站点名称</label>
                    <input type="text" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">站点URL</label>
                    <input type="url" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="mb-4">
                    <label class="block text-gray-300 mb-2">描述</label>
                    <textarea class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500" rows="3"></textarea>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-gray-300 mb-2">服务器</label>
                        <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500">
                            <option value="">选择服务器</option>
                            <option value="aws">AWS EC2</option>
                            <option value="aliyun">阿里云 ECS</option>
                            <option value="tencent">腾讯云 CVM</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-gray-300 mb-2">GACMS版本</label>
                        <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500">
                            <option value="">选择版本</option>
                            <option value="1.5.2">v1.5.2 (最新)</option>
                            <option value="1.5.0">v1.5.0</option>
                            <option value="1.4.8">v1.4.8</option>
                            <option value="1.4.5">v1.4.5</option>
                        </select>
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" id="cancelAddSite" class="px-5 py-2.5 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg transition-colors">
                        取消
                    </button>
                    <button type="submit" class="px-5 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:shadow-lg hover:shadow-blue-500/30 transition-all">
                        添加站点
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 模态框控制
            const addSiteBtn = document.getElementById('addSiteBtn');
            const addSiteModal = document.getElementById('addSiteModal');
            const closeModal = document.getElementById('closeModal');
            const cancelAddSite = document.getElementById('cancelAddSite');
            
            // 打开模态框
            addSiteBtn.addEventListener('click', function() {
                addSiteModal.classList.remove('hidden');
            });
            
            // 关闭模态框
            const closeModalFn = function() {
                addSiteModal.classList.add('hidden');
            };
            
            closeModal.addEventListener('click', closeModalFn);
            cancelAddSite.addEventListener('click', closeModalFn);
            
            // 点击模态框外部关闭
            addSiteModal.addEventListener('click', function(e) {
                if (e.target === addSiteModal) {
                    closeModalFn();
                }
            });
        });
    </script>
</body>
</html> 