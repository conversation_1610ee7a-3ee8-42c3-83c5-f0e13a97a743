/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/LicenseStore.go
 * @Description: 许可证存储实现，通过公共库数据库接口存储许可证信息
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gacms/internal/infrastructure/database"
	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// DefaultLicenseStore 默认许可证存储
type DefaultLicenseStore struct {
	db     contract.Database
	logger *zap.Logger
}

// DefaultLicenseStoreParams 许可证存储参数
type DefaultLicenseStoreParams struct {
	fx.In

	Database contract.Database
	Logger   *zap.Logger
}

// NewDefaultLicenseStore 创建默认许可证存储
func NewDefaultLicenseStore(params DefaultLicenseStoreParams) contract.LicenseStore {
	return &DefaultLicenseStore{
		db:     params.Database,
		logger: params.Logger,
	}
}

// SaveLicenseInfo 保存许可证信息（多租户支持）
func (s *DefaultLicenseStore) SaveLicenseInfo(ctx context.Context, moduleName string, info *contract.LicenseInfo) error {
	// 从上下文获取站点ID
	siteID, ok := s.getSiteIDFromContext(ctx)
	if !ok {
		return fmt.Errorf("site ID not found in context")
	}

	s.logger.Debug("Saving license info",
		zap.Uint("site_id", siteID),
		zap.String("module", moduleName),
	)

	// 序列化许可证信息
	infoJSON, err := json.Marshal(info)
	if err != nil {
		return fmt.Errorf("failed to marshal license info: %w", err)
	}

	// 构建查询参数（多租户）
	query := `
		INSERT INTO module_licenses (site_id, module_name, license_key, license_type, license_info, is_valid, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)
		ON DUPLICATE KEY UPDATE
		license_key = VALUES(license_key),
		license_type = VALUES(license_type),
		license_info = VALUES(license_info),
		is_valid = VALUES(is_valid),
		updated_at = VALUES(updated_at)
	`

	now := time.Now()
	params := []interface{}{
		siteID,
		moduleName,
		info.Key,
		info.Type,
		string(infoJSON),
		info.IsValid,
		now,
		now,
	}

	// 执行数据库操作
	if err := s.db.Exec(ctx, query, params...); err != nil {
		return fmt.Errorf("failed to save license info: %w", err)
	}

	s.logger.Debug("License info saved successfully",
		zap.Uint("site_id", siteID),
		zap.String("module", moduleName),
	)
	return nil
}

// GetLicenseInfo 获取许可证信息（多租户支持）
func (s *DefaultLicenseStore) GetLicenseInfo(ctx context.Context, moduleName string) (*contract.LicenseInfo, error) {
	// 从上下文获取站点ID
	siteID, ok := s.getSiteIDFromContext(ctx)
	if !ok {
		return nil, fmt.Errorf("site ID not found in context")
	}

	s.logger.Debug("Getting license info",
		zap.Uint("site_id", siteID),
		zap.String("module", moduleName),
	)

	query := `
		SELECT license_key, license_type, license_info, is_valid, updated_at
		FROM module_licenses
		WHERE site_id = ? AND module_name = ?
	`

	var licenseKey, licenseType, licenseInfoJSON string
	var isValid bool
	var updatedAt time.Time

	err := s.db.QueryRow(ctx, query, siteID, moduleName).Scan(
		&licenseKey,
		&licenseType,
		&licenseInfoJSON,
		&isValid,
		&updatedAt,
	)

	if err != nil {
		if s.db.IsNoRowsError(err) {
			return nil, nil // 没有找到记录
		}
		return nil, fmt.Errorf("failed to get license info: %w", err)
	}

	// 反序列化许可证信息
	var licenseInfo contract.LicenseInfo
	if err := json.Unmarshal([]byte(licenseInfoJSON), &licenseInfo); err != nil {
		return nil, fmt.Errorf("failed to unmarshal license info: %w", err)
	}

	s.logger.Debug("License info retrieved successfully",
		zap.Uint("site_id", siteID),
		zap.String("module", moduleName),
	)
	return &licenseInfo, nil
}

// DeleteLicenseInfo 删除许可证信息（多租户支持）
func (s *DefaultLicenseStore) DeleteLicenseInfo(ctx context.Context, moduleName string) error {
	// 从上下文获取站点ID
	siteID, ok := s.getSiteIDFromContext(ctx)
	if !ok {
		return fmt.Errorf("site ID not found in context")
	}

	s.logger.Debug("Deleting license info",
		zap.Uint("site_id", siteID),
		zap.String("module", moduleName),
	)

	query := `DELETE FROM module_licenses WHERE site_id = ? AND module_name = ?`

	if err := s.db.Exec(ctx, query, siteID, moduleName); err != nil {
		return fmt.Errorf("failed to delete license info: %w", err)
	}

	s.logger.Debug("License info deleted successfully",
		zap.Uint("site_id", siteID),
		zap.String("module", moduleName),
	)
	return nil
}

// ListLicensedModules 列出已授权的模块（多租户支持）
func (s *DefaultLicenseStore) ListLicensedModules(ctx context.Context) ([]string, error) {
	// 从上下文获取站点ID
	siteID, ok := s.getSiteIDFromContext(ctx)
	if !ok {
		return nil, fmt.Errorf("site ID not found in context")
	}

	s.logger.Debug("Listing licensed modules", zap.Uint("site_id", siteID))

	query := `
		SELECT module_name
		FROM module_licenses
		WHERE site_id = ? AND is_valid = true
		ORDER BY module_name
	`

	rows, err := s.db.Query(ctx, query, siteID)
	if err != nil {
		return nil, fmt.Errorf("failed to list licensed modules: %w", err)
	}
	defer rows.Close()

	var modules []string
	for rows.Next() {
		var moduleName string
		if err := rows.Scan(&moduleName); err != nil {
			return nil, fmt.Errorf("failed to scan module name: %w", err)
		}
		modules = append(modules, moduleName)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	s.logger.Debug("Licensed modules listed successfully",
		zap.Uint("site_id", siteID),
		zap.Int("count", len(modules)),
	)
	return modules, nil
}

// SaveLicenseUsage 保存许可证使用记录（多租户支持）
func (s *DefaultLicenseStore) SaveLicenseUsage(ctx context.Context, usage *contract.LicenseUsage) error {
	// 从上下文获取站点ID
	siteID, ok := s.getSiteIDFromContext(ctx)
	if !ok {
		return fmt.Errorf("site ID not found in context")
	}

	s.logger.Debug("Saving license usage",
		zap.Uint("site_id", siteID),
		zap.String("license_key", usage.LicenseKey[:min(8, len(usage.LicenseKey))]+"..."),
		zap.String("module", usage.ModuleName),
	)

	query := `
		INSERT INTO license_usage (site_id, license_key, module_name, activated_at, last_used_at, usage_count, max_usage, is_active, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		ON DUPLICATE KEY UPDATE
		last_used_at = VALUES(last_used_at),
		usage_count = VALUES(usage_count),
		is_active = VALUES(is_active),
		updated_at = VALUES(updated_at)
	`

	now := time.Now()
	params := []interface{}{
		siteID,
		usage.LicenseKey,
		usage.ModuleName,
		usage.ActivatedAt,
		usage.LastUsedAt,
		usage.UsageCount,
		usage.MaxUsage,
		usage.IsActive,
		now,
		now,
	}

	if err := s.db.Exec(ctx, query, params...); err != nil {
		return fmt.Errorf("failed to save license usage: %w", err)
	}

	s.logger.Debug("License usage saved successfully",
		zap.Uint("site_id", siteID),
		zap.String("license_key", usage.LicenseKey[:min(8, len(usage.LicenseKey))]+"..."),
		zap.String("module", usage.ModuleName),
	)
	return nil
}

// GetLicenseUsage 获取许可证使用记录（多租户支持）
func (s *DefaultLicenseStore) GetLicenseUsage(ctx context.Context, licenseKey, moduleName string) (*contract.LicenseUsage, error) {
	// 从上下文获取站点ID
	siteID, ok := s.getSiteIDFromContext(ctx)
	if !ok {
		return nil, fmt.Errorf("site ID not found in context")
	}

	s.logger.Debug("Getting license usage",
		zap.Uint("site_id", siteID),
		zap.String("license_key", licenseKey[:min(8, len(licenseKey))]+"..."),
		zap.String("module", moduleName),
	)

	query := `
		SELECT license_key, module_name, activated_at, last_used_at, usage_count, max_usage, is_active
		FROM license_usage
		WHERE site_id = ? AND license_key = ? AND module_name = ?
	`

	var usage contract.LicenseUsage
	err := s.db.QueryRow(ctx, query, siteID, licenseKey, moduleName).Scan(
		&usage.LicenseKey,
		&usage.ModuleName,
		&usage.ActivatedAt,
		&usage.LastUsedAt,
		&usage.UsageCount,
		&usage.MaxUsage,
		&usage.IsActive,
	)

	if err != nil {
		if s.db.IsNoRowsError(err) {
			return nil, nil // 没有找到记录
		}
		return nil, fmt.Errorf("failed to get license usage: %w", err)
	}

	s.logger.Debug("License usage retrieved successfully",
		zap.Uint("site_id", siteID),
		zap.String("license_key", licenseKey[:min(8, len(licenseKey))]+"..."),
		zap.String("module", moduleName),
	)
	return &usage, nil
}

// CreateTables 创建许可证相关表
func (s *DefaultLicenseStore) CreateTables(ctx context.Context) error {
	s.logger.Info("Creating license tables")

	// 创建模块许可证表（多租户支持）
	licensesTable := `
		CREATE TABLE IF NOT EXISTS module_licenses (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			site_id INT UNSIGNED NOT NULL,
			module_name VARCHAR(100) NOT NULL,
			license_key VARCHAR(500) NOT NULL,
			license_type VARCHAR(50) NOT NULL DEFAULT 'official',
			license_info JSON,
			is_valid BOOLEAN NOT NULL DEFAULT false,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			UNIQUE KEY uk_site_module (site_id, module_name),
			INDEX idx_site_id (site_id),
			INDEX idx_module_name (module_name),
			INDEX idx_license_type (license_type),
			INDEX idx_is_valid (is_valid),
			FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
	`

	if err := s.db.Exec(ctx, licensesTable); err != nil {
		return fmt.Errorf("failed to create module_licenses table: %w", err)
	}

	// 创建许可证使用记录表（多租户支持）
	usageTable := `
		CREATE TABLE IF NOT EXISTS license_usage (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			site_id INT UNSIGNED NOT NULL,
			license_key VARCHAR(500) NOT NULL,
			module_name VARCHAR(100) NOT NULL,
			activated_at TIMESTAMP NOT NULL,
			last_used_at TIMESTAMP NOT NULL,
			usage_count BIGINT NOT NULL DEFAULT 0,
			max_usage BIGINT DEFAULT NULL,
			is_active BOOLEAN NOT NULL DEFAULT true,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			UNIQUE KEY uk_site_license_module (site_id, license_key, module_name),
			INDEX idx_site_id (site_id),
			INDEX idx_license_key (license_key),
			INDEX idx_module_name (module_name),
			INDEX idx_is_active (is_active),
			INDEX idx_last_used_at (last_used_at),
			FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
	`

	if err := s.db.Exec(ctx, usageTable); err != nil {
		return fmt.Errorf("failed to create license_usage table: %w", err)
	}

	s.logger.Info("License tables created successfully")
	return nil
}

// getSiteIDFromContext 从上下文获取站点ID
func (s *DefaultLicenseStore) getSiteIDFromContext(ctx context.Context) (uint, bool) {
	return database.SiteIDFrom(ctx)
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
