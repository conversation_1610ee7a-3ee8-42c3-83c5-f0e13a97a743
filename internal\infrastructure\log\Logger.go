/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/infrastructure/log/Logger.go
 * @Description: 日志记录器
 * 
 * © 2025 GACMS. All rights reserved.
 */

package log

import (
	"os"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// Config 日志配置
type Config struct {
	Level      string `json:"level"`
	Encoding   string `json:"encoding"`
	OutputPath string `json:"output_path"`
	Debug      bool   `json:"debug"`
}

// NewLogger 创建一个新的日志记录器
func NewLogger() (*zap.Logger, error) {
	// 在实际应用中，应该从配置文件或环境变量中读取配置
	config := &Config{
		Level:      "debug",
		Encoding:   "console",
		OutputPath: "stdout",
		Debug:      true,
	}

	// 创建 Encoder 配置
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		Caller<PERSON>ey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.StringDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 设置日志级别
	var level zapcore.Level
	switch config.Level {
	case "debug":
		level = zapcore.DebugLevel
	case "info":
		level = zapcore.InfoLevel
	case "warn":
		level = zapcore.WarnLevel
	case "error":
		level = zapcore.ErrorLevel
	default:
		level = zapcore.InfoLevel
	}

	// 创建 Core
	var core zapcore.Core
	if config.Encoding == "json" {
		core = zapcore.NewCore(
			zapcore.NewJSONEncoder(encoderConfig),
			zapcore.AddSync(os.Stdout),
			level,
		)
	} else {
		core = zapcore.NewCore(
			zapcore.NewConsoleEncoder(encoderConfig),
			zapcore.AddSync(os.Stdout),
			level,
		)
	}

	// 创建 Logger
	logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	// 如果开启调试模式，添加开发模式配置
	if config.Debug {
		logger = logger.WithOptions(zap.Development())
	}

	return logger, nil
}