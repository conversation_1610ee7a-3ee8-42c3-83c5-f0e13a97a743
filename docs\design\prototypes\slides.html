<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 焦点图管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: { 500: '#007bff', 600: '#0069d9' },
                        secondary: { 500: '#00c6ff' },
                        dark: { 800: '#1F2937', 900: '#1A1A1A' }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>

        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">焦点图管理</h2>
                    <button class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-2 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30">
                        <i class="fas fa-plus mr-2"></i>添加焦点图
                    </button>
                </div>
            </div>

            <!-- 焦点图列表 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="overflow-x-auto">
                    <table class="w-full text-sm text-left text-gray-400">
                        <thead class="text-xs text-gray-300 uppercase bg-gray-700/20">
                            <tr>
                                <th scope="col" class="px-6 py-3">排序</th>
                                <th scope="col" class="px-6 py-3">图片</th>
                                <th scope="col" class="px-6 py-3">标题</th>
                                <th scope="col" class="px-6 py-3">链接</th>
                                <th scope="col" class="px-6 py-3">状态</th>
                                <th scope="col" class="px-6 py-3">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="px-6 py-4">
                                    <div class="flex items-center gap-2">
                                        <button class="text-gray-400 hover:text-white"><i class="fas fa-arrow-up"></i></button>
                                        <span>1</span>
                                        <button class="text-gray-400 hover:text-white"><i class="fas fa-arrow-down"></i></button>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <img src="./assets/images/slider-1.jpg" alt="焦点图1" class="h-12 w-24 object-cover rounded-md">
                                </td>
                                <td class="px-6 py-4 font-medium text-white">探索GACMS的强大功能</td>
                                <td class="px-6 py-4">/features</td>
                                <td class="px-6 py-4 text-green-400">显示</td>
                                <td class="px-6 py-4">
                                    <a href="#" class="font-medium text-blue-500 hover:underline mr-3">编辑</a>
                                    <a href="#" class="font-medium text-red-500 hover:underline">删除</a>
                                </td>
                            </tr>
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="px-6 py-4">
                                    <div class="flex items-center gap-2">
                                        <button class="text-gray-400 hover:text-white"><i class="fas fa-arrow-up"></i></button>
                                        <span>2</span>
                                        <button class="text-gray-400 hover:text-white"><i class="fas fa-arrow-down"></i></button>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <img src="./assets/images/slider-2.jpg" alt="焦点图2" class="h-12 w-24 object-cover rounded-md">
                                </td>
                                <td class="px-6 py-4 font-medium text-white">全新的主题市场</td>
                                <td class="px-6 py-4">/themes</td>
                                <td class="px-6 py-4 text-green-400">显示</td>
                                <td class="px-6 py-4">
                                    <a href="#" class="font-medium text-blue-500 hover:underline mr-3">编辑</a>
                                    <a href="#" class="font-medium text-red-500 hover:underline">删除</a>
                                </td>
                            </tr>
                             <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="px-6 py-4">
                                     <div class="flex items-center gap-2">
                                        <button class="text-gray-400 hover:text-white"><i class="fas fa-arrow-up"></i></button>
                                        <span>3</span>
                                        <button class="text-gray-400 hover:text-white"><i class="fas fa-arrow-down"></i></button>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <img src="./assets/images/slider-3.jpg" alt="焦点图3" class="h-12 w-24 object-cover rounded-md">
                                </td>
                                <td class="px-6 py-4 font-medium text-white">开发者文档上线</td>
                                <td class="px-6 py-4">/docs</td>
                                <td class="px-6 py-4 text-gray-500">隐藏</td>
                                <td class="px-6 py-4">
                                    <a href="#" class="font-medium text-blue-500 hover:underline mr-3">编辑</a>
                                    <a href="#" class="font-medium text-red-500 hover:underline">删除</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
</body>
</html> 