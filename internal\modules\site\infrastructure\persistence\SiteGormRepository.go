/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/site/infrastructure/persistence/SiteGormRepository.go
 * @Description: GORM implementation of the site repository.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package persistence

import (
	"context"
	"errors"
	"gacms/internal/modules/site/domain/contract"
	"gacms/internal/modules/site/domain/model"
	"gacms/pkg/contract"
	"gorm.io/gorm"
)

type SiteGormRepository struct {
	db contract.Database
}

func NewSiteGormRepository(db contract.Database) contract.SiteRepository {
	return &SiteGormRepository{db: db}
}

func (r *SiteGormRepository) Create(ctx context.Context, site *model.Site) error {
	return r.db.DB(ctx).Create(site).Error
}

func (r *SiteGormRepository) GetByID(ctx context.Context, id uint) (*model.Site, error) {
	var site model.Site
	err := r.db.DB(ctx).First(&site, id).Error
	return &site, err
}

func (r *SiteGormRepository) List(ctx context.Context, page, pageSize int) ([]*model.Site, int64, error) {
	var sites []*model.Site
	var total int64

	offset := (page - 1) * pageSize
	err := r.db.DB(ctx).Model(&model.Site{}).Count(&total).Limit(pageSize).Offset(offset).Find(&sites).Error
	return sites, total, err
}

func (r *SiteGormRepository) Update(ctx context.Context, site *model.Site) error {
	return r.db.DB(ctx).Save(site).Error
}

func (r *SiteGormRepository) Delete(ctx context.Context, id uint) error {
	return r.db.DB(ctx).Delete(&model.Site{}, id).Error
}

func (r *SiteGormRepository) GetByDomain(ctx context.Context, domain string) (*model.Site, error) {
	var site model.Site
	// This query must be unscoped as it's used to identify the site itself.
	err := r.db.DB(ctx).Where("domain = ?", domain).First(&site).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // Not an error if not found
		}
		return nil, err
	}
	return &site, nil
}

func (r *SiteGormRepository) GetByBackendDomain(ctx context.Context, backendDomain string) (*model.Site, error) {
	var site model.Site
	// This query must be unscoped as it's used to identify the site itself.
	err := r.db.DB(ctx).Where("backend_domain = ?", backendDomain).First(&site).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // Not an error if not found
		}
		return nil, err
	}
	return &site, nil
}

</rewritten_file> 