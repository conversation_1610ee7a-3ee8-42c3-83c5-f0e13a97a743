/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/user/Module.go
 * @Description: Defines and provides the components and metadata of the user module for dependency injection.
 *
 * © 2025 GACMS. All rights reserved.
 */
package user

import (
	"gacms/internal/core/service"
	"gacms/internal/modules/user/application/observer"
	"gacms/internal/modules/user/application/service"
	"gacms/internal/modules/user/domain/contract"
	repoImpl "gacms/internal/modules/user/infrastructure/repository"
	"gacms/internal/modules/user/port/http/controller"
	"gacms/internal/modules/user/port/http/middleware"
	"gacms/internal/port/http/router"
	pkgContract "gacms/pkg/contract"
	"go.uber.org/fx"
)

// Module is the standard fx dependency injection module for the user package.
var Module = fx.Options(
	// 1. Repositories
	fx.Provide(
		fx.Annotate(repoImpl.NewGormAdminRepository, fx.As(new(contract.AdminRepository))),
		fx.Annotate(repoImpl.NewGormMemberRepository, fx.As(new(contract.MemberRepository))),
		fx.Annotate(repoImpl.NewGormRoleRepository, fx.As(new(contract.RoleRepository))),
		fx.Annotate(repoImpl.NewGormPermissionRepository, fx.As(new(contract.PermissionRepository))),
	),

	// 2. Application Services & Listeners
	fx.Provide(
		service.NewAdminAuthService,
		service.NewMemberAuthService,
		service.NewRoleService,
		// Provide PermissionService and annotate it as the implementation for PermissionChecker
		fx.Annotate(
			service.NewPermissionService,
			fx.As(new(pkgContract.PermissionChecker)),
		),
		service.NewRoleManagementService,
		service.NewAdminRoleService,
		service.NewAdminService,
		// Provide the UserService proxy instead of the real service.
		// The proxy will lazy-load the actual service on first use.
		fx.Annotate(
			service.NewUserServiceProxy,
			fx.As(new(contract.UserService)),
		),
		middleware.NewPermissionMiddleware,
		observer.NewContentTypeObserver,
		observer.NewUserObserver,
	),

	// 3. HTTP Controllers (provided as normal dependencies first)
	fx.Provide(
		controller.NewAdminAuthController,
		controller.NewMemberAuthController,
	),

	// 4. Routable Registrations, provided into the 'routables' group for the main router.
	fx.Provide(
		fx.Annotate(
			func(c *controller.AdminAuthController) pkgContract.RoutableRegistration {
				return pkgContract.RoutableRegistration{
					Target:   pkgContract.AdminRoute,
					Routable: c,
				}
			},
			fx.ResultTags(`group:"routables"`),
		),
		fx.Annotate(
			func(c *controller.MemberAuthController) pkgContract.RoutableRegistration {
				return pkgContract.RoutableRegistration{
					Target:   pkgContract.FrontendRoute,
					Routable: c,
				}
			},
			fx.ResultTags(`group:"routables"`),
		),
	),

	// 5. Aggregated Router (the single routable entry point)
	// This approach might be refactored to align with the new AppContext model.
	// For now, keeping it to ensure module can be self-contained.
	fx.Provide(
		fx.Annotate(
			controller.NewRouter,
			fx.ResultTags(`name:"routable_controller"`),
		),
	),

	// 6. Event Handler Registration
	fx.Invoke(func(
		manager pkgContract.EventManager,
		contentTypeObserver *observer.ContentTypeObserver,
		userObserver *observer.UserObserver,
	) {
		manager.RegisterHandler(contentTypeObserver)
		manager.RegisterHandler(userObserver)
	}),

	// 7. Database Seeder
	fx.Provide(
		fx.Annotate(
			persistence.NewPermissionSeeder,
			fx.As(new(pkgContract.Seeder)),
			fx.ResultTags(`group:"seeders"`),
		),
	),
)


