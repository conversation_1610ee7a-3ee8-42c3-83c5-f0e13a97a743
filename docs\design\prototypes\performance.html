<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 性能优化</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: { 500: '#007bff', 600: '#0069d9' },
                        secondary: { 500: '#00c6ff' },
                        dark: { 800: '#1F2937', 900: '#1A1A1A' }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>

        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">性能优化管理</h2>
                    <div class="flex items-center space-x-3">
                         <button class="flex items-center justify-center bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-all hover:bg-gray-600">
                            <i class="fas fa-sync-alt mr-2"></i>刷新数据
                        </button>
                        <button class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-2 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30">
                            <i class="fas fa-bolt mr-2"></i>一键优化
                        </button>
                    </div>
                </div>
            </div>

            <!-- 性能概览 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400 text-sm">页面加载速度</span>
                        <i class="fas fa-tachometer-alt text-blue-400"></i>
                    </div>
                    <div class="text-2xl font-bold text-white mt-2">1.8 秒</div>
                    <div class="flex items-center text-sm mt-1 text-red-400"><i class="fas fa-arrow-down mr-1"></i> 12% 较上周</div>
                </div>
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400 text-sm">服务器响应</span>
                        <i class="fas fa-server text-purple-400"></i>
                    </div>
                    <div class="text-2xl font-bold text-white mt-2">320 ms</div>
                     <div class="flex items-center text-sm mt-1 text-red-400"><i class="fas fa-arrow-down mr-1"></i> 8% 较上周</div>
                </div>
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400 text-sm">数据库查询</span>
                        <i class="fas fa-database text-green-400"></i>
                    </div>
                    <div class="text-2xl font-bold text-white mt-2">45 ms</div>
                    <div class="flex items-center text-sm mt-1 text-gray-400"><i class="fas fa-minus mr-1"></i> 2% 较上周</div>
                </div>
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400 text-sm">缓存命中率</span>
                        <i class="fas fa-memory text-yellow-400"></i>
                    </div>
                    <div class="text-2xl font-bold text-white mt-2">87%</div>
                    <div class="flex items-center text-sm mt-1 text-green-400"><i class="fas fa-arrow-up mr-1"></i> 5% 较上周</div>
                </div>
            </div>

            <!-- 优化建议 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold text-white mb-4">优化建议</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-gray-800/20 border border-gray-700/50 rounded-xl p-5">
                        <h4 class="font-medium text-white mb-2">图片资源优化</h4>
                        <p class="text-gray-400 text-sm mb-4">检测到多个未压缩图片，建议压缩以提高加载速度。</p>
                        <button class="text-sm text-white bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg">执行优化</button>
                    </div>
                    <div class="bg-gray-800/20 border border-gray-700/50 rounded-xl p-5">
                        <h4 class="font-medium text-white mb-2">静态资源压缩</h4>
                        <p class="text-gray-400 text-sm mb-4">JS/CSS 文件未压缩，建议开启 Gzip 压缩以减小文件体积。</p>
                        <button class="text-sm text-white bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg">开启 Gzip</button>
                    </div>
                </div>
            </div>

             <!-- 资源使用情况 -->
             <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold text-white mb-4">实时资源使用</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <span class="text-gray-400 text-sm">CPU 使用率</span>
                        <div class="w-full bg-gray-700 rounded-full h-2.5 mt-2">
                            <div class="bg-green-500 h-2.5 rounded-full" style="width: 45%"></div>
                        </div>
                        <div class="text-right text-sm text-white mt-1">45%</div>
                    </div>
                    <div>
                        <span class="text-gray-400 text-sm">内存使用</span>
                        <div class="w-full bg-gray-700 rounded-full h-2.5 mt-2">
                            <div class="bg-yellow-500 h-2.5 rounded-full" style="width: 65%"></div>
                        </div>
                        <div class="text-right text-sm text-white mt-1">65% (8.2/16 GB)</div>
                    </div>
                     <div>
                        <span class="text-gray-400 text-sm">磁盘空间</span>
                        <div class="w-full bg-gray-700 rounded-full h-2.5 mt-2">
                            <div class="bg-red-500 h-2.5 rounded-full" style="width: 85%"></div>
                        </div>
                        <div class="text-right text-sm text-white mt-1">85% (425/500 GB)</div>
                    </div>
                </div>
            </div>
        </div>

        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
</body>
</html> 