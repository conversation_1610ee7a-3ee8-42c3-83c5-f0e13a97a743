/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/extension/module.go
 * @Description: Defines the extension module for dependency injection.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package extension

import (
	"context"
	"gacms/internal/core/contract"
	"gacms/internal/modules/extension/application/service"
	"gacms/internal/modules/extension/application/strategy"
	"gacms/internal/modules/extension/domain/contract"
	"gacms/internal/modules/extension/domain/model"
	"gacms/internal/modules/extension/infrastructure/persistence"
	"gacms/internal/modules/extension/port/http/controller"
	"gacms/internal/port/http/middleware"

	"github.com/gin-gonic/gin"
	"go.uber.org/fx"
	"gorm.io/gorm"
)

type ExtensionModule struct{}

func (m *ExtensionModule) GetName() string {
	return "extension"
}

func (m *ExtensionModule) GetVersion() string {
	return "1.1.0"
}

func (m *ExtensionModule) GetModels() []interface{} {
	return []interface{}{&model.Extension{}}
}

func (m *ExtensionModule) ExposePermissions() []contract.PermissionInfo {
	return []contract.PermissionInfo{
		{Name: "Install Extensions", Slug: "extensions:install", Description: "Install new extensions (themes, modules, plugins)"},
		{Name: "Manage Extensions", Slug: "extensions:manage", Description: "Manage installed extensions (enable, disable, delete)"},
	}
}

var Module = fx.Options(
	// Provide strategies for enabling/disabling extensions
	fx.Provide(strategy.NewModuleStrategy),
	// In the future, other strategies like NewThemeStrategy will be provided here.

	// Provide the services
	fx.Provide(service.NewExtensionService),
	fx.Provide(service.NewActivationService),
)

func RegisterRoutes(
	router *gin.Engine,
	authMiddleware *middleware.AdminAuthMiddleware,
	extensionController *controller.ExtensionController,
) {
	guard := authMiddleware.Handle("extensions:install", "extensions:manage")
	adminGroup := router.Group("/api/admin/extensions", guard)
	extensionController.RegisterRoutes(adminGroup)
} 