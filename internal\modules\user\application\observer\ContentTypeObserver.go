/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/modules/user/application/observer/ContentTypeObserver.go
 * @Description: 用户模块观察内容类型事件的观察者，遵循统一的目录结构和命名规范
 * 
 * © 2025 GACMS. All rights reserved.
 */

package observer

import (
	"fmt"
	contentTypeModel "gacms/internal/modules/contenttype/domain/model"
	"gacms/internal/modules/user/domain/contract"
	"gacms/internal/modules/user/domain/model"
	"gacms/pkg/contract/events"
	"log"
)

// ContentTypeObserver 观察内容类型模块的事件
type ContentTypeObserver struct {
	adminRepo contract.AdminRepository
}

// NewContentTypeObserver 创建内容类型事件观察者
func NewContentTypeObserver(adminRepo contract.AdminRepository) *ContentTypeObserver {
	return &ContentTypeObserver{adminRepo: adminRepo}
}

// Handle 处理事件
func (o *ContentTypeObserver) Handle(event events.Event) {
	if event.Name() != "content_type.created" {
		return
	}

	// The payload is the full ContentType model, as sent by the service.
	payload, ok := event.Payload().(*contentTypeModel.ContentType)
	if !ok {
		log.Printf("ERROR: Invalid payload type for event %s. Expected *contentTypeModel.ContentType.", event.Name())
		return
	}

	if err := o.syncContentTypePermissions(payload.Slug); err != nil {
		log.Printf("ERROR: Failed to sync permissions for content type %s: %v", payload.Slug, err)
	} else {
		log.Printf("INFO: Successfully synced permissions for new content type: %s", payload.Slug)
	}
}

// syncContentTypePermissions 为内容类型生成标准权限并保存
func (o *ContentTypeObserver) syncContentTypePermissions(slug string) error {
	moduleName := "content"
	permissions := []model.AdminPermission{
		{Module: moduleName, Name: fmt.Sprintf("%s:%s:create", moduleName, slug), Description: fmt.Sprintf("Create new %s entries", slug)},
		{Module: moduleName, Name: fmt.Sprintf("%s:%s:read", moduleName, slug), Description: fmt.Sprintf("Read %s entries", slug)},
		{Module: moduleName, Name: fmt.Sprintf("%s:%s:update", moduleName, slug), Description: fmt.Sprintf("Update %s entries", slug)},
		{Module: moduleName, Name: fmt.Sprintf("%s:%s:delete", moduleName, slug), Description: fmt.Sprintf("Delete %s entries", slug)},
		{Module: moduleName, Name: fmt.Sprintf("%s:%s:publish", moduleName, slug), Description: fmt.Sprintf("Publish %s entries", slug)},
	}

	return o.adminRepo.SyncPermissions(permissions)
}

// HandlerName 返回处理器名称
func (o *ContentTypeObserver) HandlerName() string {
	return "user.content_type.observer"
}

// SupportedEvents 返回支持的事件列表
func (o *ContentTypeObserver) SupportedEvents() []contract.EventName {
	return []contract.EventName{"content_type.created"}
}

// Priority 返回事件处理器的优先级
func (o *ContentTypeObserver) Priority() int {
	// 权限同步的优先级较高，需要及时处理
	return 20
}

// IsAsync 返回是否异步处理
func (o *ContentTypeObserver) IsAsync() bool {
	// 权限同步需要同步处理，确保权限及时生效
	return false
}
