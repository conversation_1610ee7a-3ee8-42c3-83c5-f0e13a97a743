/*
 * @Author: <PERSON><PERSON>eh <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: pkg/contract/Site.go
 * @Description: 站点和多租户抽象契约定义
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import "context"

// Site 站点抽象接口
type Site interface {
	GetID() uint
	GetName() string
	GetDomain() string
	GetDescription() string
	IsActive() bool
	GetConfig() SiteConfig
}

// SiteConfig 站点配置抽象接口
type SiteConfig interface {
	GetTitle() string
	GetKeywords() string
	GetDescription() string
	GetLogo() string
	GetFavicon() string
	GetSEOConfig() SEOConfig
	GetSecurityConfig() SecurityConfig
	GetCacheConfig() CacheConfig
}

// SEOConfig SEO配置抽象接口
type SEOConfig interface {
	IsEnabled() bool
	GetTitle() string
	GetKeywords() string
	GetDescription() string
	GetCanonicalURL() string
}

// SecurityConfig 安全配置抽象接口
type SecurityConfig interface {
	IsHTTPSEnabled() bool
	ShouldForceHTTPS() bool
	GetCSPPolicy() string
	IsXSSProtectionEnabled() bool
}

// CacheConfig 缓存配置抽象接口
type CacheConfig interface {
	IsEnabled() bool
	GetTTL() int
	GetStrategy() string
	GetRedisConfig() map[string]interface{}
}

// SiteResolver 站点解析器抽象接口
type SiteResolver interface {
	// ResolveSiteByDomain 根据域名解析站点
	ResolveSiteByDomain(ctx context.Context, domain string) (Site, error)
	
	// ResolveSiteByID 根据ID解析站点
	ResolveSiteByID(ctx context.Context, siteID uint) (Site, error)
	
	// GetCurrentSite 获取当前站点
	GetCurrentSite(ctx context.Context) (Site, error)
	
	// GetDefaultSite 获取默认站点
	GetDefaultSite(ctx context.Context) (Site, error)
}

// TenantContext 租户上下文抽象接口
type TenantContext interface {
	// GetCurrentTenant 获取当前租户
	GetCurrentTenant(ctx context.Context) (Tenant, error)
	
	// SetCurrentTenant 设置当前租户
	SetCurrentTenant(ctx context.Context, tenant Tenant) context.Context
	
	// GetTenantID 获取当前租户ID
	GetTenantID(ctx context.Context) (uint, error)
	
	// WithTenant 在指定租户上下文中执行
	WithTenant(ctx context.Context, tenantID uint, fn func(context.Context) error) error
}

// Tenant 租户抽象接口
type Tenant interface {
	GetID() uint
	GetSite() Site
	GetDatabaseConfig() DatabaseConfig
	GetStorageConfig() StorageConfig
	IsIsolated() bool
}

// DatabaseConfig 数据库配置抽象接口
type DatabaseConfig interface {
	GetConnectionString() string
	GetTablePrefix() string
	IsShared() bool
}

// StorageConfig 存储配置抽象接口
type StorageConfig interface {
	GetStorageType() string
	GetBasePath() string
	GetConfig() map[string]interface{}
}

// MultiTenantProvider 多租户提供者抽象接口
type MultiTenantProvider interface {
	// GetTenantByDomain 根据域名获取租户
	GetTenantByDomain(ctx context.Context, domain string) (Tenant, error)
	
	// GetTenantByID 根据ID获取租户
	GetTenantByID(ctx context.Context, tenantID uint) (Tenant, error)
	
	// CreateTenant 创建租户
	CreateTenant(ctx context.Context, site Site) (Tenant, error)
	
	// UpdateTenant 更新租户
	UpdateTenant(ctx context.Context, tenant Tenant) error
	
	// DeleteTenant 删除租户
	DeleteTenant(ctx context.Context, tenantID uint) error
	
	// ListTenants 列出所有租户
	ListTenants(ctx context.Context) ([]Tenant, error)
}

// DomainBinding 域名绑定抽象接口
type DomainBinding interface {
	GetID() uint
	GetDomain() string
	GetSiteID() uint
	GetBindingType() BindingType
	GetModuleSlug() string
	GetCategoryID() uint
	IsURLRewriteEnabled() bool
	GetDefaultController() string
	GetDefaultAction() string
	GetURLRules() []URLRewriteRule
}

// BindingType 绑定类型
type BindingType string

const (
	ModuleBinding        BindingType = "module"
	CategoryBinding      BindingType = "category"
	PlatformAdminBinding BindingType = "platform_admin"
)

// URLRewriteRule URL重写规则抽象接口
type URLRewriteRule interface {
	GetID() uint
	GetRuleName() string
	GetPattern() string
	GetReplacement() string
	GetPriority() int
	IsActive() bool
}

// DomainBindingResolver 域名绑定解析器抽象接口
type DomainBindingResolver interface {
	// ResolveDomainBinding 解析域名绑定
	ResolveDomainBinding(ctx context.Context, domain string) (DomainBinding, error)
	
	// GetDomainBindingWithRules 获取包含重写规则的域名绑定
	GetDomainBindingWithRules(ctx context.Context, domain string) (DomainBinding, error)
	
	// ClearCache 清除缓存
	ClearCache(domain string)
	
	// ClearAllCache 清除所有缓存
	ClearAllCache()
}

// SiteManager 站点管理器抽象接口
type SiteManager interface {
	// CreateSite 创建站点
	CreateSite(ctx context.Context, name, domain, description string) (Site, error)
	
	// UpdateSite 更新站点
	UpdateSite(ctx context.Context, siteID uint, name, description string) (Site, error)
	
	// DeleteSite 删除站点
	DeleteSite(ctx context.Context, siteID uint) error
	
	// ActivateSite 激活站点
	ActivateSite(ctx context.Context, siteID uint) error
	
	// DeactivateSite 停用站点
	DeactivateSite(ctx context.Context, siteID uint) error
	
	// GetSite 获取站点
	GetSite(ctx context.Context, siteID uint) (Site, error)
	
	// ListSites 列出站点
	ListSites(ctx context.Context, page, pageSize int) ([]Site, int64, error)
	
	// UpdateSiteConfig 更新站点配置
	UpdateSiteConfig(ctx context.Context, siteID uint, config SiteConfig) error
}

// SiteEvent 站点事件
type SiteEvent struct {
	Type      SiteEventType
	Site      Site
	Timestamp int64
	Extra     map[string]interface{}
}

// SiteEventType 站点事件类型
type SiteEventType string

const (
	SiteCreatedEvent     SiteEventType = "site.created"
	SiteUpdatedEvent     SiteEventType = "site.updated"
	SiteDeletedEvent     SiteEventType = "site.deleted"
	SiteActivatedEvent   SiteEventType = "site.activated"
	SiteDeactivatedEvent SiteEventType = "site.deactivated"
	SiteConfigUpdatedEvent SiteEventType = "site.config.updated"
)

// SiteEventListener 站点事件监听器
type SiteEventListener interface {
	// OnSiteEvent 处理站点事件
	OnSiteEvent(ctx context.Context, event SiteEvent) error
}
