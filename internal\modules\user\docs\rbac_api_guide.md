# RBAC权限系统API指南

本文档详细说明了GACMS系统中RBAC权限系统的API使用方法，包括角色管理、权限分配和用户权限验证等功能。

## 目录

- [API概述](#api概述)
- [角色管理API](#角色管理api)
- [权限管理API](#权限管理api)
- [用户角色分配API](#用户角色分配api)
- [权限验证API](#权限验证api)
- [常见使用场景](#常见使用场景)

## API概述

GACMS的RBAC权限系统提供以下核心功能的API：

1. **角色管理**：创建、更新、删除角色
2. **权限管理**：创建、更新、删除权限
3. **角色权限分配**：为角色分配一组权限
4. **用户角色分配**：为用户分配角色
5. **权限验证**：检查用户是否具有特定权限

所有API都遵循RESTful设计原则，并支持多租户（站点）隔离。

## 角色管理API

### 创建角色

```
POST /api/admin/roles
```

请求参数：

```json
{
  "name": "Content Editor",
  "description": "Can create and edit content",
  "type": "admin", // "admin"或"member"
  "level": 2,
  "site_id": 1
}
```

响应：

```json
{
  "code": 200,
  "message": "角色创建成功",
  "data": {
    "id": 1,
    "name": "Content Editor",
    "description": "Can create and edit content",
    "type": "admin",
    "level": 2,
    "site_id": 1,
    "created_at": "2025-06-12T10:30:00Z",
    "created_by": 1
  }
}
```

### 获取角色列表

```
GET /api/admin/roles?type=admin&site_id=1
```

查询参数：
- `type`: 可选，角色类型，`admin`或`member`
- `site_id`: 可选，站点ID
- `page`: 可选，页码，默认1
- `page_size`: 可选，每页条数，默认20

响应：

```json
{
  "code": 200,
  "message": "获取角色列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "name": "Content Editor",
        "description": "Can create and edit content",
        "type": "admin",
        "level": 2,
        "site_id": 1,
        "created_at": "2025-06-12T10:30:00Z",
        "updated_at": "2025-06-12T10:30:00Z"
      },
      // 更多角色...
    ],
    "total": 5,
    "page": 1,
    "page_size": 20
  }
}
```

### 获取角色详情

```
GET /api/admin/roles/{role_id}
```

路径参数：
- `role_id`: 角色ID

响应：

```json
{
  "code": 200,
  "message": "获取角色详情成功",
  "data": {
    "id": 1,
    "name": "Content Editor",
    "description": "Can create and edit content",
    "type": "admin",
    "level": 2,
    "site_id": 1,
    "permissions": [
      {
        "id": 1,
        "slug": "content.create",
        "description": "Can create content"
      },
      {
        "id": 2,
        "slug": "content.edit",
        "description": "Can edit content"
      }
    ],
    "created_at": "2025-06-12T10:30:00Z",
    "updated_at": "2025-06-12T10:30:00Z"
  }
}
```

### 更新角色

```
PUT /api/admin/roles/{role_id}
```

路径参数：
- `role_id`: 角色ID

请求参数：

```json
{
  "name": "Senior Content Editor",
  "description": "Can create, edit and publish content",
  "level": 3,
  "site_id": 1
}
```

响应：

```json
{
  "code": 200,
  "message": "角色更新成功",
  "data": {
    "id": 1,
    "name": "Senior Content Editor",
    "description": "Can create, edit and publish content",
    "type": "admin",
    "level": 3,
    "site_id": 1,
    "updated_at": "2025-06-12T11:15:00Z",
    "updated_by": 1
  }
}
```

### 删除角色

```
DELETE /api/admin/roles/{role_id}
```

路径参数：
- `role_id`: 角色ID

请求参数：

```json
{
  "site_id": 1
}
```

响应：

```json
{
  "code": 200,
  "message": "角色删除成功",
  "data": null
}
```

## 权限管理API

### 创建权限

```
POST /api/admin/permissions
```

请求参数：

```json
{
  "slug": "content.publish",
  "description": "Can publish content",
  "site_id": 1
}
```

响应：

```json
{
  "code": 200,
  "message": "权限创建成功",
  "data": {
    "id": 3,
    "slug": "content.publish",
    "description": "Can publish content",
    "site_id": 1,
    "created_at": "2025-06-12T13:45:00Z",
    "created_by": 1
  }
}
```

### 获取权限列表

```
GET /api/admin/permissions?site_id=1
```

查询参数：
- `site_id`: 可选，站点ID
- `page`: 可选，页码，默认1
- `page_size`: 可选，每页条数，默认20

响应：

```json
{
  "code": 200,
  "message": "获取权限列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "slug": "content.create",
        "description": "Can create content",
        "site_id": 1
      },
      {
        "id": 2,
        "slug": "content.edit",
        "description": "Can edit content",
        "site_id": 1
      },
      {
        "id": 3,
        "slug": "content.publish",
        "description": "Can publish content",
        "site_id": 1
      }
    ],
    "total": 3,
    "page": 1,
    "page_size": 20
  }
}
```

### 更新权限

```
PUT /api/admin/permissions/{permission_id}
```

路径参数：
- `permission_id`: 权限ID

请求参数：

```json
{
  "slug": "content.approve",
  "description": "Can approve content for publication",
  "site_id": 1
}
```

响应：

```json
{
  "code": 200,
  "message": "权限更新成功",
  "data": {
    "id": 3,
    "slug": "content.approve",
    "description": "Can approve content for publication",
    "site_id": 1,
    "updated_at": "2025-06-12T14:20:00Z",
    "updated_by": 1
  }
}
```

### 删除权限

```
DELETE /api/admin/permissions/{permission_id}
```

路径参数：
- `permission_id`: 权限ID

请求参数：

```json
{
  "site_id": 1
}
```

响应：

```json
{
  "code": 200,
  "message": "权限删除成功",
  "data": null
}
```

## 角色权限分配API

### 为角色分配权限

```
POST /api/admin/roles/{role_id}/permissions
```

路径参数：
- `role_id`: 角色ID

请求参数：

```json
{
  "permission_ids": [1, 2, 3],
  "site_id": 1
}
```

响应：

```json
{
  "code": 200,
  "message": "权限分配成功",
  "data": {
    "role_id": 1,
    "permissions": [
      {
        "id": 1,
        "slug": "content.create",
        "description": "Can create content"
      },
      {
        "id": 2,
        "slug": "content.edit",
        "description": "Can edit content"
      },
      {
        "id": 3,
        "slug": "content.approve",
        "description": "Can approve content for publication"
      }
    ]
  }
}
```

### 获取角色的权限列表

```
GET /api/admin/roles/{role_id}/permissions
```

路径参数：
- `role_id`: 角色ID

查询参数：
- `site_id`: 可选，站点ID

响应：

```json
{
  "code": 200,
  "message": "获取角色权限成功",
  "data": [
    {
      "id": 1,
      "slug": "content.create",
      "description": "Can create content"
    },
    {
      "id": 2,
      "slug": "content.edit",
      "description": "Can edit content"
    },
    {
      "id": 3,
      "slug": "content.approve",
      "description": "Can approve content for publication"
    }
  ]
}
```

## 用户角色分配API

### 为用户分配角色

```
POST /api/admin/users/{user_id}/roles
```

路径参数：
- `user_id`: 用户ID

请求参数：

```json
{
  "role_ids": [1, 2],
  "site_id": 1,
  "user_type": "admin" // "admin"或"member"
}
```

响应：

```json
{
  "code": 200,
  "message": "角色分配成功",
  "data": {
    "user_id": 5,
    "user_type": "admin",
    "roles": [
      {
        "id": 1,
        "name": "Senior Content Editor",
        "type": "admin"
      },
      {
        "id": 2,
        "name": "Category Manager",
        "type": "admin"
      }
    ]
  }
}
```

### 获取用户的角色列表

```
GET /api/admin/users/{user_id}/roles?user_type=admin
```

路径参数：
- `user_id`: 用户ID

查询参数：
- `user_type`: 可选，用户类型，`admin`或`member`
- `site_id`: 可选，站点ID

响应：

```json
{
  "code": 200,
  "message": "获取用户角色成功",
  "data": [
    {
      "id": 1,
      "name": "Senior Content Editor",
      "description": "Can create, edit and publish content",
      "type": "admin",
      "level": 3
    },
    {
      "id": 2,
      "name": "Category Manager",
      "description": "Can manage content categories",
      "type": "admin",
      "level": 2
    }
  ]
}
```

## 权限验证API

### 检查用户是否具有特定权限

```
GET /api/admin/users/{user_id}/can?permission=content.publish&user_type=admin
```

路径参数：
- `user_id`: 用户ID

查询参数：
- `permission`: 权限标识
- `user_type`: 可选，用户类型，`admin`或`member`

响应：

```json
{
  "code": 200,
  "message": "权限检查成功",
  "data": {
    "has_permission": true
  }
}
```

### 批量检查用户权限

```
POST /api/admin/users/{user_id}/can-batch
```

路径参数：
- `user_id`: 用户ID

请求参数：

```json
{
  "permissions": ["content.create", "content.edit", "content.publish"],
  "user_type": "admin"
}
```

响应：

```json
{
  "code": 200,
  "message": "批量权限检查成功",
  "data": {
    "permissions": {
      "content.create": true,
      "content.edit": true,
      "content.publish": false
    }
  }
}
```

## 常见使用场景

### 场景1：创建角色并分配权限

步骤：
1. 创建新角色（`POST /api/admin/roles`）
2. 为角色分配权限（`POST /api/admin/roles/{role_id}/permissions`）

### 场景2：为新用户分配角色

步骤：
1. 创建用户（使用用户管理API）
2. 为用户分配角色（`POST /api/admin/users/{user_id}/roles`）

### 场景3：检查用户权限并显示/隐藏UI元素

步骤：
1. 前端获取当前用户权限（`POST /api/admin/users/{user_id}/can-batch`）
2. 根据权限检查结果动态显示或隐藏UI元素

示例代码（Vue.js）：

```javascript
// 检查当前用户权限并缓存结果
async function checkUserPermissions() {
  const userId = store.state.user.id;
  const requiredPermissions = [
    'content.create', 
    'content.edit', 
    'content.publish',
    'content.delete'
  ];
  
  try {
    const response = await api.post(`/api/admin/users/${userId}/can-batch`, {
      permissions: requiredPermissions,
      user_type: 'admin'
    });
    
    if (response.data.code === 200) {
      // 缓存权限检查结果
      store.commit('setUserPermissions', response.data.data.permissions);
    }
  } catch (error) {
    console.error('权限检查失败', error);
  }
}

// 在组件中使用权限检查结果
function hasPermission(permission) {
  return store.state.permissions[permission] === true;
}

// 在组件模板中使用
// <button v-if="hasPermission('content.publish')">发布</button>
```

### 场景4：根据角色层级实现权限继承

步骤：
1. 创建具有不同层级的角色（例如：`level=1`为管理员，`level=2`为编辑，`level=3`为查看者）
2. 为每个角色分配相应权限
3. 在角色分配时考虑层级关系

示例业务逻辑：

```go
// 伪代码：创建基于层级的角色结构
func CreateRoleHierarchy(ctx context.Context, siteID uint) error {
    // 创建管理员角色（最高权限）
    adminRole := &model.Role{
        Name:        "Admin",
        Description: "System administrator with all permissions",
        Type:        model.AdminRole,
        Level:       1, // 最高级别
        SiteID:      siteID,
    }
    if err := roleService.CreateRole(ctx, adminRole, siteID, currentUserID); err != nil {
        return err
    }
    
    // 创建编辑角色（中等权限）
    editorRole := &model.Role{
        Name:        "Editor",
        Description: "Can create and edit content",
        Type:        model.AdminRole,
        Level:       2, // 中等级别
        SiteID:      siteID,
    }
    if err := roleService.CreateRole(ctx, editorRole, siteID, currentUserID); err != nil {
        return err
    }
    
    // 创建查看者角色（最低权限）
    viewerRole := &model.Role{
        Name:        "Viewer",
        Description: "Can only view content",
        Type:        model.AdminRole,
        Level:       3, // 最低级别
        SiteID:      siteID,
    }
    if err := roleService.CreateRole(ctx, viewerRole, siteID, currentUserID); err != nil {
        return err
    }
    
    // 分配权限...
    
    return nil
}
```