/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/modules/actionlog/application/observer/ActionLogObserver.go
 * @Description: 操作日志模块的主观察者，遵循统一的目录结构和命名规范
 * 
 * © 2025 GACMS. All rights reserved.
 */

package observer

import (
	"context"
	"time"

	actionLogService "gacms/internal/modules/actionlog/application/service"
	"gacms/internal/modules/actionlog/domain/model"
	"gacms/pkg/contract"
	"go.uber.org/zap"
)

// ActionLogObserver 操作日志模块的主观察者
// 遵循统一的观察者模式，只监听自身的通用业务事件
type ActionLogObserver struct {
	service *actionLogService.ActionLogService
	logger  *zap.Logger
}

// NewActionLogObserver 创建操作日志观察者
func NewActionLogObserver(
	service *actionLogService.ActionLogService,
	logger *zap.Logger,
) *ActionLogObserver {
	return &ActionLogObserver{
		service: service,
		logger:  logger,
	}
}

// Handle 处理事件（统一的观察者模式）
func (o *ActionLogObserver) Handle(event contract.Event) error {
	// 只处理audit.operation.create事件
	if event.Name() != "audit.operation.create" {
		return nil
	}

	// 获取事件数据
	eventData := event.GetData()
	if eventData == nil {
		o.logger.Error("ActionLog observer received nil event data")
		return nil // 不返回错误，避免影响其他观察者
	}

	// 异步处理操作日志记录
	go o.processOperationLog(eventData)

	return nil
}

// processOperationLog 处理操作日志记录（异步）
func (o *ActionLogObserver) processOperationLog(eventData map[string]interface{}) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 提取通用字段（不依赖任何模块的业务逻辑）
	userID := o.extractUint(eventData["user_id"])
	username := o.extractString(eventData["username"])
	siteID := o.extractUint(eventData["site_id"])
	ipAddress := o.extractString(eventData["ip_address"])
	userAgent := o.extractString(eventData["user_agent"])
	action := o.extractString(eventData["action"])
	description := o.extractString(eventData["description"])
	resourceType := o.extractString(eventData["resource_type"])
	resourceID := o.extractUint(eventData["resource_id"])

	// 验证必要字段
	if userID == 0 || action == "" || description == "" {
		o.logger.Warn("ActionLog observer received incomplete event data",
			zap.Uint("user_id", userID),
			zap.String("action", action),
			zap.String("description", description),
		)
		return
	}

	// 构建操作日志记录
	actionLog := &model.ActionLog{
		UserID:       userID,
		Username:     username,
		SiteID:       siteID,
		IPAddress:    ipAddress,
		Method:       o.getMethodFromAction(action),
		Path:         o.buildPathFromEvent(resourceType, resourceID),
		Description:  description,
		Status:       200, // 默认成功状态
		ResponseTime: 0,   // 操作日志事件不包含响应时间
	}

	// 保存操作日志
	if err := o.service.CreateLog(ctx, actionLog); err != nil {
		o.logger.Error("ActionLog observer failed to create log",
			zap.Error(err),
			zap.Uint("user_id", userID),
			zap.String("action", action),
			zap.String("description", description),
		)
	} else {
		o.logger.Debug("ActionLog observer created log successfully",
			zap.Uint("user_id", userID),
			zap.String("action", action),
			zap.String("description", description),
		)
	}
}

// HandlerName 返回处理器名称
func (o *ActionLogObserver) HandlerName() string {
	return "actionlog.observer"
}

// SupportedEvents 返回支持的事件列表
func (o *ActionLogObserver) SupportedEvents() []contract.EventName {
	return []contract.EventName{"audit.operation.create"}
}

// extractUint 从事件数据中提取uint值
func (o *ActionLogObserver) extractUint(value interface{}) uint {
	switch v := value.(type) {
	case uint:
		return v
	case uint32:
		return uint(v)
	case uint64:
		return uint(v)
	case int:
		if v >= 0 {
			return uint(v)
		}
	case int32:
		if v >= 0 {
			return uint(v)
		}
	case int64:
		if v >= 0 {
			return uint(v)
		}
	case float64:
		if v >= 0 && v == float64(uint(v)) {
			return uint(v)
		}
	}
	return 0
}

// extractString 从事件数据中提取string值
func (o *ActionLogObserver) extractString(value interface{}) string {
	if str, ok := value.(string); ok {
		return str
	}
	return ""
}

// getMethodFromAction 根据操作类型推断HTTP方法
func (o *ActionLogObserver) getMethodFromAction(action string) string {
	switch action {
	case "create":
		return "POST"
	case "update":
		return "PUT"
	case "delete":
		return "DELETE"
	case "view", "list":
		return "GET"
	default:
		return "POST"
	}
}

// buildPathFromEvent 根据资源类型构建路径（通用逻辑，不依赖具体模块）
func (o *ActionLogObserver) buildPathFromEvent(resourceType string, resourceID uint) string {
	if resourceType == "" {
		return "/unknown"
	}

	if resourceID > 0 {
		return "/" + resourceType + "/" + o.uintToString(resourceID)
	}

	return "/" + resourceType
}

// uintToString 将uint转换为string
func (o *ActionLogObserver) uintToString(value uint) string {
	return string(rune(value + '0'))
}

// Priority 返回事件处理器的优先级
func (o *ActionLogObserver) Priority() int {
	// 操作日志记录的优先级较低，不影响主业务流程
	return 100
}

// IsAsync 返回是否异步处理
func (o *ActionLogObserver) IsAsync() bool {
	// 操作日志记录异步处理，不阻塞主业务流程
	return true
}
