/**
 * @file: gacms-admin.js
 * @description: GACMS后台管理系统公共脚本
 * @author: <PERSON><PERSON>
 * @email: <EMAIL>
 * @copyright: Copyright (c) 2025 Cion Nieh
 */

/**
 * 初始化通知提示
 */
function initNotifications() {
    const notificationCloseButtons = document.querySelectorAll('.notification-close');
    
    notificationCloseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const notification = this.closest('.notification');
            notification.style.opacity = '0';
            setTimeout(() => {
                notification.style.display = 'none';
            }, 300);
        });
    });
}
/**
 * 初始化清理缓存按钮
 */
function initClearCacheButton() {
    const clearCacheBtn = document.getElementById('clearCacheBtn');
    
    if (clearCacheBtn) {
        clearCacheBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 模拟清理缓存操作
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 清理中...';
            this.disabled = true;
            
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-check"></i> 清理完成';
                
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-broom"></i> 清理缓存';
                    this.disabled = false;
                }, 2000);
            }, 1500);
        });
    }
}

/**
 * 加载指定路径的HTML文件到指定元素中
 * @param {string} elementId - 目标元素的ID
 * @param {string} filePath - HTML文件的路径
 */
function loadHTML(elementId, filePath) {
    fetch(filePath)
      .then(response => {
        if (!response.ok) throw new Error(`${filePath} 加载失败`);
        return response.text();
      })
      .then(data => {
        document.getElementById(elementId).innerHTML = data;
      })
      .catch(error => {
        console.error(error);
        document.getElementById(elementId).innerHTML = `<div class="error">${filePath} 加载失败</div>`;
      });
  }

/**
 * 页面加载完成后初始化所有功能
 */
document.addEventListener('DOMContentLoaded', function() {
    initNotifications();
    initClearCacheButton();
    loadHTML('topNavbar', 'top_navbar.html');
});