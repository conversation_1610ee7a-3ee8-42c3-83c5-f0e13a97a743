<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

客户表单管理页面 - 用于管理系统前台用户/客户的表单
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 客户表单管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .form-card {
            transition: all 0.3s ease;
        }
        
        .form-card:hover {
            transform: translateY(-3px);
        }
        
        .status-badge {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 6px;
        }
        
        .status-active {
            background-color: #10B981;
            box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
        }
        
        .status-inactive {
            background-color: #6B7280;
            box-shadow: 0 0 0 2px rgba(107, 114, 128, 0.2);
        }
        
        .status-draft {
            background-color: #F59E0B;
            box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
        }
        
        /* 表单预览样式 */
        .form-preview {
            border: 1px dashed #4B5563;
            padding: 20px;
            border-radius: 8px;
            background-color: rgba(31, 41, 55, 0.5);
        }
        
        .form-field {
            margin-bottom: 16px;
        }
        
        /* 深色主题下的模态框样式 */
        .modal-content {
            background-color: #1f2937;
            border: 1px solid #374151;
            color: #e5e7eb;
        }
        
        .modal-header {
            border-bottom: 1px solid #374151;
        }
        
        .modal-footer {
            border-top: 1px solid #374151;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                            950: '#082f49',
                        },
                        secondary: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                            950: '#2e1065',
                        },
                        dark: {
                            50: '#f9fafb',
                            100: '#f3f4f6',
                            200: '#e5e7eb',
                            300: '#d1d5db',
                            400: '#9ca3af',
                            500: '#6b7280',
                            600: '#4b5563',
                            700: '#374151',
                            800: '#1f2937',
                            900: '#111827',
                            950: '#030712',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="#" class="hover:text-white">用户中心</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">客户表单管理</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">客户表单管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="addFormBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <i class="fas fa-plus mr-2"></i>
                            创建表单
                        </button>
                        <button class="flex items-center justify-center bg-gradient-to-r from-purple-500 to-purple-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-purple-500/30 relative overflow-hidden action-button">
                            <i class="fas fa-file-import mr-2"></i>
                            导入表单
                        </button>
                        <button class="flex items-center justify-center bg-gradient-to-r from-gray-600 to-gray-700 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg relative overflow-hidden action-button">
                            <i class="fas fa-cog mr-2"></i>
                            批量操作
                        </button>
                    </div>
                </div>
                <p class="text-gray-400 mt-2">创建和管理用于收集客户信息的表单，可以嵌入到网站页面或通过链接分享。</p>
            </div>

            <!-- 表单统计卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-6">
                <!-- 总表单数 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-wpforms text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">总表单数</div>
                            <div class="text-xl font-semibold text-white">24</div>
                            <div class="text-xs text-blue-400 mt-0.5">本月新增: 3</div>
                        </div>
                    </div>
                </div>
                
                <!-- 活跃表单 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-check-circle text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">活跃表单</div>
                            <div class="text-xl font-semibold text-white">18</div>
                            <div class="text-xs text-green-400 mt-0.5">75% 的表单</div>
                        </div>
                    </div>
                </div>
                
                <!-- 草稿表单 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-edit text-yellow-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">草稿表单</div>
                            <div class="text-xl font-semibold text-white">4</div>
                            <div class="text-xs text-yellow-400 mt-0.5">16.7% 的表单</div>
                        </div>
                    </div>
                </div>
                
                <!-- 非活跃表单 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-gray-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-pause-circle text-gray-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">非活跃表单</div>
                            <div class="text-xl font-semibold text-white">2</div>
                            <div class="text-xs text-gray-400 mt-0.5">8.3% 的表单</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 表单筛选和搜索 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap gap-4">
                    <div class="flex-1 min-w-[200px]">
                        <label class="block text-sm text-gray-400 mb-1">搜索表单</label>
                        <div class="relative">
                            <input type="text" placeholder="输入表单名称或描述..." class="w-full bg-gray-900 border border-gray-700 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent pl-10">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>
                    </div>
                    <div class="w-full sm:w-auto">
                        <label class="block text-sm text-gray-400 mb-1">状态筛选</label>
                        <select class="bg-gray-900 border border-gray-700 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="all">全部状态</option>
                            <option value="active">活跃</option>
                            <option value="draft">草稿</option>
                            <option value="inactive">非活跃</option>
                        </select>
                    </div>
                    <div class="w-full sm:w-auto">
                        <label class="block text-sm text-gray-400 mb-1">排序方式</label>
                        <select class="bg-gray-900 border border-gray-700 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="newest">最新创建</option>
                            <option value="name">名称排序</option>
                            <option value="submissions">提交数量</option>
                            <option value="views">浏览次数</option>
                        </select>
                    </div>
                    <div class="w-full sm:w-auto flex items-end">
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-filter mr-2"></i>应用筛选
                        </button>
                    </div>
                </div>
            </div>

            <!-- 表单列表 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold text-white mb-4">表单列表</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                    <!-- 表单卡片 1 - 活跃 -->
                    <div class="form-card bg-gray-800/20 border border-gray-700 rounded-xl p-5 relative">
                        <div class="absolute top-5 right-5 flex space-x-2">
                            <button class="text-gray-400 hover:text-white transition-colors">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-gray-400 hover:text-white transition-colors">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="text-gray-400 hover:text-red-500 transition-colors">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                        <div class="flex items-center mb-3">
                            <span class="status-badge status-active"></span>
                            <span class="text-green-400 text-sm">活跃</span>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">客户注册表单</h4>
                        <p class="text-gray-400 text-sm mb-4">用于网站前台客户注册的基本信息收集表单</p>
                        <div class="flex items-center justify-between text-sm text-gray-400 mb-4">
                            <span><i class="fas fa-calendar-alt mr-1"></i> 创建于: 2025-05-15</span>
                            <span><i class="fas fa-eye mr-1"></i> 1,245 次浏览</span>
                        </div>
                        <div class="flex items-center justify-between text-sm text-gray-400 mb-4">
                            <span><i class="fas fa-paper-plane mr-1"></i> 568 次提交</span>
                            <span><i class="fas fa-list mr-1"></i> 12 个字段</span>
                        </div>
                        <div class="flex space-x-2 mt-4">
                            <button class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg transition-colors duration-200 text-sm">
                                <i class="fas fa-eye mr-1"></i> 预览
                            </button>
                            <button class="flex-1 bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded-lg transition-colors duration-200 text-sm">
                                <i class="fas fa-link mr-1"></i> 获取链接
                            </button>
                            <button class="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded-lg transition-colors duration-200 text-sm">
                                <i class="fas fa-chart-bar mr-1"></i> 数据
                            </button>
                        </div>
                    </div>

                    <!-- 表单卡片 2 - 活跃 -->
                    <div class="form-card bg-gray-800/20 border border-gray-700 rounded-xl p-5 relative">
                        <div class="absolute top-5 right-5 flex space-x-2">
                            <button class="text-gray-400 hover:text-white transition-colors">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-gray-400 hover:text-white transition-colors">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="text-gray-400 hover:text-red-500 transition-colors">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                        <div class="flex items-center mb-3">
                            <span class="status-badge status-active"></span>
                            <span class="text-green-400 text-sm">活跃</span>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">联系我们表单</h4>
                        <p class="text-gray-400 text-sm mb-4">网站联系页面的客户留言表单</p>
                        <div class="flex items-center justify-between text-sm text-gray-400 mb-4">
                            <span><i class="fas fa-calendar-alt mr-1"></i> 创建于: 2025-05-20</span>
                            <span><i class="fas fa-eye mr-1"></i> 876 次浏览</span>
                        </div>
                        <div class="flex items-center justify-between text-sm text-gray-400 mb-4">
                            <span><i class="fas fa-paper-plane mr-1"></i> 124 次提交</span>
                            <span><i class="fas fa-list mr-1"></i> 6 个字段</span>
                        </div>
                        <div class="flex space-x-2 mt-4">
                            <button class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg transition-colors duration-200 text-sm">
                                <i class="fas fa-eye mr-1"></i> 预览
                            </button>
                            <button class="flex-1 bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded-lg transition-colors duration-200 text-sm">
                                <i class="fas fa-link mr-1"></i> 获取链接
                            </button>
                            <button class="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded-lg transition-colors duration-200 text-sm">
                                <i class="fas fa-chart-bar mr-1"></i> 数据
                            </button>
                        </div>
                    </div>

                    <!-- 表单卡片 3 - 草稿 -->
                    <div class="form-card bg-gray-800/20 border border-gray-700 rounded-xl p-5 relative">
                        <div class="absolute top-5 right-5 flex space-x-2">
                            <button class="text-gray-400 hover:text-white transition-colors">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-gray-400 hover:text-white transition-colors">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="text-gray-400 hover:text-red-500 transition-colors">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                        <div class="flex items-center mb-3">
                            <span class="status-badge status-draft"></span>
                            <span class="text-yellow-400 text-sm">草稿</span>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">客户满意度调查</h4>
                        <p class="text-gray-400 text-sm mb-4">收集客户对产品和服务的满意度反馈</p>
                        <div class="flex items-center justify-between text-sm text-gray-400 mb-4">
                            <span><i class="fas fa-calendar-alt mr-1"></i> 创建于: 2025-06-01</span>
                            <span><i class="fas fa-eye mr-1"></i> 0 次浏览</span>
                        </div>
                        <div class="flex items-center justify-between text-sm text-gray-400 mb-4">
                            <span><i class="fas fa-paper-plane mr-1"></i> 0 次提交</span>
                            <span><i class="fas fa-list mr-1"></i> 8 个字段</span>
                        </div>
                        <div class="flex space-x-2 mt-4">
                            <button class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg transition-colors duration-200 text-sm">
                                <i class="fas fa-eye mr-1"></i> 预览
                            </button>
                            <button class="flex-1 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg transition-colors duration-200 text-sm">
                                <i class="fas fa-check mr-1"></i> 发布
                            </button>
                            <button class="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded-lg transition-colors duration-200 text-sm">
                                <i class="fas fa-pen mr-1"></i> 编辑
                            </button>
                        </div>
                    </div>

                    <!-- 表单卡片 4 - 非活跃 -->
                    <div class="form-card bg-gray-800/20 border border-gray-700 rounded-xl p-5 relative">
                        <div class="absolute top-5 right-5 flex space-x-2">
                            <button class="text-gray-400 hover:text-white transition-colors">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-gray-400 hover:text-white transition-colors">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="text-gray-400 hover:text-red-500 transition-colors">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                        <div class="flex items-center mb-3">
                            <span class="status-badge status-inactive"></span>
                            <span class="text-gray-400 text-sm">非活跃</span>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">旧版注册表单</h4>
                        <p class="text-gray-400 text-sm mb-4">已停用的旧版客户注册表单</p>
                        <div class="flex items-center justify-between text-sm text-gray-400 mb-4">
                            <span><i class="fas fa-calendar-alt mr-1"></i> 创建于: 2025-01-10</span>
                            <span><i class="fas fa-eye mr-1"></i> 2,456 次浏览</span>
                        </div>
                        <div class="flex items-center justify-between text-sm text-gray-400 mb-4">
                            <span><i class="fas fa-paper-plane mr-1"></i> 1,245 次提交</span>
                            <span><i class="fas fa-list mr-1"></i> 10 个字段</span>
                        </div>
                        <div class="flex space-x-2 mt-4">
                            <button class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg transition-colors duration-200 text-sm">
                                <i class="fas fa-eye mr-1"></i> 预览
                            </button>
                            <button class="flex-1 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg transition-colors duration-200 text-sm">
                                <i class="fas fa-play mr-1"></i> 激活
                            </button>
                            <button class="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded-lg transition-colors duration-200 text-sm">
                                <i class="fas fa-chart-bar mr-1"></i> 数据
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 分页 -->
                <div class="flex justify-between items-center mt-6">
                    <div class="text-sm text-gray-400">
                        显示 1 到 4，共 24 个表单
                    </div>
                    <div class="flex space-x-1">
                        <button class="px-3 py-1 rounded bg-gray-700 text-white hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="px-3 py-1 rounded bg-blue-600 text-white hover:bg-blue-700">
                            1
                        </button>
                        <button class="px-3 py-1 rounded bg-gray-700 text-white hover:bg-gray-600">
                            2
                        </button>
                        <button class="px-3 py-1 rounded bg-gray-700 text-white hover:bg-gray-600">
                            3
                        </button>
                        <button class="px-3 py-1 rounded bg-gray-700 text-white hover:bg-gray-600">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 创建表单模态框 -->
            <div id="createFormModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
                <div class="modal-content bg-gray-800 rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                    <div class="modal-header flex justify-between items-center p-6 border-b border-gray-700">
                        <h3 class="text-xl font-bold text-white">创建新表单</h3>
                        <button id="closeFormModal" class="text-gray-400 hover:text-white">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body p-6">
                        <div class="mb-4">
                            <label class="block text-sm text-gray-400 mb-1">表单名称 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full bg-gray-900 border border-gray-700 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="输入表单名称">
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm text-gray-400 mb-1">表单描述</label>
                            <textarea class="w-full bg-gray-900 border border-gray-700 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent h-24" placeholder="输入表单描述"></textarea>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm text-gray-400 mb-1">表单类型</label>
                                <select class="w-full bg-gray-900 border border-gray-700 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="standard">标准表单</option>
                                    <option value="survey">调查问卷</option>
                                    <option value="registration">注册表单</option>
                                    <option value="contact">联系表单</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm text-gray-400 mb-1">表单状态</label>
                                <select class="w-full bg-gray-900 border border-gray-700 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="draft">草稿</option>
                                    <option value="active">活跃</option>
                                    <option value="inactive">非活跃</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm text-gray-400 mb-1">表单设置</label>
                            <div class="bg-gray-900 border border-gray-700 rounded-lg p-4">
                                <div class="flex items-center mb-3">
                                    <input type="checkbox" id="requireLogin" class="mr-2 h-4 w-4 rounded border-gray-700 bg-gray-800 text-blue-600 focus:ring-blue-500">
                                    <label for="requireLogin" class="text-sm text-gray-300">要求用户登录后提交</label>
                                </div>
                                <div class="flex items-center mb-3">
                                    <input type="checkbox" id="enableCaptcha" class="mr-2 h-4 w-4 rounded border-gray-700 bg-gray-800 text-blue-600 focus:ring-blue-500">
                                    <label for="enableCaptcha" class="text-sm text-gray-300">启用验证码</label>
                                </div>
                                <div class="flex items-center mb-3">
                                    <input type="checkbox" id="storeSubmissions" class="mr-2 h-4 w-4 rounded border-gray-700 bg-gray-800 text-blue-600 focus:ring-blue-500" checked>
                                    <label for="storeSubmissions" class="text-sm text-gray-300">存储表单提交数据</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="sendNotification" class="mr-2 h-4 w-4 rounded border-gray-700 bg-gray-800 text-blue-600 focus:ring-blue-500" checked>
                                    <label for="sendNotification" class="text-sm text-gray-300">发送提交通知邮件</label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm text-gray-400 mb-1">表单字段</label>
                            <div class="bg-gray-900 border border-gray-700 rounded-lg p-4">
                                <p class="text-sm text-gray-400 mb-3">创建表单后，您可以在表单编辑页面添加和管理字段。</p>
                                <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 text-sm">
                                    <i class="fas fa-plus mr-1"></i> 添加字段
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer flex justify-end space-x-3 p-6 border-t border-gray-700">
                        <button id="cancelFormCreate" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200">
                            取消
                        </button>
                        <button class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                            创建表单
                        </button>
                    </div>
                </div>
            </div>

            <!-- 通知组件 -->
            <div id="notification" class="notification fixed bottom-4 right-4 bg-gray-800 border border-gray-700 rounded-lg shadow-lg p-4 flex items-start max-w-sm transform transition-all duration-300 translate-y-10 opacity-0 hidden">
                <div class="mr-3 text-green-500">
                    <i class="fas fa-check-circle text-xl"></i>
                </div>
                <div class="flex-1">
                    <h4 class="text-white font-medium">操作成功</h4>
                    <p class="text-gray-300 text-sm">表单已成功创建！</p>
                </div>
                <button class="notification-close text-gray-400 hover:text-white ml-4">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- 页面底部版权信息 -->
            <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
                © 2025 GACMS 后台管理系统 - 版本 v1.0.0
            </footer>
        </div>
    </main>

    <!-- 脚本引入 -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 模态框交互
            const addFormBtn = document.getElementById('addFormBtn');
            const createFormModal = document.getElementById('createFormModal');
            const closeFormModal = document.getElementById('closeFormModal');
            const cancelFormCreate = document.getElementById('cancelFormCreate');
            
            // 打开模态框
            addFormBtn.addEventListener('click', function() {
                createFormModal.classList.remove('hidden');
                setTimeout(() => {
                    createFormModal.querySelector('.modal-content').classList.add('scale-100');
                    createFormModal.querySelector('.modal-content').classList.remove('scale-95');
                }, 10);
            });
            
            // 关闭模态框
            function closeModal() {
                createFormModal.querySelector('.modal-content').classList.add('scale-95');
                createFormModal.querySelector('.modal-content').classList.remove('scale-100');
                setTimeout(() => {
                    createFormModal.classList.add('hidden');
                }, 300);
            }
            
            closeFormModal.addEventListener('click', closeModal);
            cancelFormCreate.addEventListener('click', closeModal);
            
            // 点击模态框外部关闭
            createFormModal.addEventListener('click', function(e) {
                if (e.target === createFormModal) {
                    closeModal();
                }
            });
            
            // 显示通知
            function showNotification(type, title, message) {
                const notification = document.getElementById('notification');
                const iconContainer = notification.querySelector('.mr-3');
                const titleElement = notification.querySelector('h4');
                const messageElement = notification.querySelector('p');
                
                // 设置通知类型
                iconContainer.className = 'mr-3';
                if (type === 'success') {
                    iconContainer.classList.add('text-green-500');
                    iconContainer.innerHTML = '<i class="fas fa-check-circle text-xl"></i>';
                } else if (type === 'error') {
                    iconContainer.classList.add('text-red-500');
                    iconContainer.innerHTML = '<i class="fas fa-exclamation-circle text-xl"></i>';
                } else if (type === 'warning') {
                    iconContainer.classList.add('text-yellow-500');
                    iconContainer.innerHTML = '<i class="fas fa-exclamation-triangle text-xl"></i>';
                } else if (type === 'info') {
                    iconContainer.classList.add('text-blue-500');
                    iconContainer.innerHTML = '<i class="fas fa-info-circle text-xl"></i>';
                }
                
                // 设置标题和消息
                titleElement.textContent = title;
                messageElement.textContent = message;
                
                // 显示通知
                notification.classList.remove('hidden');
                setTimeout(() => {
                    notification.classList.remove('translate-y-10', 'opacity-0');
                }, 10);
                
                // 自动隐藏通知
                setTimeout(() => {
                    notification.classList.add('translate-y-10', 'opacity-0');
                    setTimeout(() => {
                        notification.classList.add('hidden');
                    }, 300);
                }, 5000);
            }
            
            // 示例：点击创建表单按钮显示成功通知
            document.querySelector('.modal-footer button:last-child').addEventListener('click', function() {
                closeModal();
                setTimeout(() => {
                    showNotification('success', '操作成功', '表单已成功创建！');
                }, 300);
            });
        });
    </script>
</body>
</html>