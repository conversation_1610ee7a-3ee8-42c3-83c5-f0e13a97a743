/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/extension/application/service/ExtensionService.go
 * @Description: A unified service for managing extensions using the Strategy Pattern.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"archive/zip"
	"encoding/json"
	"errors"
	"fmt"
	"gacms/internal/modules/extension/application/strategy"
	"gacms/internal/modules/extension/domain/contract"
	"gacms/internal/modules/extension/domain/model"
	siteContract "gacms/internal/modules/site/domain/contract"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"

	"gorm.io/gorm"
)

const (
	TypeTheme   = "theme"
	TypeModule  = "module"
	TypePlugin  = "plugin"
	tempInstallPath = "./uploads/temp_install"
)

var extensionPaths = map[string]string{
	TypeTheme:  "./themes",
	TypeModule: "./vendors",
	TypePlugin: "./plugins",
}

// ExtensionManifest defines the structure of theme.json, module.json, etc.
type ExtensionManifest struct {
	Name            string `json:"name"`
	Type            string `json:"type,omitempty"`
	Version         string `json:"version"`
	Description     string `json:"description"`
	RequiresLicense bool   `json:"requires_license,omitempty"`
}

// ExtensionService is the context for the strategy pattern.
type ExtensionService struct {
	strategies map[string]contract.ActivationStrategy
}

// NewExtensionService creates a new extension service and registers all available strategies.
func NewExtensionService(
	moduleStrategy *strategy.ModuleStrategy,
	// In the future, other strategies like ThemeStrategy will be injected here.
) *ExtensionService {
	strategies := make(map[string]contract.ActivationStrategy)
	strategies["module"] = moduleStrategy
	// strategies["theme"] = themeStrategy
	return &ExtensionService{
		strategies: strategies,
	}
}

// SyncWithFilesystem discovers extensions from the filesystem and syncs them with the database.
func (s *ExtensionService) SyncWithFilesystem() error {
	// 1. Get all extensions from DB
	dbExtensions, err := s.extRepo.GetAll()
	if err != nil {
		return fmt.Errorf("failed to get extensions from db: %w", err)
	}
	dbMap := make(map[string]*model.Extension)
	for _, ext := range dbExtensions {
		dbMap[ext.DirectoryName] = ext
	}

	// 2. Scan filesystem
	for extType, basePath := range extensionPaths {
		manifests, err := s.scanForManifests(basePath, extType)
		if err != nil {
			// Log error but continue
			fmt.Printf("Warning: failed to scan %s extensions: %v\n", extType, err)
			continue
		}

		for dirName, manifest := range manifests {
			if dbExt, exists := dbMap[dirName]; exists {
				// Extension exists in DB, check for update
				if dbExt.Version != manifest.Version {
					dbExt.Version = manifest.Version
					dbExt.RequiresLicense = manifest.RequiresLicense
					s.extRepo.Update(dbExt)
				}
				delete(dbMap, dirName) // Mark as processed
			} else {
				// New extension found
				newExt := &model.Extension{
					Name:            manifest.Name,
					Type:            extType,
					Version:         manifest.Version,
					DirectoryName:   dirName,
					IsEnabled:       true, // Default to enabled
					RequiresLicense: manifest.RequiresLicense,
				}
				s.extRepo.Create(newExt)
			}
		}
	}
	
	// 3. Any remaining in dbMap are extensions that no longer exist on filesystem
	for _, orphan := range dbMap {
		s.extRepo.Delete(orphan.ID)
	}

	return nil
}

// Enable activates an extension of a given type.
func (s *ExtensionService) Enable(extensionType, name string) error {
	strategy, ok := s.strategies[extensionType]
	if !ok {
		return fmt.Errorf("unsupported extension type: %s", extensionType)
	}
	return strategy.Enable(name)
}

// Disable deactivates an extension of a given type.
func (s *ExtensionService) Disable(extensionType, name string) error {
	strategy, ok := s.strategies[extensionType]
	if !ok {
		return fmt.Errorf("unsupported extension type: %s", extensionType)
	}
	return strategy.Disable(name)
}

// Activate links an extension to a site (currently only for themes).
func (s *ExtensionService) Activate(dirName string, siteID uint, themeType string) error {
	// 1. Check if extension is registered and enabled in the database
	ext, err := s.extRepo.FindByDirName(dirName)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("cannot activate: extension '%s' is not registered", dirName)
		}
		return fmt.Errorf("failed to find extension '%s': %w", dirName, err)
	}
	if !ext.IsEnabled {
		return fmt.Errorf("cannot activate: extension '%s' is disabled", dirName)
	}
	
	// 2. Read manifest for further checks
	manifest, err := s.GetManifest(ext.Type, ext.DirectoryName)
	if err != nil {
		return fmt.Errorf("could not read manifest for '%s': %w", dirName, err)
	}

	// 3. Check for license if required
	if manifest.RequiresLicense {
		// Placeholder for license check logic
		// if !s.licenseService.IsActive(dirName, siteID) {
		// 	 return fmt.Errorf("activation failed: license for '%s' is missing or invalid", dirName)
		// }
	}
	
	// 4. Perform the activation (specific logic, for now only themes)
	if ext.Type != TypeTheme {
		return fmt.Errorf("activation is currently only supported for themes")
	}
	
	site, err := s.siteRepo.GetByID(siteID)
	if err != nil {
		return err
	}
	if themeType == "frontend" {
		site.FrontendTheme = dirName
	} else if themeType == "backend" {
		site.BackendTheme = dirName
	} else {
		return fmt.Errorf("invalid theme activation type '%s'", themeType)
	}

	return s.siteRepo.Update(site)
}

// Deactivate resets the specified theme type for a given site to default (empty).
func (s *ExtensionService) Deactivate(siteID uint, extType string) error {
	if extType != "frontend" && extType != "backend" {
		return fmt.Errorf("invalid deactivation type '%s', must be 'frontend' or 'backend'", extType)
	}

	site, err := s.siteRepo.GetByID(siteID)
	if err != nil {
		return err
	}

	// Reset the correct theme field to empty
	if extType == "frontend" {
		if site.FrontendTheme == "" {
			return fmt.Errorf("cannot deactivate frontend theme as no theme is active")
		}
		site.FrontendTheme = "" // Reset to default
	} else { // backend
		if site.BackendTheme == "" {
			return fmt.Errorf("cannot deactivate backend theme as no theme is active")
		}
		site.BackendTheme = "" // Reset to default
	}

	return s.siteRepo.Update(site)
}

// GetActivated returns the directory name of the activated extension for a given site and type.
func (s *ExtensionService) GetActivated(siteID uint, extType string) (string, error) {
	if extType != "frontend" && extType != "backend" {
		return "", fmt.Errorf("invalid activation type '%s', must be 'frontend' or 'backend'", extType)
	}

	site, err := s.siteRepo.GetByID(siteID)
	if err != nil {
		return "", err
	}

	if extType == "frontend" {
		return site.FrontendTheme, nil
	}
	return site.BackendTheme, nil
}

// FindThemePath searches for a theme directory in both 'frontend' and 'backend' subdirectories.
func (s *ExtensionService) FindThemePath(dirName string) (string, error) {
	basePath := extensionPaths[TypeTheme]
	// Check in frontend subdir
	frontendPath := filepath.Join(basePath, "frontend", dirName)
	if _, err := os.Stat(frontendPath); !os.IsNotExist(err) {
		return frontendPath, nil
	}
	// Check in backend subdir
	backendPath := filepath.Join(basePath, "backend", dirName)
	if _, err := os.Stat(backendPath); !os.IsNotExist(err) {
		return backendPath, nil
	}
	return "", fmt.Errorf("theme '%s' not found in frontend or backend directories", dirName)
}

// readManifestFromFile reads and parses a manifest file from a given full path.
func (s *ExtensionService) readManifestFromFile(path string) (*ExtensionManifest, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("could not read manifest file %s: %w", path, err)
	}

	var manifest ExtensionManifest
	if err := json.Unmarshal(data, &manifest); err != nil {
		return nil, fmt.Errorf("could not parse manifest file %s: %w", path, err)
	}

	// Default type if not specified, e.g., for theme.json
	if manifest.Type == "" {
		// Infer from path, simple logic for now
		if strings.Contains(path, "theme.json") {
			manifest.Type = TypeTheme
		}
	}

	return &manifest, nil
}

// ListInstalled scans the filesystem and returns a list of all available extensions of a given type.
func (s *ExtensionService) ListInstalled(extType string) ([]*ExtensionManifest, error) {
	basePath, ok := extensionPaths[extType]
	if !ok {
		return nil, fmt.Errorf("unknown extension type: %s", extType)
	}

	var extensions []*ExtensionManifest

	// For themes, we need to scan subdirectories 'frontend' and 'backend'
	if extType == TypeTheme {
		frontendPath := filepath.Join(basePath, "frontend")
		backendPath := filepath.Join(basePath, "backend")
		
		s.scanDirectoryForExtensions(frontendPath, extType, &extensions)
		s.scanDirectoryForExtensions(backendPath, extType, &extensions)

		return extensions, nil
	}

	return s.scanDirectoryForExtensions(basePath, extType, &extensions)
}

// scanDirectoryForExtensions is a helper to scan a specific directory.
func (s *ExtensionService) scanDirectoryForExtensions(path, extType string, extensions *[]*ExtensionManifest) error {
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return nil
	}
	
	entries, err := os.ReadDir(path)
	if err != nil {
		return err
	}

	manifestFileName := "theme.json" // Assume theme for now

	for _, entry := range entries {
		if entry.IsDir() {
			manifest, err := s.GetManifest(extType, entry.Name())
			if err == nil {
				*extensions = append(*extensions, manifest)
			}
		}
	}
	return nil
}

// GetManifest reads a manifest file and returns it as a struct.
func (s *ExtensionService) GetManifest(extType, dirName string) (*ExtensionManifest, error) {
	var manifestPath string
	if extType == TypeTheme {
		themePath, err := s.FindThemePath(dirName)
		if err != nil {
			return nil, err
		}
		manifestPath = filepath.Join(themePath, "theme.json")
	} else {
		// This logic needs to be more robust for modules/plugins
		manifestPath = filepath.Join(extensionPaths[extType], dirName, extType+".json")
	}

	return s.readManifestFromFile(manifestPath)
}

// GetManifestAsMap reads a manifest file and returns it as a map, useful for dynamic fields like "extends".
func (s *ExtensionService) GetManifestAsMap(extType, dirName string) (map[string]interface{}, error) {
	var manifestPath string
	if extType == TypeTheme {
		themePath, err := s.FindThemePath(dirName)
		if err != nil {
			return nil, err
		}
		manifestPath = filepath.Join(themePath, "theme.json")
	} else {
		manifestPath = filepath.Join(extensionPaths[extType], dirName, extType+".json")
	}

	data, err := os.ReadFile(manifestPath)
	if err != nil {
		return nil, fmt.Errorf("could not read manifest file %s: %w", manifestPath, err)
	}

	var manifestMap map[string]interface{}
	if err := json.Unmarshal(data, &manifestMap); err != nil {
		return nil, fmt.Errorf("could not parse manifest map %s: %w", manifestPath, err)
	}

	return manifestMap, nil
}

// Uninstall deletes an extension's directory from the filesystem and removes it from the database.
func (s *ExtensionService) Uninstall(extType, dirName string) error {
	ext, err := s.extRepo.FindByDirName(dirName)
	if err != nil {
		return err
	}

	if err := s.GetManifest(extType, dirName); err != nil {
		return err
	}

	var extPath string
	if extType == TypeTheme {
		path, err := s.FindThemePath(dirName)
		if err != nil {
			return err
		}
		extPath = path
	} else {
		extPath = filepath.Join(extensionPaths[extType], dirName)
	}

	if err := os.RemoveAll(extPath); err != nil {
		return fmt.Errorf("failed to delete extension directory: %w", err)
	}

	return nil
}

// InstallFromUpload handles the upload and installation of an extension zip file.
func (s *ExtensionService) InstallFromUpload(extType string, fileHeader *multipart.FileHeader) error {
	// 1. Save the uploaded zip file to a temporary location
	tmpZipFile, err := s.saveTempFile(fileHeader)
	if err != nil {
		return fmt.Errorf("failed to save temporary file: %w", err)
	}
	defer os.Remove(tmpZipFile) // Clean up the temp zip file

	// 2. Unzip the file to a temporary directory
	unzipDir := filepath.Join(tempInstallPath, strings.TrimSuffix(fileHeader.Filename, ".zip"))
	if err := s.unzip(tmpZipFile, unzipDir); err != nil {
		return fmt.Errorf("failed to unzip file: %w", err)
	}
	defer os.RemoveAll(unzipDir) // Clean up the unzipped files

	// 3. Validate the package
	manifest, err := s.validatePackage(unzipDir, extType)
	if err != nil {
		return fmt.Errorf("package validation failed: %w", err)
	}

	// 4. Move the validated package to the final destination directory
	finalPath := filepath.Join(extensionPaths[extType], manifest.DirectoryName)
	if _, err := os.Stat(finalPath); err == nil {
		return fmt.Errorf("extension '%s' already exists", manifest.DirectoryName)
	}

	// This logic needs to be smarter, finding the actual content folder within the zip.
	// For now, let's assume the unzipped content is the folder to be moved.
	if err := os.Rename(unzipDir, finalPath); err != nil {
		return fmt.Errorf("failed to move extension to destination: %w", err)
	}

	return nil
}

func (s *ExtensionService) saveTempFile(fileHeader *multipart.FileHeader) (string, error) {
	src, err := fileHeader.Open()
	if err != nil {
		return "", err
	}
	defer src.Close()

	tmpFile, err := os.CreateTemp(tempInstallPath, "upload-*.zip")
	if err != nil {
		return "", err
	}
	defer tmpFile.Close()

	_, err = io.Copy(tmpFile, src)
	if err != nil {
		os.Remove(tmpFile.Name())
		return "", err
	}

	return tmpFile.Name(), nil
}

func (s *ExtensionService) unzip(src, dest string) error {
	r, err := zip.OpenReader(src)
	if err != nil {
		return err
	}
	defer r.Close()

	os.MkdirAll(dest, os.ModePerm)

	for _, f := range r.File {
		fpath := filepath.Join(dest, f.Name)

		if !strings.HasPrefix(fpath, filepath.Clean(dest)+string(os.PathSeparator)) {
			return fmt.Errorf("illegal file path: %s", fpath)
		}

		if f.FileInfo().IsDir() {
			os.MkdirAll(fpath, os.ModePerm)
			continue
		}

		if err := os.MkdirAll(filepath.Dir(fpath), os.ModePerm); err != nil {
			return err
		}

		outFile, err := os.OpenFile(fpath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())
		if err != nil {
			return err
		}

		rc, err := f.Open()
		if err != nil {
			outFile.Close()
			return err
		}

		_, err = io.Copy(outFile, rc)

		outFile.Close()
		rc.Close()

		if err != nil {
			return err
		}
	}
	return nil
}

func (s *ExtensionService) validatePackage(unzipDir, extType string) (*ExtensionManifest, error) {
	manifestPath := filepath.Join(unzipDir, extType+".json")
	if extType == TypeTheme {
		manifestPath = filepath.Join(unzipDir, "theme.json")
	}

	manifest, err := s.readManifestFromFile(manifestPath)
	if err != nil {
		return nil, err
	}

	// Basic validation
	if manifest.Name == "" || manifest.Version == "" {
		return nil, errors.New("manifest file is missing required fields (name, version)")
	}

	// For simplicity, we use the manifest name as the directory name.
	// A better approach might be to derive it from the zip file name or a field in the manifest.
	manifest.DirectoryName = strings.ToLower(strings.ReplaceAll(manifest.Name, " ", "-"))

	return manifest, nil
}

func (s *ExtensionService) scanForManifests(basePath, extType string) (map[string]*ExtensionManifest, error) {
	manifests := make(map[string]*ExtensionManifest)
	manifestFileName := ""
	if extType == TypeTheme {
		manifestFileName = "theme.json"
	} else {
		manifestFileName = extType + ".json"
	}

	// Special handling for themes in subdirectories
	if extType == TypeTheme {
		frontendPath := filepath.Join(basePath, "frontend")
		backendPath := filepath.Join(basePath, "backend")
		
		s.scanManifestsInDir(frontendPath, manifestFileName, manifests)
		s.scanManifestsInDir(backendPath, manifestFileName, manifests)

		return manifests, nil
	}

	s.scanManifestsInDir(basePath, manifestFileName, manifests)
	return manifests, nil
}

func (s *ExtensionService) scanManifestsInDir(dir, manifestName string, manifests map[string]*ExtensionManifest) {
	entries, err := os.ReadDir(dir)
	if err != nil {
		return
	}

	for _, entry := range entries {
		if entry.IsDir() {
			manifestPath := filepath.Join(dir, entry.Name(), manifestName)
			if manifest, err := s.readManifestFromFile(manifestPath); err == nil {
				manifests[entry.Name()] = manifest
			}
		}
	}
}

// List returns a list of extensions for a given type.
func (s *ExtensionService) List(extensionType string) ([]string, error) {
	strategy, ok := s.strategies[extensionType]
	if !ok {
		return nil, fmt.Errorf("unsupported extension type: %s", extensionType)
	}
	return strategy.List()
}