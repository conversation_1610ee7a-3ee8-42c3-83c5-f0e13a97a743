1. 项目名称：亘安网站内容管理系统（GACMS）
2. 项目代号：GACMS
3. 项目负责人：Clion Nieh
4. 遵循通用原则：
    4.1 接口隔离原则（ISP），为每个功能定义独立接口，避免“胖接口”
    4.2 单一职责原则（SRP），每个方法只做一件事，每个类只负责一项功能
    4.3 依赖倒置原则（DIP），控制器应依赖接口而非具体实现，上层模块不依赖底层模块，都依赖抽象，避免“依赖地狱”
    4.4 开放封闭原则（OCP），对扩展开放，对修改封闭
    4.5 里氏替换原则（LSP），子类可以替换父类”
    4.6 迪米特法则（LoD），一个对象应该对其他对象有最少的了解，避免“高耦合”
    4.7 文档驱动开发（DDD），在开发过程中，文档是最重要的,所有开发工作基于书面规范进行
    4.8 自动化测试（AT），在开发过程中，自动化测试是必不可少的
    4.9 版本控制（VC），在开发过程中，始终不使用url或者目录区分版本
    4.10 代码规范（CS），在开发过程中，代码规范是必不可少的
    4.11 代码重构（CR），在开发过程中，代码重构是必不可少的
    4.12 代码优化（CO），在开发过程中，代码优化是必不可少的
    4.13 代码注释（CC），在开发过程中，代码注释是必不可少的
    4.14 代码可读性（CR），在开发过程中，代码可读性是必不可少的
    4.15 代码可维护性（CM），在开发过程中，代码可维护性是必不可少的
    4.16 代码可扩展性（CE），在开发过程中，代码可扩展性是必不可少的
    4.17 代码可重用性（CR），在开发过程中，代码可重用性是必不可少的
    4.18 代码可测试性（CT），在开发过程中，代码可测试性是必不可少的
    4.19 代码可移植性（CP），在开发过程中，代码可移植性是必不可少的
    4.20 代码可调试性（CD），在开发过程中，代码可调试性是必不可少的
    4.21 契约优先开发（CPD），在开发过程中，先定义接口，再实现具体类
    4.22 模型优先原则（MPD），在开发过程中，先定义领域模型再实现业务逻辑
    4.23 分层架构原则（LAP），在开发过程中，遵循分层架构原则，每一层只处理与其职责相关的逻辑
    4.24 领域驱动设计（DDD），在开发过程中，遵循领域驱动设计原则，将业务逻辑与领域模型分离
    4.25 松耦合与高内聚：在开发过程中，遵循高内聚低耦合原则，层与层之间通过接口通信 ，同一层内的组件功能高度相关 ，减少冗余逻辑
    4.26 统一响应格式：在开发过程中，遵循统一响应格式，所有接口返回的数据格式相同
    4.27 全局异常处理：在开发过程中，遵循全局异常处理原则，所有异常都被捕获并处理
    4.28 统一日志管理，在开发过程中，总是使用文件日志，而非使用数据库日志
    4.29 多语言支持总是使用统一的URL，不要在URL中使用语言标识，如：/en/index.html
    4.30 始终不控制API版本，始终使用最新的API版本，不要在URL中使用API版本，如：/api/v1/index.html    
5. 遵循通用规范：
    5.1 代码风格：
        5.1.1 缩进：4个空格
        5.1.2 命名：
            5.1.2.1 变量：小驼峰命名法
            5.1.2.2 函数：小驼峰命名法
            5.1.2.3 类：大驼峰命名法，与文件名一致
            5.1.2.4 常量：全大写
            5.1.2.5 注释：
                5.1.2.5.1 单行注释：//
                5.1.2.5.2 多行注释：/**/
            5.1.2.6 路径：小驼峰命名法
            5.1.2.7 文件：大驼峰命名法
            5.1.2.8 命名空间：大驼峰命名法
            
        5.1.3 注释：
            5.1.3.1 函数：
                5.1.3.1.1 输入参数：@param
                5.1.3.1.2 输出参数：@return
                5.1.3.1.3 异常：@throws
            5.1.3.2 类：@class
            5.1.3.3 方法：@method
            5.1.3.4 变量：@var
            5.1.3.5 常量：@const
6. 技术规范：
    6.1 前端：
        6.1.1 框架：React
        6.1.2 组件库：Ant Design
        6.1.3 状态管理：Redux
        6.1.4 路由：React Router
        6.1.5 国际化：i18n
        6.1.6 表单验证：Formik
        6.1.7 图表：ECharts
        6.1.8 地图：Leaflet
        6.1.9 编辑器：TinyMCE
        6.1.10 图片上传：Cloudinary
        6.1.11 视频上传：Vimeo
        6.1.12 音频上传：SoundCloud
        6.1.13 文档上传：Google Drive
        6.1.14 数据可视化：D3.js
        6.1.15 数据表格：Ant Design Table
        6.1.16 数据图表：Ant Design Chart
        6.1.17 数据地图：Ant Design Map
        6.1.18 数据表单：Ant Design Form
        6.1.19 数据布局：Ant Design Layout
        6.1.20 数据导航：Ant Design Nav
        6.1.21 数据分页：Ant Design Pagination
        6.1.22 数据筛选：Ant Design Filter
        6.1.23 数据排序：Ant Design Sort
        6.1.24 数据搜索：Ant Design Search
        6.1.25 数据统计：Ant Design Stat
        6.1.26 数据表格：Ant Design Table
        6.1.27 数据图表：Ant Design Chart
        6.1.28 数据地图：Ant Design Map
    6.2 后端：
        6.2.1 框架：Gin
        6.2.2 数据库：MySQL
        6.2.3 缓存：Redis
        6.2.4 负载均衡：Nginx
        6.2.5 容器化：Docker
        6.2.6 部署：Kubernetes
        6.2.7 监控：Prometheus