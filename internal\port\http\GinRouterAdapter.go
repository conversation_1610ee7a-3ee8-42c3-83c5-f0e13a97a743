/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/port/http/GinRouterAdapter.go
 * @Description: Implements the IRouter interface using the Gin web framework.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package http

import (
	"gacms/internal/core/contract"
	"github.com/gin-gonic/gin"
)

// GinRouterAdapter is an adapter that makes gin.Engine compatible with the IRouter interface.
type GinRouterAdapter struct {
	engine *gin.Engine
}

// NewGinRouterAdapter creates a new adapter.
func NewGinRouterAdapter(engine *gin.Engine) contract.IRouter {
	return &GinRouterAdapter{engine: engine}
}

// Group creates a new route group.
func (a *GinRouterAdapter) Group(path string) contract.IRouterGroup {
	return &GinRouterGroupAdapter{group: a.engine.Group(path)}
}

// GinRouterGroupAdapter adapts a gin.RouterGroup to the IRouterGroup interface.
type GinRouterGroupAdapter struct {
	group *gin.RouterGroup
}

// Use adds middleware to the group.
func (g *GinRouterGroupAdapter) Use(middleware ...interface{}) {
	for _, m := range middleware {
		if ginMiddleware, ok := m.(gin.HandlerFunc); ok {
			g.group.Use(ginMiddleware)
		}
	}
}

// Any registers a route that matches all HTTP methods.
func (g *GinRouterGroupAdapter) Any(relativePath string, handler interface{}) {
	if h, ok := handler.(gin.HandlerFunc); ok {
		g.group.Any(relativePath, h)
	}
}

// GET registers a route for GET requests.
func (g *GinRouterGroupAdapter) GET(relativePath string, handler interface{}) {
	if h, ok := handler.(gin.HandlerFunc); ok {
		g.group.GET(relativePath, h)
	}
}

// POST registers a route for POST requests.
func (g *GinRouterGroupAdapter) POST(relativePath string, handler interface{}) {
	if h, ok := handler.(gin.HandlerFunc); ok {
		g.group.POST(relativePath, h)
	}
}

// DELETE registers a route for DELETE requests.
func (g *GinRouterGroupAdapter) DELETE(relativePath string, handler interface{}) {
	if h, ok := handler.(gin.HandlerFunc); ok {
		g.group.DELETE(relativePath, h)
	}
}

// PATCH registers a route for PATCH requests.
func (g *GinRouterGroupAdapter) PATCH(relativePath string, handler interface{}) {
	if h, ok := handler.(gin.HandlerFunc); ok {
		g.group.PATCH(relativePath, h)
	}
}

// PUT registers a route for PUT requests.
func (g *GinRouterGroupAdapter) PUT(relativePath string, handler interface{}) {
	if h, ok := handler.(gin.HandlerFunc); ok {
		g.group.PUT(relativePath, h)
	}
} 