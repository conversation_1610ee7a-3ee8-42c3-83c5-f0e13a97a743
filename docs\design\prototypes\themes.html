<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 主题管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .content-section-title::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 5rem;
            height: 3px;
            background: linear-gradient(to right, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .user-link::after, .article-link::after, .version-link::after, .plugin-link::after, .comment-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 1px;
            background: linear-gradient(to right, #007bff, #00c6ff);
            transition: width 0.2s ease;
        }
        
        .user-link:hover, .article-link:hover, .version-link:hover, .plugin-link:hover, .comment-link:hover {
            color: #00c6ff;
        }
        
        .user-link:hover::after, .article-link:hover::after, .version-link:hover::after, .plugin-link:hover::after, .comment-link:hover::after {
            width: 100%;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            background: linear-gradient(145deg, #1e1e1e, #2a2a2a);
            border-left: 4px solid #007bff;
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            backdrop-filter: blur(20px);
            z-index: 1000;
            transform: translateX(120%);
            opacity: 0;
            transition: all 0.2s ease;
            max-width: 350px;
        }
        
        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">主题管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <a href="theme_market.html" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-store text-white"></i>
                                </span>
                                浏览主题市场
                            </span>
                        </a>
                        <a href="#" class="flex items-center justify-center bg-gradient-to-r from-green-500 to-green-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-green-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-upload text-white"></i>
                                </span>
                                上传新主题
                            </span>
                        </a>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="relative w-full md:w-96">
                        <input type="text" placeholder="搜索主题..." 
                               class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent pl-10">
                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 当前主题信息 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold text-white mb-4">当前主题</h3>
                <div class="flex flex-wrap lg:flex-nowrap gap-6">
                    <div class="theme-preview w-full lg:w-1/3">
                        <img src="./assets/images/theme-modern-dark.jpg" alt="Modern Dark Theme" class="w-full h-48 object-cover rounded-lg border border-gray-700">
                    </div>
                    <div class="theme-info flex-1">
                        <div class="flex justify-between items-start mb-3">
                            <h4 class="text-xl font-bold text-blue-400">Modern Dark</h4>
                            <span class="bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-xs font-medium">
                                已激活
                            </span>
                        </div>
                        <p class="text-gray-300 mb-3">一款专为现代企业网站设计的深色主题，提供出色的视觉效果和用户体验。</p>
                        <div class="flex flex-wrap gap-2 mb-3">
                            <span class="bg-gray-700/50 px-2 py-1 rounded text-xs">响应式</span>
                            <span class="bg-gray-700/50 px-2 py-1 rounded text-xs">深色模式</span>
                            <span class="bg-gray-700/50 px-2 py-1 rounded text-xs">多语言</span>
                            <span class="bg-gray-700/50 px-2 py-1 rounded text-xs">SEO优化</span>
                            <span class="bg-gray-700/50 px-2 py-1 rounded text-xs">高性能</span>
                        </div>
                        <div class="grid grid-cols-2 gap-4 text-sm mb-4">
                            <div>
                                <span class="text-gray-400">版本:</span>
                                <span class="text-white ml-2">2.5.0</span>
                            </div>
                            <div>
                                <span class="text-gray-400">作者:</span>
                                <a href="#" class="text-blue-400 hover:text-blue-300 ml-2">GACMS Team</a>
                            </div>
                            <div>
                                <span class="text-gray-400">最近更新:</span>
                                <span class="text-white ml-2">2025-03-15</span>
                            </div>
                            <div>
                                <span class="text-gray-400">安装日期:</span>
                                <span class="text-white ml-2">2025-04-01</span>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-3">
                            <a href="#" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition">
                                <i class="fas fa-cog mr-2"></i>主题设置
                            </a>
                            <a href="#" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition">
                                <i class="fas fa-paint-brush mr-2"></i>自定义
                            </a>
                            <a href="#" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition">
                                <i class="fas fa-file-alt mr-2"></i>文档
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 已安装主题列表 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold text-white mb-4">已安装主题</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- 主题卡片 1 -->
                    <div class="theme-card border border-gray-700 rounded-lg overflow-hidden bg-gray-800/20 hover:bg-gray-800/30 transition-all">
                        <div class="relative">
                            <img src="./assets/images/theme-light-clean.jpg" alt="Light Clean Theme" class="w-full h-48 object-cover">
                            <div class="absolute top-3 right-3">
                                <span class="bg-gray-900/60 text-gray-200 px-2 py-1 rounded text-xs backdrop-blur-sm">
                                    未激活
                                </span>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="font-bold text-white">Light Clean</h4>
                                <span class="text-xs text-gray-400">v1.8.0</span>
                            </div>
                            <p class="text-sm text-gray-400 mb-3 line-clamp-2">简约明亮的企业网站主题，适合展示产品和服务。</p>
                            <div class="flex justify-between items-center">
                                <div class="flex space-x-2">
                                    <button class="text-blue-400 hover:text-blue-300 text-sm">
                                        <i class="fas fa-check-circle mr-1"></i>启用
                                    </button>
                                    <button class="text-gray-400 hover:text-gray-300 text-sm">
                                        <i class="fas fa-cog mr-1"></i>设置
                                    </button>
                                </div>
                                <div class="dropdown relative">
                                    <button class="text-gray-400 hover:text-white p-1">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 主题卡片 2 -->
                    <div class="theme-card border border-gray-700 rounded-lg overflow-hidden bg-gray-800/20 hover:bg-gray-800/30 transition-all">
                        <div class="relative">
                            <img src="./assets/images/theme-corporate.jpg" alt="Corporate Theme" class="w-full h-48 object-cover">
                            <div class="absolute top-3 right-3">
                                <span class="bg-gray-900/60 text-gray-200 px-2 py-1 rounded text-xs backdrop-blur-sm">
                                    未激活
                                </span>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="font-bold text-white">Corporate Pro</h4>
                                <span class="text-xs text-gray-400">v2.1.0</span>
                            </div>
                            <p class="text-sm text-gray-400 mb-3 line-clamp-2">专业企业形象主题，注重业务展示和转化。</p>
                            <div class="flex justify-between items-center">
                                <div class="flex space-x-2">
                                    <button class="text-blue-400 hover:text-blue-300 text-sm">
                                        <i class="fas fa-check-circle mr-1"></i>启用
                                    </button>
                                    <button class="text-gray-400 hover:text-gray-300 text-sm">
                                        <i class="fas fa-cog mr-1"></i>设置
                                    </button>
                                </div>
                                <div class="dropdown relative">
                                    <button class="text-gray-400 hover:text-white p-1">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 主题卡片 3 -->
                    <div class="theme-card border border-gray-700 rounded-lg overflow-hidden bg-gray-800/20 hover:bg-gray-800/30 transition-all">
                        <div class="relative">
                            <img src="./assets/images/theme-news.jpg" alt="News Portal Theme" class="w-full h-48 object-cover">
                            <div class="absolute top-3 right-3">
                                <span class="bg-gray-900/60 text-gray-200 px-2 py-1 rounded text-xs backdrop-blur-sm">
                                    未激活
                                </span>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="font-bold text-white">News Portal</h4>
                                <span class="text-xs text-gray-400">v3.2.1</span>
                            </div>
                            <p class="text-sm text-gray-400 mb-3 line-clamp-2">新闻门户网站主题，优化内容展示和阅读体验。</p>
                            <div class="flex justify-between items-center">
                                <div class="flex space-x-2">
                                    <button class="text-blue-400 hover:text-blue-300 text-sm">
                                        <i class="fas fa-check-circle mr-1"></i>启用
                                    </button>
                                    <button class="text-gray-400 hover:text-gray-300 text-sm">
                                        <i class="fas fa-cog mr-1"></i>设置
                                    </button>
                                </div>
                                <div class="dropdown relative">
                                    <button class="text-gray-400 hover:text-white p-1">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <!-- 通知组件 -->
    <div class="notification" id="notification">
        <div class="mr-3">
            <i class="fas fa-check-circle text-green-400 text-xl"></i>
        </div>
        <div class="flex-1">
            <h4 class="text-white text-sm font-bold">操作成功</h4>
            <p class="text-gray-300 text-xs">主题切换成功，刷新页面查看效果。</p>
        </div>
    </div>

    <!-- 加载必要的JS -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
</body>
</html>