/*
 * @Author: <PERSON><PERSON>eh <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: Clion Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: pkg/contract/EventManager.go
 * @Description: 定义事件管理器接口，作为事件系统的主要入口点
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import "context"

// EventManager 定义了事件管理系统的主要入口点
// 它封装了事件总线的功能，并提供了额外的管理功能
type EventManager interface {
	// RegisterHandler 注册一个事件处理器
	RegisterHandler(handler EventHandler) error

	// UnregisterHandler 取消注册一个事件处理器
	UnregisterHandler(handler EventHandler) error

	// PublishEvent 发布一个事件
	// 同步模式，等待所有处理器处理完成
	PublishEvent(event Event) error

	// PublishEventAsync 异步发布一个事件
	// 立即返回，不等待处理完成
	PublishEventAsync(ctx context.Context, event Event) <-chan error

	// CreateEvent 创建一个新的事件实例
	// 提供便捷方法来创建标准事件
	CreateEvent(ctx context.Context, name EventName, payload interface{}) Event

	// GetEventBus 获取底层的事件总线实例
	GetEventBus() EventBus

	// GetHandlerRegistry 获取处理器注册表
	GetHandlerRegistry() EventHandlerRegistry
}

// EventHandlerRegistry 定义了事件处理器注册表接口
type EventHandlerRegistry interface {
	// RegisterHandler 注册一个事件处理器
	RegisterHandler(handler EventHandler) error

	// UnregisterHandler 取消注册一个事件处理器
	UnregisterHandler(handler EventHandler) error

	// GetHandlersForEvent 获取指定事件的所有处理器
	GetHandlersForEvent(eventName EventName) []EventHandler

	// HasHandlersForEvent 检查是否有处理器注册了指定事件
	HasHandlersForEvent(eventName EventName) bool

	// GetAllHandlers 获取所有注册的处理器
	GetAllHandlers() []EventHandler
}

// EventManagerFactory 定义了创建EventManager实例的工厂接口
type EventManagerFactory interface {
	// CreateEventManager 创建一个新的EventManager实例
	CreateEventManager() EventManager
} 