/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/extension/application/strategy/PluginEnablingStrategy.go
 * @Description: Implements the enabling strategy for plugins via config file.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package strategy

import (
	"encoding/json"
	"fmt"
	"gacms/internal/modules/extension/domain/contract"
	"os"
	"sync"
)

// PluginEnablingStrategy handles plugin enabling/disabling by editing a config file.
type PluginEnablingStrategy struct {
	mu sync.Mutex
}

func NewPluginEnablingStrategy() contract.EnablingStrategy {
	return &PluginEnablingStrategy{}
}

func (s *PluginEnablingStrategy) readConfig() (*enabledExtensions, error) {
	data, err := os.ReadFile(extensionsConfigFile)
	if err != nil {
		if os.IsNotExist(err) {
			return &enabledExtensions{Modules: []string{}, Plugins: []string{}}, nil
		}
		return nil, err
	}
	var cfg enabledExtensions
	if err := json.Unmarshal(data, &cfg); err != nil {
		return nil, err
	}
	return &cfg, nil
}

func (s *PluginEnablingStrategy) writeConfig(cfg *enabledExtensions) error {
	data, err := json.MarshalIndent(cfg, "", "  ")
	if err != nil {
		return err
	}
	return os.WriteFile(extensionsConfigFile, data, 0644)
}

func (s *PluginEnablingStrategy) Enable(extName string, siteID uint) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	cfg, err := s.readConfig()
	if err != nil {
		return fmt.Errorf("failed to read extensions config: %w", err)
	}

	for _, plug := range cfg.Plugins {
		if plug == extName {
			return nil // Already enabled
		}
	}

	cfg.Plugins = append(cfg.Plugins, extName)
	return s.writeConfig(cfg)
}

func (s *PluginEnablingStrategy) Disable(extName string, siteID uint) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	cfg, err := s.readConfig()
	if err != nil {
		return fmt.Errorf("failed to read extensions config: %w", err)
	}

	found := false
	var updatedPlugins []string
	for _, plug := range cfg.Plugins {
		if plug == extName {
			found = true
		} else {
			updatedPlugins = append(updatedPlugins, plug)
		}
	}

	if !found {
		return fmt.Errorf("plugin '%s' is not currently enabled", extName)
	}

	cfg.Plugins = updatedPlugins
	return s.writeConfig(cfg)
} 