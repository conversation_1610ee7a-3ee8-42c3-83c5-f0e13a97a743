/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-09
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/contenttype/application/service/ContentTypeService.go
 * @Description: Service for managing content type definitions and their permissions.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"encoding/json"
	"fmt"
	"gacms/internal/modules/contenttype/domain/contract"
	"gacms/internal/modules/contenttype/domain/model"
	pkgContract "gacms/pkg/contract"
	"gacms/pkg/contract/events"
	"gorm.io/gorm"
)

// ContentTypeService provides business logic for managing content types.
type ContentTypeService struct {
	repo          contract.ContentTypeRepository
	extensionRepo contract.SiteContentTypeExtensionRepository
	appCtx        pkgContract.AppContext
}

// NewContentTypeService creates a new ContentTypeService.
func NewContentTypeService(
	repo contract.ContentTypeRepository,
	extensionRepo contract.SiteContentTypeExtensionRepository,
	appCtx pkgContract.AppContext,
) *ContentTypeService {
	return &ContentTypeService{
		repo:          repo,
		extensionRepo: extensionRepo,
		appCtx:        appCtx,
	}
}

// CreateContentType creates a new content type, publishes an event, and logs the action.
func (s *ContentTypeService) CreateContentType(contentType *model.ContentType) error {
	if err := s.repo.Create(contentType); err != nil {
		return err
	}

	// Publish an event to notify other modules (like 'user') that a new
	// content type has been created, so they can react accordingly (e.g., create permissions).
	event := events.NewGenericEvent("content_type.created", contentType)
	s.appCtx.Events().Publish(event)

	// Log the action via API.
	s.recordActionLog(context.Background(), fmt.Sprintf("Created content type: %s", contentType.Name), "success")

	return nil
}

// UpdateContentType updates an existing content type, publishes an event, and logs the action.
func (s *ContentTypeService) UpdateContentType(contentType *model.ContentType) error {
	if err := s.repo.Update(contentType); err != nil {
		return err
	}

	// Publish an event.
	event := events.NewGenericEvent("content_type.updated", contentType)
	s.appCtx.Events().Publish(event)

	// Log the action.
	s.recordActionLog(context.Background(), fmt.Sprintf("Updated content type: %s (ID: %d)", contentType.Name, contentType.ID), "success")

	return nil
}

// DeleteContentType deletes a content type, publishes an event, and logs the action.
func (s *ContentTypeService) DeleteContentType(id uint) error {
	// It's good practice to fetch the entity before deleting to know what was deleted.
	contentType, err := s.repo.GetByID(id)
	if err != nil {
		return err
	}
	
	if err := s.repo.Delete(id); err != nil {
		return err
	}

	// Publish an event.
	event := events.NewGenericEvent("content_type.deleted", contentType)
	s.appCtx.Events().Publish(event)

	// Log the action.
	s.recordActionLog(context.Background(), fmt.Sprintf("Deleted content type: %s (ID: %d)", contentType.Name, contentType.ID), "success")

	return nil
}

// recordActionLog is a helper to asynchronously record an action to the actionlog service.
func (s *ContentTypeService) recordActionLog(ctx context.Context, description string, status string) {
	go func() {
		payload := map[string]string{
			"description": description,
			"status":      status,
		}
		_, err := s.appCtx.APIClient().Post(ctx, "actionlog", "/logs", payload)
		if err != nil {
			// Use the system logger to record the failure of the API call.
			s.appCtx.Logger().Error(context.Background(), "Failed to record action log via API", "error", err)
		}
	}()
}

// syncContentTypePermissions is removed as it's not the responsibility of this module.
// This logic will be moved to a listener in the 'user' module.
/*
func (s *ContentTypeService) syncContentTypePermissions(slug string) error {
	moduleName := "content" // All dynamic types fall under the 'content' permission scope.
	permissions := []userModel.AdminPermission{
		{Module: moduleName, Name: fmt.Sprintf("%s:%s:create", moduleName, slug), Description: fmt.Sprintf("Create new %s entries", slug)},
		{Module: moduleName, Name: fmt.Sprintf("%s:%s:read", moduleName, slug), Description: fmt.Sprintf("Read %s entries", slug)},
		{Module: moduleName, Name: fmt.Sprintf("%s:%s:update", moduleName, slug), Description: fmt.Sprintf("Update %s entries", slug)},
		{Module: moduleName, Name: fmt.Sprintf("%s:%s:delete", moduleName, slug), Description: fmt.Sprintf("Delete %s entries", slug)},
		{Module: moduleName, Name: fmt.Sprintf("%s:%s:publish", moduleName, slug), Description: fmt.Sprintf("Publish %s entries", slug)},
	}

	return s.adminRepo.SyncPermissions(permissions)
}
*/

// GetByID retrieves a content type by its ID.
func (s *ContentTypeService) GetByID(id uint) (*model.ContentType, error) {
	return s.repo.GetByID(id)
}

// GetBySlug retrieves a content type by its slug.
func (s *ContentTypeService) GetBySlug(slug string) (*model.ContentType, error) {
	return s.repo.GetBySlug(slug)
}

// GetBySlugForSite retrieves a content type for a specific site, merging global fields with site-specific ones.
func (s *ContentTypeService) GetBySlugForSite(slug string, siteID uint) (*model.ContentType, error) {
	// 1. Get the base content type
	baseType, err := s.repo.GetBySlug(slug)
	if err != nil {
		return nil, err
	}

	// 2. Find site-specific extensions
	extension, err := s.extensionRepo.FindBySiteAndContentType(siteID, baseType.ID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// No extension found, return the base type as is.
			return baseType, nil
		}
		// A real database error occurred.
		return nil, err
	}

	// 3. Merge fields if an extension exists
	mergedType, err := s.mergeFields(baseType, extension)
	if err != nil {
		return nil, err
	}

	return mergedType, nil
}

// mergeFields combines the base fields with the extended fields.
func (s *ContentTypeService) mergeFields(base *model.ContentType, extension *model.SiteContentTypeExtension) (*model.ContentType, error) {
	if extension.ExtraFields == nil {
		return base, nil
	}

	var extraFields []model.Field
	if err := json.Unmarshal(extension.ExtraFields, &extraFields); err != nil {
		return nil, err
	}

	// Create a copy to avoid modifying the original base type pointer
	mergedType := *base
	mergedType.Fields = append(mergedType.Fields, extraFields...)
	
	return &mergedType, nil
}

// GetAll retrieves all content types.
func (s *ContentTypeService) GetAll() ([]*model.ContentType, error) {
	return s.repo.GetAll()
}

// You can add more service methods here for Update, Delete, etc.
// When deleting a content type, you might also want to delete its associated permissions.

func (s *ContentTypeService) GetBySlug(slug string) (*model.ContentType, error) {
	return s.repo.FindBySlug(slug)
}

func (s *ContentTypeService) GetAll() ([]*model.ContentType, error) {
	return s.repo.FindAll()
}

// ... other methods to wrap repository functions ... 