<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 评论编辑</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .comment-content {
            background-color: rgba(75, 85, 99, 0.2);
            border-radius: 8px;
            border: 1px solid rgba(75, 85, 99, 0.3);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="content.html" class="hover:text-white">内容管理</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="comments.html" class="hover:text-white">评论管理</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">编辑评论</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">编辑评论</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="saveBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-save text-white"></i>
                                </span>
                                保存
                            </span>
                        </button>
                        <a href="comments.html" class="flex items-center justify-center bg-gradient-to-r from-gray-600 to-gray-700 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-gray-600/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-times text-white"></i>
                                </span>
                                取消
                            </span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 评论编辑表单 -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                <!-- 左侧：评论内容 -->
                <div class="lg:col-span-2">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">评论内容</h3>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">评论文本 <span class="text-red-500">*</span></label>
                            <textarea id="commentText" rows="6" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">我认为这篇文章提供了很好的见解。特别是关于数字化转型的部分讲得非常到位，对企业确实有很大的启发。不过我也有一些想法想补充，在实施数字化转型时，企业还需要考虑员工培训和适应的问题，这是很多公司容易忽略的一点。总体来说，这是篇很有价值的文章，期待后续更多相关内容！</textarea>
                        </div>
                        
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">作者名称</label>
                                <input type="text" id="authorName" value="张明" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">电子邮箱</label>
                                <input type="email" id="authorEmail" value="<EMAIL>" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">用户网站</label>
                                <input type="url" id="authorWebsite" value="https://example.com" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">IP地址</label>
                                <input type="text" id="ipAddress" value="************" readonly class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-not-allowed opacity-75">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 回复区域 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">回复</h3>
                        
                        <div class="mb-6">
                            <label class="block text-gray-300 mb-2">回复内容</label>
                            <textarea id="replyText" rows="4" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="输入回复内容..."></textarea>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <label class="flex items-center">
                                <input type="checkbox" id="notifyAuthor" class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-900 w-5 h-5">
                                <span class="ml-2 text-gray-300">发送邮件通知评论作者</span>
                            </label>
                            
                            <button class="flex items-center justify-center bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-2 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-green-500/30">
                                <i class="fas fa-reply mr-2"></i> 发送回复
                            </button>
                        </div>
                    </div>
                    
                    <!-- 历史回复 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">历史回复</h3>
                        
                        <div class="space-y-4">
                            <div class="bg-gray-800/20 rounded-lg p-4">
                                <div class="flex justify-between items-start mb-2">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold mr-2">
                                            A
                                        </div>
                                        <span class="font-medium text-white">管理员</span>
                                        <span class="ml-2 text-xs text-gray-400">2025-05-02 14:30</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-white transition-colors">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-400 transition-colors">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="pl-10">
                                    <p class="text-gray-300">感谢您的评论和建议！您提到的关于员工培训的观点非常重要，我们会在后续内容中详细探讨这方面的内容。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：信息与设置 -->
                <div class="lg:col-span-1">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">评论信息</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <p class="text-gray-400 mb-1">评论 ID</p>
                                <p class="text-white font-medium">42581</p>
                            </div>
                            
                            <div>
                                <p class="text-gray-400 mb-1">评论时间</p>
                                <p class="text-white font-medium">2025-05-01 09:45:23</p>
                            </div>
                            
                            <div>
                                <p class="text-gray-400 mb-1">所属文章</p>
                                <a href="article_edit.html?id=328" class="text-blue-400 hover:text-blue-300 font-medium break-all">数字化转型：企业创新的必由之路</a>
                            </div>
                            
                            <div>
                                <p class="text-gray-400 mb-1">用户类型</p>
                                <p class="text-white font-medium flex items-center">
                                    <span class="inline-block w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                                    注册用户
                                </p>
                            </div>
                            
                            <div>
                                <p class="text-gray-400 mb-1">用户 ID</p>
                                <a href="user_edit.html?id=8423" class="text-blue-400 hover:text-blue-300 font-medium">8423</a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 状态设置 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">状态设置</h3>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">评论状态</label>
                            <select id="commentStatus" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="approved" selected>已批准</option>
                                <option value="pending">待审核</option>
                                <option value="spam">垃圾评论</option>
                                <option value="trash">回收站</option>
                            </select>
                        </div>
                        
                        <div class="mb-4">
                            <label class="flex items-center">
                                <input type="checkbox" id="featured" checked class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-900 w-5 h-5">
                                <span class="ml-2 text-gray-300">设为精选评论</span>
                            </label>
                            <p class="text-gray-400 text-sm mt-1">精选评论将显示在文章评论区顶部</p>
                        </div>
                    </div>
                    
                    <!-- 防护与安全 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">防护与安全</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <p class="text-gray-400 mb-1">垃圾评论检测</p>
                                <p class="text-white font-medium flex items-center">
                                    <span class="inline-block w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                                    通过检测（得分: 0.2/10）
                                </p>
                            </div>
                            
                            <div>
                                <p class="text-gray-400 mb-1">敏感词检测</p>
                                <p class="text-white font-medium flex items-center">
                                    <span class="inline-block w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                                    未检测到敏感词
                                </p>
                            </div>
                            
                            <div>
                                <p class="text-gray-400 mb-1">用户评分</p>
                                <div class="flex items-center">
                                    <i class="fas fa-star text-yellow-400"></i>
                                    <i class="fas fa-star text-yellow-400"></i>
                                    <i class="fas fa-star text-yellow-400"></i>
                                    <i class="fas fa-star text-yellow-400"></i>
                                    <i class="fas fa-star-half-alt text-yellow-400"></i>
                                    <span class="ml-2 text-white">4.5/5</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 危险操作区 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <h3 class="text-lg font-semibold mb-4 text-white text-red-400">危险操作</h3>
                        
                        <button class="flex items-center justify-center bg-red-500/20 hover:bg-red-500/30 text-red-400 w-full py-3 rounded-lg transition-colors">
                            <i class="fas fa-trash-alt mr-2"></i> 删除此评论
                        </button>
                        <p class="text-gray-400 text-sm mt-2">删除后将无法恢复，包括此评论的所有回复</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 表单保存功能
            document.getElementById('saveBtn').addEventListener('click', function() {
                alert('评论保存成功！');
                // 在实际场景中，这里会有AJAX请求保存表单数据
            });
            
            // 评论状态变更时的处理
            document.getElementById('commentStatus').addEventListener('change', function() {
                const status = this.value;
                // 这里可以根据状态变化添加额外的UI反馈
                console.log('评论状态已变更为：' + status);
            });
        });
    </script>
</body>
</html> 