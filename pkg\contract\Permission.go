/*
 * @Author: <PERSON><PERSON>eh <<EMAIL>>
 * @Date: 2025-06-13
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-13
 * @FilePath: pkg/contract/Permission.go
 * @Description: Defines the public contract for permission checking services.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"context"
	"github.com/gin-gonic/gin"
)

// Permission 权限抽象接口
type Permission interface {
	GetID() uint
	GetSlug() string
	GetDescription() string
	GetModule() string
	GetScope() PermissionScope
}

// PermissionScope 权限范围
type PermissionScope string

const (
	GlobalScope PermissionScope = "global" // 全局权限
	SiteScope   PermissionScope = "site"   // 站点权限
	UserScope   PermissionScope = "user"   // 用户权限
)

// Role 角色抽象接口
type Role interface {
	GetID() uint
	GetName() string
	GetUserType() UserType
	GetSiteID() uint
	GetPermissions() []Permission
	HasPermission(permission string) bool
}

// PermissionChecker 权限检查器抽象接口
type PermissionChecker interface {
	// HasPermission 检查用户是否有指定权限
	HasPermission(ctx context.Context, user User, permission string) bool

	// HasAnyPermission 检查用户是否有任意一个权限
	HasAnyPermission(ctx context.Context, user User, permissions []string) bool

	// HasAllPermissions 检查用户是否有所有权限
	HasAllPermissions(ctx context.Context, user User, permissions []string) bool

	// Wrap 包装处理器，添加权限检查
	Wrap(permission string, handler gin.HandlerFunc) gin.HandlerFunc

	// WrapWithAny 包装处理器，检查任意权限
	WrapWithAny(permissions []string, handler gin.HandlerFunc) gin.HandlerFunc

	// WrapWithAll 包装处理器，检查所有权限
	WrapWithAll(permissions []string, handler gin.HandlerFunc) gin.HandlerFunc
}

// RoleManager 角色管理器抽象接口
type RoleManager interface {
	// GetUserRoles 获取用户角色
	GetUserRoles(ctx context.Context, userID uint, userType UserType) ([]Role, error)

	// AssignRole 分配角色给用户
	AssignRole(ctx context.Context, userID uint, userType UserType, roleID uint) error

	// RemoveRole 移除用户角色
	RemoveRole(ctx context.Context, userID uint, userType UserType, roleID uint) error

	// CreateRole 创建角色
	CreateRole(ctx context.Context, name string, userType UserType, siteID uint) (Role, error)

	// UpdateRole 更新角色
	UpdateRole(ctx context.Context, roleID uint, name string) error

	// DeleteRole 删除角色
	DeleteRole(ctx context.Context, roleID uint) error
}

// AuthorizationProvider 授权提供者抽象接口
type AuthorizationProvider interface {
	// GetName 获取提供者名称
	GetName() string

	// Authorize 执行授权检查
	Authorize(ctx context.Context, user User, resource string, action string) bool

	// GetUserPermissions 获取用户权限列表
	GetUserPermissions(ctx context.Context, user User) ([]Permission, error)

	// GetRolePermissions 获取角色权限列表
	GetRolePermissions(ctx context.Context, role Role) ([]Permission, error)
}