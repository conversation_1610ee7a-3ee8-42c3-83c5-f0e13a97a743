/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/post/domain/contract/PostRepository.go
 * @Description: Defines the contract for post data access.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"context"
	"gacms/internal/modules/post/domain/model"
)

// PostRepository defines the interface for post data operations.
type PostRepository interface {
	Create(ctx context.Context, post *model.Post) error
	GetByID(ctx context.Context, id uint) (*model.Post, error)
	GetBySlug(ctx context.Context, slug string) (*model.Post, error)
	// In a full implementation, you'd also have List, Update, Delete, etc.
} 