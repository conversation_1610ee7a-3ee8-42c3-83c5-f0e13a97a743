/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/user/infrastructure/repository/GormAdminRepository.go
 * @Description: GORM implementation of the AdminRepository interface.
 *
 * © 2025 GACMS. All rights reserved.
 */
package repository

import (
	"context"
	"gacms/internal/modules/user/domain"
	"gacms/internal/modules/user/domain/contract"
	"gacms/internal/modules/user/domain/model"
	dbContract "gacms/pkg/contract"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type gormAdminRepository struct {
	dbService dbContract.Database
}

// NewGormAdminRepository creates a new GORM-based implementation of AdminRepository.
func NewGormAdminRepository(dbService dbContract.Database) contract.AdminRepository {
	return &gormAdminRepository{dbService: dbService}
}

func (r *gormAdminRepository) GetAdminByUsername(ctx context.Context, username string) (*model.Admin, error) {
	var admin model.Admin
	db := r.dbService.DB(ctx)
	if err := db.Preload("Roles").First(&admin, "username = ?", username).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, domain.ErrNotFound
		}
		return nil, err
	}
	return &admin, nil
}

func (r *gormAdminRepository) CreateAdmin(ctx context.Context, admin *model.Admin) error {
	return r.dbService.DB(ctx).Create(admin).Error
}

func (r *gormAdminRepository) ListAdmins(ctx context.Context, page, pageSize int) ([]*model.Admin, int64, error) {
	var admins []*model.Admin
	var total int64
	db := r.dbService.DB(ctx).Model(&model.Admin{})
	
	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	err = db.Offset(offset).Limit(pageSize).Find(&admins).Error
	if err != nil {
		return nil, 0, err
	}

	return admins, total, nil
}

func (r *gormAdminRepository) GetAdminByID(ctx context.Context, id uuid.UUID) (*model.Admin, error) {
	var admin model.Admin
	db := r.dbService.DB(ctx)
	if err := db.Preload("Roles").First(&admin, "uuid = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, domain.ErrNotFound
		}
		return nil, err
	}
	return &admin, nil
}

func (r *gormAdminRepository) UpdateAdmin(ctx context.Context, admin *model.Admin) error {
	return r.dbService.DB(ctx).Save(admin).Error
}

func (r *gormAdminRepository) DeleteAdmin(ctx context.Context, id uuid.UUID) error {
	return r.dbService.DB(ctx).Delete(&model.Admin{}, "uuid = ?", id).Error
}

func (r *gormAdminRepository) AssignRolesToAdmin(ctx context.Context, adminID uuid.UUID, roleIDs []uint) error {
	admin, err := r.GetAdminByID(ctx, adminID)
	if err != nil {
		return err
	}
	
	db := r.dbService.DB(ctx)
	var roles []*model.Role
	if err := db.Where("id IN ?", roleIDs).Find(&roles).Error; err != nil {
		return err
	}

	return db.Model(admin).Association("Roles").Replace(roles)
}

func (r *gormAdminRepository) CountAdmins(ctx context.Context) (int64, error) {
	var total int64
	err := r.dbService.DB(ctx).Model(&model.Admin{}).Count(&total).Error
	return total, err
}

func (r *gormAdminRepository) GetByIDWithRoles(ctx context.Context, userID uint) (*model.Admin, error) {
	var admin model.Admin
	db := r.dbService.DB(ctx)
	if err := db.Preload("Roles.Permissions").First(&admin, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, domain.ErrNotFound
		}
		return nil, err
	}
	return &admin, nil
} 