<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 组件管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .component-card {
            background: linear-gradient(145deg, #1e1e1e, #2a2a2a);
            border-radius: 12px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.05);
            overflow: hidden;
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 25px rgba(0,0,0,0.3);
            border-color: rgba(0,123,255,0.3);
        }
        
        .component-card-header {
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .component-card-body {
            padding: 15px 20px;
            flex: 1;
        }
        
        .component-card-footer {
            padding: 15px 20px;
            border-top: 1px solid rgba(255,255,255,0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .component-preview {
            background-color: rgba(51, 51, 51, 0.5);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            min-height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .component-actions {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }
        
        .component-card:hover .component-actions {
            opacity: 1;
        }
        
        .component-action {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0,0,0,0.5);
            color: #fff;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .component-action:hover {
            background: rgba(0,123,255,0.8);
        }
        
        .component-badge {
            position: absolute;
            top: 10px;
            left: 10px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .component-badge.global {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
        }
        
        .component-badge.local {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
        }
        
        .component-badge.system {
            background: rgba(0, 123, 255, 0.2);
            color: #007bff;
        }
        
        .component-tabs {
            display: flex;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 20px;
        }
        
        .component-tab {
            padding: 10px 20px;
            cursor: pointer;
            color: #aaa;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }
        
        .component-tab:hover {
            color: #fff;
        }
        
        .component-tab.active {
            color: #007bff;
            border-bottom-color: #007bff;
        }
        
        /* 表单样式 */
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #e0e0e0;
        }
        
        .form-control {
            display: block;
            width: 100%;
            padding: 0.75rem 1rem;
            background-color: rgba(42, 42, 42, 0.5);
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 0.375rem;
            color: #e0e0e0;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        
        .form-control:focus {
            border-color: rgba(0,123,255,0.5);
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }
        
        .code-editor {
            font-family: monospace;
            background-color: rgba(42, 42, 42, 0.5);
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 0.375rem;
            height: 200px;
            color: #e0e0e0;
            padding: 0.75rem 1rem;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">组件管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-plus text-white"></i>
                                </span>
                                创建组件
                            </span>
                        </button>
                        <button class="flex items-center justify-center bg-gray-700 hover:bg-gray-600 text-white px-5 py-3 rounded-lg font-medium transition-all">
                            <span class="flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-cloud-download-alt text-white"></i>
                                </span>
                                导入组件
                            </span>
                        </button>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="component-tabs">
                        <div class="component-tab active">所有组件</div>
                        <div class="component-tab">系统组件</div>
                        <div class="component-tab">全局组件</div>
                        <div class="component-tab">自定义组件</div>
                    </div>
                </div>
            </div>

            <!-- 组件筛选和搜索 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="relative">
                    <input type="text" placeholder="搜索组件..." 
                           class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent pl-10">
                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
                
                <div>
                    <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option>所有类型</option>
                        <option>基础组件</option>
                        <option>表单组件</option>
                        <option>布局组件</option>
                        <option>媒体组件</option>
                        <option>交互组件</option>
                        <option>数据展示组件</option>
                    </select>
                </div>
                
                <div>
                    <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option>最新创建</option>
                        <option>最多使用</option>
                        <option>最近更新</option>
                        <option>名称排序</option>
                    </select>
                </div>
            </div>

            <!-- 组件列表 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                <!-- 组件卡片 1 -->
                <div class="component-card">
                    <div class="component-preview">
                        <div class="component-badge global">全局</div>
                        <div class="component-actions">
                            <div class="component-action"><i class="fas fa-edit"></i></div>
                            <div class="component-action"><i class="fas fa-copy"></i></div>
                            <div class="component-action"><i class="fas fa-trash-alt"></i></div>
                        </div>
                        <div class="bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 w-full">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center text-blue-400 mr-3">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div>
                                    <div class="font-medium text-white">用户信息卡片</div>
                                    <div class="text-xs text-gray-400">在线 | 管理员</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="component-card-header">
                        <h3 class="text-lg font-medium text-white">用户信息卡片</h3>
                        <div class="text-gray-400 text-sm">基础组件</div>
                    </div>
                    <div class="component-card-body">
                        <p class="text-gray-300 text-sm">展示用户基本信息的卡片组件，包括头像、名称、状态和角色信息。</p>
                    </div>
                    <div class="component-card-footer">
                        <span class="text-xs text-gray-400">使用次数: 24</span>
                        <span class="text-xs text-gray-400">更新于: 2025-05-10</span>
                    </div>
                </div>
                
                <!-- 组件卡片 2 -->
                <div class="component-card">
                    <div class="component-preview">
                        <div class="component-badge system">系统</div>
                        <div class="component-actions">
                            <div class="component-action"><i class="fas fa-edit"></i></div>
                            <div class="component-action"><i class="fas fa-copy"></i></div>
                        </div>
                        <div class="w-full">
                            <div class="bg-gray-700/50 border border-gray-600 rounded-lg p-3 mb-2">
                                <div class="flex items-center justify-between">
                                    <div class="font-medium text-white">标题文本</div>
                                    <div>
                                        <i class="fas fa-ellipsis-v text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="text-sm text-gray-400 mt-1">这是一个简短的描述文本，用于展示内容摘要。</div>
                                <div class="flex justify-between mt-3">
                                    <div class="text-xs text-blue-400">查看详情</div>
                                    <div class="text-xs text-gray-400">2025-06-01</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="component-card-header">
                        <h3 class="text-lg font-medium text-white">信息卡片</h3>
                        <div class="text-gray-400 text-sm">展示组件</div>
                    </div>
                    <div class="component-card-body">
                        <p class="text-gray-300 text-sm">用于展示文章摘要、通知或信息卡片的组件，包含标题、描述、操作和时间等元素。</p>
                    </div>
                    <div class="component-card-footer">
                        <span class="text-xs text-gray-400">使用次数: 37</span>
                        <span class="text-xs text-gray-400">更新于: 2025-05-15</span>
                    </div>
                </div>
                
                <!-- 组件卡片 3 -->
                <div class="component-card">
                    <div class="component-preview">
                        <div class="component-badge local">本地</div>
                        <div class="component-actions">
                            <div class="component-action"><i class="fas fa-edit"></i></div>
                            <div class="component-action"><i class="fas fa-copy"></i></div>
                            <div class="component-action"><i class="fas fa-trash-alt"></i></div>
                        </div>
                        <div class="w-full flex gap-2">
                            <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">确认</button>
                            <button class="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded-lg text-sm">取消</button>
                            <button class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm">删除</button>
                        </div>
                    </div>
                    <div class="component-card-header">
                        <h3 class="text-lg font-medium text-white">按钮组</h3>
                        <div class="text-gray-400 text-sm">表单组件</div>
                    </div>
                    <div class="component-card-body">
                        <p class="text-gray-300 text-sm">常用按钮组合，包括确认、取消和删除操作，可自定义样式和事件。</p>
                    </div>
                    <div class="component-card-footer">
                        <span class="text-xs text-gray-400">使用次数: 18</span>
                        <span class="text-xs text-gray-400">更新于: 2025-05-20</span>
                    </div>
                </div>
                
                <!-- 组件卡片 4 -->
                <div class="component-card">
                    <div class="component-preview">
                        <div class="component-badge global">全局</div>
                        <div class="component-actions">
                            <div class="component-action"><i class="fas fa-edit"></i></div>
                            <div class="component-action"><i class="fas fa-copy"></i></div>
                            <div class="component-action"><i class="fas fa-trash-alt"></i></div>
                        </div>
                        <div class="w-full">
                            <div class="w-full h-10 bg-gray-700/50 rounded-t-lg border border-gray-600 border-b-0 flex items-center px-4">
                                <div class="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                                <div class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                                <div class="w-3 h-3 rounded-full bg-green-500"></div>
                            </div>
                            <div class="bg-gray-700/50 border border-gray-600 rounded-b-lg p-3 font-mono text-sm text-gray-300">
                                <div>&lt;div class="container"&gt;</div>
                                <div>&nbsp;&nbsp;&lt;h1&gt;Hello World&lt;/h1&gt;</div>
                                <div>&lt;/div&gt;</div>
                            </div>
                        </div>
                    </div>
                    <div class="component-card-header">
                        <h3 class="text-lg font-medium text-white">代码预览</h3>
                        <div class="text-gray-400 text-sm">展示组件</div>
                    </div>
                    <div class="component-card-body">
                        <p class="text-gray-300 text-sm">用于展示代码示例或代码片段的组件，支持语法高亮和复制功能。</p>
                    </div>
                    <div class="component-card-footer">
                        <span class="text-xs text-gray-400">使用次数: 12</span>
                        <span class="text-xs text-gray-400">更新于: 2025-05-22</span>
                    </div>
                </div>
                
                <!-- 组件卡片 5 -->
                <div class="component-card">
                    <div class="component-preview">
                        <div class="component-badge system">系统</div>
                        <div class="component-actions">
                            <div class="component-action"><i class="fas fa-edit"></i></div>
                            <div class="component-action"><i class="fas fa-copy"></i></div>
                        </div>
                        <div class="w-full flex flex-col space-y-3">
                            <div class="flex items-center">
                                <input type="checkbox" class="mr-3 form-checkbox h-5 w-5 text-blue-500 rounded">
                                <span class="text-white">选项 1</span>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" checked class="mr-3 form-checkbox h-5 w-5 text-blue-500 rounded">
                                <span class="text-white">选项 2</span>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" class="mr-3 form-checkbox h-5 w-5 text-blue-500 rounded">
                                <span class="text-white">选项 3</span>
                            </div>
                        </div>
                    </div>
                    <div class="component-card-header">
                        <h3 class="text-lg font-medium text-white">多选框组</h3>
                        <div class="text-gray-400 text-sm">表单组件</div>
                    </div>
                    <div class="component-card-body">
                        <p class="text-gray-300 text-sm">自定义样式的多选框组件，支持默认选中状态和禁用状态。</p>
                    </div>
                    <div class="component-card-footer">
                        <span class="text-xs text-gray-400">使用次数: 29</span>
                        <span class="text-xs text-gray-400">更新于: 2025-05-25</span>
                    </div>
                </div>
                
                <!-- 组件卡片 6 -->
                <div class="component-card">
                    <div class="component-preview">
                        <div class="component-badge local">本地</div>
                        <div class="component-actions">
                            <div class="component-action"><i class="fas fa-edit"></i></div>
                            <div class="component-action"><i class="fas fa-copy"></i></div>
                            <div class="component-action"><i class="fas fa-trash-alt"></i></div>
                        </div>
                        <div class="w-full bg-gray-700/50 border border-gray-600 rounded-lg p-3">
                            <div class="flex justify-between items-center mb-2">
                                <div class="text-white font-medium">系统存储</div>
                                <div class="text-sm text-blue-400">75%</div>
                            </div>
                            <div class="w-full bg-gray-600 rounded-full h-2.5">
                                <div class="bg-blue-500 h-2.5 rounded-full" style="width: 75%"></div>
                            </div>
                            <div class="flex justify-between text-xs mt-1">
                                <div class="text-gray-400">0 GB</div>
                                <div class="text-gray-400">100 GB</div>
                            </div>
                        </div>
                    </div>
                    <div class="component-card-header">
                        <h3 class="text-lg font-medium text-white">进度条</h3>
                        <div class="text-gray-400 text-sm">展示组件</div>
                    </div>
                    <div class="component-card-body">
                        <p class="text-gray-300 text-sm">显示进度或资源使用情况的组件，支持自定义颜色、标签和百分比。</p>
                    </div>
                    <div class="component-card-footer">
                        <span class="text-xs text-gray-400">使用次数: 15</span>
                        <span class="text-xs text-gray-400">更新于: 2025-05-28</span>
                    </div>
                </div>
            </div>
            
            <!-- 分页栏 -->
            <div class="flex justify-between items-center mt-6">
                <div class="text-sm text-gray-400">显示 1-6 项，共 24 项</div>
                <div class="flex space-x-1">
                    <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">上一页</button>
                    <button class="px-3 py-1 rounded bg-blue-600 text-white">1</button>
                    <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">2</button>
                    <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">3</button>
                    <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">4</button>
                    <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">下一页</button>
                </div>
            </div>
        </div>

        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 标签切换功能
            const tabs = document.querySelectorAll('.component-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html> 