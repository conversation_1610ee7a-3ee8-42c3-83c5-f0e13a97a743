/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-11
 * @FilePath: pkg/contract/Config.go
 * @Description: Defines the public configuration service contract for the GACMS platform.
 *
 * © 2025 GACMS. All rights reserved.
 */

package contract

// Config defines the interface for accessing configuration values.
// This allows the application to retrieve settings without being coupled to how they are loaded.
type Config interface {
	// Get retrieves a value from the configuration.
	// It returns an interface{} and a boolean indicating if the key was found.
	Get(key string) (interface{}, bool)

	// GetString retrieves a string value from the configuration.
	GetString(key string) string

	// GetInt retrieves an integer value from the configuration.
	GetInt(key string) int

	// GetBool retrieves a boolean value from the configuration.
	GetBool(key string) bool

	// GetStringMap retrieves a map[string]interface{} value.
	GetStringMap(key string) map[string]interface{}
} 