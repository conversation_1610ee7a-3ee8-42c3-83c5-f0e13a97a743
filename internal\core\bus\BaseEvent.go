/*
 * @Author: <PERSON><PERSON>eh <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/bus/BaseEvent.go
 * @Description: 基础事件实现
 * 
 * © 2025 GACMS. All rights reserved.
 */

package bus

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gacms/pkg/contract"
)

// BaseEvent 是 contract.Event 接口的基础实现
type BaseEvent struct {
	Id        string
	Ctx       context.Context
	Name      contract.EventName
	Payload   interface{}
	Timestamp time.Time
	Metadata  map[string]interface{}
}

// NewBaseEvent 创建一个新的 BaseEvent 实例
func NewBaseEvent(ctx context.Context, name contract.EventName, payload interface{}) contract.Event {
	return &BaseEvent{
		Id:        uuid.New().String(),
		Ctx:       ctx,
		Name:      name,
		Payload:   payload,
		Timestamp: time.Now(),
		Metadata:  make(map[string]interface{}),
	}
}

// ID 返回事件的唯一标识符
func (e *BaseEvent) ID() string {
	return e.Id
}

// Context 返回事件的上下文
func (e *BaseEvent) Context() context.Context {
	return e.Ctx
}

// Name 返回事件的名称
func (e *BaseEvent) Name() contract.EventName {
	return e.Name
}

// Payload 返回事件的载荷
func (e *BaseEvent) Payload() interface{} {
	return e.Payload
}

// Timestamp 返回事件的时间戳
func (e *BaseEvent) Timestamp() time.Time {
	return e.Timestamp
}

// GetMetadata 获取事件的元数据
func (e *BaseEvent) GetMetadata(key string) (interface{}, bool) {
	value, exists := e.Metadata[key]
	return value, exists
}

// SetMetadata 设置事件的元数据
func (e *BaseEvent) SetMetadata(key string, value interface{}) {
	e.Metadata[key] = value
}

// AllMetadata 返回事件的所有元数据
func (e *BaseEvent) AllMetadata() map[string]interface{} {
	return e.Metadata
}

// WithContext 返回一个带有新上下文的事件副本
func (e *BaseEvent) WithContext(ctx context.Context) contract.Event {
	newEvent := *e
	newEvent.Ctx = ctx
	return &newEvent
}

// Clone 创建事件的深拷贝
func (e *BaseEvent) Clone() contract.Event {
	newEvent := &BaseEvent{
		Id:        e.Id,
		Ctx:       e.Ctx,
		Name:      e.Name,
		Payload:   e.Payload,
		Timestamp: e.Timestamp,
		Metadata:  make(map[string]interface{}, len(e.Metadata)),
	}

	// 复制元数据
	for k, v := range e.Metadata {
		newEvent.Metadata[k] = v
	}

	return newEvent
}

// SetID 设置事件的唯一标识符
// 注意：这个方法通常只在反序列化时使用
func (e *BaseEvent) SetID(id string) {
	e.Id = id
}

// SetTimestamp 设置事件的时间戳
// 注意：这个方法通常只在反序列化时使用
func (e *BaseEvent) SetTimestamp(timestamp time.Time) {
	e.Timestamp = timestamp
}

// SetPayload 设置事件的载荷
// 注意：这个方法通常只在反序列化时使用
func (e *BaseEvent) SetPayload(payload interface{}) {
	e.Payload = payload
}