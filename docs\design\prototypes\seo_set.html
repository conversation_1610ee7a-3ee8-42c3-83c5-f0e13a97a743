<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - SEO管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: { 500: '#007bff', 600: '#0069d9' },
                        secondary: { 500: '#00c6ff' },
                        dark: { 800: '#1F2937', 900: '#1A1A1A' }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>

        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">SEO管理</h2>
                    <button class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                        <i class="fas fa-save mr-2"></i>保存更改
                    </button>
                </div>
            </div>

            <!-- SEO概览 -->
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-6">
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 flex items-center">
                    <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4"><i class="fas fa-search text-blue-500 text-xl"></i></div>
                    <div>
                        <div class="text-sm text-gray-400">搜索引擎收录</div>
                        <div class="text-2xl font-semibold text-white">1,286</div>
                    </div>
                </div>
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 flex items-center">
                    <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4"><i class="fas fa-chart-line text-green-500 text-xl"></i></div>
                    <div>
                        <div class="text-sm text-gray-400">关键词排名</div>
                        <div class="text-2xl font-semibold text-white">Top 3</div>
                    </div>
                </div>
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 flex items-center">
                    <div class="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center mr-4"><i class="fas fa-link text-purple-500 text-xl"></i></div>
                    <div>
                        <div class="text-sm text-gray-400">外链数量</div>
                        <div class="text-2xl font-semibold text-white">468</div>
                    </div>
                </div>
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 flex items-center">
                    <div class="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center mr-4"><i class="fas fa-star text-yellow-500 text-xl"></i></div>
                    <div>
                        <div class="text-sm text-gray-400">页面得分</div>
                        <div class="text-2xl font-semibold text-white">92</div>
                    </div>
                </div>
            </div>
            
            <!-- 主要内容区 -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 左侧：基础SEO设置 -->
                <div class="lg:col-span-2 space-y-6">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <h3 class="text-lg font-bold text-white mb-4">全局SEO设置</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-gray-300 mb-2 text-sm">网站标题</label>
                                <input type="text" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" value="GACMS - 专业的内容管理系统">
                            </div>
                            <div>
                                <label class="block text-gray-300 mb-2 text-sm">网站描述</label>
                                <textarea class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" rows="3">GACMS是一个专业的内容管理系统，提供强大的内容管理、多站点管理、主题定制和插件扩展功能。</textarea>
                            </div>
                            <div>
                                <label class="block text-gray-300 mb-2 text-sm">关键词</label>
                                <input type="text" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="输入关键词，用逗号分隔" value="CMS,内容管理,建站系统">
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <h3 class="text-lg font-bold text-white mb-4">URL优化设置</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 bg-gray-800/20 rounded-lg">
                                <span class="text-gray-300">启用URL重写 (伪静态)</span>
                                <input type="checkbox" class="toggle-checkbox" checked>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-800/20 rounded-lg">
                                <span class="text-gray-300">自动推送至搜索引擎</span>
                                <input type="checkbox" class="toggle-checkbox" checked>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧：Sitemap 和 Robots.txt -->
                <div class="space-y-6">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <h3 class="text-lg font-bold text-white mb-4">Sitemap</h3>
                        <p class="text-sm text-gray-400 mb-4">自动生成并提交sitemap，有助于搜索引擎发现您网站上的所有页面。</p>
                        <button class="w-full text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 py-3 rounded-lg font-medium">
                            <i class="fas fa-sync-alt mr-2"></i>立即生成Sitemap
                        </button>
                    </div>
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <h3 class="text-lg font-bold text-white mb-4">Robots.txt</h3>
                        <p class="text-sm text-gray-400 mb-2">指导搜索引擎哪些页面可以抓取，哪些不可以。</p>
                        <textarea class="w-full h-40 bg-gray-900 border border-gray-600 rounded-lg px-4 py-3 text-gray-300 font-mono text-sm" placeholder="User-agent: * ...">User-agent: *
Allow: /
Disallow: /admin/
Disallow: /tmp/</textarea>
                    </div>
                </div>
            </div>
        </div>

        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
</body>
</html> 