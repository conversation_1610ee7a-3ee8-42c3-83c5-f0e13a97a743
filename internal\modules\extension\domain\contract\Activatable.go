/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/extension/domain/contract/Activatable.go
 * @Description: Defines the contract for extensions that require license activation.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

// Activatable defines the contract for extensions that require license activation.
// Modules, themes, or plugins can optionally implement this interface to hook into
// the activation lifecycle.
type Activatable interface {
	// Activate attempts to activate the extension with a given key.
	// The implementation should handle all logic related to validating the key,
	// storing the activation state, etc.
	Activate(licenseKey string) error

	// Deactivate deactivates the extension.
	Deactivate() error

	// CheckStatus returns the current activation status of the extension.
	// e.g., "Active", "Inactive", "Expired", "License not found".
	CheckStatus() (string, error)
} 