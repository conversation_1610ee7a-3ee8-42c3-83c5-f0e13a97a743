/*
 * @Author: <PERSON><PERSON>eh <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: pkg/contract/DomainBinding.go
 * @Description: 域名绑定服务抽象契约定义
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import "context"

// DomainBinding 域名绑定抽象接口
type DomainBinding interface {
	GetID() uint
	GetDomain() string
	GetSiteID() uint
	GetBindingType() string
	GetModuleSlug() *string
	GetCategoryID() *uint
	IsURLRewriteEnabled() bool
	GetDefaultController() string
	GetDefaultAction() string
}

// URLRewriteRule URL重写规则抽象接口
type URLRewriteRule interface {
	GetID() uint
	GetDomainBindingID() uint
	GetRuleName() string
	GetPattern() string
	GetReplacement() string
	GetPriority() int
	IsActive() bool
}

// DomainBindingService 域名绑定服务抽象接口
type DomainBindingService interface {
	// 域名绑定管理
	CreateModuleBinding(domain string, siteID uint, moduleSlug string) (DomainBinding, error)
	CreateCategoryBinding(domain string, siteID uint, categoryID uint) (DomainBinding, error)
	UpdateBinding(bindingID uint, domain string) (DomainBinding, error)
	DeleteBinding(bindingID uint) error
	GetBinding(bindingID uint) (DomainBinding, error)
	ListBindingsBySite(siteID uint, page, pageSize int) ([]DomainBinding, int64, error)
	
	// URL重写管理
	EnableURLRewrite(bindingID uint, defaultController, defaultAction string) error
	DisableURLRewrite(bindingID uint) error
	
	// URL重写规则管理
	CreateURLRule(domainBindingID uint, ruleName, pattern, replacement string, priority int) (URLRewriteRule, error)
	UpdateURLRule(ruleID uint, ruleName, pattern, replacement string, priority int, isActive bool) (URLRewriteRule, error)
	DeleteURLRule(ruleID uint) error
	GetURLRuleByID(ruleID uint) (URLRewriteRule, error)
	GetURLRulesByDomainBinding(domainBindingID uint) ([]URLRewriteRule, error)
}

// DomainBindingRepository 域名绑定仓储抽象接口
type DomainBindingRepository interface {
	// 基础CRUD
	Create(binding DomainBinding) error
	Update(binding DomainBinding) error
	Delete(id uint) error
	GetByID(id uint) (DomainBinding, error)
	GetByDomain(domain string) (DomainBinding, error)
	
	// 查询方法
	ListBySite(siteID uint, page, pageSize int) ([]DomainBinding, int64, error)
	ListByModule(moduleSlug string) ([]DomainBinding, error)
	ListByCategory(categoryID uint) ([]DomainBinding, error)
	
	// URL重写规则CRUD
	CreateURLRule(rule URLRewriteRule) error
	UpdateURLRule(rule URLRewriteRule) error
	DeleteURLRule(id uint) error
	GetURLRuleByID(id uint) (URLRewriteRule, error)
	GetURLRulesByDomainBinding(domainBindingID uint) ([]URLRewriteRule, error)
}

// DomainResolver 域名解析器抽象接口
type DomainResolver interface {
	// ResolveDomainBinding 解析域名绑定
	ResolveDomainBinding(ctx context.Context, domain string) (DomainBinding, error)
	
	// ResolveURLRewrite 解析URL重写
	ResolveURLRewrite(ctx context.Context, domain, path string) (string, error)
	
	// GetActiveRules 获取域名的活跃重写规则
	GetActiveRules(ctx context.Context, domain string) ([]URLRewriteRule, error)
}

// DomainBindingEvent 域名绑定事件
type DomainBindingEvent struct {
	Type      DomainBindingEventType
	Binding   DomainBinding
	Timestamp int64
	Extra     map[string]interface{}
}

// DomainBindingEventType 域名绑定事件类型
type DomainBindingEventType string

const (
	DomainBindingCreatedEvent   DomainBindingEventType = "domain.binding.created"
	DomainBindingUpdatedEvent   DomainBindingEventType = "domain.binding.updated"
	DomainBindingDeletedEvent   DomainBindingEventType = "domain.binding.deleted"
	URLRewriteEnabledEvent      DomainBindingEventType = "domain.url_rewrite.enabled"
	URLRewriteDisabledEvent     DomainBindingEventType = "domain.url_rewrite.disabled"
	URLRuleCreatedEvent         DomainBindingEventType = "url_rule.created"
	URLRuleUpdatedEvent         DomainBindingEventType = "url_rule.updated"
	URLRuleDeletedEvent         DomainBindingEventType = "url_rule.deleted"
)

// DomainBindingEventListener 域名绑定事件监听器
type DomainBindingEventListener interface {
	// OnDomainBindingEvent 处理域名绑定事件
	OnDomainBindingEvent(ctx context.Context, event DomainBindingEvent) error
}

// DomainBindingError 域名绑定错误
type DomainBindingError struct {
	Code    string
	Message string
	Cause   error
}

func (e *DomainBindingError) Error() string {
	return e.Message
}

// 常见域名绑定错误代码
const (
	ErrDomainAlreadyBound    = "DOMAIN_ALREADY_BOUND"
	ErrDomainNotFound        = "DOMAIN_NOT_FOUND"
	ErrInvalidDomain         = "INVALID_DOMAIN"
	ErrModuleNotFound        = "MODULE_NOT_FOUND"
	ErrCategoryNotFound      = "CATEGORY_NOT_FOUND"
	ErrURLRewriteNotEnabled  = "URL_REWRITE_NOT_ENABLED"
	ErrInvalidURLPattern     = "INVALID_URL_PATTERN"
	ErrRulePriorityConflict  = "RULE_PRIORITY_CONFLICT"
)

// DomainBindingConfig 域名绑定配置
type DomainBindingConfig struct {
	// 是否启用域名绑定功能
	Enabled bool `json:"enabled"`
	
	// 默认域名
	DefaultDomain string `json:"default_domain"`
	
	// 是否允许通配符域名
	AllowWildcard bool `json:"allow_wildcard"`
	
	// URL重写配置
	URLRewrite URLRewriteConfig `json:"url_rewrite"`
}

// URLRewriteConfig URL重写配置
type URLRewriteConfig struct {
	// 是否启用URL重写
	Enabled bool `json:"enabled"`
	
	// 默认控制器
	DefaultController string `json:"default_controller"`
	
	// 默认方法
	DefaultAction string `json:"default_action"`
	
	// 规则缓存时间（秒）
	CacheTimeout int `json:"cache_timeout"`
}

// DomainBindingStats 域名绑定统计
type DomainBindingStats struct {
	TotalBindings    int64 `json:"total_bindings"`
	ModuleBindings   int64 `json:"module_bindings"`
	CategoryBindings int64 `json:"category_bindings"`
	ActiveRules      int64 `json:"active_rules"`
	InactiveRules    int64 `json:"inactive_rules"`
}

// DomainBindingQuery 域名绑定查询条件
type DomainBindingQuery struct {
	SiteID       *uint   `json:"site_id,omitempty"`
	BindingType  *string `json:"binding_type,omitempty"`
	ModuleSlug   *string `json:"module_slug,omitempty"`
	CategoryID   *uint   `json:"category_id,omitempty"`
	Domain       *string `json:"domain,omitempty"`
	IsActive     *bool   `json:"is_active,omitempty"`
	Page         int     `json:"page"`
	PageSize     int     `json:"page_size"`
	OrderBy      string  `json:"order_by"`
	OrderDir     string  `json:"order_dir"`
}

// URLRuleQuery URL重写规则查询条件
type URLRuleQuery struct {
	DomainBindingID *uint   `json:"domain_binding_id,omitempty"`
	RuleName        *string `json:"rule_name,omitempty"`
	Pattern         *string `json:"pattern,omitempty"`
	IsActive        *bool   `json:"is_active,omitempty"`
	MinPriority     *int    `json:"min_priority,omitempty"`
	MaxPriority     *int    `json:"max_priority,omitempty"`
	Page            int     `json:"page"`
	PageSize        int     `json:"page_size"`
	OrderBy         string  `json:"order_by"`
	OrderDir        string  `json:"order_dir"`
}

// PermissionInfo 权限信息
type PermissionInfo struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Category    string `json:"category"`
	Module      string `json:"module"`
}

// EventInfo 事件信息
type EventInfo struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Module      string `json:"module"`
	EventType   string `json:"event_type"`
}

// EventListenerInfo 事件监听器信息
type EventListenerInfo struct {
	EventName   string `json:"event_name"`
	HandlerName string `json:"handler_name"`
	Module      string `json:"module"`
	Priority    int    `json:"priority"`
}
