# GACMS商业授权配置
# 控制商业授权验证的开关和相关配置

# 商业授权总开关
commercial_authorization:
  # 是否启用商业授权验证
  enabled: false  # 默认关闭，避免影响开发测试
  
  # 开发模式设置
  development_mode: true  # 开发模式下跳过某些验证
  
  # 许可证文件配置
  license_file:
    # 系统许可证文件路径
    system_license_path: "./licenses/system_license.json"
    
    # 使用许可证文件目录
    usage_license_dir: "./licenses/usage/"
    
    # 许可证文件格式
    format: "json"
    
    # 许可证文件编码
    encoding: "utf-8"
  
  # 验证配置
  validation:
    # 验证间隔（小时）
    interval_hours: 24
    
    # 离线模式（不需要在线验证）
    offline_mode: true
    
    # 宽松模式（开发阶段使用）
    lenient_mode: true
    
    # 缓存验证结果时间（分钟）
    cache_duration_minutes: 60
    
    # 验证失败重试次数
    retry_attempts: 3
    
    # 验证超时时间（秒）
    timeout_seconds: 30

# 域名验证配置
domain_validation:
  # 是否启用域名验证
  enabled: false  # 开发阶段关闭
  
  # 域名验证方式
  method: "dns_txt"  # dns_txt, whois, manual
  
  # DNS TXT记录前缀
  dns_txt_prefix: "_gacms_license"
  
  # 验证间隔（天）
  validation_interval_days: 7
  
  # 开发环境域名白名单
  development_domains:
    - "localhost"
    - "127.0.0.1"
    - "*.local"
    - "*.dev"
    - "*.test"
  
  # 子域名策略
  subdomain_policy:
    # 是否允许子域名共享授权
    allow_subdomain_sharing: true
    
    # 是否限制子域名数量
    limit_subdomain_count: false
    
    # 最大子域名数量（如果启用限制）
    max_subdomains: -1  # -1表示无限制

# 版本控制配置
edition_control:
  # 默认版本（当无许可证时）
  default_edition: "personal"
  
  # 是否允许版本降级
  allow_downgrade: true
  
  # 版本验证严格模式
  strict_mode: false  # 开发阶段使用宽松模式
  
  # 功能降级策略
  feature_degradation:
    # 当功能不可用时的处理方式
    strategy: "graceful"  # graceful, block, redirect
    
    # 降级提示信息
    show_upgrade_hints: true
    
    # 降级日志记录
    log_degradation: true

# 许可证管理系统配置
license_management:
  # 是否启用在线许可证管理系统
  online_system_enabled: false
  
  # 许可证管理系统API地址
  api_base_url: "https://license.gacms.com/api/v1"
  
  # API认证配置
  api_auth:
    # API密钥
    api_key: ""
    
    # API密钥环境变量名
    api_key_env: "GACMS_LICENSE_API_KEY"
    
    # 认证方式
    auth_method: "bearer_token"  # bearer_token, api_key, oauth2
  
  # 离线许可证配置
  offline_license:
    # 是否支持离线许可证
    enabled: true
    
    # 离线许可证有效期检查
    check_expiration: true
    
    # 离线许可证签名验证
    verify_signature: true
    
    # 公钥文件路径
    public_key_path: "./keys/gacms_public.pem"

# 商业化功能配置
commercial_features:
  # 支付集成
  payment_integration:
    enabled: false
    providers: []  # stripe, paypal, alipay, wechat_pay
  
  # 客户管理
  customer_management:
    enabled: false
    crm_integration: false
  
  # 使用统计
  usage_analytics:
    enabled: false
    collect_anonymous_stats: true
    report_interval_hours: 24
  
  # 许可证到期提醒
  expiration_notifications:
    enabled: false
    notify_days_before: [30, 7, 1]
    notification_methods: ["email", "dashboard"]

# 安全配置
security:
  # 许可证加密
  license_encryption:
    # 是否启用许可证内容加密
    enabled: true
    
    # 加密算法
    algorithm: "AES-256-GCM"
    
    # 密钥派生算法
    key_derivation: "PBKDF2"
  
  # 数字签名
  digital_signature:
    # 是否启用数字签名验证
    enabled: true
    
    # 签名算法
    algorithm: "RSA-SHA256"
    
    # 密钥长度
    key_length: 4096
  
  # 防篡改
  tamper_protection:
    # 是否启用防篡改检查
    enabled: true
    
    # 校验和算法
    checksum_algorithm: "SHA256"
    
    # 文件完整性检查
    file_integrity_check: true

# 日志配置
logging:
  # 是否启用商业授权日志
  enabled: true
  
  # 日志级别
  level: "info"  # debug, info, warn, error
  
  # 日志文件路径
  file_path: "./logs/commercial_auth.log"
  
  # 日志轮转配置
  rotation:
    max_size_mb: 100
    max_files: 10
    max_age_days: 30
  
  # 审计日志
  audit_log:
    enabled: true
    file_path: "./logs/license_audit.log"
    include_sensitive_data: false

# 环境变量映射
environment_variables:
  # 商业授权开关
  GACMS_COMMERCIAL_AUTH_ENABLED: "commercial_authorization.enabled"
  
  # 开发模式
  GACMS_DEVELOPMENT_MODE: "commercial_authorization.development_mode"
  
  # 许可证文件路径
  GACMS_SYSTEM_LICENSE_PATH: "commercial_authorization.license_file.system_license_path"
  GACMS_USAGE_LICENSE_DIR: "commercial_authorization.license_file.usage_license_dir"
  
  # 域名验证
  GACMS_DOMAIN_VALIDATION_ENABLED: "domain_validation.enabled"
  
  # 默认版本
  GACMS_DEFAULT_EDITION: "edition_control.default_edition"
  
  # 许可证API密钥
  GACMS_LICENSE_API_KEY: "license_management.api_auth.api_key"
  
  # 公钥文件路径
  GACMS_PUBLIC_KEY_PATH: "license_management.offline_license.public_key_path"

# 开发阶段特殊配置
development:
  # 跳过所有商业授权检查
  skip_all_checks: true
  
  # 模拟许可证数据
  mock_license_data:
    enabled: true
    edition: "business"  # 开发时模拟商业版
    domain: "localhost"
    expires_at: "2030-12-31T23:59:59Z"
  
  # 开发工具
  dev_tools:
    # 许可证生成工具
    license_generator: true
    
    # 许可证验证工具
    license_validator: true
    
    # 配置检查工具
    config_checker: true

# 生产环境配置模板
production_template:
  commercial_authorization:
    enabled: true
    development_mode: false
  
  domain_validation:
    enabled: true
    method: "dns_txt"
  
  edition_control:
    strict_mode: true
    default_edition: "personal"
  
  security:
    license_encryption:
      enabled: true
    digital_signature:
      enabled: true
    tamper_protection:
      enabled: true
  
  logging:
    enabled: true
    level: "warn"
    audit_log:
      enabled: true
