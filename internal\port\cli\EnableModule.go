/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/port/cli/EnableModule.go
 * @Description: Defines the 'module:enable' command.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package cli

import (
	"fmt"
	"gacms/internal/modules/system/application/service"

	"github.com/spf13/cobra"
)

// NewEnableModuleCmd creates the 'module:enable' command.
func NewEnableModuleCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "module:enable [moduleName]",
		Short: "Enable a module.",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			moduleService := service.NewModuleCommandService()
			moduleName := args[0]
			if err := moduleService.Enable(moduleName); err != nil {
				return fmt.Errorf("failed to enable module '%s': %w", moduleName, err)
			}
			fmt.Printf("Module '%s' enabled successfully.\n", moduleName)
			return nil
		},
	}
	return cmd
} 