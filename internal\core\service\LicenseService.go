/*
 * @Author: <PERSON><PERSON>
 * @Email: <EMAIL>
 * @Date: 2025-01-21 16:00:00
 * @LastEditTime: 2025-01-21 16:00:00
 * @FilePath: internal/core/service/LicenseService.go
 * @Description: 许可证服务层，提供统一的业务级许可证管理接口
 * @Copyright (c) 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"fmt"

	"gacms/pkg/contract"

	"go.uber.org/fx"
	"go.uber.org/zap"
)

// LicenseService 许可证服务接口
// 提供业务层的统一许可证管理接口，封装底层LicenseManager的复杂性
type LicenseService interface {
	// ========== 系统级许可证管理 ==========
	
	// GetSystemInfo 获取系统许可证信息
	GetSystemInfo() *SystemInfo
	
	// GetTenantInfo 获取租户许可证信息
	GetTenantInfo(tenantDomain string) *TenantInfo
	
	// CheckFeatureAccess 检查功能访问权限
	CheckFeatureAccess(ctx context.Context, featureName string) *FeatureAccessResult
	
	// CheckTenantFeatureAccess 检查租户功能访问权限
	CheckTenantFeatureAccess(ctx context.Context, tenantDomain string, featureName string) *FeatureAccessResult
	
	// ========== 模块级许可证管理 ==========
	
	// CheckModuleAccess 检查模块访问权限
	CheckModuleAccess(ctx context.Context, moduleName string) *ModuleAccessResult
	
	// GetModuleInfo 获取模块许可证信息
	GetModuleInfo(ctx context.Context, moduleName string) *ModuleInfo
	
	// ========== 许可证管理 ==========
	
	// InstallLicense 安装许可证
	InstallLicense(licenseData string) *InstallResult
	
	// RefreshLicenses 刷新所有许可证
	RefreshLicenses() error
	
	// GetLicenseStatus 获取许可证状态概览
	GetLicenseStatus() *LicenseStatus

	// ========== 双重许可证管理 ==========

	// InstallSystemLicense 安装系统许可证
	InstallSystemLicense(licenseData string) *InstallResult

	// InstallUsageLicense 安装使用许可证
	InstallUsageLicense(tenantDomain string, licenseData string) *InstallResult

	// GetSystemLicenseInfo 获取系统许可证详细信息
	GetSystemLicenseInfo() *SystemLicenseInfo

	// GetUsageLicenseInfo 获取使用许可证详细信息
	GetUsageLicenseInfo(tenantDomain string) *UsageLicenseInfo

	// ========== 降级状态管理 ==========

	// GetDegradationStatus 获取降级状态
	GetDegradationStatus() *DegradationStatus

	// ShouldShowUpgradePrompt 检查是否需要显示升级提示
	ShouldShowUpgradePrompt() bool

	// GetUpgradeRecommendation 获取升级建议
	GetUpgradeRecommendation() *UpgradeRecommendation
}

// SystemInfo 系统许可证信息
type SystemInfo struct {
	Edition       contract.Edition `json:"edition"`
	IsValid       bool             `json:"is_valid"`
	ExpiresAt     *string          `json:"expires_at,omitempty"`
	MaxTenants    int              `json:"max_tenants"`
	CurrentTenants int             `json:"current_tenants"`
	Features      []string         `json:"features"`
	ErrorMessage  string           `json:"error_message,omitempty"`

	// 降级状态信息
	IsInDegradedMode  bool   `json:"is_in_degraded_mode"`
	DegradationReason string `json:"degradation_reason,omitempty"`
	UpgradeRequired   bool   `json:"upgrade_required"`
}

// TenantInfo 租户许可证信息
type TenantInfo struct {
	Domain       string           `json:"domain"`
	Edition      contract.Edition `json:"edition"`
	IsValid      bool             `json:"is_valid"`
	IsFree       bool             `json:"is_free"`
	ExpiresAt    *string          `json:"expires_at,omitempty"`
	Features     []string         `json:"features"`
	ErrorMessage string           `json:"error_message,omitempty"`
}

// FeatureAccessResult 功能访问结果
type FeatureAccessResult struct {
	Allowed      bool             `json:"allowed"`
	Edition      contract.Edition `json:"edition"`
	Reason       string           `json:"reason,omitempty"`
	Suggestions  []string         `json:"suggestions,omitempty"`

	// 降级状态信息
	IsInDegradedMode  bool   `json:"is_in_degraded_mode"`
	DegradationReason string `json:"degradation_reason,omitempty"`
}

// ModuleAccessResult 模块访问结果
type ModuleAccessResult struct {
	Allowed      bool   `json:"allowed"`
	ModuleName   string `json:"module_name"`
	IsActivated  bool   `json:"is_activated"`
	Reason       string `json:"reason,omitempty"`
	Suggestions  []string `json:"suggestions,omitempty"`
}

// ModuleInfo 模块许可证信息
type ModuleInfo struct {
	ModuleName   string `json:"module_name"`
	IsActivated  bool   `json:"is_activated"`
	IsValid      bool   `json:"is_valid"`
	LicenseType  string `json:"license_type"`
	ErrorMessage string `json:"error_message,omitempty"`
}

// InstallResult 安装结果
type InstallResult struct {
	Success      bool   `json:"success"`
	LicenseType  string `json:"license_type"`
	Message      string `json:"message"`
	ErrorMessage string `json:"error_message,omitempty"`
}

// LicenseStatus 许可证状态概览
type LicenseStatus struct {
	SystemLicense  *SystemInfo            `json:"system_license"`
	TenantLicenses map[string]*TenantInfo `json:"tenant_licenses"`
	ModuleLicenses map[string]*ModuleInfo `json:"module_licenses"`
	OverallStatus  string                 `json:"overall_status"`
	StatusMessage  string                 `json:"status_message,omitempty"`
	UpgradeRequired bool                  `json:"upgrade_required"`
}

// DegradationStatus 降级状态信息
type DegradationStatus struct {
	IsInDegradedMode  bool              `json:"is_in_degraded_mode"`
	Reason           string            `json:"reason"`
	CurrentEdition   contract.Edition  `json:"current_edition"`
	AvailableFeatures []string         `json:"available_features"`
	UpgradeRequired  bool              `json:"upgrade_required"`
	Recommendations  []string          `json:"recommendations"`
}

// UpgradeRecommendation 升级建议
type UpgradeRecommendation struct {
	IsRequired         bool              `json:"is_required"`
	CurrentEdition     contract.Edition  `json:"current_edition"`
	RecommendedEdition contract.Edition  `json:"recommended_edition"`
	Message           string            `json:"message"`
	Benefits          []string          `json:"benefits"`
	ContactInfo       *ContactInfo      `json:"contact_info"`
}

// ContactInfo 联系信息
type ContactInfo struct {
	SalesEmail   string `json:"sales_email"`
	SupportEmail string `json:"support_email"`
	Website      string `json:"website"`
	Phone        string `json:"phone"`
}

// SystemLicenseInfo 系统许可证详细信息
type SystemLicenseInfo struct {
	LicenseID     string           `json:"license_id"`
	Edition       contract.Edition `json:"edition"`
	IsValid       bool             `json:"is_valid"`
	ExpiresAt     *string          `json:"expires_at,omitempty"`
	MaxTenants    int              `json:"max_tenants"`
	Features      []string         `json:"features"`
	Issuer        string           `json:"issuer"`
	IssuedAt      *string          `json:"issued_at,omitempty"`
	ErrorMessage  string           `json:"error_message,omitempty"`
}

// UsageLicenseInfo 使用许可证详细信息
type UsageLicenseInfo struct {
	LicenseID        string           `json:"license_id"`
	TenantDomain     string           `json:"tenant_domain"`
	Edition          contract.Edition `json:"edition"`
	IsValid          bool             `json:"is_valid"`
	ExpiresAt        *string          `json:"expires_at,omitempty"`
	Features         []string         `json:"features"`
	SystemLicenseID  string           `json:"system_license_id"`
	Issuer           string           `json:"issuer"`
	IssuedAt         *string          `json:"issued_at,omitempty"`
	ErrorMessage     string           `json:"error_message,omitempty"`
}

// DegradationInfo 降级状态信息（从LicenseManager引用）
type DegradationInfo struct {
	IsInDegradedMode  bool              `json:"is_in_degraded_mode"`
	Reason           string            `json:"reason"`
	CurrentEdition   contract.Edition  `json:"current_edition"`
	AvailableFeatures []string         `json:"available_features"`
	UpgradeRequired  bool              `json:"upgrade_required"`
}

// DefaultLicenseService 默认许可证服务实现
type DefaultLicenseService struct {
	licenseManager contract.LicenseManager
	logger         *zap.Logger
}

// DefaultLicenseServiceParams fx依赖注入参数
type DefaultLicenseServiceParams struct {
	fx.In

	LicenseManager contract.LicenseManager
	Logger         *zap.Logger
}

// NewDefaultLicenseService 创建默认许可证服务
func NewDefaultLicenseService(params DefaultLicenseServiceParams) LicenseService {
	return &DefaultLicenseService{
		licenseManager: params.LicenseManager,
		logger:         params.Logger,
	}
}

// GetSystemInfo 获取系统许可证信息
func (s *DefaultLicenseService) GetSystemInfo() *SystemInfo {
	licenseInfo := s.licenseManager.GetLicenseInfo()
	
	// 获取系统级许可证管理器（如果支持）
	if systemManager, ok := s.licenseManager.(*DefaultLicenseManager); ok {
		systemLicense := systemManager.GetSystemLicenseInfo()
		maxTenants := systemManager.GetMaxTenants()
		degradationInfo := systemManager.GetDegradationInfo()

		info := &SystemInfo{
			Edition:    licenseInfo.Edition,
			IsValid:    licenseInfo.IsValid,
			MaxTenants: maxTenants,
			Features:   licenseInfo.Features,

			// 添加降级状态信息
			IsInDegradedMode:  degradationInfo.IsInDegradedMode,
			DegradationReason: degradationInfo.Reason,
			UpgradeRequired:   degradationInfo.UpgradeRequired,
		}

		if licenseInfo.ExpiresAt != nil {
			expiresAt := licenseInfo.ExpiresAt.Format("2006-01-02 15:04:05")
			info.ExpiresAt = &expiresAt
		}

		if !licenseInfo.IsValid {
			info.ErrorMessage = licenseInfo.ErrorMsg
		}

		// 计算当前租户数量
		info.CurrentTenants = systemManager.GetCurrentTenantCount()

		return info
	}
	
	// 兼容模式
	return &SystemInfo{
		Edition:    licenseInfo.Edition,
		IsValid:    licenseInfo.IsValid,
		MaxTenants: -1, // 无限制
		Features:   licenseInfo.Features,
	}
}

// GetTenantInfo 获取租户许可证信息
func (s *DefaultLicenseService) GetTenantInfo(tenantDomain string) *TenantInfo {
	// 获取系统级许可证管理器（如果支持）
	if systemManager, ok := s.licenseManager.(*DefaultLicenseManager); ok {
		edition := systemManager.GetTenantEdition(tenantDomain)
		isFree := systemManager.IsPersonalEditionFree(tenantDomain)
		usageLicense := systemManager.GetUsageLicenseInfo(tenantDomain)
		
		info := &TenantInfo{
			Domain:  tenantDomain,
			Edition: edition,
			IsValid: true,
			IsFree:  isFree,
		}
		
		if usageLicense != nil {
			info.Features = usageLicense.Features
			if !usageLicense.ExpiresAt.IsZero() {
				expiresAt := usageLicense.ExpiresAt.Format("2006-01-02 15:04:05")
				info.ExpiresAt = &expiresAt
			}
		}
		
		return info
	}
	
	// 兼容模式
	licenseInfo := s.licenseManager.GetLicenseInfo()
	return &TenantInfo{
		Domain:  tenantDomain,
		Edition: licenseInfo.Edition,
		IsValid: licenseInfo.IsValid,
		IsFree:  true,
	}
}

// CheckFeatureAccess 检查功能访问权限
func (s *DefaultLicenseService) CheckFeatureAccess(ctx context.Context, featureName string) *FeatureAccessResult {
	allowed := s.licenseManager.IsFeatureAuthorized(ctx, featureName)
	licenseInfo := s.licenseManager.GetLicenseInfo()

	result := &FeatureAccessResult{
		Allowed: allowed,
		Edition: licenseInfo.Edition,
	}

	// 检查是否处于降级状态
	if systemManager, ok := s.licenseManager.(*DefaultLicenseManager); ok {
		degradationInfo := systemManager.GetDegradationInfo()
		result.IsInDegradedMode = degradationInfo.IsInDegradedMode

		if degradationInfo.IsInDegradedMode {
			result.DegradationReason = degradationInfo.Reason
		}
	}

	if !allowed {
		result.Reason = "Feature not authorized in current license"

		// 根据降级状态提供不同的建议
		if result.IsInDegradedMode {
			result.Suggestions = []string{
				"当前系统处于降级模式，仅支持个人版功能",
				"请检查许可证状态并更新许可证",
				"联系技术支持获取帮助",
			}
		} else {
			result.Suggestions = []string{
				"升级到更高版本以使用此功能",
				"联系销售团队了解升级选项",
				"查看功能对比了解版本差异",
			}
		}
	}

	return result
}

// CheckTenantFeatureAccess 检查租户功能访问权限
func (s *DefaultLicenseService) CheckTenantFeatureAccess(ctx context.Context, tenantDomain string, featureName string) *FeatureAccessResult {
	// 获取系统级许可证管理器（如果支持）
	if systemManager, ok := s.licenseManager.(*DefaultLicenseManager); ok {
		err := systemManager.CheckTenantAccess(ctx, tenantDomain, featureName)
		edition := systemManager.GetTenantEdition(tenantDomain)
		
		result := &FeatureAccessResult{
			Allowed: err == nil,
			Edition: edition,
		}
		
		if err != nil {
			result.Reason = err.Error()
			result.Suggestions = []string{
				"Check tenant license validity",
				"Upgrade tenant edition",
				"Contact support for assistance",
			}
		}
		
		return result
	}
	
	// 兼容模式
	return s.CheckFeatureAccess(ctx, featureName)
}

// CheckModuleAccess 检查模块访问权限
func (s *DefaultLicenseService) CheckModuleAccess(ctx context.Context, moduleName string) *ModuleAccessResult {
	isAuthorized := s.licenseManager.IsModuleAuthorized(ctx, moduleName)
	
	result := &ModuleAccessResult{
		Allowed:     isAuthorized,
		ModuleName:  moduleName,
		IsActivated: isAuthorized,
	}
	
	if !isAuthorized {
		result.Reason = "Module not authorized or activated"
		result.Suggestions = []string{
			"Activate module license",
			"Check module license validity",
			"Contact vendor for licensing",
		}
	}
	
	return result
}

// GetModuleInfo 获取模块许可证信息
func (s *DefaultLicenseService) GetModuleInfo(ctx context.Context, moduleName string) *ModuleInfo {
	licenseInfo, err := s.licenseManager.GetModuleLicenseInfo(ctx, moduleName)
	
	info := &ModuleInfo{
		ModuleName:  moduleName,
		IsActivated: false,
		IsValid:     false,
		LicenseType: "none",
	}
	
	if err != nil {
		info.ErrorMessage = err.Error()
		return info
	}
	
	if licenseInfo != nil {
		info.IsActivated = true
		info.IsValid = licenseInfo.IsValid
		info.LicenseType = licenseInfo.Type
	}
	
	return info
}

// InstallLicense 安装许可证
func (s *DefaultLicenseService) InstallLicense(licenseData string) *InstallResult {
	err := s.licenseManager.InstallLicense(licenseData)

	result := &InstallResult{
		Success: err == nil,
	}

	if err != nil {
		result.ErrorMessage = err.Error()
		result.Message = "License installation failed"
	} else {
		result.Message = "License installed successfully"

		// 检测许可证类型
		if systemManager, ok := s.licenseManager.(*DefaultLicenseManager); ok {
			licenseType := systemManager.DetectLicenseType(licenseData)
			result.LicenseType = licenseType
		} else {
			result.LicenseType = "system"
		}
	}

	return result
}

// InstallSystemLicense 安装系统许可证
func (s *DefaultLicenseService) InstallSystemLicense(licenseData string) *InstallResult {
	if systemManager, ok := s.licenseManager.(*DefaultLicenseManager); ok {
		err := systemManager.InstallSystemLicense(licenseData)

		result := &InstallResult{
			Success:     err == nil,
			LicenseType: "system",
		}

		if err != nil {
			result.ErrorMessage = err.Error()
			result.Message = "System license installation failed"
		} else {
			result.Message = "System license installed successfully"
		}

		return result
	}

	// 兼容模式
	return s.InstallLicense(licenseData)
}

// InstallUsageLicense 安装使用许可证
func (s *DefaultLicenseService) InstallUsageLicense(tenantDomain string, licenseData string) *InstallResult {
	if systemManager, ok := s.licenseManager.(*DefaultLicenseManager); ok {
		err := systemManager.InstallUsageLicense(tenantDomain, licenseData)

		result := &InstallResult{
			Success:     err == nil,
			LicenseType: "usage",
		}

		if err != nil {
			result.ErrorMessage = err.Error()
			result.Message = fmt.Sprintf("Usage license installation failed for tenant: %s", tenantDomain)
		} else {
			result.Message = fmt.Sprintf("Usage license installed successfully for tenant: %s", tenantDomain)
		}

		return result
	}

	// 兼容模式
	return &InstallResult{
		Success:      false,
		ErrorMessage: "Usage license not supported in compatibility mode",
		Message:      "Please upgrade to support usage licenses",
	}
}

// RefreshLicenses 刷新所有许可证
func (s *DefaultLicenseService) RefreshLicenses() error {
	return s.licenseManager.RefreshLicense()
}

// GetLicenseStatus 获取许可证状态概览
func (s *DefaultLicenseService) GetLicenseStatus() *LicenseStatus {
	systemInfo := s.GetSystemInfo()

	status := &LicenseStatus{
		SystemLicense:  systemInfo,
		TenantLicenses: make(map[string]*TenantInfo),
		ModuleLicenses: make(map[string]*ModuleInfo),
		OverallStatus:  "unknown",
	}

	// 收集租户许可证状态
	if systemManager, ok := s.licenseManager.(*DefaultLicenseManager); ok {
		tenantDomains := systemManager.GetAllTenantDomains()
		for _, domain := range tenantDomains {
			tenantInfo := s.GetTenantInfo(domain)
			status.TenantLicenses[domain] = tenantInfo
		}

		// 收集模块许可证状态
		moduleNames := systemManager.GetAllModuleNames()
		for _, moduleName := range moduleNames {
			moduleInfo := s.GetModuleInfo(context.Background(), moduleName)
			status.ModuleLicenses[moduleName] = moduleInfo
		}
	}

	// 确定整体状态
	if systemInfo.IsInDegradedMode {
		status.OverallStatus = "degraded"
		status.StatusMessage = systemInfo.DegradationReason
		status.UpgradeRequired = true
	} else if systemInfo.IsValid {
		status.OverallStatus = "valid"
		status.StatusMessage = "所有许可证正常"
	} else {
		status.OverallStatus = "invalid"
		status.StatusMessage = "许可证无效"
		status.UpgradeRequired = true
	}

	return status
}

// GetSystemLicenseInfo 获取系统许可证详细信息
func (s *DefaultLicenseService) GetSystemLicenseInfo() *SystemLicenseInfo {
	if systemManager, ok := s.licenseManager.(*DefaultLicenseManager); ok {
		systemLicense := systemManager.GetSystemLicenseInfo()
		if systemLicense == nil {
			return &SystemLicenseInfo{
				IsValid:      false,
				ErrorMessage: "No system license found",
			}
		}

		info := &SystemLicenseInfo{
			LicenseID:  systemLicense.LicenseID,
			Edition:    systemLicense.Edition,
			IsValid:    true,
			MaxTenants: systemLicense.MaxTenants + systemLicense.AdditionalTenants,
			Features:   systemLicense.Features,
			Issuer:     systemLicense.Issuer,
		}

		if !systemLicense.ExpiresAt.IsZero() {
			expiresAt := systemLicense.ExpiresAt.Format("2006-01-02 15:04:05")
			info.ExpiresAt = &expiresAt
		}

		if !systemLicense.IssuedAt.IsZero() {
			issuedAt := systemLicense.IssuedAt.Format("2006-01-02 15:04:05")
			info.IssuedAt = &issuedAt
		}

		return info
	}

	// 兼容模式
	licenseInfo := s.licenseManager.GetLicenseInfo()
	return &SystemLicenseInfo{
		Edition:  licenseInfo.Edition,
		IsValid:  licenseInfo.IsValid,
		Features: licenseInfo.Features,
	}
}

// GetUsageLicenseInfo 获取使用许可证详细信息
func (s *DefaultLicenseService) GetUsageLicenseInfo(tenantDomain string) *UsageLicenseInfo {
	if systemManager, ok := s.licenseManager.(*DefaultLicenseManager); ok {
		usageLicense := systemManager.GetUsageLicenseInfo(tenantDomain)
		if usageLicense == nil {
			return &UsageLicenseInfo{
				TenantDomain: tenantDomain,
				IsValid:      false,
				ErrorMessage: "No usage license found for this tenant",
			}
		}

		info := &UsageLicenseInfo{
			LicenseID:       usageLicense.LicenseID,
			TenantDomain:    usageLicense.TenantDomain,
			Edition:         usageLicense.Edition,
			IsValid:         true,
			Features:        usageLicense.Features,
			SystemLicenseID: usageLicense.SystemLicenseID,
			Issuer:          usageLicense.Issuer,
		}

		if !usageLicense.ExpiresAt.IsZero() {
			expiresAt := usageLicense.ExpiresAt.Format("2006-01-02 15:04:05")
			info.ExpiresAt = &expiresAt
		}

		if !usageLicense.IssuedAt.IsZero() {
			issuedAt := usageLicense.IssuedAt.Format("2006-01-02 15:04:05")
			info.IssuedAt = &issuedAt
		}

		return info
	}

	// 兼容模式
	return &UsageLicenseInfo{
		TenantDomain: tenantDomain,
		IsValid:      false,
		ErrorMessage: "Usage license not supported in compatibility mode",
	}
}

// ========== 降级状态管理 ==========

// GetDegradationStatus 获取降级状态
func (s *DefaultLicenseService) GetDegradationStatus() *DegradationStatus {
	if systemManager, ok := s.licenseManager.(*DefaultLicenseManager); ok {
		degradationInfo := systemManager.GetDegradationInfo()

		return &DegradationStatus{
			IsInDegradedMode:  degradationInfo.IsInDegradedMode,
			Reason:           degradationInfo.Reason,
			CurrentEdition:   degradationInfo.CurrentEdition,
			AvailableFeatures: degradationInfo.AvailableFeatures,
			UpgradeRequired:  degradationInfo.UpgradeRequired,
			Recommendations:  s.getUpgradeRecommendations(degradationInfo),
		}
	}

	// 兼容模式
	licenseInfo := s.licenseManager.GetLicenseInfo()
	return &DegradationStatus{
		IsInDegradedMode:  false,
		CurrentEdition:   licenseInfo.Edition,
		AvailableFeatures: licenseInfo.Features,
		UpgradeRequired:  false,
	}
}

// ShouldShowUpgradePrompt 检查是否需要显示升级提示
func (s *DefaultLicenseService) ShouldShowUpgradePrompt() bool {
	if systemManager, ok := s.licenseManager.(*DefaultLicenseManager); ok {
		return systemManager.IsInDegradedMode()
	}
	return false
}

// GetUpgradeRecommendation 获取升级建议
func (s *DefaultLicenseService) GetUpgradeRecommendation() *UpgradeRecommendation {
	degradationStatus := s.GetDegradationStatus()

	if !degradationStatus.UpgradeRequired {
		return &UpgradeRecommendation{
			IsRequired: false,
			Message:    "当前许可证状态正常，无需升级",
		}
	}

	return &UpgradeRecommendation{
		IsRequired:      true,
		CurrentEdition:  degradationStatus.CurrentEdition,
		RecommendedEdition: s.getRecommendedEdition(degradationStatus.CurrentEdition),
		Message:        degradationStatus.Reason,
		Benefits:       s.getUpgradeBenefits(degradationStatus.CurrentEdition),
		ContactInfo:    s.getContactInfo(),
	}
}



// getUpgradeRecommendations 获取升级建议列表
func (s *DefaultLicenseService) getUpgradeRecommendations(degradationInfo *DegradationInfo) []string {
	if !degradationInfo.IsInDegradedMode {
		return []string{}
	}

	recommendations := []string{
		"检查许可证文件是否存在且有效",
		"确认许可证未过期",
		"验证许可证签名完整性",
	}

	switch degradationInfo.CurrentEdition {
	case contract.EditionPersonal:
		recommendations = append(recommendations,
			"升级到专业版以获得更多功能",
			"升级到商业版以获得企业级功能",
		)
	case contract.EditionProfessional:
		recommendations = append(recommendations,
			"升级到商业版以获得企业级功能",
		)
	}

	recommendations = append(recommendations,
		"联系技术支持获取帮助",
		"访问官网了解许可证选项",
	)

	return recommendations
}

// getRecommendedEdition 获取推荐版本
func (s *DefaultLicenseService) getRecommendedEdition(currentEdition contract.Edition) contract.Edition {
	switch currentEdition {
	case contract.EditionPersonal:
		return contract.EditionProfessional
	case contract.EditionProfessional:
		return contract.EditionBusiness
	default:
		return contract.EditionBusiness
	}
}

// getUpgradeBenefits 获取升级收益
func (s *DefaultLicenseService) getUpgradeBenefits(currentEdition contract.Edition) []string {
	switch currentEdition {
	case contract.EditionPersonal:
		return []string{
			"高级主题和模板",
			"高级SEO工具",
			"工作流管理",
			"更多API功能",
			"优先技术支持",
		}
	case contract.EditionProfessional:
		return []string{
			"企业级API",
			"高级安全功能",
			"系统集成支持",
			"高级分析工具",
			"白标定制",
			"专属技术支持",
		}
	default:
		return []string{
			"完整功能访问",
			"企业级支持",
			"定制开发服务",
		}
	}
}

// getContactInfo 获取联系信息
func (s *DefaultLicenseService) getContactInfo() *ContactInfo {
	return &ContactInfo{
		SalesEmail:   "<EMAIL>",
		SupportEmail: "<EMAIL>",
		Website:      "https://www.gacms.com",
		Phone:        "+86-************",
	}
}
