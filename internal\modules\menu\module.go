/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/menu/module.go
 * @Description: Defines the menu module for GACMS.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package menu

import (
	"context"
	"gacms/internal/core/contract"
	"gacms/internal/modules/menu/application/service"
	"gacms/internal/modules/menu/domain/model"
	"gacms/internal/modules/menu/infrastructure/persistence"
	"gacms/internal/modules/menu/port/http/controller"
	"gacms/internal/port/http/middleware"

	"github.com/gin-gonic/gin"
	"go.uber.org/fx"
)

type MenuModule struct{}

func (m *MenuModule) Name() string {
	return "Menu"
}

func (m *MenuModule) GetModels() []interface{} {
	return []interface{}{
		&model.Menu{},
		&model.MenuItem{},
	}
}

func (m *MenuModule) ExposePermissions() []contract.Permission {
	return []contract.Permission{
		{Module: m.Name(), Name: "menu:manage", Description: "Manage frontend menus and menu items"},
	}
}

var Module = fx.Options(
	// Register services
	fx.Provide(service.NewMenuService),

	// Register controllers
	fx.Provide(controller.NewMenuController),
)

// NewModule creates an instance of the MenuModule.
func NewModule() *MenuModule {
	return &MenuModule{}
}

// RegisterRoutes registers the HTTP routes for this module.
func RegisterRoutes(
	router *gin.Engine,
	authMiddleware *middleware.AdminAuthMiddleware,
	menuController *controller.MenuController,
) {
	adminGuard := authMiddleware.Handle("menu:manage")
	adminGroup := router.Group("/api/admin", adminGuard)
	menuController.RegisterAdminRoutes(adminGroup)

	publicGroup := router.Group("/api/public")
	menuController.RegisterPublicRoutes(publicGroup)
} 