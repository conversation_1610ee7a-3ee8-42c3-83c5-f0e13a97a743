/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/port/cli/ActivationCommand.go
 * @Description: Defines CLI commands for extension activation.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package cli

import (
	"fmt"
	"gacms/internal/modules/extension/application/service"
	"github.com/spf13/cobra"
)

func NewActivationCommand(service *service.ActivationService) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "extension:activate",
		Short: "Activate, deactivate, or check the status of an extension",
	}

	activateCmd := &cobra.Command{
		Use:   "activate [module-name] [license-key]",
		Short: "Activate an extension with a license key",
		Args:  cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			moduleName := args[0]
			licenseKey := args[1]
			err := service.Activate(moduleName, licenseKey)
			if err != nil {
				return err
			}
			fmt.Printf("Activation request for module '%s' sent successfully.\n", moduleName)
			return nil
		},
	}

	deactivateCmd := &cobra.Command{
		Use:   "deactivate [module-name]",
		Short: "Deactivate an extension",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			moduleName := args[0]
			err := service.Deactivate(moduleName)
			if err != nil {
				return err
			}
			fmt.Printf("Deactivation request for module '%s' sent successfully.\n", moduleName)
			return nil
		},
	}

	statusCmd := &cobra.Command{
		Use:   "status [module-name]",
		Short: "Check the activation status of an extension",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Cobra, args []string) error {
			moduleName := args[0]
			status, err := service.CheckStatus(moduleName)
			if err != nil {
				return err
			}
			fmt.Printf("Module '%s' status: %s\n", moduleName, status)
			return nil
		},
	}

	cmd.AddCommand(activateCmd, deactivateCmd, statusCmd)

	return cmd
} 