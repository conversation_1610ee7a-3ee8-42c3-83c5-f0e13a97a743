/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/ServiceProxyFactory.go
 * @Description: 服务代理工厂，扩展现有的服务代理模式
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"fmt"
	"reflect"
	"sync"

	"gacms/internal/infrastructure/database"
	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// ServiceProxyFactory 服务代理工厂
// 扩展现有的服务代理模式，支持通用的服务懒加载
type ServiceProxyFactory struct {
	globalDeps *GlobalDependencies
	logger     *zap.Logger
	eventMgr   contract.EventManager

	// 服务工厂注册表
	serviceFactories map[string]*ServiceFactory

	// 服务实例缓存
	globalServices map[string]interface{}           // 全局服务实例
	tenantServices map[uint]map[string]interface{}  // 租户服务实例

	// 并发控制
	mu sync.RWMutex
}

// ServiceFactory 服务工厂配置
type ServiceFactory struct {
	Name         string
	ServiceType  reflect.Type
	IsGlobal     bool
	SupportsTenant bool
	
	// 工厂函数
	GlobalFactory func(deps *GlobalDependencies) (interface{}, error)
	TenantFactory func(deps *TenantDependencies) (interface{}, error)
}

// ServiceProxyFactoryParams fx依赖注入参数
type ServiceProxyFactoryParams struct {
	fx.In

	GlobalDeps *GlobalDependencies
	Logger     *zap.Logger
	EventMgr   contract.EventManager
}

// NewServiceProxyFactory 创建服务代理工厂
func NewServiceProxyFactory(params ServiceProxyFactoryParams) *ServiceProxyFactory {
	factory := &ServiceProxyFactory{
		globalDeps:       params.GlobalDeps,
		logger:           params.Logger,
		eventMgr:         params.EventMgr,
		serviceFactories: make(map[string]*ServiceFactory),
		globalServices:   make(map[string]interface{}),
		tenantServices:   make(map[uint]map[string]interface{}),
	}

	// 注册服务工厂
	factory.registerServiceFactories()

	return factory
}

// GetService 获取服务实例（多租户感知）
func (f *ServiceProxyFactory) GetService(ctx context.Context, serviceName string) (interface{}, error) {
	// 从上下文提取租户信息
	if siteID, hasSiteID := database.SiteIDFrom(ctx); hasSiteID && siteID > 0 {
		return f.GetServiceForSite(siteID, serviceName)
	}
	
	return f.getGlobalService(serviceName)
}

// GetServiceForSite 获取指定站点的服务实例
func (f *ServiceProxyFactory) GetServiceForSite(siteID uint, serviceName string) (interface{}, error) {
	f.logger.Debug("Getting service for site",
		zap.Uint("site_id", siteID),
		zap.String("service", serviceName),
	)

	// 1. 检查租户服务缓存
	f.mu.RLock()
	if tenantServices, exists := f.tenantServices[siteID]; exists {
		if service, exists := tenantServices[serviceName]; exists {
			f.mu.RUnlock()
			return service, nil
		}
	}
	f.mu.RUnlock()

	// 2. 检查全局服务缓存
	if service, err := f.getGlobalService(serviceName); err == nil {
		return service, nil
	}

	// 3. 懒加载租户特定服务
	return f.createTenantService(siteID, serviceName)
}

// getGlobalService 获取全局服务实例
func (f *ServiceProxyFactory) getGlobalService(serviceName string) (interface{}, error) {
	// 1. 检查全局服务缓存
	f.mu.RLock()
	if service, exists := f.globalServices[serviceName]; exists {
		f.mu.RUnlock()
		return service, nil
	}
	f.mu.RUnlock()

	// 2. 懒加载全局服务
	return f.createGlobalService(serviceName)
}

// createGlobalService 创建全局服务实例
func (f *ServiceProxyFactory) createGlobalService(serviceName string) (interface{}, error) {
	f.mu.Lock()
	defer f.mu.Unlock()

	// 双重检查
	if service, exists := f.globalServices[serviceName]; exists {
		return service, nil
	}

	f.logger.Info("Creating global service instance", zap.String("service", serviceName))

	// 1. 获取服务工厂
	factory, exists := f.serviceFactories[serviceName]
	if !exists {
		return nil, fmt.Errorf("service factory not found: %s", serviceName)
	}

	// 2. 检查是否支持全局实例
	if !factory.IsGlobal || factory.GlobalFactory == nil {
		return nil, fmt.Errorf("service %s does not support global instances", serviceName)
	}

	// 3. 调用全局工厂创建实例
	service, err := factory.GlobalFactory(f.globalDeps)
	if err != nil {
		return nil, fmt.Errorf("failed to create global service %s: %w", serviceName, err)
	}

	// 4. 缓存服务实例
	f.globalServices[serviceName] = service

	f.logger.Info("Global service instance created successfully",
		zap.String("service", serviceName),
		zap.String("type", factory.ServiceType.String()),
	)

	return service, nil
}

// createTenantService 创建租户特定服务实例
func (f *ServiceProxyFactory) createTenantService(siteID uint, serviceName string) (interface{}, error) {
	f.mu.Lock()
	defer f.mu.Unlock()

	// 双重检查
	if tenantServices, exists := f.tenantServices[siteID]; exists {
		if service, exists := tenantServices[serviceName]; exists {
			return service, nil
		}
	}

	f.logger.Info("Creating tenant service instance",
		zap.Uint("site_id", siteID),
		zap.String("service", serviceName),
	)

	// 1. 获取服务工厂
	factory, exists := f.serviceFactories[serviceName]
	if !exists {
		return nil, fmt.Errorf("service factory not found: %s", serviceName)
	}

	// 2. 检查是否支持租户实例
	if !factory.SupportsTenant {
		return nil, fmt.Errorf("service %s does not support tenant instances", serviceName)
	}

	// 3. 创建租户依赖
	tenantDeps := NewTenantDependencies(f.globalDeps, siteID)

	// 4. 调用租户工厂创建实例
	var service interface{}
	var err error

	if factory.TenantFactory != nil {
		service, err = factory.TenantFactory(tenantDeps)
	} else if factory.GlobalFactory != nil {
		// 使用全局工厂但注入租户依赖
		service, err = factory.GlobalFactory(f.globalDeps)
	} else {
		return nil, fmt.Errorf("no factory available for service %s", serviceName)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create tenant service %s for site %d: %w", serviceName, siteID, err)
	}

	// 5. 缓存服务实例
	if f.tenantServices[siteID] == nil {
		f.tenantServices[siteID] = make(map[string]interface{})
	}
	f.tenantServices[siteID][serviceName] = service

	f.logger.Info("Tenant service instance created successfully",
		zap.Uint("site_id", siteID),
		zap.String("service", serviceName),
		zap.String("type", factory.ServiceType.String()),
	)

	return service, nil
}

// RegisterServiceFactory 注册服务工厂
func (f *ServiceProxyFactory) RegisterServiceFactory(factory *ServiceFactory) error {
	if factory.Name == "" {
		return fmt.Errorf("service factory name cannot be empty")
	}

	if factory.GlobalFactory == nil && factory.TenantFactory == nil {
		return fmt.Errorf("at least one factory function is required for service %s", factory.Name)
	}

	f.mu.Lock()
	defer f.mu.Unlock()

	f.serviceFactories[factory.Name] = factory

	f.logger.Debug("Service factory registered",
		zap.String("service", factory.Name),
		zap.String("type", factory.ServiceType.String()),
		zap.Bool("is_global", factory.IsGlobal),
		zap.Bool("supports_tenant", factory.SupportsTenant),
	)

	return nil
}

// registerServiceFactories 注册服务工厂
func (f *ServiceProxyFactory) registerServiceFactories() {
	f.logger.Info("Registering service factories")
	
	// TODO: 注册具体的服务工厂
	// 这里可以注册各种服务的工厂函数
	
	f.logger.Info("Service factories registered", 
		zap.Int("total_factories", len(f.serviceFactories)),
	)
}

// CreateProxy 创建服务代理
func (f *ServiceProxyFactory) CreateProxy(serviceName string, serviceType reflect.Type) interface{} {
	// 创建通用服务代理
	proxy := &GenericServiceProxy{
		factory:     f,
		serviceName: serviceName,
		serviceType: serviceType,
		once:        sync.Once{},
	}

	return proxy
}

// GenericServiceProxy 通用服务代理
type GenericServiceProxy struct {
	factory     *ServiceProxyFactory
	serviceName string
	serviceType reflect.Type
	realService interface{}
	once        sync.Once
}

// GetRealService 获取真实服务（懒加载）
func (p *GenericServiceProxy) GetRealService(ctx context.Context) interface{} {
	p.once.Do(func() {
		service, err := p.factory.GetService(ctx, p.serviceName)
		if err != nil {
			// 记录错误但不阻塞
			p.factory.logger.Error("Failed to load service",
				zap.String("service", p.serviceName),
				zap.Error(err),
			)
			return
		}
		p.realService = service
	})
	return p.realService
}

// GetStats 获取工厂统计信息
func (f *ServiceProxyFactory) GetStats() map[string]interface{} {
	f.mu.RLock()
	defer f.mu.RUnlock()

	return map[string]interface{}{
		"global_services":      len(f.globalServices),
		"tenant_services":      len(f.tenantServices),
		"registered_factories": len(f.serviceFactories),
	}
}
