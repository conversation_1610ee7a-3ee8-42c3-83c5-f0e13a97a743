/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/bus/DefaultEventBus.go
 * @Description: 默认事件总线实现
 * 
 * © 2025 GACMS. All rights reserved.
 */

package bus

import (
	"context"
	"fmt"
	"sync"

	"gacms/internal/infrastructure/database"
	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// DefaultEventBus 是 contract.EventBus 接口的默认实现
// 它使用线程安全的映射来存储事件处理器
type DefaultEventBus struct {
	// 事件名称到处理器列表的映射
	handlers     map[contract.EventName][]contract.EventHandler
	// 处理器到其支持的事件的映射
	handlerMap   map[string][]contract.EventName
	// 互斥锁，用于保护并发访问
	mu           sync.RWMutex
	// 日志记录器
	logger       *zap.Logger
	// 默认分发策略
	dispatchStrategy contract.EventDispatchStrategy
}

// DefaultEventBusParams 定义了创建 DefaultEventBus 所需的参数
type DefaultEventBusParams struct {
	fx.In

	Logger *zap.Logger
}

// NewDefaultEventBus 创建一个新的 DefaultEventBus 实例
func NewDefaultEventBus(params DefaultEventBusParams) contract.EventBus {
	return &DefaultEventBus{
		handlers:     make(map[contract.EventName][]contract.EventHandler),
		handlerMap:   make(map[string][]contract.EventName),
		logger:       params.Logger,
		dispatchStrategy: contract.SyncDispatch,
	}
}

// Register 注册一个事件处理器
func (b *DefaultEventBus) Register(handler contract.EventHandler) error {
	if handler == nil {
		return fmt.Errorf("cannot register nil handler")
	}

	b.mu.Lock()
	defer b.mu.Unlock()

	// 获取处理器支持的事件
	events := handler.SupportedEvents()
	if len(events) == 0 {
		return fmt.Errorf("handler %s does not support any events", handler.HandlerName())
	}

	// 注册处理器到每个支持的事件
	for _, eventName := range events {
		b.handlers[eventName] = append(b.handlers[eventName], handler)
		b.logger.Debug("Registered handler for event",
			zap.String("event", string(eventName)),
			zap.String("handler", handler.HandlerName()),
		)
	}

	// 记录处理器支持的事件
	b.handlerMap[handler.HandlerName()] = events

	return nil
}

// Unregister 取消注册一个事件处理器
func (b *DefaultEventBus) Unregister(handler contract.EventHandler) error {
	if handler == nil {
		return fmt.Errorf("cannot unregister nil handler")
	}

	b.mu.Lock()
	defer b.mu.Unlock()

	handlerName := handler.HandlerName()
	events, exists := b.handlerMap[handlerName]
	if !exists {
		return fmt.Errorf("handler %s is not registered", handlerName)
	}

	// 从每个事件的处理器列表中移除处理器
	for _, eventName := range events {
		handlers := b.handlers[eventName]
		for i, h := range handlers {
			if h.HandlerName() == handlerName {
				// 移除处理器
				b.handlers[eventName] = append(handlers[:i], handlers[i+1:]...)
				break
			}
		}

		// 如果没有处理器，删除事件条目
		if len(b.handlers[eventName]) == 0 {
			delete(b.handlers, eventName)
		}

		b.logger.Debug("Unregistered handler for event",
			zap.String("event", string(eventName)),
			zap.String("handler", handlerName),
		)
	}

	// 删除处理器映射
	delete(b.handlerMap, handlerName)

	return nil
}

// Publish 发布一个事件到总线
func (b *DefaultEventBus) Publish(event contract.Event) error {
	if event == nil {
		return fmt.Errorf("cannot publish nil event")
	}

	b.mu.RLock()
	handlers, exists := b.handlers[event.Name()]
	b.mu.RUnlock()

	if !exists || len(handlers) == 0 {
		b.logger.Debug("No handlers registered for event",
			zap.String("event", string(event.Name())),
		)
		return nil
	}

	// 获取租户信息用于日志
	tenantID := b.getTenantIDFromEvent(event)

	b.logger.Info("Publishing event",
		zap.String("event", string(event.Name())),
		zap.Int("handler_count", len(handlers)),
		zap.Any("tenant_id", tenantID),
	)

	// 同步分发事件（租户感知）
	for _, handler := range handlers {
		// 检查处理器是否应该处理此租户的事件
		if !b.shouldHandleEvent(handler, event) {
			b.logger.Debug("Skipping handler due to tenant isolation",
				zap.String("event", string(event.Name())),
				zap.String("handler", handler.HandlerName()),
				zap.Any("tenant_id", tenantID),
			)
			continue
		}

		if err := handler.Handle(event); err != nil {
			b.logger.Error("Error handling event",
				zap.String("event", string(event.Name())),
				zap.String("handler", handler.HandlerName()),
				zap.Any("tenant_id", tenantID),
				zap.Error(err),
			)
			return fmt.Errorf("error handling event %s by %s: %w",
				event.Name(), handler.HandlerName(), err)
		}
	}

	return nil
}

// PublishAsync 异步发布一个事件到总线
func (b *DefaultEventBus) PublishAsync(ctx context.Context, event contract.Event) <-chan error {
	errChan := make(chan error, 1)

	if event == nil {
		errChan <- fmt.Errorf("cannot publish nil event")
		close(errChan)
		return errChan
	}

	b.mu.RLock()
	handlers, exists := b.handlers[event.Name()]
	b.mu.RUnlock()

	if !exists || len(handlers) == 0 {
		b.logger.Debug("No handlers registered for event",
			zap.String("event", string(event.Name())),
		)
		close(errChan)
		return errChan
	}

	b.logger.Info("Async publishing event",
		zap.String("event", string(event.Name())),
		zap.Int("handler_count", len(handlers)),
	)

	go func() {
		defer close(errChan)

		// 使用WaitGroup等待所有处理器完成
		var wg sync.WaitGroup
		wg.Add(len(handlers))

		// 为每个处理器创建一个goroutine
		for _, handler := range handlers {
			go func(h contract.EventHandler) {
				defer wg.Done()

				// 检查上下文是否已取消
				select {
				case <-ctx.Done():
					errChan <- ctx.Err()
					return
				default:
					// 继续处理
				}

				// 尝试处理事件
				if err := h.Handle(event); err != nil {
					b.logger.Error("Error async handling event",
						zap.String("event", string(event.Name())),
						zap.String("handler", h.HandlerName()),
						zap.Error(err),
					)
					errChan <- fmt.Errorf("error handling event %s by %s: %w",
						event.Name(), h.HandlerName(), err)
				}
			}(handler)
		}

		// 等待所有处理器完成
		wg.Wait()
	}()

	return errChan
}

// HasHandlers 检查是否有处理器注册了指定的事件类型
func (b *DefaultEventBus) HasHandlers(eventName contract.EventName) bool {
	b.mu.RLock()
	defer b.mu.RUnlock()

	handlers, exists := b.handlers[eventName]
	return exists && len(handlers) > 0
}

// GetHandlers 获取注册了指定事件类型的所有处理器
func (b *DefaultEventBus) GetHandlers(eventName contract.EventName) []contract.EventHandler {
	b.mu.RLock()
	defer b.mu.RUnlock()

	handlers, exists := b.handlers[eventName]
	if !exists {
		return []contract.EventHandler{}
	}

	// 创建一个副本以避免并发修改
	result := make([]contract.EventHandler, len(handlers))
	copy(result, handlers)
	return result
}

// getTenantIDFromEvent 从事件获取租户ID
func (b *DefaultEventBus) getTenantIDFromEvent(event contract.Event) interface{} {
	if tenantID, exists := event.GetMetadata("tenant_id"); exists {
		return tenantID
	}

	// 从上下文获取
	if siteID, ok := database.SiteIDFrom(event.Context()); ok {
		return siteID
	}

	return nil
}

// shouldHandleEvent 检查处理器是否应该处理此事件（租户隔离）
func (b *DefaultEventBus) shouldHandleEvent(handler contract.EventHandler, event contract.Event) bool {
	// 检查处理器是否实现了租户感知接口
	if tenantAware, ok := handler.(contract.TenantAwareEventHandler); ok {
		eventTenantID := b.getTenantIDFromEvent(event)
		return tenantAware.ShouldHandleForTenant(eventTenantID)
	}

	// 默认情况下，所有处理器都处理所有事件（向后兼容）
	return true
}