/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/port/cli/Root.go
 * @Description: Defines the root command for the CLI application.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package cli

import (
	"fmt"
	"github.com/spf13/cobra"
	"go.uber.org/fx"
	"os"
)

// RootCmd is the main command for the CLI.
var RootCmd = &cobra.Command{
	Use:   "gacms-cli",
	Short: "GACMS CLI is a tool for managing the GACMS application.",
	Long: `A powerful command-line interface to help you manage modules,
themes, and other aspects of your GACMS project.`,
}

// Execute runs the root command. It's the main entry point for the CLI application.
func Execute() {
	if err := RootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

// RegisterCommands is an fx.Invoke function that attaches all registered CLI commands to the root command.
func RegisterCommands(extensionCmd, activationCmd *cobra.Command) {
	// The activationCmd from NewActivationCommand has multiple subcommands.
	// We want to add these subcommands (activate, deactivate, status) directly to the extensionCmd.
	extensionCmd.AddCommand(activationCmd.Commands()...)
	RootCmd.AddCommand(extensionCmd)
}

// CliModule provides all the CLI commands and registration logic to the DI container.
var CliModule = fx.Options(
	fx.Provide(NewExtensionCommand),
	fx.Provide(NewActivationCommand),
	fx.Invoke(RegisterCommands),
) 