/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/modules/site/port/http/controller/DomainBindingMethods.go
 * @Description: 域名绑定管理方法扩展（SiteController的扩展方法）
 * 
 * © 2025 GACMS. All rights reserved.
 */

package controller

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// UpdateDomainBinding 更新域名绑定
func (c *SiteController) UpdateDomainBinding(ctx *gin.Context) {
	siteID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid site ID"})
		return
	}
	
	bindingID, err := strconv.ParseUint(ctx.Param("bindingId"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid binding ID"})
		return
	}
	
	var req struct {
		Domain string `json:"domain" binding:"required"`
	}
	
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	binding, err := c.bindingService.UpdateBinding(uint(bindingID), req.Domain)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	// 通过事件系统记录操作日志
	c.publishOperationLog(ctx, "update", "Updated domain binding: "+req.Domain, "domain_binding", binding.GetID())
	ctx.JSON(http.StatusOK, gin.H{
		"data": binding,
		"message": "Domain binding updated successfully",
	})
}

// DeleteDomainBinding 删除域名绑定
func (c *SiteController) DeleteDomainBinding(ctx *gin.Context) {
	siteID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid site ID"})
		return
	}
	
	bindingID, err := strconv.ParseUint(ctx.Param("bindingId"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid binding ID"})
		return
	}
	
	// 获取绑定信息用于日志
	binding, err := c.bindingService.GetBinding(uint(bindingID))
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Domain binding not found"})
		return
	}
	
	if err := c.bindingService.DeleteBinding(uint(bindingID)); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	// 通过事件系统记录操作日志
	c.publishOperationLog(ctx, "delete", "Deleted domain binding: "+binding.GetDomain(), "domain_binding", binding.GetID())
	ctx.JSON(http.StatusOK, gin.H{
		"message": "Domain binding deleted successfully",
	})
}

// EnableURLRewrite 启用URL重写
func (c *SiteController) EnableURLRewrite(ctx *gin.Context) {
	bindingID, err := strconv.ParseUint(ctx.Param("bindingId"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid binding ID"})
		return
	}
	
	var req struct {
		DefaultController string `json:"default_controller" binding:"required"`
		DefaultAction     string `json:"default_action" binding:"required"`
	}
	
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	if err := c.bindingService.EnableURLRewrite(uint(bindingID), req.DefaultController, req.DefaultAction); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	// 通过事件系统记录操作日志
	c.publishOperationLog(ctx, "enable_url_rewrite", "Enabled URL rewrite for binding "+strconv.Itoa(int(bindingID)), "domain_binding", bindingID)
	ctx.JSON(http.StatusOK, gin.H{
		"message": "URL rewrite enabled successfully",
	})
}

// DisableURLRewrite 禁用URL重写
func (c *SiteController) DisableURLRewrite(ctx *gin.Context) {
	bindingID, err := strconv.ParseUint(ctx.Param("bindingId"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid binding ID"})
		return
	}
	
	if err := c.bindingService.DisableURLRewrite(uint(bindingID)); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	// 通过事件系统记录操作日志
	c.publishOperationLog(ctx, "disable_url_rewrite", "Disabled URL rewrite for binding "+strconv.Itoa(int(bindingID)), "domain_binding", bindingID)
	ctx.JSON(http.StatusOK, gin.H{
		"message": "URL rewrite disabled successfully",
	})
}

// ListURLRules 获取URL重写规则列表
func (c *SiteController) ListURLRules(ctx *gin.Context) {
	bindingID, err := strconv.ParseUint(ctx.Param("bindingId"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid binding ID"})
		return
	}

	rules, err := c.bindingService.GetURLRulesByDomainBinding(uint(bindingID))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data": rules,
		"message": "URL rules retrieved successfully",
	})
}

// CreateURLRule 创建URL重写规则
func (c *SiteController) CreateURLRule(ctx *gin.Context) {
	bindingID, err := strconv.ParseUint(ctx.Param("bindingId"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid binding ID"})
		return
	}

	var req struct {
		RuleName    string `json:"rule_name" binding:"required"`
		Pattern     string `json:"pattern" binding:"required"`
		Replacement string `json:"replacement" binding:"required"`
		Priority    int    `json:"priority"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	rule, err := c.bindingService.CreateURLRule(uint(bindingID), req.RuleName, req.Pattern, req.Replacement, req.Priority)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 通过事件系统记录操作日志
	c.publishOperationLog(ctx, "create", "Created URL rule: "+req.RuleName, "url_rule", rule.GetID())
	ctx.JSON(http.StatusCreated, gin.H{
		"data": rule,
		"message": "URL rule created successfully",
	})
}

// UpdateURLRule 更新URL重写规则
func (c *SiteController) UpdateURLRule(ctx *gin.Context) {
	bindingID, err := strconv.ParseUint(ctx.Param("bindingId"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid binding ID"})
		return
	}

	ruleID, err := strconv.ParseUint(ctx.Param("ruleId"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid rule ID"})
		return
	}

	var req struct {
		RuleName    string `json:"rule_name" binding:"required"`
		Pattern     string `json:"pattern" binding:"required"`
		Replacement string `json:"replacement" binding:"required"`
		Priority    int    `json:"priority"`
		IsActive    bool   `json:"is_active"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	rule, err := c.bindingService.UpdateURLRule(uint(ruleID), req.RuleName, req.Pattern, req.Replacement, req.Priority, req.IsActive)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 通过事件系统记录操作日志
	c.publishOperationLog(ctx, "update", "Updated URL rule: "+req.RuleName, "url_rule", rule.GetID())
	ctx.JSON(http.StatusOK, gin.H{
		"data": rule,
		"message": "URL rule updated successfully",
	})
}

// DeleteURLRule 删除URL重写规则
func (c *SiteController) DeleteURLRule(ctx *gin.Context) {
	bindingID, err := strconv.ParseUint(ctx.Param("bindingId"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid binding ID"})
		return
	}

	ruleID, err := strconv.ParseUint(ctx.Param("ruleId"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid rule ID"})
		return
	}

	if err := c.bindingService.DeleteURLRule(uint(ruleID)); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 通过事件系统记录操作日志
	c.publishOperationLog(ctx, "delete", "Deleted URL rule "+strconv.Itoa(int(ruleID)), "url_rule", ruleID)
	ctx.JSON(http.StatusOK, gin.H{
		"message": "URL rule deleted successfully",
	})
}
