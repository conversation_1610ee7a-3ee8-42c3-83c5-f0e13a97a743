/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON> Nieh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/system/seeder.go
 * @Description: Seeds initial data for the system module.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package system

import (
	"gacms/internal/modules/system/domain/model"
	"gorm.io/gorm"
	"log"
)

func SeedSettings(db *gorm.DB) {
	// Use a pointer for siteID to handle NULL values
	var site1ID *uint
	val1 := uint(1)
	site1ID = &val1

	settings := []model.Setting{
		// --- Platform-level Settings (site_id is NULL) ---
		{SiteID: nil, Group: "email", Key: "smtp_host", Value: "smtp.example.com", Type: model.SettingTypeString, Name: "SMTP Host", Description: "The hostname of the SMTP server."},
		{SiteID: nil, Group: "email", Key: "smtp_port", Value: "587", Type: model.SettingTypeNumber, Name: "SMTP Port", Description: "The port of the SMTP server."},
		{SiteID: nil, Group: "email", Key: "smtp_user", Value: "<EMAIL>", Type: model.SettingTypeString, Name: "SMTP Username", Description: "Username for SMTP authentication."},
		{SiteID: nil, Group: "email", Key: "smtp_password", Value: "password", Type: model.SettingTypeString, Name: "SMTP Password", Description: "Password for SMTP authentication.", IsSecret: true},
		{SiteID: nil, Group: "storage", Key: "provider", Value: "local", Type: model.SettingTypeString, Name: "Storage Provider", Description: "Could be 'local', 's3', 'oss', etc."},
		{SiteID: nil, Group: "storage", Key: "s3_access_key", Value: "", Type: model.SettingTypeString, Name: "S3 Access Key", IsSecret: true},

		// --- Site-specific Settings (for site_id = 1) ---
		{SiteID: site1ID, Group: "general", Key: "site_name", Value: "My First Site", Type: model.SettingTypeString, Name: "Site Name", Description: "The public name of the site.", IsPublic: true},
		{SiteID: site1ID, Group: "general", Key: "site_logo", Value: "/uploads/logo.png", Type: model.SettingTypeString, Name: "Site Logo", Description: "URL to the site logo.", IsPublic: true},
		{SiteID: site1ID, Group: "seo", Key: "meta_description", Value: "Default meta description for the site.", Type: model.SettingTypeText, Name: "Meta Description"},
	}

	for _, setting := range settings {
		var existing model.Setting
		query := db.Where("`group` = ? AND `key` = ?", setting.Group, setting.Key)
		if setting.SiteID == nil {
			query = query.Where("site_id IS NULL")
		} else {
			query = query.Where("site_id = ?", *setting.SiteID)
		}

		if err := query.First(&existing).Error; err == gorm.ErrRecordNotFound {
			if err := db.Create(&setting).Error; err != nil {
				log.Printf("Failed to seed setting '%s' for group '%s': %v", setting.Key, setting.Group, err)
			}
		}
	}
} 