/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/theme/application/service/ThemeService.go
 * @Description: Implements the full suite of theme management logic, including creation,
 *               editing, and resource loading with inheritance. This is the "theme workshop".
 *
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"encoding/json"
	"errors"
	"fmt"
	extService "gacms/internal/modules/extension/application/service"
	themeContract "gacms/internal/modules/theme/domain/contract"
	"gacms/internal/modules/theme/domain/model"
	"os"
	"path/filepath"
)

const maxInheritanceDepth = 10

type ThemeService struct {
	settingRepo  themeContract.ThemeSettingRepository
	extensionSvc *extService.ExtensionService
}

func NewThemeService(
	settingRepo themeContract.ThemeSettingRepository,
	extensionSvc *extService.ExtensionService,
) *ThemeService {
	return &ThemeService{
		settingRepo:  settingRepo,
		extensionSvc: extensionSvc,
	}
}

// CreateChildTheme creates a "hybrid" child theme.
// It creates a directory with just a manifest pointing to the parent,
// then notifies ExtensionService to register it.
func (s *ThemeService) CreateChildTheme(baseThemeDirName string, siteID uint, userID uint) (*extService.ExtensionManifest, error) {
	// [PERMISSION CHECK] Placeholder for checking if the user (userID)
	// has permission to create/edit themes for the given site (siteID).
	// if !auth.Can(userID, "theme:create", siteID) {
	// 	 return nil, errors.New("permission denied")
	// }

	baseManifest, err := s.extensionSvc.GetManifest("theme", baseThemeDirName)
	if err != nil {
		return nil, fmt.Errorf("base theme '%s' not found: %w", baseThemeDirName, err)
	}

	baseThemePath, err := s.extensionSvc.FindThemePath(baseThemeDirName)
	if err != nil {
		return nil, err
	}
	parentDir := filepath.Dir(baseThemePath)

	newThemeDirName := fmt.Sprintf("%s-site%d", baseThemeDirName, siteID)
	newThemePath := filepath.Join(parentDir, newThemeDirName)

	if _, err := os.Stat(newThemePath); !os.IsNotExist(err) {
		// Child theme already exists, no need to create.
		return s.extensionSvc.GetManifest("theme", newThemeDirName)
	}

	// Create the directory for the child theme
	if err := os.MkdirAll(newThemePath, os.ModePerm); err != nil {
		return nil, fmt.Errorf("failed to create child theme directory: %w", err)
	}

	// Create the manifest file for the child theme
	newManifest := map[string]interface{}{
		"name":        fmt.Sprintf("%s (Site %d)", baseManifest.Name, siteID),
		"version":     baseManifest.Version,
		"type":        "theme",
		"description": fmt.Sprintf("Customized version of %s for Site %d.", baseManifest.Name, siteID),
		"extends":     baseThemeDirName,
	}

	manifestBytes, err := json.MarshalIndent(newManifest, "", "  ")
	if err != nil {
		os.RemoveAll(newThemePath)
		return nil, fmt.Errorf("failed to marshal new manifest: %w", err)
	}

	if err := os.WriteFile(filepath.Join(newThemePath, "theme.json"), manifestBytes, 0644); err != nil {
		os.RemoveAll(newThemePath)
		return nil, fmt.Errorf("failed to write new manifest: %w", err)
	}

	// Notify ExtensionService to discover this new extension
	if err := s.extensionSvc.SyncWithFilesystem(); err != nil {
		// This is not a fatal error, but should be logged.
		fmt.Printf("Warning: SyncWithFilesystem failed after creating child theme: %v\n", err)
	}

	return s.extensionSvc.GetManifest("theme", newThemeDirName)
}

// GetThemeSettings retrieves the merged settings for a theme, applying inheritance and database overrides.
func (s *ThemeService) GetThemeSettings(siteID uint, themeName string) (map[string]interface{}, error) {
	// [PERMISSION CHECK] Placeholder for checking read access to theme settings for the site.

	chain, err := s.getInheritanceChain(themeName)
	if err != nil {
		return nil, err
	}

	// Merge settings from the bottom up (base theme first)
	mergedSettings := make(map[string]interface{})
	for i := len(chain) - 1; i >= 0; i-- {
		theme := chain[i]
		// Merge settings from theme.json
		if themeSettings, ok := theme["settings"].(map[string]interface{}); ok {
			for k, v := range themeSettings {
				mergedSettings[k] = v
			}
		}
	}

	// Apply database overrides for the specific site and theme
	dbOverrides, err := s.settingRepo.GetSettings(siteID, themeName)
	if err != nil && !errors.Is(err, os.ErrNotExist) { // It's ok if no record exists
		return nil, fmt.Errorf("failed to get db overrides: %w", err)
	}

	if dbOverrides != nil {
		for k, v := range dbOverrides.Settings {
			mergedSettings[k] = v
		}
	}

	return mergedSettings, nil
}

// UpdateThemeSettings saves custom settings for a theme to the database.
func (s *ThemeService) UpdateThemeSettings(siteID uint, themeName string, settings map[string]interface{}, userID uint) error {
	// [PERMISSION CHECK] Placeholder for checking write access.
	// if !auth.Can(userID, "theme:edit", siteID) {
	// 	 return errors.New("permission denied")
	// }

	return s.settingRepo.SaveSettings(&model.ThemeSetting{
		SiteID:    siteID,
		ThemeName: themeName,
		Settings:  settings,
	})
}

// GetThemeFileContent finds a file within a theme's inheritance chain.
func (s *ThemeService) GetThemeFileContent(themeName, filePath string) ([]byte, error) {
	chain, err := s.getInheritanceChain(themeName)
	if err != nil {
		return nil, err
	}

	// Look for the file from the top down (child theme first)
	for _, theme := range chain {
		dirName, ok := theme["directoryName"].(string)
		if !ok {
			continue // Should not happen if chain is built correctly
		}

		currentThemePath, err := s.extensionSvc.FindThemePath(dirName)
		if err != nil {
			continue // Theme path not found, try next in chain
		}

		fullPath := filepath.Join(currentThemePath, filePath)
		if _, err := os.Stat(fullPath); !os.IsNotExist(err) {
			return os.ReadFile(fullPath)
		}
	}

	return nil, fmt.Errorf("file '%s' not found in theme '%s' or its parents", filePath, themeName)
}

// getInheritanceChain builds the list of themes from child to parent.
func (s *ThemeService) getInheritanceChain(themeName string) ([]map[string]interface{}, error) {
	var chain []map[string]interface{}
	currentThemeName := themeName

	for i := 0; i < maxInheritanceDepth; i++ {
		manifest, err := s.extensionSvc.GetManifestAsMap("theme", currentThemeName)
		if err != nil {
			return nil, fmt.Errorf("failed to get manifest for '%s': %w", currentThemeName, err)
		}

		manifest["directoryName"] = currentThemeName
		chain = append(chain, manifest)

		parentTheme, ok := manifest["extends"].(string)
		if !ok || parentTheme == "" {
			break // End of the chain
		}
		currentThemeName = parentTheme
	}
	return chain, nil
} 