/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2024-07-16
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2024-07-16
 * @FilePath: internal/modules/user/infrastructure/repository/GormPermissionRepository.go
 * @Description: GORM implementation for the PermissionRepository contract.
 *
 * © 2024 GACMS. All rights reserved.
 */
package repository

import (
	"gacms/internal/modules/user/domain/contract"
	"gacms/internal/modules/user/domain/model"
	"gacms/pkg/database"
	"gacms/pkg/logger"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type GormPermissionRepository struct {
	db  *gorm.DB
	log *logger.Logger
}

func NewGormPermissionRepository(db *gorm.DB, log *logger.Logger) contract.PermissionRepository {
	return &GormPermissionRepository{db: db, log: log}
}

func (r *GormPermissionRepository) Create(ctx *gin.Context, permission *model.Permission) error {
	return r.db.WithContext(ctx).Create(permission).Error
}

func (r *GormPermissionRepository) GetByID(ctx *gin.Context, id uint) (*model.Permission, error) {
	var permission model.Permission
	err := r.db.WithContext(ctx).First(&permission, id).Error
	return &permission, err
}

func (r *GormPermissionRepository) GetBySlug(ctx *gin.Context, slug string) (*model.Permission, error) {
	var permission model.Permission
	err := r.db.WithContext(ctx).Where("slug = ?", slug).First(&permission).Error
	return &permission, err
}

func (r *GormPermissionRepository) List(ctx *gin.Context, options *database.ListOptions) ([]*model.Permission, int64, error) {
	var permissions []*model.Permission
	var total int64

	query := r.db.WithContext(ctx).Model(&model.Permission{})

	if options.Search != "" {
		searchPattern := "%" + options.Search + "%"
		query = query.Where("slug LIKE ? OR description LIKE ?", searchPattern, searchPattern)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if options.SortBy != "" {
		order := options.SortBy
		if options.SortDesc {
			order += " desc"
		}
		query = query.Order(order)
	}

	query = query.Limit(options.PageSize).Offset((options.Page - 1) * options.PageSize)

	if err := query.Find(&permissions).Error; err != nil {
		return nil, 0, err
	}

	return permissions, total, nil
}

func (r *GormPermissionRepository) Update(ctx *gin.Context, permission *model.Permission) error {
	return r.db.WithContext(ctx).Save(permission).Error
}

func (r *GormPermissionRepository) Delete(ctx *gin.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.Permission{}, id).Error
}

func (r *GormPermissionRepository) GetByIDs(ctx *gin.Context, ids []uint) ([]*model.Permission, error) {
	var permissions []*model.Permission
	err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&permissions).Error
	return permissions, err
} 