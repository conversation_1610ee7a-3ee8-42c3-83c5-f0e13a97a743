/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/category/application/service/CategoryService.go
 * @Description: Application service for category-related business logic.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package service

import (
	"context"
	"fmt"
	"gacms/internal/modules/category/domain/contract"
	"gacms/internal/modules/category/domain/model"
	pkgContract "gacms/pkg/contract"
	"gacms/pkg/contract/events"
)

type CategoryService struct {
	repo   contract.CategoryRepository
	appCtx pkgContract.AppContext
}

func NewCategoryService(
	repo contract.CategoryRepository,
	appCtx pkgContract.AppContext,
) *CategoryService {
	return &CategoryService{
		repo:   repo,
		appCtx: appCtx,
	}
}

// CreateCategory creates a new category, publishes an event, and logs the action.
func (s *CategoryService) CreateCategory(ctx context.Context, category *model.Category) error {
	if err := s.repo.Create(ctx, category); err != nil {
		return err
	}

	event := events.NewGenericEvent("category.created", category)
	s.appCtx.Events().Publish(event)

	s.recordActionLog(ctx, fmt.Sprintf("Created category: %s", category.Name), "success")

	return nil
}

// UpdateCategory updates an existing category, publishes an event, and logs the action.
func (s *CategoryService) UpdateCategory(ctx context.Context, category *model.Category) error {
	if err := s.repo.Update(ctx, category); err != nil {
		return err
	}

	event := events.NewGenericEvent("category.updated", category)
	s.appCtx.Events().Publish(event)

	s.recordActionLog(ctx, fmt.Sprintf("Updated category: %s (ID: %d)", category.Name, category.ID), "success")

	return nil
}

// DeleteCategory deletes a category, publishes an event, and logs the action.
func (s *CategoryService) DeleteCategory(ctx context.Context, id uint) error {
	category, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return err
	}

	if err := s.repo.Delete(ctx, id); err != nil {
		return err
	}

	event := events.NewGenericEvent("category.deleted", category)
	s.appCtx.Events().Publish(event)

	s.recordActionLog(ctx, fmt.Sprintf("Deleted category: %s (ID: %d)", category.Name, category.ID), "success")

	return nil
}

// recordActionLog is a helper to asynchronously record an action to the actionlog service.
func (s *CategoryService) recordActionLog(ctx context.Context, description string, status string) {
	go func() {
		payload := map[string]string{
			"description": description,
			"status":      status,
		}
		// Using a background context for the API call itself, but we should propagate auth from the original context.
		bgCtx := context.Background()
		if token, ok := s.appCtx.Auth().TokenFrom(ctx); ok {
			bgCtx = s.appCtx.Auth().ContextWithToken(bgCtx, token)
		}

		_, err := s.appCtx.APIClient().Post(bgCtx, "actionlog", "/logs", payload)
		if err != nil {
			// Use the system logger to record the failure of the API call.
			s.appCtx.Logger().Error(context.Background(), "Failed to record action log via API", "error", err)
		}
	}()
}

// GetCategory retrieves a single category by its ID.
func (s *CategoryService) GetCategory(ctx context.Context, id uint) (*model.Category, error) {
	return s.repo.GetByID(ctx, id)
}

// GetCategoryTree builds a tree structure from a flat list of categories.
func (s *CategoryService) GetCategoryTree(ctx context.Context) ([]*model.Category, error) {
	categories, err := s.repo.GetAll(ctx)
	if err != nil {
		return nil, err
	}
	return s.buildTree(categories, 0), nil
}

// buildTree is a recursive helper to build the category tree.
func (s *CategoryService) buildTree(categories []*model.Category, parentID uint) []*model.Category {
	var tree []*model.Category
	for _, category := range categories {
		if category.ParentID == parentID {
			children := s.buildTree(categories, category.ID)
			if children != nil {
				category.Children = children
			}
			tree = append(tree, category)
		}
	}
	return tree
}

// NOTE: A proper GetCategoryTree method would now fetch all categories
// from the repository and then build the tree. This logic can be
// more complex and might involve a dedicated TreeBuilder utility.
// For this refactoring, we focus on correcting the dependency. 