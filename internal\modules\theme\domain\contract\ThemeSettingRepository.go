/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/theme/domain/contract/ThemeSettingRepository.go
 * @Description: Defines the contract for theme settings persistence.
 *
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"gacms/internal/modules/theme/domain/model"
)

// ThemeSettingRepository defines the methods for interacting with theme setting storage.
type ThemeSettingRepository interface {
	// GetSettings retrieves the custom settings for a given site and theme.
	// Returns a ThemeSetting object or an error if something goes wrong.
	// It should handle the case where no settings are found gracefully (e.g., return nil, nil or a specific error).
	GetSettings(siteID uint, themeName string) (*model.ThemeSetting, error)

	// SaveSettings creates or updates the custom settings for a given site and theme.
	SaveSettings(setting *model.ThemeSetting) error
} 