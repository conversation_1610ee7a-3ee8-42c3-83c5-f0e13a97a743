<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专题管理 - GACMS</title>
    <link rel="icon" href="./assets/images/favicon.ico" type="image/x-icon">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 100%;
            background-color: #3b82f6;
            border-radius: 2px;
        }
        .action-button {
            transition: all 0.3s ease;
        }
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
    </style>
</head>
<body class="flex h-screen text-gray-200">
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>

        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- Page Title -->
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-white">专题管理</h1>
                <a href="#" class="action-button flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-700 text-white px-5 py-2 rounded-lg font-medium hover:from-blue-600 hover:to-blue-800 transition-all duration-300 relative overflow-hidden">
                    <i class="fas fa-plus mr-2"></i> 新建专题
                </a>
            </div>

            <!-- Topics List Card -->
            <div class="bg-gray-800/10 border border-gray-700/50 rounded-xl p-6">
                <h2 class="text-xl font-bold text-white relative pl-4 section-title mb-6">专题列表</h2>

                <!-- Search and Filter -->
                <div class="flex justify-between items-center mb-6 flex-wrap gap-4">
                    <div class="relative">
                        <input type="text" placeholder="搜索专题..." class="bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 pl-10 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                    <div>
                        <select class="bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500">
                            <option>所有分类</option>
                            <option>科技前沿</option>
                            <option>产品评测</option>
                            <option>行业动态</option>
                        </select>
                    </div>
                </div>

                <!-- Topics Table -->
                <div class="overflow-x-auto">
                    <table class="w-full text-left">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="p-4">ID</th>
                                <th class="p-4">专题名称</th>
                                <th class="p-4">所属分类</th>
                                <th class="p-4">创建者</th>
                                <th class="p-4">发布日期</th>
                                <th class="p-4 text-center">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border-b border-gray-700/50 hover:bg-gray-800/40 transition-colors duration-200">
                                <td class="p-4">1</td>
                                <td class="p-4">AI在内容创作中的应用</td>
                                <td class="p-4">科技前沿</td>
                                <td class="p-4">Admin</td>
                                <td class="p-4">2024-07-21</td>
                                <td class="p-4 text-center">
                                    <a href="#" class="text-blue-400 hover:text-blue-300 mr-4"><i class="fas fa-edit"></i> 编辑</a>
                                    <a href="#" class="text-red-400 hover:text-red-300"><i class="fas fa-trash"></i> 删除</a>
                                </td>
                            </tr>
                            <tr class="border-b border-gray-700/50 hover:bg-gray-800/40 transition-colors duration-200">
                                <td class="p-4">2</td>
                                <td class="p-4">GACMS v1.0 新功能深度解析</td>
                                <td class="p-4">产品评测</td>
                                <td class="p-4">Admin</td>
                                <td class="p-4">2024-07-20</td>
                                <td class="p-4 text-center">
                                    <a href="#" class="text-blue-400 hover:text-blue-300 mr-4"><i class="fas fa-edit"></i> 编辑</a>
                                    <a href="#" class="text-red-400 hover:text-red-300"><i class="fas fa-trash"></i> 删除</a>
                                </td>
                            </tr>
                             <tr class="border-b border-gray-700/50 hover:bg-gray-800/40 transition-colors duration-200">
                                <td class="p-4">3</td>
                                <td class="p-4">内容管理系统的发展趋势</td>
                                <td class="p-4">行业动态</td>
                                <td class="p-4">Alice</td>
                                <td class="p-4">2024-07-19</td>
                                <td class="p-4 text-center">
                                    <a href="#" class="text-blue-400 hover:text-blue-300 mr-4"><i class="fas fa-edit"></i> 编辑</a>
                                    <a href="#" class="text-red-400 hover:text-red-300"><i class="fas fa-trash"></i> 删除</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                 <!-- Pagination -->
                <div class="flex justify-between items-center mt-6">
                    <span class="text-sm text-gray-400">显示 1-3 of 3 条记录</span>
                    <div class="flex">
                        <a href="#" class="px-3 py-1 bg-gray-700 rounded-l-lg hover:bg-gray-600 transition-colors">-</a>
                        <a href="#" class="px-3 py-1 bg-blue-600 text-white">1</a>
                        <a href="#" class="px-3 py-1 bg-gray-700 rounded-r-lg hover:bg-gray-600 transition-colors">+</a>
                    </div>
                </div>
            </div>
        </div>

        <footer class="page-footer mt-4 py-6 border-t border-gray-700 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
</body>
</html> 