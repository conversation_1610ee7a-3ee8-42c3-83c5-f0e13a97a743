/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/user/port/http/middleware/PermissionMiddleware.go
 * @Description: Middleware for RBAC permission checking before processing requests.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package middleware

import (
	"gacms/internal/modules/user/application/service"
	"gacms/internal/modules/user/domain/model"
	"gacms/internal/port/http/response"
	"net/http"

	"github.com/gin-gonic/gin"
)

// PermissionMiddleware provides middleware functions for RBAC permission checking.
type PermissionMiddleware struct {
	permissionSvc *service.PermissionService
}

// NewPermissionMiddleware creates a new instance of PermissionMiddleware.
func NewPermissionMiddleware(permissionSvc *service.PermissionService) *PermissionMiddleware {
	return &PermissionMiddleware{
		permissionSvc: permissionSvc,
	}
}

// RequirePermission returns a middleware function that checks if the user has the specified permission.
// This middleware should be placed after the Auth middleware that populates the user context.
func (m *PermissionMiddleware) RequirePermission(permissionSlug string, userType model.UserType) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from the context set by auth middleware
		userID, exists := c.Get("user_id")
		if !exists {
			response.Fail(c, http.StatusUnauthorized, "Authentication required")
			c.Abort()
			return
		}

		// Check if the user has the required permission
		hasPermission, err := m.permissionSvc.Can(c.Request.Context(), userID.(uint), userType, permissionSlug)
		if err != nil {
			response.Fail(c, http.StatusInternalServerError, "Failed to check permissions")
			c.Abort()
			return
		}

		if !hasPermission {
			response.Fail(c, http.StatusForbidden, "You don't have permission to perform this action")
			c.Abort()
			return
		}

		// If the user has the permission, proceed with the request
		c.Next()
	}
}

// RequireAdminPermission is a convenience wrapper for requiring admin permissions
func (m *PermissionMiddleware) RequireAdminPermission(permissionSlug string) gin.HandlerFunc {
	return m.RequirePermission(permissionSlug, model.AdminUser)
}

// RequireMemberPermission is a convenience wrapper for requiring member permissions
func (m *PermissionMiddleware) RequireMemberPermission(permissionSlug string) gin.HandlerFunc {
	return m.RequirePermission(permissionSlug, model.MemberUser)
} 