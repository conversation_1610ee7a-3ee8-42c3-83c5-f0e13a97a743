<!--
Author: <PERSON><PERSON> Nieh
EMAIL: <EMAIL>
Copyright (c) 2025 Cion Nieh
-->
# 接口设计文档 (Interface Design) - GACMS

## 目录

- [1. 文档信息](#1-文档信息)
  - [1.1 版本历史](#11-版本历史)
  - [1.2 文档目的](#12-文档目的)
  - [1.3 相关文档引用](#13-相关文档引用)
- [2. 接口设计概述](#2-接口设计概述)
  - [2.1 设计原则](#21-设计原则)
  - [2.2 接口分类](#22-接口分类)
  - [2.3 通用规范](#23-通用规范)
  - [2.4 模块化API规范](#24-模块化api规范)
- [3. RESTful API设计](#3-restful-api设计)
  - [3.1 认证与授权](#31-认证与授权)
  - [3.2 请求与响应格式](#32-请求与响应格式)
  - [3.3 错误处理](#33-错误处理)
  - [3.4 API文档规范](#34-api文档规范)
- [4. 模块API接口 (Module API Interfaces)](#4-模块api接口-module-api-interfaces)
  - [4.1 核心平台模块 (Core Module)](#41-核心平台模块-core-module)
  - [4.2 用户模块 (User Module)](#42-用户模块-user-module)
  - [4.3 内容模块 (Post Module)](#43-内容模块-post-module)
  - [4.4 系统模块 (System Module)](#44-系统模块-system-module)
- [5. 内部服务接口](#5-内部服务接口)
  - [5.1 缓存服务接口](#51-缓存服务接口)
  - [5.2 搜索服务接口](#52-搜索服务接口)
  - [5.3 消息队列接口](#53-消息队列接口)
- [6. 外部集成接口](#6-外部集成接口)
  - [6.1 第三方认证接口](#61-第三方认证接口)
  - [6.2 微信生态接口](#62-微信生态接口)
  - [6.3 支付接口](#63-支付接口)
- [7. WebHooks](#7-webhooks)
  - [7.1 系统事件钩子](#71-系统事件钩子)
  - [7.2 插件事件钩子](#72-插件事件钩子)
- [8. 安全设计](#8-安全设计)
  - [8.1 接口安全策略](#81-接口安全策略)
  - [8.2 数据加密传输](#82-数据加密传输)
  - [8.3 访问控制](#83-访问控制)
- [9. 性能优化](#9-性能优化)
  - [9.1 接口性能指标](#91-接口性能指标)
  - [9.2 优化策略](#92-优化策略)

## 1. 文档信息

### 1.1 版本历史

| 版本号 | 修订日期   | 修订人     | 修订描述                                     |
| ------ | ---------- | ---------- | -------------------------------------------- |
| V1.0.0 | 2025-05-17 | Cion Nieh  | 初始版本创建                                   |
| V1.1.0 | {current_date} | Trae AI   | **重大更新**：根据ADR-001 v4架构，明确API统一入口、无版本号原则、标准响应格式和扩展模块API规范。 |
| V1.2.0 | {current_date} | Trae AI   | **架构对齐**：根据ADR-001 v5（模块化核心）架构，重构API结构为模块化路径，统一命名规范，并明确模块API设计准则。 |

### 1.2 文档目的

本文档旨在详细定义亘安网站内容管理系统 (GACMS) 的所有对外及对内接口。其主要目的包括：

- 为前端开发团队提供清晰、准确的API调用规范。
- 为后端开发团队提供统一的接口实现标准。
- 为第三方开发者或系统集成提供必要的接口信息。
- 作为系统测试和验收的依据之一。
- 促进团队成员对系统功能的理解和沟通。

### 1.3 相关文档引用

本文档的编写参考了以下相关文档，并在必要时进行引用：

- [需求规格说明书 (RSD.md)](./RSD.md)
- [系统设计文档 (SDD.md)](./SDD.md)
- [系统架构设计文档 (SADD.md)](./SADD.md)
- [数据模型设计文档 (Data_Model_Design.md)](./Data_Model_Design.md)
- [技术选型文档 (Technology_Selection.md)](./Technology_Selection.md)

## 2. 接口设计概述

### 2.1 设计原则

GACMS的接口设计遵循以下核心原则，以确保接口的健壮性、易用性、可扩展性和安全性：

- **RESTful风格**: 采用业界广泛接受的RESTful架构风格设计API，利用HTTP方法（GET, POST, PUT, DELETE等）对资源进行操作。
- **资源导向**: 接口设计以资源为中心，URL清晰地标识资源。
- **无状态性**: 服务器不保存客户端的会话状态，每个请求都应包含所有必要的信息。
- **单一最新版本**: 遵循ADR-001 v4架构原则，API不进行版本控制（即URL中不包含`/v1/`, `/v2/`等），所有接口始终代表系统的最新稳定版本。接口演进通过非破坏性变更实现。
- **统一接口**: 提供一致的接口设计模式，包括URL结构、请求/响应格式、错误处理机制等。
- **安全性**: 所有敏感操作和数据传输必须经过认证和授权，并采用HTTPS加密。

- **易用性**: 接口设计应简单直观，易于理解和集成。
- **可扩展性**: 接口设计应考虑未来的功能扩展，保持良好的向前兼容性。
- **文档驱动**: 提供清晰、完整、最新的API文档。
- **遵循项目通用原则**: 如接口隔离原则（ISP）、单一职责原则（SRP）、依赖倒置原则（DIP）等在 <mcsymbol name="project_rules.md" path=".trae/rules/project_rules.md" type="file"></mcsymbol> 中定义的规范。

### 2.2 接口分类

GACMS的接口主要分为以下几类：

- **核心API接口**: 提供系统核心功能的接口，如用户认证、内容管理、用户管理、系统配置等。
- **扩展API接口**: 提供系统扩展功能的接口，如多站点管理、插件管理、主题管理、数据分析、国际化等。
- **内部服务接口**: 系统内部模块之间调用的接口，不对外暴露，如缓存服务、搜索服务、消息队列等。
- **外部集成接口**: 用于与第三方系统或服务集成的接口，如第三方认证、支付接口、社交媒体分享等。
- **WebHooks**: 当系统中发生特定事件时，主动向外部URL发送通知的机制。

### 2.3 通用规范

所有API接口均需遵循以下通用规范：

- **基础路径 (Base Path)**: 所有后端API都必须以 `/api` 作为基础路径前缀。例如: `https://yourdomain.com/api/admins`。
- **URL命名**: 
    - 使用小写字母。
    - 单词之间使用下划线 `_` 连接 (snake_case)。例如: `user_roles`。
    - 资源名使用复数形式，例如 `/api/admins`, `/api/posts`。
    - 避免在URL中使用动词，HTTP方法本身已表达操作意图。
- **URL命名**:
    - 遵循`小驼峰命名法` (lowerCamelCase)，与项目整体规范保持一致。例如：`userRoles`。
    - 资源名使用复数形式。例如：`/api/user/users`, `/api/post/posts`。
    - 避免在URL中使用动词，HTTP方法本身已表达操作意图。
    - 完整的URL结构应为：`/api/[module_slug]/[resource_name]`。详见 `2.4 模块化API规范`。
- **HTTP方法**: 
    - `GET`: 获取资源。
    - `POST`: 创建新资源或执行特定操作。
    - `PUT`: 完整更新已存在的资源。
    - `PATCH`: 部分更新已存在的资源。
    - `DELETE`: 删除资源。
- **请求头 (Request Headers)**:
    - `Content-Type`: 对于包含请求体的请求 (POST, PUT, PATCH)，通常为 `application/json`。
    - `Accept`: 客户端期望接收的响应格式，通常为 `application/json`。
    - `Authorization`: 用于传递认证凭证，例如 `Bearer <token>`。
    - `Accept-Language`: 客户端期望的响应语言，例如 `en-US`, `zh-CN`。
- **响应头 (Response Headers)**:
    - `Content-Type`: 响应体的MIME类型，通常为 `application/json; charset=utf-8`。
    - `Cache-Control`, `ETag`, `Last-Modified`: 用于HTTP缓存控制。
- **日期和时间格式**: 所有日期和时间均采用ISO 8601格式 (例如 `YYYY-MM-DDTHH:mm:ss.sssZ`)，并使用UTC时区。
- **分页 (Pagination)**: 对于返回列表数据的接口，支持分页参数：
    - `page` (integer, optional, default: 1): 请求的页码。
    - `per_page` (integer, optional, default: 15): 每页返回的记录数 (最大值可由系统配置限定)。
    - 响应中应包含分页信息，如 `total_items`, `total_pages`, `current_page`, `per_page`。
- **排序 (Sorting)**: 对于返回列表数据的接口，支持排序参数：
    - `sort_by` (string, optional): 排序字段，例如 `created_at`。
    - `sort_order` (string, optional, default: `desc`): 排序顺序 (`asc` 或 `desc`)。
- **过滤 (Filtering)**: 对于返回列表数据的接口，支持基于字段的过滤参数，具体参数名根据资源属性定义。
- **字段选择 (Field Selection)**: 允许客户端指定响应中需要返回的字段，以减少数据传输量。
    - `fields` (string, optional): 逗号分隔的字段列表，例如 `id,name,email`。

### 2.4 模块化API规范

根据ADR-001 v5架构，所有API都由模块提供。为保证API的清晰、一致和无冲突，所有模块的API必须遵循以下规范：

- **模块化路径前缀**: 所有模块提供的API，其URL路径必须以该模块的唯一标识（`module_slug`）作为前缀。
  - **格式**: `/api/[module_slug]/...`
  - **示例**:
    - 用户模块 (`user`): `/api/user/users`, `/api/user/roles`
    - 文章模块 (`post`): `/api/post/posts`, `/api/post/categories`
    - 论坛模块 (`forum`): `/api/forum/threads`, `/api/forum/replies`
- **核心模块**: 平台核心提供的极少数API（如获取系统状态、所有已加载模块列表等），可以使用 `core` 作为其 `module_slug`。例如：`/api/core/status`。
- **路由自治**: 每个模块负责自身的路由注册。模块的 `module.go` 文件应将其所有Controller的路由注册到由平台核心提供的全局路由器中。

## 3. RESTful API设计

### 3.1 认证与授权

- **认证机制**: 
    - 主要采用基于 **JSON Web Tokens (JWT)** 的Bearer Token认证方式。
    - 用户登录成功后，服务器颁发一个Access Token和一个可选的Refresh Token。
    - Access Token用于后续请求的身份验证，通过HTTP `Authorization` 头部传递 (`Authorization: Bearer <access_token>`)。
    - Access Token具有较短的有效期，过期后需要使用Refresh Token获取新的Access Token。
    - 对于某些公开接口（如获取文章列表），可以无需认证或采用API Key方式进行速率限制。
- **授权机制**: 
    - 基于角色的访问控制 (RBAC) 和/或基于权限的访问控制 (PBAC)。
    - 用户的角色和权限在JWT中或通过后端查询确定。
    - 接口将根据用户拥有的权限决定是否允许访问特定资源或执行特定操作。
    - 未授权的访问将返回 `403 Forbidden` 错误。

### 3.2 请求与响应格式

- **请求体格式**: 
    - 对于 `POST`, `PUT`, `PATCH` 请求，请求体统一使用 `application/json` 格式。
    - 文件上传等特殊情况使用 `multipart/form-data`。
- **响应体格式**: 
    - 所有API响应（包括成功和错误响应）统一使用 `application/json` 格式，并使用UTF-8编码。
    - **成功响应结构**: 
      ```json
      {
        "code": 0, // 业务成功代码，固定为0
        "message": "Success", // 描述信息
        "data": { ... } // 实际的响应数据，可以是对象或数组
      }
      ```
      对于列表数据，`data` 字段应包含 `items` (实际数据列表) 和 `pagination` (分页信息) 对象：
      ```json
      {
        "code": 0,
        "message": "Success",
        "data": {
          "items": [
            { "id": 1, "name": "Item 1" },
            { "id": 2, "name": "Item 2" }
          ],
          "pagination": {
            "totalItems": 100,
            "totalPages": 10,
            "currentPage": 1,
            "perPage": 10
          }
        }
      }
      ```
    - **错误响应结构**: 
      ```json
      {
        "code": 40401, // 业务相关错误码，非0即可
        "message": "资源未找到", // 错误摘要信息
        "data": null, // 错误响应时，data字段为null
        "errors": { // 可选，详细的错误信息，特别是用于表单验证错误
          "field1": ["Error message for field1."],
          "field2": ["Error message 1 for field2.", "Error message 2 for field2."]
        }
      }
      ```

### 3.3 错误处理

- **HTTP状态码**: 严格遵循HTTP状态码的语义。
    - `2xx` (成功): 
        - `200 OK`: 请求成功，通常用于GET和PUT/PATCH的成功响应。
        - `201 Created`: 资源创建成功，通常用于POST的成功响应。响应体中应包含新创建的资源。
        - `202 Accepted`: 请求已被接受处理，但处理尚未完成（例如异步任务）。
        - `204 No Content`: 请求成功，但响应体中无内容，通常用于DELETE的成功响应或某些PUT操作。
    - `4xx` (客户端错误):
        - `400 Bad Request`: 请求无效，例如参数错误、格式错误。
        - `401 Unauthorized`: 未认证或认证失败。客户端应提示用户登录或检查凭证。
        - `403 Forbidden`: 已认证，但用户无权访问该资源或执行该操作。
        - `404 Not Found`: 请求的资源不存在。
        - `405 Method Not Allowed`: 请求的HTTP方法不被允许用于该资源。
        - `409 Conflict`: 请求冲突，例如尝试创建已存在的唯一资源。
        - `415 Unsupported Media Type`: 请求的媒体类型不被服务器支持。
        - `422 Unprocessable Entity`: 请求格式正确，但由于语义错误而无法处理（例如表单验证失败）。
        - `429 Too Many Requests`: 请求过于频繁，触发了速率限制。
    - `5xx` (服务器错误):
        - `500 Internal Server Error`: 服务器内部发生未知错误。
        - `502 Bad Gateway`: 作为网关或代理的服务器从上游服务器收到了无效的响应。
        - `503 Service Unavailable`: 服务器暂时无法处理请求（例如过载或维护）。
        - `504 Gateway Timeout`: 作为网关或代理的服务器未能及时从上游服务器获得响应。
- **错误响应体**: 如3.2节所述，提供结构化的错误信息，便于客户端解析和展示。

### 3.4 API文档规范

- **文档工具**: 推荐使用OpenAPI Specification (OAS, 原Swagger) 描述API，并使用工具（如Swagger UI, ReDoc）生成可交互的API文档。
- **文档内容**: 每个API端点应包含以下信息：
    - **路径 (Path)**: 例如 `/api/user/users/{userId}`。
    - **HTTP方法 (Method)**: 例如 `GET`。
    - **描述 (Description)**: 清晰说明接口的功能和用途。
    - **认证要求 (Authentication)**: 是否需要认证，以及认证方式。
    - **权限要求 (Permissions)**: 执行此接口所需的特定权限（如果适用）。
    - **路径参数 (Path Parameters)**: 参数名、类型、是否必需、描述。
    - **查询参数 (Query Parameters)**: 参数名、类型、是否必需、默认值、描述。
    - **请求头 (Request Headers)**: 特殊的请求头及其描述。
    
## 4. 模块API接口 (Module API Interfaces)

本章节根据模块划分，列举核心模块提供的API接口。

### 4.1 核心平台模块 (Core Module)
- **API前缀**: `/api/core`
- **说明**: 提供平台级的状态查询和管理功能。

| 功能描述 | HTTP方法 | URL路径 | 认证要求 | 主要参数 | 成功响应 (data) |
|---|---|---|---|---|---|
| 获取系统状态 | GET | `/status` | 无 | - | `{"version": "1.0.0", "status": "running"}` |
| 获取已加载模块 | GET | `/modules` | 管理员 | - | `[{"name": "user", "version": "1.0.0"}, ...]` |

### 4.2 用户模块 (User Module)
- **API前缀**: `/api/user`
- **说明**: 负责管理所有与用户、角色、权限相关的操作。此模块同时管理后台管理员(`admins`)和前台会员(`members`)，通过路径进行区分。

#### 4.2.1 管理员 (Admins)
| 功能描述 | HTTP方法 | URL路径 | 认证要求 | 主要参数 | 成功响应 (data) |
|---|---|---|---|---|---|
| 创建管理员 | POST | `/admins` | 超级管理员 | `username`, `email`, `password`, `roles` | `Admin`对象 |
| 获取管理员列表 | GET | `/admins` | 管理员 | `page`, `perPage`, `sortBy` | `items: [Admin], pagination: {...}` |
| 获取单个管理员 | GET | `/admins/{adminId}` | 管理员 | - | `Admin`对象 |
| 更新管理员 | PUT/PATCH | `/admins/{adminId}` | 管理员 | `email`, `roles`, etc. | `Admin`对象 |
| 删除管理员 | DELETE | `/admins/{adminId}` | 超级管理员 | - | `null` |

#### 4.2.2 会员 (Members)
| 功能描述 | HTTP方法 | URL路径 | 认证要求 | 主要参数 | 成功响应 (data) |
|---|---|---|---|---|---|
| 会员注册 | POST | `/members/register` | 无 | `email`, `password` | `Member`对象 |
| 会员登录 | POST | `/members/login` | 无 | `email`, `password` | `{"token": "...", "member": {...}}` |
| 获取当前会员信息 | GET | `/members/me` | 会员 | - | `Member`对象 |
| 更新当前会员信息 | PUT/PATCH | `/members/me` | 会员 | `nickname`, `avatarUrl` | `Member`对象 |

#### 4.2.3 角色与权限 (Roles & Permissions)
| 功能描述 | HTTP方法 | URL路径 | 认证要求 | 主要参数 | 成功响应 (data) |
|---|---|---|---|---|---|
| 获取角色列表 | GET | `/roles` | 管理员 | `type=admin` 或 `type=member` | `items: [Role], pagination: {...}` |
| 创建角色 | POST | `/roles` | 超级管理员 | `name`, `slug`, `type` | `Role`对象 |
| ... | ... | ... | ... | ... | ... |

### 4.3 内容模块 (Post Module)
- **API前缀**: `/api/post`
- **说明**: 负责管理所有内容，包括文章、分类、标签等。

| 功能描述 | HTTP方法 | URL路径 | 认证要求 | 主要参数 | 成功响应 (data) |
|---|---|---|---|---|---|
| 创建文章 | POST | `/posts` | 编辑及以上 | `title`, `content`, `categoryId` | `Post`对象 |
| 获取文章列表 | GET | `/posts` | 无 | `page`, `perPage`, `categoryId` | `items: [Post], pagination: {...}` |
| 获取单篇文章 | GET | `/posts/{postId}` | 无 | - | `Post`对象 |
| ... | ... | ... | ... | ... | ... |

### 4.4 系统模块 (System Module)
- **API前缀**: `/api/system`
- **说明**: 负责管理全局性的系统配置和设置。

| 功能描述 | HTTP方法 | URL路径 | 认证要求 | 主要参数 | 成功响应 (data) |
|---|---|---|---|---|---|
| 获取所有设置组 | GET | `/settings/groups` | 管理员 | - | `["site", "email", "storage"]` |
| 按组获取设置 | GET | `/settings/{group}` | 管理员 | - | `[{"key": "site_name", "value": "My CMS"}, ...]` |
| 批量更新设置 | PUT | `/settings/{group}` | 超级管理员 | 请求体: `[{"key": "site_name", "value": "New Name"}]` | `{"success": true}` |

*(此处省略其他模块API的详细列表，格式同上)*

## 5. 内部服务接口

### 5.1 缓存服务接口

缓存服务接口提供统一的缓存操作，供系统内部各模块调用，以提高性能和响应速度。这些接口通常是内部使用的，不直接暴露给前端或第三方应用。

#### 5.1.1 获取缓存 (Get Cache)

- **描述**: 根据缓存键获取缓存数据。
- **内部调用**: `CacheService.get(key)`
- **参数**:
  - `key` (string, 必需): 缓存的唯一键。
- **返回值**:
  - `Promise<any | null>`: 返回缓存的数据，如果缓存不存在或已过期，则返回 `null`。
- **示例**:
  ```typescript
  // 假设 CacheService 是一个注入的服务
  async function getUserProfile(userId: string) {
    const cacheKey = `user_profile:${userId}`;
    let userProfile = await CacheService.get(cacheKey);
    if (!userProfile) {
      // 从数据库或其他来源获取数据
      userProfile = await fetchUserProfileFromDB(userId);
      if (userProfile) {
        // 设置缓存，例如有效期1小时
        await CacheService.set(cacheKey, userProfile, 3600);
      }
    }
    return userProfile;
  }
  ```

#### 5.1.2 设置缓存 (Set Cache)

- **描述**: 设置缓存数据，并可以指定过期时间。
- **内部调用**: `CacheService.set(key, value, ttl?)`
- **参数**:
  - `key` (string, 必需): 缓存的唯一键。
  - `value` (any, 必需): 需要缓存的数据。
  - `ttl` (number, 可选): 缓存的生存时间（秒）。如果未提供，则可能使用默认的TTL或永不过期（取决于具体实现）。
- **返回值**:
  - `Promise<void>`: 表示操作完成。

#### 5.1.3 删除缓存 (Delete Cache)

- **描述**: 根据缓存键删除指定的缓存数据。
- **内部调用**: `CacheService.delete(key)`
- **参数**:
  - `key` (string, 必需): 缓存的唯一键。
- **返回值**:
  - `Promise<boolean>`: 如果成功删除则返回 `true`，否则返回 `false`。

#### 6.1.4 清除所有缓存 (Clear All Cache)

- **描述**: 清除所有缓存数据。这是一个高风险操作，应谨慎使用。
- **内部调用**: `CacheService.clear()`
- **参数**: 无
- **返回值**:
  - `Promise<void>`: 表示操作完成。

#### 6.1.5 判断缓存是否存在 (Has Cache)

- **描述**: 判断指定的缓存键是否存在且未过期。
- **内部调用**: `CacheService.has(key)`
- **参数**:
  - `key` (string, 必需): 缓存的唯一键。
- **返回值**:
  - `Promise<boolean>`: 如果缓存存在且有效则返回 `true`，否则返回 `false`。

#### 6.1.6 获取并删除缓存 (Get and Delete Cache - Atomic Pop)

- **描述**: 原子操作，获取缓存数据然后立即删除它。常用于任务队列等场景。
- **内部调用**: `CacheService.getAndDelete(key)`
- **参数**:
  - `key` (string, 必需): 缓存的唯一键。
- **返回值**:
  - `Promise<any | null>`: 返回缓存的数据，如果缓存不存在，则返回 `null`。

**注意**: 上述接口定义是概念性的，具体的实现会依赖于项目选择的缓存技术（如 Redis, Memcached, 或内存缓存）。这些接口旨在提供一个统一的抽象层。

### 6.2 搜索服务接口 (Search Service)

搜索服务接口提供统一的搜索功能，供系统内部各模块调用，以实现对内容、用户等数据的快速检索。这些接口通常是内部使用的。

#### 6.2.1 索引文档 (Index Document)

- **描述**: 将一个文档（如文章、产品、用户信息等）添加到搜索引擎的索引中，使其可以被搜索到。
- **内部调用**: `SearchService.index(document)`
- **参数**:
  - `document` (object, 必需): 需要被索引的文档对象。该对象通常包含一个唯一ID和需要被搜索的字段。
    - `id` (string | number, 必需): 文档的唯一标识符。
    - `type` (string, 必需): 文档类型（例如：`article`, `user`, `product`），用于区分不同类型的索引数据。
    - `data` (object, 必需): 包含文档具体内容的对象，其字段将被索引。
- **返回值**:
  - `Promise<void>`: 表示操作完成。
- **示例**:
  ```typescript
  // 假设 SearchService 是一个注入的服务
  async function publishArticle(articleData: any) {
    // ...保存文章到数据库...
    await SearchService.index({
      id: articleData.id,
      type: 'article',
      data: {
        title: articleData.title,
        content: articleData.content,
        tags: articleData.tags,
        authorId: articleData.authorId
      }
    });
  }
  ```

#### 6.2.2 更新索引文档 (Update Indexed Document)

- **描述**: 更新搜索引擎中已存在的索引文档。
- **内部调用**: `SearchService.update(documentId, partialDocument)`
- **参数**:
  - `documentId` (string | number, 必需): 需要更新的文档的唯一ID。
  - `type` (string, 必需): 文档类型。
  - `partialDocumentData` (object, 必需): 包含需要更新的字段和新值的对象。
- **返回值**:
  - `Promise<void>`: 表示操作完成。

#### 6.2.3 从索引中删除文档 (Delete Document from Index)

- **描述**: 从搜索引擎的索引中删除指定的文档。
- **内部调用**: `SearchService.delete(documentId, type)`
- **参数**:
  - `documentId` (string | number, 必需): 需要删除的文档的唯一ID。
  - `type` (string, 必需): 文档类型。
- **返回值**:
  - `Promise<void>`: 表示操作完成。

#### 6.2.4 执行搜索 (Perform Search)

- **描述**: 根据查询条件执行搜索，并返回匹配的结果。
- **内部调用**: `SearchService.search(queryOptions)`
- **参数**:
  - `queryOptions` (object, 必需): 搜索查询的配置对象。
    - `query` (string, 必需): 搜索关键词或查询语句。
    - `type` (string | string[], 可选): 限定搜索的文档类型。可以是单个类型或类型数组。
    - `filters` (object, 可选): 额外的过滤条件 (例如: `{ status: 'published', categoryId: 123 }`)。
    - `sortBy` (string, 可选): 排序字段 (例如: `createdAt:desc`)。
    - `page` (number, 可选, 默认 1): 分页的页码。
    - `pageSize` (number, 可选, 默认 10): 每页结果数量。
    - `highlightFields` (string[], 可选): 需要高亮显示的字段。
- **返回值**:
  - `Promise<SearchResult>`: 返回搜索结果对象，通常包含：
    - `hits` (object[]): 匹配的文档列表。
    - `total` (number): 匹配的总数。
    - `page` (number): 当前页码。
    - `pageSize` (number): 每页数量。
    - `aggregations` (object, 可选): 聚合结果 (例如用于分面搜索)。
- **示例**:
  ```typescript
  async function searchArticles(keyword: string, page: number = 1) {
    const results = await SearchService.search({
      query: keyword,
      type: 'article',
      filters: { status: 'published' },
      sortBy: 'publishDate:desc',
      page: page,
      pageSize: 10,
      highlightFields: ['title', 'content']
    });
    return results;
  }
  ```

#### 6.2.5 重建索引 (Rebuild Index)

- **描述**: 清除指定类型或所有类型的现有索引，并从数据源重新构建索引。这是一个耗时且高风险的操作。
- **内部调用**: `SearchService.rebuildIndex(type?)`
- **参数**:
  - `type` (string, 可选): 如果提供，则只重建该类型的索引。否则，重建所有类型的索引。
- **返回值**:
  - `Promise<void>`: 表示操作完成。

**注意**: 上述接口定义是概念性的，具体的实现会依赖于项目选择的搜索引擎技术（如 Elasticsearch, OpenSearch, MeiliSearch, Algolia 等）。这些接口旨在提供一个统一的抽象层。

### 6.3 消息队列接口 (Message Queue Service)

消息队列接口用于系统内部模块间的异步通信，解耦服务，提高系统的可伸缩性和可靠性。这些接口通常是内部使用的。

#### 6.3.1 发送消息 (Publish Message)

- **描述**: 向指定的主题（Topic）或队列（Queue）发送一条消息。
- **内部调用**: `MessageQueueService.publish(topicOrQueueName, message, options?)`
- **参数**:
  - `topicOrQueueName` (string, 必需): 消息将要发送到的主题或队列的名称。
  - `message` (object | string, 必需): 要发送的消息内容。通常是一个包含任务详情的JSON对象。
  - `options` (object, 可选): 发送消息时的额外选项，例如：
    - `delaySeconds` (number, 可选): 延迟发送的时间（秒）。
    - `priority` (number, 可选): 消息优先级（如果消息队列支持）。
    - `messageGroupId` (string, 可选): 消息组ID，用于FIFO队列保证顺序。
    - `deduplicationId` (string, 可选): 消息去重ID。
- **返回值**:
  - `Promise<MessageSendResult>`: 返回发送结果，通常包含消息ID等信息。
    - `messageId` (string): 成功发送的消息的唯一标识符。
- **示例**:
  ```typescript
  // 假设 MessageQueueService 是一个注入的服务
  async function processUserRegistration(userData: any) {
    // ...其他同步处理...
    await MessageQueueService.publish('user-registration-events', {
      userId: userData.id,
      email: userData.email,
      timestamp: new Date().toISOString()
    }, { delaySeconds: 5 }); // 延迟5秒发送，给其他服务一点时间
    console.log('User registration event published.');
  }
  ```

#### 6.3.2 订阅消息/消费消息 (Subscribe/Consume Message)

- **描述**: 订阅指定的主题或从队列中消费消息，并提供一个回调函数来处理接收到的消息。
- **内部调用**: `MessageQueueService.subscribe(topicOrQueueName, handler, options?)`
- **参数**:
  - `topicOrQueueName` (string, 必需): 要订阅的主题或消费的队列名称。
  - `handler` (function, 必需): 处理接收到消息的回调函数。该函数通常接收消息内容作为参数，并应返回一个Promise来指示处理是否成功。
    - `async handler(message: any): Promise<void>`
  - `options` (object, 可选): 消费消息时的额外选项，例如：
    - `batchSize` (number, 可选): 一次拉取的最大消息数量。
    - `visibilityTimeout` (number, 可选): 消息可见性超时时间（秒）。
    - `concurrency` (number, 可选): 并发处理消息的数量。
- **返回值**:
  - `Promise<Subscription>`: 返回一个订阅对象，该对象可以用来取消订阅。
    - `unsubscribe(): Promise<void>`
- **示例**:
  ```typescript
  // 邮件服务订阅用户注册事件
  async function handleUserRegistrationEvent(message: any) {
    console.log('Received user registration event:', message);
    // 发送欢迎邮件
    await sendWelcomeEmail(message.email);
    // 可以在这里确认消息已被处理，具体取决于MQ的实现
  }

  async function startEmailServiceListeners() {
    const subscription = await MessageQueueService.subscribe(
      'user-registration-events',
      handleUserRegistrationEvent,
      { batchSize: 10, concurrency: 5 }
    );
    console.log('Email service listening for user registration events.');
    // 若要停止监听: await subscription.unsubscribe();
  }
  ```

#### 6.3.3 确认消息处理 (Acknowledge Message -  如果需要手动ACK)

- **描述**: 在某些消息队列实现中（如RabbitMQ），消费者处理完消息后需要显式地确认消息，告知队列该消息已被成功处理，可以从队列中移除。
- **内部调用**: `MessageQueueService.ack(messageReceipt)` (具体方法名和参数取决于MQ库)
- **参数**:
  - `messageReceipt` (any, 必需): 确认消息所需的信息，通常从接收到的消息对象中获取（例如，RabbitMQ中的`channel.ack(msg)`）。
- **返回值**:
  - `Promise<void>`: 表示操作完成。

#### 6.3.4 拒绝/重试消息 (Nack/Retry Message - 如果需要手动NACK)

- **描述**: 如果消息处理失败，消费者可以通知队列拒绝该消息，并选择是否将其重新放回队列等待后续处理。
- **内部调用**: `MessageQueueService.nack(messageReceipt, requeue?)` (具体方法名和参数取决于MQ库)
- **参数**:
  - `messageReceipt` (any, 必需): 拒绝消息所需的信息。
  - `requeue` (boolean, 可选, 默认false): 是否将消息重新放回队列。如果为 `false`，消息可能会被发送到死信队列（DLQ）。
- **返回值**:
  - `Promise<void>`: 表示操作完成。

**注意**: 上述接口定义是概念性的，具体的实现会依赖于项目选择的消息队列技术（如 RabbitMQ, Kafka, AWS SQS, Google Cloud Pub/Sub, Redis Streams 等）。这些接口旨在提供一个统一的抽象层。

## 7. 外部集成接口

*(此部分待补充详细设计)*

### 7.1 第三方认证接口 (OAuth 2.0 / OpenID Connect)

这些接口用于与外部身份提供商（IdP）集成，允许用户使用其现有的第三方账户（如Google, GitHub, 微信等）登录到本系统。

#### 7.1.1 发起第三方登录请求 (Initiate Third-Party Login)

- **描述**: 用户点击"使用XX登录"按钮后，系统将用户重定向到第三方身份提供商的授权页面。
- **URL**: `/api/auth/oauth/{provider}/redirect` (例如: `/api/auth/oauth/google/redirect`)
- **方法**: `GET`
- **认证**: 无 (用户尚未登录)
- **路径参数**:
  - `provider` (string, 必需): 第三方提供商的标识符 (例如: `google`, `github`, `wechat`).
- **查询参数 (可选, 用于传递状态或重定向信息)**:
  - `redirect_uri` (string, 可选): 认证成功后，如果需要重定向到不同于默认配置的特定前端页面。
  - `state` (string, 可选): 用于防止CSRF攻击，并可在回调时传递状态信息。
- **行为**: 此接口会构建第三方IdP的授权URL（包含 `client_id`, `redirect_uri`, `scope`, `response_type`, `state` 等参数），然后将用户重定向到该URL。
- **成功响应**: HTTP `302 Found` 重定向到第三方IdP的授权页面。
- **错误响应**:
  - `400 Bad Request`: 无效的 `provider`。
  - `500 Internal Server Error`: 服务器内部错误（例如，无法生成重定向URL）。

#### 7.1.2 第三方登录回调处理 (Handle Third-Party Login Callback)

- **描述**: 用户在第三方IdP完成授权后，IdP会将用户重定向回此回调URL，并附带授权码 (`code`) 或错误信息。
- **URL**: `/api/auth/oauth/{provider}/callback` (例如: `/api/auth/oauth/google/callback`)
- **方法**: `GET` (通常，也可能是 `POST`，取决于IdP)
- **认证**: 无
- **路径参数**:
  - `provider` (string, 必需): 第三方提供商的标识符。
- **查询参数 (由IdP提供)**:
  - `code` (string, 必需，如果授权成功): IdP返回的授权码。
  - `state` (string, 可选): IdP返回的 `state` 参数，用于校验。
  - `error` (string, 可选，如果授权失败): 错误代码 (例如: `access_denied`)。
  - `error_description` (string, 可选): 错误的描述。
- **行为**:
  1. 校验 `state` 参数（如果提供）。
  2. 如果收到 `error`，则处理错误（例如，重定向到前端的错误页面）。
  3. 使用收到的 `code` 向IdP的令牌端点请求访问令牌 (Access Token) 和可能的ID令牌 (ID Token)。
  4. 使用访问令牌从IdP的用户信息端点获取用户信息（如邮箱、昵称、头像等）。
  5. 根据获取到的用户信息：
     - 如果用户已存在（例如，通过邮箱匹配），则登录该用户。
     - 如果用户不存在，则根据配置选择创建新用户或提示用户关联现有账户。
  6. 生成系统内部的会话凭证（如JWT）。
  7. 将用户重定向到前端的指定页面（例如，用户仪表盘），并在URL参数或Cookie中附带会话凭证。
- **成功响应**: HTTP `302 Found` 重定向到前端应用页面，通常会携带认证令牌。
- **错误响应**:
  - `400 Bad Request`: 无效的 `code` 或 `state`，或IdP返回错误。
  - `401 Unauthorized`: 无法从IdP获取令牌或用户信息，或用户信息验证失败。
  - `500 Internal Server Error`: 服务器内部错误。

#### 7.1.3 获取已连接的第三方账户 (List Connected Accounts - 用户)

- **描述**: 获取当前登录用户已绑定的所有第三方账户列表。
- **URL**: `/api/users/me/connections`
- **方法**: `GET`
- **认证**: 必需 (用户令牌)
- **成功响应 (200 OK)**:
  ```json
  {
    "code": 0,
    "message": "成功",
    "data": [
      {
        "provider": "google",
        "provider_user_id": "109876543210987654321",
        "display_name": "<EMAIL>",
        "connected_at": "2023-10-26T10:00:00Z"
      },
      {
        "provider": "github",
        "provider_user_id": "1234567",
        "display_name": "github_username",
        "connected_at": "2023-11-15T14:30:00Z"
      }
      // ...更多已连接账户
    ]
  }
  ```
- **错误响应**:
  - `401 Unauthorized`: 未授权访问。
  - `500 Internal Server Error`: 服务器内部错误。

#### 7.1.4 解绑第三方账户 (Disconnect Third-Party Account - 用户)

- **描述**: 允许当前登录用户解绑一个已连接的第三方账户。
- **URL**: `/api/users/me/connections/{provider}`
- **方法**: `DELETE`
- **认证**: 必需 (用户令牌)
- **路径参数**:
  - `provider` (string, 必需): 要解绑的第三方提供商的标识符。
- **成功响应 (204 No Content)**: 表示解绑成功。
- **错误响应**:
  - `401 Unauthorized`: 未授权访问。
  - `404 Not Found`: 未找到指定的已连接账户。
  - `400 Bad Request`: 例如，试图解绑唯一的登录方式（如果系统策略不允许）。
  - `500 Internal Server Error`: 服务器内部错误。

**注意**: 实现第三方登录需要仔细处理安全问题，如CSRF保护（使用`state`参数）、安全存储客户端密钥、以及正确验证从IdP接收到的数据。还需要为每个支持的提供商进行单独的配置。

### 7.2 微信生态接口 (WeChat Ecosystem)

这些接口用于与微信生态系统集成，例如微信登录、微信支付、公众号/小程序消息交互等。

#### 7.2.1 微信登录 (WeChat Login)

与7.1中的第三方登录类似，但针对微信平台有其特定的流程和参数。

- **发起微信登录 (PC端扫码或公众号/小程序内授权)**
  - **PC端扫码登录重定向URL**: `/api/auth/oauth/wechat_open/redirect` (使用微信开放平台)
  - **公众号授权重定向URL**: `/api/auth/oauth/wechat_mp/redirect` (使用微信公众平台)
  - **小程序登录**: 通常在小程序端调用 `wx.login()` 获取 `code`，然后将 `code` 发送到后端接口。
    - **小程序code换取session接口**: `/api/auth/oauth/wechat_miniapp/login`
      - **方法**: `POST`
      - **请求体**: `{ "code": "JSCODE_FROM_WX_LOGIN" }`
      - **成功响应**: 返回系统内部的会话凭证 (JWT) 及可能的 `openid` 和 `session_key` (安全存储，不直接返给前端)。

- **微信登录回调处理**: `/api/auth/oauth/wechat_open/callback` 或 `/api/auth/oauth/wechat_mp/callback`
  - 行为与通用OAuth回调类似，但会使用微信特定的API获取 `access_token`, `openid`, 和用户信息。

#### 7.2.2 微信支付通知回调 (WeChat Pay Notification)

- **描述**: 接收微信支付成功或退款等状态的异步通知。
- **URL**: `/api/payments/wechat/notify` (此URL需要在微信商户平台配置)
- **方法**: `POST`
- **认证**: 微信支付签名验证。
- **请求体 (XML格式)**: 微信支付服务器发送的包含支付结果的XML数据。
- **行为**:
  1. 验证请求的签名是否来自微信支付。
  2. 解析XML数据，获取订单号、支付状态、金额等信息。
  3. 更新系统内部的订单状态。
  4. 根据微信支付的要求，返回特定格式的XML响应给微信服务器，告知已成功接收通知。
- **成功响应 (给微信服务器)**:
  ```xml
  <xml>
    <return_code><![CDATA[SUCCESS]]></return_code>
    <return_msg><![CDATA[OK]]></return_msg>
  </xml>
  ```
- **错误处理**: 如果验签失败或处理内部逻辑出错，应记录日志，但仍需尽量按微信要求响应，避免微信持续重试通知。

#### 7.2.3 公众号/小程序消息与事件接收 (WeChat Message & Event Handler)

- **描述**: 接收来自微信公众号或小程序用户的消息（文本、图片、语音等）以及各种事件（关注、取消关注、菜单点击等）。
- **URL**: `/api/wechat/mp/events` 或 `/api/wechat/miniapp/events` (此URL需要在微信公众平台或小程序后台配置)
- **方法**: `POST` (通常，微信验证服务器URL时会使用GET请求)
- **认证**: 微信消息签名验证。
- **请求体 (XML格式)**: 微信服务器发送的包含消息或事件内容的XML数据。
- **行为**:
  1. 验证请求的签名是否来自微信服务器。
  2. 解析XML数据，识别消息类型或事件类型。
  3. 根据不同的消息/事件类型，执行相应的业务逻辑（例如，自动回复、记录用户行为、调用客服接口等）。
  4. 如果需要回复用户消息，则构造符合微信规范的XML格式的响应消息。
- **成功响应 (给微信服务器, 如果需要回复)**:
  ```xml
  <xml>
    <ToUserName><![CDATA[toUser]]></ToUserName>
    <FromUserName><![CDATA[fromUser]]></FromUserName>
    <CreateTime>12345678</CreateTime>
    <MsgType><![CDATA[text]]></MsgType>
    <Content><![CDATA[你好]]></Content>
  </xml>
  ```
  如果不需要立即回复，可以返回空字符串或 `success` 字符串（具体看微信文档要求）。

#### 7.2.4 调用微信JSSDK的配置接口 (Get JSSDK Config)

- **描述**: 前端页面在使用微信JSSDK（例如分享、扫一扫、选择图片等功能）前，需要向后端请求签名配置。
- **URL**: `/api/wechat/jssdk/config`
- **方法**: `POST`
- **认证**: 必需 (用户令牌，或根据场景判断是否需要)
- **请求体 (application/json)**:
  ```json
  {
    "url": "CURRENT_PAGE_URL_FOR_SIGNATURE"
  }
  ```
  - `url` (string, 必需): 当前页面的URL（不包含#及其后面部分），用于生成JSSDK签名。
- **成功响应 (200 OK)**:
  ```json
  {
    "code": 0,
    "message": "成功",
    "data": {
      "appId": "WECHAT_APPID",
      "timestamp": 1678886400,
      "nonceStr": "NONCE_STRING",
      "signature": "GENERATED_SIGNATURE",
      "jsApiList": ["onMenuShareTimeline", "onMenuShareAppMessage"]
    }
  }
  ```
- **错误响应**:
  - `400 Bad Request`: 请求参数错误。
  - `401 Unauthorized`: 未授权访问。
  - `500 Internal Server Error`: 获取access_token或jsapi_ticket失败，或签名生成错误。

**注意**: 与微信生态集成涉及较多细节和安全考虑，如 `access_token` 的管理与刷新、`session_key` 的安全存储、消息的加解密（如果启用）、以及严格遵守微信平台的开发规范和最佳实践。

### 7.3 支付接口 (Payment Gateway Integration - General)

这些接口用于与各种支付网关（如支付宝、PayPal、Stripe等，微信支付已在7.2中单独列出）集成，处理在线支付流程。

#### 7.3.1 创建支付订单 (Create Payment Order)

- **描述**: 用户选择商品或服务并准备支付时，系统后端创建支付订单，并从支付网关获取支付凭证或重定向URL。
- **URL**: `/api/payments/{gateway}/orders` (例如: `/api/payments/alipay/orders`, `/api/payments/stripe/orders`)
- **方法**: `POST`
- **认证**: 必需 (用户令牌)
- **路径参数**:
  - `gateway` (string, 必需): 支付网关的标识符 (例如: `alipay`, `paypal`, `stripe`).
- **请求体 (application/json)**:
  ```json
  {
    "order_id": "YOUR_SYSTEM_ORDER_ID_123", // 系统内部订单号
    "amount": 100.50, // 支付金额
    "currency": "CNY", // 货币代码 (ISO 4217)
    "description": "商品描述或订单摘要",
    "return_url": "YOUR_FRONTEND_RETURN_URL_AFTER_PAYMENT", // 支付成功后前端跳转URL
    "notify_url": "YOUR_BACKEND_NOTIFICATION_URL", // 支付网关异步通知URL (通常在网关配置)
    "payment_method_specific_params": { // 特定支付方式的参数
      // 例如，Stripe的payment_method_types: ['card']
      // 例如，支付宝的product_code: 'FAST_INSTANT_TRADE_PAY'
    }
  }
  ```
- **成功响应 (200 OK)**:
  - **对于需要重定向的网关 (如支付宝PC)**:
    ```json
    {
      "code": 0,
      "message": "成功",
      "data": {
        "type": "redirect",
        "redirect_url": "PAYMENT_GATEWAY_REDIRECT_URL_WITH_PARAMS"
      }
    }
    ```
  - **对于返回支付凭证的网关 (如Stripe Elements, App支付)**:
    ```json
    {
      "code": 0,
      "message": "成功",
      "data": {
        "type": "credentials",
        "credentials": { /* 例如Stripe的client_secret, 或App支付所需的参数 */ }
      }
    }
    ```
- **错误响应**:
  - `400 Bad Request`: 请求参数错误，或订单信息校验失败。
  - `401 Unauthorized`: 未授权访问。
  - `402 Payment Required`: 支付相关错误，例如支付网关返回的错误。
  - `500 Internal Server Error`: 服务器内部错误。

#### 7.3.2 处理支付网关异步通知 (Handle Payment Notification)

- **描述**: 接收来自支付网关的异步通知，确认支付状态（成功、失败、退款等）。
- **URL**: `/api/payments/{gateway}/notify` (此URL通常在创建支付订单时提供给支付网关，或在支付网关后台配置)
- **方法**: `POST` (通常，也可能是 `GET`，取决于网关)
- **认证**: 支付网关签名验证。
- **路径参数**:
  - `gateway` (string, 必需): 支付网关的标识符。
- **请求体**: 通常是 `application/x-www-form-urlencoded` 或 `application/json` 或 `XML`，具体格式取决于支付网关。
- **行为**:
  1. 验证请求的签名或来源是否为真实的支付网关。
  2. 解析请求体，获取订单号、支付状态、金额、交易号等信息。
  3. 更新系统内部的订单状态。
  4. 根据支付网关的要求，返回特定格式的响应（例如，纯文本 `success` 或 `OK`，或特定XML/JSON结构），告知已成功接收通知。
- **成功响应 (给支付网关)**: 取决于支付网关的要求。
- **错误处理**: 记录日志，并按网关要求响应，避免持续重试。

#### 7.3.3 查询支付订单状态 (Query Payment Order Status)

- **描述**: 主动向支付网关查询特定支付订单的当前状态。用于同步状态或处理异步通知可能丢失的情况。
- **URL**: `/api/payments/{gateway}/orders/{order_id_or_transaction_id}`
- **方法**: `GET`
- **认证**: 必需 (通常是后端服务间的认证，例如API Key/Secret)
- **路径参数**:
  - `gateway` (string, 必需): 支付网关的标识符。
  - `order_id_or_transaction_id` (string, 必需): 系统内部订单号或支付网关的交易号。
- **成功响应 (200 OK)**:
  ```json
  {
    "code": 0,
    "message": "成功",
    "data": {
      "order_id": "YOUR_SYSTEM_ORDER_ID_123",
      "transaction_id": "GATEWAY_TRANSACTION_ID_ABC",
      "status": "PAID" // 例如: PAID, PENDING, FAILED, REFUNDED
      // ...更多支付网关返回的详细信息
    }
  }
  ```
- **错误响应**:
  - `401 Unauthorized`: 认证失败。
  - `404 Not Found`: 订单未找到。
  - `500 Internal Server Error`: 服务器内部错误或支付网关通信错误。

#### 7.3.4 创建退款 (Create Refund)

- **描述**: 对已成功支付的订单发起退款请求。
- **URL**: `/api/payments/{gateway}/refunds`
- **方法**: `POST`
- **认证**: 必需 (通常是管理员权限，并可能需要额外的操作员认证)
- **路径参数**:
  - `gateway` (string, 必需): 支付网关的标识符。
- **请求体 (application/json)**:
  ```json
  {
    "original_transaction_id": "GATEWAY_TRANSACTION_ID_TO_REFUND", // 原支付的网关交易号
    "refund_amount": 50.00, // 退款金额 (可以是部分退款)
    "currency": "CNY",
    "reason": "用户申请退款 - 商品损坏",
    "refund_id": "YOUR_SYSTEM_REFUND_ID_789" // 系统内部退款单号 (可选，用于幂等性)
  }
  ```
- **成功响应 (200 OK 或 202 Accepted)**:
  ```json
  {
    "code": 0,
    "message": "退款请求已提交", // 或 "退款成功"
    "data": {
      "refund_id": "YOUR_SYSTEM_REFUND_ID_789",
      "gateway_refund_id": "GATEWAY_REFUND_ID_XYZ",
      "status": "PROCESSING" // 例如: PROCESSING, SUCCEEDED, FAILED
    }
  }
  ```
- **错误响应**:
  - `400 Bad Request`: 请求参数错误，或退款条件不满足。
  - `401 Unauthorized`: 未授权操作。
  - `402 Payment Required`: 支付相关错误，例如原订单状态不支持退款。
  - `500 Internal Server Error`: 服务器内部错误。

**注意**: 不同支付网关的API细节差异很大。上述接口是高度抽象和通用的。实际实现时需要为每个支付网关编写特定的适配器代码，并仔细阅读各网关的官方文档。安全性（如API密钥管理、防欺诈）和幂等性处理至关重要。

### 7.4 短信服务接口 (SMS Service Integration)

这些接口用于与第三方短信服务提供商（如阿里云、腾讯云、Twilio等）集成，发送短信验证码、通知等。

#### 7.4.1 发送短信验证码 (Send SMS Verification Code)

- **描述**: 向指定手机号发送短信验证码，通常用于用户注册、登录、密码重置等场景。
- **URL**: `/api/sms/verification-codes`
- **方法**: `POST`
- **认证**: 可选 (某些场景下可能需要用户已登录，或通过应用级认证保护接口不被滥用)
- **请求体 (application/json)**:
  ```json
  {
    "phone_number": "+8613800138000", // 国际格式手机号
    "template_id": "SMS_TEMPLATE_VERIFICATION_CODE", // 短信服务商提供的模板ID
    "template_params": { // 模板参数，例如验证码本身
      "code": "123456" // 验证码由后端生成并缓存，然后作为参数发送
    },
    "usage_scenario": "user_registration" // 可选，用于风控和日志记录 (e.g., user_registration, password_reset)
  }
  ```
- **成功响应 (200 OK)**:
  ```json
  {
    "code": 0,
    "message": "短信验证码已发送",
    "data": {
      "message_id": "SMS_PROVIDER_MESSAGE_ID", // 短信服务商返回的消息ID (可选)
      "expires_in": 300 // 验证码有效期（秒），提示前端 (可选)
    }
  }
  ```
- **错误响应**:
  - `400 Bad Request`: 请求参数错误（如手机号格式不正确、模板ID无效）。
  - `429 Too Many Requests`: 发送频率过高（需要实现限流逻辑）。
  - `500 Internal Server Error`: 服务器内部错误或短信服务商接口调用失败。
  - `503 Service Unavailable`: 短信服务暂时不可用。

#### 7.4.2 发送通知类短信 (Send Notification SMS)

- **描述**: 向指定手机号发送通知类短信，如订单状态更新、重要提醒等。
- **URL**: `/api/sms/notifications`
- **方法**: `POST`
- **认证**: 必需 (通常是内部服务调用，或需要管理员权限)
- **请求体 (application/json)**:
  ```json
  {
    "phone_number": "+8613800138000",
    "template_id": "SMS_TEMPLATE_ORDER_CONFIRMATION",
    "template_params": {
      "order_id": "ORDER12345",
      "product_name": "示例商品"
      // ...其他模板所需参数
    }
  }
  ```
- **成功响应 (200 OK)**:
  ```json
  {
    "code": 0,
    "message": "通知短信已发送",
    "data": {
      "message_id": "SMS_PROVIDER_MESSAGE_ID"
    }
  }
  ```
- **错误响应**:
  - `400 Bad Request`: 请求参数错误。
  - `401 Unauthorized`: 未授权访问。
  - `500 Internal Server Error`: 服务器内部错误或短信服务商接口调用失败。

#### 7.4.3 校验短信验证码 (Verify SMS Verification Code)

- **描述**: 校验用户提交的短信验证码是否正确且在有效期内。
- **URL**: `/api/sms/verification-codes/check`
- **方法**: `POST`
- **认证**: 可选
- **请求体 (application/json)**:
  ```json
  {
    "phone_number": "+8613800138000",
    "code": "123456",
    "usage_scenario": "user_registration" // 应与发送时场景一致
  }
  ```
- **成功响应 (200 OK)**:
  ```json
  {
    "code": 0,
    "message": "验证码校验成功",
    "data": {
      "verified": true
    }
  }
  ```
- **错误响应**:
  - `400 Bad Request`: 请求参数错误，或验证码错误/已过期/不存在。
    ```json
    {
      "code": 40001, // 自定义错误码
      "message": "验证码错误或已失效",
      "data": {
        "verified": false
      }
    }
    ```
  - `500 Internal Server Error`: 服务器内部错误。

**注意**: 
- 短信验证码的生成、存储（例如使用Redis缓存，并设置有效期）、校验逻辑需要在后端实现。
- 必须考虑短信发送的成本和安全性，实施严格的限流策略（如IP、手机号维度）以防止接口被滥用（短信炸弹）。
- 不同短信服务商的API接口和参数会有差异，需要进行适配。
- 遵守当地法律法规关于短信发送的规定。

### 7.5 邮件服务接口 (Email Service Integration)

这些接口用于与第三方邮件服务提供商（如 SendGrid, Mailgun, AWS SES, 或自建SMTP服务）集成，发送各类邮件。

#### 7.5.1 发送邮件 (Send Email)

- **描述**: 发送单封或批量邮件，支持HTML内容、附件等。
- **URL**: `/api/emails/send`
- **方法**: `POST`
- **认证**: 必需 (通常是内部服务调用，或需要特定权限，以防止滥用)
- **请求体 (application/json)**:
  ```json
  {
    "to": [{"email": "<EMAIL>", "name": "Recipient One"}], // 收件人列表
    "cc": [], // 抄送列表 (可选)
    "bcc": [], // 密送列表 (可选)
    "from": {"email": "<EMAIL>", "name": "Your Application Name"}, // 发件人
    "subject": "邮件主题",
    "html_content": "<h1>邮件标题</h1><p>这是邮件的HTML内容。</p>", // HTML邮件内容
    "text_content": "邮件标题\n\n这是邮件的纯文本内容。", // 纯文本邮件内容 (可选, 推荐提供以兼容不支持HTML的客户端)
    "attachments": [ // 附件列表 (可选)
      {
        "filename": "report.pdf",
        "content_base64": "BASE64_ENCODED_CONTENT_OF_THE_FILE",
        "content_type": "application/pdf"
      }
    ],
    "template_id": "EMAIL_TEMPLATE_WELCOME", // 可选，如果使用邮件服务商的模板功能
    "template_data": { // 可选，模板所需的数据
      "username": "JohnDoe",
      "activation_link": "https://yourdomain.com/activate?token=xxxx"
    },
    "tags": ["transactional", "welcome_email"] // 可选，用于分类和追踪
  }
  ```
- **成功响应 (202 Accepted 或 200 OK)**:
  ```json
  {
    "code": 0,
    "message": "邮件已成功加入发送队列", // 或 "邮件已发送"
    "data": {
      "message_id": "EMAIL_PROVIDER_MESSAGE_ID_OR_BATCH_ID" // 邮件服务商返回的消息ID或批次ID (可选)
    }
  }
  ```
- **错误响应**:
  - `400 Bad Request`: 请求参数错误（如邮箱格式无效、缺少必要字段）。
  - `401 Unauthorized`: 未授权访问。
  - `429 Too Many Requests`: 发送频率过高（需要实现限流逻辑）。
  - `500 Internal Server Error`: 服务器内部错误或邮件服务商接口调用失败。
  - `503 Service Unavailable`: 邮件服务暂时不可用。

#### 7.5.2 处理邮件发送状态回调 (Handle Email Status Callbacks / Webhooks)

- **描述**: 接收来自邮件服务商的异步回调（Webhooks），获取邮件发送状态（如已发送、已送达、打开、点击、退信、标记为垃圾邮件等）。
- **URL**: `/api/emails/callbacks/{provider}` (此URL通常在邮件服务商后台配置)
- **方法**: `POST` (通常)
- **认证**: 邮件服务商签名验证或IP白名单。
- **路径参数**:
  - `provider` (string, 必需): 邮件服务商的标识符 (e.g., `sendgrid`, `mailgun`).
- **请求体**: 具体格式取决于邮件服务商 (通常是 `application/json`)。
- **行为**:
  1. 验证请求的真实性（例如，通过签名）。
  2. 解析请求体，获取邮件ID、事件类型、时间戳、收件人等信息。
  3. 更新系统内部的邮件发送记录和用户状态（例如，标记无效邮箱）。
  4. 根据邮件服务商的要求，返回特定格式的响应（例如，HTTP 200 OK）。
- **成功响应 (给邮件服务商)**: 通常是 HTTP `200 OK`。
- **错误处理**: 记录日志，并按服务商要求响应。

**注意**: 
- 邮件发送涉及许多最佳实践，如SPF, DKIM, DMARC记录的正确配置，以提高送达率并避免被标记为垃圾邮件。
- 管理退订列表和处理退信是必要的，以遵守反垃圾邮件法规（如CAN-SPAM, GDPR）。
- 对于大量邮件发送，需要考虑IP信誉和发送配额。
- 不同邮件服务商的API和回调格式会有差异，需要进行适配。

### 7.6 文件存储服务接口 (File Storage Service Integration)

这些接口用于与云存储服务（如 AWS S3, Google Cloud Storage, Azure Blob Storage, 阿里云OSS等）集成，管理用户上传的文件或系统生成的文件。

#### 7.6.1 获取上传凭证/签名URL (Get Upload Credentials / Signed URL)

- **描述**: 为了安全地允许客户端直接上传文件到云存储，后端生成临时的上传凭证或预签名的URL。这避免了文件数据流经应用服务器，减轻了服务器负载并提高了上传速度。
- **URL**: `/api/storage/upload-credentials`
- **方法**: `POST`
- **认证**: 必需 (用户令牌)
- **请求体 (application/json)**:
  ```json
  {
    "filename": "user_avatar.jpg",
    "content_type": "image/jpeg",
    "size": 102400, // 文件大小 (字节, 可选，用于某些策略)
    "storage_path_prefix": "users/{user_id}/avatars/", // 存储路径前缀 (可选，后端可根据业务逻辑决定)
    "access_control": "public-read" // 可选，上传后文件的访问权限 (e.g., private, public-read)
  }
  ```
- **成功响应 (200 OK)**:
  ```json
  {
    "code": 0,
    "message": "成功获取上传凭证",
    "data": {
      "upload_url": "SIGNED_URL_FOR_PUT_REQUEST_TO_CLOUD_STORAGE", // 客户端用于上传的URL
      "method": "PUT", // 通常是PUT
      "headers": { // 可能需要的HTTP头部，例如 Content-Type
        "Content-Type": "image/jpeg"
      },
      "fields": { // 对于表单上传 (POST) 可能需要的额外字段 (例如S3 POST Policy)
        // "key": "users/user_id/avatars/user_avatar.jpg", ...
      },
      "file_key": "users/user_id/avatars/user_avatar.jpg", // 文件在存储服务中的唯一标识 (路径)
      "access_url": "PUBLIC_OR_SIGNED_URL_TO_ACCESS_THE_FILE_AFTER_UPLOAD" // 上传成功后访问文件的URL (可选)
    }
  }
  ```
- **错误响应**:
  - `400 Bad Request`: 请求参数错误。
  - `401 Unauthorized`: 未授权访问。
  - `500 Internal Server Error`: 服务器内部错误或与云存储服务通信失败。

#### 7.6.2 上传完成通知 (Notify Upload Completion)

- **描述**: （可选）客户端在成功将文件上传到云存储后，通知后端更新文件状态或执行后续处理（如图片处理、记录到数据库等）。某些场景下，云存储服务本身也可能提供事件通知（如S3 Event Notifications）。
- **URL**: `/api/storage/upload-callback`
- **方法**: `POST`
- **认证**: 必需 (用户令牌)
- **请求体 (application/json)**:
  ```json
  {
    "file_key": "users/user_id/avatars/user_avatar.jpg",
    "filename": "user_avatar.jpg",
    "content_type": "image/jpeg",
    "size": 102400,
    "etag": "CLOUD_STORAGE_ETAG_OR_VERSION_ID" // 云存储返回的文件ETag或版本ID (可选)
  }
  ```
- **成功响应 (200 OK)**:
  ```json
  {
    "code": 0,
    "message": "文件上传状态已更新",
    "data": {
      "file_id": "SYSTEM_INTERNAL_FILE_ID", // 系统内部文件记录ID
      "access_url": "FINAL_PUBLIC_OR_SIGNED_URL_TO_ACCESS_THE_FILE"
    }
  }
  ```
- **错误响应**:
  - `400 Bad Request`: 请求参数错误。
  - `401 Unauthorized`: 未授权访问。
  - `404 Not Found`: 未找到对应的上传任务或文件记录。
  - `500 Internal Server Error`: 服务器内部错误。

#### 7.6.3 获取下载凭证/签名URL (Get Download Credentials / Signed URL)

- **描述**: 对于存储在私有存储桶中的文件，后端生成临时的、有时限的预签名URL，允许授权用户下载。
- **URL**: `/api/storage/download-url`
- **方法**: `POST`
- **认证**: 必需 (用户令牌，并校验用户是否有权访问该文件)
- **请求体 (application/json)**:
  ```json
  {
    "file_key": "path/to/private/file.pdf",
    "expires_in_seconds": 3600 // URL有效期 (可选，默认值由后端设定)
  }
  ```
- **成功响应 (200 OK)**:
  ```json
  {
    "code": 0,
    "message": "成功获取下载URL",
    "data": {
      "download_url": "SIGNED_URL_FOR_GET_REQUEST_TO_CLOUD_STORAGE",
      "expires_at": "ISO_8601_TIMESTAMP_OF_EXPIRATION"
    }
  }
  ```
- **错误响应**:
  - `400 Bad Request`: 请求参数错误。
  - `401 Unauthorized`: 用户无权访问该文件。
  - `404 Not Found`: 文件未找到。
  - `500 Internal Server Error`: 服务器内部错误。

#### 7.6.4 删除文件 (Delete File)

- **描述**: 从云存储中删除文件。通常需要先在后端校验用户权限。
- **URL**: `/api/storage/files/{file_key}` (file_key 需要URL编码)
- **方法**: `DELETE`
- **认证**: 必需 (用户令牌，并校验用户是否有权删除该文件)
- **路径参数**:
  - `file_key` (string, 必需): 文件在存储服务中的唯一标识。
- **成功响应 (200 OK 或 204 No Content)**:
  ```json
  {
    "code": 0,
    "message": "文件已成功删除"
  }
  ```
- **错误响应**:
  - `401 Unauthorized`: 用户无权删除该文件。
  - `404 Not Found`: 文件未找到。
  - `500 Internal Server Error`: 服务器内部错误或删除失败。

**注意**:
- 文件管理是复杂的主题，涉及安全性（权限控制、防盗链）、成本优化（存储类别、生命周期管理）、版本控制、大文件分片上传/断点续传等。
- 上述接口是通用模型，具体实现依赖于所选的云存储服务及其SDK。
- 考虑文件元数据的存储和管理（例如，文件名、上传者、上传时间、MIME类型、大小等），通常在应用数据库中维护一份记录，并与云存储中的`file_key`关联。

## 8. WebHooks

WebHooks 允许第三方应用程序或服务在您系统中发生特定事件时接收实时HTTP通知。这是一种实现系统间异步通信和集成的重要机制。

### 8.1 注册 WebHook (Register WebHook)

- **描述**: 允许外部应用注册一个URL，以便在特定事件发生时接收通知。
- **URL**: `/api/webhooks`
- **方法**: `POST`
- **认证**: 必需 (通常需要API密钥或OAuth令牌，具有管理WebHooks的权限)
- **请求体 (application/json)**:
  ```json
  {
    "target_url": "https://external-app.com/webhook-receiver", // 接收通知的URL
    "events": ["content.created", "user.updated"], // 订阅的事件类型列表
    "secret": "A_SHARED_SECRET_FOR_SIGNATURE_VERIFICATION", // 可选，用于验证Webhook请求的共享密钥
    "description": "Notify on new content and user updates", // 可选，Webhook的描述
    "is_active": true // 可选，默认为true
  }
  ```
- **成功响应 (201 Created)**:
  ```json
  {
    "code": 0,
    "message": "WebHook注册成功",
    "data": {
      "id": "webhook_123xyz",
      "target_url": "https://external-app.com/webhook-receiver",
      "events": ["content.created", "user.updated"],
      "description": "Notify on new content and user updates",
      "is_active": true,
      "created_at": "2023-10-27T10:00:00Z"
    }
  }
  ```
- **错误响应**:
  - `400 Bad Request`: 请求参数错误（如URL无效、事件类型不支持）。
  - `401 Unauthorized`: 未授权访问。
  - `409 Conflict`: 相似的Webhook已存在 (可选，取决于业务逻辑)。

### 8.2 列出已注册的 WebHooks (List Registered WebHooks)

- **描述**: 获取当前应用或用户已注册的所有WebHooks。
- **URL**: `/api/webhooks`
- **方法**: `GET`
- **认证**: 必需
- **查询参数**:
  - `page` (integer, 可选): 页码，默认为1。
  - `per_page` (integer, 可选): 每页数量，默认为20。
  - `event` (string, 可选): 按特定事件类型过滤。
- **成功响应 (200 OK)**:
  ```json
  {
    "code": 0,
    "message": "成功获取WebHooks列表",
    "data": [
      {
        "id": "webhook_123xyz",
        "target_url": "https://external-app.com/webhook-receiver",
        "events": ["content.created", "user.updated"],
        "is_active": true,
        "created_at": "2023-10-27T10:00:00Z"
      }
      // ...更多WebHooks
    ],
    "meta": {
      "total_items": 1,
      "current_page": 1,
      "per_page": 20,
      "total_pages": 1
    }
  }
  ```

### 8.3 获取 WebHook 详情 (Get WebHook Details)

- **描述**: 获取特定WebHook的详细信息。
- **URL**: `/api/webhooks/{webhook_id}`
- **方法**: `GET`
- **认证**: 必需
- **路径参数**:
  - `webhook_id` (string, 必需): WebHook的唯一ID。
- **成功响应 (200 OK)**:
  ```json
  {
    "code": 0,
    "message": "成功获取WebHook详情",
    "data": {
      "id": "webhook_123xyz",
      "target_url": "https://external-app.com/webhook-receiver",
      "events": ["content.created", "user.updated"],
      "secret_configured": true, // 不直接返回secret本身
      "description": "Notify on new content and user updates",
      "is_active": true,
      "created_at": "2023-10-27T10:00:00Z",
      "last_triggered_at": "2023-10-28T12:00:00Z", // 可选，上次触发时间
      "last_delivery_status": "success" // 可选，上次投递状态
    }
  }
  ```
- **错误响应**:
  - `404 Not Found`: WebHook未找到。

### 8.4 更新 WebHook (Update WebHook)

- **描述**: 修改已注册WebHook的配置。
- **URL**: `/api/webhooks/{webhook_id}`
- **方法**: `PUT` 或 `PATCH`
- **认证**: 必需
- **路径参数**:
  - `webhook_id` (string, 必需): WebHook的唯一ID。
- **请求体 (application/json)**: (与创建时类似，但字段可选)
  ```json
  {
    "target_url": "https://new-external-app.com/webhook-receiver",
    "events": ["content.created", "content.deleted"],
    "secret": "NEW_OR_EXISTING_SECRET", // 更新secret时需提供
    "description": "Updated description",
    "is_active": false
  }
  ```
- **成功响应 (200 OK)**:
  ```json
  {
    "code": 0,
    "message": "WebHook更新成功",
    "data": { /* 更新后的WebHook详情 */ }
  }
  ```
- **错误响应**:
  - `400 Bad Request`: 请求参数错误。
  - `404 Not Found`: WebHook未找到。

### 8.5 删除 WebHook (Delete WebHook)

- **描述**: 移除一个已注册的WebHook。
- **URL**: `/api/webhooks/{webhook_id}`
- **方法**: `DELETE`
- **认证**: 必需
- **路径参数**:
  - `webhook_id` (string, 必需): WebHook的唯一ID。
- **成功响应 (204 No Content 或 200 OK with message)**:
  ```json
  {
    "code": 0,
    "message": "WebHook删除成功"
  }
  ```
- **错误响应**:
  - `404 Not Found`: WebHook未找到。

### 8.6 WebHook 事件传递 (Event Delivery)

当订阅的事件发生时，系统会向注册的 `target_url` 发送一个HTTP `POST` 请求。

- **请求方法**: `POST`
- **Content-Type**: `application/json`
- **请求体 (Payload)**: 包含事件类型和相关数据。
  ```json
  {
    "event_id": "evt_randomuniqueid", // 事件的唯一ID
    "event_type": "content.created", // 触发的事件类型
    "timestamp": "2023-10-27T10:05:00Z", // 事件发生时间
    "data": { // 事件相关的数据
      // 例如，对于 content.created
      "content_id": "post_abc123",
      "title": "New Blog Post",
      "author_id": "user_xyz789"
      // ... 其他相关字段
    },
    "attempt": 1 // 重试次数 (如果支持重试)
  }
  ```
- **请求头**: 可能包含用于验证的签名，例如 `X-Webhook-Signature`。

### 8.7 WebHook 安全性 (Security)

- **签名验证**: 强烈建议为每个Webhook配置一个共享密钥 (`secret`)。系统在发送Webhook时，会使用此密钥对请求体进行HMAC签名（例如，HMAC-SHA256），并将签名放在请求头中（如 `X-Webhook-Signature: t=timestamp,v1=signature`）。接收方应使用相同的密钥和算法验证签名，以确保请求来自您的系统且未被篡改。
- **HTTPS**: `target_url` 必须使用HTTPS，以确保数据在传输过程中的加密。
- **重试机制**: 系统应实现Webhook投递的重试机制，以应对接收方临时不可用的情况。重试通常采用指数退避策略。
- **日志与监控**: 记录Webhook的投递尝试、成功和失败情况，便于排查问题。
- **幂等性**: 接收方应设计为能够幂等地处理Webhook事件，因为网络问题可能导致同一事件被多次投递。

## 9. 安全设计

接口安全是GACMS系统设计的重中之重，旨在保护系统数据不被未授权访问、篡改或泄露，确保服务的连续性和可靠性。

### 9.1 接口安全策略

- **输入验证 (Input Validation)**:
    - 所有来自客户端的输入（包括URL参数、请求头、请求体）都必须经过严格的验证和清洗，以防止常见的Web攻击，如SQL注入、跨站脚本（XSS）、命令注入、XML外部实体注入（XXE）等。
    - 对数据类型、长度、格式、范围进行校验。
    - 使用白名单机制进行输入验证，只接受已知的、合法的输入模式。
    - 对用户上传的文件进行严格的类型和大小检查，并在隔离环境中进行病毒扫描。
- **输出编码 (Output Encoding)**:
    - 在将数据显示到客户端（例如HTML页面、JSON响应）之前，对所有动态数据进行适当的上下文编码，以防止XSS攻击。
- **错误处理与日志记录 (Error Handling & Logging)**:
    - 避免在错误信息中泄露敏感的系统信息（如堆栈跟踪、数据库结构、内部IP地址）。
    - 提供通用的、对用户友好的错误提示。
    - 记录详细的安全事件日志，包括认证尝试（成功与失败）、授权决策、重要操作、以及检测到的攻击行为。日志应包含时间戳、源IP、用户身份（如果可用）、事件类型和结果。
- **HTTPS强制 (HTTPS Everywhere)**:
    - 所有API接口（包括内部服务接口，如果它们通过网络通信）都必须强制使用HTTPS (TLS 1.2或更高版本) 进行通信，确保数据在传输过程中的机密性和完整性。
    - 禁用不安全的HTTP协议。
    - 使用HSTS (HTTP Strict Transport Security) 头部，强制浏览器始终使用HTTPS连接。
- **安全头部 (Security Headers)**:
    - 使用一系列HTTP安全头部来增强应用的安全性，例如：
        - `Content-Security-Policy (CSP)`: 控制资源加载策略，减少XSS风险。
        - `X-Content-Type-Options: nosniff`: 防止浏览器MIME类型嗅探。
        - `X-Frame-Options: DENY` 或 `SAMEORIGIN`: 防止点击劫持。
        - `X-XSS-Protection: 1; mode=block`: 启用浏览器的XSS过滤器 (虽然CSP是更现代的替代方案)。
        - `Referrer-Policy`: 控制Referer头的发送策略。
        - `Permissions-Policy` (原 `Feature-Policy`): 控制浏览器特性的使用权限。
- **速率限制与防暴力破解 (Rate Limiting & Brute-force Protection)**:
    - 对敏感操作（如登录、密码重置、验证码发送）实施严格的速率限制，以防止暴力破解和拒绝服务攻击。
    - 限制来自同一IP地址或同一用户在单位时间内的请求次数。
    - 登录失败次数过多时，应临时锁定账户或要求进行人机验证（如验证码）。
- **依赖项安全 (Dependency Security)**:
    - 定期扫描项目依赖的第三方库和组件，及时发现并修复已知的安全漏洞。
    - 使用工具（如Snyk, Dependabot, OWASP Dependency-Check）自动化此过程。
    - 只从可信的源获取依赖项。
- **API密钥管理 (API Key Management)**:
    - 对于需要API密钥访问的接口，密钥应具备足够的随机性和长度。
    - 密钥不应硬编码在客户端代码中。
    - 提供密钥轮换和吊销机制。
    - 限制API密钥的权限范围，遵循最小权限原则。
- **Web应用防火墙 (WAF)**:
    - 考虑在生产环境部署WAF，以提供额外的保护层，抵御常见的Web攻击。

### 9.2 数据加密传输

- **TLS/SSL**: 
    - 所有通过公共网络传输的数据，特别是API请求和响应，都必须使用TLS (Transport Layer Security) 1.2 或更高版本进行加密。确保使用强加密套件，并禁用已知的弱加密算法和协议版本 (如SSLv3, TLS 1.0, TLS 1.1)。
    - 服务器证书应由受信任的证书颁发机构 (CA) 签发，并定期更新。
    - 启用证书透明度 (Certificate Transparency) 监控。
- **数据加密标准**: 
    - 推荐使用行业标准的加密算法，如AES-256进行对称加密，RSA (2048位或更高) 或 ECC (椭圆曲线加密) 进行非对称加密。
    - 哈希算法应使用SHA-256或更强版本 (如SHA-3)。对于密码存储，必须使用加盐的、自适应的哈希函数，如 bcrypt, scrypt, 或 Argon2。
- **敏感数据保护**: 
    - 除了传输加密外，对于存储在数据库中的高度敏感数据（如支付信息、个人身份信息PII），应考虑进行字段级加密。
    - 加密密钥的管理至关重要，应使用安全的密钥管理系统 (KMS)。

### 9.3 访问控制

- **认证 (Authentication)**: 
    - 确认请求者的身份。如3.1节所述，主要采用JWT Bearer Token机制。
    - 强制多因素认证 (MFA) 对于管理员账户和高权限用户。
- **授权 (Authorization)**: 
    - 在身份认证成功后，确定该用户是否有权限执行请求的操作或访问请求的资源。
    - **基于角色的访问控制 (RBAC)**: 将用户分配到预定义的角色（如管理员、编辑、撰稿人、访客），每个角色拥有一组权限。接口根据用户的角色来判断其访问权限。
    - **基于属性的访问控制 (ABAC)** 或 **基于策略的访问控制 (PBAC)**: 更细粒度的访问控制，基于用户属性、资源属性、操作类型和环境条件（如时间、地点）动态评估权限。这提供了更大的灵活性。
    - **最小权限原则 (Principle of Least Privilege)**: 用户和服务只应被授予执行其任务所必需的最小权限集合。
    - **权限检查点**: 在每个受保护的API端点，都必须执行严格的权限检查。
    - **资源所有权**: 对于用户创建或拥有的资源（例如，用户只能编辑自己的文章），需要检查资源所有权。
    - **拒绝默认 (Deny by Default)**: 除非明确授予权限，否则默认拒绝访问。
    - **会话管理**: 
        - 安全地生成和管理会话标识符（如JWT）。
        - 设置合理的会话超时时间。
        - 提供安全的会话终止机制（登出）。
        - 防止会话固定攻击。

## 10. 性能优化

接口性能是影响用户体验和系统可扩展性的关键因素。GACMS致力于提供响应迅速且高效的API服务。

### 10.1 接口性能指标

为了量化和监控API性能，我们将关注以下关键指标：

- **平均响应时间 (Average Response Time)**: 从接收请求到返回完整响应的平均时间。这是衡量API速度的核心指标。
    - *目标*: 核心接口 < 200ms，普通接口 < 500ms (在典型负载下)。
- **P95/P99响应时间 (95th/99th Percentile Response Time)**: 95%或99%的请求的响应时间低于此值。这有助于识别和优化长尾请求，确保大多数用户获得良好体验。
    - *目标*: P95 < 500ms，P99 < 1000ms。
- **吞吐量 (Throughput / Requests Per Second - RPS)**: API在单位时间内能够成功处理的请求数量。衡量系统的处理能力。
    - *目标*: 根据预估用户量和并发场景设定，例如，核心认证和内容读取接口应能支持数百RPS。
- **错误率 (Error Rate)**: API返回错误响应（4xx, 5xx状态码）的百分比。高错误率可能表示系统不稳定或存在设计缺陷。
    - *目标*: < 0.1% (排除预期的客户端错误如401/403)。
- **并发用户数 (Concurrent Users)**: 系统在保持可接受性能水平下能够同时处理的活跃用户请求数。
- **CPU使用率 (CPU Utilization)**: API服务器的CPU负载情况。持续高CPU使用率可能表明需要优化代码或增加资源。
- **内存使用率 (Memory Utilization)**: API服务器的内存消耗情况。内存泄漏或过高消耗需要关注。
- **网络带宽 (Network Bandwidth)**: API数据传输所占用的网络带宽。对于数据密集型接口尤其重要。
- **数据库查询性能**: 
    - 平均查询时间。
    - 慢查询数量和频率。
    - 数据库连接池使用情况。
- **缓存命中率 (Cache Hit Rate)**: 衡量缓存系统（如Redis, Memcached）的有效性。高命中率意味着更多请求从缓存快速响应，减少了对后端服务的压力。
    - *目标*: > 90% 对于频繁访问且不经常变化的数据。

这些指标将通过监控系统（如Prometheus, Grafana, ELK Stack, 或云服务商提供的监控工具）进行持续跟踪和告警。

### 10.2 优化策略

为了达到上述性能目标，我们将采用以下优化策略：

- **代码层面优化**: 
    - **高效算法与数据结构**: 在处理数据和执行业务逻辑时，选择最优的算法和数据结构。
    - **异步处理**: 对于耗时操作（如邮件发送、复杂计算、第三方服务调用），采用异步任务队列（如Celery, RabbitMQ, Kafka）处理，避免阻塞主请求线程，快速响应客户端。
    - **减少不必要的计算和IO**: 避免冗余的数据库查询、文件读写或外部API调用。
    - **连接池**: 对数据库连接、HTTP客户端连接等使用连接池，减少连接建立的开销。
    - **代码分析与性能剖析**: 定期使用性能分析工具（profilers）识别代码中的性能瓶颈。
- **数据库优化**: 
    - **合理索引**: 为经常查询的字段创建合适的数据库索引，加速查询速度。避免过多或不当索引。
    - **查询优化 (Query Optimization)**: 编写高效的SQL查询语句，避免全表扫描，使用JOIN替代子查询（视情况而定），减少查询返回的不必要字段。
    - **读写分离**: 对于读多写少的场景，考虑使用数据库读写分离架构，将读请求分发到只读副本。
    - **数据库连接优化**: 调整连接池参数，确保高效利用数据库连接。
    - **定期维护**: 如表分析、索引重建等。
- **缓存策略 (Caching)**: 
    - **多级缓存**: 应用层缓存、分布式缓存（如Redis, Memcached）、CDN缓存、浏览器缓存。
    - **缓存常用数据**: 对频繁访问且不经常变化的数据（如配置信息、热门文章、用户信息）进行缓存。
    - **缓存查询结果**: 缓存复杂或耗时查询的结果。
    - **设置合理的缓存过期策略**: 根据数据特性设置合适的TTL (Time-To-Live)，并考虑缓存失效和更新机制（如Cache-Aside, Read-Through, Write-Through, Write-Back）。
    - **使用ETag和Last-Modified**: 配合HTTP头部实现客户端缓存和条件请求，减少不必要的数据传输。
- **网络优化**: 
    - **CDN (Content Delivery Network)**: 将静态资源（如图片、CSS、JS文件）和部分动态内容分发到离用户更近的CDN节点，减少延迟，提高加载速度。
    - **HTTP/2 或 HTTP/3**: 利用这些协议的多路复用、头部压缩等特性提高传输效率。
    - **数据压缩**: 对响应体进行Gzip或Brotli压缩，减少传输数据量。
    - **减少请求次数**: 合并请求，使用GraphQL按需获取数据，或利用HTTP/2的服务器推送。
- **负载均衡 (Load Balancing)**: 
    - 在多个应用服务器实例前部署负载均衡器，将请求分发到不同的服务器，提高系统的处理能力和可用性。
    - 选择合适的负载均衡算法（如轮询、最少连接、IP哈希）。
- **水平扩展与垂直扩展 (Scaling)**: 
    - **水平扩展 (Horizontal Scaling)**: 增加应用服务器实例数量。
    - **垂直扩展 (Vertical Scaling)**: 增加单个服务器的资源（CPU、内存）。
    - 结合自动伸缩策略，根据负载动态调整资源。
- **API网关 (API Gateway)**: 
    - API网关可以处理通用功能如认证、授权、速率限制、请求路由、响应转换和缓存，减轻后端服务的压力。
- **轻量级数据格式**: 
    - 优先使用JSON作为API的数据交换格式。考虑在特定场景下使用更紧凑的格式如Protocol Buffers或MessagePack，尤其是在内部服务间通信。
- **分页与数据裁剪**: 
    - 对返回列表数据的接口强制分页，避免一次性返回大量数据。
    - 允许客户端通过参数（如`fields`）指定需要返回的字段，避免传输不必要的数据。
- **后台任务与批处理**: 
    - 对于非实时性要求的数据处理任务（如报表生成、数据同步），使用后台任务或批处理方式执行，避免影响在线服务的性能。
- **监控与告警**: 
    - 建立完善的性能监控体系，实时收集和分析各项性能指标。
    - 设置合理的告警阈值，当性能指标恶化时及时通知相关人员进行处理。
- **压力测试与容量规划**: 
    - 定期进行压力测试，模拟高并发场景，评估系统的性能极限和瓶颈。
    - 根据业务发展进行容量规划，确保系统资源充足。