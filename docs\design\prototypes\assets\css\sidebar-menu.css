/*
© 2025 Clion Nieh. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
*/

/* 侧边栏基础样式 */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    color: #a0a0a0;
    z-index: 1000;
    display: flex;
    overflow-y: auto;
    flex-direction: column;
    background-color: #161b22;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
}

/* 侧边栏头部和LOGO区域 */
.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: opacity 0.2s ease;
}

.logo:hover {
    opacity: 0.9;
}

.logo img {
    width: 3rem;
    height: 3rem;
    object-fit: contain;
}

.logo span {
    font-size: 1.25rem;
    font-weight: 600;
    color: #fff;
    letter-spacing: 0.5px;
}

/* 侧边栏菜单 */
.sidebar-menu {
    padding: 15px 0;
}

/* 菜单项 */
.sidebar-menu-item {
    margin-bottom: 5px;
}

/* 菜单链接 */
.sidebar-menu-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #a0a0a0;
    text-decoration: none;
    transition: all 0.2s ease;
    position: relative;
    font-size: 0.95rem;
}

.sidebar-menu-link:hover {
    background: linear-gradient(90deg, rgba(37, 99, 235, 0.1), transparent);
    color: #fff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    transform: translateY(-2px);
}

.sidebar-menu-link.active {
    background: linear-gradient(90deg, rgba(37, 99, 235, 0.2), transparent);
    color: #fff;
    border-left: 3px solid #2563eb;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    transform: translateY(-2px);
}

/* 菜单图标 */
.sidebar-menu-link i:first-child {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    font-size: 1rem;
    transition: transform 0.2s ease;
}

.sidebar-menu-link:hover i:first-child {
    transform: scale(1.1);
}

/* 菜单展开/折叠图标 */
.sidebar-menu-toggle {
    margin-left: auto;
    transition: transform 0.3s ease;
    font-size: 0.8rem;
}

.sidebar-menu-toggle.rotate {
    transform: rotate(180deg);
}

/* 子菜单 */
.sidebar-submenu {
    display: none;
}

.sidebar-menu-item.open > .sidebar-submenu {
    display: block;
}

/* 子菜单链接 */
.submenu-link {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px 10px 48px;
    color: #888;
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    position: relative;
}

.submenu-link:hover {
    background: linear-gradient(90deg, rgba(20, 184, 166, 0.1), transparent);
    color: #fff;
}

.submenu-link.active {
    background: linear-gradient(90deg, rgba(20, 184, 166, 0.2), transparent);
    color: #fff;
    border-left: 3px solid #14b8a6;
}

.submenu-link i {
    font-size: 0.85rem;
    width: 16px;
    text-align: center;
    color: #666;
    transition: all 0.2s ease;
}

.submenu-link:hover i,
.submenu-link.active i {
    color: #14b8a6;
}

/* 子菜单图标 */
.submenu-link i {
    margin-right: 10px;
    width: 16px;
    text-align: center;
    font-size: 0.9rem;
    transition: transform 0.2s ease;
}

.submenu-link:hover i {
    transform: scale(1.1);
}

.sidebar-footer a {
    text-decoration: none;
    transition: color 0.2s ease;
}

.sidebar-footer a:hover {
    color: #fff;
}

/* 移动端菜单按钮 */
.mobile-menu-toggle {
    display: none; /* 默认隐藏，只在移动端显示 */
    background: transparent;
    border: none;
    color: #a0a0a0;
    font-size: 1.25rem;
    padding: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mobile-menu-toggle:hover {
    color: #fff;
    transform: scale(1.1);
}

.mobile-menu-toggle:focus {
    outline: none;
    color: #fff;
}

.mobile-menu-toggle i {
    pointer-events: none;
}

/* 遮罩层 */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sidebar-overlay.active {
    display: block;
    opacity: 1;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block; /* 仅在移动端显示 */
    }
    
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
    
    body.sidebar-open {
        overflow: hidden;
    }
    
    /* 移动端Logo样式调整 */
    .logo {
        margin-right: auto;
    }
    
    .logo span {
        font-size: 1.1rem;
    }
}
@media screen and (max-width: 768px) {
    .menu-toggle {
        display: block;
    }
}

@media (max-width: 480px) {

    .menu-toggle {
        top: 15px;
        left: 15px;
        font-size: 1.3rem;
        padding: 8px 12px;
    }
}

.submenu-container {
    background-color: #161b22;
}
