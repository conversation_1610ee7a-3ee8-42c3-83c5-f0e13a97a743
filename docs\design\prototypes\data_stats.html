<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 数据概览</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .content-section-title::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 5rem;
            height: 3px;
            background: linear-gradient(to right, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            background: linear-gradient(145deg, #1e1e1e, #2a2a2a);
            border-left: 4px solid #007bff;
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            backdrop-filter: blur(20px);
            z-index: 1000;
            transform: translateX(120%);
            opacity: 0;
            transition: all 0.2s ease;
            max-width: 350px;
        }
        
        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }
        
        /* 图表容器样式 */
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
        
        /* 数据卡片渐变效果 */
        .stat-card-1 {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        }
        
        .stat-card-2 {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        .stat-card-3 {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }
        
        .stat-card-4 {
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">数据概览</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <div class="relative">
                            <select class="bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none pr-10">
                                <option value="7">最近7天</option>
                                <option value="30" selected>最近30天</option>
                                <option value="90">最近90天</option>
                                <option value="365">最近一年</option>
                            </select>
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none text-gray-400">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                        <a href="#" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-2 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <i class="fas fa-file-export mr-2"></i>
                                导出报表
                            </span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 数据卡片区域 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- 访问量统计卡片 -->
                <div class="stat-card-1 rounded-xl p-6 text-white shadow-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold">总访问量</h3>
                        <div class="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                            <i class="fas fa-eye text-white"></i>
                        </div>
                    </div>
                    <div class="text-3xl font-bold mb-1">128,543</div>
                    <div class="flex items-center text-sm">
                        <span class="flex items-center text-green-300 mr-2">
                            <i class="fas fa-arrow-up mr-1"></i>12.5%
                        </span>
                        <span class="text-white/70">较上月</span>
                    </div>
                </div>
                
                <!-- 用户统计卡片 -->
                <div class="stat-card-2 rounded-xl p-6 text-white shadow-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold">注册用户</h3>
                        <div class="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                            <i class="fas fa-users text-white"></i>
                        </div>
                    </div>
                    <div class="text-3xl font-bold mb-1">2,845</div>
                    <div class="flex items-center text-sm">
                        <span class="flex items-center text-green-300 mr-2">
                            <i class="fas fa-arrow-up mr-1"></i>8.3%
                        </span>
                        <span class="text-white/70">较上月</span>
                    </div>
                </div>
                
                <!-- 内容统计卡片 -->
                <div class="stat-card-3 rounded-xl p-6 text-white shadow-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold">内容数量</h3>
                        <div class="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                            <i class="fas fa-file-alt text-white"></i>
                        </div>
                    </div>
                    <div class="text-3xl font-bold mb-1">1,267</div>
                    <div class="flex items-center text-sm">
                        <span class="flex items-center text-green-300 mr-2">
                            <i class="fas fa-arrow-up mr-1"></i>5.7%
                        </span>
                        <span class="text-white/70">较上月</span>
                    </div>
                </div>
                
                <!-- 转化率卡片 -->
                <div class="stat-card-4 rounded-xl p-6 text-white shadow-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold">平均转化率</h3>
                        <div class="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                            <i class="fas fa-chart-line text-white"></i>
                        </div>
                    </div>
                    <div class="text-3xl font-bold mb-1">3.2%</div>
                    <div class="flex items-center text-sm">
                        <span class="flex items-center text-red-300 mr-2">
                            <i class="fas fa-arrow-down mr-1"></i>0.5%
                        </span>
                        <span class="text-white/70">较上月</span>
                    </div>
                </div>
            </div>

            <!-- 访问趋势图 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold text-white mb-4">访问趋势</h3>
                <div class="chart-container">
                    <canvas id="visitsChart"></canvas>
                </div>
            </div>

            <!-- 数据分析区域 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- 流量来源分析 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <h3 class="text-lg font-bold text-white mb-4">流量来源分析</h3>
                    <div class="chart-container" style="height: 250px;">
                        <canvas id="trafficSourceChart"></canvas>
                    </div>
                </div>
                
                <!-- 设备分布 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <h3 class="text-lg font-bold text-white mb-4">设备分布</h3>
                    <div class="chart-container" style="height: 250px;">
                        <canvas id="deviceChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 热门内容排行 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold text-white mb-4">热门内容排行</h3>
                <div class="overflow-x-auto">
                    <table class="w-full text-left">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="py-3 px-4 text-gray-400 font-medium">标题</th>
                                <th class="py-3 px-4 text-gray-400 font-medium">类型</th>
                                <th class="py-3 px-4 text-gray-400 font-medium">浏览量</th>
                                <th class="py-3 px-4 text-gray-400 font-medium">转化率</th>
                                <th class="py-3 px-4 text-gray-400 font-medium">发布日期</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border-b border-gray-700/50 hover:bg-gray-700/20">
                                <td class="py-3 px-4">
                                    <a href="#" class="text-blue-400 hover:text-blue-300">2025年企业网站设计趋势分析</a>
                                </td>
                                <td class="py-3 px-4">文章</td>
                                <td class="py-3 px-4">12,458</td>
                                <td class="py-3 px-4">
                                    <span class="text-green-400">4.8%</span>
                                </td>
                                <td class="py-3 px-4">2025-05-15</td>
                            </tr>
                            <tr class="border-b border-gray-700/50 hover:bg-gray-700/20">
                                <td class="py-3 px-4">
                                    <a href="#" class="text-blue-400 hover:text-blue-300">如何提高网站SEO排名的10个技巧</a>
                                </td>
                                <td class="py-3 px-4">指南</td>
                                <td class="py-3 px-4">9,872</td>
                                <td class="py-3 px-4">
                                    <span class="text-green-400">5.2%</span>
                                </td>
                                <td class="py-3 px-4">2025-05-10</td>
                            </tr>
                            <tr class="border-b border-gray-700/50 hover:bg-gray-700/20">
                                <td class="py-3 px-4">
                                    <a href="#" class="text-blue-400 hover:text-blue-300">GACMS最新版本功能介绍</a>
                                </td>
                                <td class="py-3 px-4">产品</td>
                                <td class="py-3 px-4">8,541</td>
                                <td class="py-3 px-4">
                                    <span class="text-green-400">3.9%</span>
                                </td>
                                <td class="py-3 px-4">2025-05-22</td>
                            </tr>
                            <tr class="border-b border-gray-700/50 hover:bg-gray-700/20">
                                <td class="py-3 px-4">
                                    <a href="#" class="text-blue-400 hover:text-blue-300">企业网站安全防护指南</a>
                                </td>
                                <td class="py-3 px-4">指南</td>
                                <td class="py-3 px-4">7,236</td>
                                <td class="py-3 px-4">
                                    <span class="text-green-400">4.1%</span>
                                </td>
                                <td class="py-3 px-4">2025-05-05</td>
                            </tr>
                            <tr class="hover:bg-gray-700/20">
                                <td class="py-3 px-4">
                                    <a href="#" class="text-blue-400 hover:text-blue-300">电子商务网站优化策略</a>
                                </td>
                                <td class="py-3 px-4">文章</td>
                                <td class="py-3 px-4">6,894</td>
                                <td class="py-3 px-4">
                                    <span class="text-green-400">4.5%</span>
                                </td>
                                <td class="py-3 px-4">2025-05-18</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="mt-4 text-right">
                    <a href="content_stats.html" class="text-blue-400 hover:text-blue-300 inline-flex items-center">
                        查看更多 <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>

            <!-- 用户活跃度分析 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold text-white mb-4">用户活跃度分析</h3>
                <div class="chart-container">
                    <canvas id="userActivityChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <!-- 通知组件 -->
    <div class="notification" id="notification">
        <div class="mr-3">
            <i class="fas fa-info-circle text-blue-400 text-xl"></i>
        </div>
        <div class="flex-1">
            <h4 class="text-white text-sm font-bold">数据已更新</h4>
            <p class="text-gray-300 text-xs">最新的统计数据已加载完成。</p>
        </div>
    </div>

    <!-- 加载必要的JS -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initCharts();
            
            // 显示通知
            setTimeout(() => {
                document.getElementById('notification').classList.add('show');
                setTimeout(() => {
                    document.getElementById('notification').classList.remove('show');
                }, 3000);
            }, 1000);
        });
        
        /**
         * @function initCharts
         * @description 初始化所有图表
         */
        function initCharts() {
            // 设置Chart.js全局配置
            Chart.defaults.color = '#a0a0a0';
            Chart.defaults.borderColor = 'rgba(107, 114, 128, 0.3)';
            
            // 访问趋势图
            const visitsCtx = document.getElementById('visitsChart').getContext('2d');
            new Chart(visitsCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [
                        {
                            label: '访问量',
                            data: [15000, 21000, 18000, 24000, 23000, 28000],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '独立访客',
                            data: [10000, 15000, 12000, 18000, 16000, 20000],
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                boxWidth: 12,
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(107, 114, 128, 0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
            
            // 流量来源分析饼图
            const trafficSourceCtx = document.getElementById('trafficSourceChart').getContext('2d');
            new Chart(trafficSourceCtx, {
                type: 'doughnut',
                data: {
                    labels: ['搜索引擎', '直接访问', '社交媒体', '外部链接', '邮件营销'],
                    datasets: [{
                        data: [45, 25, 15, 10, 5],
                        backgroundColor: [
                            '#3b82f6',
                            '#10b981',
                            '#f59e0b',
                            '#6366f1',
                            '#ec4899'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 12,
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        }
                    },
                    cutout: '70%'
                }
            });
            
            // 设备分布饼图
            const deviceCtx = document.getElementById('deviceChart').getContext('2d');
            new Chart(deviceCtx, {
                type: 'doughnut',
                data: {
                    labels: ['移动设备', '桌面设备', '平板设备', '其他'],
                    datasets: [{
                        data: [65, 25, 8, 2],
                        backgroundColor: [
                            '#3b82f6',
                            '#10b981',
                            '#f59e0b',
                            '#6366f1'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 12,
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        }
                    },
                    cutout: '70%'
                }
            });
            
            // 用户活跃度分析图
            const userActivityCtx = document.getElementById('userActivityChart').getContext('2d');
            new Chart(userActivityCtx, {
                type: 'bar',
                data: {
                    labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                    datasets: [
                        {
                            label: '活跃用户',
                            data: [580, 620, 750, 690, 840, 920, 780],
                            backgroundColor: '#3b82f6'
                        },
                        {
                            label: '新注册用户',
                            data: [120, 85, 95, 130, 110, 150, 90],
                            backgroundColor: '#10b981'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                boxWidth: 12,
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(107, 114, 128, 0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }
    </script>
</body>
</html> 