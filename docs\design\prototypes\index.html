<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 原型预览</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #0d1117;
            color: #e0e0e0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }
        .header {
            background-color: rgba(13, 17, 23, 0.8);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            border-bottom: 1px solid #30363d;
            position: sticky;
            top: 0;
            z-index: 50;
        }
        .header-content {
            max-width: 1600px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: #fff;
            display: flex;
            align-items: center;
        }
        .logo i {
            margin-right: 0.75rem;
            color: #58a6ff;
        }
        .header-nav a {
            color: #c9d1d9;
            margin-left: 1.5rem;
            text-decoration: none;
            transition: color 0.2s ease;
            font-weight: 500;
        }
        .header-nav a:hover, .header-nav a.active {
            color: #58a6ff;
        }
        .main-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        .section-title {
            font-size: 2rem;
            font-weight: 700;
            color: #fff;
            margin: 3rem 0 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #30363d;
        }
        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
        }
        .prototype-item {
            background-color: #161b22;
            border: 1px solid #30363d;
            border-radius: 0.5rem;
            overflow: hidden;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        .prototype-item:hover {
            transform: translateY(-5px);
            border-color: #58a6ff;
            box-shadow: 0 10px 25px rgba(88, 166, 255, 0.1);
        }
        .prototype-header {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #30363d;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .prototype-title {
            font-size: 1rem;
            font-weight: 600;
            color: #c9d1d9;
        }
        .prototype-actions a {
            color: #58a6ff;
            text-decoration: none;
            transition: color 0.2s ease;
            font-size: 0.875rem;
        }
        .prototype-actions a:hover {
            color: #96d1ff;
        }
        .prototype-preview {
            height: 250px;
            background-color: #0d1117;
            overflow: hidden;
            position: relative;
        }
        .prototype-frame {
            width: 100%;
            height: 100%;
            border: none;
            transform: scale(0.5);
            transform-origin: 0 0;
            width: 200%;
            height: 200%;
        }
        footer {
            text-align: center;
            padding: 3rem 2rem;
            color: #8b949e;
            border-top: 1px solid #30363d;
            margin-top: 3rem;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-layer-group"></i>
                <span>GACMS 原型预览</span>
            </div>
            <nav class="header-nav">
                <a href="#dashboard" class="active">主面板</a>
                <a href="#content">内容核心</a>
                <a href="#site">站点构建</a>
                <a href="#users">用户与权限</a>
                <a href="#settings">系统设置</a>
                <a href="#developer">开发者中心</a>
                <a href="#market">应用市场</a>
                <a href="#analytics">数据分析</a>
                <a href="#auth">认证页面</a>
            </nav>
        </div>
    </header>
    
    <main class="main-container">
        <!-- Sections will be generated here by script -->
    </main>
    
    <footer class="footer">
        © 2025 GACMS. All rights reserved.
    </footer>

    <script>
        const pages = {
            "主面板": [
                { file: "dashboard.html", title: "仪表盘" }
            ],
            "内容核心": [
                { file: "posts.html", title: "文章管理" },
                { file: "post_edit.html", title: "文章编辑" },
                { file: "pages.html", title: "页面管理" },
                { file: "page_edit.html", title: "页面编辑" },
                { file: "categories.html", title: "分类管理" },
                { file: "category_edit.html", title: "分类编辑" },
                { file: "tags.html", title: "标签管理" },
                { file: "tag_edit.html", title: "标签编辑" },
                { file: "topics.html", title: "专题管理" },
                { file: "comments.html", title: "评论管理" },
                { file: "comment_edit.html", title: "评论编辑" },
                { file: "medias.html", title: "媒体库" },
                { file: "media_edit.html", title: "媒体编辑" }
            ],
            "站点构建": [
                { file: "menus.html", title: "菜单管理" },
                { file: "menu_edit.html", title: "菜单编辑" },
                { file: "blocks.html", title: "区块管理" },
                { file: "layouts.html", title: "布局管理" },
                { file: "templates.html", title: "模板管理" },
                { file: "themes.html", title: "主题管理" },
                { file: "theme_editor.html", title: "主题编辑器" },
                { file: "widgets.html", title: "小部件管理" }
            ],
            "用户与权限": [
                { file: "users.html", title: "用户管理" },
                { file: "user_edit.html", title: "用户编辑" },
                { file: "roles.html", title: "角色管理" },
                { file: "role_edit.html", title: "角色编辑" },
                { file: "permissions.html", title: "权限管理" },
                { file: "role_perms.html", title: "角色权限分配" },
                { file: "access_controls.html", title: "访问控制" },
                { file: "client_fields.html", title: "客户端字段" },
                { file: "client_perms.html", title: "客户端权限" },
                { file: "client_forms.html", title: "客户端表单" }
            ],
            "系统设置": [
                { file: "system_set.html", title: "系统设置" },
                { file: "site_set.html", title: "站点设置" },
                { file: "sites.html", title: "多站点管理" },
                { file: "domains.html", title: "域名管理" },
                { file: "languages.html", title: "语言管理" },
                { file: "dictionary.html", title: "数据字典" },
                { file: "caches.html", title: "缓存管理" },
                { file: "backups.html", title: "备份管理" },
                { file: "security.html", title: "安全设置" },
                { file: "seo_set.html", title: "SEO设置" }
            ],
            "开发者中心": [
                { file: "apis.html", title: "API接口" },
                { file: "api_docs.html", title: "API文档" },
                { file: "webhooks.html", title: "WebHooks" },
                { file: "hooks.html", title: "钩子管理" },
                { file: "logs.html", title: "系统日志" },
                { file: "devtools.html", title: "开发者工具" },
                { file: "dev_docs.html", title: "开发文档" },
                { file: "theme_dev.html", title: "主题开发" },
                { file: "database.html", title: "数据库管理" },
                { file: "updates.html", title: "系统更新" }
            ],
            "应用市场": [
                { file: "app_market.html", title: "应用市场" },
                { file: "plugin_market.html", title: "插件市场" },
                { file: "plugins.html", title: "已安装插件" },
                { file: "theme_market.html", title: "主题市场" }
            ],
            "数据分析": [
                { file: "content_stats.html", title: "内容统计" },
                { file: "client_stats.html", title: "客户端统计" },
                { file: "data_stats.html", title: "数据统计" },
                { file: "flow_stats.html", title: "流量分析" },
                { file: "seo_stats.html", title: "SEO分析" },
                { file: "monitor.html", title: "系统监控" },
                { file: "performance.html", title: "性能监控" }
            ],
            "认证页面": [
                { file: "login.html", title: "登录" },
                { file: "register.html", title: "注册" },
                { file: "forgot_password.html", title: "忘记密码" }
            ]
        };

        const mainContainer = document.querySelector('.main-container');

        for (const [section, items] of Object.entries(pages)) {
            const sectionId = section.replace(/\s+/g, '-').toLowerCase();
            const sectionTitleEl = document.createElement('h2');
            sectionTitleEl.id = Object.keys(pages).find(key => pages[key] === items) === '主面板' ? 'dashboard' :
                                Object.keys(pages).find(key => pages[key] === items) === '内容核心' ? 'content' :
                                Object.keys(pages).find(key => pages[key] === items) === '站点构建' ? 'site' :
                                Object.keys(pages).find(key => pages[key] === items) === '用户与权限' ? 'users' :
                                Object.keys(pages).find(key => pages[key] === items) === '系统设置' ? 'settings' :
                                Object.keys(pages).find(key => pages[key] === items) === '开发者中心' ? 'developer' :
                                Object.keys(pages).find(key => pages[key] === items) === '应用市场' ? 'market' :
                                Object.keys(pages).find(key => pages[key] === items) === '数据分析' ? 'analytics' :
                                Object.keys(pages).find(key => pages[key] === items) === '认证页面' ? 'auth' :
                                sectionId;
            sectionTitleEl.className = 'section-title';
            sectionTitleEl.textContent = section;
            mainContainer.appendChild(sectionTitleEl);

            const gridEl = document.createElement('div');
            gridEl.className = 'prototype-grid';
            mainContainer.appendChild(gridEl);

            items.forEach(item => {
                const itemEl = document.createElement('div');
                itemEl.className = 'prototype-item';
                itemEl.innerHTML = `
                    <div class="prototype-header">
                        <div class="prototype-title">${item.title}</div>
                        <div class="prototype-actions">
                            <a href="${item.file}" target="_blank" title="在新窗口中打开">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                    </div>
                    <div class="prototype-preview">
                        <iframe src="${item.file}" class="prototype-frame" loading="lazy" scrolling="no"></iframe>
                    </div>
                `;
                gridEl.appendChild(itemEl);
            });
        }
    </script>
</body>
</html>