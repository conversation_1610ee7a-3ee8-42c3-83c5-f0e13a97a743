/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: {current_date}
 * @LastEditors: <PERSON><PERSON> Nieh
 * @LastEditTime: {current_date}
 * @FilePath: internal/modules/user/application/service/AdminAuthService.go
 * @Description: Implements the application service for backend user authentication.
 *
 * © {current_year} GACMS. All rights reserved.
 */
package service

import (
	"context"
	"fmt"
	"golang.org/x/crypto/bcrypt"
	"gacms/internal/modules/user/domain/contract"
	"gacms/internal/modules/user/domain/model"
	pkgContract "gacms/pkg/contract"
)

// AdminAuthService provides the business logic for admin user authentication.
type AdminAuthService struct {
	adminRepo contract.AdminRepository
	appCtx    pkgContract.AppContext
}

// NewAdminAuthService creates a new instance of AdminAuthService.
func NewAdminAuthService(adminRepo contract.AdminRepository, appCtx pkgContract.AppContext) *AdminAuthService {
	return &AdminAuthService{
		adminRepo: adminRepo,
		appCtx:    appCtx,
	}
}

// LoginDTO holds the data transfer object for an admin login request.
type LoginDTO struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

// Login handles the business logic for an admin user logging in.
func (s *AdminAuthService) Login(ctx context.Context, dto LoginDTO) (string, error) {
	// 1. Find user by email
	admin, err := s.adminRepo.FindByEmail(ctx, dto.Email)
	if err != nil {
		return "", fmt.Errorf("invalid credentials") // Generic error for security
	}

	// 2. Compare password
	err = bcrypt.CompareHashAndPassword([]byte(admin.PasswordHash), []byte(dto.Password))
	if err != nil {
		return "", fmt.Errorf("invalid credentials") // Generic error
	}

	// TODO: 3. Handle 2FA if enabled

	// 4. Generate JWT
	siteID, _ := s.appCtx.Auth().SiteIDFrom(ctx)
	claims := map[string]interface{}{
		"user_id":   admin.ID,
		"site_id":   siteID,
		"user_type": "admin",
		"email":     admin.Email,
	}

	token, err := s.appCtx.Auth().GenerateToken(claims)
	if err != nil {
		return "", fmt.Errorf("could not generate token: %w", err)
	}

	return token, nil
}

// RegisterDTO holds the data for creating a new admin.
type RegisterDTO struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=8"`
}

// Register handles creating a new admin user.
func (s *AdminAuthService) Register(ctx context.Context, dto RegisterDTO) (*model.Admin, error) {
	// 1. Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(dto.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("could not hash password: %w", err)
	}

	// 2. Get site_id from context
	siteID, ok := s.appCtx.Auth().SiteIDFrom(ctx)
	if !ok || siteID == 0 {
		return nil, fmt.Errorf("invalid site context")
	}

	// 3. Create admin model
	admin := &model.Admin{
		SiteID:       siteID,
		Email:        dto.Email,
		PasswordHash: string(hashedPassword),
	}

	// 4. Persist to database
	if err := s.adminRepo.CreateAdmin(ctx, admin); err != nil {
		return nil, fmt.Errorf("could not create admin: %w", err)
	}

	return admin, nil
} 