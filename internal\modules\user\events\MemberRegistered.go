/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: {current_date}
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: {current_date}
 * @FilePath: internal/modules/user/events/MemberRegistered.go
 * @Description: Defines the event dispatched when a new member user is registered.
 *
 * © {current_year} GACMS. All rights reserved.
 */
package events

import (
	"context"
	"gacms/internal/core/bus"
	"gacms/internal/modules/user/domain/model"
	"gacms/pkg/contract"
)

const MemberRegisteredEventName contract.EventName = "user.member.registered"

// MemberRegisteredEvent is dispatched when a new member user is successfully created.
type MemberRegisteredEvent struct {
	bus.BaseEvent
	Payload *model.Member
}

// NewMemberRegisteredEvent creates a new MemberRegisteredEvent.
func NewMemberRegisteredEvent(ctx context.Context, member *model.Member) MemberRegisteredEvent {
	baseEvent := bus.NewBaseEvent(ctx, MemberRegisteredEventName, member).(*bus.BaseEvent)
	return MemberRegisteredEvent{
		BaseEvent: *baseEvent,
		Payload:   member,
	}
} 