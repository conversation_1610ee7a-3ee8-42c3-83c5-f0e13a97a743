<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 角色管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        /* 表格样式 */
        .table-row {
            transition: all 0.3s ease;
        }
        .table-row:hover {
            background-color: rgba(50, 50, 50, 0.5);
        }

        /* 状态标签样式 */
        .status-badge {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .status-badge.active {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }
        
        .status-badge.inactive {
            background: rgba(108, 117, 125, 0.2);
            color: #6c757d;
            border: 1px solid rgba(108, 117, 125, 0.3);
        }
        
        .status-badge.pending {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
            border: 1px solid rgba(245, 158, 11, 0.3);
        }

        /* 开关样式 */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #555;
            transition: .4s;
            border-radius: 24px;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .toggle-slider {
            background-color: #007bff;
        }
        
        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">角色管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <a href="role_edit.html" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-plus text-white"></i>
                                </span>
                                新增角色
                            </span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 统计概览 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- 角色总数 -->
                <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-user-tag text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">角色总数</div>
                            <div class="text-xl font-semibold text-white">8</div>
                            <div class="text-xs text-gray-400 mt-1">系统角色 3 个</div>
                        </div>
                    </div>
                </div>

                <!-- 自定义角色 -->
                <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-user-cog text-purple-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">自定义角色</div>
                            <div class="text-xl font-semibold text-white">5</div>
                            <div class="text-xs text-gray-400 mt-1">本月新增 1 个</div>
                        </div>
                    </div>
                </div>

                <!-- 已分配用户 -->
                <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-users text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">已分配用户</div>
                            <div class="text-xl font-semibold text-white">76</div>
                            <div class="text-xs text-gray-400 mt-1">平均每角色 9.5 人</div>
                        </div>
                    </div>
                </div>

                <!-- 待审核角色 -->
                <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-orange-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-user-clock text-orange-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">待审核角色</div>
                            <div class="text-xl font-semibold text-white">1</div>
                            <div class="text-xs text-gray-400 mt-1">
                                <span class="text-orange-400">1</span> 个需要审核
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 角色列表 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="mb-6 flex flex-wrap justify-between items-center">
                    <h3 class="text-lg font-semibold text-white">角色列表</h3>
                    
                    <!-- 搜索和过滤 -->
                    <div class="flex flex-wrap gap-4 mt-4 sm:mt-0">
                        <div class="relative">
                            <input type="text" placeholder="搜索角色名称..." 
                                   class="w-full md:w-64 bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent pl-10">
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                        <div>
                            <select class="bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="all">所有角色</option>
                                <option value="system">系统角色</option>
                                <option value="custom">自定义角色</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">#</th>
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">角色名称</th>
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">描述</th>
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">类型</th>
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">用户数</th>
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">状态</th>
                                <th class="text-left py-3 px-4 text-gray-300 font-medium">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 超级管理员 -->
                            <tr class="table-row border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-4 text-white">1</td>
                                <td class="py-4 px-4 text-white font-medium">超级管理员</td>
                                <td class="py-4 px-4 text-gray-300">拥有系统最高权限</td>
                                <td class="py-4 px-4">
                                    <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">系统角色</span>
                                </td>
                                <td class="py-4 px-4 text-gray-300">2</td>
                                <td class="py-4 px-4">
                                    <span class="status-badge active">启用</span>
                                </td>
                                <td class="py-4 px-4">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-blue-500" title="查看">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-yellow-500" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-gray-300" title="权限设置">
                                            <i class="fas fa-key"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 编辑 -->
                            <tr class="table-row border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-4 text-white">2</td>
                                <td class="py-4 px-4 text-white font-medium">编辑</td>
                                <td class="py-4 px-4 text-gray-300">内容管理和审核角色</td>
                                <td class="py-4 px-4">
                                    <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">系统角色</span>
                                </td>
                                <td class="py-4 px-4 text-gray-300">6</td>
                                <td class="py-4 px-4">
                                    <span class="status-badge active">启用</span>
                                </td>
                                <td class="py-4 px-4">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-blue-500" title="查看">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-yellow-500" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-gray-300" title="权限设置">
                                            <i class="fas fa-key"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 作者 -->
                            <tr class="table-row border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-4 text-white">3</td>
                                <td class="py-4 px-4 text-white font-medium">作者</td>
                                <td class="py-4 px-4 text-gray-300">内容创作和发布角色</td>
                                <td class="py-4 px-4">
                                    <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">系统角色</span>
                                </td>
                                <td class="py-4 px-4 text-gray-300">45</td>
                                <td class="py-4 px-4">
                                    <span class="status-badge active">启用</span>
                                </td>
                                <td class="py-4 px-4">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-blue-500" title="查看">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-yellow-500" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-gray-300" title="权限设置">
                                            <i class="fas fa-key"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 市场专员 -->
                            <tr class="table-row border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-4 text-white">4</td>
                                <td class="py-4 px-4 text-white font-medium">市场专员</td>
                                <td class="py-4 px-4 text-gray-300">市场推广和数据分析角色</td>
                                <td class="py-4 px-4">
                                    <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">自定义角色</span>
                                </td>
                                <td class="py-4 px-4 text-gray-300">8</td>
                                <td class="py-4 px-4">
                                    <span class="status-badge active">启用</span>
                                </td>
                                <td class="py-4 px-4">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-blue-500" title="查看">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-yellow-500" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-gray-300" title="权限设置">
                                            <i class="fas fa-key"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 财务管理 -->
                            <tr class="table-row border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-4 text-white">5</td>
                                <td class="py-4 px-4 text-white font-medium">财务管理</td>
                                <td class="py-4 px-4 text-gray-300">财务数据和订单管理角色</td>
                                <td class="py-4 px-4">
                                    <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">自定义角色</span>
                                </td>
                                <td class="py-4 px-4 text-gray-300">2</td>
                                <td class="py-4 px-4">
                                    <span class="status-badge active">启用</span>
                                </td>
                                <td class="py-4 px-4">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-blue-500" title="查看">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-yellow-500" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-gray-300" title="权限设置">
                                            <i class="fas fa-key"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 客服专员 -->
                            <tr class="table-row border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-4 text-white">6</td>
                                <td class="py-4 px-4 text-white font-medium">客服专员</td>
                                <td class="py-4 px-4 text-gray-300">客户服务和工单处理角色</td>
                                <td class="py-4 px-4">
                                    <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">自定义角色</span>
                                </td>
                                <td class="py-4 px-4 text-gray-300">6</td>
                                <td class="py-4 px-4">
                                    <span class="status-badge active">启用</span>
                                </td>
                                <td class="py-4 px-4">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-blue-500" title="查看">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-yellow-500" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-gray-300" title="权限设置">
                                            <i class="fas fa-key"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- SEO专员 -->
                            <tr class="table-row border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-4 text-white">7</td>
                                <td class="py-4 px-4 text-white font-medium">SEO专员</td>
                                <td class="py-4 px-4 text-gray-300">搜索引擎优化和数据分析角色</td>
                                <td class="py-4 px-4">
                                    <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">自定义角色</span>
                                </td>
                                <td class="py-4 px-4 text-gray-300">3</td>
                                <td class="py-4 px-4">
                                    <span class="status-badge inactive">停用</span>
                                </td>
                                <td class="py-4 px-4">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-blue-500" title="查看">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-yellow-500" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-gray-300" title="权限设置">
                                            <i class="fas fa-key"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 实习编辑 -->
                            <tr class="table-row hover:bg-gray-800/20">
                                <td class="py-4 px-4 text-white">8</td>
                                <td class="py-4 px-4 text-white font-medium">实习编辑</td>
                                <td class="py-4 px-4 text-gray-300">有限内容编辑权限的实习角色</td>
                                <td class="py-4 px-4">
                                    <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">自定义角色</span>
                                </td>
                                <td class="py-4 px-4 text-gray-300">4</td>
                                <td class="py-4 px-4">
                                    <span class="status-badge pending">待审核</span>
                                </td>
                                <td class="py-4 px-4">
                                    <div class="flex space-x-2">
                                        <button class="text-gray-400 hover:text-blue-500" title="查看">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-yellow-500" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-gray-300" title="权限设置">
                                            <i class="fas fa-key"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div class="flex justify-between items-center mt-6">
                    <div class="text-sm text-gray-400">
                        显示 1 至 8，共 8 条
                    </div>
                    <div class="flex space-x-2">
                        <button disabled class="bg-gray-800 text-gray-500 px-4 py-2 rounded-lg">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="bg-blue-500 text-white px-4 py-2 rounded-lg">1</button>
                        <button disabled class="bg-gray-800 text-gray-500 px-4 py-2 rounded-lg">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
        });
    </script>
</body>
</html>