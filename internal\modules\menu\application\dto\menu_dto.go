/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/menu/application/dto/menu_dto.go
 * @Description: Data Transfer Objects for the Menu module.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package dto

// MenuCreateDTO is used for creating a new menu.
type MenuCreateDTO struct {
	SiteID      uint   `json:"siteId" binding:"required"`
	Name        string `json:"name" binding:"required,max=255"`
	Slug        string `json:"slug" binding:"required,max=255"`
	Description string `json:"description" binding:"max=500"`
}

// MenuUpdateDTO is used for updating an existing menu.
type MenuUpdateDTO struct {
	Name        string `json:"name" binding:"required,max=255"`
	Description string `json:"description" binding:"max=500"`
}

// MenuItemDTO represents a single menu item in request/response bodies.
// It's recursive to support tree structures.
type MenuItemDTO struct {
	ID        uint           `json:"id"`
	Title     string         `json:"title" binding:"required,max=255"`
	URL       string         `json:"url" binding:"required,max=2048"`
	Target    string         `json:"target" binding:"required,oneof=_self _blank"`
	SortOrder int            `json:"sortOrder"`
	Children  []*MenuItemDTO `json:"children"`
}

// MenuStructureUpdateDTO is used to update the entire structure of a menu.
type MenuStructureUpdateDTO struct {
	Items []*MenuItemDTO `json:"items"`
}

// MenuResponseDTO is the standard response for a single menu.
type MenuResponseDTO struct {
	ID          uint   `json:"id"`
	SiteID      uint   `json:"siteId"`
	Name        string `json:"name"`
	Slug        string `json:"slug"`
	Description string `json:"description"`
}

// FullMenuResponseDTO represents a menu with its full item tree.
type FullMenuResponseDTO struct {
	ID          uint           `json:"id"`
	SiteID      uint           `json:"siteId"`
	Name        string         `json:"name"`
	Slug        string         `json:"slug"`
	Description string         `json:"description"`
	Items       []*MenuItemDTO `json:"items"`
}