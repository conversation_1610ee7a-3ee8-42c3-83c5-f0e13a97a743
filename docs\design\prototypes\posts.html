<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 文章管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .content-section-title::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 5rem;
            height: 3px;
            background: linear-gradient(to right, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .user-link::after, .article-link::after, .version-link::after, .plugin-link::after, .comment-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 1px;
            background: linear-gradient(to right, #007bff, #00c6ff);
            transition: width 0.2s ease;
        }
        
        .user-link:hover, .article-link:hover, .version-link:hover, .plugin-link:hover, .comment-link:hover {
            color: #00c6ff;
        }
        
        .user-link:hover::after, .article-link:hover::after, .version-link:hover::after, .plugin-link:hover::after, .comment-link:hover::after {
            width: 100%;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            background: linear-gradient(145deg, #1e1e1e, #2a2a2a);
            border-left: 4px solid #007bff;
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            backdrop-filter: blur(20px);
            z-index: 1000;
            transform: translateX(120%);
            opacity: 0;
            transition: all 0.2s ease;
            max-width: 350px;
        }
        
        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 文章列表卡片 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">文章列表</h2>
                    <a href="post_edit.html" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button mt-4 sm:mt-0">
                        <span class="relative flex items-center">
                            <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                <i class="fas fa-plus-circle text-white"></i>
                            </span>
                            新建文章
                        </span>
                    </a>
                </div>
                
                <div class="mb-6">
                    <div class="relative w-full md:w-96">
                        <input type="text" placeholder="搜索文章标题..." 
                               class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent pl-10">
                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="py-3 px-4 text-left text-gray-300 font-medium"><input type="checkbox" class="rounded bg-gray-700 border-gray-600"></th>
                                <th class="py-3 px-4 text-left text-gray-300 font-medium">ID</th>
                                <th class="py-3 px-4 text-left text-gray-300 font-medium">标题</th>
                                <th class="py-3 px-4 text-left text-gray-300 font-medium hidden md:table-cell">栏目</th>
                                <th class="py-3 px-4 text-left text-gray-300 font-medium hidden md:table-cell">作者</th>
                                <th class="py-3 px-4 text-left text-gray-300 font-medium">状态</th>
                                <th class="py-3 px-4 text-left text-gray-300 font-medium">发布日期</th>
                                <th class="py-3 px-4 text-left text-gray-300 font-medium">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-3 px-4"><input type="checkbox" class="rounded bg-gray-700 border-gray-600"></td>
                                <td class="py-3 px-4 text-gray-300">101</td>
                                <td class="py-3 px-4"><a href="#" class="text-blue-400 hover:text-blue-300 article-link relative">GACMS v1.0 正式发布！</a></td>
                                <td class="py-3 px-4 text-gray-300 hidden md:table-cell">新闻动态</td>
                                <td class="py-3 px-4 text-gray-300 hidden md:table-cell">admin</td>
                                <td class="py-3 px-4"><span class="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs font-medium">已发布</span></td>
                                <td class="py-3 px-4 text-gray-300">2025-05-20</td>
                                <td class="py-3 px-4">
                                    <div class="flex space-x-3">
                                        <a href="post_edit.html" title="编辑" class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" title="预览" class="text-gray-400 hover:text-gray-300">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" title="删除" class="text-red-400 hover:text-red-300">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-3 px-4"><input type="checkbox" class="rounded bg-gray-700 border-gray-600"></td>
                                <td class="py-3 px-4 text-gray-300">102</td>
                                <td class="py-3 px-4"><a href="#" class="text-blue-400 hover:text-blue-300 article-link relative">如何高效使用 Tailwind CSS 进行开发</a></td>
                                <td class="py-3 px-4 text-gray-300 hidden md:table-cell">技术分享</td>
                                <td class="py-3 px-4 text-gray-300 hidden md:table-cell">editor</td>
                                <td class="py-3 px-4"><span class="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded-full text-xs font-medium">草稿</span></td>
                                <td class="py-3 px-4 text-gray-300">2025-05-18</td>
                                <td class="py-3 px-4">
                                    <div class="flex space-x-3">
                                        <a href="post_edit.html" title="编辑" class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" title="预览" class="text-gray-400 hover:text-gray-300">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" title="删除" class="text-red-400 hover:text-red-300">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-3 px-4"><input type="checkbox" class="rounded bg-gray-700 border-gray-600"></td>
                                <td class="py-3 px-4 text-gray-300">103</td>
                                <td class="py-3 px-4"><a href="#" class="text-blue-400 hover:text-blue-300 article-link relative">企业内容管理系统选型指南</a></td>
                                <td class="py-3 px-4 text-gray-300 hidden md:table-cell">行业观察</td>
                                <td class="py-3 px-4 text-gray-300 hidden md:table-cell">admin</td>
                                <td class="py-3 px-4"><span class="bg-orange-500/20 text-orange-400 px-2 py-1 rounded-full text-xs font-medium">待审批</span></td>
                                <td class="py-3 px-4 text-gray-300">2025-05-15</td>
                                <td class="py-3 px-4">
                                    <div class="flex space-x-3">
                                        <a href="post_edit.html" title="编辑" class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" title="预览" class="text-gray-400 hover:text-gray-300">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" title="删除" class="text-red-400 hover:text-red-300">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="py-3 px-4"><input type="checkbox" class="rounded bg-gray-700 border-gray-600"></td>
                                <td class="py-3 px-4 text-gray-300">104</td>
                                <td class="py-3 px-4"><a href="#" class="text-blue-400 hover:text-blue-300 article-link relative">探索 Go Gin 框架的强大功能</a></td>
                                <td class="py-3 px-4 text-gray-300 hidden md:table-cell">技术分享</td>
                                <td class="py-3 px-4 text-gray-300 hidden md:table-cell">author</td>
                                <td class="py-3 px-4"><span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded-full text-xs font-medium">已归档</span></td>
                                <td class="py-3 px-4 text-gray-300">2025-05-10</td>
                                <td class="py-3 px-4">
                                    <div class="flex space-x-3">
                                        <a href="post_edit.html" title="编辑" class="text-blue-400 hover:text-blue-300">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" title="预览" class="text-gray-400 hover:text-gray-300">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" title="删除" class="text-red-400 hover:text-red-300">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 批量操作与分页 -->
                <div class="mt-6 flex flex-wrap justify-between items-center">
                    <div class="flex items-center space-x-2 mb-4 sm:mb-0">
                        <select class="bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">批量操作</option>
                            <option value="publish">批量发布</option>
                            <option value="draft">设为草稿</option>
                            <option value="archive">归档</option>
                            <option value="delete">删除</option>
                        </select>
                        <button class="px-4 py-2 bg-gray-700 text-gray-200 rounded-lg hover:bg-gray-600 transition-all">
                            应用
                        </button>
                    </div>
                    
                    <!-- 分页控件 -->
                    <div class="flex space-x-1">
                        <a href="#" class="px-3 py-1 rounded-md bg-gray-800 text-gray-400 flex items-center justify-center">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <a href="#" class="px-3 py-1 rounded-md bg-blue-600 text-white flex items-center justify-center">1</a>
                        <a href="#" class="px-3 py-1 rounded-md bg-gray-800 text-gray-200 flex items-center justify-center">2</a>
                        <a href="#" class="px-3 py-1 rounded-md bg-gray-800 text-gray-200 flex items-center justify-center">3</a>
                        <a href="#" class="px-3 py-1 rounded-md bg-gray-800 text-gray-400 flex items-center justify-center">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
        });
    </script>
</body>
</html>