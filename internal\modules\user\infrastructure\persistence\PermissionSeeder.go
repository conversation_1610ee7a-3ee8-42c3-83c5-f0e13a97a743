/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-13
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-13
 * @FilePath: internal/modules/user/infrastructure/persistence/PermissionSeeder.go
 * @Description: Seeds the database with initial permissions and roles.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package persistence

import (
	"gacms/internal/modules/user/domain/model"
	"gorm.io/gorm"
	"errors"
)

// PermissionSeeder populates the database with essential permissions and roles.
type PermissionSeeder struct {
	db *gorm.DB
}

// NewPermissionSeeder creates a new seeder for user permissions and roles.
func NewPermissionSeeder(db *gorm.DB) *PermissionSeeder {
	return &PermissionSeeder{db: db}
}

// Name returns the name of the seeder.
func (s *PermissionSeeder) Name() string {
	return "permissions-and-roles"
}

// Run executes the seeder.
func (s *PermissionSeeder) Run() error {
	// 1. Create permissions
	profileReadPerm := &model.Permission{
		Slug:        "user.profile.read",
		Description: "Allows reading the user's own profile information.",
	}
	if err := s.firstOrCreatePermission(profileReadPerm); err != nil {
		return err
	}

	// 2. Create roles
	normalAdminRole := &model.Role{
		Name:        "Normal Admin",
		Description: "A standard administrator with basic permissions.",
		UserType:    model.AdminUser,
	}
	if err := s.firstOrCreateRole(normalAdminRole); err != nil {
		return err
	}

	// 3. Associate permissions with roles
	// Use a transaction to ensure atomicity
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Check if the association already exists
        var count int64
        tx.Model(&model.RolePermission{}).Where("role_id = ? AND permission_id = ?", normalAdminRole.ID, profileReadPerm.ID).Count(&count)
        if count > 0 {
            return nil // Association already exists
        }

		// Create the association
		rolePerm := &model.RolePermission{
			RoleID:       normalAdminRole.ID,
			PermissionID: profileReadPerm.ID,
		}
		return tx.Create(rolePerm).Error
	})
}

func (s *PermissionSeeder) firstOrCreatePermission(p *model.Permission) error {
	err := s.db.Where(model.Permission{Slug: p.Slug}).First(p).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return s.db.Create(p).Error
		}
		return err
	}
	return nil
}

func (s *PermissionSeeder) firstOrCreateRole(r *model.Role) error {
	err := s.db.Where(model.Role{Name: r.Name, UserType: r.UserType}).First(r).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return s.db.Create(r).Error
		}
		return err
	}
	return nil
} 