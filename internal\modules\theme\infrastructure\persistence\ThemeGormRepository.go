/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON> <<EMAIL>>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/theme/infrastructure/persistence/ThemeGormRepository.go
 * @Description: GORM implementation of the ThemeRepository for managing theme installations.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package persistence

import (
	"errors"
	"gacms/internal/modules/theme/domain/contract"
	"gacms/internal/modules/theme/domain/model"
	"gorm.io/gorm"
)

type ThemeGormRepository struct {
	db *gorm.DB
}

func NewThemeGormRepository(db *gorm.DB) contract.ThemeRepository {
	return &ThemeGormRepository{db: db}
}

func (r *ThemeGormRepository) InstallThemeForSite(siteID uint, themeName string) error {
	siteTheme := model.SiteTheme{
		SiteID:    siteID,
		ThemeName: themeName,
	}
	return r.db.Create(&siteTheme).Error
}

func (r *ThemeGormRepository) UninstallThemeForSite(siteID uint, themeName string) error {
	result := r.db.Where("site_id = ? AND theme_name = ?", siteID, themeName).Delete(&model.SiteTheme{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

func (r *ThemeGormRepository) GetInstalledThemesForSite(siteID uint) ([]model.SiteTheme, error) {
	var themes []model.SiteTheme
	err := r.db.Where("site_id = ?", siteID).Find(&themes).Error
	return themes, err
}

func (r *ThemeGormRepository) IsThemeInstalledForSite(siteID uint, themeName string) (bool, error) {
	var count int64
	err := r.db.Model(&model.SiteTheme{}).Where("site_id = ? AND theme_name = ?", siteID, themeName).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
} 