//go:build personal || (!professional && !business)

/*
Author: <PERSON><PERSON>eh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package service

import "gacms/pkg/contract"

// PersonalEditionCompiler 个人版编译器
type PersonalEditionCompiler struct {
	BaseEditionCompiler
}

// 编译时返回个人版管理器（最简配置）
func getCompiledEditionManager() CompileTimeEditionManager {
	return &PersonalEditionCompiler{
		BaseEditionCompiler: BaseEditionCompiler{
			edition: contract.EditionPersonal,
			features: map[string]bool{
				// 个人版基础功能（固化启用）
				"basic_content": true,
				"basic_theme":   true,
				"basic_seo":     true,
				"basic_user":    true,
				"api_access":    true,
				// 删除高级功能的false配置，编译时直接排除
			},
			limits: &EditionLimits{
				MaxSites: 1, // 1个站点
				MaxUsers: 3, // 3个管理用户
				// 删除其他限制，个人版内容管理无限制
			},
		},
	}
}

// PersonalFeatureGuard 个人版功能守卫
func PersonalFeatureGuard(featureName string) bool {
	compiler := GetCompiledEditionManager()
	return compiler.IsFeatureCompiledIn(featureName)
}

// PersonalLimitGuard 个人版限制守卫（最简版本）
func PersonalLimitGuard(limitType string, currentValue int) bool {
	// 个人版只检查必要的限制
	switch limitType {
	case "sites":
		return currentValue >= 1  // 1个站点限制
	case "users":
		return currentValue >= 3  // 3个管理用户限制
	default:
		return false  // 其他限制在个人版中不存在
	}
}
