/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-13
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-13
 * @FilePath: pkg/contract/APIClient.go
 * @Description: Defines the contract for an internal API client.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"context"
	"net/http"
)

// APIResponse represents the response from an internal API call
type APIResponse struct {
	StatusCode int                 `json:"status_code"`
	Body       []byte              `json:"body"`
	Header     http.Header         `json:"header"`
}

// ExternalAPIClient defines the interface for making external API calls only.
// This interface is specifically designed for calling external third-party services,
// not for internal module communication (which should use the event system).
type ExternalAPIClient interface {
	// Call sends an HTTP request to an external API
	Call(ctx context.Context, config ExternalAPIConfig, method, path string, body interface{}) (*APIResponse, error)
}

// ExternalAPIConfig contains configuration for external API calls
type ExternalAPIConfig struct {
	BaseURL     string            `json:"base_url"`     // External API base URL
	Timeout     int               `json:"timeout"`      // Timeout in seconds
	RetryCount  int               `json:"retry_count"`  // Number of retries
	Headers     map[string]string `json:"headers"`      // Custom headers
	Auth        AuthConfig        `json:"auth"`         // Authentication config
}

// AuthConfig contains authentication configuration
type AuthConfig struct {
	Type   string `json:"type"`   // "bearer", "basic", "api_key"
	Token  string `json:"token"`  // Token value
	Header string `json:"header"` // Custom header name for API key
}

// Deprecated: Use ExternalAPIClient instead
// APIClient is kept for backward compatibility but should not be used for new code
type APIClient interface {
	// Get sends a GET request to a full internal path.
	Get(ctx context.Context, path string, params map[string]string) (*APIResponse, error)

	// Post sends a POST request with a JSON body to a full internal path.
	Post(ctx context.Context, path string, body interface{}) (*APIResponse, error)

	// Put sends a PUT request with a JSON body to a full internal path.
	Put(ctx context.Context, path string, body interface{}) (*APIResponse, error)

	// Patch sends a PATCH request with a JSON body to a full internal path.
	Patch(ctx context.Context, path string, body interface{}) (*APIResponse, error)

	// Delete sends a DELETE request to a full internal path.
	Delete(ctx context.Context, path string) (*APIResponse, error)
}