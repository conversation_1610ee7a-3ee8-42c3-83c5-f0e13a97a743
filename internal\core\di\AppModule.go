/*
 * @Author: <PERSON><PERSON>eh <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/di/AppModule.go
 * @Description: 应用程序的主模块
 * 
 * © 2025 GACMS. All rights reserved.
 */

package di

import (
	"gacms/internal/core/service"
	"gacms/internal/infrastructure/config"
	"gacms/internal/infrastructure/database"
	"gacms/internal/infrastructure/log"
	"gacms/pkg/contract"

	"go.uber.org/fx"
)

// CoreModule 只提供核心基础设施和代理工厂
// 移除所有业务模块的静态加载，实现真正的懒加载
var CoreModule = fx.Options(
	// 核心基础设施
	fx.Provide(
		log.NewLogger,
		config.New,
		database.NewDB,
	),
	fx.Invoke(database.RegisterHooks),

	// 核心事件系统
	EventModule,

	// 系统设置基础设施模块
	SystemModule,

	// 代理工厂（关键组件）
	fx.Provide(
		service.NewGlobalDependencies,
		service.NewModuleProxyFactory,
		service.NewServiceProxyFactory,
		service.NewLazyRouter,
		// 将LazyRouter注册为Router接口的实现
		fx.Annotate(
			service.NewLazyRouter,
			fx.As(new(contract.Router)),
		),
	),

	// 模块配置管理
	fx.Provide(
		fx.Annotate(
			service.NewDefaultModuleConfigManager,
			fx.As(new(service.ModuleConfigManager)),
		),
	),

	// Vendors模块管理
	fx.Provide(
		fx.Annotate(
			service.NewDefaultVendorsModuleManager,
			fx.As(new(service.VendorsModuleManager)),
		),
	),

	// 模块性能管理
	fx.Provide(
		fx.Annotate(
			service.NewDefaultModulePerformanceManager,
			fx.As(new(service.ModulePerformanceManager)),
		),
	),

	// 预加载策略管理
	fx.Provide(
		fx.Annotate(
			service.NewDefaultPreloadStrategyManager,
			fx.As(new(service.PreloadStrategyManager)),
		),
	),

	// 许可证管理模块
	LicenseModule,

	// Vendors模块授权
	fx.Provide(
		fx.Annotate(
			service.NewDefaultVendorsLicenseManager,
			fx.As(new(service.VendorsLicenseManager)),
		),
	),

	// Vendors商业化
	fx.Provide(
		fx.Annotate(
			service.NewDefaultVendorsCommerceManager,
			fx.As(new(service.VendorsCommerceManager)),
		),
	),

	// Vendors开发者工具
	fx.Provide(
		fx.Annotate(
			service.NewDefaultVendorsDeveloperTools,
			fx.As(new(service.VendorsDeveloperTools)),
		),
	),

	// 核心服务（多租户支持）
	fx.Provide(
		service.NewModuleDiscovery,
		service.NewModuleManager,
		service.NewEventManager,
		service.NewConventionRouter,
	),

	// ❌ 移除所有业务模块的静态加载
	// di.UserModule,     // 改为懒加载
	// theme.Module,      // 改为懒加载
	// content.Module,    // 改为懒加载

	// 注册生命周期钩子
	fx.Invoke(registerPreloadLifecycleHooks),
)

// AppModule 保持向后兼容，指向CoreModule
var AppModule = CoreModule

// registerPreloadLifecycleHooks 注册预加载生命周期钩子
func registerPreloadLifecycleHooks(
	lc fx.Lifecycle,
	preloadManager service.PreloadStrategyManager,
	logger fx.Logger,
) {
	lc.Append(fx.Hook{
		OnStart: func(ctx fx.Context) error {
			logger.Info("Starting module preload...")

			// 执行启动时预加载
			if err := preloadManager.ExecutePreload(ctx, service.PreloadOnStartup); err != nil {
				logger.Error("Failed to execute startup preload", "error", err)
				// 不返回错误，允许系统继续启动
			}

			return nil
		},
	})
}