# ADR-010: 统一应用上下文、代理模式与API通信架构

## Status
Accepted

## Context
在对GACMS核心架构进行深入审查时，我们发现现有及设想中的模块加载、依赖注入和通信机制存在一些模糊和潜在冲突。主要挑战包括：
1.  如何实现服务实例的延迟加载（Lazy Loading）以优化启动性能。
2.  如何实现模块与核心之间、模块与模块之间的清晰解耦。
3.  如何确保多租户架构下的服务复用和数据隔离。
4.  如何在不重启应用的情况下实现模块的热插拔。

经过多轮讨论，我们明确了必须在Go语言原生能力（特别是Windows开发环境限制）下寻找最稳健、可维护的方案。

## Decision
我们决定采纳一套统一的、分层的架构设计原则，该原则由以下三个核心模式组成：

1.  **统一应用上下文 (`AppContext`) 模式**:
    *   核心层将不再向模块暴露一系列零散的服务接口（如`Database`, `Logger`, `Config`等）。
    *   取而代之，核心将提供一个单一、稳定的接口：`contract.AppContext`。
    *   所有模块，无论是自研还是第三方，都只依赖这个`AppContext`接口来获取所有核心功能。
    *   **目的**: 极大简化模块的依赖，实现模块与核心的彻底解耦，并遵循"开闭原则"，使得核心在未来扩展新服务时，无需修改任何模块代码。

2.  **服务代理 (Proxy) 模式**:
    *   对于初始化开销较大或不常用的服务，我们将不直接在DI容器中注册真实服务。
    *   我们将注册一个轻量级的"服务代理"。此代理实现了与真实服务完全相同的接口。
    *   代理在应用启动时被创建，其成本极低。当代理的方法被首次调用时，它将负责创建并缓存"真实服务"的实例。
    *   **目的**: 实现服务实例的"延迟加载"，显著降低应用启动时间和资源开销，同时对服务调用方完全透明。

3.  **模块间API通信模式**:
    *   模块之间的横向通信，必须通过标准化的API进行，而不是通过直接的服务注入或方法调用。
    *   一个模块需要另一个模块的功能时，它应该依赖于一个`APIClient`接口，该接口封装了对目标模块API的HTTP调用。
    *   **目的**: 强制模块间的边界，确保模块的独立性和可替代性。为未来将模块拆分为独立微服务铺平了道路，保障了系统的长期可扩展性。

## Alternatives Considered
### 方案一：动态创建`fx`子容器 (原`ModuleManager`思路)
- **优点**: 实现了模块的延迟加载。
- **缺点**: 子容器是隔离的，无法访问主容器的共享核心服务（如数据库连接池），会导致核心服务被重复创建，造成资源浪费和状态不一致。此方案存在根本性缺陷。
- **Why Not**: 破坏了应用内共享单例服务的核心原则，不可取。

### 方案二：Go `plugin`动态库
- **优点**: 可以实现真正的模块热插拔。
- **缺点**: Go的`plugin`包不支持Windows平台，与项目指定的开发环境冲突。同时，它对编译环境的一致性要求极高，非常脆弱。
- **Why Not**: 存在无法绕过的平台限制，技术上不可行。

## Consequences

### Positive
- **高度标准化**: 模块的依赖注入、路由注册、扩展和通信机制都将有统一、清晰的标准。
- **性能优化**: 通过代理模式解决了重量级服务的启动性能开销问题。
- **强解耦**: 模块与核心、模块与模块之间都有了清晰的边界，降低了系统的复杂度，提高了可维护性。
- **可扩展性**: 架构设计为未来的微服务化演进预留了清晰的路径。
- **开发效率**: 开发者只需关注`AppContext`和模块自身的业务逻辑，心智负担大大降低。

### Negative
- **放弃热插拔**: 我们明确接受了在Go的静态编译模型下，安装新模块需要重启应用这一事实。
- **轻微的复杂性增加**: 代理模式和`AppContext`模式需要编写一些额外的模板代码，但这种一次性的投入是值得的。

## Implementation Notes
- **重构任务**:
    1.  在`pkg/contract/`中定义`AppContext`接口。
    2.  在`internal/core/service/`中实现`AppContext`。
    3.  修改`internal/core/di/Container.go`，使其提供`AppContext`的实现，并移除对零散核心服务的直接提供。
    4.  识别需要延迟加载的服务，并为其创建代理实现。
    5.  重构现有模块，使其依赖`AppContext`而非多个核心服务接口。
    6.  为需要跨模块通信的场景创建`APIClient`接口和实现。
    7.  移除原有的`ModuleManager`及其子容器加载逻辑。
- **路由设计**: 路由应由控制器通过实现统一接口（如`RoutableController`）来显式声明，并在启动时由路由管理器统一注册。
- **多租户**: 多租户数据隔离通过在`Repository`层结合`context.Context`中的`tenant_id`来实现，与本架构正交，可以良好协作。 