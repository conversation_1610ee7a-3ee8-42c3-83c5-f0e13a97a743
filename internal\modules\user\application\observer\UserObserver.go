/*
 * @Author: <PERSON><PERSON>eh <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/modules/user/application/observer/UserObserver.go
 * @Description: 用户模块的主观察者，遵循统一的目录结构和命名规范
 * 
 * © 2025 GACMS. All rights reserved.
 */

package observer

import (
	"gacms/internal/modules/user/events"
	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// UserObserver 用户模块的主观察者
type UserObserver struct {
	logger *zap.Logger
}

// UserObserverParams 定义了创建 UserObserver 所需的参数
type UserObserverParams struct {
	fx.In

	Logger *zap.Logger
}

// NewUserObserver 创建一个新的 UserObserver 实例
func NewUserObserver(params UserObserverParams) *UserObserver {
	return &UserObserver{
		logger: params.Logger,
	}
}

// Handle 处理用户相关事件
func (o *UserObserver) Handle(event contract.Event) error {
	switch event.Name() {
	case string(events.UserRegistered):
		return o.handleUserRegistered(event)
	case string(events.UserLoggedIn):
		return o.handleUserLoggedIn(event)
	case string(events.UserProfileUpdated):
		return o.handleUserProfileUpdated(event)
	default:
		// 不处理不支持的事件
		return nil
	}
}

// handleUserRegistered 处理用户注册事件
func (o *UserObserver) handleUserRegistered(event contract.Event) error {
	payload, ok := event.Payload().(*events.UserRegisteredPayload)
	if !ok {
		o.logger.Error("Invalid payload type for UserRegistered event")
		return nil
	}

	o.logger.Info("User registered",
		zap.String("userId", payload.UserID),
		zap.String("username", payload.Username),
		zap.String("email", payload.Email),
		zap.String("siteId", payload.SiteID),
	)

	// 执行用户注册后的业务逻辑，例如：
	// 1. 发送欢迎邮件
	// 2. 创建用户默认设置
	// 3. 分配默认角色
	// 4. 记录用户活动日志

	return nil
}

// handleUserLoggedIn 处理用户登录事件
func (o *UserObserver) handleUserLoggedIn(event contract.Event) error {
	payload, ok := event.Payload().(*events.UserLoggedInPayload)
	if !ok {
		o.logger.Error("Invalid payload type for UserLoggedIn event")
		return nil
	}

	o.logger.Info("User logged in",
		zap.String("userId", payload.UserID),
		zap.String("username", payload.Username),
		zap.String("ip", payload.IP),
		zap.String("userAgent", payload.UserAgent),
		zap.String("siteId", payload.SiteID),
	)

	// 执行用户登录后的业务逻辑，例如：
	// 1. 更新用户最后登录时间
	// 2. 记录登录历史
	// 3. 检查异常登录行为
	// 4. 更新用户在线状态

	return nil
}

// handleUserProfileUpdated 处理用户资料更新事件
func (o *UserObserver) handleUserProfileUpdated(event contract.Event) error {
	payload, ok := event.Payload().(*events.UserProfileUpdatedPayload)
	if !ok {
		o.logger.Error("Invalid payload type for UserProfileUpdated event")
		return nil
	}

	o.logger.Info("User profile updated",
		zap.String("userId", payload.UserID),
		zap.String("username", payload.Username),
		zap.Any("changedFields", payload.ChangedFields),
		zap.String("updatedBy", payload.UpdatedBy),
		zap.String("siteId", payload.SiteID),
	)

	// 执行用户资料更新后的业务逻辑，例如：
	// 1. 更新缓存中的用户信息
	// 2. 记录用户资料变更历史
	// 3. 发送通知（如果涉及重要信息变更）
	// 4. 触发相关业务流程（如邮箱变更需要验证）

	return nil
}

// HandlerName 返回处理器的名称
func (o *UserObserver) HandlerName() string {
	return "user.observer"
}

// SupportedEvents 返回处理器支持的事件列表
func (o *UserObserver) SupportedEvents() []contract.EventName {
	return []contract.EventName{
		events.UserRegistered,
		events.UserLoggedIn,
		events.UserProfileUpdated,
	}
}

// Priority 返回事件处理器的优先级
func (o *UserObserver) Priority() int {
	// 用户事件处理的优先级中等
	return 50
}

// IsAsync 返回是否异步处理
func (o *UserObserver) IsAsync() bool {
	// 用户事件可以异步处理，不阻塞主业务流程
	return true
}
