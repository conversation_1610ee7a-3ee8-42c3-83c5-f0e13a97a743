# 权限中间件使用示例

本文档展示了如何在控制器中使用权限中间件来保护API端点，基于GACMS的规则路由和懒加载模式。

## 基本用法

有两种方式使用权限中间件：

### 1. 方法级别的权限控制（推荐）

遵循GACMS的命名约定和规则路由，为每个控制器方法添加权限检查：

```go
// 在控制器中
type PostController struct {
    permMiddleware *middleware.PermissionMiddleware
    // 其他依赖...
}

// 构造函数通过依赖注入获取中间件
func NewPostController(permMiddleware *middleware.PermissionMiddleware, ...) *PostController {
    return &PostController{
        permMiddleware: permMiddleware,
        // 初始化其他字段...
    }
}

// 公开API - 无需权限
func (c *PostController) GetPosts(ctx *gin.Context) {
    // 处理获取文章列表...
}

func (c *PostController) GetPost(ctx *gin.Context) {
    // 处理获取单个文章...
}

// 需要权限的API - 在方法内部检查权限
func (c *PostController) PostPost(ctx *gin.Context) {
    // 使用中间件处理函数包装实际处理逻辑
    handler := c.permMiddleware.RequireAdminPermission("posts.create")
    
    // 创建一个内部处理函数
    actualHandler := func(ctx *gin.Context) {
        // 处理创建文章...
    }
    
    // 手动执行中间件链
    handler(ctx)
    
    // 如果中间件没有中止请求，继续执行实际处理程序
    if !ctx.IsAborted() {
        actualHandler(ctx)
    }
}
```

### 2. 使用自定义路由组（在需要手动注册路由时）

如果需要手动注册路由，可以使用以下方式：

```go
// 在控制器中
func (c *PostController) RegisterRoutes(group *gin.RouterGroup) {
    posts := group.Group("/posts")
    {
        // 公开API - 无需权限
        posts.GET("", c.GetPosts)
        posts.GET("/:id", c.GetPost)
        
        // 需要权限的API
        posts.POST("", c.permMiddleware.RequireAdminPermission("posts.create"), c.CreatePost)
        posts.PUT("/:id", c.permMiddleware.RequireAdminPermission("posts.update"), c.UpdatePost)
        posts.DELETE("/:id", c.permMiddleware.RequireAdminPermission("posts.delete"), c.DeletePost)
    }
}
```

## 在依赖注入中注册中间件

在`module.go`中注册`PermissionMiddleware`：

```go
// 在module.go中
fx.Provide(
    // 其他依赖项...
    middleware.NewPermissionMiddleware,
),
```

## 权限检查流程

1. 用户请求受保护的API端点
2. Auth中间件验证用户并在Gin上下文中设置`user_id`
3. PermissionMiddleware检查用户是否拥有执行操作所需的权限
4. 如果用户有权限，请求继续处理；否则返回403错误

## 权限命名约定

权限应遵循以下命名约定：`{模块}.{操作}`，例如：

- `posts.create` - 创建文章
- `posts.update` - 更新文章
- `posts.delete` - 删除文章
- `users.manage` - 管理用户
- `settings.edit` - 编辑系统设置

## 超级管理员

超级管理员用户会自动获得所有权限，无需明确分配。`PermissionService.Can()`方法会自动检查用户是否为超级管理员。 