/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON> <<EMAIL>>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/theme/domain/contract/ThemeRepository.go
 * @Description: Defines the repository interface for theme installation and file operations.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package contract

import "gacms/internal/modules/theme/domain/model"

type ThemeRepository interface {
	InstallThemeForSite(siteID uint, themeName string) error
	UninstallThemeForSite(siteID uint, themeName string) error
	GetInstalledThemesForSite(siteID uint) ([]model.SiteTheme, error)
	IsThemeInstalledForSite(siteID uint, themeName string) (bool, error)
} 