<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 数据导入导出</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .card-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 20px;
        }
        
        .upload-zone {
            border: 2px dashed #4B5563;
            border-radius: 8px;
            padding: 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            transition: all 0.3s ease;
            background-color: rgba(75, 85, 99, 0.1);
        }
        
        .upload-zone:hover {
            border-color: #3B82F6;
            background-color: rgba(59, 130, 246, 0.05);
        }
        
        .upload-icon {
            font-size: 3rem;
            color: #4B5563;
            margin-bottom: 16px;
        }
        
        .progress-bar {
            height: 6px;
            background-color: #374151;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 8px;
        }
        
        .progress-value {
            height: 100%;
            background: linear-gradient(90deg, #3B82F6, #60A5FA);
            border-radius: 3px;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部页面标题 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h2 class="text-xl font-bold text-white relative pl-3 section-title">数据导入导出</h2>
                <p class="mt-4 text-gray-400">通过该功能，您可以轻松地导入或导出系统中的各类数据，包括文章、用户、分类等信息。支持多种格式，便于系统数据的备份、迁移和恢复。</p>
            </div>
            
            <!-- 主要功能区 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <!-- 导入区域 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center mb-6">
                        <div class="card-icon bg-blue-500/20 text-blue-400">
                            <i class="fas fa-file-import"></i>
                        </div>
                        <h3 class="text-lg font-semibold">数据导入</h3>
                    </div>
                    
                    <div class="space-y-6">
                        <div>
                            <label class="block text-gray-300 mb-2">选择数据类型：</label>
                            <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option>文章数据</option>
                                <option>用户数据</option>
                                <option>分类数据</option>
                                <option>评论数据</option>
                                <option>标签数据</option>
                                <option>全部数据</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-gray-300 mb-2">选择文件格式：</label>
                            <div class="flex flex-wrap gap-3">
                                <label class="flex items-center px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg cursor-pointer hover:bg-gray-700">
                                    <input type="radio" name="importFormat" class="mr-2" checked>
                                    <span>JSON</span>
                                </label>
                                <label class="flex items-center px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg cursor-pointer hover:bg-gray-700">
                                    <input type="radio" name="importFormat" class="mr-2">
                                    <span>XML</span>
                                </label>
                                <label class="flex items-center px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg cursor-pointer hover:bg-gray-700">
                                    <input type="radio" name="importFormat" class="mr-2">
                                    <span>CSV</span>
                                </label>
                                <label class="flex items-center px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg cursor-pointer hover:bg-gray-700">
                                    <input type="radio" name="importFormat" class="mr-2">
                                    <span>SQL</span>
                                </label>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-gray-300 mb-2">上传文件：</label>
                            <div class="upload-zone">
                                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                <p class="mb-2">将文件拖放到此处，或</p>
                                <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                                    选择文件
                                </button>
                                <input type="file" class="hidden" id="importFile">
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-gray-300 mb-2">导入选项：</label>
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                                    <span class="ml-2">覆盖已存在的数据</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                                    <span class="ml-2">导入完成后验证数据完整性</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500">
                                    <span class="ml-2">导入前备份现有数据</span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="flex justify-end">
                            <button class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                                <span class="relative flex items-center">
                                    <i class="fas fa-file-import mr-2"></i> 开始导入
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 导出区域 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center mb-6">
                        <div class="card-icon bg-green-500/20 text-green-400">
                            <i class="fas fa-file-export"></i>
                        </div>
                        <h3 class="text-lg font-semibold">数据导出</h3>
                    </div>
                    
                    <div class="space-y-6">
                        <div>
                            <label class="block text-gray-300 mb-2">选择数据类型：</label>
                            <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option>文章数据</option>
                                <option>用户数据</option>
                                <option>分类数据</option>
                                <option>评论数据</option>
                                <option>标签数据</option>
                                <option>全部数据</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-gray-300 mb-2">选择文件格式：</label>
                            <div class="flex flex-wrap gap-3">
                                <label class="flex items-center px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg cursor-pointer hover:bg-gray-700">
                                    <input type="radio" name="exportFormat" class="mr-2" checked>
                                    <span>JSON</span>
                                </label>
                                <label class="flex items-center px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg cursor-pointer hover:bg-gray-700">
                                    <input type="radio" name="exportFormat" class="mr-2">
                                    <span>XML</span>
                                </label>
                                <label class="flex items-center px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg cursor-pointer hover:bg-gray-700">
                                    <input type="radio" name="exportFormat" class="mr-2">
                                    <span>CSV</span>
                                </label>
                                <label class="flex items-center px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg cursor-pointer hover:bg-gray-700">
                                    <input type="radio" name="exportFormat" class="mr-2">
                                    <span>SQL</span>
                                </label>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-gray-300 mb-2">导出范围：</label>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm text-gray-400 mb-1">开始日期：</label>
                                    <input type="date" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm text-gray-400 mb-1">结束日期：</label>
                                    <input type="date" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-gray-300 mb-2">导出选项：</label>
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                                    <span class="ml-2">包含关联数据</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                                    <span class="ml-2">导出完成后下载</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500">
                                    <span class="ml-2">压缩文件</span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="flex justify-end">
                            <button class="flex items-center justify-center bg-gradient-to-r from-green-500 to-green-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-green-500/30 relative overflow-hidden action-button">
                                <span class="relative flex items-center">
                                    <i class="fas fa-file-export mr-2"></i> 开始导出
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 历史记录 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl overflow-hidden mb-6">
                <div class="p-6 border-b border-gray-700">
                    <h3 class="text-lg font-semibold">导入导出历史</h3>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="text-left border-b border-gray-700">
                                <th class="px-6 py-4 text-sm font-semibold">操作类型</th>
                                <th class="px-6 py-4 text-sm font-semibold">数据类型</th>
                                <th class="px-6 py-4 text-sm font-semibold">文件格式</th>
                                <th class="px-6 py-4 text-sm font-semibold">文件大小</th>
                                <th class="px-6 py-4 text-sm font-semibold">处理时间</th>
                                <th class="px-6 py-4 text-sm font-semibold">状态</th>
                                <th class="px-6 py-4 text-sm font-semibold">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <span class="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full mr-2">导出</span>
                                        <span>数据导出</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">文章数据</td>
                                <td class="px-6 py-4">JSON</td>
                                <td class="px-6 py-4">5.2 MB</td>
                                <td class="px-6 py-4">2025-03-09 14:30</td>
                                <td class="px-6 py-4">
                                    <span class="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full">成功</span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-400 hover:text-blue-300" title="下载">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <button class="text-red-400 hover:text-red-300" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <span class="px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full mr-2">导入</span>
                                        <span>数据导入</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">用户数据</td>
                                <td class="px-6 py-4">CSV</td>
                                <td class="px-6 py-4">1.8 MB</td>
                                <td class="px-6 py-4">2025-03-08 10:15</td>
                                <td class="px-6 py-4">
                                    <span class="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full">成功</span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-400 hover:text-blue-300" title="查看日志">
                                            <i class="fas fa-file-alt"></i>
                                        </button>
                                        <button class="text-red-400 hover:text-red-300" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <span class="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full mr-2">导出</span>
                                        <span>数据导出</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">全部数据</td>
                                <td class="px-6 py-4">SQL</td>
                                <td class="px-6 py-4">42.7 MB</td>
                                <td class="px-6 py-4">2025-03-05 08:45</td>
                                <td class="px-6 py-4">
                                    <span class="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full">成功</span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-400 hover:text-blue-300" title="下载">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <button class="text-red-400 hover:text-red-300" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <span class="px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full mr-2">导入</span>
                                        <span>数据导入</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">分类数据</td>
                                <td class="px-6 py-4">XML</td>
                                <td class="px-6 py-4">0.7 MB</td>
                                <td class="px-6 py-4">2025-03-03 16:20</td>
                                <td class="px-6 py-4">
                                    <span class="px-2 py-1 bg-red-500/20 text-red-400 text-xs rounded-full">失败</span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-400 hover:text-blue-300" title="查看日志">
                                            <i class="fas fa-file-alt"></i>
                                        </button>
                                        <button class="text-red-400 hover:text-red-300" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <span class="px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full mr-2">导入</span>
                                        <span>数据导入</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">评论数据</td>
                                <td class="px-6 py-4">JSON</td>
                                <td class="px-6 py-4">3.1 MB</td>
                                <td class="px-6 py-4">2025-03-01 11:05</td>
                                <td class="px-6 py-4">
                                    <span class="px-2 py-1 bg-yellow-500/20 text-yellow-400 text-xs rounded-full">处理中</span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex space-x-2">
                                        <button class="text-yellow-400 hover:text-yellow-300" title="取消">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页控件 -->
                <div class="p-6 border-t border-gray-700 flex justify-between items-center">
                    <span class="text-sm text-gray-400">显示 1-5 条，共 12 条</span>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded" disabled>上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded">1</button>
                        <button class="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded">2</button>
                        <button class="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded">3</button>
                        <button class="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded">下一页</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 文件上传交互
            const uploadZone = document.querySelector('.upload-zone');
            const importFileInput = document.getElementById('importFile');
            
            if (uploadZone && importFileInput) {
                uploadZone.addEventListener('click', function() {
                    importFileInput.click();
                });
                
                uploadZone.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.style.borderColor = '#3B82F6';
                    this.style.backgroundColor = 'rgba(59, 130, 246, 0.05)';
                });
                
                uploadZone.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.style.borderColor = '#4B5563';
                    this.style.backgroundColor = 'rgba(75, 85, 99, 0.1)';
                });
                
                uploadZone.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.style.borderColor = '#4B5563';
                    this.style.backgroundColor = 'rgba(75, 85, 99, 0.1)';
                    
                    // 实际应用中，这里将处理拖放的文件
                    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                        importFileInput.files = e.dataTransfer.files;
                        // 显示文件名称或其他处理
                        const fileName = e.dataTransfer.files[0].name;
                        alert(`已选择文件: ${fileName}`);
                    }
                });
                
                importFileInput.addEventListener('change', function() {
                    if (this.files && this.files.length > 0) {
                        // 显示文件名称或其他处理
                        const fileName = this.files[0].name;
                        alert(`已选择文件: ${fileName}`);
                    }
                });
            }
        });
    </script>
</body>
</html> 