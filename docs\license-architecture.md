# GACMS 许可证架构设计

## 概述

GACMS 采用双重许可证验证机制，通过编译时功能固化和运行时许可证验证，实现安全可靠的版本控制和功能管理。

## 架构原则

### 1. 分层控制
- **编译时**: 定义功能边界，固化版本差异
- **安装时**: 验证部署权限，配置启用功能
- **运行时**: 验证使用权限，控制功能访问

### 2. 安全优先
- 功能边界在编译时固化，运行时无法篡改
- 双重许可证验证，确保权限层级清晰
- 数字签名保护，防止许可证被伪造

### 3. 降级保证
- 系统许可证过期时自动降级到个人版
- 确保系统始终可用，不会完全停止服务
- 优雅降级，保持基础功能正常运行

## 版本控制架构

### 个人版 (Personal Edition)
```
编译时: 硬编码基础功能
    ↓
安装时: 无验证，直接启用所有基础功能
    ↓
运行时: 无许可证检查，直接使用
```

**特点**:
- 功能固化在代码中，无法修改
- 无需任何许可证文件
- 所有基础功能默认启用
- 内容管理无数量限制

### 专业版/商业版 (Professional/Business Edition)
```
编译时: 读取配置，编译功能边界
    ↓
安装时: 验证系统许可证 → 配置启用功能
    ↓
运行时: 验证使用许可证 → 控制功能访问
```

**特点**:
- 功能边界在编译时确定
- 需要系统许可证验证部署权限
- 需要使用许可证验证使用权限
- 支持功能动态启用/停用

## 双重许可证机制

### 系统许可证 (System License)

**作用**: 控制系统部署和功能边界
**验证时机**: 安装时
**权限范围**: 全局系统级别

```json
{
  "license_type": "system",
  "edition": "business",
  "allowed_features": ["feature1", "feature2", ...],
  "max_tenants": 10,
  "includes_default_tenant": true,
  "expires_at": "2024-12-31T23:59:59Z"
}
```

**验证逻辑**:
1. 检查许可证是否存在且有效
2. 验证版本是否匹配编译版本
3. 确定可启用的功能范围
4. 检查租户数量限制
5. 验证有效期

### 使用许可证 (Usage License)

**作用**: 控制租户功能使用权限
**验证时机**: 运行时
**权限范围**: 租户级别

```json
{
  "license_type": "usage",
  "tenant_domain": "example.com",
  "edition": "professional",
  "features": ["feature1", "feature2", ...],
  "system_license_id": "sys_001",
  "expires_at": "2024-12-31T23:59:59Z"
}
```

**验证逻辑**:
1. 检查系统许可证是否有效
2. 验证使用许可证是否存在且有效
3. 确认功能权限不超过系统许可证
4. 检查租户域名匹配
5. 验证有效期

## 功能控制流程

### 功能可用性检查
```go
func IsFeatureUsable(featureName string, tenantDomain string) bool {
    // 1. 编译边界检查
    if !isFeatureCompiledIn(featureName) {
        return false
    }
    
    // 2. 个人版直接检查基础功能
    if isPersonalEdition() {
        return isPersonalBaseFeature(featureName)
    }
    
    // 3. 安装配置检查
    if !isFeatureEnabledInInstallation(featureName) {
        return false
    }
    
    // 4. 系统许可证检查
    if !isSystemLicenseValid() {
        return isPersonalBaseFeature(featureName) // 降级到个人版
    }
    
    // 5. 使用许可证检查
    if !isUsageLicenseValid(tenantDomain, featureName) {
        return isPersonalBaseFeature(featureName) // 降级到个人版
    }
    
    return true
}
```

### 降级机制
```go
func HandleLicenseExpiration() {
    if !isSystemLicenseValid() {
        // 系统许可证过期，降级到个人版
        degradeToPersonalEdition()
        logDegradationEvent("System license expired")
        notifyAdministrators("System degraded to personal edition")
    }
    
    for tenant := range getAllTenants() {
        if !isUsageLicenseValid(tenant.Domain) {
            // 使用许可证过期，该租户降级到个人版
            degradeTenantToPersonalEdition(tenant.Domain)
            logDegradationEvent(fmt.Sprintf("Tenant %s degraded", tenant.Domain))
            notifyTenant(tenant.Domain, "License expired, degraded to personal edition")
        }
    }
}
```

## 安全机制

### 编译时安全
- 功能边界在编译时固化，运行时无法修改
- 版本差异通过构建标签控制
- 防止运行时篡改版本配置

### 许可证安全
- RSA 4096位数字签名
- 许可证内容防篡改
- 定期验证许可证有效性

### 降级安全
- 确保系统始终可用
- 个人版功能作为安全底线
- 优雅降级，不影响基础服务

## 实现要点

### 编译时实现
1. 根据构建标签确定版本
2. 读取配置文件（专业版/商业版）
3. 固化功能边界到二进制文件
4. 生成版本指纹和校验码

### 运行时实现
1. 加载和验证系统许可证
2. 加载和验证使用许可证
3. 实现三层功能检查
4. 处理许可证过期和降级

### 监控和日志
1. 记录许可证验证事件
2. 监控功能使用情况
3. 跟踪降级事件
4. 生成使用报告

## 部署指南

### 个人版部署
1. 下载个人版二进制文件
2. 直接运行，无需配置
3. 所有基础功能自动可用

### 专业版/商业版部署
1. 下载对应版本二进制文件
2. 安装系统许可证文件
3. 验证许可证并配置功能
4. 分配使用许可证给租户
5. 启动系统并验证功能

### 升级和维护
1. 定期检查许可证有效期
2. 及时更新即将过期的许可证
3. 监控系统降级事件
4. 备份许可证文件
