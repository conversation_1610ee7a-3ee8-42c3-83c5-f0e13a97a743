/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/contenttype/domain/contract/ContentItemRepository.go
 * @Description: Defines the repository interface for managing ContentItems.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import "gacms/internal/modules/contenttype/domain/model"

// QueryOptions provides a structured way to pass find options.
type QueryOptions struct {
	Page    int
	PerPage int
	// Future additions: SortBy, Filters, etc.
}

// ContentItemRepository defines the persistence contract for ContentItem entities.
type ContentItemRepository interface {
	Create(item *model.ContentItem) error
	Update(item *model.ContentItem) error
	Delete(id uint) error
	FindByID(id uint) (*model.ContentItem, error)
	FindBatchByIDs(ids []uint) ([]*model.ContentItem, error)
	FindAll(siteID uint, contentTypeID uint) ([]*model.ContentItem, error)
	FindAllByRelation(siteID uint, contentTypeID uint, relationFieldSlug string, relatedID uint) ([]*model.ContentItem, error)
} 