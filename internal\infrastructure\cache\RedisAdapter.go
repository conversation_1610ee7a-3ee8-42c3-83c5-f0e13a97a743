/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/infrastructure/cache/RedisCache.go
 * @Description: Provides a Redis-based cache implementation, the recommended solution for production.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package cache

import (
	"context"
	"encoding/json"
	"time"

	"github.com/redis/go-redis/v9"
)

// RedisAdapter is an adapter for the redis.Client to implement our Cache interface.
type RedisAdapter struct {
	client *redis.Client
}

// NewRedisAdapter creates a new RedisAdapter.
func NewRedisAdapter(client *redis.Client) *RedisAdapter {
	return &RedisAdapter{client: client}
}

// Set stores a value in Redis. It serializes the value to JSON.
func (r *RedisAdapter) Set(key string, value interface{}, ttl time.Duration) error {
	ctx := context.Background()
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}
	return r.client.Set(ctx, key, data, ttl).Err()
}

// Get retrieves a value from Redis. It deserializes the value from JSON.
// Note: The caller needs to type-assert the returned interface{}.
func (r *RedisAdapter) Get(key string) (interface{}, error) {
	ctx := context.Background()
	val, err := r.client.Get(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	var data interface{}
	err = json.Unmarshal([]byte(val), &data)
	if err != nil {
		return nil, err
	}
	return data, nil
}

// Delete removes a value from Redis.
func (r *RedisAdapter) Delete(key string) error {
	ctx := context.Background()
	return r.client.Del(ctx, key).Err()
}

// Has checks if a key exists in Redis.
func (r *RedisAdapter) Has(key string) bool {
	ctx := context.Background()
	return r.client.Exists(ctx, key).Val() > 0
} 