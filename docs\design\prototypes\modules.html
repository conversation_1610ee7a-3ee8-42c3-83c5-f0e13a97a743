<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 模块管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome CDN -->
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <!-- 核心CSS文件 -->
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <!-- Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
    <!-- 页面特定样式 -->
    <style>
        /* 通用样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        /* 模块状态样式 */
        .status-active {
            @apply text-green-400 font-medium;
        }
        
        .status-inactive {
            @apply text-gray-400 font-medium;
        }
        
        .status-error {
            @apply text-red-400 font-medium;
        }
        
        /* 模块卡片样式 */
        .module-card {
            @apply bg-gray-800/20 border border-gray-700 rounded-xl p-5 transition-all;
        }
        
        .module-card:hover {
            @apply border-blue-500/50 shadow-lg shadow-blue-500/10;
        }
        
        .module-icon {
            @apply w-12 h-12 flex items-center justify-center rounded-xl text-xl;
        }
        
        .module-actions button {
            @apply transition-colors;
        }
    </style>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10">
        <!-- 侧边栏内容将通过JavaScript从模板加载 -->
    </aside>
    
    <!-- 侧边栏遮罩层 -->
    <div id="sidebarOverlay" class="sidebar-overlay"></div>
    
    <!-- 主内容区 -->
    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <!-- 顶部导航栏 -->
        <header class="header sticky top-0 z-30">
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        
        <!-- 页面内容区 -->
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">模块管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="installModuleBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-plus text-white"></i>
                                </span>
                                安装模块
                            </span>
                        </button>
                        <button id="refreshModulesBtn" class="flex items-center justify-center bg-gradient-to-r from-green-500 to-green-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-green-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-sync-alt text-white"></i>
                                </span>
                                刷新缓存
                            </span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 模块筛选区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap gap-4">
                    <div class="flex-1 min-w-[200px]">
                        <label class="block text-sm font-medium text-gray-400 mb-2">分类</label>
                        <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2.5 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">全部分类</option>
                            <option value="core">核心模块</option>
                            <option value="content">内容模块</option>
                            <option value="user">用户模块</option>
                            <option value="system">系统模块</option>
                            <option value="extension">扩展模块</option>
                        </select>
                    </div>
                    <div class="flex-1 min-w-[200px]">
                        <label class="block text-sm font-medium text-gray-400 mb-2">状态</label>
                        <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2.5 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">全部状态</option>
                            <option value="active">已启用</option>
                            <option value="inactive">已禁用</option>
                            <option value="error">异常</option>
                        </select>
                    </div>
                    <div class="flex-1 min-w-[200px]">
                        <label class="block text-sm font-medium text-gray-400 mb-2">搜索</label>
                        <div class="relative">
                            <input type="text" placeholder="搜索模块名称..." class="w-full bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2.5 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                    </div>
                    <div class="flex items-end">
                        <button class="bg-gray-700 hover:bg-gray-600 text-white px-5 py-2.5 rounded-lg font-medium transition-all">
                            筛选
                        </button>
                    </div>
                </div>
            </div>

            <!-- 模块列表 -->
            <div class="grid grid-cols-1 p-6 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                <!-- 核心模块 -->
                <div class="module-card">
                    <div class="flex items-start">
                        <div class="module-icon bg-blue-900/30 text-blue-400 mr-4">
                            <i class="fas fa-cube"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between items-start">
                                <h3 class="font-bold text-white text-lg">核心系统</h3>
                                <span class="status-active">已启用</span>
                            </div>
                            <p class="text-gray-400 text-sm mt-1">系统核心功能模块，提供基础框架支持</p>
                            <div class="flex justify-between items-center mt-3">
                                <div class="text-xs text-gray-500">v1.0.0 | 系统</div>
                                <div class="module-actions flex space-x-2">
                                    <button class="text-gray-400 hover:text-gray-300 cursor-not-allowed" title="核心模块无法禁用" disabled>
                                        <i class="fas fa-power-off"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300" title="配置">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 内容管理模块 -->
                <div class="module-card">
                    <div class="flex items-start">
                        <div class="module-icon bg-green-900/30 text-green-400 mr-4">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between items-start">
                                <h3 class="font-bold text-white text-lg">内容管理</h3>
                                <span class="status-active">已启用</span>
                            </div>
                            <p class="text-gray-400 text-sm mt-1">文章、页面、分类等内容管理功能</p>
                            <div class="flex justify-between items-center mt-3">
                                <div class="text-xs text-gray-500">v1.0.0 | 系统</div>
                                <div class="module-actions flex space-x-2">
                                    <button class="text-yellow-400 hover:text-yellow-300" title="禁用">
                                        <i class="fas fa-power-off"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300" title="配置">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 用户管理模块 -->
                <div class="module-card">
                    <div class="flex items-start">
                        <div class="module-icon bg-purple-900/30 text-purple-400 mr-4">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between items-start">
                                <h3 class="font-bold text-white text-lg">用户管理</h3>
                                <span class="status-active">已启用</span>
                            </div>
                            <p class="text-gray-400 text-sm mt-1">用户、角色、权限等用户管理功能</p>
                            <div class="flex justify-between items-center mt-3">
                                <div class="text-xs text-gray-500">v1.0.0 | 系统</div>
                                <div class="module-actions flex space-x-2">
                                    <button class="text-yellow-400 hover:text-yellow-300" title="禁用">
                                        <i class="fas fa-power-off"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300" title="配置">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 媒体管理模块 -->
                <div class="module-card">
                    <div class="flex items-start">
                        <div class="module-icon bg-pink-900/30 text-pink-400 mr-4">
                            <i class="fas fa-images"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between items-start">
                                <h3 class="font-bold text-white text-lg">媒体管理</h3>
                                <span class="status-active">已启用</span>
                            </div>
                            <p class="text-gray-400 text-sm mt-1">图片、视频、文件等媒体资源管理</p>
                            <div class="flex justify-between items-center mt-3">
                                <div class="text-xs text-gray-500">v1.0.0 | 系统</div>
                                <div class="module-actions flex space-x-2">
                                    <button class="text-yellow-400 hover:text-yellow-300" title="禁用">
                                        <i class="fas fa-power-off"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300" title="配置">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 评论模块 -->
                <div class="module-card">
                    <div class="flex items-start">
                        <div class="module-icon bg-yellow-900/30 text-yellow-400 mr-4">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between items-start">
                                <h3 class="font-bold text-white text-lg">评论系统</h3>
                                <span class="status-inactive">已禁用</span>
                            </div>
                            <p class="text-gray-400 text-sm mt-1">文章评论、回复、审核等功能</p>
                            <div class="flex justify-between items-center mt-3">
                                <div class="text-xs text-gray-500">v1.0.0 | 系统</div>
                                <div class="module-actions flex space-x-2">
                                    <button class="text-green-400 hover:text-green-300" title="启用">
                                        <i class="fas fa-power-off"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300" title="配置">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- SEO模块 -->
                <div class="module-card">
                    <div class="flex items-start">
                        <div class="module-icon bg-cyan-900/30 text-cyan-400 mr-4">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between items-start">
                                <h3 class="font-bold text-white text-lg">SEO优化</h3>
                                <span class="status-active">已启用</span>
                            </div>
                            <p class="text-gray-400 text-sm mt-1">搜索引擎优化、站点地图、元标签管理</p>
                            <div class="flex justify-between items-center mt-3">
                                <div class="text-xs text-gray-500">v1.0.0 | 系统</div>
                                <div class="module-actions flex space-x-2">
                                    <button class="text-yellow-400 hover:text-yellow-300" title="禁用">
                                        <i class="fas fa-power-off"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300" title="配置">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 缓存模块 -->
                <div class="module-card">
                    <div class="flex items-start">
                        <div class="module-icon bg-indigo-900/30 text-indigo-400 mr-4">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between items-start">
                                <h3 class="font-bold text-white text-lg">缓存系统</h3>
                                <span class="status-active">已启用</span>
                            </div>
                            <p class="text-gray-400 text-sm mt-1">页面缓存、数据缓存、CDN集成</p>
                            <div class="flex justify-between items-center mt-3">
                                <div class="text-xs text-gray-500">v1.0.0 | 系统</div>
                                <div class="module-actions flex space-x-2">
                                    <button class="text-yellow-400 hover:text-yellow-300" title="禁用">
                                        <i class="fas fa-power-off"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300" title="配置">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 安全模块 -->
                <div class="module-card">
                    <div class="flex items-start">
                        <div class="module-icon bg-red-900/30 text-red-400 mr-4">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between items-start">
                                <h3 class="font-bold text-white text-lg">安全防护</h3>
                                <span class="status-error">异常</span>
                            </div>
                            <p class="text-gray-400 text-sm mt-1">防火墙、验证码、登录保护、防SQL注入</p>
                            <div class="flex justify-between items-center mt-3">
                                <div class="text-xs text-gray-500">v1.0.0 | 系统</div>
                                <div class="module-actions flex space-x-2">
                                    <button class="text-blue-400 hover:text-blue-300" title="修复">
                                        <i class="fas fa-wrench"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300" title="配置">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 数据统计模块 -->
                <div class="module-card">
                    <div class="flex items-start">
                        <div class="module-icon bg-green-900/30 text-green-400 mr-4">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between items-start">
                                <h3 class="font-bold text-white text-lg">数据统计</h3>
                                <span class="status-active">已启用</span>
                            </div>
                            <p class="text-gray-400 text-sm mt-1">访问统计、内容分析、用户行为分析</p>
                            <div class="flex justify-between items-center mt-3">
                                <div class="text-xs text-gray-500">v1.0.0 | 系统</div>
                                <div class="module-actions flex space-x-2">
                                    <button class="text-yellow-400 hover:text-yellow-300" title="禁用">
                                        <i class="fas fa-power-off"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300" title="配置">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 分页 -->
            <div class="flex justify-between items-center mb-6">
                <div class="text-sm text-gray-400">
                    显示 1 到 9，共 15 个模块
                </div>
                <div class="flex space-x-1">
                    <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="px-3 py-1 rounded bg-blue-600 text-white">
                        1
                    </button>
                    <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">
                        2
                    </button>
                    <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>
    
    <!-- 安装模块模态框 -->
    <div id="installModuleModal" class="fixed inset-0 z-50 hidden">
        <div class="absolute inset-0 bg-black/50"></div>
        <div class="relative z-10 max-w-lg w-full mx-auto mt-20 bg-gray-800 rounded-xl shadow-xl">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-white">安装模块</h3>
                    <button id="closeInstallModuleModal" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-400 mb-2">安装方式</label>
                        <div class="flex space-x-4">
                            <label class="inline-flex items-center">
                                <input type="radio" name="installType" value="upload" checked class="w-4 h-4 bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                                <span class="ml-2 text-gray-300">上传模块包</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="installType" value="market" class="w-4 h-4 bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                                <span class="ml-2 text-gray-300">从应用市场安装</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 上传模块包 -->
                    <div id="uploadModuleSection" class="mb-4">
                        <label class="block text-sm font-medium text-gray-400 mb-2">选择模块包文件</label>
                        <div class="flex items-center justify-center w-full">
                            <label class="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-gray-700 border-gray-600 hover:bg-gray-600">
                                <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                    <i class="fas fa-cloud-upload-alt text-2xl text-gray-400 mb-2"></i>
                                    <p class="mb-2 text-sm text-gray-400">点击或拖放文件到此处</p>
                                    <p class="text-xs text-gray-500">支持 .zip 格式模块包</p>
                                </div>
                                <input id="moduleFile" type="file" class="hidden" accept=".zip" />
                            </label>
                        </div>
                    </div>
                    
                    <!-- 从应用市场安装 -->
                    <div id="marketModuleSection" class="mb-4 hidden">
                        <label class="block text-sm font-medium text-gray-400 mb-2">搜索模块</label>
                        <div class="relative">
                            <input type="text" placeholder="输入模块名称搜索..." class="w-full bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2.5 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        <div class="text-center text-gray-400 text-sm mt-4">
                            <i class="fas fa-info-circle mr-1"></i> 请先搜索模块，然后从结果中选择
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" id="cancelInstallModule" class="px-5 py-2.5 bg-gray-700 hover:bg-gray-600 text-white rounded-lg">
                            取消
                        </button>
                        <button type="submit" class="px-5 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:shadow-lg hover:shadow-blue-500/30">
                            安装
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 核心JS文件 -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <!-- 页面特定脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面
            const installModuleBtn = document.getElementById('installModuleBtn');
            const installModuleModal = document.getElementById('installModuleModal');
            const closeInstallModuleModal = document.getElementById('closeInstallModuleModal');
            const cancelInstallModule = document.getElementById('cancelInstallModule');
            const refreshModulesBtn = document.getElementById('refreshModulesBtn');
            
            // 安装方式切换
            const installTypeRadios = document.querySelectorAll('input[name="installType"]');
            const uploadModuleSection = document.getElementById('uploadModuleSection');
            const marketModuleSection = document.getElementById('marketModuleSection');
            
            if (installTypeRadios.length > 0 && uploadModuleSection && marketModuleSection) {
                installTypeRadios.forEach(radio => {
                    radio.addEventListener('change', function() {
                        if (this.value === 'upload') {
                            uploadModuleSection.classList.remove('hidden');
                            marketModuleSection.classList.add('hidden');
                        } else if (this.value === 'market') {
                            uploadModuleSection.classList.add('hidden');
                            marketModuleSection.classList.remove('hidden');
                        }
                    });
                });
            }
            
            // 打开安装模块模态框
            if (installModuleBtn && installModuleModal) {
                installModuleBtn.addEventListener('click', function() {
                    installModuleModal.classList.remove('hidden');
                });
            }
            
            // 关闭安装模块模态框
            if (closeInstallModuleModal && installModuleModal) {
                closeInstallModuleModal.addEventListener('click', function() {
                    installModuleModal.classList.add('hidden');
                });
            }
            
            // 取消安装模块
            if (cancelInstallModule && installModuleModal) {
                cancelInstallModule.addEventListener('click', function() {
                    installModuleModal.classList.add('hidden');
                });
            }
            
            // 刷新模块缓存
            if (refreshModulesBtn) {
                refreshModulesBtn.addEventListener('click', function() {
                    // 显示加载动画
                    this.classList.add('animate-pulse');
                    this.querySelector('i').classList.add('fa-spin');
                    
                    // 模拟刷新操作
                    setTimeout(() => {
                        // 移除加载动画
                        this.classList.remove('animate-pulse');
                        this.querySelector('i').classList.remove('fa-spin');
                        
                        // 显示成功提示
                        alert('模块缓存已刷新');
                    }, 1500);
                });
            }
            
            // 模块卡片交互
            const moduleCards = document.querySelectorAll('.module-card');
            if (moduleCards.length > 0) {
                moduleCards.forEach(card => {
                    // 配置按钮点击事件
                    const configBtn = card.querySelector('.module-actions button:last-child');
                    if (configBtn) {
                        configBtn.addEventListener('click', function(e) {
                            e.stopPropagation();
                            const moduleName = card.querySelector('h3').textContent;
                            alert(`配置模块: ${moduleName}`);
                        });
                    }
                    
                    // 启用/禁用按钮点击事件
                    const toggleBtn = card.querySelector('.module-actions button:first-child');
                    if (toggleBtn && !toggleBtn.disabled) {
                        toggleBtn.addEventListener('click', function(e) {
                            e.stopPropagation();
                            const moduleName = card.querySelector('h3').textContent;
                            const statusEl = card.querySelector('.status-active, .status-inactive, .status-error');
                            
                            if (statusEl.classList.contains('status-active')) {
                                statusEl.textContent = '已禁用';
                                statusEl.classList.remove('status-active');
                                statusEl.classList.add('status-inactive');
                                this.innerHTML = '<i class="fas fa-power-off"></i>';
                                this.classList.remove('text-yellow-400', 'hover:text-yellow-300');
                                this.classList.add('text-green-400', 'hover:text-green-300');
                                this.title = '启用';
                                alert(`已禁用模块: ${moduleName}`);
                            } else if (statusEl.classList.contains('status-inactive')) {
                                statusEl.textContent = '已启用';
                                statusEl.classList.remove('status-inactive');
                                statusEl.classList.add('status-active');
                                this.innerHTML = '<i class="fas fa-power-off"></i>';
                                this.classList.remove('text-green-400', 'hover:text-green-300');
                                this.classList.add('text-yellow-400', 'hover:text-yellow-300');
                                this.title = '禁用';
                                alert(`已启用模块: ${moduleName}`);
                            }
                        });
                    }
                });
            }
        });
    </script>
</body>
</html>