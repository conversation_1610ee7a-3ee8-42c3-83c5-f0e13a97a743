<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

用户分析页面 - 用于分析网站用户数据和行为
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 用户分析</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <!-- 引入图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment"></script>
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .stat-card {
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-3px);
        }

        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="data_stats.html" class="hover:text-white">数据报告</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">用户分析</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">用户分析</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="exportBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <i class="fas fa-download mr-2"></i>
                            导出报告
                        </button>
                        <button class="flex items-center justify-center bg-gradient-to-r from-gray-600 to-gray-700 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg relative overflow-hidden action-button">
                            <i class="fas fa-cog mr-2"></i>
                            设置
                        </button>
                    </div>
                </div>
            </div>

            <!-- 日期筛选 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap gap-4 items-center">
                    <div class="flex items-center">
                        <span class="text-gray-400 mr-2">时间范围:</span>
                        <div class="relative">
                            <select id="dateRange" class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white appearance-none pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="today">今天</option>
                                <option value="yesterday">昨天</option>
                                <option value="7days" selected>最近7天</option>
                                <option value="30days">最近30天</option>
                                <option value="90days">最近90天</option>
                                <option value="custom">自定义</option>
                            </select>
                            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                                <i class="fas fa-chevron-down text-xs"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <span class="text-gray-400 mr-2">用户分组:</span>
                        <div class="relative">
                            <select id="userGroup" class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white appearance-none pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="all" selected>所有用户</option>
                                <option value="new">新用户</option>
                                <option value="returning">回访用户</option>
                                <option value="registered">注册用户</option>
                                <option value="guest">游客</option>
                            </select>
                            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                                <i class="fas fa-chevron-down text-xs"></i>
                            </div>
                        </div>
                    </div>

                    <div id="customDateRange" class="flex items-center space-x-2 hidden">
                        <input type="date" class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="text-gray-400">至</span>
                        <input type="date" class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div class="ml-auto">
                        <button id="refreshBtn" class="flex items-center justify-center bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-all">
                            <i class="fas fa-sync-alt mr-2"></i>
                            刷新数据
                        </button>
                    </div>
                </div>
            </div>

            <!-- 用户概览卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-6">
                <!-- 总访问用户 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 stat-card">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-users text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">总访问用户</div>
                            <div class="text-xl font-semibold text-white">8,743</div>
                            <div class="text-xs text-green-400 mt-0.5">
                                <i class="fas fa-arrow-up mr-1"></i>
                                12.4% 较上周
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 新增用户 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 stat-card">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-user-plus text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">新增用户</div>
                            <div class="text-xl font-semibold text-white">1,256</div>
                            <div class="text-xs text-green-400 mt-0.5">
                                <i class="fas fa-arrow-up mr-1"></i>
                                8.7% 较上周
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 活跃用户 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 stat-card">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-user-check text-purple-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">活跃用户</div>
                            <div class="text-xl font-semibold text-white">3,845</div>
                            <div class="text-xs text-red-400 mt-0.5">
                                <i class="fas fa-arrow-down mr-1"></i>
                                2.3% 较上周
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 平均会话时长 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 stat-card">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-clock text-yellow-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">平均会话时长</div>
                            <div class="text-xl font-semibold text-white">4分32秒</div>
                            <div class="text-xs text-green-400 mt-0.5">
                                <i class="fas fa-arrow-up mr-1"></i>
                                1.5% 较上周
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户趋势图表 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold text-white mb-4">用户趋势</h3>
                <div class="chart-container">
                    <canvas id="userTrendChart"></canvas>
                </div>
            </div>

            <!-- 用户地理分布和设备分析 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- 用户地理分布 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">用户地理分布</h3>
                    <div class="relative mb-4 h-[250px]">
                        <!-- 地图容器 - 这里需要特定的地图库 -->
                        <div id="userGeoMap" class="w-full h-full bg-gray-800/30 rounded-lg flex items-center justify-center">
                            <span class="text-gray-400">地图加载中...</span>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <h4 class="text-sm text-gray-400 mb-2">前5位地区</h4>
                            <ul class="space-y-2">
                                <li class="flex justify-between">
                                    <span>北京</span>
                                    <span>23.4%</span>
                                </li>
                                <li class="flex justify-between">
                                    <span>上海</span>
                                    <span>18.7%</span>
                                </li>
                                <li class="flex justify-between">
                                    <span>广州</span>
                                    <span>12.3%</span>
                                </li>
                                <li class="flex justify-between">
                                    <span>深圳</span>
                                    <span>10.5%</span>
                                </li>
                                <li class="flex justify-between">
                                    <span>杭州</span>
                                    <span>7.8%</span>
                                </li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="text-sm text-gray-400 mb-2">增长最快地区</h4>
                            <ul class="space-y-2">
                                <li class="flex justify-between">
                                    <span>成都</span>
                                    <span>+35.4%</span>
                                </li>
                                <li class="flex justify-between">
                                    <span>西安</span>
                                    <span>+28.7%</span>
                                </li>
                                <li class="flex justify-between">
                                    <span>重庆</span>
                                    <span>+22.1%</span>
                                </li>
                                <li class="flex justify-between">
                                    <span>武汉</span>
                                    <span>+18.5%</span>
                                </li>
                                <li class="flex justify-between">
                                    <span>南京</span>
                                    <span>+15.2%</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- 设备分析 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">设备分析</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 设备类型 -->
                        <div>
                            <h4 class="text-sm text-gray-400 mb-3">设备类型</h4>
                            <div class="chart-container" style="height: 180px">
                                <canvas id="deviceTypeChart"></canvas>
                            </div>
                            <div class="grid grid-cols-3 gap-2 mt-3">
                                <div class="flex items-center">
                                    <span class="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                                    <span class="text-xs text-gray-400">手机 (68%)</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="w-3 h-3 rounded-full bg-purple-500 mr-2"></span>
                                    <span class="text-xs text-gray-400">桌面 (26%)</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                                    <span class="text-xs text-gray-400">平板 (6%)</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 浏览器分布 -->
                        <div>
                            <h4 class="text-sm text-gray-400 mb-3">浏览器分布</h4>
                            <div class="chart-container" style="height: 180px">
                                <canvas id="browserChart"></canvas>
                            </div>
                            <div class="grid grid-cols-3 gap-2 mt-3">
                                <div class="flex items-center">
                                    <span class="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                                    <span class="text-xs text-gray-400">Chrome (52%)</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="w-3 h-3 rounded-full bg-purple-500 mr-2"></span>
                                    <span class="text-xs text-gray-400">Safari (31%)</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></span>
                                    <span class="text-xs text-gray-400">其他 (17%)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户行为分析 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold text-white mb-4">用户行为分析</h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- 访问频次分布 -->
                    <div class="bg-gray-800/20 rounded-lg p-5">
                        <h4 class="text-white mb-4">访问频次分布</h4>
                        <div class="chart-container" style="height: 220px;">
                            <canvas id="visitFrequencyChart"></canvas>
                        </div>
                    </div>
                    
                    <!-- 用户留存率 -->
                    <div class="bg-gray-800/20 rounded-lg p-5">
                        <h4 class="text-white mb-4">用户留存率</h4>
                        <div class="chart-container" style="height: 220px;">
                            <canvas id="retentionRateChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户分群分析 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold text-white mb-4">用户分群分析</h3>
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead class="text-gray-400 text-left">
                            <tr>
                                <th class="pb-4 px-2">用户分群</th>
                                <th class="pb-4 px-2">用户数量</th>
                                <th class="pb-4 px-2">占比</th>
                                <th class="pb-4 px-2">平均访问频次</th>
                                <th class="pb-4 px-2">平均停留时间</th>
                                <th class="pb-4 px-2">转化率</th>
                                <th class="pb-4 px-2">消费金额</th>
                            </tr>
                        </thead>
                        <tbody class="text-gray-300">
                            <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <span class="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                                        <span>高价值用户</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">846</td>
                                <td class="py-4 px-2">9.7%</td>
                                <td class="py-4 px-2">12.5 次/月</td>
                                <td class="py-4 px-2">8分32秒</td>
                                <td class="py-4 px-2">32.4%</td>
                                <td class="py-4 px-2">¥876.50</td>
                            </tr>
                            <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                                        <span>活跃用户</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">2,184</td>
                                <td class="py-4 px-2">25.0%</td>
                                <td class="py-4 px-2">8.4 次/月</td>
                                <td class="py-4 px-2">5分46秒</td>
                                <td class="py-4 px-2">18.7%</td>
                                <td class="py-4 px-2">¥324.80</td>
                            </tr>
                            <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <span class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></span>
                                        <span>普通用户</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">3,549</td>
                                <td class="py-4 px-2">40.6%</td>
                                <td class="py-4 px-2">3.2 次/月</td>
                                <td class="py-4 px-2">3分12秒</td>
                                <td class="py-4 px-2">8.3%</td>
                                <td class="py-4 px-2">¥126.30</td>
                            </tr>
                            <tr class="border-t border-gray-700 hover:bg-gray-800/20">
                                <td class="py-4 px-2">
                                    <div class="flex items-center">
                                        <span class="w-3 h-3 rounded-full bg-red-500 mr-2"></span>
                                        <span>低活跃用户</span>
                                    </div>
                                </td>
                                <td class="py-4 px-2">2,164</td>
                                <td class="py-4 px-2">24.7%</td>
                                <td class="py-4 px-2">1.1 次/月</td>
                                <td class="py-4 px-2">1分45秒</td>
                                <td class="py-4 px-2">2.1%</td>
                                <td class="py-4 px-2">¥28.50</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 页面底部版权信息 -->
            <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
                © 2025 GACMS 后台管理系统 - 版本 v1.0.0
            </footer>
        </div>
    </main>

    <!-- JavaScript 导入区域 -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <script>
        /**
         * @function initCharts
         * @description 初始化所有图表
         */
        function initCharts() {
            // 用户趋势图表
            const userTrendCtx = document.getElementById('userTrendChart').getContext('2d');
            const userTrendChart = new Chart(userTrendCtx, {
                type: 'line',
                data: {
                    labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                    datasets: [
                        {
                            label: '总用户数',
                            data: [5800, 6200, 6500, 6300, 6800, 7200, 7500],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.3,
                            fill: true
                        },
                        {
                            label: '活跃用户',
                            data: [3200, 3400, 3800, 3600, 3900, 4100, 3800],
                            borderColor: '#a855f7',
                            backgroundColor: 'rgba(168, 85, 247, 0.1)',
                            tension: 0.3,
                            fill: true
                        },
                        {
                            label: '新用户',
                            data: [480, 520, 580, 540, 620, 680, 720],
                            borderColor: '#22c55e',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            tension: 0.3,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                color: '#e0e0e0'
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#9CA3AF'
                            }
                        },
                        y: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#9CA3AF'
                            }
                        }
                    }
                }
            });

            // 设备类型饼图
            const deviceTypeCtx = document.getElementById('deviceTypeChart').getContext('2d');
            const deviceTypeChart = new Chart(deviceTypeCtx, {
                type: 'doughnut',
                data: {
                    labels: ['手机', '桌面', '平板'],
                    datasets: [{
                        data: [68, 26, 6],
                        backgroundColor: [
                            '#3b82f6',
                            '#a855f7',
                            '#22c55e'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    cutout: '70%'
                }
            });

            // 浏览器分布饼图
            const browserCtx = document.getElementById('browserChart').getContext('2d');
            const browserChart = new Chart(browserCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Chrome', 'Safari', '其他'],
                    datasets: [{
                        data: [52, 31, 17],
                        backgroundColor: [
                            '#3b82f6',
                            '#a855f7',
                            '#eab308'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    cutout: '70%'
                }
            });

            // 访问频次分布
            const visitFrequencyCtx = document.getElementById('visitFrequencyChart').getContext('2d');
            const visitFrequencyChart = new Chart(visitFrequencyCtx, {
                type: 'bar',
                data: {
                    labels: ['1次', '2-5次', '6-10次', '11-20次', '20次以上'],
                    datasets: [{
                        label: '用户数量',
                        data: [3245, 2856, 1540, 784, 318],
                        backgroundColor: '#3b82f6',
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#9CA3AF'
                            }
                        },
                        y: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#9CA3AF'
                            }
                        }
                    }
                }
            });

            // 用户留存率
            const retentionRateCtx = document.getElementById('retentionRateChart').getContext('2d');
            const retentionRateChart = new Chart(retentionRateCtx, {
                type: 'line',
                data: {
                    labels: ['1天后', '3天后', '7天后', '14天后', '30天后'],
                    datasets: [{
                        label: '用户留存率',
                        data: [85, 62, 48, 32, 24],
                        borderColor: '#22c55e',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#9CA3AF'
                            }
                        },
                        y: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#9CA3AF',
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                }
            });

            // 模拟地图加载
            setTimeout(() => {
                const mapContainer = document.getElementById('userGeoMap');
                if (mapContainer) {
                    mapContainer.innerHTML = '<img src="./assets/images/demo_map.png" class="w-full h-full object-cover rounded-lg" alt="用户地理分布地图">';
                }
            }, 1000);
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initCharts();

            // 日期范围选择器逻辑
            const dateRangeSelect = document.getElementById('dateRange');
            const customDateRange = document.getElementById('customDateRange');
            
            if (dateRangeSelect && customDateRange) {
                dateRangeSelect.addEventListener('change', function() {
                    if (this.value === 'custom') {
                        customDateRange.classList.remove('hidden');
                    } else {
                        customDateRange.classList.add('hidden');
                    }
                });
            }

            // 刷新按钮事件
            const refreshBtn = document.getElementById('refreshBtn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function() {
                    // 模拟刷新数据
                    const btn = this;
                    btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> 刷新中...';
                    btn.disabled = true;
                    
                    setTimeout(() => {
                        btn.innerHTML = '<i class="fas fa-sync-alt mr-2"></i> 刷新数据';
                        btn.disabled = false;
                        
                        // 可以在这里重新加载图表数据
                        initCharts();
                    }, 1500);
                });
            }

            // 导出按钮事件
            const exportBtn = document.getElementById('exportBtn');
            if (exportBtn) {
                exportBtn.addEventListener('click', function() {
                    alert('报告导出功能将在此实现');
                });
            }
        });
    </script>
</body>
</html>