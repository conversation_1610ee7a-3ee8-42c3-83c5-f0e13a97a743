/*
 * @Author: <PERSON><PERSON>eh <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/ConventionRouter.go
 * @Description: 约定式路由系统
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"fmt"
	"net/http"
	"reflect"
	"strings"
	"sync"

	"gacms/internal/infrastructure/database"
	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// ControllerMetadata 控制器元数据（多租户支持）
type ControllerMetadata struct {
	Name       string
	Type       reflect.Type
	Instance   interface{}
	ModuleName string
	Routes     []RouteMetadata
	// 多租户支持
	SiteID     uint   `json:"site_id,omitempty"`     // 租户ID，0表示全局控制器
	IsGlobal   bool   `json:"is_global"`             // 是否为全局控制器
	Scope      string `json:"scope,omitempty"`       // 控制器作用域：global/tenant/site
}

// RouteMetadata 路由元数据（多租户支持）
type RouteMetadata struct {
	Pattern    string
	Method     string
	Action     string
	Handler    reflect.Method
	Middleware []string
	// 多租户支持
	SiteID     uint   `json:"site_id,omitempty"`     // 租户ID，0表示全局路由
	IsGlobal   bool   `json:"is_global"`             // 是否为全局路由
	Scope      string `json:"scope,omitempty"`       // 路由作用域：global/tenant/site
}

// ConventionRouter 约定式路由器（多租户支持）
type ConventionRouter struct {
	// 全局控制器：所有租户共享
	globalControllers map[string]*ControllerMetadata
	// 租户控制器：按站点ID分组
	tenantControllers map[uint]map[string]*ControllerMetadata
	// 全局路由：所有租户共享
	globalRoutes      map[string]*RouteMetadata
	// 租户路由：按站点ID分组
	tenantRoutes      map[uint]map[string]*RouteMetadata
	mu                sync.RWMutex
	logger            *zap.Logger
	discovery         *ModuleDiscovery
}

// ConventionRouterParams 约定式路由器参数
type ConventionRouterParams struct {
	fx.In

	Logger    *zap.Logger
	Discovery *ModuleDiscovery
}

// NewConventionRouter 创建约定式路由器（多租户支持）
func NewConventionRouter(params ConventionRouterParams) *ConventionRouter {
	router := &ConventionRouter{
		globalControllers: make(map[string]*ControllerMetadata),
		tenantControllers: make(map[uint]map[string]*ControllerMetadata),
		globalRoutes:      make(map[string]*RouteMetadata),
		tenantRoutes:      make(map[uint]map[string]*RouteMetadata),
		logger:            params.Logger,
		discovery:         params.Discovery,
	}

	// 监听模块变化，自动发现新的控制器
	params.Discovery.AddChangeCallback(router.handleModuleChange)

	return router
}

// RegisterController 注册控制器（向后兼容，注册为全局控制器）
func (r *ConventionRouter) RegisterController(moduleName string, controller interface{}) error {
	return r.RegisterGlobalController(moduleName, controller)
}

// RegisterGlobalController 注册全局控制器
func (r *ConventionRouter) RegisterGlobalController(moduleName string, controller interface{}) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	controllerType := reflect.TypeOf(controller)
	if controllerType.Kind() == reflect.Ptr {
		controllerType = controllerType.Elem()
	}

	controllerName := controllerType.Name()

	// 创建控制器元数据
	metadata := &ControllerMetadata{
		Name:       controllerName,
		Type:       controllerType,
		Instance:   controller,
		ModuleName: moduleName,
		Routes:     make([]RouteMetadata, 0),
		SiteID:     0,
		IsGlobal:   true,
		Scope:      "global",
	}

	// 分析控制器方法，生成约定式路由
	r.analyzeController(metadata)

	r.globalControllers[controllerName] = metadata

	r.logger.Info("Global controller registered",
		zap.String("module", moduleName),
		zap.String("controller", controllerName),
		zap.Int("routes", len(metadata.Routes)),
	)

	return nil
}

// RegisterTenantController 注册租户控制器
func (r *ConventionRouter) RegisterTenantController(siteID uint, moduleName string, controller interface{}) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	controllerType := reflect.TypeOf(controller)
	if controllerType.Kind() == reflect.Ptr {
		controllerType = controllerType.Elem()
	}

	controllerName := controllerType.Name()

	// 创建控制器元数据
	metadata := &ControllerMetadata{
		Name:       controllerName,
		Type:       controllerType,
		Instance:   controller,
		ModuleName: moduleName,
		Routes:     make([]RouteMetadata, 0),
		SiteID:     siteID,
		IsGlobal:   false,
		Scope:      "site",
	}

	// 分析控制器方法，生成约定式路由
	r.analyzeController(metadata)

	// 确保租户控制器映射存在
	if r.tenantControllers[siteID] == nil {
		r.tenantControllers[siteID] = make(map[string]*ControllerMetadata)
	}

	r.tenantControllers[siteID][controllerName] = metadata

	r.logger.Info("Tenant controller registered",
		zap.Uint("site_id", siteID),
		zap.String("module", moduleName),
		zap.String("controller", controllerName),
		zap.Int("routes", len(metadata.Routes)),
	)

	return nil
}

// analyzeController 分析控制器，生成约定式路由
func (r *ConventionRouter) analyzeController(metadata *ControllerMetadata) {
	controllerType := metadata.Type
	controllerName := strings.ToLower(metadata.Name)
	
	// 移除Controller后缀
	if strings.HasSuffix(controllerName, "controller") {
		controllerName = controllerName[:len(controllerName)-10]
	}

	// 遍历所有公共方法
	for i := 0; i < controllerType.NumMethod(); i++ {
		method := controllerType.Method(i)

		// 跳过非公共方法
		if !method.IsExported() {
			continue
		}

		// 分析方法名，生成路由（传递租户信息）
		routes := r.generateRoutesFromMethod(metadata.ModuleName, controllerName, method, metadata.SiteID, metadata.IsGlobal)
		metadata.Routes = append(metadata.Routes, routes...)
	}
}

// generateRoutesFromMethod 从方法生成路由（多租户支持）
func (r *ConventionRouter) generateRoutesFromMethod(moduleName, controllerName string, method reflect.Method, siteID uint, isGlobal bool) []RouteMetadata {
	methodName := method.Name
	routes := make([]RouteMetadata, 0)

	// 约定式路由规则：
	// Index() -> GET /module/controller
	// Show() -> GET /module/controller/{id}
	// Create() -> POST /module/controller
	// Store() -> POST /module/controller
	// Edit() -> GET /module/controller/{id}/edit
	// Update() -> PUT /module/controller/{id}
	// Delete() -> DELETE /module/controller/{id}
	// 其他方法 -> GET /module/controller/method

	basePattern := fmt.Sprintf("/%s/%s", moduleName, controllerName)

	switch strings.ToLower(methodName) {
	case "index":
		routes = append(routes, RouteMetadata{
			Pattern: basePattern,
			Method:  "GET",
			Action:  methodName,
			Handler: method,
		})
		
	case "show":
		routes = append(routes, RouteMetadata{
			Pattern: basePattern + "/{id}",
			Method:  "GET",
			Action:  methodName,
			Handler: method,
		})
		
	case "create":
		routes = append(routes, RouteMetadata{
			Pattern: basePattern + "/create",
			Method:  "GET",
			Action:  methodName,
			Handler: method,
		})
		
	case "store":
		routes = append(routes, RouteMetadata{
			Pattern: basePattern,
			Method:  "POST",
			Action:  methodName,
			Handler: method,
		})
		
	case "edit":
		routes = append(routes, RouteMetadata{
			Pattern: basePattern + "/{id}/edit",
			Method:  "GET",
			Action:  methodName,
			Handler: method,
		})
		
	case "update":
		routes = append(routes, RouteMetadata{
			Pattern: basePattern + "/{id}",
			Method:  "PUT",
			Action:  methodName,
			Handler: method,
		})
		
	case "delete", "destroy":
		routes = append(routes, RouteMetadata{
			Pattern: basePattern + "/{id}",
			Method:  "DELETE",
			Action:  methodName,
			Handler: method,
		})
		
	default:
		// 其他方法生成GET路由
		routes = append(routes, RouteMetadata{
			Pattern: basePattern + "/" + strings.ToLower(methodName),
			Method:  "GET",
			Action:  methodName,
			Handler: method,
		})
	}

	return routes
}

// getScopeFromSiteID 根据站点ID获取作用域
func (r *ConventionRouter) getScopeFromSiteID(siteID uint, isGlobal bool) string {
	if isGlobal {
		return "global"
	} else if siteID > 0 {
		return "site"
	} else {
		return "tenant"
	}
}

// ServeHTTP 处理HTTP请求（多租户支持）
func (r *ConventionRouter) ServeHTTP(w http.ResponseWriter, req *http.Request) {
	// 从请求上下文获取租户信息
	ctx := req.Context()
	siteID, hasSiteID := database.SiteIDFrom(ctx)

	route, params, err := r.findRouteForSite(req.Method, req.URL.Path, siteID, hasSiteID)
	if err != nil {
		r.logger.Debug("Route not found",
			zap.String("method", req.Method),
			zap.String("path", req.URL.Path),
			zap.Uint("site_id", siteID),
			zap.Bool("has_site_id", hasSiteID),
			zap.Error(err),
		)
		http.NotFound(w, req)
		return
	}

	// 调用控制器方法
	err = r.invokeController(route, params, w, req)
	if err != nil {
		r.logger.Error("Controller invocation failed",
			zap.String("route", route.Pattern),
			zap.String("action", route.Action),
			zap.Uint("site_id", siteID),
			zap.Error(err),
		)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
}

// findRoute 查找匹配的路由（向后兼容，查找全局路由）
func (r *ConventionRouter) findRoute(method, path string) (*RouteMetadata, map[string]string, error) {
	return r.findRouteForSite(method, path, 0, false)
}

// findRouteForSite 为指定站点查找匹配的路由
func (r *ConventionRouter) findRouteForSite(method, path string, siteID uint, hasSiteID bool) (*RouteMetadata, map[string]string, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	// 优先查找租户特定路由
	if hasSiteID && siteID > 0 {
		if tenantControllers, exists := r.tenantControllers[siteID]; exists {
			for _, controller := range tenantControllers {
				for _, route := range controller.Routes {
					if route.Method == method {
						if params, matched := r.matchPattern(route.Pattern, path); matched {
							return &route, params, nil
						}
					}
				}
			}
		}
	}

	// 查找全局路由
	for _, controller := range r.globalControllers {
		for _, route := range controller.Routes {
			if route.Method == method {
				if params, matched := r.matchPattern(route.Pattern, path); matched {
					return &route, params, nil
				}
			}
		}
	}

	return nil, nil, fmt.Errorf("no route found for %s %s (site_id: %d)", method, path, siteID)
}

// matchPattern 匹配路由模式
func (r *ConventionRouter) matchPattern(pattern, path string) (map[string]string, bool) {
	params := make(map[string]string)
	
	// 简单的模式匹配实现
	// TODO: 实现更复杂的路由匹配，支持参数提取
	
	// 精确匹配
	if pattern == path {
		return params, true
	}
	
	// 参数匹配（简化版）
	patternParts := strings.Split(pattern, "/")
	pathParts := strings.Split(path, "/")
	
	if len(patternParts) != len(pathParts) {
		return params, false
	}
	
	for i, part := range patternParts {
		if strings.HasPrefix(part, "{") && strings.HasSuffix(part, "}") {
			// 参数部分
			paramName := part[1 : len(part)-1]
			params[paramName] = pathParts[i]
		} else if part != pathParts[i] {
			// 不匹配
			return params, false
		}
	}
	
	return params, true
}

// invokeController 调用控制器方法（多租户支持）
func (r *ConventionRouter) invokeController(route *RouteMetadata, params map[string]string, w http.ResponseWriter, req *http.Request) error {
	// 查找控制器（多租户感知）
	controller := r.findControllerForRoute(route)

	if controller == nil {
		return fmt.Errorf("controller not found for route %s", route.Pattern)
	}

	// 使用反射调用方法
	controllerValue := reflect.ValueOf(controller.Instance)
	method := controllerValue.MethodByName(route.Action)
	
	if !method.IsValid() {
		return fmt.Errorf("method %s not found in controller", route.Action)
	}

	// 准备参数
	args := r.prepareMethodArgs(method.Type(), params, w, req)
	
	// 调用方法
	results := method.Call(args)
	
	// 处理返回值
	return r.handleMethodResults(results, w)
}

// prepareMethodArgs 准备方法参数
func (r *ConventionRouter) prepareMethodArgs(methodType reflect.Type, params map[string]string, w http.ResponseWriter, req *http.Request) []reflect.Value {
	args := make([]reflect.Value, 0)
	
	// 简化的参数注入
	// TODO: 实现更复杂的参数绑定逻辑
	
	for i := 0; i < methodType.NumIn(); i++ {
		paramType := methodType.In(i)
		
		switch paramType {
		case reflect.TypeOf((*http.ResponseWriter)(nil)).Elem():
			args = append(args, reflect.ValueOf(w))
		case reflect.TypeOf((*http.Request)(nil)):
			args = append(args, reflect.ValueOf(req))
		default:
			// 其他类型的参数处理
			args = append(args, reflect.Zero(paramType))
		}
	}
	
	return args
}

// handleMethodResults 处理方法返回值
func (r *ConventionRouter) handleMethodResults(results []reflect.Value, w http.ResponseWriter) error {
	// 简化的返回值处理
	// TODO: 实现更复杂的返回值处理逻辑
	
	for _, result := range results {
		if result.Type().Implements(reflect.TypeOf((*error)(nil)).Elem()) {
			if !result.IsNil() {
				return result.Interface().(error)
			}
		}
	}
	
	return nil
}

// findControllerForRoute 查找路由对应的控制器
func (r *ConventionRouter) findControllerForRoute(route *RouteMetadata) *ControllerMetadata {
	// 根据路由的租户信息查找对应的控制器
	if route.IsGlobal {
		// 查找全局控制器
		for _, ctrl := range r.globalControllers {
			for _, ctrlRoute := range ctrl.Routes {
				if ctrlRoute.Pattern == route.Pattern && ctrlRoute.Action == route.Action {
					return ctrl
				}
			}
		}
	} else {
		// 查找租户特定控制器
		if tenantControllers, exists := r.tenantControllers[route.SiteID]; exists {
			for _, ctrl := range tenantControllers {
				for _, ctrlRoute := range ctrl.Routes {
					if ctrlRoute.Pattern == route.Pattern && ctrlRoute.Action == route.Action {
						return ctrl
					}
				}
			}
		}
	}

	return nil
}

// handleModuleChange 处理模块变化
func (r *ConventionRouter) handleModuleChange(event ModuleChangeEvent) error {
	switch event.Type {
	case ModuleActivated:
		// 模块激活时，扫描并注册其控制器
		r.logger.Info("Scanning controllers for activated module", zap.String("module", event.ModuleName))
		return r.scanModuleControllers(event.ModuleName)

	case ModuleDeactivated, ModuleDisabled:
		// 模块停用或禁用时，移除其控制器
		r.logger.Info("Removing controllers for deactivated module", zap.String("module", event.ModuleName))
		r.removeModuleControllers(event.ModuleName)

	case ModuleError:
		// 模块错误时，移除其控制器
		r.logger.Warn("Removing controllers for error module",
			zap.String("module", event.ModuleName),
			zap.String("error", event.Metadata.ErrorMsg),
		)
		r.removeModuleControllers(event.ModuleName)

	case ModuleUpdated:
		// 模块更新时，重新扫描控制器
		r.logger.Info("Rescanning controllers for updated module", zap.String("module", event.ModuleName))
		r.removeModuleControllers(event.ModuleName)
		return r.scanModuleControllers(event.ModuleName)

	case ModuleDiscovered, ModuleEnabled:
		// 模块发现或启用时，不需要立即处理路由
		// 只有激活时才注册路由
		r.logger.Debug("Module state changed",
			zap.String("module", event.ModuleName),
			zap.String("type", r.changeTypeString(event.Type)),
		)
	}

	return nil
}

// scanModuleControllers 扫描模块的控制器
func (r *ConventionRouter) scanModuleControllers(moduleName string) error {
	// TODO: 实现实际的控制器扫描逻辑
	// 这里应该：
	// 1. 扫描模块目录下的控制器文件
	// 2. 使用反射分析控制器结构
	// 3. 自动注册控制器到路由系统

	r.logger.Info("Controller scanning not yet implemented", zap.String("module", moduleName))
	return nil
}

// changeTypeString 变化类型转字符串
func (r *ConventionRouter) changeTypeString(changeType ModuleChangeType) string {
	switch changeType {
	case ModuleDiscovered:
		return "discovered"
	case ModuleEnabled:
		return "enabled"
	case ModuleDisabled:
		return "disabled"
	case ModuleActivated:
		return "activated"
	case ModuleDeactivated:
		return "deactivated"
	case ModuleError:
		return "error"
	case ModuleUpdated:
		return "updated"
	default:
		return "unknown"
	}
}

// removeModuleControllers 移除模块的所有控制器（多租户支持）
func (r *ConventionRouter) removeModuleControllers(moduleName string) {
	r.mu.Lock()
	defer r.mu.Unlock()

	// 移除全局控制器
	globalToRemove := make([]string, 0)
	for name, controller := range r.globalControllers {
		if controller.ModuleName == moduleName {
			globalToRemove = append(globalToRemove, name)
		}
	}

	for _, name := range globalToRemove {
		delete(r.globalControllers, name)
		r.logger.Info("Global controller removed",
			zap.String("module", moduleName),
			zap.String("controller", name),
		)
	}

	// 移除租户控制器
	for siteID, tenantControllers := range r.tenantControllers {
		tenantToRemove := make([]string, 0)
		for name, controller := range tenantControllers {
			if controller.ModuleName == moduleName {
				tenantToRemove = append(tenantToRemove, name)
			}
		}

		for _, name := range tenantToRemove {
			delete(tenantControllers, name)
			r.logger.Info("Tenant controller removed",
				zap.Uint("site_id", siteID),
				zap.String("module", moduleName),
				zap.String("controller", name),
			)
		}

		// 如果租户控制器映射为空，删除整个映射
		if len(tenantControllers) == 0 {
			delete(r.tenantControllers, siteID)
		}
	}
}
