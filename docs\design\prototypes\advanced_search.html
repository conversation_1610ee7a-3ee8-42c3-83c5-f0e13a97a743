<!--
© 2025 Clion Nieh. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 高级搜索</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <style>
        .search-card {
            background-color: #1F2937;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.05);
            transition: all 0.3s ease;
        }
        
        .search-card:hover {
            border-color: rgba(59, 130, 246, 0.3);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }
        
        .badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .badge-article {
            background-color: rgba(59, 130, 246, 0.2);
            color: rgb(59, 130, 246);
        }
        
        .badge-page {
            background-color: rgba(16, 185, 129, 0.2);
            color: rgb(16, 185, 129);
        }
        
        .badge-user {
            background-color: rgba(245, 158, 11, 0.2);
            color: rgb(245, 158, 11);
        }
        
        .badge-file {
            background-color: rgba(139, 92, 246, 0.2);
            color: rgb(139, 92, 246);
        }
        
        .badge-comment {
            background-color: rgba(236, 72, 153, 0.2);
            color: rgb(236, 72, 153);
        }
        
        .search-result-item {
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            padding: 16px 0;
            transition: all 0.2s ease;
        }
        
        .search-result-item:hover {
            background-color: rgba(55, 65, 81, 0.3);
        }
        
        .search-result-item:last-child {
            border-bottom: none;
        }
        
        .highlight {
            background-color: rgba(245, 158, 11, 0.2);
            padding: 0 2px;
            border-radius: 2px;
        }
        
        .date-range-picker {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .filter-tag {
            display: inline-flex;
            align-items: center;
            background-color: rgba(55, 65, 81, 0.7);
            color: #e5e7eb;
            padding: 4px 8px;
            border-radius: 16px;
            font-size: 0.875rem;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        
        .filter-tag-remove {
            margin-left: 6px;
            font-size: 0.75rem;
            color: #9ca3af;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .filter-tag-remove:hover {
            color: #f87171;
        }
        
        .view-switcher {
            display: flex;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .view-switcher-btn {
            padding: 6px 12px;
            background-color: rgba(55, 65, 81, 0.5);
            transition: all 0.2s ease;
            color: #9ca3af;
        }
        
        .view-switcher-btn:hover {
            background-color: rgba(55, 65, 81, 0.8);
            color: #e5e7eb;
        }
        
        .view-switcher-btn.active {
            background-color: #3B82F6;
            color: white;
        }
    </style>
</head>
<body class="bg-gray-900 text-gray-100 flex h-screen">
    <!-- 侧边栏 -->
    <aside class="w-64 bg-gray-800 h-screen fixed left-0 top-0 overflow-y-auto transition-all duration-300 ease-in-out z-10" id="sidebar">
        <div class="flex items-center justify-between p-4 border-b border-gray-700">
            <a href="#" class="flex items-center space-x-2">
                <img src="./assets/images/logo.svg" alt="GACMS Logo" class="h-8 w-8">
                <span class="text-xl font-bold text-white">GACMS</span>
            </a>
            <button id="sidebarToggle" class="lg:hidden focus:outline-none">
                <i class="fas fa-times text-gray-400 hover:text-white"></i>
            </button>
        </div>
        <div class="px-4 py-2">
            <div class="flex items-center space-x-3 mb-4 mt-4">
                <img src="./assets/images/avatar.jpg" alt="User Avatar" class="w-10 h-10 rounded-full border-2 border-blue-500">
                <div>
                    <h3 class="font-medium text-sm text-gray-200">管理员</h3>
                    <p class="text-xs text-gray-400">超级管理员</p>
                </div>
            </div>
        </div>
        
        <nav class="mt-4">
            <ul class="space-y-1 px-4">
                <li>
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white rounded-lg transition-colors">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-gray-400"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>
                
                <li class="menu-section">
                    <h3 class="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">内容管理</h3>
                </li>
                
                <li>
                    <a href="#" class="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white rounded-lg transition-colors">
                        <i class="fas fa-newspaper w-5 h-5 text-gray-400"></i>
                        <span class="ml-3">文章管理</span>
                    </a>
                </li>
                
                <li>
                    <a href="#" class="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white rounded-lg transition-colors">
                        <i class="fas fa-folder w-5 h-5 text-gray-400"></i>
                        <span class="ml-3">栏目管理</span>
                    </a>
                </li>
                
                <li>
                    <a href="#" class="flex items-center px-4 py-3 bg-gray-700 text-white rounded-lg transition-colors">
                        <i class="fas fa-search w-5 h-5 text-blue-400"></i>
                        <span class="ml-3">高级搜索</span>
                    </a>
                </li>
                
                <li class="menu-section">
                    <h3 class="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">系统设置</h3>
                </li>
                
                <li>
                    <a href="#" class="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white rounded-lg transition-colors">
                        <i class="fas fa-cog w-5 h-5 text-gray-400"></i>
                        <span class="ml-3">系统设置</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <div class="ml-64 flex-1 flex flex-col min-h-screen transition-all duration-300 ease-in-out" id="main-content">
        <!-- 顶部导航栏 -->
        <header class="bg-gray-800 border-b border-gray-700 sticky top-0 z-10">
            <div class="flex items-center justify-between px-6 h-16">
                <!-- 左侧菜单切换和面包屑 -->
                <div class="flex items-center">
                    <button id="menuToggle" class="text-gray-400 hover:text-white focus:outline-none lg:hidden">
                        <i class="fas fa-bars"></i>
                    </button>
                    
                    <div class="hidden lg:flex ml-4 items-center text-sm text-gray-400">
                        <a href="dashboard.html" class="hover:text-white">仪表盘</a>
                        <span class="mx-2">/</span>
                        <a href="#" class="text-white">高级搜索</a>
                    </div>
                </div>
                
                <!-- 右侧功能区 -->
                <div class="flex items-center space-x-4">
                    <!-- 通知 -->
                    <div class="relative">
                        <button class="text-gray-400 hover:text-white focus:outline-none">
                            <div class="relative">
                                <i class="fas fa-bell text-xl"></i>
                                <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">3</span>
                            </div>
                        </button>
                    </div>
                    
                    <!-- 用户菜单 -->
                    <div class="relative">
                        <button class="flex items-center space-x-2 text-gray-400 hover:text-white focus:outline-none">
                            <img src="./assets/images/avatar.jpg" alt="User Avatar" class="w-8 h-8 rounded-full border border-gray-600">
                            <span class="hidden lg:inline-block">管理员</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- 页面内容 -->
        <main class="flex-1 p-6 bg-gray-900 overflow-y-auto">
            <div class="max-w-7xl mx-auto">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                    <div>
                        <h2 class="text-2xl font-bold text-white">高级搜索</h2>
                        <p class="text-gray-400 mt-1">强大的多条件搜索工具，查找系统内的任何内容</p>
                    </div>
                </div>
                
                <!-- 搜索表单 -->
                <div class="search-card p-6 mb-6">
                    <div class="relative mb-6">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-4">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text" id="mainSearchInput" value="系统管理" class="w-full bg-gray-800 border border-gray-700 rounded-lg pl-12 pr-12 py-3 text-gray-300 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="输入关键词搜索...">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                            <button class="text-gray-400 hover:text-white focus:outline-none" id="clearSearchBtn">
                                <i class="fas fa-times-circle"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <div>
                            <label class="block text-gray-300 text-sm mb-2">内容类型</label>
                            <select class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="all">全部类型</option>
                                <option value="article">文章</option>
                                <option value="page">页面</option>
                                <option value="user">用户</option>
                                <option value="file">文件</option>
                                <option value="comment">评论</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-gray-300 text-sm mb-2">日期范围</label>
                            <div class="date-range-picker">
                                <input type="date" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500" value="2025-01-01">
                                <span class="text-gray-400">至</span>
                                <input type="date" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500" value="2025-05-30">
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-gray-300 text-sm mb-2">状态</label>
                            <select class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="all">所有状态</option>
                                <option value="published">已发布</option>
                                <option value="draft">草稿</option>
                                <option value="pending">待审核</option>
                                <option value="trash">回收站</option>
                            </select>
                        </div>
                    </div>
                    
                    <div id="advancedOptions" class="border-t border-gray-700 pt-6 mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label class="block text-gray-300 text-sm mb-2">作者</label>
                                <select class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">全部作者</option>
                                    <option value="1">管理员</option>
                                    <option value="2">编辑</option>
                                    <option value="3">张三</option>
                                    <option value="4">李四</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 text-sm mb-2">分类</label>
                                <select class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">全部分类</option>
                                    <option value="1">新闻动态</option>
                                    <option value="2">技术文章</option>
                                    <option value="3">产品介绍</option>
                                    <option value="4">使用教程</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-gray-300 text-sm mb-2">标签</label>
                                <input type="text" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="输入标签，多个标签用逗号分隔">
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex flex-wrap items-center justify-between">
                        <div>
                            <button id="toggleAdvancedBtn" class="text-blue-400 hover:text-blue-300 focus:outline-none text-sm">
                                <i class="fas fa-chevron-up mr-1"></i> 收起高级选项
                            </button>
                        </div>
                        
                        <div class="flex space-x-3">
                            <button id="resetBtn" class="bg-gray-700 hover:bg-gray-600 text-gray-300 px-4 py-2 rounded-lg transition-colors">
                                重置
                            </button>
                            <button id="searchBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                                搜索
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 当前筛选条件 -->
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-gray-400 mb-3">当前筛选条件：</h3>
                    
                    <div>
                        <span class="filter-tag">
                            关键词: 系统管理
                            <span class="filter-tag-remove"><i class="fas fa-times"></i></span>
                        </span>
                        
                        <span class="filter-tag">
                            日期: 2025-01-01 至 2025-05-30
                            <span class="filter-tag-remove"><i class="fas fa-times"></i></span>
                        </span>
                    </div>
                </div>
                
                <!-- 搜索结果 -->
                <div class="search-card p-6 mb-6">
                    <div class="flex justify-between items-center mb-6">
                        <div>
                            <h3 class="text-lg font-semibold text-white">搜索结果</h3>
                            <p class="text-sm text-gray-400 mt-1">找到 26 个结果，用时 0.12 秒</p>
                        </div>
                        
                        <div class="flex items-center space-x-4">
                            <div>
                                <select class="bg-gray-800 border border-gray-700 rounded-lg px-3 py-1 text-sm text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option>按相关度排序</option>
                                    <option>按日期排序</option>
                                    <option>按标题排序</option>
                                </select>
                            </div>
                            
                            <div class="view-switcher">
                                <button class="view-switcher-btn active" data-view="list">
                                    <i class="fas fa-list"></i>
                                </button>
                                <button class="view-switcher-btn" data-view="grid">
                                    <i class="fas fa-th-large"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div id="listView">
                        <div class="search-result-item">
                            <div class="flex justify-between items-start">
                                <div>
                                    <div class="flex items-center mb-1">
                                        <span class="badge badge-article mr-2">文章</span>
                                        <a href="#" class="text-lg font-medium text-blue-400 hover:text-blue-300">CMS<span class="highlight">系统管理</span>最佳实践指南</a>
                                    </div>
                                    <p class="text-sm text-gray-400 mb-2">发布于: 2025-05-15 | 作者: 管理员 | 分类: 技术文章</p>
                                    <p class="text-gray-300">本文将介绍CMS<span class="highlight">系统管理</span>的最佳实践，包括用户权限设置、内容审核流程、备份策略等方面的建议，帮助管理员更有效地维护CMS系统...</p>
                                </div>
                                <div class="ml-4">
                                    <a href="#" class="text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="search-result-item">
                            <div class="flex justify-between items-start">
                                <div>
                                    <div class="flex items-center mb-1">
                                        <span class="badge badge-page mr-2">页面</span>
                                        <a href="#" class="text-lg font-medium text-blue-400 hover:text-blue-300"><span class="highlight">系统管理</span>员培训课程</a>
                                    </div>
                                    <p class="text-sm text-gray-400 mb-2">发布于: 2025-04-10 | 作者: 管理员 | 分类: 培训资料</p>
                                    <p class="text-gray-300">GACMS提供专业的<span class="highlight">系统管理</span>员培训课程，帮助您的团队掌握CMS系统的管理技能，包括内容发布、用户管理、权限控制等核心功能...</p>
                                </div>
                                <div class="ml-4">
                                    <a href="#" class="text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="search-result-item">
                            <div class="flex justify-between items-start">
                                <div>
                                    <div class="flex items-center mb-1">
                                        <span class="badge badge-file mr-2">文件</span>
                                        <a href="#" class="text-lg font-medium text-blue-400 hover:text-blue-300">GACMS_<span class="highlight">系统管理</span>_手册.pdf</a>
                                    </div>
                                    <p class="text-sm text-gray-400 mb-2">上传于: 2025-03-22 | 大小: 2.4 MB | 类型: PDF文档</p>
                                    <p class="text-gray-300">这是GACMS的<span class="highlight">系统管理</span>手册，详细介绍了系统的各项管理功能、配置选项和最佳实践，是系统管理员必备的参考资料...</p>
                                </div>
                                <div class="ml-4">
                                    <a href="#" class="text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-download"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="search-result-item">
                            <div class="flex justify-between items-start">
                                <div>
                                    <div class="flex items-center mb-1">
                                        <span class="badge badge-user mr-2">用户</span>
                                        <a href="#" class="text-lg font-medium text-blue-400 hover:text-blue-300"><span class="highlight">系统管理</span>员</a>
                                    </div>
                                    <p class="text-sm text-gray-400 mb-2">注册于: 2025-01-01 | 角色: 超级管理员 | 状态: 活跃</p>
                                    <p class="text-gray-300"><span class="highlight">系统管理</span>员账户拥有最高权限，可以执行所有管理操作，包括用户管理、内容管理、系统配置等...</p>
                                </div>
                                <div class="ml-4">
                                    <a href="#" class="text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-user-edit"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="search-result-item">
                            <div class="flex justify-between items-start">
                                <div>
                                    <div class="flex items-center mb-1">
                                        <span class="badge badge-article mr-2">文章</span>
                                        <a href="#" class="text-lg font-medium text-blue-400 hover:text-blue-300">如何优化CMS<span class="highlight">系统管理</span>效率</a>
                                    </div>
                                    <p class="text-sm text-gray-400 mb-2">发布于: 2025-02-18 | 作者: 李四 | 分类: 使用教程</p>
                                    <p class="text-gray-300">本文介绍了提高CMS<span class="highlight">系统管理</span>效率的10个技巧，包括批量操作、快捷键使用、自动化任务配置等，帮助管理员节省大量时间...</p>
                                </div>
                                <div class="ml-4">
                                    <a href="#" class="text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="gridView" class="hidden grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- 网格视图内容在这里，隐藏状态 -->
                    </div>
                    
                    <div class="mt-6 flex justify-between items-center">
                        <div class="text-sm text-gray-400">
                            显示 1-5，共 26 个结果
                        </div>
                        
                        <div class="flex space-x-1">
                            <button class="bg-gray-700 text-gray-300 px-3 py-1 rounded-lg text-sm transition-colors">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="bg-blue-600 text-white px-3 py-1 rounded-lg text-sm transition-colors">1</button>
                            <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-1 rounded-lg text-sm transition-colors">2</button>
                            <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-1 rounded-lg text-sm transition-colors">3</button>
                            <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-1 rounded-lg text-sm transition-colors">4</button>
                            <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-1 rounded-lg text-sm transition-colors">5</button>
                            <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-1 rounded-lg text-sm transition-colors">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 保存的搜索 -->
                <div class="search-card p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-white">已保存的搜索</h3>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-lg text-sm transition-colors">
                            <i class="fas fa-save mr-1"></i> 保存当前搜索
                        </button>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-gray-800 rounded-lg overflow-hidden">
                            <thead>
                                <tr class="bg-gray-700">
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">名称</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">搜索条件</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">创建时间</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-700">
                                <tr class="hover:bg-gray-700 transition-colors">
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">最近更新的文章</td>
                                    <td class="px-4 py-3 text-sm text-gray-300">类型: 文章, 日期: 最近30天</td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">2025-05-20</td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-right">
                                        <button class="text-gray-400 hover:text-blue-400 transition-colors p-1">
                                            <i class="fas fa-search"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-400 transition-colors p-1">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                
                                <tr class="hover:bg-gray-700 transition-colors">
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">我的草稿</td>
                                    <td class="px-4 py-3 text-sm text-gray-300">类型: 文章, 状态: 草稿, 作者: 当前用户</td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">2025-05-15</td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-right">
                                        <button class="text-gray-400 hover:text-blue-400 transition-colors p-1">
                                            <i class="fas fa-search"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-400 transition-colors p-1">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                
                                <tr class="hover:bg-gray-700 transition-colors">
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">系统相关内容</td>
                                    <td class="px-4 py-3 text-sm text-gray-300">关键词: 系统, 类型: 全部</td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">2025-04-10</td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-right">
                                        <button class="text-gray-400 hover:text-blue-400 transition-colors p-1">
                                            <i class="fas fa-search"></i>
                                        </button>
                                        <button class="text-gray-400 hover:text-red-400 transition-colors p-1">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
        
        <!-- 底部版权 -->
        <footer class="bg-gray-800 border-t border-gray-700 p-4 text-center text-sm text-gray-500">
            &copy; 2025 GACMS. All rights reserved.
        </footer>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script>
        $(document).ready(function() {
            // 高级选项切换
            $('#toggleAdvancedBtn').click(function() {
                $('#advancedOptions').slideToggle();
                
                const icon = $(this).find('i');
                const text = $(this).text().trim();
                
                if (text.includes('展开')) {
                    $(this).html('<i class="fas fa-chevron-up mr-1"></i> 收起高级选项');
                } else {
                    $(this).html('<i class="fas fa-chevron-down mr-1"></i> 展开高级选项');
                }
            });
            
            // 视图切换
            $('.view-switcher-btn').click(function() {
                $('.view-switcher-btn').removeClass('active');
                $(this).addClass('active');
                
                const view = $(this).data('view');
                
                if (view === 'list') {
                    $('#listView').show();
                    $('#gridView').hide();
                } else {
                    $('#listView').hide();
                    $('#gridView').show();
                }
            });
            
            // 清空搜索框
            $('#clearSearchBtn').click(function() {
                $('#mainSearchInput').val('');
            });
            
            // 移除筛选条件
            $('.filter-tag-remove').click(function() {
                $(this).parent().remove();
            });
        });
    </script>
</body>
</html> 