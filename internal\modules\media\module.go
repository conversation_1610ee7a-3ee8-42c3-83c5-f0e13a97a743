/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/media/module.go
 * @Description: Defines the media module, its services, and its integration with the core system.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package media

import (
	"gacms/internal/modules/media/application/service"
	domainContract "gacms/internal/modules/media/domain/contract"
	"gacms/internal/modules/media/domain/model"
	"gacms/internal/modules/media/infrastructure/persistence"
	"gacms/internal/modules/media/infrastructure/storage"
	"gacms/internal/modules/media/port/http/controller"
	"gacms/internal/port/http/middleware"
	pkgContract "gacms/pkg/contract"

	"github.com/gin-gonic/gin"
	"go.uber.org/fx"
)

type MediaModule struct {
	controller *controller.MediaController
}

// Info provides metadata about the module.
func (m *MediaModule) Info() pkgContract.ModuleInfo {
	return pkgContract.ModuleInfo{
		Name:        "Media",
		Description: "Manages media assets, uploads, and storage.",
		Version:     "1.0.0",
	}
}

// GetModels returns the GORM models for auto-migration.
func (m *MediaModule) GetModels() []interface{} {
	return []interface{}{&model.Media{}}
}

// ExposePermissions declares the permissions this module uses.
func (m *MediaModule) ExposePermissions() []pkgContract.PermissionInfo {
	return []pkgContract.PermissionInfo{
		{Name: "media:upload", Description: "Get credentials to upload new media"},
		{Name: "media:manage", Description: "Manage all media assets"},
		{Name: "media:view", Description: "View media assets"},
		{Name: "media:delete", Description: "Delete media assets"},
	}
}

// Routes defines the HTTP routes for this module.
func (m *MediaModule) Routes(authMiddleware *middleware.AdminAuthMiddleware) []pkgContract.Route {
	manageGuard := authMiddleware.Handle("media:manage")
	uploadGuard := authMiddleware.Handle("media:upload")

	return []pkgContract.Route{
		{Method: "GET", Path: "/media", Handler: m.controller.ListMedia, Middlewares: []gin.HandlerFunc{manageGuard}},
		{Method: "POST", Path: "/media/upload-token", Handler: m.controller.GetUploadToken, Middlewares: []gin.HandlerFunc{uploadGuard}},
		// Internal route for handling the actual upload, protected by a different mechanism (e.g., token).
		{Method: "POST", Path: "/media/upload-handler", Handler: m.controller.HandleUpload},
	}
}

// Module bundle for fx
var Module = fx.Options(
	// Repositories and Storage
	fx.Provide(
		fx.Annotate(persistence.NewMediaGormRepository, fx.As(new(domainContract.MediaRepository))),
		fx.Annotate(storage.NewLocalStorage, fx.As(new(domainContract.Storage))),
	),
	// Service & Controller
	fx.Provide(
		service.NewMediaService,
		controller.NewMediaController,
	),
	// Module
	fx.Provide(
		fx.Annotate(
			func(controller *controller.MediaController) *MediaModule {
				return &MediaModule{controller: controller}
			},
			fx.As(new(pkgContract.IModule)),
			fx.As(new(pkgContract.IMigratable)),
			fx.As(new(pkgContract.IPermissionExposer)),
		),
	),
	// Invokers
	fx.Invoke(func(router *gin.Engine, module *MediaModule, auth *middleware.AdminAuthMiddleware) {
		for _, route := range module.Routes(auth) {
			prefix := "/api/admin"
			if route.Path == "/media/upload-handler" {
				prefix = "/api/internal" // Special case for internal route
			}
			fullPath := prefix + route.Path
			router.Handle(route.Method, fullPath, append(route.Middlewares, route.Handler)...)
		}
	}),
) 