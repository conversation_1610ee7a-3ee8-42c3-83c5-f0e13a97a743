# GACMS 专业级文件结构与设计说明

## 整体架构设计

GACMS 采用**微核心架构**，通过分层设计实现高内聚低耦合，支持多租户、模块化扩展和前后端分离。系统由六大核心组件构成：

1. **核心引擎** - 系统运行时管理
2. **功能模块** - 独立业务单元
3. **扩展系统** - 应用/插件/主题管理
4. **配置体系** - 全局+站点配置
5. **前端框架** - React应用体系
6. **资源管理** - 文件与静态资源

## 完整文件结构

```
GACMS/
├── cmd/
│   └── gacms/
│       └── main.go                 # 系统入口：初始化核心引擎
├── configs/                        # 配置中心
│   ├── global/                     # 全局系统配置
│   │   ├── system.yaml             # 核心参数
│   │   ├── security.yaml           # 安全策略
│   │   └── modules/                # 模块默认配置
│   ├── sites/                      # 站点级配置
│   │   ├── site1/                  # 站点1配置
│   │   └── site2/                  # 站点2配置
│   └── env/                        # 环境覆盖配置
├── docs/                           # 架构文档
│   ├── ADR/                        # 架构决策记录
│   └── modules/                    # 模块设计文档
├── internal/                       # 核心实现（私有）
│   ├── core/                       # 微核心引擎
│   │   ├── service/                # 核心服务
│   │   │   ├── ModuleRegistry.go   # 模块生命周期管理
│   │   │   ├── SiteCoordinator.go  # 多站点调度器
│   │   │   └── HookDispatcher.go   # 插件钩子系统
│   │   └── di/                     # 依赖注入容器
│   ├── infrastructure/             # 跨模块基础设施
│   │   ├── database/               # 数据访问抽象
│   │   ├── cache/                  # 缓存管理
│   │   └── events/                 # 事件总线
│   ├── port/                       # 外部接口适配器
│   │   ├── http/                   # HTTP接口
│   │   │   ├── middleware/         # 全局中间件
│   │   │   ├── response/           # 统一响应处理
│   │   │   └── router/             # 动态路由中心
│   │   ├── cli/                    # 命令行接口
│   │   ├── websocket/              # websocket接口（预留）
│   │   └── rpc/                    # gRPC接口（预留）
│   └── modules/                    # 官方核心模块
│       ├── user/                   # 用户模块
│       ├── content/                # 内容模块
│       └── ...                     # 其他核心模块
├── pkg/                            # 公共库（可复用）
│   ├── contract/                   # 系统契约接口
│   │   ├── Module.go               # 模块接口规范
│   │   ├── Plugin.go               # 插件接口规范
│   │   └── Theme.go                # 主题接口规范
│   ├── moduleSdk/N                 # 模块开发套件
│   ├── pluginSdk/                  # 插件开发套件
│   ├── themeSdk/                   # 主题开发套件
│   └── utils/                      # 通用工具包
├── vendors/                        # 第三方扩展
│   └── {vendorName}/               # 供应商命名空间
│       ├── modules/                # 第三方模块
│       └── plugins/                # 第三方插件
├── themes/                         # 主题存储
│   └── {themeName}/                # 主题实现
│       ├── assets/                 # 静态资源
│       ├── config/                 # 主题配置
│       └── templates/              # 模板文件
├── web/                            # 前端工程(React)
│   ├── public/                     # 公共资源
│   └── src/
│       ├── core/                   # 前端核心框架
│       │   ├── api/                # API服务
│       │   ├── context/            # 全局上下文
│       │   ├── layout/             # 基础布局
│       │   └── store/              # 状态管理
│       ├── modules/                # 前端功能模块
│       │   ├── core/               # 核心模块
│       │   └── features/           # 功能模块
│       ├── themes/                 # 主题实现
│       │   └── components/         # 主题组件库
│       ├── hooks/                  # 全局hooks
│       ├── utils/                  # 工具函数
│       ├── App.js                  # 应用入口
│       └── Router.js               # 动态路由
├── scripts/                        # 部署/维护系统脚本
├── static/                         # 系统静态资源
└── uploads/                        # 上传目录
    └── sites/                      # 按站点隔离
```

## 核心设计说明

### 1. 微核心引擎
- **契约驱动**：通过标准化接口(Module/Plugin/Theme)定义扩展规范
- **依赖倒置**：高层模块不依赖低层实现，通过接口交互
- **事件总线**：模块间通过发布/订阅模式解耦通信
- **沙箱机制**：第三方扩展在隔离环境中运行

**核心功能：**
- 模块生命周期管理（加载/卸载/热更新）
- 依赖注入容器（全局/站点级容器）
- 事件总线（模块间通信）
- 钩子系统（插件扩展点）
- 多租户上下文管理

**关键服务：**
- 模块注册表：管理所有模块元数据
- 站点协调器：处理多租户资源分配
- 钩子调度器：管理插件扩展点
- 配置加载器：合并全局+站点配置

### 2. 模块化系统
- **官方核心模块**：用户/内容/媒体等基础功能
- **模块结构**：
  - `port/`：对外接口(API控制器)
  - `application/`：业务逻辑服务
  - `domain/`：领域模型与仓储接口
  - `infrastructure/`：持久化等实现
  - `module.go`：模块入口(实现核心契约)
  - `module.yaml`：模块元数据

**模块结构规范：**
```
user/                            # 用户模块
├── port/                        # 暴露接口
│   ├── http/                    # HTTP控制器
│   └── events/                  # 事件订阅
├── application/                 # 应用层
│   ├── service/                 # 业务服务
│   │   ├── admin/               # 后台业务逻辑
│   │   └── front/               # 前台业务逻辑
│   └── dto/                     # 数据传输对象
├── domain/                      # 领域层
│   ├── model/                   # 领域模型
│   └── repository/              # 仓储接口
├── infrastructure/              # 实现层
│   ├── auth/                    # 认证实现
│   └── persistence/             # 持久化实现
└── module.go                    # 模块入口
```

**模块特性：**
- 声明式路由注册
- 依赖显式声明
- 配置隔离（仅访问自身配置）
- 事件发布/订阅能力
- 权限声明（注册所需权限项）

### 3. 多租户架构
- **站点隔离**：
  - 配置隔离：每个站点独立配置
  - 数据隔离：数据库级多租户支持
  - 资源隔离：上传目录按站点划分
- **租户管理**：
  - 站点注册/停用接口
  - 资源配额管理
  - 跨站点数据共享控制
  
**租户隔离维度：**
1. 配置隔离
   - 全局配置 → 站点配置 → 模块配置
   - 热重载机制（配置变更实时生效）

2. 数据隔离
   - 数据库层自动注入 `site_id`
   - 跨站点数据共享白名单机制

3. 资源隔离
   - 文件存储按站点隔离
   - 计算资源配额管理
   - 内存使用限制

**站点管理：**
- 站点注册/停用接口
- 资源使用监控
- 站点级备份恢复

### 4. 扩展系统
- **插件系统**：
  - 轻量级功能扩展
  - 通过钩子(Hook)介入核心流程
  - 热插拔设计，无需重启系统
- **主题系统**：
  - 纯前端实现
  - 模板覆盖机制
  - 资源动态加载

**插件系统：**
```
seo/                      # SEO插件
├── hooks/                       # 钩子实现
│   ├── ContentRender.go         # 内容渲染钩子
│   └── SitemapGenerator.go      # 站点地图生成
├── services/                    # 插件服务
└── plugin.yaml                  # 元数据声明
```

**插件特性：**
- 热插拔（无需重启系统）
- 沙箱运行环境
- 钩子扩展点（预定义+自定义）
- 资源访问限制

**主题系统：**
- 模板覆盖机制
- 主题继承体系
- 运行时切换
- 组件库支持

### 5. 配置体系
- **三层配置结构**：
  1. 全局配置：系统级参数
  2. 站点配置：租户级覆盖
  3. 环境配置：部署环境特定值
- **动态加载**：
  - 配置变更实时生效
  - 模块仅能访问自身配置
  - 敏感数据加密存储

### 6. 权限系统
- **双用户体系**：
  - 角色可定义
  - 后台用户：系统管理员/站点管理员/内容管理员/市场营销员/开发者/运维员
  - 前端用户：普通注册用户/会员用户/游客
- **RBAC模型**：
  - 角色继承机制
  - 权限细粒度控制
  - 前端组件级权限
  - 权限可分配给角色/用户/组
  
**双用户体系：**
```
用户体系          后台用户                 前端用户
认证方式          JWT+2FA               OAuth2.0/社交登录
权限模型          RBAC+ABAC             RBAC
存储隔离         独立数据表             独立数据表
会话管理         集中式会话             分布式会话
```

**权限控制点：**
1. API端点权限
2. 数据访问权限
3. UI元素可见性
4. 操作按钮权限

**权限声明：**
```yaml
# 用户模块权限声明
permissions:
  - id: user.create
    name: 创建用户
    scope: admin
  - id: user.delete
    name: 删除用户
    scope: admin
```

### 7. 前后端分离
- **核心逻辑**：
  - 前端使用React框架，后端使用Gin框架（Go语言），vite工具构建
  - 前端负责页面渲染，后端负责业务逻辑，前端与后端通过API通信

- **API规范**：
  - `/api/admin/`：管理后台接口
  - `/api/front/`：前端用户接口
  - `/api/module/`：模块间API

- **通信协议**：
  - RESTful API（主）
  - WebSocket（实时通知）
  - GraphQL（可选，复杂查询）
  - RPC（可选，跨模块通信）

- **前端架构**：
  - 模块化React应用
  - 动态组件加载，缓存优化，页面静态化
  - 主题皮肤系统

### 8. 前端架构设计

**架构特性：**
- 模块化React应用
- 动态组件加载
- 主题皮肤系统
- 响应式布局支持

**关键服务：**
```mermaid
graph TD
    A[API Client] --> B[认证拦截器]
    B --> C[错误处理器]
    C --> D[响应格式化]
    D --> E[模块通信总线]
```

**模块结构：**
```
user-module/                     # 用户模块前端
├── components/                  # 组件库
│   ├── UserList.js              # 用户列表
│   └── ProfileForm.js           # 资料表单
├── hooks/                       # 模块hooks
│   └── useUser.js               # 用户数据hook
├── pages/                       # 页面组件
│   ├── AdminUsers.js            # 后台用户管理
│   └── FrontProfile.js          # 前端个人资料
├── routes.js                    # 模块路由
└── index.js                     # 模块入口
```

**模块特性：**
- 按需加载（代码分割）
- 独立状态管理
- 路由动态注册
- 主题组件覆盖

**主题结构：**
```
theme-light/                     # 明亮主题
├── assets/                      # 静态资源
├── components/                  # 主题组件
│   ├── Button.js                # 按钮组件
│   └── Card.js                  # 卡片组件
├── palette.js                   # 配色方案
└── theme.json                   # 主题元数据
```

**主题特性：**
- 组件级样式覆盖
- 运行时切换
- 主题继承（基础主题→扩展主题）
- 响应式变量支持

**前端权限控制：**
1. 路由守卫（页面级访问控制）
2. 条件渲染（组件级显示控制）
3. 操作禁用（按钮级权限）
4. 数据过滤（API响应处理）

**权限声明：**
```js
// 路由权限配置
const routes = [
  {
    path: '/admin/users',
    component: AdminUsers,
    meta: { 
      permissions: ['user.view'] 
    }
  }
]
```

## 核心工作流程

### 系统启动流程
```mermaid
sequenceDiagram
    participant M as Main
    participant C as ConfigLoader
    participant D as DI
    participant R as ModuleRegistry
    participant S as SiteManager
    
    M->>C: 加载全局配置
    C->>D: 初始化DI容器
    D->>R: 扫描模块目录
    R->>R: 解析依赖关系
    R->>D: 注册核心服务
    D->>S: 初始化站点管理器
    S->>S: 加载站点配置
    S->>R: 按需加载模块
    R->>D: 注册模块服务
    D->>M: 启动完成
```

### 请求处理流程
```mermaid
flowchart TD
    A[请求进入] --> B{站点识别中间件}
    B --> C[加载站点配置]
    C --> D{认证中间件}
    D --> E[解析用户身份]
    E --> F{路由分发}
    F --> G[模块控制器]
    G --> H[应用服务]
    H --> I[领域模型]
    I --> J[基础设施]
    J --> K[生成响应]
    K --> L[钩子处理]
    L --> M[返回响应]
```

### 模块加载流程
```mermaid
graph LR
    A[模块扫描] --> B[元数据解析]
    B --> C[依赖关系分析]
    C --> D[初始化队列排序]
    D --> E[执行模块初始化]
    E --> F[注册路由]
    F --> G[注册服务]
    G --> H[发布模块就绪事件]
```

## 扩展性设计

### 模块扩展点
1. **路由注册**：模块声明API端点
2. **菜单扩展**：后台管理菜单动态注入
3. **事件订阅**：响应系统核心事件
4. **权限声明**：注册所需权限项
5. **配置扩展**：添加模块专属配置

### 插件扩展点
1. **内容渲染钩子**：在内容展示前后介入
2. **表单处理钩子**：扩展表单提交逻辑
3. **定时任务钩子**：注册后台任务
4. **API端点扩展**：添加新API端点
5. **工作流扩展**：介入业务流程

## 安全设计

1. **分层防护**：
   - **网络层**
     - DDoS防护
     - WAF集成（可选）
   - **应用层**
     - 输入验证
     - 输出编码
     - 请求限速
   - **数据层**
     - 加密存储
     - 访问控制
     - 审计日志
2. **访问控制**：
   - 认证安全
     - JWT令牌
     - 双因素认证
     - 会话固定防护
   - 权限安全
     - 最小权限原则
     - 权限变更审计
     - 权限验证框架
3. **数据安全**：
   - 传输层加密(TLS)
   - 敏感数据脱敏
   - 定期安全扫描审计

## 运维支持

1. **健康检查**：
   - `/health` 核心状态
   - `/health/modules` 模块状态
2. **监控指标**：
   - Prometheus指标
     - 请求量/延迟
     - 资源使用率
     - 模块负载
   - 性能指标采集
   - 资源使用统计
3. **日志系统**：
   - 结构化日志
   - 分级日志管理
   - 日志审计追踪

### 维护工具
1. CLI管理工具
   - 模块安装/卸载
   - 站点管理
   - 配置管理
2. 后台运维面板
   - 系统监控
   - 模块管理
   - 站点资源查看

## 设计核心原则

1. **开闭原则**：对扩展开放，对修改关闭
2. **单一职责**：每个模块/组件专注单一功能
3. **模块化原则** - 功能边界清晰，高内聚低耦合
4. **扩展性原则** - 通过标准接口支持灵活扩展
5. **隔离性原则** - 租户/模块/插件资源隔离
6. **最小化原则** - 按需加载，资源高效利用
7. **显式声明原则** - 依赖/权限/配置显式声明
8. **渐进增强原则** - 核心精简，按需增强复杂度

