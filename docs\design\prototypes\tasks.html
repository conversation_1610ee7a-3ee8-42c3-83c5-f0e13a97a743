<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 定时任务管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .task-card {
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.05);
            transition: all 0.3s ease;
        }
        
        .task-card:hover {
            border-color: rgba(59, 130, 246, 0.3);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }
        
        .progress-bar {
            height: 6px;
            background-color: #374151;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 8px;
        }
        
        .progress-value {
            height: 100%;
            border-radius: 3px;
        }
        
        .task-switch {
            position: relative;
            display: inline-block;
            width: 46px;
            height: 24px;
        }
        
        .task-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .switch-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #4B5563;
            transition: .4s;
            border-radius: 24px;
        }
        
        .switch-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .switch-slider {
            background-color: #3B82F6;
        }
        
        input:checked + .switch-slider:before {
            transform: translateX(22px);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">定时任务管理</h2>
                    <button id="createTask" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                        <span class="relative flex items-center">
                            <i class="fas fa-plus mr-2"></i> 创建任务
                        </span>
                    </button>
                </div>
                <div class="mt-4">
                    <p class="text-gray-400">定时任务允许您在特定时间或按照特定时间间隔自动执行系统操作。这些任务可以包括数据库备份、缓存清理、报表生成等系统维护工作。</p>
                </div>
            </div>
            
            <!-- 任务统计卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <!-- 总任务数 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-400">总任务数</p>
                            <h3 class="text-2xl font-bold mt-1">24</h3>
                        </div>
                        <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center">
                            <i class="fas fa-tasks text-blue-400 text-xl"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 运行中 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-400">运行中</p>
                            <h3 class="text-2xl font-bold mt-1">3</h3>
                        </div>
                        <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center">
                            <i class="fas fa-play text-green-400 text-xl"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 暂停中 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-400">暂停中</p>
                            <h3 class="text-2xl font-bold mt-1">5</h3>
                        </div>
                        <div class="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center">
                            <i class="fas fa-pause text-yellow-400 text-xl"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 失败任务 -->
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-400">失败任务</p>
                            <h3 class="text-2xl font-bold mt-1">2</h3>
                        </div>
                        <div class="w-12 h-12 rounded-full bg-red-500/20 flex items-center justify-center">
                            <i class="fas fa-exclamation-circle text-red-400 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 任务列表 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl overflow-hidden mb-6">
                <!-- 筛选条件 -->
                <div class="p-4 border-b border-gray-700">
                    <div class="flex flex-wrap items-center gap-4">
                        <div class="flex-1 min-w-[200px]">
                            <input type="text" placeholder="搜索任务..." class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        <div>
                            <select class="bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option>所有状态</option>
                                <option>运行中</option>
                                <option>等待中</option>
                                <option>已暂停</option>
                                <option>已失败</option>
                                <option>已禁用</option>
                            </select>
                        </div>
                        <div>
                            <select class="bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option>所有类型</option>
                                <option>系统维护</option>
                                <option>数据处理</option>
                                <option>报表生成</option>
                                <option>邮件发送</option>
                                <option>自定义</option>
                            </select>
                        </div>
                        <div>
                            <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-filter mr-2"></i>筛选
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 任务表格 -->
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="text-left border-b border-gray-700">
                                <th class="px-6 py-4 text-sm font-semibold">任务名称</th>
                                <th class="px-6 py-4 text-sm font-semibold">执行规则</th>
                                <th class="px-6 py-4 text-sm font-semibold">上次执行</th>
                                <th class="px-6 py-4 text-sm font-semibold">下次执行</th>
                                <th class="px-6 py-4 text-sm font-semibold">状态</th>
                                <th class="px-6 py-4 text-sm font-semibold">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 运行中的任务 -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-database text-green-400"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium">数据库备份</div>
                                            <div class="text-xs text-gray-400">系统维护</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <span class="text-sm">每天 03:00</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="text-sm text-gray-400">2025-03-09 03:00</span>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="text-sm text-gray-400">2025-03-10 03:00</span>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full">运行中</span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-400 hover:text-blue-300" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-yellow-400 hover:text-yellow-300" title="暂停">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                        <button class="text-red-400 hover:text-red-300" title="停止">
                                            <i class="fas fa-stop"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 等待中的任务 -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded-full bg-blue-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-chart-bar text-blue-400"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium">流量统计分析</div>
                                            <div class="text-xs text-gray-400">报表生成</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <span class="text-sm">每周一 08:00</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="text-sm text-gray-400">2025-03-03 08:00</span>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="text-sm text-gray-400">2025-03-10 08:00</span>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full">等待中</span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-400 hover:text-blue-300" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-green-400 hover:text-green-300" title="立即执行">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="text-red-400 hover:text-red-300" title="禁用">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 已暂停的任务 -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded-full bg-yellow-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-file-alt text-yellow-400"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium">文章索引重建</div>
                                            <div class="text-xs text-gray-400">数据处理</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <span class="text-sm">每月1日 00:00</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="text-sm text-gray-400">2025-03-01 00:00</span>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="text-sm text-gray-400">--</span>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="px-2 py-1 bg-yellow-500/20 text-yellow-400 text-xs rounded-full">已暂停</span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-400 hover:text-blue-300" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-green-400 hover:text-green-300" title="恢复">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="text-red-400 hover:text-red-300" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 已失败的任务 -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-envelope text-red-400"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium">邮件订阅发送</div>
                                            <div class="text-xs text-gray-400">邮件发送</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <span class="text-sm">每周五 16:00</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="text-sm text-gray-400">2025-03-07 16:00</span>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="text-sm text-gray-400">2025-03-14 16:00</span>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="px-2 py-1 bg-red-500/20 text-red-400 text-xs rounded-full">已失败</span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-400 hover:text-blue-300" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-green-400 hover:text-green-300" title="重试">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                        <button class="text-red-400 hover:text-red-300" title="禁用">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- 已禁用的任务 -->
                            <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded-full bg-gray-500/20 flex items-center justify-center mr-3">
                                            <i class="fas fa-broom text-gray-400"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium">临时文件清理</div>
                                            <div class="text-xs text-gray-400">系统维护</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <span class="text-sm">每6小时</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="text-sm text-gray-400">2025-03-08 18:00</span>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="text-sm text-gray-400">--</span>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="px-2 py-1 bg-gray-500/20 text-gray-400 text-xs rounded-full">已禁用</span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-400 hover:text-blue-300" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-green-400 hover:text-green-300" title="启用">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="text-red-400 hover:text-red-300" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页控件 -->
                <div class="p-6 border-t border-gray-700 flex justify-between items-center">
                    <span class="text-sm text-gray-400">显示 1-5 条，共 24 条</span>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded" disabled>上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded">1</button>
                        <button class="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded">2</button>
                        <button class="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded">3</button>
                        <button class="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded">4</button>
                        <button class="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded">5</button>
                        <button class="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded">下一页</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <!-- 创建定时任务弹窗 -->
    <div id="createTaskModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-gray-800 rounded-xl p-6 max-w-lg w-full">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">创建定时任务</h3>
                <button class="text-gray-400 hover:text-white" id="closeTaskModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="space-y-4 mb-6">
                <div>
                    <label class="block text-gray-300 mb-2">任务名称:</label>
                    <input type="text" placeholder="输入任务名称" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                
                <div>
                    <label class="block text-gray-300 mb-2">任务类型:</label>
                    <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option>系统维护</option>
                        <option>数据处理</option>
                        <option>报表生成</option>
                        <option>邮件发送</option>
                        <option>自定义</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-gray-300 mb-2">执行命令或脚本:</label>
                    <textarea class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none" rows="3" placeholder="/usr/bin/php /path/to/script.php"></textarea>
                    <p class="text-xs text-gray-400 mt-1">输入要执行的命令或脚本路径</p>
                </div>
                
                <div>
                    <label class="block text-gray-300 mb-2">执行规则:</label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <select class="bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option>每分钟</option>
                            <option>每小时</option>
                            <option selected>每天</option>
                            <option>每周</option>
                            <option>每月</option>
                            <option>自定义</option>
                        </select>
                        <input type="time" value="03:00" class="bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <p class="text-xs text-gray-400 mt-1">高级计划请选择"自定义"并使用cron表达式</p>
                </div>
                
                <div class="hidden">
                    <label class="block text-gray-300 mb-2">Cron表达式 (高级):</label>
                    <input type="text" value="0 3 * * *" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <p class="text-xs text-gray-400 mt-1">使用标准cron表达式：分 时 日 月 周</p>
                </div>
                
                <div>
                    <label class="block text-gray-300 mb-2">任务超时时间:</label>
                    <div class="flex items-center">
                        <input type="number" value="30" class="w-full bg-gray-700/50 border border-gray-600 rounded-l-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <span class="bg-gray-600 px-4 py-2 rounded-r-lg">分钟</span>
                    </div>
                    <p class="text-xs text-gray-400 mt-1">超过此时间任务将被强制终止</p>
                </div>
                
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                        <span class="ml-2">任务失败后自动重试</span>
                    </label>
                </div>
            </div>
            
            <div class="flex justify-end space-x-3">
                <button class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors" id="cancelTask">
                    取消
                </button>
                <button class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors" id="saveTask">
                    保存
                </button>
            </div>
        </div>
    </div>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 定时任务创建相关功能
            const createTaskBtn = document.getElementById('createTask');
            const createTaskModal = document.getElementById('createTaskModal');
            const closeTaskModal = document.getElementById('closeTaskModal');
            const cancelTaskBtn = document.getElementById('cancelTask');
            const saveTaskBtn = document.getElementById('saveTask');
            
            // 打开创建定时任务弹窗
            if(createTaskBtn) {
                createTaskBtn.addEventListener('click', function() {
                    createTaskModal.classList.remove('hidden');
                });
            }
            
            // 关闭创建定时任务弹窗
            if(closeTaskModal) {
                closeTaskModal.addEventListener('click', function() {
                    createTaskModal.classList.add('hidden');
                });
            }
            
            // 取消创建定时任务
            if(cancelTaskBtn) {
                cancelTaskBtn.addEventListener('click', function() {
                    createTaskModal.classList.add('hidden');
                });
            }
            
            // 保存定时任务
            if(saveTaskBtn) {
                saveTaskBtn.addEventListener('click', function() {
                    // 实际应用中这里会调用API保存任务数据
                    alert('定时任务已创建！');
                    createTaskModal.classList.add('hidden');
                });
            }
        });
    </script>
</body>
</html> 