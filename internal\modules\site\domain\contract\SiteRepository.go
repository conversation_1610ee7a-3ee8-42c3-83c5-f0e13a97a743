/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/site/domain/contract/SiteRepository.go
 * @Description: Defines the repository interface for sites.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"context"
	"gacms/internal/modules/site/domain/model"
)

// SiteRepository defines the interface for site data operations.
type SiteRepository interface {
	Create(ctx context.Context, site *model.Site) error
	GetByID(ctx context.Context, id uint) (*model.Site, error)
	List(ctx context.Context, page, pageSize int) ([]*model.Site, int64, error)
	Update(ctx context.Context, site *model.Site) error
	Delete(ctx context.Context, id uint) error
	GetByDomain(ctx context.Context, domain string) (*model.Site, error)
	GetByBackendDomain(ctx context.Context, backendDomain string) (*model.Site, error)
} 