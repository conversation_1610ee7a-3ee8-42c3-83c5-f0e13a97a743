/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/recommendation/infrastructure/strategy/TagBasedStrategy.go
 * @Description: Implements a tag-based recommendation strategy.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package strategy

import (
	"encoding/json"
	"gacms/internal/modules/contenttype/domain/contract"
	"gacms/internal/modules/contenttype/domain/model"
)

// TagBasedStrategy recommends content based on shared tags.
type TagBasedStrategy struct {
	itemRepo contract.ContentItemRepository
}

func NewTagBasedStrategy(itemRepo contract.ContentItemRepository) *TagBasedStrategy {
	return &TagBasedStrategy{itemRepo: itemRepo}
}

// GetRecommendations finds other posts that share tags with the given post.
func (s *TagBasedStrategy) GetRecommendations(item *model.ContentItem, siteID uint, limit int) ([]*model.ContentItem, error) {
	// 1. Extract tag IDs from the source item's 'tags' relation field.
	var itemData struct {
		TagIDs []uint `json:"tags"`
	}
	if err := json.Unmarshal(item.Data, &itemData); err != nil || len(itemData.TagIDs) == 0 {
		return []*model.ContentItem{}, nil
	}

	// 2. For each tag, find other items with that tag.
	recommendationMap := make(map[uint]*model.ContentItem)
	
	for _, tagID := range itemData.TagIDs {
		// Use the new repository method to find related items.
		relatedItems, err := s.itemRepo.FindAllByRelation(siteID, item.ContentTypeID, "tags", tagID)
		if err != nil {
			// Log the error but continue, so one bad tag doesn't fail everything.
			// log.Printf("Error finding items for tag %d: %v", tagID, err)
			continue
		}

		for _, relatedItem := range relatedItems {
			// Exclude the source item itself and items already added.
			if relatedItem.ID != item.ID {
				recommendationMap[relatedItem.ID] = relatedItem
			}
		}
	}

	// 3. Convert map to slice and respect the limit.
	var recommendations []*model.ContentItem
	for _, rec := range recommendationMap {
		if len(recommendations) < limit {
			recommendations = append(recommendations, rec)
		} else {
			break
		}
	}

	return recommendations, nil
}
