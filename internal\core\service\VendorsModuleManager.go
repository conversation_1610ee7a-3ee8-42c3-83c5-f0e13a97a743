/*
Author: <PERSON><PERSON> Nieh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"sync"

	"gacms/internal/core/domain/model"
	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// VendorsModuleManager Vendors模块管理器接口
// 管理vendors目录下的所有扩展模块，包括：
// - 官方扩展：GACMS官方开发的扩展模块
// - 社区模块：社区开发者贡献的模块
// - 第三方商业：商业供应商的付费模块
// - 企业定制：企业内部开发的专用模块
// 目录结构：vendors/vendor_name/module_name/
type VendorsModuleManager interface {
	// 安装和卸载
	InstallModule(packagePath string) error
	UninstallModule(moduleName string) error
	
	// 配置管理
	LoadModuleFromJSON(jsonPath string) (*VendorsModuleConfig, error)
	ValidateModuleConfig(config *VendorsModuleConfig) error
	
	// 模块发现
	ScanVendorsDirectory() ([]*VendorsModuleConfig, error)
	GetInstalledVendorsModules() ([]*VendorsModuleConfig, error)
	
	// 模块状态
	IsModuleInstalled(moduleName string) bool
	GetModuleInfo(moduleName string) (*VendorsModuleConfig, error)
}

// VendorsModuleConfig Vendors模块JSON配置结构
type VendorsModuleConfig struct {
	// 基本信息
	Name        string `json:"name"`
	Version     string `json:"version"`
	Description string `json:"description"`
	Author      string `json:"author"`
	Vendor      string `json:"vendor"`
	Homepage    string `json:"homepage,omitempty"`
	Repository  string `json:"repository,omitempty"`
	License     string `json:"license,omitempty"`
	
	// 依赖和兼容性
	Dependencies    []string          `json:"dependencies,omitempty"`
	Conflicts       []string          `json:"conflicts,omitempty"`
	MinGacmsVersion string            `json:"min_gacms_version"`
	MaxGacmsVersion string            `json:"max_gacms_version,omitempty"`
	
	// 功能定义
	Permissions []PermissionConfig    `json:"permissions,omitempty"`
	Routes      []RouteConfig         `json:"routes,omitempty"`
	Events      EventsConfig          `json:"events,omitempty"`
	
	// 配置和设置
	Config   map[string]interface{} `json:"config,omitempty"`
	Settings []SettingConfig        `json:"settings,omitempty"`
	
	// 文件和资源
	EntryPoint string            `json:"entry_point"`
	Assets     []string          `json:"assets,omitempty"`
	Templates  []string          `json:"templates,omitempty"`
	
	// 安装信息
	InstallPath string `json:"install_path,omitempty"`
	Enabled     bool   `json:"enabled"`
}

// PermissionConfig 权限配置
type PermissionConfig struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Category    string `json:"category,omitempty"`
}

// RouteConfig 路由配置
type RouteConfig struct {
	Path        string            `json:"path"`
	Method      string            `json:"method"`
	Controller  string            `json:"controller"`
	Action      string            `json:"action,omitempty"`
	Permission  string            `json:"permission,omitempty"`
	Middlewares []string          `json:"middlewares,omitempty"`
	Params      map[string]string `json:"params,omitempty"`
}

// EventsConfig 事件配置
type EventsConfig struct {
	Publishes []string              `json:"publishes,omitempty"`
	Listens   []EventListenerConfig `json:"listens,omitempty"`
}

// SettingConfig 设置配置
type SettingConfig struct {
	Key         string      `json:"key"`
	Type        string      `json:"type"`
	Default     interface{} `json:"default,omitempty"`
	Required    bool        `json:"required"`
	Description string      `json:"description"`
	Options     []string    `json:"options,omitempty"`
}

// DefaultVendorsModuleManager 默认Vendors模块管理器实现
type DefaultVendorsModuleManager struct {
	configManager ModuleConfigManager
	eventMapper   ModuleEventMapper
	logger        *zap.Logger
	
	// Vendors模块目录
	vendorsDir string
	
	// 已安装模块缓存
	installedModules map[string]*VendorsModuleConfig
	mu               sync.RWMutex
}

// VendorsModuleManagerParams fx依赖注入参数
type VendorsModuleManagerParams struct {
	fx.In

	ConfigManager ModuleConfigManager
	EventMapper   ModuleEventMapper
	Logger        *zap.Logger
}

// NewDefaultVendorsModuleManager 创建默认Vendors模块管理器
func NewDefaultVendorsModuleManager(params VendorsModuleManagerParams) VendorsModuleManager {
	manager := &DefaultVendorsModuleManager{
		configManager:    params.ConfigManager,
		eventMapper:      params.EventMapper,
		logger:           params.Logger,
		vendorsDir:       "vendors", // 默认vendors目录
		installedModules: make(map[string]*VendorsModuleConfig),
	}
	
	// 初始化时扫描已安装的模块
	manager.initializeInstalledModules()
	
	return manager
}

// InstallModule 安装模块
func (m *DefaultVendorsModuleManager) InstallModule(packagePath string) error {
	// 1. 解析模块包
	config, err := m.extractModulePackage(packagePath)
	if err != nil {
		return fmt.Errorf("failed to extract module package: %w", err)
	}
	
	// 2. 验证模块配置
	if err := m.ValidateModuleConfig(config); err != nil {
		return fmt.Errorf("module config validation failed: %w", err)
	}
	
	// 3. 检查依赖
	if err := m.checkDependencies(config); err != nil {
		return fmt.Errorf("dependency check failed: %w", err)
	}
	
	// 4. 检查冲突
	if err := m.checkConflicts(config); err != nil {
		return fmt.Errorf("conflict check failed: %w", err)
	}
	
	// 5. 安装模块文件
	installPath := filepath.Join(m.vendorsDir, config.Vendor, config.Name)
	if err := m.installModuleFiles(packagePath, installPath); err != nil {
		return fmt.Errorf("failed to install module files: %w", err)
	}
	
	// 6. 更新配置
	config.InstallPath = installPath
	config.Enabled = true
	
	// 7. 注册到数据库
	if err := m.registerModuleToDatabase(config); err != nil {
		// 回滚文件安装
		os.RemoveAll(installPath)
		return fmt.Errorf("failed to register module to database: %w", err)
	}
	
	// 8. 注册事件映射
	m.registerModuleEvents(config)
	
	// 9. 更新缓存
	m.mu.Lock()
	m.installedModules[config.Name] = config
	m.mu.Unlock()
	
	m.logger.Info("Vendors module installed successfully",
		zap.String("module", config.Name),
		zap.String("vendor", config.Vendor),
		zap.String("version", config.Version),
	)
	
	return nil
}

// UninstallModule 卸载模块
func (m *DefaultVendorsModuleManager) UninstallModule(moduleName string) error {
	// 1. 检查模块是否存在
	config, err := m.GetModuleInfo(moduleName)
	if err != nil {
		return fmt.Errorf("module not found: %w", err)
	}
	
	// 2. 检查依赖模块
	if err := m.checkDependentModules(moduleName); err != nil {
		return fmt.Errorf("cannot uninstall module: %w", err)
	}
	
	// 3. 从数据库移除
	if err := m.unregisterModuleFromDatabase(moduleName); err != nil {
		return fmt.Errorf("failed to unregister module from database: %w", err)
	}
	
	// 4. 移除事件映射
	m.eventMapper.RemoveModuleMapping(moduleName)
	
	// 5. 删除模块文件
	if config.InstallPath != "" {
		if err := os.RemoveAll(config.InstallPath); err != nil {
			m.logger.Warn("Failed to remove module files",
				zap.String("module", moduleName),
				zap.String("path", config.InstallPath),
				zap.Error(err),
			)
		}
	}
	
	// 6. 更新缓存
	m.mu.Lock()
	delete(m.installedModules, moduleName)
	m.mu.Unlock()
	
	m.logger.Info("Vendors module uninstalled successfully",
		zap.String("module", moduleName),
	)
	
	return nil
}

// LoadModuleFromJSON 从JSON文件加载模块配置
func (m *DefaultVendorsModuleManager) LoadModuleFromJSON(jsonPath string) (*VendorsModuleConfig, error) {
	data, err := ioutil.ReadFile(jsonPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read JSON file: %w", err)
	}
	
	var config VendorsModuleConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}
	
	return &config, nil
}

// ValidateModuleConfig 验证模块配置
func (m *DefaultVendorsModuleManager) ValidateModuleConfig(config *VendorsModuleConfig) error {
	// 基本字段验证
	if config.Name == "" {
		return fmt.Errorf("module name is required")
	}

	if config.Version == "" {
		return fmt.Errorf("module version is required")
	}

	if config.Vendor == "" {
		return fmt.Errorf("module vendor is required")
	}

	if config.EntryPoint == "" {
		return fmt.Errorf("module entry point is required")
	}

	// 名称格式验证
	if !isValidModuleName(config.Name) {
		return fmt.Errorf("invalid module name format: %s", config.Name)
	}

	// 版本格式验证
	if !isValidVersion(config.Version) {
		return fmt.Errorf("invalid version format: %s", config.Version)
	}

	// 权限验证
	for _, perm := range config.Permissions {
		if perm.Name == "" {
			return fmt.Errorf("permission name is required")
		}
	}

	// 路由验证
	for _, route := range config.Routes {
		if route.Path == "" {
			return fmt.Errorf("route path is required")
		}
		if route.Method == "" {
			return fmt.Errorf("route method is required")
		}
		if route.Controller == "" {
			return fmt.Errorf("route controller is required")
		}
	}

	return nil
}

// ScanVendorsDirectory 扫描vendors目录
func (m *DefaultVendorsModuleManager) ScanVendorsDirectory() ([]*VendorsModuleConfig, error) {
	var modules []*VendorsModuleConfig

	if _, err := os.Stat(m.vendorsDir); os.IsNotExist(err) {
		return modules, nil
	}

	err := filepath.Walk(m.vendorsDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.Name() == "module.json" {
			config, err := m.LoadModuleFromJSON(path)
			if err != nil {
				m.logger.Warn("Failed to load module config",
					zap.String("path", path),
					zap.Error(err),
				)
				return nil
			}

			modules = append(modules, config)
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to scan vendors directory: %w", err)
	}

	return modules, nil
}

// GetInstalledVendorsModules 获取已安装的vendors模块
func (m *DefaultVendorsModuleManager) GetInstalledVendorsModules() ([]*VendorsModuleConfig, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	modules := make([]*VendorsModuleConfig, 0, len(m.installedModules))
	for _, config := range m.installedModules {
		modules = append(modules, config)
	}

	return modules, nil
}

// IsModuleInstalled 检查模块是否已安装
func (m *DefaultVendorsModuleManager) IsModuleInstalled(moduleName string) bool {
	m.mu.RLock()
	defer m.mu.RUnlock()

	_, exists := m.installedModules[moduleName]
	return exists
}

// GetModuleInfo 获取模块信息
func (m *DefaultVendorsModuleManager) GetModuleInfo(moduleName string) (*VendorsModuleConfig, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if config, exists := m.installedModules[moduleName]; exists {
		return config, nil
	}

	return nil, fmt.Errorf("module %s not found", moduleName)
}

// 私有方法

// initializeInstalledModules 初始化已安装模块
func (m *DefaultVendorsModuleManager) initializeInstalledModules() {
	// 从数据库加载已安装的vendors模块
	configs, err := m.configManager.GetModulesByType(model.ModuleTypeVendors)
	if err != nil {
		m.logger.Error("Failed to load vendors modules from database", zap.Error(err))
		return
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	for _, config := range configs {
		// 尝试加载JSON配置
		jsonPath := filepath.Join(config.InstallPath, "module.json")
		if vendorsConfig, err := m.LoadModuleFromJSON(jsonPath); err == nil {
			m.installedModules[config.ModuleName] = vendorsConfig
		}
	}

	m.logger.Info("Initialized vendors modules",
		zap.Int("count", len(m.installedModules)),
	)
}

// extractModulePackage 解压模块包
func (m *DefaultVendorsModuleManager) extractModulePackage(packagePath string) (*VendorsModuleConfig, error) {
	// 这里应该实现解压逻辑，暂时假设packagePath是一个目录
	configPath := filepath.Join(packagePath, "module.json")
	return m.LoadModuleFromJSON(configPath)
}

// checkDependencies 检查依赖
func (m *DefaultVendorsModuleManager) checkDependencies(config *VendorsModuleConfig) error {
	for _, dep := range config.Dependencies {
		if !m.IsModuleInstalled(dep) {
			// 检查是否是核心模块或可选模块
			if _, err := m.configManager.GetModuleConfig(dep); err != nil {
				return fmt.Errorf("dependency %s not found", dep)
			}
		}
	}
	return nil
}

// checkConflicts 检查冲突
func (m *DefaultVendorsModuleManager) checkConflicts(config *VendorsModuleConfig) error {
	for _, conflict := range config.Conflicts {
		if m.IsModuleInstalled(conflict) {
			return fmt.Errorf("conflict with installed module: %s", conflict)
		}
	}
	return nil
}

// installModuleFiles 安装模块文件
func (m *DefaultVendorsModuleManager) installModuleFiles(sourcePath, targetPath string) error {
	// 创建目标目录
	if err := os.MkdirAll(targetPath, 0755); err != nil {
		return err
	}

	// 复制文件（这里应该实现完整的文件复制逻辑）
	// 暂时简化实现
	return nil
}

// registerModuleToDatabase 注册模块到数据库
func (m *DefaultVendorsModuleManager) registerModuleToDatabase(config *VendorsModuleConfig) error {
	dbConfig := &model.ModuleConfig{
		ModuleName:      config.Name,
		ModuleType:      model.ModuleTypeVendors,
		Enabled:         config.Enabled,
		Version:         config.Version,
		Description:     config.Description,
		Author:          config.Author,
		Dependencies:    model.DependencyList(config.Dependencies),
		Conflicts:       model.DependencyList(config.Conflicts),
		InstallPath:     config.InstallPath,
		DisplayName:     config.Name,
		Homepage:        config.Homepage,
		Repository:      config.Repository,
	}

	// 转换权限
	permissions := make(model.PermissionList, len(config.Permissions))
	for i, perm := range config.Permissions {
		permissions[i] = model.PermissionInfo{
			Name:        perm.Name,
			Description: perm.Description,
			Category:    perm.Category,
		}
	}
	dbConfig.Permissions = permissions

	// 转换路由
	routes := make(model.RouteList, len(config.Routes))
	for i, route := range config.Routes {
		routes[i] = model.RouteInfo{
			Path:        route.Path,
			Method:      route.Method,
			Controller:  route.Controller,
			Action:      route.Action,
			Permission:  route.Permission,
			Middlewares: route.Middlewares,
			Params:      route.Params,
		}
	}
	dbConfig.Routes = routes

	// 转换事件
	listens := make([]model.EventListenerConfig, len(config.Events.Listens))
	for i, listener := range config.Events.Listens {
		listens[i] = model.EventListenerConfig{
			Event:    listener.Event,
			Handler:  listener.Handler,
			Priority: listener.Priority,
		}
	}
	dbConfig.Events = model.EventConfig{
		Publishes: config.Events.Publishes,
		Listens:   listens,
	}

	return m.configManager.UpdateModuleConfig(dbConfig)
}

// unregisterModuleFromDatabase 从数据库注销模块
func (m *DefaultVendorsModuleManager) unregisterModuleFromDatabase(moduleName string) error {
	// 这里应该实现从数据库删除模块配置的逻辑
	// 暂时简化实现
	return nil
}

// checkDependentModules 检查依赖此模块的其他模块
func (m *DefaultVendorsModuleManager) checkDependentModules(moduleName string) error {
	dependents, err := m.configManager.GetDependentModules(moduleName)
	if err != nil {
		return err
	}

	if len(dependents) > 0 {
		return fmt.Errorf("module is required by: %v", dependents)
	}

	return nil
}

// registerModuleEvents 注册模块事件
func (m *DefaultVendorsModuleManager) registerModuleEvents(config *VendorsModuleConfig) {
	m.eventMapper.RegisterModuleEvents(config.Name, config.Events.Publishes, config.Events.Listens)
}

// 辅助函数

// isValidModuleName 验证模块名称格式
func isValidModuleName(name string) bool {
	// 模块名称应该只包含字母、数字、下划线和连字符
	for _, r := range name {
		if !((r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') ||
			 (r >= '0' && r <= '9') || r == '_' || r == '-') {
			return false
		}
	}
	return len(name) > 0
}

// isValidVersion 验证版本格式
func isValidVersion(version string) bool {
	// 简单的版本格式验证，应该是 x.y.z 格式
	parts := strings.Split(version, ".")
	return len(parts) == 3
}
