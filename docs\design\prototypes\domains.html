<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 域名绑定</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome CDN -->
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <!-- 核心CSS文件 -->
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <!-- Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
    <!-- 页面特定样式 -->
    <style>
        /* 通用样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        /* 域名状态样式 */
        .status-active {
            @apply text-green-400 font-medium;
        }
        
        .status-pending {
            @apply text-yellow-400 font-medium;
        }
        
        .status-expired {
            @apply text-red-400 font-medium;
        }
        
        /* 表格样式 */
        .domain-table th {
            @apply py-3 px-4 text-left font-medium text-gray-300 border-b border-gray-700;
        }
        
        .domain-table td {
            @apply py-3 px-4 border-b border-gray-700;
        }
        
        .domain-table tr:hover {
            @apply bg-gray-800/20;
        }
    </style>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10">
        <!-- 侧边栏内容将通过JavaScript从模板加载 -->
    </aside>
    
    <!-- 侧边栏遮罩层 -->
    <div id="sidebarOverlay" class="sidebar-overlay"></div>
    
    <!-- 主内容区 -->
    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <!-- 顶部导航栏 -->
        <header class="header sticky top-0 z-30">
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        
        <!-- 页面内容区 -->
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">域名绑定</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="addDomainBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-plus text-white"></i>
                                </span>
                                添加域名
                            </span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 域名筛选区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap gap-4">
                    <div class="flex-1 min-w-[200px]">
                        <label class="block text-sm font-medium text-gray-400 mb-2">站点</label>
                        <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2.5 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">全部站点</option>
                            <option value="1">主站</option>
                            <option value="2">博客</option>
                            <option value="3">商城</option>
                            <option value="4">论坛</option>
                        </select>
                    </div>
                    <div class="flex-1 min-w-[200px]">
                        <label class="block text-sm font-medium text-gray-400 mb-2">状态</label>
                        <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2.5 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">全部状态</option>
                            <option value="active">已生效</option>
                            <option value="pending">待验证</option>
                            <option value="expired">已过期</option>
                        </select>
                    </div>
                    <div class="flex-1 min-w-[200px]">
                        <label class="block text-sm font-medium text-gray-400 mb-2">搜索</label>
                        <div class="relative">
                            <input type="text" placeholder="搜索域名..." class="w-full bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2.5 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                    </div>
                    <div class="flex items-end">
                        <button class="bg-gray-700 hover:bg-gray-600 text-white px-5 py-2.5 rounded-lg font-medium transition-all">
                            筛选
                        </button>
                    </div>
                </div>
            </div>

            <!-- 域名列表 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6 overflow-x-auto">
                <table class="w-full domain-table">
                    <thead>
                        <tr>
                            <th class="w-12">
                                <input type="checkbox" class="w-4 h-4 rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                            </th>
                            <th>域名</th>
                            <th>所属站点</th>
                            <th>SSL证书</th>
                            <th>状态</th>
                            <th>到期时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <input type="checkbox" class="w-4 h-4 rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                            </td>
                            <td>
                                <div class="font-medium text-white">example.com</div>
                                <div class="text-xs text-gray-400">主域名</div>
                            </td>
                            <td>主站</td>
                            <td>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900/30 text-green-400">
                                    <i class="fas fa-lock mr-1"></i> 已启用
                                </span>
                            </td>
                            <td><span class="status-active">已生效</span></td>
                            <td>2026-05-20</td>
                            <td>
                                <div class="flex space-x-2">
                                    <button class="text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                    <button class="text-red-400 hover:text-red-300">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="w-4 h-4 rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                            </td>
                            <td>
                                <div class="font-medium text-white">blog.example.com</div>
                                <div class="text-xs text-gray-400">子域名</div>
                            </td>
                            <td>博客</td>
                            <td>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900/30 text-green-400">
                                    <i class="fas fa-lock mr-1"></i> 已启用
                                </span>
                            </td>
                            <td><span class="status-active">已生效</span></td>
                            <td>2026-05-20</td>
                            <td>
                                <div class="flex space-x-2">
                                    <button class="text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                    <button class="text-red-400 hover:text-red-300">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="w-4 h-4 rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                            </td>
                            <td>
                                <div class="font-medium text-white">shop.example.com</div>
                                <div class="text-xs text-gray-400">子域名</div>
                            </td>
                            <td>商城</td>
                            <td>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900/30 text-green-400">
                                    <i class="fas fa-lock mr-1"></i> 已启用
                                </span>
                            </td>
                            <td><span class="status-active">已生效</span></td>
                            <td>2026-05-20</td>
                            <td>
                                <div class="flex space-x-2">
                                    <button class="text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                    <button class="text-red-400 hover:text-red-300">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="w-4 h-4 rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                            </td>
                            <td>
                                <div class="font-medium text-white">forum.example.com</div>
                                <div class="text-xs text-gray-400">子域名</div>
                            </td>
                            <td>论坛</td>
                            <td>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-900/30 text-yellow-400">
                                    <i class="fas fa-clock mr-1"></i> 待配置
                                </span>
                            </td>
                            <td><span class="status-pending">待验证</span></td>
                            <td>-</td>
                            <td>
                                <div class="flex space-x-2">
                                    <button class="text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-check-circle"></i>
                                    </button>
                                    <button class="text-red-400 hover:text-red-300">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="w-4 h-4 rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                            </td>
                            <td>
                                <div class="font-medium text-white">old-domain.com</div>
                                <div class="text-xs text-gray-400">重定向域名</div>
                            </td>
                            <td>主站</td>
                            <td>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-900/30 text-red-400">
                                    <i class="fas fa-times-circle mr-1"></i> 已过期
                                </span>
                            </td>
                            <td><span class="status-expired">已过期</span></td>
                            <td>2024-12-31</td>
                            <td>
                                <div class="flex space-x-2">
                                    <button class="text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                    <button class="text-red-400 hover:text-red-300">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- 分页 -->
                <div class="flex justify-between items-center mt-6">
                    <div class="text-sm text-gray-400">
                        显示 1 到 5，共 5 条记录
                    </div>
                    <div class="flex space-x-1">
                        <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="px-3 py-1 rounded bg-blue-600 text-white">
                            1
                        </button>
                        <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>
    
    <!-- 添加域名模态框 -->
    <div id="addDomainModal" class="fixed inset-0 z-50 hidden">
        <div class="absolute inset-0 bg-black/50"></div>
        <div class="relative z-10 max-w-lg w-full mx-auto mt-20 bg-gray-800 rounded-xl shadow-xl">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-white">添加域名</h3>
                    <button id="closeAddDomainModal" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-400 mb-2">域名</label>
                        <input type="text" placeholder="example.com" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2.5 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-400 mb-2">所属站点</label>
                        <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2.5 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="1">主站</option>
                            <option value="2">博客</option>
                            <option value="3">商城</option>
                            <option value="4">论坛</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-400 mb-2">域名类型</label>
                        <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2.5 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="main">主域名</option>
                            <option value="sub">子域名</option>
                            <option value="redirect">重定向域名</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-400 mb-2">SSL证书</label>
                        <div class="flex items-center">
                            <input type="checkbox" id="enableSSL" class="w-4 h-4 rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                            <label for="enableSSL" class="ml-2 text-sm text-gray-300">启用SSL证书</label>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" id="cancelAddDomain" class="px-5 py-2.5 bg-gray-700 hover:bg-gray-600 text-white rounded-lg">
                            取消
                        </button>
                        <button type="submit" class="px-5 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:shadow-lg hover:shadow-blue-500/30">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 核心JS文件 -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <!-- 页面特定脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面
            const addDomainBtn = document.getElementById('addDomainBtn');
            const addDomainModal = document.getElementById('addDomainModal');
            const closeAddDomainModal = document.getElementById('closeAddDomainModal');
            const cancelAddDomain = document.getElementById('cancelAddDomain');
            
            // 打开添加域名模态框
            if (addDomainBtn && addDomainModal) {
                addDomainBtn.addEventListener('click', function() {
                    addDomainModal.classList.remove('hidden');
                });
            }
            
            // 关闭添加域名模态框
            if (closeAddDomainModal && addDomainModal) {
                closeAddDomainModal.addEventListener('click', function() {
                    addDomainModal.classList.add('hidden');
                });
            }
            
            // 取消添加域名
            if (cancelAddDomain && addDomainModal) {
                cancelAddDomain.addEventListener('click', function() {
                    addDomainModal.classList.add('hidden');
                });
            }
            
            // 表格行复选框
            const mainCheckbox = document.querySelector('thead input[type="checkbox"]');
            const rowCheckboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            
            if (mainCheckbox && rowCheckboxes.length > 0) {
                // 全选/取消全选
                mainCheckbox.addEventListener('change', function() {
                    rowCheckboxes.forEach(checkbox => {
                        checkbox.checked = mainCheckbox.checked;
                    });
                });
                
                // 更新主复选框状态
                rowCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        const allChecked = Array.from(rowCheckboxes).every(cb => cb.checked);
                        const someChecked = Array.from(rowCheckboxes).some(cb => cb.checked);
                        
                        mainCheckbox.checked = allChecked;
                        mainCheckbox.indeterminate = someChecked && !allChecked;
                    });
                });
            }
        });
    </script>
</body>
</html>