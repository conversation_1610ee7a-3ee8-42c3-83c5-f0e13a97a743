<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 插件市场</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .card {
            transition: all 0.3s ease;
        }
        
        .btn {
            transition: all 0.3s ease;
        }
        
        .tag {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
        }
        
        .plugin-card {
            transition: all 0.3s ease;
        }
        
        .plugin-preview {
            position: relative;
            overflow: hidden;
            border-radius: 0.5rem 0.5rem 0 0;
        }
        
        .rating {
            color: #FFD700;
        }
        
        .star {
            margin-right: 1px;
        }
        
        .empty {
            color: #4B5563;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm text-gray-400 mb-4">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <span class="mx-2">/</span>
                <a href="plugins.html" class="hover:text-white">插件管理</a>
                <span class="mx-2">/</span>
                <span class="text-white">插件市场</span>
            </div>
            
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">插件市场</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <a href="plugins.html" class="flex items-center justify-center bg-gray-700 hover:bg-gray-600 text-white px-5 py-3 rounded-lg font-medium transition-all">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-puzzle-piece text-white"></i>
                                </span>
                                已安装插件
                            </span>
                        </a>
                        <a href="plugin_dev.html" class="flex items-center justify-center bg-gradient-to-r from-purple-500 to-purple-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-purple-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-code text-white"></i>
                                </span>
                                插件开发
                            </span>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 统计概览 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="flex items-center p-4 bg-gray-800/30 rounded-xl border border-gray-700">
                        <div class="p-3 rounded-full bg-blue-500/20 mr-4">
                            <i class="fas fa-puzzle-piece text-blue-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">插件总数</h3>
                            <p class="text-2xl font-bold">124</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center p-4 bg-gray-800/30 rounded-xl border border-gray-700">
                        <div class="p-3 rounded-full bg-green-500/20 mr-4">
                            <i class="fas fa-check-circle text-green-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">已安装插件</h3>
                            <p class="text-2xl font-bold">8</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center p-4 bg-gray-800/30 rounded-xl border border-gray-700">
                        <div class="p-3 rounded-full bg-yellow-500/20 mr-4">
                            <i class="fas fa-sync-alt text-yellow-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">可更新插件</h3>
                            <p class="text-2xl font-bold">3</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center p-4 bg-gray-800/30 rounded-xl border border-gray-700">
                        <div class="p-3 rounded-full bg-purple-500/20 mr-4">
                            <i class="fas fa-download text-purple-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-gray-400 text-sm">下载总量</h3>
                            <p class="text-2xl font-bold">25.7K</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 主要内容区 -->
            <div class="flex flex-col lg:flex-row gap-6">
                <!-- 左侧筛选区 -->
                <div class="lg:w-1/4">
                    <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5 mb-6">
                        <h3 class="text-lg font-semibold mb-4">搜索</h3>
                        <div class="relative">
                            <input type="text" placeholder="搜索插件..." class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 pl-10 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                    </div>
                    
                    <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5 mb-6">
                        <h3 class="text-lg font-semibold mb-4">插件分类</h3>
                        <ul class="space-y-2">
                            <li>
                                <a href="#" class="flex items-center justify-between text-blue-400 hover:text-blue-300">
                                    <span>全部插件</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">124</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>内容增强</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">28</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>SEO工具</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">22</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>社交媒体</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">19</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>电子商务</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">17</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>安全防护</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">15</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>性能优化</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">13</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center justify-between text-gray-400 hover:text-white">
                                    <span>数据分析</span>
                                    <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">10</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5 mb-6">
                        <h3 class="text-lg font-semibold mb-4">价格</h3>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                <span class="ml-2 text-gray-400">免费</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                <span class="ml-2 text-gray-400">付费</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-5">
                        <h3 class="text-lg font-semibold mb-4">兼容性</h3>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                <span class="ml-2 text-gray-400">GACMS v1.0+</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600" checked>
                                <span class="ml-2 text-gray-400">GACMS v2.0+</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox rounded text-blue-500 focus:ring-blue-500 focus:ring-offset-0 bg-gray-700 border-gray-600">
                                <span class="ml-2 text-gray-400">GACMS v3.0+</span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧插件列表 -->
                <div class="lg:w-3/4">
                    <!-- 排序工具栏 -->
                    <div class="bg-gray-800/20 border border-gray-700 rounded-xl p-4 mb-6 flex flex-wrap items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="text-gray-400">排序:</span>
                            <button class="bg-blue-600 text-white px-3 py-1 rounded-lg text-sm">最新发布</button>
                            <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-1 rounded-lg text-sm">下载最多</button>
                            <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-1 rounded-lg text-sm">评分最高</button>
                        </div>
                        <div class="flex items-center space-x-2 mt-2 sm:mt-0">
                            <span class="text-gray-400">视图:</span>
                            <button class="bg-blue-600 text-white p-1 rounded-lg">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="bg-gray-700 hover:bg-gray-600 text-gray-300 p-1 rounded-lg">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 插件列表 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                        <!-- 插件卡片 1 -->
                        <div class="card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="plugin-card">
                                <div class="plugin-preview">
                                    <img src="https://images.unsplash.com/photo-1555949963-ff9fe0c870eb?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="安全防护盾">
                                    <!-- 图片来源: https://unsplash.com/photos/person-using-macbook-pro-near-white-ceramic-mug-QckxruozjRg -->
                                </div>
                                <div class="p-5">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-white">安全防护盾</h3>
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">¥199</span>
                                    </div>
                                    <div class="flex items-center mb-2">
                                        <div class="rating mr-2">
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                        </div>
                                        <span class="text-gray-400 text-sm">5.0 (128)</span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-3">全面的网站安全防护插件，包含防火墙、恶意代码检测、登录保护和实时监控功能。</p>
                                    <div class="flex flex-wrap mb-4">
                                        <span class="tag bg-red-500/10 text-red-400">安全</span>
                                        <span class="tag bg-blue-500/10 text-blue-400">防护</span>
                                        <span class="tag bg-yellow-500/10 text-yellow-400">监控</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400 text-sm">v3.2.1 | 8.7K 下载</span>
                                        <button class="bg-gradient-to-r from-green-500 to-green-700 text-white px-4 py-1 rounded-lg text-sm hover:shadow-lg hover:shadow-green-500/30 transition-all btn">
                                            购买
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 插件卡片 2 -->
                        <div class="card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="plugin-card">
                                <div class="plugin-preview">
                                    <img src="https://images.unsplash.com/photo-1461749280684-dccba630e2f6?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="内容编辑器增强">
                                    <!-- 图片来源: https://unsplash.com/photos/computer-source-code-display-2EJCSULRwC8 -->
                                </div>
                                <div class="p-5">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-white">内容编辑器增强</h3>
                                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">免费</span>
                                    </div>
                                    <div class="flex items-center mb-2">
                                        <div class="rating mr-2">
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star-half-alt star"></i>
                                        </div>
                                        <span class="text-gray-400 text-sm">4.7 (256)</span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-3">增强内容编辑器功能，提供更多格式选项、媒体管理和排版工具，提升内容创作体验。</p>
                                    <div class="flex flex-wrap mb-4">
                                        <span class="tag bg-blue-500/10 text-blue-400">编辑器</span>
                                        <span class="tag bg-purple-500/10 text-purple-400">内容</span>
                                        <span class="tag bg-green-500/10 text-green-400">排版</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400 text-sm">v2.5.0 | 12.3K 下载</span>
                                        <button class="bg-gradient-to-r from-blue-500 to-blue-700 text-white px-4 py-1 rounded-lg text-sm hover:shadow-lg hover:shadow-blue-500/30 transition-all btn">
                                            安装
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 插件卡片 3 -->
                        <div class="card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="plugin-card">
                                <div class="plugin-preview">
                                    <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="数据分析大师">
                                    <!-- 图片来源: https://unsplash.com/photos/blue-and-white-graph-illustration-FO7JIlwjOtU -->
                                </div>
                                <div class="p-5">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-white">数据分析大师</h3>
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">¥299</span>
                                    </div>
                                    <div class="flex items-center mb-2">
                                        <div class="rating mr-2">
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star empty"></i>
                                        </div>
                                        <span class="text-gray-400 text-sm">4.2 (87)</span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-3">强大的数据分析工具，提供访问统计、用户行为跟踪、转化率分析和自定义报表功能。</p>
                                    <div class="flex flex-wrap mb-4">
                                        <span class="tag bg-blue-500/10 text-blue-400">分析</span>
                                        <span class="tag bg-yellow-500/10 text-yellow-400">统计</span>
                                        <span class="tag bg-purple-500/10 text-purple-400">报表</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400 text-sm">v1.8.3 | 5.4K 下载</span>
                                        <button class="bg-gradient-to-r from-green-500 to-green-700 text-white px-4 py-1 rounded-lg text-sm hover:shadow-lg hover:shadow-green-500/30 transition-all btn">
                                            购买
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 插件卡片 4 -->
                        <div class="card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="plugin-card">
                                <div class="plugin-preview">
                                    <img src="https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="缓存加速器">
                                    <!-- 图片来源: https://unsplash.com/photos/person-using-laptop-computer-82TpEld0_e4 -->
                                </div>
                                <div class="p-5">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-white">缓存加速器</h3>
                                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">免费</span>
                                    </div>
                                    <div class="flex items-center mb-2">
                                        <div class="rating mr-2">
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                        </div>
                                        <span class="text-gray-400 text-sm">4.9 (312)</span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-3">全面的网站缓存解决方案，提供页面缓存、数据库查询缓存和CDN集成，显著提升网站加载速度。</p>
                                    <div class="flex flex-wrap mb-4">
                                        <span class="tag bg-green-500/10 text-green-400">性能</span>
                                        <span class="tag bg-blue-500/10 text-blue-400">缓存</span>
                                        <span class="tag bg-yellow-500/10 text-yellow-400">加速</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400 text-sm">v2.3.5 | 15.8K 下载</span>
                                        <button class="bg-gradient-to-r from-blue-500 to-blue-700 text-white px-4 py-1 rounded-lg text-sm hover:shadow-lg hover:shadow-blue-500/30 transition-all btn">
                                            安装
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 插件卡片 5 -->
                        <div class="card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="plugin-card">
                                <div class="plugin-preview">
                                    <img src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="SEO优化大师">
                                    <!-- 图片来源: https://unsplash.com/photos/person-using-macbook-pro-on-person-s-lap-gcsNOsPEXfs -->
                                </div>
                                <div class="p-5">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-white">SEO优化大师</h3>
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">¥249</span>
                                    </div>
                                    <div class="flex items-center mb-2">
                                        <div class="rating mr-2">
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star-half-alt star"></i>
                                        </div>
                                        <span class="text-gray-400 text-sm">4.6 (175)</span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-3">全面的SEO工具套件，提供关键词分析、内容优化建议、网站诊断和搜索引擎排名跟踪功能。</p>
                                    <div class="flex flex-wrap mb-4">
                                        <span class="tag bg-blue-500/10 text-blue-400">SEO</span>
                                        <span class="tag bg-yellow-500/10 text-yellow-400">优化</span>
                                        <span class="tag bg-purple-500/10 text-purple-400">分析</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400 text-sm">v2.1.4 | 7.2K 下载</span>
                                        <button class="bg-gradient-to-r from-green-500 to-green-700 text-white px-4 py-1 rounded-lg text-sm hover:shadow-lg hover:shadow-green-500/30 transition-all btn">
                                            购买
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 插件卡片 6 -->
                        <div class="card bg-gray-800/20 rounded-xl overflow-hidden">
                            <div class="plugin-card">
                                <div class="plugin-preview">
                                    <img src="https://images.unsplash.com/photo-1563986768609-322da13575f3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="社交媒体连接器">
                                    <!-- 图片来源: https://unsplash.com/photos/blue-and-white-light-digital-wallpaper-8bghKxNU1j0 -->
                                </div>
                                <div class="p-5">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-white">社交媒体连接器</h3>
                                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">免费</span>
                                    </div>
                                    <div class="flex items-center mb-2">
                                        <div class="rating mr-2">
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star star"></i>
                                            <i class="fas fa-star empty"></i>
                                        </div>
                                        <span class="text-gray-400 text-sm">4.1 (203)</span>
                                    </div>
                                    <p class="text-gray-400 text-sm mb-3">将网站与各大社交媒体平台无缝集成，支持内容自动分享、社交登录和互动功能。</p>
                                    <div class="flex flex-wrap mb-4">
                                        <span class="tag bg-green-500/10 text-green-400">社交</span>
                                        <span class="tag bg-blue-500/10 text-blue-400">分享</span>
                                        <span class="tag bg-red-500/10 text-red-400">集成</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-400 text-sm">v1.9.2 | 10.5K 下载</span>
                                        <button class="bg-gradient-to-r from-blue-500 to-blue-700 text-white px-4 py-1 rounded-lg text-sm hover:shadow-lg hover:shadow-blue-500/30 transition-all btn">
                                            安装
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 分页控制 -->
                    <div class="flex justify-between items-center">
                        <div class="text-sm text-gray-400">
                            显示 1-6 / 共 124 个插件
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="px-3 py-1 bg-blue-600 text-white rounded-md">1</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">2</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">3</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">4</button>
                            <button class="px-3 py-1 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 卡片悬停效果
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.classList.add('transform', 'scale-[1.02]');
                });
                card.addEventListener('mouseleave', function() {
                    this.classList.remove('transform', 'scale-[1.02]');
                });
            });
            
            // 按钮悬停效果
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.classList.add('transform', 'scale-[1.05]');
                });
                button.addEventListener('mouseleave', function() {
                    this.classList.remove('transform', 'scale-[1.05]');
                });
            });
        });
    </script>
</body>
</html>