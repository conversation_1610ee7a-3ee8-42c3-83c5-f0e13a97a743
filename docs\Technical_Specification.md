<!--
Author: <PERSON><PERSON>eh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
-->
# 技术规范文档 (Technical Specification) - GACMS

## 目录

- [1. 文档信息](#1-文档信息)
  - [1.1 版本历史](#11-版本历史)
  - [1.2 文档目的](#12-文档目的)
  - [1.3 相关文档引用](#13-相关文档引用)
- [2. 开发规范](#2-开发规范)
  - [2.1 代码风格规范](#21-代码风格规范)
    - [2.1.1 Go代码规范 (Effective Go, Go Code Review Comments)](#211-go代码规范-effective-go-go-code-review-comments)
    - [2.1.2 JavaScript/TypeScript代码规范 (ESL<PERSON>, Prettier)](#212-javascripttypescript代码规范-eslint-prettier)
    - [2.1.3 CSS/SCSS代码规范 (Stylelint, BEM/SMACSS)](#213-cssscss代码规范-stylelint-bemsmacss)
    - [2.1.4 HTML代码规范 (W3C标准, 可访问性)](#214-html代码规范-w3c标准-可访问性)
    - [2.1.5 SQL代码规范](#215-sql代码规范)
    - [2.1.6 JSON/YAML配置文件规范](#216-jsonyaml配置文件规范)
  - [2.2 命名规范](#22-命名规范)
    - [2.2.1 通用命名原则](#221-通用命名原则)
    - [2.2.2 文件与目录命名](#222-文件与目录命名)
    - [2.2.3 Go相关命名](#223-go相关命名)
    - [2.2.4 JavaScript/TypeScript相关命名](#224-javascripttypescript相关命名)
    - [2.2.5 CSS/SCSS相关命名](#225-cssscss相关命名)
    - [2.2.6 数据库对象命名](#226-数据库对象命名)
    - [2.2.7 API Endpoint命名](#227-api-endpoint命名)
  - [2.3 注释规范](#23-注释规范)
    - [2.3.1 文件头部注释](#231-文件头部注释)
    - [2.3.2 类/模块注释](#232-类模块注释)
    - [2.3.3 函数/方法注释](#233-函数方法注释)
    - [2.3.4 行内注释与TODO/FIXME](#234-行内注释与todofixme)
    - [2.3.5 API文档注释 (GoDoc, Swagger/OpenAPI)](#235-api文档注释-godoc-swaggeropenapi)
    - [2.3.6 Git提交信息规范](#236-git提交信息规范)
  - [2.4 版本控制规范 (Git)](#24-版本控制规范-git)
    - [2.4.1 分支管理策略 (Git Flow/GitHub Flow 及多版本并行策略)](#241-分支管理策略-git-flowgithub-flow-及多版本并行策略)
    - [2.4.2 提交信息规范 (Conventional Commits)](#242-提交信息规范-conventional-commits)
    - [2.4.3 版本号规范 (Semantic Versioning 2.0.0)](#243-版本号规范-semantic-versioning-200)
    - [2.4.4 Tagging规范](#244-tagging规范)
    - [2.4.5 .gitignore配置规范](#245-gitignore配置规范)
    - [2.4.6 功能开关 (Feature Flags/Toggles) 实施规范](#246-功能开关-feature-flagstoggles-实施规范)
  - [2.5 项目结构规范](#25-项目结构规范)
    - [2.5.1 后端模块化架构 (Go/Gin)](#251-后端模块化架构-gogin)
    - [2.5.2 前端核心架构 (React)](#252-前端核心架构-react)
    - [2.5.3 配置文件组织与管理](#253-配置文件组织与管理)
    - [2.5.4 静态资源管理](#254-静态资源管理)
  - [2.6 模块开发与集成](#26-模块开发与集成)
    - [2.6.1 核心概念：模块 vs. 插件](#261-核心概念模块-vs-插件)
    - [2.6.2 模块开发规范 (Module Contract)](#262-模块开发规范-module-contract)
    - [2.6.3 模块集成与生命周期](#263-模块集成与生命周期)
- [3. 编码规范](#3-编码规范)
  - [3.1 通用编码原则](#31-通用编码原则)
    - [3.1.1 SOLID原则](#311-solid原则)
    - [3.1.2 DRY原则 (Don't Repeat Yourself)](#312-dry原则-dont-repeat-yourself)
    - [3.1.3 KISS原则 (Keep It Simple, Stupid)](#313-kiss原则-keep-it-simple-stupid)
    - [3.1.4 YAGNI原则 (You Ain't Gonna Need It)](#314-yagni原则-you-aint-gonna-need-it)
    - [3.1.5 LoD原则 (Law of Demeter)](#315-lod原则-law-of-demeter)
    - [3.1.6 Code Readability and Maintainability](#316-code-readability-and-maintainability)
  - [3.2 后端编码规范 (Go)](#32-后端编码规范-go)
    - [3.2.1 控制器 (Controller) 规范](#321-控制器-controller-规范)
    - [3.2.2 模型 (Model) / 实体 (Entity) 规范](#322-模型-model--实体-entity-规范)
    - [3.2.3 服务层 (Service Layer) / 业务逻辑规范](#323-服务层-service-layer--业务逻辑规范)
    - [3.2.4 数据仓库 (Repository) / 数据访问层规范](#324-数据仓库-repository--数据访问层规范)
    - [3.2.5 数据库操作规范 (ORM, Query Builder, SQL)](#325-数据库操作规范-orm-query-builder-sql)
    - [3.2.6 异常处理与错误报告规范](#326-异常处理与错误报告规范)
    - [3.2.7 日志记录规范](#327-日志记录规范)
    - [3.2.8 依赖注入 (Dependency Injection) 使用规范](#328-依赖注入-dependency-injection-使用规范)
    - [3.2.9 配置管理规范](#329-配置管理规范)
    - [3.2.10 异步任务与队列处理规范](#3210-异步任务与队列处理规范)
  - [3.3 前端编码规范 (JavaScript/TypeScript)](#33-前端编码规范-javascripttypescript)
    - [3.3.1 组件设计规范 (Component-Based Architecture)](#331-组件设计规范-component-based-architecture)
    - [3.3.2 状态管理规范 (Vuex, Redux, Zustand等)](#332-状态管理规范-vuex-redux-zustand等)
    - [3.3.3 路由管理规范 (Vue Router, React Router等)](#333-路由管理规范-vue-router-react-router等)
    - [3.3.4 API请求与数据处理规范](#334-api请求与数据处理规范)
    - [3.3.5 DOM操作规范](#335-dom操作规范)
    - [3.3.6 事件处理规范](#336-事件处理规范)
    - [3.3.7 模块化与代码分割规范](#337-模块化与代码分割规范)
    - [3.3.8 性能优化规范 (懒加载, Debounce, Throttle等)](#338-性能优化规范-懒加载-debounce-throttle等)
    - [3.3.9 国际化 (i18n) 与本地化 (l10n) 规范](#339-国际化-i18n-与本地化-l10n-规范)
    - [3.3.10 可访问性 (Accessibility, A11Y) 规范](#3310-可访问性规范)
- [4. 测试规范](#4-测试规范)
  - [4.1 测试策略与测试金字塔](#41-测试策略与测试金字塔)
  - [4.2 单元测试规范 (Go testing, Jest, Mocha)](#42-单元测试规范)
    - [4.2.1 测试命名规范 (testXXX, describe/it)](#421-测试命名规范)
    - [4.2.2 测试用例编写规范 (Arrange-Act-Assert)](#422-测试用例编写规范)
    - [4.2.3 Mock, Stub, Spy使用规范](#423-mock-stub-spy使用规范)
    - [4.2.4 代码覆盖率要求](#424-代码覆盖率要求)
  - [4.3 集成测试规范](#43-集成测试规范)
  - [4.4 端到端 (E2E) 测试规范 (Cypress, Selenium, Playwright)](#44-端到端-e2e-测试规范)
  - [4.5 API测试规范 (Postman, Newman)](#45-api测试规范-postman-newman)
  - [4.6 性能测试规范 (JMeter, k6, LoadRunner)](#46-性能测试规范-jmeter-k6-loadrunner)
  - [4.7 安全测试规范 (OWASP ZAP, Burp Suite)](#47-安全测试规范-owasp-zap-burp-suite)
  - [4.8 可用性测试规范](#48-可用性测试规范)
- [5. API设计规范](#5-api设计规范)
  - [5.1 RESTful API设计原则](#51-restful-api设计原则)
    - [5.1.1 资源命名规范](#511-资源命名规范)
    - [5.1.2 HTTP方法使用规范 (GET, POST, PUT, DELETE, PATCH)](#512-http方法使用规范)
    - [5.1.3 HTTP状态码使用规范](#513-http状态码使用规范)
    - [5.1.4 请求与响应体格式规范 (JSON)](#514-请求与响应体格式规范-json)
    - [5.1.5 统一响应结构](#515-统一响应结构)
  - [5.2 API版本控制规范 (无版本化)](#52-api版本控制规范-无版本化)
  - [5.3 API文档规范 (OpenAPI/Swagger)](#53-api文档规范-openapiswagger)
  - [5.4 API安全规范 (认证, 授权, 输入验证, 输出编码)](#54-api安全规范-认证-授权-输入验证-输出编码)
  - [5.5 API错误处理与异常报告规范](#55-api错误处理与异常报告规范)
  - [5.6 API限流与配额规范](#56-api限流与配额规范)
  - [5.7 API幂等性设计规范](#57-api幂等性设计规范)
  - [5.8 HATEOAS原则 (可选)](#58-hateoas原则-可选)
- [6. 安全规范](#6-安全规范)
  - [6.1 通用安全原则](#61-通用安全原则)
  - [6.2 认证与授权 (Authentication and Authorization)](#62-认证与授权)
  - [6.3 输入验证与输出编码 (Input Validation and Output Encoding)](#63-输入验证与输出编码)
  - [6.4 数据保护 (Data Protection)](#64-数据保护)
  - [6.5 依赖管理 (Dependency Management)](#65-依赖管理)
  - [6.6 日志与监控 (Logging and Monitoring)](#66-日志与监控)
  - [6.7 Web服务器与基础设施安全](#67-web服务器与基础设施安全)
  - [6.8 安全测试与代码审查](#68-安全测试与代码审查)
  - [6.9 应急响应 (Incident Response)](#69-应急响应)
  - [6.10 GACMS 特定安全考虑](#610-gacms-特定安全考虑)
    - [6.10.1 核心系统安全](#6101-核心系统安全)
    - [6.10.2 扩展模块安全](#6102-扩展模块安全)
- [7. 部署规范](#7-部署规范)
  - [7.1 环境定义](#71-环境定义)
  - [7.2 部署策略](#72-部署策略)
  - [7.3 自动化部署 (CI/CD)](#73-自动化部署-cicd)
  - [7.4 构建与打包](#74-构建与打包)
  - [7.5 配置管理](#75-配置管理)
  - [7.6 部署流程](#76-部署流程)
  - [7.7 数据库部署与迁移](#77-数据库部署与迁移)
  - [7.8 监控与日志](#78-监控与日志)
  - [7.9 GACMS 特定部署考虑](#79-gacms-特定部署考虑)
- [8. 文档规范](#8-文档规范)
  - [8.1 文档版本控制](#81-文档版本控制)
  - [8.2 文档结构与格式](#82-文档结构与格式)
  - [8.3 文档审查与更新](#83-文档审查与更新)
  - [8.4 GACMS 特定文档要求](#84-gacms-特定文档要求)
  - [8.5 用户手册与操作指南规范](#85-用户手册与操作指南规范)
  - [8.6 文档版本控制与更新机制](#86-文档版本控制与更新机制)
- [9. 工具与环境规范](#9-工具与环境规范)
  - [9.1 开发工具与IDE规范 (VS Code, GoLand, WebStorm)](#91-开发工具与ide规范-vs-code-goland-webstorm)
  - [9.2 开发环境一致性规范 (Docker, Vagrant)](#92-开发环境一致性规范-docker-vagrant)
  - [9.3 构建工具链规范 (npm/yarn, Go Modules, Webpack/Vite)](#93-构建工具链规范-npmyarn-go-modules-webpackvite)
  - [9.4 测试工具与框架规范](#94-测试工具与框架规范)
  - [9.5 代码质量检查工具规范 (ESLint, Stylelint, golangci-lint, SonarQube)](#95-代码质量检查工具规范-eslint-stylelint-golangci-lint-sonarqube)
  - [9.6 项目管理与协作工具规范 (Jira, Confluence, GitLab/GitHub)](#96-项目管理与协作工具规范-jira-confluence-gitlabgithub)
- [10. 质量控制规范](#10-质量控制规范)
  - [10.1 代码审查 (Code Review) 流程与标准](#101-代码审查-code-review-流程与标准)
  - [10.2 持续集成 (CI) 流程规范](#102-持续集成-ci-流程规范)
  - [10.3 静态代码分析规范](#103-静态代码分析规范)
  - [10.4 动态代码分析规范](#104-动态代码分析规范)
  - [10.5 性能监控与基准测试规范](#105-性能监控与基准测试规范)
  - [10.6 Bug跟踪与管理规范](#106-bug跟踪与管理规范)
  - [10.7 技术债务管理规范](#107-技术债务管理规范)
- [11. 附录](#11-附录)
  - [11.1 术语表](#111-术语表)
  - [11.2 推荐工具列表](#112-推荐工具列表)

---

## 1. 文档信息

### 1.1 版本历史

| 版本号 | 日期       | 作者     | 变更说明                                     |
| ------ | ---------- | -------- | -------------------------------------------- |
| 0.5.0  | {current_date} | Trae AI   | **路径规范**: 根据最终讨论，明确官方模块位于`internal/modules`，第三方模块/插件位于`vendors/{vendor-name}/`。 |
| 0.4.0  | {current_date} | Trae AI   | **架构对齐**: 与ADR-001 v5（模块化核心）架构对齐，明确"万物皆模块"原则，并重构模块化开发规范。 |
| 0.3.0  | 2025-05-18 | Cion Nieh | 与ADR-001 v4架构对齐，重写项目结构、API版本和平台化扩展规范 |
| 0.2.0  | 2025-05-17 | Cion Nieh | 更新文档结构，细化开发规范、编码规范等章节内容 |
| 0.1.0  | 2025-05-17 | Cion Nieh | 初稿创建，定义基本结构                         |

### 1.2 文档目的

本文档旨在为亘安网站内容管理系统 (GACMS) 的设计、开发、测试、部署和维护全生命周期提供一套统一的技术标准、规范和最佳实践指南。其目标是确保项目的高质量交付，提升开发效率，增强系统的可维护性、可扩展性、安全性和稳定性。所有项目参与者（包括开发人员、测试人员、运维人员和项目经理）都应遵循本文档中定义的规范。

### 1.3 相关文档引用

本文档的制定参考了以下项目核心文档，并与之保持一致性：

- **[需求规格说明书 (RSD.md)](RSD.md)**: 定义了系统的功能性和非功能性需求。
- **[系统架构设计文档 (SADD.md)](SADD.md)**: 描述了GACMS的整体架构设计、核心组件和技术选型。
- **[架构决策记录 (ADR-001-project-structure.md)](ADR-001-project-structure.md)**: 记录了v5最终版"模块化核心"架构的决策过程与核心思想。
- **[接口设计文档 (Interface_Design.md)](Interface_Design.md)**: 详细定义了系统各模块间及对外暴露的API接口。
- **[数据模型设计文档 (Data_Model_Design.md)](Data_Model_Design.md)**: 阐述了系统的数据结构和数据库设计。
- **[安全设计文档 (Security_Design.md)](Security_Design.md)**: 规定了系统的安全策略、机制和风险应对措施。
- **[部署架构文档 (Deployment_Architecture.md)](Deployment_Architecture.md)**: 描述了系统的部署拓扑、环境配置和运维方案。
- **[性能设计文档 (Performance_Design.md)](Performance_Design.md)**: 明确了系统的性能目标、优化策略和测试计划。
- **[技术风险评估文档 (Technical_Risk_Assessment.md)](Technical_Risk_Assessment.md)**: 识别并评估了项目可能面临的技术风险及应对策略。

---

## 2. 开发规范

本章节定义了GACMS项目在开发过程中应遵循的各项规范，旨在提高代码质量、团队协作效率和项目的可维护性。

### 2.1 代码风格规范

统一的代码风格是保证代码可读性和可维护性的基础。所有提交到代码库的代码都必须符合以下规范。

#### 2.1.1 Go代码规范 (Effective Go, Go Code Review Comments)

- **基本规范**:
  - 遵循官方的 [Effective Go](https://golang.org/doc/effective_go.html) 文档中的指导原则。
  - 参考 [Go Code Review Comments](https://github.com/golang/go/wiki/CodeReviewComments) 作为代码风格和实践的补充指南。
  - 使用 `gofmt` (或 `goimports`) 自动格式化代码，确保代码风格一致性。
  - 文件编码统一使用 **UTF-8 without BOM**。
  - 包名应为小写、简洁且具有描述性。避免使用下划线或混合大小写。
  - 导出的标识符（变量、常量、函数、类型、方法）首字母大写，未导出的标识符首字母小写。
  - 代码缩进使用 **Tab字符** (通常显示为4个或8个空格，由编辑器配置决定，但`gofmt`会强制使用Tab)。
  - 每行代码长度建议不超过 **80-100个字符**，超过时应合理换行。
  - 所有Go源文件都应包含标准的包声明 `package main` 或 `package mypackage`。
  - 文件头部可以包含构建约束 (build constraints/tags) 如果需要。
- **语言特性**:
  - 充分利用Go语言的特性，如Goroutines和Channels进行并发编程。
  - 使用接口 (interfaces) 实现多态和解耦。
  - 遵循错误处理的最佳实践，显式检查和处理错误返回值。
  - 使用Go Modules进行依赖管理。
  - 利用内置的测试框架 `testing` 编写单元测试和基准测试。
  - 熟悉并使用标准库提供的功能，避免不必要的第三方依赖。

#### 2.1.2 JavaScript/TypeScript代码规范 (ESLint, Prettier)

- **基本规范**:
  - 遵循业界广泛接受的JavaScript/TypeScript编码风格，如 [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript) 或 [Google JavaScript Style Guide](https://google.github.io/styleguide/jsguide.html) (根据项目选型决定并统一)。
  - 使用 **ESLint** 进行代码质量检查和风格约束，配置文件 (`.eslintrc.js` 或 `.eslintrc.json`) 需纳入版本控制。
  - 使用 **Prettier** 进行代码格式化，确保团队成员提交的代码风格一致，配置文件 (`.prettierrc.js` 或 `.prettierrc.json`) 需纳入版本控制。
  - 代码缩进使用 **2个空格**。
  - 文件编码统一使用 **UTF-8**。
  - 推荐使用 ECMAScript 6 (ES6)+ 语法特性。
  - TypeScript项目需遵循其官方推荐的最佳实践，并启用严格的类型检查选项（如 `strict: true`）。
- **模块化**:
  - 使用 ES6 Modules (`import`/`export`)进行模块化开发。
  - 避免使用 CommonJS (`require`/`module.exports`)除非特定场景需要（如Node.js后端）。

#### 2.1.3 CSS/SCSS代码规范 (Stylelint, BEM/SMACSS)

- **基本规范**:
  - 使用 **Stylelint** 进行CSS/SCSS代码质量检查和风格约束，配置文件 (`.stylelintrc.js` 或 `.stylelintrc.json`) 需纳入版本控制。
  - 代码缩进使用 **2个空格**。
  - 文件编码统一使用 **UTF-8**。
  - 颜色值优先使用十六进制表示法（例如 `#FFF`），除非需要透明度则使用 `rgba()`。
  - 属性声明顺序建议遵循一定的逻辑分组（例如：定位 -> 盒模型 ->排版 -> 视觉 -> 其他）。
- **命名约定**:
  - 推荐使用一种成熟的CSS命名方法论，如 **BEM (Block, Element, Modifier)** 或 **SMACSS (Scalable and Modular Architecture for CSS)**，以提高样式的可维护性和复用性。
  - 类名使用小写字母，单词间用短横线 `-` 连接（kebab-case）。
- **SCSS/SASS特定规范**:
  - 合理使用嵌套，避免过深的嵌套层级（建议不超过3-4层）。
  - 变量、混入（Mixin）、函数等应有清晰的命名和组织。
  - 避免在循环或条件语句中生成大量不必要的CSS规则。

#### 2.1.4 HTML代码规范 (W3C标准, 可访问性)

- **基本规范**:
  - 遵循 **W3C HTML5标准**。
  - 代码缩进使用 **2个空格**。
  - 文件编码统一使用 **UTF-8**。
  - 标签和属性名使用小写字母。
  - 属性值使用双引号 `""` 包裹。
  - 合理使用HTML语义化标签（如 `<article>`, `<nav>`, `<aside>`, `<header>`, `<footer>` 等）。
- **可访问性 (A11Y)**:
  - 确保所有内容都是可访问的，遵循WCAG (Web Content Accessibility Guidelines) 标准。
  - 为所有图片提供有意义的 `alt` 属性。
  - 使用正确的ARIA (Accessible Rich Internet Applications) 角色和属性来增强动态内容的可访问性。

#### 2.1.5 SQL代码规范

- **基本规范**:
  - SQL关键字（如 `SELECT`, `FROM`, `WHERE`）大写。
  - 数据库、表、字段名使用小写字母和下划线（snake_case）。
  - 代码缩进使用 **2个空格**。
  - 复杂查询应添加注释说明其业务逻辑。

#### 2.1.6 JSON/YAML配置文件规范

- **基本规范**:
  - 键名使用驼峰式命名（camelCase）或下划线命名（snake_case），并保持项目内统一。
  - 缩进使用 **2个空格**。
  - 字符串使用双引号。
  - YAML文件中避免使用Tab，始终使用空格。

### 2.2 命名规范

清晰、一致的命名是代码自解释性的关键。

#### 2.2.1 通用命名原则

- **清晰性**: 命名应清晰、无歧义地描述其用途。避免使用模糊或过于缩写的名称。
- **一致性**: 在整个项目中保持命名风格的一致性。
- **简洁性**: 在不牺牲清晰性的前提下，保持命名简洁。

#### 2.2.2 文件与目录命名

- 使用小写字母，单词间用短横线 `-` 分隔 (kebab-case)，例如 `user-profile.js`, `api-client`。
- Go项目中的文件名遵循 `snake_case.go` 格式。
- React组件文件使用大驼峰命名 (PascalCase)，例如 `UserProfile.jsx`。

#### 2.2.3 Go相关命名

- 遵循Go社区的惯例，使用简短但有意义的名称。
- 包名：小写，单个单词。
- 变量/函数：驼峰式命名 (camelCase)，首字母根据是否导出决定大小写。
- 接口：通常以 `-er` 结尾，如 `Reader`, `Writer`。
- 结构体：大驼峰命名 (PascalCase)。

#### 2.2.4 JavaScript/TypeScript相关命名

- 变量/函数：驼峰式命名 (camelCase)。
- 常量：全大写，下划线分隔 (UPPER_SNAKE_CASE)。
- 类/组件/类型/接口：大驼峰命名 (PascalCase)。

#### 2.2.5 CSS/SCSS相关命名

- BEM命名法: `.block__element--modifier`
- 类名：小写，短横线分隔 (kebab-case)。
- 变量：小写，短横线分隔 (kebab-case)，并带有前缀（如 `$color-primary`）。

#### 2.2.6 数据库对象命名

- 表名：小写，下划线分隔 (snake_case)，复数形式（如 `users`, `posts`）。
- 字段名：小写，下划线分隔 (snake_case)。
- 索引/约束：遵循 `ix_tablename_fieldname` 或 `fk_tablename_referencestablename` 的格式。

#### 2.2.7 API Endpoint命名

- 使用小写，短横线分隔 (kebab-case)。
- 资源使用复数形式。
- 示例: `/users/{userId}/posts`

### 2.3 注释规范

代码注释的目的是解释"为什么"和"如何"，而不是"是什么"。

#### 2.3.1 文件头部注释

所有源代码文件（`.go`, `.js`, `.ts`, `.css`等）都必须包含一个标准的文件头部注释块。

```
/*
 * @Author: Your Name <<EMAIL>>
 * @Date: YYYY-MM-DD
 * @LastEditors: Your Name
 * @LastEditTime: YYYY-MM-DD
 * @FilePath: /path/to/your/file.ext
 * @Description: A brief description of the file's purpose.
 * 
 * © YYYY GACMS. All rights reserved.
 */
```

#### 2.3.2 类/模块注释

- **Go**: 使用 godoc 格式为导出的类型和包提供文档。
- **JS/TS**: 使用 JSDoc 格式为类和模块提供说明。

#### 2.3.3 函数/方法注释

- 为所有公共/导出的函数和方法提供注释。
- 注释应包含：功能描述、参数说明 (`@param`)、返回值说明 (`@return`)、可能抛出的异常 (`@throws`)。

#### 2.3.4 行内注释与TODO/FIXME

- 对于复杂的业务逻辑或算法，应添加行内注释解释其工作原理。
- 使用 `// TODO:` 标记待完成的功能。
- 使用 `// FIXME:` 标记已知需要修复的问题。

#### 2.3.5 API文档注释 (GoDoc, Swagger/OpenAPI)

- Go后端应使用符合Swagger/OpenAPI规范的注释，以便自动生成API文档。

#### 2.3.6 Git提交信息规范

- 遵循 Conventional Commits 规范。
- 格式: `<type>(<scope>): <subject>`
- 示例: `feat(auth): add jwt authentication`

### 2.4 版本控制规范 (Git)

#### 2.4.1 分支管理策略 (Git Flow/GitHub Flow 及多版本并行策略)

- 主分支:
  - `main`/`master`: 始终保持稳定，对应生产环境的最新版本。
  - `develop`: 开发主分支，集成所有已完成的功能。
- 功能分支:
  - 从 `develop` 分支创建，命名为 `feat/feature-name`。
- 修复分支:
  - 从 `main` 创建，命名为 `fix/issue-name`。

#### 2.4.2 提交信息规范 (Conventional Commits)

见 2.3.6。

#### 2.4.3 版本号规范 (Semantic Versioning 2.0.0)

- 遵循 `MAJOR.MINOR.PATCH` 格式。
- `MAJOR`: 不兼容的API变更。
- `MINOR`: 向后兼容的功能性新增。
- `PATCH`: 向后兼容的问题修正。

#### 2.4.4 Tagging规范

- 每个发布到生产环境的版本都必须创建一个对应的Git tag。
- Tag名称应与版本号一致，如 `v1.2.3`。

#### 2.4.5 .gitignore配置规范

- 使用社区维护的、针对不同语言和框架的 `.gitignore` 模板。
- 包含本地配置文件、依赖目录、构建产物、IDE和系统文件。

#### 2.4.6 功能开关 (Feature Flags/Toggles) 实施规范

- 对于大型或有风险的功能，应使用功能开关进行包裹。
- 功能开关的配置应外部化，方便在不同环境中开启或关闭。

### 2.5 项目结构规范

项目的目录结构是架构思想的直接体现。GACMS采纳在 `ADR-001-project-structure.md (v5)` 中最终确定的、以"万物皆模块"和"物理隔离"为核心的规范。

#### 2.5.1 后端模块化架构 (Go/Gin)

GACMS后端遵循"**微核心 + 模块化**"的架构思想。

- **微核心 (Microkernel)**:
    - 位于`internal/platform/` (或类似) 目录，提供平台级的核心能力，如配置加载、日志、数据库连接池、以及模块管理器本身。
    - **微核心不包含任何业务逻辑**。它只定义模块所需遵循的契约 (`IModule` interface) 和提供给模块使用的核心服务。
- **模块 (Modules)**:
    - **所有业务功能**，无论是官方的还是第三方的，都必须作为独立的模块实现。
    - **官方模块**: 存放在 `internal/modules/` 目录下，作为GACMS的核心组成部分，受`internal`机制保护。
    - **第三方模块**: 存放在根目录的 `vendors/{vendor-name}/modules/` 目录下，以实现清晰的物理隔离和供应商管理。
    - 每个模块自身都是一个**高度内聚、低耦合的迷你应用**。

**最终目录结构示例:**
```
GACMS/
├── cmd/gacms/main.go
├── configs/
├── internal/
│   ├── modules/             // 官方核心模块
│   │   ├── user/
│   │   └── post/
│   └── platform/            // 平台微核心
├── pkg/
├── vendors/                 // 第三方供应商根目录
│   ├── gacms-community/     // 示例：GACMS社区
│   │   ├── modules/
│   │   │   └── forum/
│   │   └── plugins/
│   │       └── seo-optimizer/
│   └── another-dev/
│       └── modules/
│           └── shop/
//...
```

#### 2.5.2 前端核心架构 (React)

前端架构采用**功能优先 (Feature-First)** 的目录结构，与后端模块的划分保持逻辑一致。

- **核心思想**:
  采用**功能优先 (Feature-First)** 的目录结构，与后端领域划分（如Admin, Member, Post）保持一致，实现高度内聚。同时借鉴后端分层思想，使架构清晰。

- **目录结构 (`web/admin/` 或 `web/frontend/`)**:
  ```
  src/
  ├── application/       // 应用级 (路由, 全局状态, 布局)
  │   ├── router/
  │   ├── store/         // 全局状态 (Redux)
  │   └── ...
  ├── domain/            // 领域/功能模块
  │   ├── admin/         // 后台管理员模块
  │   │   ├── pages/       // 页面 (e.g., AdminListPage)
  │   │   ├── components/  // 模块内可复用组件
  │   │   └── service.js
  │   ├── member/        // 前台会员模块
  │   └── ...
  ├── infrastructure/    // 基础设施 (通用能力)
  │   ├── api/           // API客户端
  │   ├── components/    // 通用UI组件 (e.g., Button)
  │   └── hooks/         // 通用Hooks
  └── index.js           // 应用入口
  ```

#### 2.5.3 配置文件组织与管理

- 配置文件应集中存放在 `configs/` 目录下。
- 使用 `.yaml` 或 `.toml` 格式，并提供 `config.example.yaml` 作为模板。
- 严禁将敏感信息（如密码、API密钥）硬编码在代码中或提交到版本库。应通过环境变量或安全的配置管理服务注入。

#### 2.5.4 静态资源管理

- 前端静态资源（如图片、字体）由Webpack/Vite等构建工具管理。
- 用户上传的文件（uploads）等动态静态资源，应存储在独立的、可通过CDN访问的存储服务中，路径由配置决定。

### 2.6 模块开发与集成

#### 2.6.1 核心概念：模块 vs. 插件

为提供清晰的扩展能力，GACMS严格区分两种扩展机制：

- **模块 (Module)**:
    - **定义**: 一个**功能完整、相对独立**的业务单元。
    - **用途**: 添加**全新的、大型的功能**（如论坛、商城）。
    - **存放路径**:
        - **官方模块**: `internal/modules/`
        - **第三方模块**: `vendors/{vendor-name}/modules/`
- **插件 (Plugin)**:
    - **定义**: 一个用于**监听和修改**现有模块或平台核心行为的**轻量级**代码片段。
    - **用途**: 对现有功能进行微调、扩展或集成（如发送邮件、社交分享）。
    - **存放路径**:
        - **官方插件**: (待定, e.g., `internal/plugins/`)
        - **第三方插件**: `vendors/{vendor-name}/plugins/`
    - *注：插件系统的具体规范将在 `ADR-002-plugin-system.md` (待创建) 中详细定义。*

#### 2.6.2 模块开发规范 (Module Contract)

所有模块（无论是官方还是第三方）都必须实现平台定义的 `IModule` 接口契约。

- **核心契约 (示例 `internal/platform/contract/module.go`)**:
  ```go
  package contract

  type ICore interface {
      // 获取数据库连接、配置等核心服务
  }

  type IModule interface {
      Name() string
      Init(core ICore) error
      RegisterRoutes(router *gin.Engine)
      // 其他生命周期方法，如 Start(), Stop()
  }
  ```
- **开发流程**: 模块开发者在一个独立的包（如`internal/modules/forum`）中创建一个`module.go`文件，并实现`IModule`接口。

#### 2.6.3 模块集成与生命周期

- **注册 (Registration)**: 所有需要启用的模块都必须在应用入口 (`cmd/gacms/main.go`) 的模块管理器中进行注册。
- **初始化 (Initialization)**: 应用启动时，模块管理器会遍历所有已注册的模块，依次调用其`Init()`方法，将平台核心服务注入给模块。
- **路由注册 (Route Registration)**: 模块管理器接着调用每个模块的`RegisterRoutes()`方法，将模块的API路由注册到全局Gin路由器中。

---

## 3. 编码规范

本章节定义了GACMS项目在编码过程中应遵循的各项原则，旨在提升代码质量和可维护性。

### 3.1 通用编码原则

#### 3.1.1 SOLID原则

- **S (Single Responsibility Principle)**: 单一职责原则。每个类或模块只应有一个改变的理由。
- **O (Open/Closed Principle)**: 开放封闭原则。软件实体（类、模块、函数等）应该对扩展开放，对修改封闭。
- **L (Liskov Substitution Principle)**: 里氏替换原则。子类型必须能够替换掉它们的基类型。
- **I (Interface Segregation Principle)**: 接口隔离原则。不应强迫客户端依赖于它们不使用的方法。
- **D (Dependency Inversion Principle)**: 依赖倒置原则。高层模块不应依赖于低层模块，二者都应依赖于抽象。抽象不应依赖于细节，细节应依赖于抽象。

#### 3.1.2 DRY原则 (Don't Repeat Yourself)

避免在代码中重复相同的逻辑。应通过函数、类或模块来封装和复用。

#### 3.1.3 KISS原则 (Keep It Simple, Stupid)

保持设计和代码简单。简单的解决方案通常比复杂的更容易理解、维护和调试。

#### 3.1.4 YAGNI原则 (You Ain't Gonna Need It)

除非确实需要，否则不要添加功能。避免过度工程化。

#### 3.1.5 LoD原则 (Law of Demeter)

迪米特法则，或最少知识原则。一个对象应该对其他对象有尽可能少的了解。

#### 3.1.6 Code Readability and Maintainability

代码是写给人读的，顺便给机器执行。始终以可读性和可维护性为首要目标。

### 3.2 后端编码规范 (Go)

#### 3.2.1 控制器 (Controller) 规范

- 职责单一：控制器只负责解析HTTP请求、验证输入（基础格式）、调用应用服务、并根据服务返回结果构造HTTP响应。
- 保持轻薄：严禁在控制器中包含业务逻辑。
- DTO使用：控制器接收和返回的都应该是DTO（数据传输对象），而不是领域模型。

#### 3.2.2 模型 (Model) / 实体 (Entity) 规范

- 领域模型是业务的核心，应包含业务属性和业务行为。
- 模型应是富含业务逻辑的"充血模型"，而不是只有get/set方法的"贫血模型"。
- 模型不应依赖任何外部框架或库。

#### 3.2.3 服务层 (Service Layer) / 业务逻辑规范

- 应用服务负责编排和协调领域模型以完成一个完整的业务用例。
- 服务应该是事务的边界。
- 服务可以依赖领域仓库接口，但不能依赖具体实现。

#### 3.2.4 数据仓库 (Repository) / 数据访问层规范

- 仓库负责领域模型的持久化和检索，充当领域层和基础设施层之间的中介。
- 仓库接口在领域层定义，具体实现在基础设施层。
- 仓库的实现（如使用GORM）对领域层透明。

#### 3.2.5 数据库操作规范 (ORM, Query Builder, SQL)

- 优先使用GORM进行数据库操作，以保证代码的类型安全和可读性。
- 对于复杂的查询，可以使用Query Builder或原生SQL，但应封装在仓库实现中。
- 严禁在SQL语句中拼接字符串，必须使用参数化查询防止SQL注入。

#### 3.2.6 异常处理与错误报告规范

- 使用Go的`error`类型进行错误传递，而不是`panic`。
- 在应用的最顶层（如中间件）统一捕获错误，并转换为合适的HTTP响应。
- 记录详细的错误日志，包含堆栈信息，以便于调试。

#### 3.2.7 日志记录规范

- 使用结构化日志（如JSON格式）。
- 日志应包含时间戳、级别、消息和上下文信息。
- 严禁在日志中记录敏感信息（如密码、Token）。

#### 3.2.8 依赖注入 (Dependency Injection) 使用规范

- 在`cmd/gacms/Main.go`中进行依赖的组装和注入。
- 遵循DIP原则，依赖抽象（接口）而不是具体实现。

#### 3.2.9 配置管理规范

- 使用`viper`等库进行配置管理。
- 配置应支持从文件、环境变量等多种来源加载。

#### 3.2.10 异步任务与队列处理规范

- 对于耗时操作（如发送邮件、数据处理），应使用异步任务队列处理。
- 推荐使用`Asynq`等成熟的库。

### 3.3 前端编码规范 (JavaScript/TypeScript)

#### 3.3.1 组件设计规范 (Component-Based Architecture)

- 遵循单一职责原则，每个组件只做一件事。
- 区分容器组件（处理逻辑）和展示组件（处理UI）。
- 优先使用函数组件和Hooks。

#### 3.3.2 状态管理规范 (Vuex, Redux, Zustand等)

- 全局状态使用Redux进行管理。
- 组件内部状态使用`useState`。
- 异步操作使用Redux Thunk或Redux Saga。

#### 3.3.3 路由管理规范 (Vue Router, React Router等)

- 使用React Router进行路由管理。
- 路由定义应集中管理，并支持代码分割和懒加载。

#### 3.3.4 API请求与数据处理规范

- 封装统一的API请求客户端（如使用Axios）。
- 在API层处理请求/响应拦截、错误处理等。
- 使用React Query或SWR进行数据获取、缓存和同步。

#### 3.3.5 DOM操作规范

- 严禁直接操作DOM，应通过React的状态驱动UI更新。

#### 3.3.6 事件处理规范

- 事件处理函数应清晰命名，如`handleLoginClick`。
- 及时在组件卸载时清理事件监听器。

#### 3.3.7 模块化与代码分割规范

- 使用ES6模块。
- 利用Webpack/Vite和React.lazy进行代码分割，实现按需加载。

#### 3.3.8 性能优化规范 (懒加载, Debounce, Throttle等)

- 图片和组件使用懒加载。
- 对高频触发的事件使用Debounce（防抖）或Throttle（节流）。
- 使用`React.memo`和`useCallback`避免不必要的重渲染。

#### 3.3.9 国际化 (i18n) 与本地化 (l10n) 规范

- 使用`react-i18next`等库实现国际化。
- 文本内容应存储在语言文件中，而不是硬编码在组件中。

#### 3.3.10 可访问性 (Accessibility, A11Y) 规范

- 遵循WCAG标准。
- 使用语义化HTML标签。
- 为所有交互元素提供键盘可访问性。
- 为图片提供`alt`文本。

---

## 4. 测试规范

### 4.1 测试策略与测试金字塔

遵循测试金字塔策略，重点是大量的单元测试，适量的集成测试，和少量的端到端测试。

### 4.2 单元测试规范 (Go testing, Jest, Mocha)

#### 4.2.1 测试命名规范

- Go: `TestXxx` (Xxx为被测试函数名)
- JS/TS: 文件命名为 `*.test.js` 或 `*.spec.js`，使用`describe`和`it`块。

#### 4.2.2 测试用例编写规范 (Arrange-Act-Assert)

- **Arrange**: 准备测试数据和环境。
- **Act**: 执行被测试的代码。
- **Assert**: 验证结果是否符合预期。

#### 4.2.3 Mock, Stub, Spy使用规范

- 使用`gomock`或`testify/mock`进行Go的mock。
- 使用`Jest`内置的mock功能进行JS/TS的mock。
- 清晰地区分Mock（模拟对象）、Stub（存根）和Spy（间谍）。

#### 4.2.4 代码覆盖率要求

- 核心业务逻辑的单元测试覆盖率应达到80%以上。

### 4.3 集成测试规范

- 测试多个组件协同工作的场景。
- 通常需要真实的服务依赖，如数据库、缓存。

### 4.4 端到端 (E2E) 测试规范 (Cypress, Selenium, Playwright)

- 模拟真实用户操作，测试完整的业务流程。
- 使用Cypress或Playwright。

### 4.5 API测试规范 (Postman, Newman)

- 编写Postman集合来测试API的正确性。
- 使用Newman将API测试集成到CI/CD流程中。

### 4.6 性能测试规范 (JMeter, k6, LoadRunner)

- 使用k6或JMeter对关键接口和业务流程进行压力测试和基准测试。

### 4.7 安全测试规范 (OWASP ZAP, Burp Suite)

- 定期使用DAST（动态应用安全测试）工具扫描应用。
- 结合SAST（静态应用安全测试）工具，如`gosec`。

### 4.8 可用性测试规范

- 邀请真实用户参与，测试产品的易用性。

---

## 5. API设计规范

### 5.1 RESTful API设计原则

#### 5.1.1 资源命名规范

- URL路径代表资源，应使用名词而不是动词。
- 资源使用复数形式，如 `/users`。
- URL路径使用小写字母和短横线。

#### 5.1.2 HTTP方法使用规范 (GET, POST, PUT, DELETE, PATCH)

- `GET`: 获取资源。
- `POST`: 创建资源。
- `PUT`: 完整更新资源。
- `DELETE`: 删除资源。
- `PATCH`: 部分更新资源。

#### 5.1.3 HTTP状态码使用规范

- `2xx`: 成功 (e.g., `200 OK`, `201 Created`, `204 No Content`)
- `3xx`: 重定向 (e.g., `304 Not Modified`)
- `4xx`: 客户端错误 (e.g., `400 Bad Request`, `401 Unauthorized`, `403 Forbidden`, `404 Not Found`)
- `5xx`: 服务器错误 (e.g., `500 Internal Server Error`)

#### 5.1.4 请求与响应体格式规范 (JSON)

- 所有请求和响应体都应使用`application/json`格式。
- JSON字段命名使用驼峰式 (camelCase)。

#### 5.1.5 统一响应结构

所有API响应都应遵循统一的结构：
```json
{
  "code": 0,
  "message": "Success",
  "data": {}
}
```

### 5.2 API版本控制规范 (无版本化)

**GACMS API遵循"无版本化"策略**。我们始终向后兼容地演进API，不提供多个并存的API版本。

-   **严禁**在URL中使用版本标识（如 `/api/v1/...`）。
-   所有客户端都应使用当前最新版本的API。
-   对API的任何不兼容变更都必须在项目的主要版本升级中进行，并提供详细的迁移指南。

### 5.3 API文档规范 (OpenAPI/Swagger)

- 使用Swagger/OpenAPI 3.0规范编写API文档。
- 在Go代码中添加注释，使用`swaggo`等工具自动生成文档。

### 5.4 API安全规范 (认证, 授权, 输入验证, 输出编码)

- 认证使用JWT (JSON Web Tokens)。
- 授权基于角色的访问控制 (RBAC)。
- 对所有用户输入进行严格验证。
- 对所有输出到客户端的数据进行编码，防止XSS。

### 5.5 API错误处理与异常报告规范

- API应返回统一的错误响应结构。
- 将详细的错误信息记录到日志中，而不是返回给客户端。

### 5.6 API限流与配额规范

- 对关键API实施基于IP或用户的速率限制，防止滥用。

### 5.7 API幂等性设计规范

- `GET`, `PUT`, `DELETE`请求应是幂等的。
- `POST`请求可以通过在客户端生成唯一ID并由服务器检查来保证幂等性。

### 5.8 HATEOAS原则 (可选)

- 在API响应中可以包含相关操作的链接，以提高API的自发现能力。

---

## 6. 安全规范

### 6.1 通用安全原则

- **深度防御**: 采用多层安全措施。
- **最小权限**: 每个组件或用户只应拥有完成其任务所必需的最小权限。
- **默认安全**: 默认配置应是安全的。

### 6.2 认证与授权 (Authentication and Authorization)

- 使用强密码策略。
- 密码存储使用`bcrypt`等强哈希算法。
- JWT应设置合理的过期时间，并使用安全的签名算法。

### 6.3 输入验证与输出编码 (Input Validation and Output Encoding)

- 绝不信任任何用户输入。
- 在服务端对所有输入进行白名单验证。
- 对所有输出到HTML的动态数据进行HTML实体编码。

### 6.4 数据保护 (Data Protection)

- 敏感数据在传输过程中应使用TLS加密。
- 敏感数据在存储时应进行加密。
- 遵守数据保护法规，如GDPR。

### 6.5 依赖管理 (Dependency Management)

- 定期扫描项目依赖，修复已知的安全漏洞。
- 使用`go.sum`和`package-lock.json`锁定依赖版本。

### 6.6 日志与监控 (Logging and Monitoring)

- 记录所有安全相关的事件，如登录失败、权限变更。
- 设置告警，以及时发现可疑活动。

### 6.7 Web服务器与基础设施安全

- 及时更新服务器和软件补丁。
- 配置防火墙，只开放必要的端口。
- 使用HTTPS。

### 6.8 安全测试与代码审查

- 将安全测试集成到CI/CD流程中。
- 代码审查应包含安全方面的检查。

### 6.9 应急响应 (Incident Response)

- 制定安全事件应急响应计划。
- 定期进行演练。

### 6.10 GACMS 特定安全考虑

#### 6.10.1 核心系统安全
- **用户物理隔离**: 必须在数据库层面保证`admins`（后台用户）和`members`（前台用户）表的物理隔离，防止任何形式的权限交叉或数据泄露。相关的认证和授权逻辑也必须完全独立。
- **依赖安全**: 定期使用`govulncheck`等工具扫描后端依赖，使用`npm audit`或类似工具扫描前端依赖，及时修复已知的安全漏洞。

#### 6.10.2 扩展模块安全
- **沙箱化执行**: 模块通过`CoreServices`接口与核心系统交互，其权限被严格限制在该接口暴露的能力范围内。
- **数据访问隔离**: 模块只能访问GACMS核心数据库连接，但应只操作其自身创建的数据表。严禁模块直接操作核心数据表。
- **API安全**: 模块注册的API同样受GACMS的全局中间件（如认证、限流）保护。模块开发者有责任对其自定义的API进行输入验证和权限检查。
- **代码审查**: 引入第三方模块前，必须对其源代码进行安全审查，评估其安全性。

---

## 7. 部署规范

### 7.1 环境定义

- `development`: 本地开发环境。
- `testing`: 测试环境。
- `staging`: 预生产环境。
- `production`: 生产环境。

### 7.2 部署策略

- 采用蓝绿部署或金丝雀发布，以实现零停机部署。

### 7.3 自动化部署 (CI/CD)

- 使用GitHub Actions或GitLab CI/CD。
- CI流程应包括：代码检查、单元测试、集成测试、构建。
- CD流程应实现自动化部署到各个环境。

### 7.4 构建与打包

- Go后端编译为单个二进制文件。
- 前端使用Webpack/Vite打包为静态文件。
- 使用Docker将应用容器化。

### 7.5 配置管理

- 使用环境变量或Consul/Etcd等配置中心进行配置管理。
- 严禁将配置硬编码在代码或镜像中。

### 7.6 部署流程

1. 开发者提交代码到功能分支。
2. 创建Pull Request到`develop`分支。
3. 触发CI流程。
4. Code Review通过后，合并到`develop`分支。
5. 自动部署到`testing`环境。
6. 测试通过后，创建发布分支，并合并到`main`分支。
7. 创建Git Tag，触发CD流程部署到`production`环境。

### 7.7 数据库部署与迁移

- 使用`migrate`或GORM的迁移功能管理数据库Schema变更。
- 数据库迁移应是部署流程的一部分。

### 7.8 监控与日志

- 使用Prometheus进行性能监控。
- 使用ELK/Loki/Grafana进行日志聚合与分析。
- 设置关键指标的告警。

### 7.9 GACMS 特定部署考虑

- **模块化部署**: `cmd/gacms/Main.go`是应用的唯一入口，也是模块注册中心。部署时，可以通过修改此文件来决定加载哪些扩展模块，实现功能的动态组合。
- **数据库迁移**: 核心系统和各个模块的数据库迁移脚本应独立管理。部署流程需要确保在应用启动前，依次执行核心系统及所有已启用模块的数据库迁移。
- **配置分离**: 核心系统配置与各模块的特定配置应在配置文件中清晰分离，例如使用独立的顶级键（`core: ...`, `forum_module: ...`）。

---

## 8. 文档规范

### 8.1 文档版本控制

- 所有文档都应纳入Git版本控制。

### 8.2 文档结构与格式

- 使用Markdown格式。
- 文档应结构清晰，包含目录。

### 8.3 文档审查与更新

- 文档应与代码同步更新。
- 重大变更需要进行文档审查。

### 8.4 GACMS 特定文档要求

- **ADR (架构决策记录)**: 所有重要的架构决策都必须通过创建ADR进行记录和归档。
- **模块文档**: 每个扩展模块的开发者都有责任提供一份详细的`README.md`，说明模块的功能、配置选项、API端点以及如何安装和使用。
- **API文档**: API文档应通过代码注释（如GoDoc+Swagger）自动生成，确保与代码实现同步。

### 8.5 用户手册与操作指南规范

- 为最终用户提供清晰、易懂的操作手册。

### 8.6 文档版本控制与更新机制

- 文档的更新应与相关的功能开发或代码变更在同一个Pull Request中进行。

---

## 9. 工具与环境规范

### 9.1 开发工具与IDE规范 (VS Code, GoLand, WebStorm)

- 推荐使用GoLand/WebStorm或带有相关插件的VS Code。
- 必须安装并启用ESLint, Prettier, gofmt等插件。

### 9.2 开发环境一致性规范 (Docker, Vagrant)

- 使用Docker和Docker Compose来保证开发、测试和生产环境的一致性。

### 9.3 构建工具链规范 (npm/yarn, Go Modules, Webpack/Vite)

- 后端使用Go Modules。
- 前端使用npm/yarn和Vite。

### 9.4 测试工具与框架规范

- 见第4章 测试规范。

### 9.5 代码质量检查工具规范 (ESLint, Stylelint, golangci-lint, SonarQube)

- 使用`golangci-lint`作为Go的linter。
- 使用ESLint和Stylelint作为前端的linter。
- 可以集成SonarQube进行持续的代码质量监控。

### 9.6 项目管理与协作工具规范 (Jira, Confluence, GitLab/GitHub)

- 使用GitHub进行代码托管和项目管理。
- 使用GitHub Issues进行任务和Bug跟踪。

---

## 10. 质量控制规范

### 10.1 代码审查 (Code Review) 流程与标准

- 所有代码变更都必须通过Pull Request进行，并至少需要一名其他开发者审查批准。
- 审查标准：是否符合设计、是否遵循编码规范、是否有潜在的Bug、测试是否充分。

### 10.2 持续集成 (CI) 流程规范

- CI流程必须包含代码风格检查、静态代码分析、单元测试和构建。

### 10.3 静态代码分析规范

- 见9.5。

### 10.4 动态代码分析规范

- 见4.7。

### 10.5 性能监控与基准测试规范

- 定期运行基准测试，监控性能变化趋势。

### 10.6 Bug跟踪与管理规范

- 使用GitHub Issues进行Bug管理。
- Bug报告应包含清晰的复现步骤、期望结果和实际结果。

### 10.7 技术债务管理规范

- 使用`// TODO:`和`// FIXME:`标记技术债务。
- 定期规划时间偿还技术债务。

---

## 11. 附录

### 11.1 术语表

- **ADR**: Architecture Decision Record (架构决策记录)
- **CI/CD**: Continuous Integration / Continuous Deployment (持续集成/持续部署)
- **DDD**: Domain-Driven Design (领域驱动设计)
- **DTO**: Data Transfer Object (数据传输对象)
- **ORM**: Object-Relational Mapping (对象关系映射)
- **RBAC**: Role-Based Access Control (基于角色的访问控制)

### 11.2 推荐工具列表

- **IDE**: GoLand, WebStorm, VS Code
- **版本控制**: Git, GitHub
- **CI/CD**: GitHub Actions
- **容器化**: Docker, Docker Compose
- **项目管理**: GitHub Issues
- **测试**: Go testing, Jest, Cypress
- **监控**: Prometheus, Grafana
- **日志**: ELK Stack / Loki