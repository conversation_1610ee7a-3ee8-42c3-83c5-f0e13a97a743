/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/port/cli/ListModules.go
 * @Description: Defines the 'module:list' command.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package cli

import (
	"fmt"
	"gacms/internal/modules/system/application/service"
	"os"

	"github.com/olekukonko/tablewriter"
	"github.com/spf13/cobra"
)

// NewListModulesCmd creates the 'module:list' command.
func NewListModulesCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "module:list",
		Short: "List all available modules and their status.",
		RunE: func(cmd *cobra.Command, args []string) error {
			moduleService := service.NewModuleCommandService()
			modules, err := moduleService.List()
			if err != nil {
				return fmt.Errorf("failed to list modules: %w", err)
			}

			table := tablewriter.NewWriter(os.Stdout)
			table.SetHeader([]string{"Name", "Version", "Enabled", "Description"})
			table.SetBorder(true)
			table.SetRowLine(true)

			for _, mod := range modules {
				enabledStr := "No"
				if mod.Enabled {
					enabledStr = "Yes"
				}
				row := []string{mod.Name, mod.Version, enabledStr, mod.Description}
				table.Append(row)
			}

			table.Render()
			return nil
		},
	}
	return cmd
} 