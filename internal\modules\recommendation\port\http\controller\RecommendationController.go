/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/recommendation/port/http/controller/RecommendationController.go
 * @Description: Controller for content recommendations.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package controller

import (
	"gacms/internal/modules/recommendation/application/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type RecommendationController struct {
	svc *service.RecommendationService
}

func NewRecommendationController(svc *service.RecommendationService) *RecommendationController {
	return &RecommendationController{svc: svc}
}

func (c *RecommendationController) RegisterRoutes(rg *gin.RouterGroup) {
	// e.g., /api/public/recommendations/sites/1/items/123
	recRoutes := rg.Group("/recommendations/sites/:siteId/items/:itemId")
	{
		recRoutes.GET("", c.getRecommendations)
	}
}

func (c *RecommendationController) getRecommendations(ctx *gin.Context) {
	siteID, err := strconv.ParseUint(ctx.Param("siteId"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid site ID"})
		return
	}
	itemID, err := strconv.ParseUint(ctx.Param("itemId"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid item ID"})
		return
	}

	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "5"))

	result, err := c.svc.GetRelatedContent(uint(siteID), uint(itemID), limit)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": result})
} 