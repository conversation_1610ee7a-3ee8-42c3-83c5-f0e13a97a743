/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/di/EventModule.go
 * @Description: 事件系统的依赖注入模块
 * 
 * © 2025 GACMS. All rights reserved.
 */

package di

import (
	"gacms/internal/core/bus"
	"gacms/internal/core/service"
	"gacms/pkg/contract"

	"go.uber.org/fx"
)

// EventModule 提供事件系统的所有组件
var EventModule = fx.Options(
	// 提供事件总线
	fx.Provide(
		fx.Annotate(
			bus.NewDefaultEventBus,
			fx.As(new(contract.EventBus)),
		),
	),

	// 提供事件处理器注册表
	fx.Provide(
		fx.Annotate(
			service.NewDefaultEventHandlerRegistry,
			fx.As(new(contract.EventHandlerRegistry)),
		),
	),

	// 提供事件序列化器
	fx.Provide(
		fx.Annotate(
			service.NewDefaultEventSerializer,
			fx.As(new(contract.EventSerializer)),
		),
	),

	// 提供事件存储器
	fx.Provide(
		fx.Annotate(
			service.NewDefaultEventStore,
			fx.As(new(contract.EventStore)),
		),
	),

	// 提供事件分发器
	fx.Provide(
		fx.Annotate(
			service.NewDefaultEventDispatcher,
			fx.As(new(contract.EventDispatcher)),
		),
	),

	// 提供模块事件映射器
	fx.Provide(
		fx.Annotate(
			service.NewDefaultModuleEventMapper,
			fx.As(new(service.ModuleEventMapper)),
		),
	),

	// 提供事件管理器（依赖于上面的组件）
	fx.Provide(
		fx.Annotate(
			service.NewDefaultEventManager,
			fx.As(new(contract.EventManager)),
		),
	),

	// 注册生命周期钩子
	fx.Invoke(registerEventLifecycleHooks),

	// 解决循环依赖：将EventManager注入到ModuleProxyFactory
	fx.Invoke(injectEventManagerToModuleFactory),
)

// registerEventLifecycleHooks 注册事件系统的生命周期钩子
func registerEventLifecycleHooks(
	lc fx.Lifecycle,
	eventBus contract.EventBus,
	eventManager contract.EventManager,
	eventStore contract.EventStore,
	logger fx.Logger,
) {
	lc.Append(fx.Hook{
		OnStart: func(ctx fx.Context) error {
			logger.Info("Starting event system...")
			return nil
		},
		OnStop: func(ctx fx.Context) error {
			logger.Info("Stopping event system...")
			return nil
		},
	})
}

// injectEventManagerToModuleFactory 解决循环依赖：将EventManager注入到ModuleProxyFactory
func injectEventManagerToModuleFactory(
	eventManager contract.EventManager,
	moduleFactory *service.ModuleProxyFactory,
) {
	// 将EventManager注入到ModuleProxyFactory中
	moduleFactory.SetEventManager(eventManager)
}