<!--
© 2025 Clion Nieh. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 现代简约Logo设计</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background-color: #0F172A; /* 深蓝色背景 */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            gap: 60px;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }
        .logo-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 40px;
            width: 100%;
            max-width: 800px;
        }
        .logo-row {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            padding: 30px;
            border-radius: 12px;
            background-color: #1E293B; /* 稍亮的背景 */
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }
        .logo-display {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            gap: 16px;
        }
        .logo-title {
            color: #E2E8F0;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .logo-description {
            color: #94A3B8;
            text-align: center;
            max-width: 600px;
            line-height: 1.6;
        }
        .color-palette {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        .color-swatch {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.1);
        }
        .page-title {
            color: white;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            text-align: center;
        }
        .page-subtitle {
            color: #94A3B8;
            font-size: 16px;
            text-align: center;
            max-width: 600px;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1 class="page-title">GACMS 现代简约Logo设计方案</h1>
    <p class="page-subtitle">专注于简约美、现代感、科技感和设计感的视觉标识</p>

    <div class="logo-container">
        <!-- 方案一：几何简约 -->
        <div class="logo-row">
            <h2 class="logo-title">方案一：几何简约</h2>
            <div class="logo-display">
                <!-- SVG Logo -->
                <svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
                    <!-- 底层圆环 -->
                    <circle cx="30" cy="30" r="28" fill="none" stroke="#0EA5E9" stroke-width="2" opacity="0.7"/>
                    <!-- G字形 -->
                    <path d="M30,10 A20,20 0 1 0 30,50 A20,20 0 0 1 15,30 L30,30 L30,20 L15,20" 
                          fill="none" 
                          stroke="#06B6D4" 
                          stroke-width="4"
                          stroke-linecap="round" />
                    <!-- 点缀元素 -->
                    <circle cx="40" cy="20" r="3" fill="#0EA5E9" />
                </svg>
                
                <!-- 文字部分 -->
                <div style="display: flex; align-items: baseline;">
                    <span style="font-size: 32px; font-weight: 700; color: white; letter-spacing: -0.5px;">GA</span>
                    <span style="font-size: 28px; font-weight: 500; color: #94A3B8; letter-spacing: -0.3px;">CMS</span>
                </div>
            </div>
            <p class="logo-description">
                这个设计使用几何线条勾勒出一个抽象的"G"形，搭配圆环元素表示完整的内容管理生态。简约的线条和明亮的蓝色调传达现代科技感，同时保持视觉的简洁性。
            </p>
            <div class="color-palette">
                <div class="color-swatch" style="background-color: #0EA5E9;"></div>
                <div class="color-swatch" style="background-color: #06B6D4;"></div>
                <div class="color-swatch" style="background-color: white;"></div>
                <div class="color-swatch" style="background-color: #94A3B8;"></div>
            </div>
        </div>

        <!-- 方案二：渐变未来 -->
        <div class="logo-row">
            <h2 class="logo-title">方案二：渐变未来</h2>
            <div class="logo-display">
                <!-- SVG Logo -->
                <svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
                    <!-- 渐变定义 -->
                    <defs>
                        <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="#3B82F6" />
                            <stop offset="100%" stop-color="#10B981" />
                        </linearGradient>
                    </defs>
                    
                    <!-- 主体形状 - 现代化的"G"与"A"的融合 -->
                    <path d="M15,20 L30,10 L45,20 L40,40 L30,50 L20,40 L15,20 Z" fill="none" stroke="url(#gradient1)" stroke-width="2.5" />
                    <path d="M22,25 L30,15 L38,25 L30,45 Z" fill="url(#gradient1)" opacity="0.9" />
                    <path d="M15,20 L30,10 L45,20" fill="none" stroke="url(#gradient1)" stroke-width="3" stroke-linecap="round" />
                </svg>
                
                <!-- 文字部分 -->
                <div style="display: flex; align-items: center;">
                    <span style="font-size: 32px; font-weight: 300; color: white; letter-spacing: 1px;">GACMS</span>
                </div>
            </div>
            <p class="logo-description">
                这个设计采用蓝绿渐变色调和几何多边形，呈现出未来感和科技感。线条更加流畅，形状更具动态，同时保持整体设计的简约和现代感。轻量化的字体进一步强调了现代与简约的美学。
            </p>
            <div class="color-palette">
                <div class="color-swatch" style="background-color: #3B82F6;"></div>
                <div class="color-swatch" style="background-color: #10B981;"></div>
                <div class="color-swatch" style="background-color: white;"></div>
            </div>
        </div>

        <!-- 方案三：极简主义 -->
        <div class="logo-row">
            <h2 class="logo-title">方案三：极简主义</h2>
            <div class="logo-display">
                <!-- SVG Logo -->
                <svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
                    <!-- 主要图形元素 - 极简的"G" -->
                    <rect x="15" y="15" width="30" height="30" fill="none" stroke="#8B5CF6" stroke-width="2.5" rx="5" />
                    <path d="M30,15 L30,45 M15,30 L40,30" stroke="#8B5CF6" stroke-width="2.5" />
                    <circle cx="40" cy="30" r="4" fill="#8B5CF6" />
                </svg>
                
                <!-- 文字部分 -->
                <div style="display: flex; align-items: center;">
                    <span style="font-size: 32px; font-weight: 600; color: white; letter-spacing: -0.5px; font-family: 'SF Pro Display', -apple-system, sans-serif;">GA</span>
                    <span style="font-size: 26px; font-weight: 400; color: #A78BFA; letter-spacing: -0.3px; margin-left: 2px;">CMS</span>
                </div>
            </div>
            <p class="logo-description">
                这个设计采用极简主义风格，使用基本几何形状和精确的线条构建标识。紫色调带来创新感和独特性，简约的布局和留白突出设计感。这个设计体现了"少即是多"的设计哲学。
            </p>
            <div class="color-palette">
                <div class="color-swatch" style="background-color: #8B5CF6;"></div>
                <div class="color-swatch" style="background-color: #A78BFA;"></div>
                <div class="color-swatch" style="background-color: white;"></div>
            </div>
        </div>

        <!-- 方案四：分子结构 -->
        <div class="logo-row">
            <h2 class="logo-title">方案四：分子结构</h2>
            <div class="logo-display">
                <!-- SVG Logo -->
                <svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
                    <!-- 主元素 - 分子/网络结构 -->
                    <circle cx="30" cy="30" r="5" fill="#F43F5E" />
                    <circle cx="45" cy="15" r="3" fill="#F43F5E" opacity="0.8" />
                    <circle cx="15" cy="20" r="3" fill="#F43F5E" opacity="0.8" />
                    <circle cx="20" cy="45" r="3" fill="#F43F5E" opacity="0.8" />
                    <circle cx="42" cy="40" r="3" fill="#F43F5E" opacity="0.8" />
                    
                    <line x1="30" y1="30" x2="45" y2="15" stroke="#F43F5E" stroke-width="1.5" />
                    <line x1="30" y1="30" x2="15" y2="20" stroke="#F43F5E" stroke-width="1.5" />
                    <line x1="30" y1="30" x2="20" y2="45" stroke="#F43F5E" stroke-width="1.5" />
                    <line x1="30" y1="30" x2="42" y2="40" stroke="#F43F5E" stroke-width="1.5" />
                    
                    <!-- G形状轮廓 -->
                    <path d="M15,20 C15,15 20,10 30,10 C40,10 45,15 45,25 C45,35 40,40 30,40 C25,40 20,38 20,32 L30,32" 
                          fill="none" 
                          stroke="#FB7185" 
                          stroke-width="1.5"
                          stroke-linecap="round"
                          opacity="0.7" />
                </svg>
                
                <!-- 文字部分 -->
                <div style="display: flex; align-items: center;">
                    <span style="font-size: 28px; font-weight: 500; color: white; letter-spacing: 0px;">GA</span>
                    <span style="font-size: 24px; font-weight: 400; color: #FB7185; letter-spacing: 0px;">CMS</span>
                </div>
            </div>
            <p class="logo-description">
                这个设计灵感来自分子结构和网络连接，象征内容管理系统中各模块的互联性。红色调带来活力和热情，连接线和节点的排列呈现出科技感和系统化的视觉效果。
            </p>
            <div class="color-palette">
                <div class="color-swatch" style="background-color: #F43F5E;"></div>
                <div class="color-swatch" style="background-color: #FB7185;"></div>
                <div class="color-swatch" style="background-color: white;"></div>
            </div>
        </div>
    </div>

    <p style="color: #94A3B8; margin-top: 30px; font-size: 14px; text-align: center; max-width: 600px;">
        以上设计均为概念示意，最终选定方案后可提供SVG矢量格式文件和相应的品牌规范指南。<br>
        所有设计方案均注重简约美学、现代感、科技感和设计感，同时保持较高的识别度和适应性。
    </p>

</body>
</html> 