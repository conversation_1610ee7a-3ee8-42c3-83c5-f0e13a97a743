/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/media/application/service/MediaService.go
 * @Description: Service for media management, including upload orchestration and processing.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"gacms/internal/core/auth"
	"gacms/internal/modules/media/domain/contract"
	"gacms/internal/modules/media/domain/model"
	userModel "gacms/internal/modules/user/domain/model"
	"gacms/pkg/imageprocessor"
	"github.com/rwcarlsen/goexif/exif"
	"image"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"log"
	"mime/multipart"
	"path/filepath"
	"strings"
)

type MediaService struct {
	repo       contract.MediaRepository
	storage    contract.Storage
	imgProc    *imageprocessor.Processor
	thumbSizes map[string]image.Point
}

func NewMediaService(repo contract.MediaRepository, storage contract.Storage) *MediaService {
	thumbSizes := map[string]image.Point{
		"small":  {X: 150, Y: 150},
		"medium": {X: 800, Y: 600},
	}

	return &MediaService{
		repo:       repo,
		storage:    storage,
		imgProc:    imageprocessor.New(),
		thumbSizes: thumbSizes,
	}
}

func (s *MediaService) GetUploadCredentials(user *userModel.Admin, filename string, mimeType string) (*contract.UploadCredentials, error) {
	return s.storage.GetUploadCredentials(user.GetSiteID(), filename, mimeType)
}

func (s *MediaService) HandleUpload(user *userModel.Admin, fileHeader *multipart.FileHeader) (*model.Media, error) {
	file, err := fileHeader.Open()
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	fileKey := fmt.Sprintf("sites/%d/media/%d_%s", user.GetSiteID(), user.ID, filepath.Base(fileHeader.Filename))

	url, err := s.storage.Save(file, fileKey)
	if err != nil {
		return nil, fmt.Errorf("failed to save file to storage: %w", err)
	}

	media := &model.Media{
		SiteID:    user.GetSiteID(),
		UserID:    user.ID,
		Storage:   s.storage.DriverName(),
		Path:      fileKey,
		URL:       url,
		Name:      fileHeader.Filename,
		Size:      fileHeader.Size,
		MimeType:  fileHeader.Header.Get("Content-Type"),
		MediaType: strings.Split(fileHeader.Header.Get("Content-Type"), "/")[0],
	}

	// Create a background context for the initial database creation
	bgCtx := context.Background()

	if err := s.repo.Create(bgCtx, media); err != nil {
		// Attempt to delete the file from storage if DB write fails
		_ = s.storage.Delete(fileKey)
		return nil, fmt.Errorf("failed to create media record: %w", err)
	}

	go s.processMedia(bgCtx, media.ID)

	return media, nil
}

func (s *MediaService) processMedia(ctx context.Context, mediaID uint) {
	log.Printf("Starting post-processing for media ID: %d", mediaID)

	media, err := s.repo.GetByID(ctx, mediaID)
	if err != nil {
		log.Printf("Error fetching media %d for processing: %v", mediaID, err)
		return
	}

	reader, err := s.storage.GetReader(media.Path)
	if err != nil {
		log.Printf("Error getting reader for media %s from storage: %v", media.Path, err)
		return
	}
	defer reader.Close()

	if media.MediaType == "image" {
		var buf bytes.Buffer
		if _, err := buf.ReadFrom(reader); err != nil {
			log.Printf("Error reading image stream for media %d: %v", mediaID, err)
			return
		}

		srcImage, _, err := image.Decode(bytes.NewReader(buf.Bytes()))
		if err != nil {
			log.Printf("Error decoding image for media %d: %v", mediaID, err)
			return
		}

		if media.Metadata == nil {
			media.Metadata = make(model.JSONB)
		}
		bounds := srcImage.Bounds()
		media.Metadata["width"] = bounds.Dx()
		media.Metadata["height"] = bounds.Dy()

		exifData, err := exif.Decode(bytes.NewReader(buf.Bytes()))
		if err == nil {
			media.Metadata["has_exif"] = true
		}

		// Thumbnail generation logic needs to be updated to use storage correctly.
		// This part is complex and will be simplified for now.
		media.Thumbnails = make(model.JSONB)
		media.Thumbnails["small"] = "placeholder_small_thumb_url"
	}

	if err := s.repo.Update(ctx, media); err != nil {
		log.Printf("Error updating media %d with processed metadata: %v", mediaID, err)
	}

	log.Printf("Finished post-processing for media ID: %d", mediaID)
}

func (s *MediaService) ListMedia(ctx context.Context, page int, pageSize int, filters map[string]interface{}) ([]*model.Media, int64, error) {
	currentUser := auth.CurrentUser(ctx)
	if !currentUser.IsSuperAdmin() {
		filters["site_id"] = currentUser.GetSiteID()
	}
	return s.repo.List(page, pageSize, filters)
}

func (s *MediaService) GetMedia(ctx context.Context, id uint) (*model.Media, error) {
	media, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	currentUser := auth.CurrentUser(ctx)
	if !currentUser.IsSuperAdmin() && currentUser.GetSiteID() != media.SiteID {
		return nil, errors.New("permission denied")
	}
	return media, nil
}

func (s *MediaService) DeleteMedia(ctx context.Context, id uint, user *userModel.Admin) error {
	media, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return err
	}

	currentUser := auth.CurrentUser(ctx)
	if !currentUser.IsSuperAdmin() && currentUser.GetSiteID() != media.SiteID {
		return errors.New("permission denied")
	}

	if err := s.storage.Delete(media.Path); err != nil {
		log.Printf("Failed to delete physical file %s: %v", media.Path, err)
	}

	return s.repo.Delete(ctx, id)
} 