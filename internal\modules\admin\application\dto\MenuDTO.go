/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/admin/application/dto/MenuDTO.go
 * @Description: DTOs for admin menu management.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package dto

// MenuCreateDTO is used for creating a new menu item.
type MenuCreateDTO struct {
	Name         string `json:"name" binding:"required"`
	Path         string `json:"path" binding:"required"`
	Icon         string `json:"icon"`
	ParentID     *uint  `json:"parent_id"`
	SortOrder    int    `json:"sort_order"`
	PermissionID *uint  `json:"permission_id"`
	IsVisible    bool   `json:"is_visible"`
}

// MenuUpdateDTO is used for updating an existing menu item.
type MenuUpdateDTO struct {
	Name         string `json:"name"`
	Path         string `json:"path"`
	Icon         string `json:"icon"`
	ParentID     *uint  `json:"parent_id"`
	SortOrder    *int   `json:"sort_order"`
	PermissionID *uint  `json:"permission_id"`
	IsVisible    *bool  `json:"is_visible"`
} 