<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 系统设置</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }
        
        .content-section-title::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 5rem;
            height: 3px;
            background: linear-gradient(to right, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            background: linear-gradient(145deg, #1e1e1e, #2a2a2a);
            border-left: 4px solid #007bff;
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            backdrop-filter: blur(20px);
            z-index: 1000;
            transform: translateX(120%);
            opacity: 0;
            transition: all 0.2s ease;
            max-width: 350px;
        }
        
        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }
        
        /* 标签页样式 */
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">系统设置</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <a href="#" class="flex items-center justify-center bg-gradient-to-r from-green-500 to-green-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-green-500/30 relative overflow-hidden action-button" id="saveSettingsBtn">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-save text-white"></i>
                                </span>
                                保存设置
                            </span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 设置标签页导航 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-4 mb-6">
                <div class="flex flex-wrap border-b border-gray-700">
                    <button class="tab-button px-6 py-3 text-blue-400 border-b-2 border-blue-400 font-medium" data-tab="basic">
                        <i class="fas fa-cog mr-2"></i>基本设置
                    </button>
                    <button class="tab-button px-6 py-3 text-gray-400 hover:text-gray-200 font-medium" data-tab="seo">
                        <i class="fas fa-search mr-2"></i>SEO设置
                    </button>
                    <button class="tab-button px-6 py-3 text-gray-400 hover:text-gray-200 font-medium" data-tab="email">
                        <i class="fas fa-envelope mr-2"></i>邮件设置
                    </button>
                    <button class="tab-button px-6 py-3 text-gray-400 hover:text-gray-200 font-medium" data-tab="upload">
                        <i class="fas fa-upload mr-2"></i>上传设置
                    </button>
                    <button class="tab-button px-6 py-3 text-gray-400 hover:text-gray-200 font-medium" data-tab="api">
                        <i class="fas fa-plug mr-2"></i>API设置
                    </button>
                </div>
            </div>

            <!-- 基本设置内容 -->
            <div id="basic" class="tab-content active">
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                    <h3 class="text-lg font-bold text-white mb-4">网站基本信息</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">网站名称</label>
                                <input type="text" value="GACMS官方网站" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">网站URL</label>
                                <input type="text" value="https://www.gacms.com" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">管理员邮箱</label>
                                <input type="email" value="<EMAIL>" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                        <div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">网站Logo</label>
                                <div class="flex items-center gap-4">
                                    <div class="w-20 h-20 bg-gray-700 rounded-lg flex items-center justify-center overflow-hidden border border-gray-600">
                                        <img src="./assets/images/logo.svg" alt="Logo" class="w-16 h-16 object-contain">
                                    </div>
                                    <div>
                                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition mb-2 w-full">
                                            <i class="fas fa-upload mr-2"></i>上传新Logo
                                        </button>
                                        <div class="text-xs text-gray-400">建议尺寸: 512 x 512px</div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">网站图标 (Favicon)</label>
                                <div class="flex items-center gap-4">
                                    <div class="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center overflow-hidden border border-gray-600">
                                        <img src="./assets/images/favicon.ico" alt="Favicon" class="w-8 h-8 object-contain">
                                    </div>
                                    <div>
                                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition mb-2">
                                            <i class="fas fa-upload mr-2"></i>上传图标
                                        </button>
                                        <div class="text-xs text-gray-400">建议尺寸: 32 x 32px</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                    <h3 class="text-lg font-bold text-white mb-4">系统配置</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">默认语言</label>
                                <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="zh-CN" selected>简体中文</option>
                                    <option value="en-US">English (US)</option>
                                    <option value="ja-JP">日本語</option>
                                </select>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">时区设置</label>
                                <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="Asia/Shanghai" selected>亚洲/上海 (GMT+8)</option>
                                    <option value="America/New_York">美国/纽约 (GMT-4)</option>
                                    <option value="Europe/London">欧洲/伦敦 (GMT+1)</option>
                                </select>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">日期格式</label>
                                <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="Y-m-d" selected>2025-06-06</option>
                                    <option value="Y/m/d">2025/06/06</option>
                                    <option value="d-m-Y">06-06-2025</option>
                                    <option value="m/d/Y">06/06/2025</option>
                                </select>
                            </div>
                        </div>
                        <div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">调试模式</label>
                                <div class="flex items-center space-x-4">
                                    <label class="flex items-center">
                                        <input type="radio" name="debug_mode" class="form-radio text-blue-500" value="0" checked>
                                        <span class="ml-2">关闭</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="debug_mode" class="form-radio text-blue-500" value="1">
                                        <span class="ml-2">开启</span>
                                    </label>
                                </div>
                                <p class="text-xs text-gray-400 mt-1">开启后将在前台显示错误信息，建议仅在开发环境使用</p>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">网站维护模式</label>
                                <div class="flex items-center space-x-4">
                                    <label class="flex items-center">
                                        <input type="radio" name="maintenance_mode" class="form-radio text-blue-500" value="0" checked>
                                        <span class="ml-2">关闭</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="maintenance_mode" class="form-radio text-blue-500" value="1">
                                        <span class="ml-2">开启</span>
                                    </label>
                                </div>
                                <p class="text-xs text-gray-400 mt-1">开启后除管理员外的访问者将看到维护页面</p>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">页面缓存</label>
                                <div class="flex items-center space-x-4">
                                    <label class="flex items-center">
                                        <input type="radio" name="page_cache" class="form-radio text-blue-500" value="0">
                                        <span class="ml-2">关闭</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="page_cache" class="form-radio text-blue-500" value="1" checked>
                                        <span class="ml-2">开启</span>
                                    </label>
                                </div>
                                <p class="text-xs text-gray-400 mt-1">开启后可提高页面访问速度</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- SEO设置内容 -->
            <div id="seo" class="tab-content">
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                    <h3 class="text-lg font-bold text-white mb-4">搜索引擎优化</h3>
                    <div class="mb-4">
                        <label class="block text-gray-300 mb-2">网站标题 (Title)</label>
                        <input type="text" value="GACMS - 高性能内容管理系统" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <p class="text-xs text-gray-400 mt-1">建议保持在60个字符以内</p>
                    </div>
                    <div class="mb-4">
                        <label class="block text-gray-300 mb-2">网站描述 (Description)</label>
                        <textarea rows="3" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">GACMS 是一款高性能、安全可靠的基于Go+React的现代化内容管理系统，为企业网站和电商平台提供全面解决方案。</textarea>
                        <p class="text-xs text-gray-400 mt-1">建议保持在160个字符以内</p>
                    </div>
                    <div class="mb-4">
                        <label class="block text-gray-300 mb-2">关键词 (Keywords)</label>
                        <input type="text" value="CMS,内容管理,网站建设,Go,React" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <p class="text-xs text-gray-400 mt-1">多个关键词之间使用英文逗号分隔</p>
                    </div>
                    <div class="mb-4">
                        <label class="block text-gray-300 mb-2">站点地图 (Sitemap)</label>
                        <div class="flex items-center space-x-4">
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox text-blue-500" checked>
                                <span class="ml-2">自动生成站点地图</span>
                            </label>
                            <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition">
                                <i class="fas fa-sync-alt mr-2"></i>立即更新
                            </button>
                        </div>
                        <p class="text-xs text-gray-400 mt-1">URL: https://www.gacms.com/sitemap.xml</p>
                    </div>
                </div>
            </div>

            <!-- 邮件设置内容 -->
            <div id="email" class="tab-content">
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                    <h3 class="text-lg font-bold text-white mb-4">邮件服务配置</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">SMTP服务器</label>
                                <input type="text" value="smtp.example.com" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">SMTP端口</label>
                                <input type="text" value="587" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">加密方式</label>
                                <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">无</option>
                                    <option value="ssl">SSL</option>
                                    <option value="tls" selected>TLS</option>
                                </select>
                            </div>
                        </div>
                        <div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">发件人邮箱</label>
                                <input type="email" value="<EMAIL>" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">邮箱账户</label>
                                <input type="text" value="<EMAIL>" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">邮箱密码</label>
                                <input type="password" value="********" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition">
                            <i class="fas fa-paper-plane mr-2"></i>发送测试邮件
                        </button>
                    </div>
                </div>
            </div>

            <!-- 上传设置内容 -->
            <div id="upload" class="tab-content">
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                    <h3 class="text-lg font-bold text-white mb-4">文件上传设置</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">上传目录</label>
                                <input type="text" value="uploads" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">最大上传大小 (MB)</label>
                                <input type="number" value="10" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">允许上传的文件类型</label>
                                <input type="text" value="jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,zip,rar" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <p class="text-xs text-gray-400 mt-1">多种类型之间使用英文逗号分隔</p>
                            </div>
                        </div>
                        <div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">图片处理</label>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" class="form-checkbox text-blue-500" checked>
                                        <span class="ml-2">自动生成缩略图</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="form-checkbox text-blue-500" checked>
                                        <span class="ml-2">优化图片大小</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                        <span class="ml-2">添加水印</span>
                                    </label>
                                </div>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">存储方式</label>
                                <select class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="local" selected>本地存储</option>
                                    <option value="oss">阿里云OSS</option>
                                    <option value="cos">腾讯云COS</option>
                                    <option value="s3">Amazon S3</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- API设置内容 -->
            <div id="api" class="tab-content">
                <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                    <h3 class="text-lg font-bold text-white mb-4">API接口设置</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">启用REST API</label>
                                <div class="flex items-center space-x-4">
                                    <label class="flex items-center">
                                        <input type="radio" name="rest_api" class="form-radio text-blue-500" value="0">
                                        <span class="ml-2">关闭</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="rest_api" class="form-radio text-blue-500" value="1" checked>
                                        <span class="ml-2">开启</span>
                                    </label>
                                </div>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">API访问控制</label>
                                <div class="flex items-center space-x-4">
                                    <label class="flex items-center">
                                        <input type="radio" name="api_access" class="form-radio text-blue-500" value="none">
                                        <span class="ml-2">无限制</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="api_access" class="form-radio text-blue-500" value="key" checked>
                                        <span class="ml-2">API密钥</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="api_access" class="form-radio text-blue-500" value="oauth">
                                        <span class="ml-2">OAuth2</span>
                                    </label>
                                </div>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">API速率限制 (每分钟请求数)</label>
                                <input type="number" value="60" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                        <div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">CORS设置</label>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" class="form-checkbox text-blue-500" checked>
                                        <span class="ml-2">允许跨域请求</span>
                                    </label>
                                </div>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2">允许的源 (Origin)</label>
                                <textarea rows="3" class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent">https://www.example.com
https://api.example.com</textarea>
                                <p class="text-xs text-gray-400 mt-1">每行一个域名，留空表示允许所有源</p>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="api_docs.html" class="inline-flex items-center text-blue-400 hover:text-blue-300">
                            <i class="fas fa-book mr-2"></i>查看API文档
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <!-- 通知组件 -->
    <div class="notification" id="notification">
        <div class="mr-3">
            <i class="fas fa-check-circle text-green-400 text-xl"></i>
        </div>
        <div class="flex-1">
            <h4 class="text-white text-sm font-bold">操作成功</h4>
            <p class="text-gray-300 text-xs">系统设置已保存。</p>
        </div>
    </div>

    <!-- 加载必要的JS -->
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化标签页功能
            function initTabs() {
                const tabButtons = document.querySelectorAll('.tab-button');
                
                tabButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        // 移除所有标签页按钮的活动状态
                        tabButtons.forEach(btn => {
                            btn.classList.remove('text-blue-400', 'border-b-2', 'border-blue-400');
                            btn.classList.add('text-gray-400', 'hover:text-gray-200');
                        });
                        
                        // 添加当前按钮的活动状态
                        this.classList.remove('text-gray-400', 'hover:text-gray-200');
                        this.classList.add('text-blue-400', 'border-b-2', 'border-blue-400');
                        
                        // 隐藏所有标签页内容
                        const tabContents = document.querySelectorAll('.tab-content');
                        tabContents.forEach(content => {
                            content.classList.remove('active');
                        });
                        
                        // 显示当前标签页内容
                        const target = this.getAttribute('data-tab');
                        document.getElementById(target).classList.add('active');
                    });
                });
            }
            
            // 初始化通知功能
            function initNotification() {
                const saveButton = document.getElementById('saveSettingsBtn');
                const notification = document.getElementById('notification');
                
                if (saveButton && notification) {
                    saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        
                        // 显示通知
                        notification.classList.add('show');
                        
                        // 3秒后隐藏通知
                        setTimeout(() => {
                            notification.classList.remove('show');
                        }, 3000);
                    });
                }
            }
            
            // 初始化页面功能
            initTabs();
            initNotification();
        });
    </script>
</body>
</html> 