/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON> <<EMAIL>>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/system/port/http/controller/DomainBindingController.go
 * @Description: Controller for managing Domain Bindings API.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package controller

import (
	"gacms/internal/modules/system/application/dto"
	"gacms/internal/modules/system/application/service"
	"gacms/internal/modules/system/domain/model"
	"gacms/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
	"net/http"
)

type DomainBindingController struct {
	service *service.DomainBindingService
}

func NewDomainBindingController(s *service.DomainBindingService) *DomainBindingController {
	return &DomainBindingController{service: s}
}

// CreateBinding godoc
// @Summary Create a new domain binding
// @Description Binds a domain to a site's module or category
// @Tags System
// @Accept  json
// @Produce  json
// @Param   binding body dto.DomainBindingCreateDTO true "Domain Binding Payload"
// @Success 201 {object} response.Response{data=dto.DomainBindingDTO}
// @Failure 400 {object} response.Response
// @Failure 403 {object} response.Response
// @Router /system/bindings [post]
func (c *DomainBindingController) CreateBinding(ctx *gin.Context) {
	var input dto.DomainBindingCreateDTO
	if err := ctx.ShouldBindJSON(&input); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	binding, err := c.service.CreateBinding(&input)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	response.JSON(ctx, http.StatusCreated, c.toDTO(binding))
}

// ListBindingsBySite godoc
// @Summary List domain bindings for a site
// @Description Get all domain bindings for a specific site
// @Tags System
// @Produce  json
// @Param   siteId path int true "Site ID"
// @Param   page query int false "Page number" default(1)
// @Param   pageSize query int false "Page size" default(10)
// @Success 200 {object} response.Response{data=response.PaginatedData{items=[]dto.DomainBindingDTO}}
// @Failure 400 {object} response.Response
// @Failure 403 {object} response.Response
// @Router /system/sites/{siteId}/bindings [get]
func (c *DomainBindingController) ListBindingsBySite(ctx *gin.Context) {
	siteID, err := strconv.ParseUint(ctx.Param("siteId"), 10, 32)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "Invalid site ID")
		return
	}

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	bindings, total, err := c.service.ListBindingsBySite(uint(siteID), page, pageSize)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	
	var dtos []*dto.DomainBindingDTO
	for _, b := range bindings {
		dtos = append(dtos, c.toDTO(b))
	}

	response.Paginated(ctx, dtos, total, page, pageSize)
}

// DeleteBinding godoc
// @Summary Delete a domain binding
// @Description Remove a domain binding by its ID
// @Tags System
// @Produce  json
// @Param   id path int true "Binding ID"
// @Success 204
// @Failure 400 {object} response.Response
// @Failure 403 {object} response.Response
// @Router /system/bindings/{id} [delete]
func (c *DomainBindingController) DeleteBinding(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "Invalid binding ID")
		return
	}

	if err := c.service.DeleteBinding(uint(id)); err != nil {
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	ctx.Status(http.StatusNoContent)
}


func (c *DomainBindingController) toDTO(binding *model.DomainBinding) *dto.DomainBindingDTO {
	return &dto.DomainBindingDTO{
		ID:          binding.ID,
		Domain:      binding.Domain,
		SiteID:      binding.SiteID,
		BindingType: string(binding.BindingType),
		ModuleSlug:  binding.ModuleSlug,
		CategoryID:  binding.CategoryID,
		CreatedAt:   binding.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   binding.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
} 