<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 系统日志</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s ease;
        }
        
        .action-button:hover::before {
            left: 100%;
        }

        /* 日志级别标签样式 */
        .log-level {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .level-info {
            background-color: rgba(23, 162, 184, 0.2);
            color: #17a2b8;
        }
        .level-warning {
            background-color: rgba(255, 193, 7, 0.2);
            color: #ffc107;
        }
        .level-error {
            background-color: rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }
        .level-debug {
            background-color: rgba(108, 117, 125, 0.2);
            color: #6c757d;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">系统日志</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-download text-white"></i>
                                </span>
                                导出日志
                            </span>
                        </button>
                        <button class="flex items-center justify-center bg-gradient-to-r from-red-500 to-red-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-red-500/30 relative overflow-hidden action-button">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-trash-alt text-white"></i>
                                </span>
                                清空日志
                            </span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 主要内容区域 -->
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- 左侧筛选面板 -->
                <div class="lg:col-span-1">
                    <!-- 搜索框 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <div class="relative">
                            <input type="text" placeholder="搜索日志..." class="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent pl-10">
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                    </div>

                    <!-- 筛选选项 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="font-semibold mb-4">日志级别</h3>
                        <div class="space-y-3">
                            <label class="flex items-center gap-2 cursor-pointer">
                                <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                                <span>信息 (245)</span>
                            </label>
                            <label class="flex items-center gap-2 cursor-pointer">
                                <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                                <span>警告 (32)</span>
                            </label>
                            <label class="flex items-center gap-2 cursor-pointer">
                                <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                                <span>错误 (15)</span>
                            </label>
                            <label class="flex items-center gap-2 cursor-pointer">
                                <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500">
                                <span>调试 (128)</span>
                            </label>
                        </div>

                        <h3 class="font-semibold mb-4 mt-6">时间范围</h3>
                        <div class="space-y-3">
                            <label class="flex items-center gap-2 cursor-pointer">
                                <input type="radio" name="time" class="form-radio bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500" checked>
                                <span>全部时间</span>
                            </label>
                            <label class="flex items-center gap-2 cursor-pointer">
                                <input type="radio" name="time" class="form-radio bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                                <span>最近24小时</span>
                            </label>
                            <label class="flex items-center gap-2 cursor-pointer">
                                <input type="radio" name="time" class="form-radio bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                                <span>最近7天</span>
                            </label>
                            <label class="flex items-center gap-2 cursor-pointer">
                                <input type="radio" name="time" class="form-radio bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500">
                                <span>自定义范围</span>
                            </label>
                        </div>

                        <h3 class="font-semibold mb-4 mt-6">操作类型</h3>
                        <div class="space-y-3">
                            <label class="flex items-center gap-2 cursor-pointer">
                                <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                                <span>登录/登出 (56)</span>
                            </label>
                            <label class="flex items-center gap-2 cursor-pointer">
                                <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                                <span>内容操作 (168)</span>
                            </label>
                            <label class="flex items-center gap-2 cursor-pointer">
                                <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                                <span>系统设置 (45)</span>
                            </label>
                            <label class="flex items-center gap-2 cursor-pointer">
                                <input type="checkbox" class="form-checkbox bg-gray-700 border-gray-600 rounded text-blue-500 focus:ring-blue-500" checked>
                                <span>插件操作 (23)</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 右侧日志列表 -->
                <div class="lg:col-span-3">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <!-- 日志表格 -->
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="border-b border-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-sm font-semibold">时间</th>
                                        <th class="px-6 py-3 text-left text-sm font-semibold">级别</th>
                                        <th class="px-6 py-3 text-left text-sm font-semibold">操作者</th>
                                        <th class="px-6 py-3 text-left text-sm font-semibold">IP地址</th>
                                        <th class="px-6 py-3 text-left text-sm font-semibold">操作内容</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 日志条目 -->
                                    <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                        <td class="px-6 py-4 text-sm">2025-06-15 10:23:45</td>
                                        <td class="px-6 py-4">
                                            <span class="log-level level-error">错误</span>
                                        </td>
                                        <td class="px-6 py-4 text-sm">admin</td>
                                        <td class="px-6 py-4 text-sm">*************</td>
                                        <td class="px-6 py-4 text-sm">数据库备份失败，错误代码：DB-503</td>
                                    </tr>
                                    <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                        <td class="px-6 py-4 text-sm">2025-06-15 10:15:32</td>
                                        <td class="px-6 py-4">
                                            <span class="log-level level-warning">警告</span>
                                        </td>
                                        <td class="px-6 py-4 text-sm">system</td>
                                        <td class="px-6 py-4 text-sm">-</td>
                                        <td class="px-6 py-4 text-sm">磁盘空间不足，剩余空间低于15%</td>
                                    </tr>
                                    <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                        <td class="px-6 py-4 text-sm">2025-06-15 10:05:18</td>
                                        <td class="px-6 py-4">
                                            <span class="log-level level-info">信息</span>
                                        </td>
                                        <td class="px-6 py-4 text-sm">editor01</td>
                                        <td class="px-6 py-4 text-sm">192.168.1.120</td>
                                        <td class="px-6 py-4 text-sm">发布文章 "GACMS新版本功能介绍" (ID: 1258)</td>
                                    </tr>
                                    <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                        <td class="px-6 py-4 text-sm">2025-06-15 09:58:03</td>
                                        <td class="px-6 py-4">
                                            <span class="log-level level-info">信息</span>
                                        </td>
                                        <td class="px-6 py-4 text-sm">admin</td>
                                        <td class="px-6 py-4 text-sm">*************</td>
                                        <td class="px-6 py-4 text-sm">启用插件 "高级SEO优化" (版本: 2.3.4)</td>
                                    </tr>
                                    <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                        <td class="px-6 py-4 text-sm">2025-06-15 09:45:27</td>
                                        <td class="px-6 py-4">
                                            <span class="log-level level-info">信息</span>
                                        </td>
                                        <td class="px-6 py-4 text-sm">user123</td>
                                        <td class="px-6 py-4 text-sm">192.168.1.135</td>
                                        <td class="px-6 py-4 text-sm">登录成功</td>
                                    </tr>
                                    <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                        <td class="px-6 py-4 text-sm">2025-06-15 09:42:15</td>
                                        <td class="px-6 py-4">
                                            <span class="log-level level-warning">警告</span>
                                        </td>
                                        <td class="px-6 py-4 text-sm">unknown</td>
                                        <td class="px-6 py-4 text-sm">************</td>
                                        <td class="px-6 py-4 text-sm">多次登录失败尝试，IP已临时封禁30分钟</td>
                                    </tr>
                                    <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                        <td class="px-6 py-4 text-sm">2025-06-15 09:30:08</td>
                                        <td class="px-6 py-4">
                                            <span class="log-level level-info">信息</span>
                                        </td>
                                        <td class="px-6 py-4 text-sm">admin</td>
                                        <td class="px-6 py-4 text-sm">*************</td>
                                        <td class="px-6 py-4 text-sm">更改系统设置：缓存配置</td>
                                    </tr>
                                    <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                        <td class="px-6 py-4 text-sm">2025-06-15 09:25:36</td>
                                        <td class="px-6 py-4">
                                            <span class="log-level level-debug">调试</span>
                                        </td>
                                        <td class="px-6 py-4 text-sm">system</td>
                                        <td class="px-6 py-4 text-sm">-</td>
                                        <td class="px-6 py-4 text-sm">自动清理临时文件完成，释放空间：256MB</td>
                                    </tr>
                                    <tr class="border-b border-gray-700 hover:bg-gray-800/20">
                                        <td class="px-6 py-4 text-sm">2025-06-15 09:15:42</td>
                                        <td class="px-6 py-4">
                                            <span class="log-level level-info">信息</span>
                                        </td>
                                        <td class="px-6 py-4 text-sm">editor02</td>
                                        <td class="px-6 py-4 text-sm">192.168.1.110</td>
                                        <td class="px-6 py-4 text-sm">上传文件：product-image-05.jpg (2.4MB)</td>
                                    </tr>
                                    <tr class="hover:bg-gray-800/20">
                                        <td class="px-6 py-4 text-sm">2025-06-15 09:10:21</td>
                                        <td class="px-6 py-4">
                                            <span class="log-level level-error">错误</span>
                                        </td>
                                        <td class="px-6 py-4 text-sm">system</td>
                                        <td class="px-6 py-4 text-sm">-</td>
                                        <td class="px-6 py-4 text-sm">缓存服务连接失败，已切换到备份服务</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页控件 -->
                        <div class="flex justify-between items-center mt-6">
                            <div class="text-sm text-gray-400">显示 1-10 项，共 420 项</div>
                            <div class="flex space-x-1">
                                <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">上一页</button>
                                <button class="px-3 py-1 rounded bg-blue-600 text-white">1</button>
                                <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">2</button>
                                <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">3</button>
                                <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">4</button>
                                <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">...</button>
                                <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">42</button>
                                <button class="px-3 py-1 rounded bg-gray-700 text-gray-300 hover:bg-gray-600">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏和导航栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 其他初始化...
        });
    </script>
</body>
</html> 