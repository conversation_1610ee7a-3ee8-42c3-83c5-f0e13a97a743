/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/post/module.go
 * @Description: DI bindings for the post module.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package post

import (
	"gacms/internal/core/service"
	"gacms/internal/modules/post/application/service"
	"gacms/internal/modules/post/domain/contract"
	"gacms/internal/modules/post/infrastructure/persistence"
	"gacms/internal/modules/post/port/http/controller"
	"go.uber.org/fx"
)

// Recipe is the DI recipe for the post module.
// It's collected by the ModuleManager.
var Recipe = service.ModuleRecipe{
	Name: "post",
	Options: fx.Options(
		// Repositories
		fx.Provide(
			fx.Annotate(
				persistence.NewPostGormRepository,
				fx.As(new(contract.PostRepository)),
			),
		),

		// Services
		fx.Provide(service.NewPostService),

		// Controller - Annotated for dynamic loading by the ModuleManager
		fx.Provide(
			fx.Annotate(
				controller.NewPostController,
				fx.ResultTags(`name:"routable_controller"`),
			),
		),
	),
} 