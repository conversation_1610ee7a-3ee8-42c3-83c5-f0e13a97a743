/*
Author: <PERSON><PERSON>eh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package service

import (
	"gacms/pkg/contract"
)

// CompileTimeEditionManager 编译时版本管理器接口
type CompileTimeEditionManager interface {
	// 编译时版本信息
	GetCompiledEdition() contract.Edition
	GetCompiledFeatures() []string
	GetCompiledLimits() *EditionLimits
	
	// 编译时功能检查
	IsFeatureCompiledIn(featureName string) bool
	IsLimitCompiledIn(limitType string) bool
}

// 编译时版本配置（通过build tags控制）
// 这些变量在编译时通过不同的build tags文件设置

//go:build personal || (!professional && !business)
// +build personal !professional,!business

// PersonalEditionCompiler 个人版编译时管理器
type PersonalEditionCompiler struct {
	BaseEditionCompiler
}

func init() {
	if compiledEditionManager == nil {
		compiledEditionManager = &PersonalEditionCompiler{
			BaseEditionCompiler: BaseEditionCompiler{
				edition: contract.EditionPersonal,
				features: map[string]bool{
					// 个人版基础功能（固化，无需验证）
					"basic_content": true,
					"basic_theme":   true,
					"basic_seo":     true,
					"basic_user":    true,
					"api_access":    true,  // 个人版包含API访问
				},
				limits: map[string]bool{
					// 个人版只保留必要的限制
					"max_sites":       true,  // 1个站点
					"max_admin_users": true,  // 3个管理用户
					"api_calls_per_day": true, // API调用限制
					// 删除页面、文章、文件大小等限制检查
				},
			},
		}
	}
}

// BaseEditionCompiler 基础编译时版本管理器
type BaseEditionCompiler struct {
	edition  contract.Edition
	features map[string]bool
	limits   map[string]bool
}

// GetCompiledEdition 获取编译时版本
func (c *BaseEditionCompiler) GetCompiledEdition() contract.Edition {
	return c.edition
}

// GetCompiledFeatures 获取编译时功能列表
func (c *BaseEditionCompiler) GetCompiledFeatures() []string {
	var features []string
	for feature, included := range c.features {
		if included {
			features = append(features, feature)
		}
	}
	return features
}

// GetCompiledLimits 获取编译时限制
func (c *BaseEditionCompiler) GetCompiledLimits() *EditionLimits {
	// 个人版只返回必要的限制
	if c.edition == contract.EditionPersonal {
		return &EditionLimits{
			MaxSites: 1,  // 1个站点
			MaxUsers: 3,  // 3个管理用户
			// 其他限制在个人版中不存在，删除相关检查代码
		}
	}

	// 其他版本的限制配置（专业版/商业版）
	switch c.edition {
	case contract.EditionProfessional:
		return &EditionLimits{
			MaxSites:     5,
			MaxUsers:     20,
			MaxStorage:   -1,
			MaxBandwidth: -1,
			MaxPages:     1000,
			MaxPosts:     2000,
		}
	case contract.EditionBusiness:
		return &EditionLimits{
			MaxSites:     -1,
			MaxUsers:     -1,
			MaxStorage:   -1,
			MaxBandwidth: -1,
			MaxPages:     -1,
			MaxPosts:     -1,
		}
	default:
		return &EditionLimits{}
	}
}

// IsFeatureCompiledIn 检查功能是否编译进来
func (c *BaseEditionCompiler) IsFeatureCompiledIn(featureName string) bool {
	if included, exists := c.features[featureName]; exists {
		return included
	}
	return false
}

// IsLimitCompiledIn 检查限制是否编译进来
func (c *BaseEditionCompiler) IsLimitCompiledIn(limitType string) bool {
	if included, exists := c.limits[limitType]; exists {
		return included
	}
	return false
}

// 全局编译时版本管理器实例
var compiledEditionManager CompileTimeEditionManager

// GetCompiledEditionManager 获取编译时版本管理器
func GetCompiledEditionManager() CompileTimeEditionManager {
	if compiledEditionManager == nil {
		// 默认返回个人版（最简配置）
		compiledEditionManager = &PersonalEditionCompiler{
			BaseEditionCompiler: BaseEditionCompiler{
				edition: contract.EditionPersonal,
				features: map[string]bool{
					"basic_content": true,
					"basic_theme":   true,
					"basic_seo":     true,
					"basic_user":    true,
					"api_access":    true,
				},
				limits: map[string]bool{
					"max_sites":       true,
					"max_admin_users": true,
					// 删除页面、文章等不必要的限制
				},
			},
		}
	}
	return compiledEditionManager
}

// SetCompiledEditionManager 设置编译时版本管理器（用于测试）
func SetCompiledEditionManager(manager CompileTimeEditionManager) {
	compiledEditionManager = manager
}

// 编译时版本检查函数（可以在编译时优化掉不需要的代码）

// IsPersonalEdition 检查是否为个人版编译
func IsPersonalEdition() bool {
	return GetCompiledEditionManager().GetCompiledEdition() == contract.EditionPersonal
}

// IsProfessionalEdition 检查是否为专业版编译
func IsProfessionalEdition() bool {
	return GetCompiledEditionManager().GetCompiledEdition() == contract.EditionProfessional
}

// IsBusinessEdition 检查是否为商业版编译
func IsBusinessEdition() bool {
	return GetCompiledEditionManager().GetCompiledEdition() == contract.EditionBusiness
}

// CompileTimeFeatureCheck 编译时功能检查（可以被编译器优化）
func CompileTimeFeatureCheck(featureName string) bool {
	return GetCompiledEditionManager().IsFeatureCompiledIn(featureName)
}

// CompileTimeLimitCheck 编译时限制检查（可以被编译器优化）
func CompileTimeLimitCheck(limitType string) bool {
	return GetCompiledEditionManager().IsLimitCompiledIn(limitType)
}

// 编译时常量（可以被编译器优化）
const (
	// 根据编译时版本设置的常量
	CompiledEditionName = "personal" // 这个值会在不同的build tags文件中被覆盖
)

// 编译时功能开关（个人版简化版本）
var (
	// 个人版基础功能（固化启用）
	BasicContentEnabled = true
	BasicThemeEnabled   = true
	BasicSEOEnabled     = true
	BasicUserEnabled    = true
	APIAccessEnabled    = true

	// 个人版不包含高级功能，删除相关开关
)

// 编译时限制开关（个人版简化版本）
var (
	SiteLimitEnabled = true  // 1个站点限制
	UserLimitEnabled = true  // 3个用户限制
	APILimitEnabled  = true  // API调用限制
	// 删除页面、文章、文件大小等限制开关
)

// 编译时版本信息
type CompileTimeVersionInfo struct {
	Edition  contract.Edition `json:"edition"`
	Features []string         `json:"features"`
	Limits   []string         `json:"limits"`
	BuildTag string           `json:"build_tag"`
}

// GetCompileTimeVersionInfo 获取编译时版本信息
func GetCompileTimeVersionInfo() *CompileTimeVersionInfo {
	manager := GetCompiledEditionManager()
	
	var limits []string
	limitTypes := []string{
		"max_sites", "max_admin_users", "max_pages", "max_posts",
		"api_calls_per_day", "max_file_size", "max_concurrent_connections",
	}
	
	for _, limitType := range limitTypes {
		if manager.IsLimitCompiledIn(limitType) {
			limits = append(limits, limitType)
		}
	}
	
	return &CompileTimeVersionInfo{
		Edition:  manager.GetCompiledEdition(),
		Features: manager.GetCompiledFeatures(),
		Limits:   limits,
		BuildTag: CompiledEditionName,
	}
}
