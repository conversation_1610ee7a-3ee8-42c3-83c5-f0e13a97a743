/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON> <<EMAIL>>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/system/domain/contract/DomainBindingRepository.go
 * @Description: Defines the repository interface for DomainBinding operations.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package contract

import "gacms/internal/modules/system/domain/model"

type DomainBindingRepository interface {
	Create(binding *model.DomainBinding) error
	Delete(id uint) error
	GetByID(id uint) (*model.DomainBinding, error)
	GetByDomain(domain string) (*model.DomainBinding, error)
	GetByDomainWithRules(domain string) (*model.DomainBinding, error)
	ListBySiteID(siteID uint, page, pageSize int) ([]*model.DomainBinding, int64, error)

	// URL重写规则相关
	CreateURLRule(rule *model.URLRewriteRule) error
	UpdateURLRule(rule *model.URLRewriteRule) error
	DeleteURLRule(id uint) error
	GetURLRulesByDomainBinding(domainBindingID uint) ([]*model.URLRewriteRule, error)
}