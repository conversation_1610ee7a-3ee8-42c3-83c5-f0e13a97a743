/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/extension/domain/contract/ActivationStrategy.go
 * @Description: Defines the strategy interface for activating/deactivating extensions.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

// ActivationStrategy defines the interface for different extension type activation logics.
type ActivationStrategy interface {
	Enable(name string) error
	Disable(name string) error
} 