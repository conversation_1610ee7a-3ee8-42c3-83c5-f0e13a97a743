/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/user/domain/error.go
 * @Description: Defines domain-specific errors for the user module.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package domain

import "errors"

var (
	// ErrNotFound indicates that a requested resource was not found.
	ErrNotFound = errors.New("resource not found")

	// ErrAlreadyExists indicates that a resource to be created already exists.
	ErrAlreadyExists = errors.New("resource already exists")

	// ErrAuthenticationFailed indicates that a login attempt failed.
	ErrAuthenticationFailed = errors.New("authentication failed")
) 