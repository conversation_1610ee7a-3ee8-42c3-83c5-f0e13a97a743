/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: {current_date}
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: {current_date}
 * @FilePath: internal/modules/user/domain/model/Permission.go
 * @Description: Defines the data model for a permission.
 *
 * © {current_year} GACMS. All rights reserved.
 */
package model

import "time"

// Permission represents an action that can be performed in the system.
type Permission struct {
	ID          uint      `gorm:"primaryKey"`
	Slug        string    `gorm:"not null;size:255;uniqueIndex"` // e.g., "posts.create"
	Description string    `gorm:"type:text"`
	CreatedAt   time.Time
	UpdatedAt   time.Time
} 