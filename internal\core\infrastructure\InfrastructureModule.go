/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/infrastructure/InfrastructureModule.go
 * @Description: 基础设施抽象模块，提供核心抽象契约的默认实现
 * 
 * © 2025 GACMS. All rights reserved.
 */

package infrastructure

import (
	"gacms/internal/core/infrastructure/auth"
	"gacms/internal/core/infrastructure/permission"
	"gacms/internal/core/infrastructure/site"
	"gacms/pkg/contract"
	"go.uber.org/fx"
)

// InfrastructureModule 基础设施模块
var InfrastructureModule = fx.Module("infrastructure",
	// 认证基础设施
	fx.Provide(
		// 提供默认的认证器实现（可被业务模块覆盖）
		fx.Annotate(
			auth.NewDefaultAuthenticator,
			fx.As(new(contract.Authenticator)),
		),
		
		// 提供用户上下文实现
		fx.Annotate(
			auth.NewUserContextProvider,
			fx.As(new(contract.UserContext)),
		),
		
		// 提供会话管理器实现
		fx.Annotate(
			auth.NewSessionManager,
			fx.As(new(contract.SessionManager)),
		),
	),

	// 权限基础设施
	fx.Provide(
		// 提供默认的权限检查器实现（可被业务模块覆盖）
		fx.Annotate(
			permission.NewDefaultPermissionChecker,
			fx.As(new(contract.PermissionChecker)),
		),
		
		// 提供角色管理器实现
		fx.Annotate(
			permission.NewRoleManager,
			fx.As(new(contract.RoleManager)),
		),
		
		// 提供授权提供者实现
		fx.Annotate(
			permission.NewAuthorizationProvider,
			fx.As(new(contract.AuthorizationProvider)),
		),
	),

	// 站点基础设施
	fx.Provide(
		// 提供站点解析器实现
		fx.Annotate(
			site.NewSiteResolver,
			fx.As(new(contract.SiteResolver)),
		),
		
		// 提供租户上下文实现
		fx.Annotate(
			site.NewTenantContext,
			fx.As(new(contract.TenantContext)),
		),
		
		// 提供多租户提供者实现
		fx.Annotate(
			site.NewMultiTenantProvider,
			fx.As(new(contract.MultiTenantProvider)),
		),
		
		// 提供域名绑定解析器实现
		fx.Annotate(
			site.NewDomainBindingResolver,
			fx.As(new(contract.DomainBindingResolver)),
		),
		
		// 提供站点管理器实现（基于系统服务）
		fx.Annotate(
			site.NewSiteManager,
			fx.As(new(contract.SiteManager)),
		),
	),
)

// DefaultImplementationNote 默认实现说明
/*
基础设施模块提供的是默认实现，具有以下特点：

1. 认证基础设施：
   - DefaultAuthenticator: 提供基本的认证功能，可被user模块的实现覆盖
   - UserContextProvider: 提供用户上下文管理
   - SessionManager: 提供会话管理功能

2. 权限基础设施：
   - DefaultPermissionChecker: 提供基本的权限检查，可被user模块的实现覆盖
   - RoleManager: 提供角色管理功能
   - AuthorizationProvider: 提供授权检查功能

3. 站点基础设施：
   - SiteResolver: 提供站点解析功能
   - TenantContext: 提供租户上下文管理
   - MultiTenantProvider: 提供多租户支持
   - DomainBindingResolver: 提供域名绑定解析
   - SiteManager: 提供站点管理功能

业务模块可以通过提供相同接口的实现来覆盖这些默认实现。
例如，user模块可以提供更完整的Authenticator和PermissionChecker实现。

依赖注入优先级：
1. 业务模块提供的实现（优先级最高）
2. 基础设施模块提供的默认实现（备用）

这样确保了：
- 系统始终有可用的基础功能
- 业务模块可以提供更专业的实现
- 模块可以独立开发和测试
*/
