/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/extension/application/service/ActivationService.go
 * @Description: Handles the activation lifecycle of extensions.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package service

import (
	"context"
	"fmt"
	coreSvc "gacms/internal/core/service"
	"gacms/internal/modules/extension/domain/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// ActivationService manages the activation and deactivation of modules.
type ActivationService struct {
	registry *coreSvc.ModuleRegistry
	logger   *zap.Logger
}

// NewActivationService creates a new activation service.
func NewActivationService(registry *coreSvc.ModuleRegistry, logger *zap.Logger) *ActivationService {
	return &ActivationService{
		registry: registry,
		logger:   logger,
	}
}

// Activate attempts to activate a module by its name with a license key.
func (s *ActivationService) Activate(moduleName, licenseKey string) error {
	var activatable contract.Activatable
	err := s.withModuleAs(&activatable, moduleName, func() error {
		return activatable.Activate(licenseKey)
	})
	return err
}

// Deactivate attempts to deactivate a module.
func (s *ActivationService) Deactivate(moduleName string) error {
	var activatable contract.Activatable
	err := s.withModuleAs(&activatable, moduleName, func() error {
		return activatable.Deactivate()
	})
	return err
}

// CheckStatus checks the activation status of a module.
func (s *ActivationService) CheckStatus(moduleName string) (string, error) {
	var status string
	var activatable contract.Activatable
	err := s.withModuleAs(&activatable, moduleName, func() error {
		var err error
		status, err = activatable.CheckStatus()
		return err
	})
	return status, err
}

// withModuleAs is a helper function to instantiate a module temporarily
// to check if it implements a specific interface and then execute an action.
func (s *ActivationService) withModuleAs(out interface{}, moduleName string, action func() error) error {
	provider, ok := s.registry.GetProvider(moduleName)
	if !ok {
		return fmt.Errorf("module '%s' is not registered", moduleName)
	}

	// Create a temporary, minimal DI container for the module.
	app := fx.New(
		fx.Provide(func() *zap.Logger { return s.logger }), // Provide logger to the submodule
		provider,
		fx.Populate(out),
	)

	ctx := context.Background()
	// Start and immediately stop the app to resolve dependencies into 'out'.
	if err := app.Start(ctx); err != nil {
		// This can happen if the module has unmet dependencies.
		// It can also happen if `out` is not a pointer to an interface provided by the module.
		s.logger.Error("Failed to instantiate module to check for interface",
			zap.String("module", moduleName),
			zap.Error(err),
		)
		return fmt.Errorf("module '%s' does not implement the Activatable interface or has missing dependencies", moduleName)
	}
	defer app.Stop(ctx)

	// 'out' is now populated. We can perform the action.
	return action()
} 