/*
Author: <PERSON><PERSON>eh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package controller

import (
	"net/http"
	"strconv"

	"gacms/internal/core/service"
	"gacms/pkg/contract"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// VendorsController 第三方模块管理控制器
type VendorsController struct {
	vendorsManager        service.VendorsModuleManager
	configManager         service.ModuleConfigManager
	vendorsLicenseManager service.VendorsLicenseManager
	vendorsCommerceManager service.VendorsCommerceManager
	vendorsDeveloperTools service.VendorsDeveloperTools
	logger                *zap.Logger
}

// NewVendorsController 创建第三方模块管理控制器
func NewVendorsController(
	vendorsManager service.VendorsModuleManager,
	configManager service.ModuleConfigManager,
	vendorsLicenseManager service.VendorsLicenseManager,
	vendorsCommerceManager service.VendorsCommerceManager,
	vendorsDeveloperTools service.VendorsDeveloperTools,
	logger *zap.Logger,
) *VendorsController {
	return &VendorsController{
		vendorsManager:         vendorsManager,
		configManager:          configManager,
		vendorsLicenseManager:  vendorsLicenseManager,
		vendorsCommerceManager: vendorsCommerceManager,
		vendorsDeveloperTools:  vendorsDeveloperTools,
		logger:                 logger,
	}
}

// RegisterRoutes 注册路由
func (c *VendorsController) RegisterRoutes(group *gin.RouterGroup) {
	vendors := group.Group("/vendors")
	{
		// 模块列表和信息
		vendors.GET("", c.ListModules)
		vendors.GET("/:name", c.GetModuleInfo)
		vendors.GET("/scan", c.ScanModules)
		
		// 模块安装和卸载
		vendors.POST("/install", c.InstallModule)
		vendors.DELETE("/:name", c.UninstallModule)
		
		// 模块状态管理
		vendors.PUT("/:name/enable", c.EnableModule)
		vendors.PUT("/:name/disable", c.DisableModule)
		
		// 模块配置
		vendors.GET("/:name/config", c.GetModuleConfig)
		vendors.PUT("/:name/config", c.UpdateModuleConfig)
		
		// 模块验证
		vendors.POST("/validate", c.ValidateModule)

		// 许可证管理
		license := vendors.Group("/license")
		{
			license.GET("/validators", c.GetRegisteredValidators)
			license.POST("/validators", c.RegisterValidator)
			license.DELETE("/validators/:name", c.UnregisterValidator)

			license.GET("/modules/:path/validate", c.ValidateModuleLicense)
			license.POST("/modules/:path/activate", c.ActivateModuleLicense)
			license.DELETE("/modules/:path/deactivate", c.DeactivateModuleLicense)

			license.GET("/modules/:path/info", c.GetModuleLicenseInfo)
			license.GET("/modules/:path/payment", c.CheckModulePayment)
		}

		// 商业化管理
		commerce := vendors.Group("/commerce")
		{
			commerce.POST("/payment", c.ProcessPayment)
			commerce.POST("/refund", c.ProcessRefund)

			commerce.POST("/subscription", c.CreateSubscription)
			commerce.DELETE("/subscription/:id", c.CancelSubscription)
			commerce.PUT("/subscription/:id/renew", c.RenewSubscription)

			commerce.GET("/revenue/vendor/:name", c.GetVendorRevenue)
			commerce.GET("/revenue/module/:path", c.GetModuleRevenue)
			commerce.POST("/revenue/split", c.CalculateRevenueSplit)
			commerce.POST("/payout/:vendor", c.ProcessPayout)
		}

		// 开发者工具
		developer := vendors.Group("/developer")
		{
			developer.POST("/validate/structure", c.ValidateModuleStructure)
			developer.POST("/generate/template", c.GenerateModuleTemplate)
			developer.POST("/generate/license", c.GenerateLicenseTemplate)
			developer.POST("/validate/license", c.ValidateLicenseFormat)

			developer.GET("/docs/module/:path", c.GenerateModuleDocumentation)
			developer.GET("/docs/api/:path", c.GenerateAPIDocumentation)

			developer.POST("/quality/check", c.RunQualityCheck)
			developer.POST("/security/scan", c.RunSecurityScan)
			developer.POST("/performance/test", c.RunPerformanceTest)

			developer.POST("/package", c.PackageModule)
			developer.POST("/publish", c.PublishModule)
		}
	}
}

// ListModules 获取模块列表
func (c *VendorsController) ListModules(ctx *gin.Context) {
	// 获取查询参数
	status := ctx.Query("status") // all, installed, available
	
	var response gin.H
	
	switch status {
	case "installed":
		modules, err := c.vendorsManager.GetInstalledVendorsModules()
		if err != nil {
			c.logger.Error("Failed to get installed vendors modules", zap.Error(err))
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to get installed modules",
			})
			return
		}
		response = gin.H{
			"status": "success",
			"data": gin.H{
				"modules": modules,
				"count":   len(modules),
			},
		}
		
	case "available":
		modules, err := c.vendorsManager.ScanVendorsDirectory()
		if err != nil {
			c.logger.Error("Failed to scan vendors directory", zap.Error(err))
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to scan available modules",
			})
			return
		}
		response = gin.H{
			"status": "success",
			"data": gin.H{
				"modules": modules,
				"count":   len(modules),
			},
		}
		
	default: // all
		installed, err := c.vendorsManager.GetInstalledVendorsModules()
		if err != nil {
			c.logger.Error("Failed to get installed vendors modules", zap.Error(err))
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to get modules",
			})
			return
		}
		
		available, err := c.vendorsManager.ScanVendorsDirectory()
		if err != nil {
			c.logger.Error("Failed to scan vendors directory", zap.Error(err))
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to scan modules",
			})
			return
		}
		
		response = gin.H{
			"status": "success",
			"data": gin.H{
				"installed": installed,
				"available": available,
				"counts": gin.H{
					"installed": len(installed),
					"available": len(available),
				},
			},
		}
	}
	
	ctx.JSON(http.StatusOK, response)
}

// GetModuleInfo 获取模块信息
func (c *VendorsController) GetModuleInfo(ctx *gin.Context) {
	moduleName := ctx.Param("name")
	
	moduleInfo, err := c.vendorsManager.GetModuleInfo(moduleName)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": "Module not found",
		})
		return
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   moduleInfo,
	})
}

// ScanModules 扫描可用模块
func (c *VendorsController) ScanModules(ctx *gin.Context) {
	modules, err := c.vendorsManager.ScanVendorsDirectory()
	if err != nil {
		c.logger.Error("Failed to scan vendors directory", zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to scan modules",
		})
		return
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"modules": modules,
			"count":   len(modules),
		},
	})
}

// InstallModule 安装模块
func (c *VendorsController) InstallModule(ctx *gin.Context) {
	var req struct {
		PackagePath string `json:"package_path" binding:"required"`
	}
	
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}
	
	err := c.vendorsManager.InstallModule(req.PackagePath)
	if err != nil {
		c.logger.Error("Failed to install module",
			zap.String("package_path", req.PackagePath),
			zap.Error(err),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Module installed successfully",
	})
}

// UninstallModule 卸载模块
func (c *VendorsController) UninstallModule(ctx *gin.Context) {
	moduleName := ctx.Param("name")
	
	err := c.vendorsManager.UninstallModule(moduleName)
	if err != nil {
		c.logger.Error("Failed to uninstall module",
			zap.String("module", moduleName),
			zap.Error(err),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Module uninstalled successfully",
	})
}

// EnableModule 启用模块
func (c *VendorsController) EnableModule(ctx *gin.Context) {
	moduleName := ctx.Param("name")
	
	err := c.configManager.EnableModule(moduleName)
	if err != nil {
		c.logger.Error("Failed to enable module",
			zap.String("module", moduleName),
			zap.Error(err),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Module enabled successfully",
	})
}

// DisableModule 禁用模块
func (c *VendorsController) DisableModule(ctx *gin.Context) {
	moduleName := ctx.Param("name")
	
	err := c.configManager.DisableModule(moduleName)
	if err != nil {
		c.logger.Error("Failed to disable module",
			zap.String("module", moduleName),
			zap.Error(err),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Module disabled successfully",
	})
}

// GetModuleConfig 获取模块配置
func (c *VendorsController) GetModuleConfig(ctx *gin.Context) {
	moduleName := ctx.Param("name")
	
	config, err := c.configManager.GetModuleConfig(moduleName)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": "Module config not found",
		})
		return
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   config,
	})
}

// UpdateModuleConfig 更新模块配置
func (c *VendorsController) UpdateModuleConfig(ctx *gin.Context) {
	moduleName := ctx.Param("name")
	
	var req struct {
		Settings map[string]interface{} `json:"settings"`
	}
	
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}
	
	err := c.configManager.UpdateModuleSettings(moduleName, req.Settings)
	if err != nil {
		c.logger.Error("Failed to update module config",
			zap.String("module", moduleName),
			zap.Error(err),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Module config updated successfully",
	})
}

// ValidateModule 验证模块
func (c *VendorsController) ValidateModule(ctx *gin.Context) {
	var req struct {
		ConfigPath string `json:"config_path" binding:"required"`
	}
	
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}
	
	config, err := c.vendorsManager.LoadModuleFromJSON(req.ConfigPath)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Failed to load module config: " + err.Error(),
		})
		return
	}
	
	err = c.vendorsManager.ValidateModuleConfig(config)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":  "Module validation failed",
			"details": err.Error(),
		})
		return
	}
	
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Module validation passed",
		"data":    config,
	})
}

// GetRegisteredValidators 获取已注册的验证器
func (c *VendorsController) GetRegisteredValidators(ctx *gin.Context) {
	validators := c.vendorsLicenseManager.GetRegisteredValidators()

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"validators": validators,
			"count":      len(validators),
		},
	})
}

// RegisterValidator 注册验证器
func (c *VendorsController) RegisterValidator(ctx *gin.Context) {
	var req struct {
		Name      string                 `json:"name" binding:"required"`
		Config    map[string]interface{} `json:"config"`
		Validator interface{}            `json:"validator"` // 这里需要更复杂的处理
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}

	// TODO: 实现验证器注册逻辑
	// 这里需要根据实际需求实现验证器的动态注册

	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Validator registered successfully",
	})
}

// UnregisterValidator 注销验证器
func (c *VendorsController) UnregisterValidator(ctx *gin.Context) {
	name := ctx.Param("name")

	err := c.vendorsLicenseManager.UnregisterThirdPartyValidator(name)
	if err != nil {
		c.logger.Error("Failed to unregister validator", zap.Error(err))
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":   "Failed to unregister validator",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Validator unregistered successfully",
	})
}

// ValidateModuleLicense 验证模块许可证
func (c *VendorsController) ValidateModuleLicense(ctx *gin.Context) {
	modulePath := ctx.Param("path")

	licenseInfo, err := c.vendorsLicenseManager.ValidateVendorsModule(ctx, modulePath)
	if err != nil {
		c.logger.Error("Failed to validate module license", zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to validate module license",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   licenseInfo,
	})
}

// ActivateModuleLicense 激活模块许可证
func (c *VendorsController) ActivateModuleLicense(ctx *gin.Context) {
	modulePath := ctx.Param("path")

	var req struct {
		LicenseKey string `json:"license_key" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}

	err := c.vendorsLicenseManager.ActivateVendorsLicense(ctx, modulePath, req.LicenseKey)
	if err != nil {
		c.logger.Error("Failed to activate license", zap.Error(err))
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":   "Failed to activate license",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "License activated successfully",
	})
}

// DeactivateModuleLicense 停用模块许可证
func (c *VendorsController) DeactivateModuleLicense(ctx *gin.Context) {
	modulePath := ctx.Param("path")

	err := c.vendorsLicenseManager.DeactivateVendorsLicense(ctx, modulePath)
	if err != nil {
		c.logger.Error("Failed to deactivate license", zap.Error(err))
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":   "Failed to deactivate license",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "License deactivated successfully",
	})
}

// GetModuleLicenseInfo 获取模块许可证信息
func (c *VendorsController) GetModuleLicenseInfo(ctx *gin.Context) {
	modulePath := ctx.Param("path")

	moduleInfo, err := c.vendorsLicenseManager.GetVendorsModuleInfo(modulePath)
	if err != nil {
		c.logger.Error("Failed to get module info", zap.Error(err))
		ctx.JSON(http.StatusNotFound, gin.H{
			"error":   "Module not found",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   moduleInfo,
	})
}

// CheckModulePayment 检查模块支付状态
func (c *VendorsController) CheckModulePayment(ctx *gin.Context) {
	modulePath := ctx.Param("path")

	paymentStatus, err := c.vendorsLicenseManager.CheckVendorsModulePayment(ctx, modulePath)
	if err != nil {
		c.logger.Error("Failed to check payment status", zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to check payment status",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   paymentStatus,
	})
}

// ProcessPayment 处理支付
func (c *VendorsController) ProcessPayment(ctx *gin.Context) {
	var paymentInfo service.PaymentInfo

	if err := ctx.ShouldBindJSON(&paymentInfo); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid payment information",
		})
		return
	}

	result, err := c.vendorsCommerceManager.ProcessPayment(ctx, &paymentInfo)
	if err != nil {
		c.logger.Error("Payment processing failed", zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Payment processing failed",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   result,
	})
}

// ProcessRefund 处理退款
func (c *VendorsController) ProcessRefund(ctx *gin.Context) {
	var req struct {
		TransactionID string `json:"transaction_id" binding:"required"`
		Reason        string `json:"reason" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}

	// TODO: 实现退款逻辑
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Refund processed successfully",
	})
}

// CreateSubscription 创建订阅
func (c *VendorsController) CreateSubscription(ctx *gin.Context) {
	var subscriptionInfo service.SubscriptionInfo

	if err := ctx.ShouldBindJSON(&subscriptionInfo); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid subscription information",
		})
		return
	}

	// TODO: 实现订阅创建逻辑
	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Subscription created successfully",
	})
}

// CancelSubscription 取消订阅
func (c *VendorsController) CancelSubscription(ctx *gin.Context) {
	subscriptionID := ctx.Param("id")

	// TODO: 实现订阅取消逻辑
	c.logger.Info("Cancelling subscription", zap.String("subscription_id", subscriptionID))

	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Subscription cancelled successfully",
	})
}

// RenewSubscription 续订
func (c *VendorsController) RenewSubscription(ctx *gin.Context) {
	subscriptionID := ctx.Param("id")

	// TODO: 实现订阅续订逻辑
	c.logger.Info("Renewing subscription", zap.String("subscription_id", subscriptionID))

	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Subscription renewed successfully",
	})
}

// GetVendorRevenue 获取供应商收入
func (c *VendorsController) GetVendorRevenue(ctx *gin.Context) {
	vendorName := ctx.Param("name")

	// TODO: 实现供应商收入查询逻辑
	c.logger.Info("Getting vendor revenue", zap.String("vendor", vendorName))

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"vendor":        vendorName,
			"total_revenue": 0.0,
			"currency":      "USD",
		},
	})
}

// GetModuleRevenue 获取模块收入
func (c *VendorsController) GetModuleRevenue(ctx *gin.Context) {
	modulePath := ctx.Param("path")

	// TODO: 实现模块收入查询逻辑
	c.logger.Info("Getting module revenue", zap.String("module_path", modulePath))

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"module_path":   modulePath,
			"total_revenue": 0.0,
			"currency":      "USD",
		},
	})
}

// CalculateRevenueSplit 计算收入分成
func (c *VendorsController) CalculateRevenueSplit(ctx *gin.Context) {
	var req struct {
		Amount     float64 `json:"amount" binding:"required"`
		VendorTier string  `json:"vendor_tier" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}

	split, err := c.vendorsCommerceManager.CalculateRevenueSplit(req.Amount, req.VendorTier)
	if err != nil {
		c.logger.Error("Failed to calculate revenue split", zap.Error(err))
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":   "Failed to calculate revenue split",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   split,
	})
}

// ProcessPayout 处理分成
func (c *VendorsController) ProcessPayout(ctx *gin.Context) {
	vendorName := ctx.Param("vendor")

	// TODO: 实现分成处理逻辑
	c.logger.Info("Processing payout", zap.String("vendor", vendorName))

	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Payout processed successfully",
	})
}

// ValidateModuleStructure 验证模块结构
func (c *VendorsController) ValidateModuleStructure(ctx *gin.Context) {
	var req struct {
		ModulePath string `json:"module_path" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}

	result, err := c.vendorsDeveloperTools.ValidateModuleStructure(req.ModulePath)
	if err != nil {
		c.logger.Error("Module structure validation failed", zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Module structure validation failed",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   result,
	})
}

// GenerateModuleTemplate 生成模块模板
func (c *VendorsController) GenerateModuleTemplate(ctx *gin.Context) {
	var templateInfo service.ModuleTemplateInfo

	if err := ctx.ShouldBindJSON(&templateInfo); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid template information",
		})
		return
	}

	err := c.vendorsDeveloperTools.GenerateModuleTemplate(&templateInfo)
	if err != nil {
		c.logger.Error("Failed to generate module template", zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to generate module template",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Module template generated successfully",
	})
}

// GenerateLicenseTemplate 生成许可证模板
func (c *VendorsController) GenerateLicenseTemplate(ctx *gin.Context) {
	var moduleInfo service.VendorsModuleInfo

	if err := ctx.ShouldBindJSON(&moduleInfo); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid module information",
		})
		return
	}

	template, err := c.vendorsDeveloperTools.GenerateLicenseTemplate(&moduleInfo)
	if err != nil {
		c.logger.Error("Failed to generate license template", zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to generate license template",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   template,
	})
}

// ValidateLicenseFormat 验证许可证格式
func (c *VendorsController) ValidateLicenseFormat(ctx *gin.Context) {
	var req struct {
		LicenseData []byte `json:"license_data" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}

	result, err := c.vendorsDeveloperTools.ValidateLicenseFormat(req.LicenseData)
	if err != nil {
		c.logger.Error("License format validation failed", zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "License format validation failed",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   result,
	})
}

// GenerateModuleDocumentation 生成模块文档
func (c *VendorsController) GenerateModuleDocumentation(ctx *gin.Context) {
	modulePath := ctx.Param("path")

	documentation, err := c.vendorsDeveloperTools.GenerateModuleDocumentation(modulePath)
	if err != nil {
		c.logger.Error("Failed to generate module documentation", zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to generate module documentation",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   documentation,
	})
}

// GenerateAPIDocumentation 生成API文档
func (c *VendorsController) GenerateAPIDocumentation(ctx *gin.Context) {
	modulePath := ctx.Param("path")

	apiDoc, err := c.vendorsDeveloperTools.GenerateAPIDocumentation(modulePath)
	if err != nil {
		c.logger.Error("Failed to generate API documentation", zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to generate API documentation",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   apiDoc,
	})
}

// RunQualityCheck 运行质量检查
func (c *VendorsController) RunQualityCheck(ctx *gin.Context) {
	var req struct {
		ModulePath string `json:"module_path" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}

	report, err := c.vendorsDeveloperTools.RunQualityCheck(req.ModulePath)
	if err != nil {
		c.logger.Error("Quality check failed", zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Quality check failed",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   report,
	})
}

// RunSecurityScan 运行安全扫描
func (c *VendorsController) RunSecurityScan(ctx *gin.Context) {
	var req struct {
		ModulePath string `json:"module_path" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}

	report, err := c.vendorsDeveloperTools.RunSecurityScan(req.ModulePath)
	if err != nil {
		c.logger.Error("Security scan failed", zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Security scan failed",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   report,
	})
}

// RunPerformanceTest 运行性能测试
func (c *VendorsController) RunPerformanceTest(ctx *gin.Context) {
	var req struct {
		ModulePath string `json:"module_path" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}

	report, err := c.vendorsDeveloperTools.RunPerformanceTest(req.ModulePath)
	if err != nil {
		c.logger.Error("Performance test failed", zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Performance test failed",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   report,
	})
}

// PackageModule 打包模块
func (c *VendorsController) PackageModule(ctx *gin.Context) {
	var req struct {
		ModulePath string                    `json:"module_path" binding:"required"`
		Options    service.PackageOptions   `json:"options"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}

	result, err := c.vendorsDeveloperTools.PackageModule(req.ModulePath, &req.Options)
	if err != nil {
		c.logger.Error("Module packaging failed", zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Module packaging failed",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   result,
	})
}

// PublishModule 发布模块
func (c *VendorsController) PublishModule(ctx *gin.Context) {
	var req struct {
		PackagePath string                 `json:"package_path" binding:"required"`
		PublishInfo service.PublishInfo    `json:"publish_info"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}

	result, err := c.vendorsDeveloperTools.PublishModule(req.PackagePath, &req.PublishInfo)
	if err != nil {
		c.logger.Error("Module publishing failed", zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Module publishing failed",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   result,
	})
}
