//go:build professional
// +build professional

/*
Author: <PERSON><PERSON> Nieh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package service

import (
	"gacms/pkg/contract"
)

// ProfessionalEditionCompiler 专业版编译时管理器
type ProfessionalEditionCompiler struct {
	BaseEditionCompiler
}

func init() {
	// 覆盖默认的编译时版本管理器
	compiledEditionManager = &ProfessionalEditionCompiler{
		BaseEditionCompiler: BaseEditionCompiler{
			edition: contract.EditionProfessional,
			features: map[string]bool{
				// 基础功能模块（专业版包含）
				"basic_content": true,
				"basic_theme":   true,
				"basic_seo":     true,
				"basic_user":    true,
				
				// 高级功能模块（专业版包含）
				"advanced_theme": true,
				"advanced_seo":   true,
				"workflow":       true,
				"api_access":     true,
				
				// 高级用户管理（专业版不包含）
				"advanced_user": false,
				
				// 企业功能模块（专业版不包含）
				"enterprise_api":         false,
				"enterprise_security":    false,
				"enterprise_integration": false,
				"enterprise_analytics":   false,
			},
			limits: map[string]bool{
				"max_sites":                   true,
				"max_admin_users":             true,
				"max_pages":                   true,
				"max_posts":                   true,
				"api_calls_per_day":           true,
				"max_file_size":               true,
				"max_concurrent_connections":  true,
			},
		},
	}
	
	// 更新编译时功能开关
	updateProfessionalFeatureFlags()
}

// updateProfessionalFeatureFlags 更新专业版功能开关
func updateProfessionalFeatureFlags() {
	// 基础功能（所有版本都包含）
	BasicContentEnabled = true
	BasicThemeEnabled   = true
	BasicSEOEnabled     = true
	BasicUserEnabled    = true
	
	// 高级功能（专业版包含）
	AdvancedThemeEnabled = true
	AdvancedSEOEnabled   = true
	WorkflowEnabled      = true
	APIAccessEnabled     = true
	AdvancedUserEnabled  = false // 专业版不包含高级用户管理
	
	// 企业功能（专业版不包含）
	EnterpriseAPIEnabled         = false
	EnterpriseSecurityEnabled    = false
	EnterpriseIntegrationEnabled = false
	EnterpriseAnalyticsEnabled   = false
}

// 专业版编译时常量
const (
	CompiledEditionName = "professional"
)

// 专业版功能和限制通过ProfessionalEditionProvider接口统一管理
// 不再需要重复的函数定义

// 专业版编译时验证
func ValidateProfessionalCompilation() error {
	manager := GetCompiledEditionManager()
	
	// 验证编译版本
	if manager.GetCompiledEdition() != contract.EditionProfessional {
		return fmt.Errorf("expected professional edition, got %s", manager.GetCompiledEdition())
	}
	
	// 验证必需功能
	requiredFeatures := []string{
		"basic_content", "basic_theme", "basic_seo", "basic_user",
		"advanced_theme", "advanced_seo", "workflow", "api_access",
	}
	
	for _, feature := range requiredFeatures {
		if !manager.IsFeatureCompiledIn(feature) {
			return fmt.Errorf("required feature %s not compiled in professional edition", feature)
		}
	}
	
	// 验证不应包含的功能
	excludedFeatures := []string{
		"enterprise_api", "enterprise_security", 
		"enterprise_integration", "enterprise_analytics",
	}
	
	for _, feature := range excludedFeatures {
		if manager.IsFeatureCompiledIn(feature) {
			return fmt.Errorf("enterprise feature %s should not be compiled in professional edition", feature)
		}
	}
	
	return nil
}
