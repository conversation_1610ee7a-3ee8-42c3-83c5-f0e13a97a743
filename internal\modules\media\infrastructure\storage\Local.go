/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/media/infrastructure/storage/Local.go
 * @Description: Local file storage driver.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package storage

import (
	"fmt"
	"gacms/internal/modules/media/domain/contract"
	"io"
	"mime/multipart"
	"net/url"
	"os"
	"path/filepath"
	"time"

	"github.com/google/uuid"
)

// TODO: Move these to configuration
const (
	localStorePath  = "./uploads"
	localServerBase = "http://localhost:8080" // This should be your app's base URL
)

type LocalStorage struct {
	// In a real app, you would inject a config object here
}

func NewLocalStorage() contract.Storage {
	// Ensure the base directory exists
	if err := os.MkdirAll(localStorePath, os.ModePerm); err != nil {
		panic(fmt.Sprintf("failed to create local storage directory: %v", err))
	}
	return &LocalStorage{}
}

func (l *LocalStorage) GetUploadCredentials(siteID uint, filename string, mimeType string) (*contract.UploadCredentials, error) {
	// For local storage, we generate a unique token and a temporary upload URL
	// that points back to our own API. The client will post to this URL.
	
	// Create a unique file key
	ext := filepath.Ext(filename)
	newFileName := uuid.New().String() + ext
	fileKey := filepath.Join(fmt.Sprintf("site_%d", siteID), time.Now().Format("2006/01/02"), newFileName)
	
	// Generate a temporary upload token (e.g., a short-lived JWT or a random string stored in Redis)
	// For simplicity here, we'll just use the fileKey as a pseudo-token.
	// In a real implementation, this should be a secure, expiring token.
	uploadToken := fileKey // Simplified for example

	// The client will post to an internal API endpoint with this token.
	uploadURL := fmt.Sprintf("%s/api/internal/media/upload-handler?token=%s", localServerBase, url.QueryEscape(uploadToken))

	return &contract.UploadCredentials{
		URL:       uploadURL,
		Method:    "POST",
		Headers:   nil, // No special headers needed for this approach
		FileKey:   fileKey,
		AccessURL: fmt.Sprintf("%s/uploads/%s", localServerBase, fileKey),
	}, nil
}

func (l *LocalStorage) Save(reader io.Reader, siteID uint, path string) (string, error) {
	fullPath := filepath.Join(localStorePath, path)
	
	// Ensure the directory for the file exists
	if err := os.MkdirAll(filepath.Dir(fullPath), os.ModePerm); err != nil {
		return "", err
	}

	// Create the file
	dst, err := os.Create(fullPath)
	if err != nil {
		return "", err
	}
	defer dst.Close()

	// Copy the file content
	if _, err = io.Copy(dst, reader); err != nil {
		return "", err
	}
	
	return l.GetURL(path), nil
}

func (l *LocalStorage) GetURL(path string) string {
	// In a real app, this should come from config
	return fmt.Sprintf("%s/uploads/%s", localServerBase, path)
}

func (l *LocalStorage) GetSignedURL(path string, expires int64) (string, error) {
	// Local storage doesn't typically use signed URLs in the same way as S3.
	// We can simulate it by generating a token that the app can validate.
	// For now, we'll just return the regular URL as this is a protected route anyway.
	return l.GetURL(path), nil
}

func (l *LocalStorage) Delete(path string) error {
	fullPath := filepath.Join(localStorePath, path)
	return os.Remove(fullPath)
} 