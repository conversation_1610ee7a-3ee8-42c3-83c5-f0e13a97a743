# GACMS 许可证管理系统

## 概述

GACMS 采用双重许可证验证机制，支持个人版、专业版、商业版三个版本。通过编译时功能固化和运行时许可证验证，确保系统安全性和功能控制。

## 版本架构

### 个人版 (Personal Edition)
- **功能范围**: 系统默认最小化配置，硬编码固化
- **许可证要求**: 无需任何许可证，完全免费使用
- **功能特点**: 基础内容管理(无限制)、基础主题、基础SEO、基础用户管理、API访问
- **限制**: 1个站点、3个管理用户、API调用限制

### 专业版 (Professional Edition)
- **功能范围**: 个人版基础功能 + 编译时配置的专业功能
- **许可证要求**: 系统许可证 + 使用许可证
- **功能特点**: 高级主题、高级SEO、工作流、高级分析等
- **部署**: 需要系统许可证验证，运行时需要使用许可证验证

### 商业版 (Business Edition)
- **功能范围**: 专业版所有功能 + 编译时配置的企业功能
- **许可证要求**: 系统许可证 + 使用许可证
- **功能特点**: 企业API、企业安全、LDAP集成、SSO集成、白标定制等
- **部署**: 需要系统许可证验证，运行时需要使用许可证验证

## 双重许可证验证机制

### 系统许可证 (System License)
- **验证时机**: 安装时验证
- **作用范围**: 控制系统部署权限和功能边界
- **文件位置**: `./system_license.json`
- **授权对象**: 部署方（企业IT部门/SaaS服务商）
- **包含内容**:
  - 许可证版本 (专业版/商业版)
  - 允许的功能模块列表
  - 最大租户数量
  - 有效期限
  - 包含一个默认租户的使用许可
- **过期后果**: 整个系统降级到个人版基础功能

### 使用许可证 (Usage License)
- **验证时机**: 运行时验证
- **作用范围**: 控制租户级别的功能使用权限
- **文件位置**: `./usage/{domain}.json`
- **授权对象**: 最终用户（租户/域名）
- **包含内容**:
  - 租户域名
  - 租户版本
  - 可用功能列表
  - 有效期限
- **分配方式**: 由系统管理员从系统许可证中分配给具体租户
- **层级关系**: 使用许可证权限不能超过系统许可证权限

## 许可证验证流程

### 个人版流程
```
编译时: 硬编码基础功能 → 安装时: 无验证 → 运行时: 直接使用
```

### 专业版/商业版流程
```
编译时: 读取配置编译功能边界
    ↓
安装时: 验证系统许可证 → 选择启用功能 → 保存配置
    ↓
运行时: 验证使用许可证 → 检查功能权限 → 允许/拒绝访问
```

## 功能控制机制

### 三层功能检查
1. **编译边界检查**: 功能是否在编译时包含
2. **安装配置检查**: 功能是否在安装时启用（专业版/商业版）
3. **许可证验证检查**: 功能是否有有效的使用许可证

### 功能可用条件
```
功能可用 = 编译时包含 AND 安装时启用 AND 许可证验证通过
```

### 降级机制
- **系统许可证过期**: 整个系统降级到个人版基础功能
- **使用许可证过期**: 对应租户降级到个人版基础功能
- **降级保证**: 系统始终保持个人版基础功能可用，确保系统不会完全不可用

## 版本详细说明

### 个人版 (Personal Edition)
- **价格**: 免费
- **许可证**: 无需许可证
- **站点数**: 1个
- **管理用户**: 3个
- **页面/文章**: 无限制
- **API调用**: 1000次/天
- **功能**:
  - 基础内容管理（无数量限制）
  - 基础主题
  - 基础SEO
  - 基础用户管理
  - API访问（有限制）

### 专业版 (Professional Edition)
- **价格**: ¥5,000/年
- **许可证**: 系统许可证 + 使用许可证
- **站点数**: 5个
- **管理用户**: 20个
- **页面/文章**: 无限制
- **API调用**: 10,000次/天
- **功能**: 个人版所有功能 +
  - 高级主题
  - 高级SEO
  - 工作流管理
  - 高级分析

### 商业版 (Business Edition)
- **价格**: ¥15,000/年
- **许可证**: 系统许可证 + 使用许可证
- **站点数**: 无限制
- **管理用户**: 无限制
- **页面/文章**: 无限制
- **API调用**: 无限制
- **功能**: 专业版所有功能 +
  - 企业API
  - 企业安全
  - LDAP集成
  - SSO集成
  - 白标定制
  - 企业集成

## 域名授权策略

### 根域名授权
- 购买 `example.com` 的授权
- 自动覆盖所有 `*.example.com` 子域名
- 不限制子域名数量和用途

### 支持的使用场景
- 用户个性化子域名: `user1.example.com`
- 商户子域名: `shop1.example.com`
- 功能模块子域名: `blog.example.com`, `api.example.com`
- 任何合理的子域名使用

### 域名验证
- 通过DNS TXT记录验证域名所有权
- 记录格式: `_gacms_license.example.com TXT "verification_value"`
- 定期验证域名所有权
- 防止许可证跨域名使用

## 许可证文件结构

### 目录结构
```
licenses/
├── README.md                    # 本说明文件
├── system_license.json          # 系统许可证
├── usage/                       # 使用许可证目录
│   ├── default.json            # 默认租户许可证
│   └── example.com.json        # 其他租户许可证
└── templates/                   # 许可证模板（可选）
    ├── system_license_template.json
    └── usage_license_template.json
```

### 系统许可证文件格式
```json
{
  "license_id": "sys_20240101_001",
  "license_type": "system",
  "licensee": "Example Corp",
  "edition": "business",
  "allowed_features": [
    "basic_content", "basic_theme", "basic_seo", "basic_user", "api_access",
    "advanced_theme", "advanced_seo", "workflow", "advanced_analytics",
    "enterprise_api", "enterprise_security", "ldap_integration"
  ],
  "max_tenants": 10,
  "includes_default_tenant": true,
  "issued_at": "2024-01-01T00:00:00Z",
  "expires_at": "2024-12-31T23:59:59Z",
  "signature": "..."
}
```

### 使用许可证文件格式
```json
{
  "license_id": "usage_20240101_001",
  "license_type": "usage",
  "tenant_domain": "example.com",
  "edition": "professional",
  "features": [
    "basic_content", "basic_theme", "basic_seo", "basic_user", "api_access",
    "advanced_theme", "advanced_seo", "workflow"
  ],
  "system_license_id": "sys_20240101_001",
  "issued_at": "2024-01-01T00:00:00Z",
  "expires_at": "2024-12-31T23:59:59Z",
  "signature": "..."
}
```

## 许可证验证机制

### 数字签名
- 使用RSA 4096位密钥对
- 官方私钥签名，公钥验证
- 防止许可证内容被篡改

### 系统许可证验证流程
1. 检查许可证文件是否存在
2. 验证许可证格式和必需字段
3. 验证数字签名
4. 检查过期时间
5. 验证版本兼容性
6. 检查功能权限范围

### 使用许可证验证流程
1. 检查系统许可证是否有效
2. 验证使用许可证格式和字段
3. 验证数字签名
4. 检查过期时间
5. 验证与系统许可证的关联
6. 检查功能权限不超过系统许可证
7. 验证域名所有权（如果启用）

## 环境变量配置

```bash
# 商业授权开关
export GACMS_COMMERCIAL_AUTH_ENABLED=true

# 开发模式（跳过验证）
export GACMS_DEVELOPMENT_MODE=false

# 许可证文件路径
export GACMS_SYSTEM_LICENSE_PATH="./licenses/system_license.json"
export GACMS_USAGE_LICENSE_DIR="./licenses/usage/"

# 域名验证
export GACMS_DOMAIN_VALIDATION_ENABLED=true

# 公钥文件路径
export GACMS_PUBLIC_KEY_PATH="./keys/gacms_public.pem"
```

## 开发和测试

### 开发模式
- 设置 `GACMS_DEVELOPMENT_MODE=true`
- 跳过所有商业授权验证
- 所有功能可用（相当于商业版）

### 测试模式
- 使用示例许可证文件
- 设置 `GACMS_COMMERCIAL_AUTH_ENABLED=false`
- 或使用宽松模式验证

## 安全注意事项

### 生产环境
1. **不要使用示例许可证文件**
2. **保护好私钥文件**
3. **定期更新许可证**
4. **启用域名验证**
5. **监控异常使用**

### 许可证保护
1. 设置适当的文件权限 (600)
2. 定期备份许可证文件
3. 使用安全的传输方式
4. 避免在日志中记录敏感信息

## 故障排除

### 常见问题

**1. 许可证验证失败**
- 检查文件路径是否正确
- 验证文件格式是否有效
- 确认数字签名是否正确
- 检查是否过期

**2. 域名验证失败**
- 确认DNS TXT记录设置正确
- 检查域名解析是否生效
- 验证域名所有权

**3. 功能不可用**
- 检查版本是否支持该功能
- 确认许可证是否包含该功能
- 验证使用限制是否超出

### 调试模式
```bash
# 启用调试日志
export GACMS_LOG_LEVEL=debug

# 查看许可证验证详情
export GACMS_LICENSE_DEBUG=true
```

## 联系支持

如果遇到许可证相关问题，请联系：

- **邮箱**: <EMAIL>
- **技术支持**: <EMAIL>
- **销售咨询**: <EMAIL>

---

**注意**: 示例文件仅用于开发和测试，请勿在生产环境中使用。
