/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/extension/domain/model/Extension.go
 * @Description: Defines the data model for an extension.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package model

import "gorm.io/gorm"

// Extension represents an installable unit (theme, module, plugin) registered in the system.
// Its state is persisted in the database.
type Extension struct {
	gorm.Model
	Name            string `gorm:"type:varchar(100);not null"`
	Type            string `gorm:"type:varchar(50);not null;index"` // "theme", "module", "plugin"
	Version         string `gorm:"type:varchar(50);not null"`
	DirectoryName   string `gorm:"type:varchar(100);not null;uniqueIndex:idx_type_directory"`
	IsEnabled       bool   `gorm:"default:true"`
	RequiresLicense bool   `gorm:"default:false"`
	// We can add more fields like Author, Description etc. if needed,
	// but they can also live in the manifest file to keep this table light.
} 