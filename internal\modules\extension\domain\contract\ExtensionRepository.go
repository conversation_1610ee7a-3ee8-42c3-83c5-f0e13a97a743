/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/extension/domain/contract/ExtensionRepository.go
 * @Description: Defines the repository contract for extensions.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package contract

import "gacms/internal/modules/extension/domain/model"

type ExtensionRepository interface {
	Create(extension *model.Extension) error
	Update(extension *model.Extension) error
	Delete(id uint) error
	FindByDirName(dirName string) (*model.Extension, error)
	FindByType(extType string) ([]*model.Extension, error)
	GetAll() ([]*model.Extension, error)
	// You might add more specific finders like FindByTypeAndStatus, etc.
} 