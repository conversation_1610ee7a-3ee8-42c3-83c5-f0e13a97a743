<!--
© 2025 Clion Nieh. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 注册账户</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1a1c23 0%, #121317 100%);
            font-family: 'Inter', sans-serif;
        }
        
        .login-card {
            background: rgba(30, 32, 40, 0.6);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
        
        .input-group {
            position: relative;
        }
        
        .input-icon {
            position: absolute;
            top: 50%;
            left: 1rem;
            transform: translateY(-50%);
            color: #6b7280;
        }
        
        .input-field {
            background-color: rgba(30, 32, 40, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #e5e7eb;
            padding-left: 2.5rem;
            transition: all 0.3s ease;
        }
        
        .input-field:focus {
            background-color: rgba(30, 32, 40, 0.8);
            border-color: rgba(59, 130, 246, 0.5);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
        }
        
        .submit-btn {
            background: linear-gradient(90deg, #3b82f6, #2563eb);
            transition: all 0.3s ease;
        }
        
        .submit-btn:hover {
            background: linear-gradient(90deg, #2563eb, #1d4ed8);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
            transform: translateY(-1px);
        }
        
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }
        
        .shape {
            position: absolute;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 50%;
            animation: float 15s infinite ease-in-out;
        }
        
        .shape:nth-child(1) {
            width: 400px;
            height: 400px;
            top: -200px;
            left: -200px;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 300px;
            height: 300px;
            top: 60%;
            right: -150px;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 200px;
            height: 200px;
            bottom: -100px;
            left: 30%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0% {
                transform: translateY(0) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(5deg);
            }
            100% {
                transform: translateY(0) rotate(0deg);
            }
        }
        
        .progress-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
            position: relative;
        }
        
        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 1;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #374151;
            color: #9ca3af;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .step-label {
            color: #9ca3af;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }
        
        .progress-step.active .step-number {
            background-color: #3b82f6;
            color: white;
        }
        
        .progress-step.active .step-label {
            color: #e5e7eb;
        }
        
        .progress-step.completed .step-number {
            background-color: #10b981;
            color: white;
        }
        
        .progress-line {
            position: absolute;
            top: 15px;
            left: 15%;
            right: 15%;
            height: 2px;
            background-color: #374151;
            z-index: 0;
        }
        
        .progress-line-active {
            position: absolute;
            top: 15px;
            left: 15%;
            width: 33.3%;
            height: 2px;
            background-color: #3b82f6;
            z-index: 0;
            transition: width 0.3s ease;
        }
        
        .registration-form {
            display: none;
        }
        
        .registration-form.active {
            display: block;
        }
        
        .password-strength {
            height: 5px;
            background-color: #374151;
            border-radius: 2px;
            margin-top: 8px;
            overflow: hidden;
        }
        
        .password-strength-bar {
            height: 100%;
            width: 0;
            transition: all 0.3s ease;
        }
        
        .strength-weak {
            width: 33.3%;
            background-color: #ef4444;
        }
        
        .strength-medium {
            width: 66.6%;
            background-color: #f59e0b;
        }
        
        .strength-strong {
            width: 100%;
            background-color: #10b981;
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="login-card w-full max-w-md rounded-xl p-8 md:p-10">
        <div class="text-center mb-8">
            <img src="./assets/images/logo.svg" alt="GACMS Logo" class="h-12 mx-auto mb-2">
            <h1 class="text-2xl font-bold text-white">创建 GACMS 账户</h1>
            <p class="text-gray-400 mt-2">完成注册，开始使用强大的内容管理系统</p>
        </div>

        <!-- 进度指示器 -->
        <div class="progress-indicator">
            <div class="progress-line"></div>
            <div class="progress-line-active" id="progressLine"></div>
            
            <div class="progress-step active" data-step="1">
                <div class="step-number">1</div>
                <div class="step-label">基本信息</div>
            </div>
            
            <div class="progress-step" data-step="2">
                <div class="step-number">2</div>
                <div class="step-label">安全设置</div>
            </div>
            
            <div class="progress-step" data-step="3">
                <div class="step-number">3</div>
                <div class="step-label">完成注册</div>
            </div>
        </div>

        <!-- 步骤1：基本信息 -->
        <form class="registration-form active" id="step1Form">
            <div class="space-y-6">
                <div class="input-group">
                    <span class="input-icon">
                        <i class="fas fa-user"></i>
                    </span>
                    <input type="text" placeholder="用户名" class="input-field w-full px-4 py-3 rounded-lg focus:outline-none" required>
                </div>
                
                <div class="input-group">
                    <span class="input-icon">
                        <i class="fas fa-envelope"></i>
                    </span>
                    <input type="email" placeholder="电子邮箱" class="input-field w-full px-4 py-3 rounded-lg focus:outline-none" required>
                </div>
                
                <div class="input-group">
                    <span class="input-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </span>
                    <input type="tel" placeholder="手机号码（选填）" class="input-field w-full px-4 py-3 rounded-lg focus:outline-none">
                </div>
                
                <button type="button" id="nextToStep2" class="submit-btn w-full py-3 rounded-lg text-white font-medium focus:outline-none">
                    下一步 <i class="fas fa-arrow-right ml-2"></i>
                </button>
                
                <div class="text-center mt-6">
                    <p class="text-gray-400">
                        已有账户？ 
                        <a href="login.html" class="text-blue-400 hover:text-blue-300 transition-colors">登录</a>
                    </p>
                </div>
            </div>
        </form>
        
        <!-- 步骤2：安全设置 -->
        <form class="registration-form" id="step2Form">
            <div class="space-y-6">
                <div>
                    <div class="input-group">
                        <span class="input-icon">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" id="password" placeholder="设置密码" class="input-field w-full px-4 py-3 rounded-lg focus:outline-none" required>
                    </div>
                    
                    <div class="password-strength mt-2">
                        <div class="password-strength-bar" id="passwordStrengthBar"></div>
                    </div>
                    
                    <div class="flex justify-between mt-2">
                        <span class="text-xs text-gray-400">强度：</span>
                        <span class="text-xs text-gray-400" id="passwordStrengthText">未输入</span>
                    </div>
                </div>
                
                <div class="input-group">
                    <span class="input-icon">
                        <i class="fas fa-lock"></i>
                    </span>
                    <input type="password" placeholder="确认密码" class="input-field w-full px-4 py-3 rounded-lg focus:outline-none" required>
                </div>
                
                <div class="flex items-start space-x-3 mt-4">
                    <input type="checkbox" id="enableTwoFactor" class="mt-1">
                    <label for="enableTwoFactor" class="text-gray-300 text-sm">
                        启用两步验证 (推荐)
                        <p class="text-gray-400 text-xs mt-1">提高账户安全性，登录时需要额外验证码</p>
                    </label>
                </div>
                
                <div class="flex space-x-4">
                    <button type="button" id="backToStep1" class="bg-gray-700 hover:bg-gray-600 transition-colors w-1/3 py-3 rounded-lg text-white font-medium focus:outline-none">
                        <i class="fas fa-arrow-left mr-2"></i> 返回
                    </button>
                    
                    <button type="button" id="nextToStep3" class="submit-btn w-2/3 py-3 rounded-lg text-white font-medium focus:outline-none">
                        下一步 <i class="fas fa-arrow-right ml-2"></i>
                    </button>
                </div>
            </div>
        </form>
        
        <!-- 步骤3：完成注册 -->
        <form class="registration-form" id="step3Form">
            <div class="space-y-6">
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto bg-blue-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-check text-white text-2xl"></i>
                    </div>
                    <h2 class="text-xl font-bold text-white mt-4">即将完成注册</h2>
                    <p class="text-gray-400 mt-2">请选择您的角色和兴趣，帮助我们为您提供更好的服务</p>
                </div>
                
                <div>
                    <label class="block text-gray-300 text-sm mb-2">您的角色</label>
                    <select class="input-field w-full px-4 py-3 rounded-lg focus:outline-none">
                        <option value="">请选择...</option>
                        <option value="developer">开发者</option>
                        <option value="designer">设计师</option>
                        <option value="marketer">营销人员</option>
                        <option value="blogger">博主/作者</option>
                        <option value="business">企业管理者</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-gray-300 text-sm mb-2">您感兴趣的功能 (可多选)</label>
                    <div class="space-y-2">
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" class="form-checkbox text-blue-500">
                            <span class="text-gray-300">内容管理</span>
                        </label>
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" class="form-checkbox text-blue-500">
                            <span class="text-gray-300">多语言支持</span>
                        </label>
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" class="form-checkbox text-blue-500">
                            <span class="text-gray-300">电子商务</span>
                        </label>
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" class="form-checkbox text-blue-500">
                            <span class="text-gray-300">SEO优化</span>
                        </label>
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" class="form-checkbox text-blue-500">
                            <span class="text-gray-300">数据分析</span>
                        </label>
                    </div>
                </div>
                
                <div class="flex items-start space-x-3">
                    <input type="checkbox" id="agreeTerms" class="mt-1" required>
                    <label for="agreeTerms" class="text-gray-300 text-sm">
                        我已阅读并同意 <a href="#" class="text-blue-400 hover:text-blue-300 transition-colors">服务条款</a> 和 <a href="#" class="text-blue-400 hover:text-blue-300 transition-colors">隐私政策</a>
                    </label>
                </div>
                
                <div class="flex space-x-4">
                    <button type="button" id="backToStep2" class="bg-gray-700 hover:bg-gray-600 transition-colors w-1/3 py-3 rounded-lg text-white font-medium focus:outline-none">
                        <i class="fas fa-arrow-left mr-2"></i> 返回
                    </button>
                    
                    <button type="submit" class="submit-btn w-2/3 py-3 rounded-lg text-white font-medium focus:outline-none">
                        完成注册 <i class="fas fa-check ml-2"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 步骤导航
            const step1Form = document.getElementById('step1Form');
            const step2Form = document.getElementById('step2Form');
            const step3Form = document.getElementById('step3Form');
            const progressLine = document.getElementById('progressLine');
            
            // 步骤1到步骤2
            document.getElementById('nextToStep2').addEventListener('click', function() {
                step1Form.classList.remove('active');
                step2Form.classList.add('active');
                
                document.querySelector('.progress-step[data-step="1"]').classList.add('completed');
                document.querySelector('.progress-step[data-step="2"]').classList.add('active');
                progressLine.style.width = '66.6%';
            });
            
            // 步骤2到步骤1
            document.getElementById('backToStep1').addEventListener('click', function() {
                step2Form.classList.remove('active');
                step1Form.classList.add('active');
                
                document.querySelector('.progress-step[data-step="1"]').classList.remove('completed');
                document.querySelector('.progress-step[data-step="2"]').classList.remove('active');
                progressLine.style.width = '33.3%';
            });
            
            // 步骤2到步骤3
            document.getElementById('nextToStep3').addEventListener('click', function() {
                step2Form.classList.remove('active');
                step3Form.classList.add('active');
                
                document.querySelector('.progress-step[data-step="2"]').classList.add('completed');
                document.querySelector('.progress-step[data-step="3"]').classList.add('active');
                progressLine.style.width = '100%';
            });
            
            // 步骤3到步骤2
            document.getElementById('backToStep2').addEventListener('click', function() {
                step3Form.classList.remove('active');
                step2Form.classList.add('active');
                
                document.querySelector('.progress-step[data-step="2"]').classList.remove('completed');
                document.querySelector('.progress-step[data-step="3"]').classList.remove('active');
                progressLine.style.width = '66.6%';
            });
            
            // 密码强度检测
            const passwordInput = document.getElementById('password');
            const passwordStrengthBar = document.getElementById('passwordStrengthBar');
            const passwordStrengthText = document.getElementById('passwordStrengthText');
            
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                let strength = 0;
                
                // 检查密码长度
                if (password.length >= 8) {
                    strength += 1;
                }
                
                // 检查是否包含数字和字母
                if (/[a-zA-Z]/.test(password) && /[0-9]/.test(password)) {
                    strength += 1;
                }
                
                // 检查是否包含特殊字符
                if (/[^a-zA-Z0-9]/.test(password)) {
                    strength += 1;
                }
                
                // 更新密码强度指示器
                passwordStrengthBar.className = 'password-strength-bar';
                
                if (password.length === 0) {
                    passwordStrengthBar.style.width = '0';
                    passwordStrengthText.textContent = '未输入';
                } else if (strength === 1) {
                    passwordStrengthBar.classList.add('strength-weak');
                    passwordStrengthText.textContent = '弱';
                    passwordStrengthText.className = 'text-xs text-red-500';
                } else if (strength === 2) {
                    passwordStrengthBar.classList.add('strength-medium');
                    passwordStrengthText.textContent = '中';
                    passwordStrengthText.className = 'text-xs text-yellow-500';
                } else {
                    passwordStrengthBar.classList.add('strength-strong');
                    passwordStrengthText.textContent = '强';
                    passwordStrengthText.className = 'text-xs text-green-500';
                }
            });
            
            // 表单提交
            document.getElementById('step3Form').addEventListener('submit', function(e) {
                e.preventDefault();
                // 这里可以添加表单验证和提交逻辑
                alert('注册成功！即将跳转到登录页面...');
                window.location.href = 'login.html';
            });
        });
    </script>
</body>
</html> 