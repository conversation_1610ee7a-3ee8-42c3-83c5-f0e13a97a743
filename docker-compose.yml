version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    volumes:
      - .:/app
    depends_on:
      - db
      - redis
    environment:
      # Database Configuration
      - DB_HOST=db
      - DB_PORT=3306
      - DB_USER=gacms
      - DB_PASSWORD=gacms_password
      - DB_NAME=gacms_dev
      # Redis Configuration
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      # System Configuration
      - GIN_MODE=debug
      - ADMIN_PATH=/admin

  db:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      MYSQL_DATABASE: gacms_dev
      MYSQL_USER: gacms
      MYSQL_PASSWORD: gacms_password
      MYSQL_ROOT_PASSWORD: root_password
    volumes:
      - mysql_data:/var/lib/mysql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:6.2-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  mysql_data:
  redis_data: 