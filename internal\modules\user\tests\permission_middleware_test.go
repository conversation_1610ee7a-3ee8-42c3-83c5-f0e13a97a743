/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/user/tests/permission_middleware_test.go
 * @Description: 权限中间件集成测试
 * 
 * © 2025 GACMS. All rights reserved.
 */
package tests

import (
	"context"
	"errors"
	"gacms/internal/modules/user/application/service"
	"gacms/internal/modules/user/domain/model"
	"gacms/internal/port/http/middleware"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockSessionHandler 模拟会话处理器
type MockSessionHandler struct {
	mock.Mock
}

func (m *MockSessionHandler) GetUserID(ctx *gin.Context) (uint, error) {
	args := m.Called(ctx)
	return args.Get(0).(uint), args.Error(1)
}

func (m *MockSessionHandler) GetUserType(ctx *gin.Context) (model.UserType, error) {
	args := m.Called(ctx)
	return args.Get(0).(model.UserType), args.Error(1)
}

func (m *MockSessionHandler) GetSiteID(ctx *gin.Context) (uint, error) {
	args := m.Called(ctx)
	return args.Get(0).(uint), args.Error(1)
}

func TestPermissionMiddleware(t *testing.T) {
	// 准备测试用的Gin引擎
	gin.SetMode(gin.TestMode)

	// 测试场景: 用户有权限访问
	t.Run("HasPermission_AccessGranted", func(t *testing.T) {
		// 创建模拟对象
		mockPermService := new(MockPermissionService)
		mockSessionHandler := new(MockSessionHandler)

		// 创建中间件
		permMiddleware := middleware.NewPermissionMiddleware(mockPermService, mockSessionHandler)

		// 设置模拟行为
		mockSessionHandler.On("GetUserID", mock.Anything).Return(uint(1), nil).Once()
		mockSessionHandler.On("GetUserType", mock.Anything).Return(model.AdminUser, nil).Once()
		mockPermService.On("Can", mock.Anything, uint(1), model.AdminUser, "content.view").Return(true, nil).Once()

		// 创建一个测试HTTP响应记录器和请求
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request, _ = http.NewRequest("GET", "/api/content", nil)

		// 创建一个简单的处理函数，验证是否会被调用
		handlerCalled := false
		handler := func(c *gin.Context) {
			handlerCalled = true
			c.Status(http.StatusOK)
		}

		// 应用中间件
		permMiddleware.RequirePermission("content.view")(c)

		// 如果中间件允许通过，手动调用处理函数
		if !c.IsAborted() {
			handler(c)
		}

		// 验证结果
		assert.True(t, handlerCalled, "Handler should be called when user has permission")
		assert.Equal(t, http.StatusOK, w.Code)
		mockPermService.AssertExpectations(t)
		mockSessionHandler.AssertExpectations(t)
	})

	// 测试场景: 用户没有权限访问
	t.Run("NoPermission_AccessDenied", func(t *testing.T) {
		// 创建模拟对象
		mockPermService := new(MockPermissionService)
		mockSessionHandler := new(MockSessionHandler)

		// 创建中间件
		permMiddleware := middleware.NewPermissionMiddleware(mockPermService, mockSessionHandler)

		// 设置模拟行为
		mockSessionHandler.On("GetUserID", mock.Anything).Return(uint(2), nil).Once()
		mockSessionHandler.On("GetUserType", mock.Anything).Return(model.AdminUser, nil).Once()
		mockPermService.On("Can", mock.Anything, uint(2), model.AdminUser, "content.edit").Return(false, nil).Once()

		// 创建一个测试HTTP响应记录器和请求
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request, _ = http.NewRequest("GET", "/api/content/edit", nil)

		// 创建一个简单的处理函数，验证是否会被调用
		handlerCalled := false
		handler := func(c *gin.Context) {
			handlerCalled = true
			c.Status(http.StatusOK)
		}

		// 应用中间件
		permMiddleware.RequirePermission("content.edit")(c)

		// 如果中间件允许通过，手动调用处理函数
		if !c.IsAborted() {
			handler(c)
		}

		// 验证结果
		assert.False(t, handlerCalled, "Handler should not be called when user lacks permission")
		assert.Equal(t, http.StatusForbidden, w.Code)
		mockPermService.AssertExpectations(t)
		mockSessionHandler.AssertExpectations(t)
	})

	// 测试场景: 会话获取失败
	t.Run("SessionError_Unauthorized", func(t *testing.T) {
		// 创建模拟对象
		mockPermService := new(MockPermissionService)
		mockSessionHandler := new(MockSessionHandler)

		// 创建中间件
		permMiddleware := middleware.NewPermissionMiddleware(mockPermService, mockSessionHandler)

		// 设置模拟行为 - 会话获取失败
		mockSessionHandler.On("GetUserID", mock.Anything).Return(uint(0), errors.New("session expired")).Once()

		// 创建一个测试HTTP响应记录器和请求
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request, _ = http.NewRequest("GET", "/api/content", nil)

		// 创建一个简单的处理函数，验证是否会被调用
		handlerCalled := false
		handler := func(c *gin.Context) {
			handlerCalled = true
			c.Status(http.StatusOK)
		}

		// 应用中间件
		permMiddleware.RequirePermission("content.view")(c)

		// 如果中间件允许通过，手动调用处理函数
		if !c.IsAborted() {
			handler(c)
		}

		// 验证结果
		assert.False(t, handlerCalled, "Handler should not be called when session is invalid")
		assert.Equal(t, http.StatusUnauthorized, w.Code)
		mockSessionHandler.AssertExpectations(t)
		// 确保权限服务不会被调用
		mockPermService.AssertNotCalled(t, "Can")
	})

	// 测试场景: 权限验证出错
	t.Run("PermissionCheckError_InternalError", func(t *testing.T) {
		// 创建模拟对象
		mockPermService := new(MockPermissionService)
		mockSessionHandler := new(MockSessionHandler)

		// 创建中间件
		permMiddleware := middleware.NewPermissionMiddleware(mockPermService, mockSessionHandler)

		// 设置模拟行为
		mockSessionHandler.On("GetUserID", mock.Anything).Return(uint(3), nil).Once()
		mockSessionHandler.On("GetUserType", mock.Anything).Return(model.AdminUser, nil).Once()
		mockPermService.On("Can", mock.Anything, uint(3), model.AdminUser, "content.delete").
			Return(false, errors.New("database error")).Once()

		// 创建一个测试HTTP响应记录器和请求
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request, _ = http.NewRequest("GET", "/api/content/delete", nil)

		// 创建一个简单的处理函数，验证是否会被调用
		handlerCalled := false
		handler := func(c *gin.Context) {
			handlerCalled = true
			c.Status(http.StatusOK)
		}

		// 应用中间件
		permMiddleware.RequirePermission("content.delete")(c)

		// 如果中间件允许通过，手动调用处理函数
		if !c.IsAborted() {
			handler(c)
		}

		// 验证结果
		assert.False(t, handlerCalled, "Handler should not be called when permission check fails")
		assert.Equal(t, http.StatusInternalServerError, w.Code)
		mockPermService.AssertExpectations(t)
		mockSessionHandler.AssertExpectations(t)
	})
}

// MockPermissionService 模拟权限服务
type MockPermissionService struct {
	mock.Mock
}

func (m *MockPermissionService) Can(ctx context.Context, userID uint, userType model.UserType, permission string) (bool, error) {
	args := m.Called(ctx, userID, userType, permission)
	return args.Bool(0), args.Error(1)
}

func (m *MockPermissionService) CreatePermission(ctx context.Context, payload *service.CreatePermissionPayload) (*model.Permission, error) {
	args := m.Called(ctx, payload)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Permission), args.Error(1)
}

func (m *MockPermissionService) UpdatePermission(ctx context.Context, permission *model.Permission, siteID, updatedBy uint) error {
	args := m.Called(ctx, permission, siteID, updatedBy)
	return args.Error(0)
}

func (m *MockPermissionService) DeletePermission(ctx context.Context, permissionID, siteID, deletedBy uint) error {
	args := m.Called(ctx, permissionID, siteID, deletedBy)
	return args.Error(0)
}

func (m *MockPermissionService) GetPermissionByID(ctx context.Context, permissionID uint) (*model.Permission, error) {
	args := m.Called(ctx, permissionID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Permission), args.Error(1)
}

func (m *MockPermissionService) GetPermissionBySlug(ctx context.Context, slug string) (*model.Permission, error) {
	args := m.Called(ctx, slug)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Permission), args.Error(1)
}

func (m *MockPermissionService) ListPermissions(ctx context.Context, options interface{}) ([]*model.Permission, int64, error) {
	args := m.Called(ctx, options)
	if args.Get(0) == nil {
		return nil, args.Int64(1), args.Error(2)
	}
	return args.Get(0).([]*model.Permission), args.Int64(1), args.Error(2)
}