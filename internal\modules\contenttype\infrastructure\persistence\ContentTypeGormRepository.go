/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/contenttype/infrastructure/persistence/ContentTypeGormRepository.go
 * @Description: GORM implementation of the ContentTypeRepository, using the core database service.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package persistence

import (
	"context"
	"gacms/internal/modules/contenttype/domain/contract"
	"gacms/internal/modules/contenttype/domain/model"
	dbContract "gacms/pkg/contract"
)

// Ensure ContentTypeGormRepository implements ContentTypeRepository.
var _ contract.ContentTypeRepository = (*ContentTypeGormRepository)(nil)

// ContentTypeGormRepository is the GORM implementation for the content type repository.
type ContentTypeGormRepository struct {
	dbService dbContract.Database
}

// NewContentTypeGormRepository creates a new ContentTypeGormRepository.
func NewContentTypeGormRepository(dbService dbContract.Database) contract.ContentTypeRepository {
	return &ContentTypeGormRepository{dbService: dbService}
}

func (r *ContentTypeGormRepository) Create(ctx context.Context, contentType *model.ContentType) error {
	return r.dbService.DB(ctx).Create(contentType).Error
}

func (r *ContentTypeGormRepository) Update(ctx context.Context, contentType *model.ContentType) error {
	return r.dbService.DB(ctx).Save(contentType).Error
}

func (r *ContentTypeGormRepository) Delete(ctx context.Context, id uint) error {
	return r.dbService.DB(ctx).Delete(&model.ContentType{}, id).Error
}

func (r *ContentTypeGormRepository) GetByID(ctx context.Context, id uint) (*model.ContentType, error) {
	var contentType model.ContentType
	err := r.dbService.DB(ctx).First(&contentType, id).Error
	return &contentType, err
}

func (r *ContentTypeGormRepository) GetBySlug(ctx context.Context, slug string) (*model.ContentType, error) {
	var contentType model.ContentType
	err := r.dbService.DB(ctx).Where("slug = ?", slug).First(&contentType).Error
	return &contentType, err
}

func (r *ContentTypeGormRepository) GetAll(ctx context.Context) ([]*model.ContentType, error) {
	var contentTypes []*model.ContentType
	err := r.dbService.DB(ctx).Find(&contentTypes).Error
	return contentTypes, err
} 