/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-13
 * @FilePath: internal/modules/user/port/http/controller/AdminController.go
 * @Description: Controller for handling admin user API requests using a pure event-driven approach.
 *
 * © 2025 GACMS. All rights reserved.
 */
package controller

import (
	"gacms/internal/modules/user/application/dto"
	"gacms/internal/modules/user/events"
	"gacms/pkg/contract"
	"github.com/gin-gonic/gin"
	"net/http"
)

const (
	notImplementedMsg = "This endpoint has not been migrated to the new event-driven architecture yet."
)

// AdminController handles API requests related to admin users.
// It is purely event-driven and does not contain business logic.
type AdminController struct {
	eventBus contract.EventBus
}

// NewAdminController creates a new AdminController.
// It only requires the event bus to dispatch action events.
func NewAdminController(eventBus contract.EventBus) *AdminController {
	return &AdminController{
		eventBus: eventBus,
	}
}

// RegisterRoutes registers the routes for admin users.
func (c *AdminController) RegisterRoutes(public, authed *gin.RouterGroup) {
	// Public route for login
	public.POST("/admin/login", c.Login)

	// Authenticated routes
	admins := authed.Group("/admins")
	{
		admins.POST("", c.CreateAdmin)
		admins.GET("", c.ListAdmins)
		admins.GET("/:id", c.GetAdmin)
		admins.PUT("/:id", c.UpdateAdmin)
		admins.DELETE("/:id", c.DeleteAdmin)
		admins.POST("/:id/roles", c.AssignRoles)
	}

	// Example of another route group that might exist
	authed.GET("/admins/stats", c.GetStats)
}

// Login handles administrator login requests.
func (c *AdminController) Login(ctx *gin.Context) {
	ctx.JSON(http.StatusNotImplemented, gin.H{"error": notImplementedMsg})
}

// CreateAdmin handles the creation of a new administrator by dispatching an event.
func (c *AdminController) CreateAdmin(ctx *gin.Context) {
	var input dto.AdminCreateDTO
	if err := ctx.ShouldBindJSON(&input); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Note: We might need to map AdminCreateDTO to a more generic CreateUserRequest DTO in a real app.
	createUserDTO := &dto.CreateUserRequest{
		Username: input.Username,
		Password: input.Password,
		Email:    input.Email,
		IsActive: input.IsActive,
	}
	event := events.NewCreateAdminUserEvent(ctx.Request.Context(), createUserDTO)

	if err := c.eventBus.Dispatch(event); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process user creation request: " + err.Error()})
		return
	}

	ctx.JSON(http.StatusAccepted, gin.H{"message": "User creation request accepted and is being processed."})
}

// ListAdmins handles listing administrators with pagination.
func (c *AdminController) ListAdmins(ctx *gin.Context) {
	ctx.JSON(http.StatusNotImplemented, gin.H{"error": notImplementedMsg})
}

// GetAdmin handles retrieving a single administrator by their UUID.
func (c *AdminController) GetAdmin(ctx *gin.Context) {
	ctx.JSON(http.StatusNotImplemented, gin.H{"error": notImplementedMsg})
}

// UpdateAdmin handles updating an existing administrator.
func (c *AdminController) UpdateAdmin(ctx *gin.Context) {
	ctx.JSON(http.StatusNotImplemented, gin.H{"error": notImplementedMsg})
}

// DeleteAdmin handles deleting an administrator.
func (c *AdminController) DeleteAdmin(ctx *gin.Context) {
	ctx.JSON(http.StatusNotImplemented, gin.H{"error": notImplementedMsg})
}

// AssignRoles handles assigning roles to an administrator.
func (c *AdminController) AssignRoles(ctx *gin.Context) {
	ctx.JSON(http.StatusNotImplemented, gin.H{"error": notImplementedMsg})
}

// GetStats handles retrieving statistics about admin users.
func (c *AdminController) GetStats(ctx *gin.Context) {
	ctx.JSON(http.StatusNotImplemented, gin.H{"error": notImplementedMsg})
} 