/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: {current_date}
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: {current_date}
 * @FilePath: internal/modules/user/application/service/RoleManagementService.go
 * @Description: Implements the application service for managing roles and permissions.
 *
 * © {current_year} GACMS. All rights reserved.
 */
package service

import (
	"context"
	"fmt"
	"gacms/internal/modules/user/contract"
	"gacms/internal/modules/user/domain/model"
)

// RoleManagementService provides business logic for role and permission management.
type RoleManagementService struct {
	roleRepo contract.RoleRepository
	permRepo contract.PermissionRepository
}

// NewRoleManagementService creates a new instance of RoleManagementService.
func NewRoleManagementService(roleRepo contract.RoleRepository, permRepo contract.PermissionRepository) *RoleManagementService {
	return &RoleManagementService{
		roleRepo: roleRepo,
		permRepo: permRepo,
	}
}

// CreateAdminRoleDTO holds the data for creating a new admin role.
type CreateAdminRoleDTO struct {
	Name string `json:"name" binding:"required"`
}

// CreateAdminRole creates a new role for admin users.
func (s *RoleManagementService) CreateAdminRole(ctx context.Context, dto CreateAdminRoleDTO) (*model.Role, error) {
	siteID, ok := ctx.Value("site_id").(uint)
	if !ok || siteID == 0 {
		return nil, fmt.Errorf("invalid site context")
	}

	role := &model.Role{
		SiteID:   siteID,
		Name:     dto.Name,
		UserType: model.AdminUser,
	}

	if err := s.roleRepo.Create(ctx, role); err != nil {
		return nil, err
	}
	return role, nil
}

// AssignPermissionToRoleDTO holds the data for assigning a permission to a role.
type AssignPermissionToRoleDTO struct {
	RoleID       uint `json:"role_id" binding:"required"`
	PermissionID uint `json:"permission_id" binding:"required"`
}

// AssignPermissionToRole assigns a permission to a role.
func (s *RoleManagementService) AssignPermissionToRole(ctx context.Context, dto AssignPermissionToRoleDTO) error {
	return s.roleRepo.AddPermissionToRole(ctx, dto.RoleID, dto.PermissionID)
}

// AssignRoleToAdminDTO holds the data for assigning a role to an admin user.
type AssignRoleToAdminDTO struct {
	UserID uint `json:"user_id" binding:"required"`
	RoleID uint `json:"role_id" binding:"required"`
}

// AssignRoleToAdmin assigns a role to an admin user.
func (s *RoleManagementService) AssignRoleToAdmin(ctx context.Context, dto AssignRoleToAdminDTO) error {
	return s.roleRepo.AddRoleToUser(ctx, dto.UserID, dto.RoleID, model.AdminUser)
} 