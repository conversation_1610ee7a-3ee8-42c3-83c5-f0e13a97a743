/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/theme/domain/model/Theme.go
 * @Description: Defines the data model for a theme.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package model

// Theme represents the metadata of a theme, read from its theme.json file.
type Theme struct {
	Name            string `json:"name"`
	Version         string `json:"version"`
	Author          string `json:"author"`
	Description     string `json:"description"`
	PreviewImageURL string `json:"previewImageUrl"`
	DirectoryName   string `json:"directoryName"` // The name of the theme's folder
} 