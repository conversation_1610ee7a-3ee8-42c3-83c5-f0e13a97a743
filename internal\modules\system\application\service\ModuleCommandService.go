/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/system/application/service/ModuleCommandService.go
 * @Description: Provides services for CLI commands related to module management.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
)

// ModuleInfo holds metadata for a single module for listing.
type ModuleInfo struct {
	Name        string
	Version     string
	Description string
	Enabled     bool
}

// ModuleCommandService handles the business logic for module-related CLI commands.
type ModuleCommandService struct {
	moduleDir string
}

// NewModuleCommandService creates a new service for module commands.
func NewModuleCommandService() *ModuleCommandService {
	return &ModuleCommandService{
		moduleDir: "internal/modules",
	}
}

// List scans the modules directory and returns information about each module.
func (s *ModuleCommandService) List() ([]ModuleInfo, error) {
	var modules []ModuleInfo

	files, err := ioutil.ReadDir(s.moduleDir)
	if err != nil {
		return nil, fmt.Errorf("could not read modules directory: %w", err)
	}

	for _, file := range files {
		if !file.IsDir() {
			continue
		}
		moduleName := file.Name()
		manifestPath := filepath.Join(s.moduleDir, moduleName, "module.json")

		data, err := ioutil.ReadFile(manifestPath)
		if os.IsNotExist(err) {
			continue // Not a manageable module
		}
		if err != nil {
			return nil, fmt.Errorf("failed to read manifest for %s: %w", moduleName, err)
		}

		var meta struct {
			Name        string `json:"name"`
			Version     string `json:"version"`
			Description string `json:"description"`
			Enabled     bool   `json:"enabled"`
		}
		if err := json.Unmarshal(data, &meta); err != nil {
			return nil, fmt.Errorf("failed to parse manifest for %s: %w", moduleName, err)
		}
		modules = append(modules, ModuleInfo{
			Name:        meta.Name,
			Version:     meta.Version,
			Description: meta.Description,
			Enabled:     meta.Enabled,
		})
	}
	return modules, nil
}

// Enable enables a module by setting "enabled": true in its module.json.
func (s *ModuleCommandService) Enable(moduleName string) error {
	return s.setModuleState(moduleName, true)
}

// Disable disables a module by setting "enabled": false in its module.json.
func (s *ModuleCommandService) Disable(moduleName string) error {
	return s.setModuleState(moduleName, false)
}

func (s *ModuleCommandService) setModuleState(moduleName string, enabled bool) error {
	manifestPath := filepath.Join(s.moduleDir, moduleName, "module.json")

	data, err := ioutil.ReadFile(manifestPath)
	if err != nil {
		return fmt.Errorf("module '%s' not found or manifest unreadable", moduleName)
	}

	var meta map[string]interface{}
	if err := json.Unmarshal(data, &meta); err != nil {
		return fmt.Errorf("failed to parse manifest for %s: %w", moduleName, err)
	}

	meta["enabled"] = enabled

	updatedData, err := json.MarshalIndent(meta, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to serialize updated manifest for %s: %w", moduleName, err)
	}

	return ioutil.WriteFile(manifestPath, updatedData, 0644)
} 