/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/infrastructure/cache/Cache.go
 * @Description: Defines the standard interface for all cache implementations.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package cache

import "time"

// Cache is the standard interface that all cache stores must implement.
// This allows for swapping cache implementations (e.g., file, redis) seamlessly.
type Cache interface {
	// Set stores a value in the cache with a specific key and expiration time.
	Set(key string, value interface{}, ttl time.Duration) error

	// Get retrieves a value from the cache by its key.
	Get(key string) (interface{}, error)

	// Delete removes a value from the cache by its key.
	Delete(key string) error

	// Has checks if a key exists in the cache.
	Has(key string) bool
} 