<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 内容管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">内容管理</span>
            </div>

            <!-- 内容管理概览 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">内容管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <a href="post_edit.html" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <i class="fas fa-plus mr-2"></i>
                            添加内容
                        </a>
                    </div>
                </div>

                <!-- 内容统计卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mt-6">
                    <!-- 文章总数 -->
                    <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                                <i class="fas fa-file-alt text-blue-500 text-xl"></i>
                            </div>
                            <div>
                                <div class="text-sm text-gray-400">文章总数</div>
                                <div class="text-xl font-semibold text-white">236</div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="flex justify-between text-xs text-gray-400">
                                <span>已发布</span>
                                <span>187</span>
                            </div>
                            <div class="h-1.5 bg-gray-700 rounded-full mt-1 overflow-hidden">
                                <div class="bg-blue-500 h-full" style="width: 80%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 分类数量 -->
                    <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                                <i class="fas fa-folder text-green-500 text-xl"></i>
                            </div>
                            <div>
                                <div class="text-sm text-gray-400">分类数量</div>
                                <div class="text-xl font-semibold text-white">18</div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="flex justify-between text-xs text-gray-400">
                                <span>本月新增</span>
                                <span>2</span>
                            </div>
                            <div class="h-1.5 bg-gray-700 rounded-full mt-1 overflow-hidden">
                                <div class="bg-green-500 h-full" style="width: 15%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 标签数量 -->
                    <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center mr-4">
                                <i class="fas fa-tags text-purple-500 text-xl"></i>
                            </div>
                            <div>
                                <div class="text-sm text-gray-400">标签数量</div>
                                <div class="text-xl font-semibold text-white">124</div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="flex justify-between text-xs text-gray-400">
                                <span>本月新增</span>
                                <span>15</span>
                            </div>
                            <div class="h-1.5 bg-gray-700 rounded-full mt-1 overflow-hidden">
                                <div class="bg-purple-500 h-full" style="width: 25%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 评论数量 -->
                    <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center mr-4">
                                <i class="fas fa-comments text-yellow-500 text-xl"></i>
                            </div>
                            <div>
                                <div class="text-sm text-gray-400">评论数量</div>
                                <div class="text-xl font-semibold text-white">582</div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="flex justify-between text-xs text-gray-400">
                                <span>待审核</span>
                                <span>32</span>
                            </div>
                            <div class="h-1.5 bg-gray-700 rounded-full mt-1 overflow-hidden">
                                <div class="bg-red-500 h-full" style="width: 10%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速入口导航 -->
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-6">
                <!-- 文章管理 -->
                <a href="posts.html" class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 hover:bg-gray-800/20 transition-all group">
                    <div class="flex items-center">
                        <div class="w-16 h-16 rounded-xl flex items-center justify-center bg-blue-500/20 text-blue-500 mr-5 group-hover:scale-110 transition-transform">
                            <i class="fas fa-file-alt text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white mb-1">文章管理</h3>
                            <p class="text-gray-400 text-sm">撰写、编辑、发布和管理您的文章</p>
                            <div class="flex items-center mt-2 text-blue-400 text-sm">
                                <span>查看详情</span>
                                <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                            </div>
                        </div>
                    </div>
                </a>
                
                <!-- 分类管理 -->
                <a href="categories.html" class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 hover:bg-gray-800/20 transition-all group">
                    <div class="flex items-center">
                        <div class="w-16 h-16 rounded-xl flex items-center justify-center bg-green-500/20 text-green-500 mr-5 group-hover:scale-110 transition-transform">
                            <i class="fas fa-folder text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white mb-1">分类管理</h3>
                            <p class="text-gray-400 text-sm">创建和管理内容分类结构</p>
                            <div class="flex items-center mt-2 text-green-400 text-sm">
                                <span>查看详情</span>
                                <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                            </div>
                        </div>
                    </div>
                </a>
                
                <!-- 标签管理 -->
                <a href="tags.html" class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 hover:bg-gray-800/20 transition-all group">
                    <div class="flex items-center">
                        <div class="w-16 h-16 rounded-xl flex items-center justify-center bg-purple-500/20 text-purple-500 mr-5 group-hover:scale-110 transition-transform">
                            <i class="fas fa-tags text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white mb-1">标签管理</h3>
                            <p class="text-gray-400 text-sm">创建和管理内容标签</p>
                            <div class="flex items-center mt-2 text-purple-400 text-sm">
                                <span>查看详情</span>
                                <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                            </div>
                        </div>
                    </div>
                </a>
                
                <!-- 评论管理 -->
                <a href="comments.html" class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 hover:bg-gray-800/20 transition-all group">
                    <div class="flex items-center">
                        <div class="w-16 h-16 rounded-xl flex items-center justify-center bg-yellow-500/20 text-yellow-500 mr-5 group-hover:scale-110 transition-transform">
                            <i class="fas fa-comments text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white mb-1">评论管理</h3>
                            <p class="text-gray-400 text-sm">查看和管理用户评论</p>
                            <div class="flex items-center mt-2 text-yellow-400 text-sm">
                                <span>查看详情</span>
                                <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                            </div>
                        </div>
                    </div>
                </a>
                
                <!-- 媒体库 -->
                <a href="medias.html" class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 hover:bg-gray-800/20 transition-all group">
                    <div class="flex items-center">
                        <div class="w-16 h-16 rounded-xl flex items-center justify-center bg-red-500/20 text-red-500 mr-5 group-hover:scale-110 transition-transform">
                            <i class="fas fa-photo-video text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white mb-1">媒体库</h3>
                            <p class="text-gray-400 text-sm">管理图片、视频和其他媒体文件</p>
                            <div class="flex items-center mt-2 text-red-400 text-sm">
                                <span>查看详情</span>
                                <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                            </div>
                        </div>
                    </div>
                </a>
                
                <!-- 专题管理 -->
                <a href="topics.html" class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 hover:bg-gray-800/20 transition-all group">
                    <div class="flex items-center">
                        <div class="w-16 h-16 rounded-xl flex items-center justify-center bg-indigo-500/20 text-indigo-500 mr-5 group-hover:scale-110 transition-transform">
                            <i class="fas fa-star text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white mb-1">专题管理</h3>
                            <p class="text-gray-400 text-sm">管理专题内容和特色板块</p>
                            <div class="flex items-center mt-2 text-indigo-400 text-sm">
                                <span>查看详情</span>
                                <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <!-- 最近文章 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold text-white">最近文章</h3>
                    <a href="posts.html" class="text-blue-400 flex items-center text-sm hover:text-blue-300 transition-colors">
                        查看全部
                        <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">标题</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">类别</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">作者</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">状态</th>
                                <th class="py-3 px-4 text-center text-xs font-medium text-gray-400 uppercase tracking-wider">阅读量</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">发布时间</th>
                                <th class="py-3 px-4 text-center text-xs font-medium text-gray-400 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border-b border-gray-800 hover:bg-gray-800/20">
                                <td class="py-4 px-4">
                                    <div class="flex items-center">
                                        <img src="https://images.unsplash.com/photo-1661956602116-aa6865609028?ixlib=rb-4.0.3&ixid=M3wxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=80&h=80&q=80" alt="Article" class="w-10 h-10 rounded object-cover mr-3">
                                        <span class="text-white">2025年Web开发趋势预测</span>
                                    </div>
                                </td>
                                <td class="py-4 px-4">
                                    <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">技术</span>
                                </td>
                                <td class="py-4 px-4 text-gray-300">张小明</td>
                                <td class="py-4 px-4">
                                    <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">已发布</span>
                                </td>
                                <td class="py-4 px-4 text-center text-gray-300">2,450</td>
                                <td class="py-4 px-4 text-gray-400">2025-06-02</td>
                                <td class="py-4 px-4">
                                    <div class="flex items-center justify-center space-x-2">
                                        <a href="post_edit.html" class="text-gray-400 hover:text-blue-400 transition-colors">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="text-gray-400 hover:text-red-400 transition-colors">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="border-b border-gray-800 hover:bg-gray-800/20">
                                <td class="py-4 px-4">
                                    <div class="flex items-center">
                                        <img src="https://images.unsplash.com/photo-1501139083538-0139583c060f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dGltZXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=80&h=80&q=80" alt="Article" class="w-10 h-10 rounded object-cover mr-3">
                                        <span class="text-white">如何提高工作效率：10个实用技巧</span>
                                    </div>
                                </td>
                                <td class="py-4 px-4">
                                    <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">职场</span>
                                </td>
                                <td class="py-4 px-4 text-gray-300">李华</td>
                                <td class="py-4 px-4">
                                    <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">已发布</span>
                                </td>
                                <td class="py-4 px-4 text-center text-gray-300">1,822</td>
                                <td class="py-4 px-4 text-gray-400">2025-06-01</td>
                                <td class="py-4 px-4">
                                    <div class="flex items-center justify-center space-x-2">
                                        <a href="post_edit.html" class="text-gray-400 hover:text-blue-400 transition-colors">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="text-gray-400 hover:text-red-400 transition-colors">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="border-b border-gray-800 hover:bg-gray-800/20">
                                <td class="py-4 px-4">
                                    <div class="flex items-center">
                                        <img src="https://images.unsplash.com/photo-1523961131990-5ea7c61b2107?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8QUl8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=80&h=80&q=80" alt="Article" class="w-10 h-10 rounded object-cover mr-3">
                                        <span class="text-white">人工智能在企业中的5个实际应用场景</span>
                                    </div>
                                </td>
                                <td class="py-4 px-4">
                                    <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">AI</span>
                                </td>
                                <td class="py-4 px-4 text-gray-300">王刚</td>
                                <td class="py-4 px-4">
                                    <span class="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded text-xs">草稿</span>
                                </td>
                                <td class="py-4 px-4 text-center text-gray-300">0</td>
                                <td class="py-4 px-4 text-gray-400">-</td>
                                <td class="py-4 px-4">
                                    <div class="flex items-center justify-center space-x-2">
                                        <a href="post_edit.html" class="text-gray-400 hover:text-blue-400 transition-colors">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="text-gray-400 hover:text-red-400 transition-colors">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
</body>
</html> 