/*
Author: <PERSON><PERSON>eh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
*/

package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"gacms/internal/core/domain/model"
	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ModuleConfigManager 模块配置管理器接口
type ModuleConfigManager interface {
	// 获取模块配置
	GetModuleConfig(moduleName string) (*model.ModuleConfig, error)
	GetAllModuleConfigs() ([]*model.ModuleConfig, error)
	GetEnabledModules() ([]*model.ModuleConfig, error)
	GetModulesByType(moduleType model.ModuleType) ([]*model.ModuleConfig, error)

	// 更新模块配置
	UpdateModuleConfig(config *model.ModuleConfig) error
	EnableModule(moduleName string) error
	DisableModule(moduleName string) error
	UpdateModuleSettings(moduleName string, settings model.ConfigData) error

	// 配置热重载
	ReloadModuleConfig(moduleName string) error
	ReloadAllConfigs() error

	// 依赖检查
	CheckDependencies(moduleName string) error
	GetDependentModules(moduleName string) ([]string, error)

	// 事件通知
	RegisterConfigChangeListener(listener ModuleConfigChangeListener)
}

// ModuleConfigChangeListener 配置变更监听器
type ModuleConfigChangeListener interface {
	OnModuleEnabled(moduleName string, config *model.ModuleConfig)
	OnModuleDisabled(moduleName string, config *model.ModuleConfig)
	OnModuleConfigChanged(moduleName string, oldConfig, newConfig *model.ModuleConfig)
}

// DefaultModuleConfigManager 默认模块配置管理器实现
type DefaultModuleConfigManager struct {
	db           *gorm.DB
	eventMapper  ModuleEventMapper
	eventManager contract.EventManager
	logger       *zap.Logger

	// 配置缓存
	configCache map[string]*model.ModuleConfig
	cacheMu     sync.RWMutex

	// 变更监听器
	listeners []ModuleConfigChangeListener
	listenerMu sync.RWMutex
}

// ModuleConfigManagerParams fx依赖注入参数
type ModuleConfigManagerParams struct {
	fx.In

	DB           *gorm.DB
	EventMapper  ModuleEventMapper
	EventManager contract.EventManager
	Logger       *zap.Logger
}

// NewDefaultModuleConfigManager 创建默认模块配置管理器
func NewDefaultModuleConfigManager(params ModuleConfigManagerParams) ModuleConfigManager {
	manager := &DefaultModuleConfigManager{
		db:           params.DB,
		eventMapper:  params.EventMapper,
		eventManager: params.EventManager,
		logger:       params.Logger,
		configCache:  make(map[string]*model.ModuleConfig),
		listeners:    make([]ModuleConfigChangeListener, 0),
	}

	// 初始化配置缓存
	manager.initializeCache()

	return manager
}

// GetModuleConfig 获取模块配置
func (m *DefaultModuleConfigManager) GetModuleConfig(moduleName string) (*model.ModuleConfig, error) {
	// 先从缓存获取
	m.cacheMu.RLock()
	if config, exists := m.configCache[moduleName]; exists {
		m.cacheMu.RUnlock()
		return config, nil
	}
	m.cacheMu.RUnlock()

	// 从数据库获取
	var config model.ModuleConfig
	err := m.db.Where("module_name = ?", moduleName).First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("module config not found: %s", moduleName)
		}
		return nil, fmt.Errorf("failed to get module config: %w", err)
	}

	// 更新缓存
	m.cacheMu.Lock()
	m.configCache[moduleName] = &config
	m.cacheMu.Unlock()

	return &config, nil
}

// GetAllModuleConfigs 获取所有模块配置
func (m *DefaultModuleConfigManager) GetAllModuleConfigs() ([]*model.ModuleConfig, error) {
	var configs []*model.ModuleConfig
	err := m.db.Find(&configs).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get all module configs: %w", err)
	}

	// 更新缓存
	m.cacheMu.Lock()
	for _, config := range configs {
		m.configCache[config.ModuleName] = config
	}
	m.cacheMu.Unlock()

	return configs, nil
}

// GetEnabledModules 获取启用的模块
func (m *DefaultModuleConfigManager) GetEnabledModules() ([]*model.ModuleConfig, error) {
	var configs []*model.ModuleConfig
	err := m.db.Where("enabled = ?", true).Find(&configs).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get enabled modules: %w", err)
	}
	return configs, nil
}

// GetModulesByType 根据类型获取模块
func (m *DefaultModuleConfigManager) GetModulesByType(moduleType model.ModuleType) ([]*model.ModuleConfig, error) {
	var configs []*model.ModuleConfig
	err := m.db.Where("module_type = ?", moduleType).Find(&configs).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get modules by type %s: %w", moduleType, err)
	}
	return configs, nil
}

// UpdateModuleConfig 更新模块配置
func (m *DefaultModuleConfigManager) UpdateModuleConfig(config *model.ModuleConfig) error {
	// 获取旧配置
	oldConfig, err := m.GetModuleConfig(config.ModuleName)
	if err != nil {
		return err
	}

	// 更新数据库
	err = m.db.Save(config).Error
	if err != nil {
		return fmt.Errorf("failed to update module config: %w", err)
	}

	// 更新缓存
	m.cacheMu.Lock()
	m.configCache[config.ModuleName] = config
	m.cacheMu.Unlock()

	// 更新事件映射
	m.updateEventMapping(config)

	// 通知监听器
	m.notifyConfigChanged(config.ModuleName, oldConfig, config)

	// 发布配置变更事件
	m.publishConfigChangeEvent("module.config.updated", config.ModuleName, nil)

	m.logger.Info("Module config updated",
		zap.String("module", config.ModuleName),
		zap.Bool("enabled", config.Enabled),
	)

	return nil
}

// EnableModule 启用模块
func (m *DefaultModuleConfigManager) EnableModule(moduleName string) error {
	config, err := m.GetModuleConfig(moduleName)
	if err != nil {
		return err
	}

	// 检查是否可以启用
	if config.IsCore() {
		return fmt.Errorf("core module %s cannot be disabled", moduleName)
	}

	if config.Enabled {
		return nil // 已经启用
	}

	// 检查依赖
	if err := m.CheckDependencies(moduleName); err != nil {
		return fmt.Errorf("dependency check failed: %w", err)
	}

	// 更新状态
	config.Enabled = true
	config.UpdatedAt = time.Now()

	err = m.db.Save(config).Error
	if err != nil {
		return fmt.Errorf("failed to enable module: %w", err)
	}

	// 更新缓存
	m.cacheMu.Lock()
	m.configCache[moduleName] = config
	m.cacheMu.Unlock()

	// 更新事件映射
	m.updateEventMapping(config)

	// 通知监听器
	m.notifyModuleEnabled(moduleName, config)

	// 发布模块启用事件
	m.publishConfigChangeEvent("module.enabled", moduleName, nil)

	m.logger.Info("Module enabled", zap.String("module", moduleName))

	return nil
}

// DisableModule 禁用模块
func (m *DefaultModuleConfigManager) DisableModule(moduleName string) error {
	config, err := m.GetModuleConfig(moduleName)
	if err != nil {
		return err
	}

	// 检查是否可以禁用
	if config.IsCore() {
		return fmt.Errorf("core module %s cannot be disabled", moduleName)
	}

	if !config.Enabled {
		return nil // 已经禁用
	}

	// 检查依赖模块
	dependents, err := m.GetDependentModules(moduleName)
	if err != nil {
		return err
	}

	if len(dependents) > 0 {
		return fmt.Errorf("cannot disable module %s: it is required by %v", moduleName, dependents)
	}

	// 更新状态
	config.Enabled = false
	config.UpdatedAt = time.Now()

	err = m.db.Save(config).Error
	if err != nil {
		return fmt.Errorf("failed to disable module: %w", err)
	}

	// 更新缓存
	m.cacheMu.Lock()
	m.configCache[moduleName] = config
	m.cacheMu.Unlock()

	// 移除事件映射
	m.eventMapper.RemoveModuleMapping(moduleName)

	// 通知监听器
	m.notifyModuleDisabled(moduleName, config)

	// 发布模块禁用事件
	m.publishConfigChangeEvent("module.disabled", moduleName, nil)

	m.logger.Info("Module disabled", zap.String("module", moduleName))

	return nil
}

// UpdateModuleSettings 更新模块设置
func (m *DefaultModuleConfigManager) UpdateModuleSettings(moduleName string, settings model.ConfigData) error {
	config, err := m.GetModuleConfig(moduleName)
	if err != nil {
		return err
	}

	oldSettings := config.Settings
	config.Settings = settings
	config.UpdatedAt = time.Now()

	err = m.db.Save(config).Error
	if err != nil {
		return fmt.Errorf("failed to update module settings: %w", err)
	}

	// 更新缓存
	m.cacheMu.Lock()
	m.configCache[moduleName] = config
	m.cacheMu.Unlock()

	// 发布设置变更事件
	eventData := map[string]interface{}{
		"module_name":  moduleName,
		"old_settings": oldSettings,
		"new_settings": settings,
	}
	m.publishConfigChangeEvent("module.settings.updated", moduleName, eventData)

	m.logger.Info("Module settings updated", zap.String("module", moduleName))

	return nil
}

// ReloadModuleConfig 重新加载模块配置
func (m *DefaultModuleConfigManager) ReloadModuleConfig(moduleName string) error {
	// 从数据库重新加载
	var config model.ModuleConfig
	err := m.db.Where("module_name = ?", moduleName).First(&config).Error
	if err != nil {
		return fmt.Errorf("failed to reload module config: %w", err)
	}

	// 更新缓存
	m.cacheMu.Lock()
	m.configCache[moduleName] = &config
	m.cacheMu.Unlock()

	// 更新事件映射
	m.updateEventMapping(&config)

	m.logger.Info("Module config reloaded", zap.String("module", moduleName))

	return nil
}

// ReloadAllConfigs 重新加载所有配置
func (m *DefaultModuleConfigManager) ReloadAllConfigs() error {
	configs, err := m.GetAllModuleConfigs()
	if err != nil {
		return err
	}

	// 清空缓存
	m.cacheMu.Lock()
	m.configCache = make(map[string]*model.ModuleConfig)
	m.cacheMu.Unlock()

	// 重新构建缓存和事件映射
	for _, config := range configs {
		m.cacheMu.Lock()
		m.configCache[config.ModuleName] = config
		m.cacheMu.Unlock()

		if config.Enabled {
			m.updateEventMapping(config)
		}
	}

	m.logger.Info("All module configs reloaded", zap.Int("count", len(configs)))

	return nil
}

// CheckDependencies 检查模块依赖
func (m *DefaultModuleConfigManager) CheckDependencies(moduleName string) error {
	config, err := m.GetModuleConfig(moduleName)
	if err != nil {
		return err
	}

	for _, dep := range config.Dependencies {
		depConfig, err := m.GetModuleConfig(dep)
		if err != nil {
			return fmt.Errorf("dependency %s not found", dep)
		}

		if !depConfig.Enabled {
			return fmt.Errorf("dependency %s is not enabled", dep)
		}
	}

	return nil
}

// GetDependentModules 获取依赖指定模块的模块列表
func (m *DefaultModuleConfigManager) GetDependentModules(moduleName string) ([]string, error) {
	var configs []*model.ModuleConfig
	err := m.db.Where("enabled = ?", true).Find(&configs).Error
	if err != nil {
		return nil, err
	}

	var dependents []string
	for _, config := range configs {
		for _, dep := range config.Dependencies {
			if dep == moduleName {
				dependents = append(dependents, config.ModuleName)
				break
			}
		}
	}

	return dependents, nil
}

// RegisterConfigChangeListener 注册配置变更监听器
func (m *DefaultModuleConfigManager) RegisterConfigChangeListener(listener ModuleConfigChangeListener) {
	m.listenerMu.Lock()
	defer m.listenerMu.Unlock()
	m.listeners = append(m.listeners, listener)
}

// 私有方法

// initializeCache 初始化配置缓存
func (m *DefaultModuleConfigManager) initializeCache() {
	configs, err := m.GetAllModuleConfigs()
	if err != nil {
		m.logger.Error("Failed to initialize config cache", zap.Error(err))
		return
	}

	// 初始化事件映射
	for _, config := range configs {
		if config.Enabled {
			m.updateEventMapping(config)
		}
	}

	m.logger.Info("Module config cache initialized", zap.Int("count", len(configs)))
}

// updateEventMapping 更新事件映射
func (m *DefaultModuleConfigManager) updateEventMapping(config *model.ModuleConfig) {
	if !config.Enabled {
		return
	}

	// 注册模块事件映射
	publishes := config.Events.Publishes
	listens := make([]EventListenerConfig, len(config.Events.Listens))
	for i, listener := range config.Events.Listens {
		listens[i] = EventListenerConfig{
			Event:    listener.Event,
			Handler:  listener.Handler,
			Priority: listener.Priority,
		}
	}

	m.eventMapper.RegisterModuleEvents(config.ModuleName, publishes, listens)
}

// notifyModuleEnabled 通知模块启用
func (m *DefaultModuleConfigManager) notifyModuleEnabled(moduleName string, config *model.ModuleConfig) {
	m.listenerMu.RLock()
	defer m.listenerMu.RUnlock()

	for _, listener := range m.listeners {
		listener.OnModuleEnabled(moduleName, config)
	}
}

// notifyModuleDisabled 通知模块禁用
func (m *DefaultModuleConfigManager) notifyModuleDisabled(moduleName string, config *model.ModuleConfig) {
	m.listenerMu.RLock()
	defer m.listenerMu.RUnlock()

	for _, listener := range m.listeners {
		listener.OnModuleDisabled(moduleName, config)
	}
}

// notifyConfigChanged 通知配置变更
func (m *DefaultModuleConfigManager) notifyConfigChanged(moduleName string, oldConfig, newConfig *model.ModuleConfig) {
	m.listenerMu.RLock()
	defer m.listenerMu.RUnlock()

	for _, listener := range m.listeners {
		listener.OnModuleConfigChanged(moduleName, oldConfig, newConfig)
	}
}

// publishConfigChangeEvent 发布配置变更事件
func (m *DefaultModuleConfigManager) publishConfigChangeEvent(eventName, moduleName string, extraData map[string]interface{}) {
	eventData := map[string]interface{}{
		"module_name": moduleName,
		"timestamp":   time.Now().Unix(),
	}

	if extraData != nil {
		for k, v := range extraData {
			eventData[k] = v
		}
	}

	event := m.eventManager.CreateEvent(context.Background(), contract.EventName(eventName), eventData)
	if err := m.eventManager.PublishEvent(event); err != nil {
		m.logger.Error("Failed to publish config change event",
			zap.String("event", eventName),
			zap.String("module", moduleName),
			zap.Error(err),
		)
	}
}
