/**
 * @file: site-settings.js
 * @description: GACMS站点设置页面特定脚本
 * @author: <PERSON><PERSON>
 * @email: <EMAIL>
 * @copyright: Copyright (c) 2025 Cion Nieh
 */

/**
 * @function openTab
 * @description 切换选项卡显示内容
 * @param {Event} evt - 点击事件
 * @param {string} tabName - 要显示的选项卡ID
 */
function openTab(evt, tabName) {
    var i, tabcontent, tabbuttons;
    tabcontent = document.getElementsByClassName("tab-content");
    for (i = 0; i < tabcontent.length; i++) {
        tabcontent[i].style.display = "none";
    }
    tabbuttons = document.getElementsByClassName("tab-button");
    for (i = 0; i < tabbuttons.length; i++) {
        tabbuttons[i].className = tabbuttons[i].className.replace(" active", "");
    }
    document.getElementById(tabName).style.display = "block";
    evt.currentTarget.className += " active";
}

/**
 * @function previewUploadedImage
 * @description 显示上传图片的预览
 * @param {Event} event - 文件输入变更事件
 * @param {string} previewElementId - 显示预览的img元素ID
 */
function previewUploadedImage(event, previewElementId) {
    const reader = new FileReader();
    const previewElement = document.getElementById(previewElementId);

    reader.onload = function(){
        if (previewElement) {
            previewElement.src = reader.result;
        }
    }
    if(event.target.files[0]){
        reader.readAsDataURL(event.target.files[0]);
    } else {
        // 如果没有选择文件，可以选择重置为默认图片或隐藏
        // previewElement.src = 'default-placeholder.png'; 
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 默认显示第一个选项卡
    document.querySelector('.tab-button').click();
    
    // 初始化侧边栏
    if (typeof initSidebar === 'function') {
        initSidebar();
    }
    
    // 高亮当前页面菜单项
    if (typeof highlightCurrentPageMenuItem === 'function') {
        highlightCurrentPageMenuItem();
    }
});