# ADR-004: 平台级架构重构与设计深化

## 状态
已接受

## 摘要
本文档提出了一套对 GACMS 现有 v5 架构（见 `ADR-001`）的全面深化和重构方案。其核心目标是将平台从"模块化的应用"演进为真正的"微核心平台生态"，通过引入更明确的核心引擎、多租户原生支持、标准化的扩展开发套件（SDK）和更成熟的配置与前端架构，为项目的长期发展奠定坚实基础。

## 背景
`ADR-001` 所定义的 v5 架构成功地引入了"万物皆模块"的核心理念，但在实践中，核心与模块的边界、多租户的实现、第三方开发的体验等方面仍存在模糊地带。随着项目复杂度的提升，我们需要一套更专业、更严谨、更具前瞻性的架构来指导后续开发。这份新设计正是基于 v5 的实践反馈和对未来扩展的思考而提出的。

## 提议的架构变更
新架构在保留 v5 核心思想的基础上，进行了以下关键深化设计：

### 1. **核心引擎（Micro-kernel Engine）的显式化**
- **提议**：将原先分散在 `internal` 各处的平台级逻辑，集中到新建的 `internal/core` 目录中。
- **结构**：
  ```
  internal/core/
  ├── contract/       # 平台级核心契约 (Module, Plugin, Theme)
  ├── service/        # 核心服务 (ModuleRegistry, SiteCoordinator)
  └── di/             # 依赖注入容器
  ```
- **理由**：使平台核心的职责更清晰，与跨模块的 `infrastructure` 和 `port` 彻底分离，降低认知负荷。

### 2. **原生多租户（Multi-tenancy）架构**
- **提议**：将多租户设计从一个普通模块提升为平台的一等公民。
- **实现**：
    - **配置隔离**：`configs/sites/{site_id}` 目录结构，实现配置的继承与覆盖。
    - **数据隔离**：在数据库基础设施层（`internal/infrastructure/database`）自动为查询注入 `site_id` 过滤。
    - **资源隔离**：文件上传路径按站点划分，如 `uploads/sites/{site_id}`。
- **理由**：从根本上解决多站点的数据和配置安全问题，简化模块开发，使其默认即支持多租户。

### 3. **标准化扩展开发套件（SDK）**
- **提议**：在 `pkg/` 目录下为不同类型的扩展创建专用的开发套件。
- **结构**：
  ```
  pkg/
  ├── moduleSdk/      # 模块开发套件
  ├── pluginSdk/      # 插件开发套件
  └── themeSdk/       # 主题开发套件
  ```
- **理由**：极大地降低第三方开发者的入门门槛，规范扩展的开发模式，提高生态系统的健壮性。

### 4. **前端架构现代化**
- **提议**：将前端架构从"功能优先（Feature-First）"升级为与后端对齐的"模块化（Module-based）"架构。
- **结构**：
  ```
  web/src/
  ├── core/           # 前端核心框架（路由、状态管理、API客户端）
  └── modules/        # 与后端模块一一对应的前端模块
  ```
- **理由**：实现前后端的同构，使功能开发更内聚，便于大型团队协作和长期维护。

### 5. **三层配置体系**
- **提议**：建立 `全局配置` -> `站点配置` -> `环境覆盖` 的三层配置加载与合并机制。
- **理由**：提供极高的灵活性和清晰的配置管理方案，适应从开发到生产的各种环境需求。

## 与当前架构 (ADR-001) 的对比

| 方面 | ADR-001 (当前) | ADR-004 (提议) | 变更影响 |
| :--- | :--- | :--- | :--- |
| **核心逻辑** | 分散在 `internal` | 集中于 `internal/core` | **高** (需重构) |
| **多租户** | `site` 模块实现 | 平台原生支持 | **高** (需重构) |
| **配置管理** | 结构模糊 | 清晰的三层结构 | **高** (需重构) |
| **扩展开发** | 依赖核心契约 | 提供标准化SDK | **高** (新增开发) |
| **前端架构** | 功能优先 | 模块化 | **高** (需重构) |
| **整体理念** | 模块化的应用 | 微核心平台生态 | **战略升级** |

## 架构迁移可行性与工作量评估

### 可行性
该架构迁移**完全可行**。提议的设计并非颠覆式革命，而是对现有理念的合理演进和深化，解决了当前架构的痛点，逻辑上更自洽，技术上没有不可逾越的障碍。

### 工作量估算（人/周）
这是一个**重大级别的重构**，涉及项目方方面面，预计总工作量约为 **8 - 12 人周**。

- **阶段一：后端核心重构 (2-3 人周)**
  - 任务：创建 `internal/core`，迁移核心服务，重构依赖注入和模块加载器。
- **阶段二：配置与多租户体系实现 (2-3 人周)**
  - 任务：实现三层配置加载器，改造数据库层以支持原生多租户，调整文件存储。
- **阶段三：前端架构重构 (3-4 人周)**
  - 任务：搭建新的模块化前端框架，并逐步迁移现有页面和组件。
- **阶段四：SDK 开发 (并行, 2 人周)**
  - 任务：设计并实现 `moduleSdk`, `pluginSdk`, `themeSdk`。
- **阶段五：模块迁移与适配 (持续进行)**
  - 任务：将现有模块（如User, Post）逐一迁移至新架构，并适配多租户和新配置。

## 后果与风险

### 积极后果
1.  **架构清晰**：平台职责、模块职责、扩展职责高度清晰，极大降低新成员的理解成本。
2.  **开发提效**：SDK的引入将大幅提升新模块和插件的开发效率和质量。
3.  **高可维护性**：清晰的边界和原生多租户支持将使系统在未来更易于维护和扩展。
4.  **生态系统**：为构建开放的第三方开发者生态奠定了坚实的技术基础。

### 消极后果与风险
1.  **重构成本高**：短期内需要投入大量开发资源，可能会影响新功能的迭代速度。
2.  **迁移阵痛**：在迁移过程中，可能出现新旧代码并存的过渡状态，增加开发和测试的复杂度。
3.  **技术要求高**：新的架构对开发团队的纪律性和理解力提出了更高的要求，需要防止架构腐化。

## 最终建议

**强烈建议采纳并实施此次架构升级。**

虽然短期投入巨大，但这是 GACMS 从一个"项目"走向一个"平台"的关键一步。其带来的长期收益（可维护性、可扩展性、开发效率、生态潜力）将远超重构成本。

**实施策略建议：**
1.  **正式立项**：将此架构重构作为一次独立的技术升级项目，获得管理层支持，并分配合理的资源。
2.  **分阶段实施**：严格按照上述估算的阶段进行，优先重构后端基础，再造前端，最后逐个迁移业务模块。
3.  **冻结主干**：在核心重构期间，可以考虑冻结主干的大功能开发，或采用特性分支策略，待重构完成后再合并，以降低冲突风险。
4.  **文档先行**：在编码前，先将此 ADR 文档完善并达成共识，作为后续所有重构工作的"宪法"。
