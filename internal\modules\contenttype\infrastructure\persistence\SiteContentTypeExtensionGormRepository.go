/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/contenttype/infrastructure/persistence/SiteContentTypeExtensionGormRepository.go
 * @Description: 
 * 
 * © 2025 GACMS. All rights reserved.
 */

package persistence

import (
	"gacms/internal/modules/contenttype/domain/model"
	"gorm.io/gorm"
)

type SiteContentTypeExtensionGormRepository struct {
	db *gorm.DB
}

func NewSiteContentTypeExtensionRepository(db *gorm.DB) *SiteContentTypeExtensionGormRepository {
	return &SiteContentTypeExtensionGormRepository{db: db}
}

func (r *SiteContentTypeExtensionGormRepository) FindBySiteAndContentType(siteID, contentTypeID uint) (*model.SiteContentTypeExtension, error) {
	var extension model.SiteContentTypeExtension
	err := r.db.Where("site_id = ? AND content_type_id = ?", siteID, contentTypeID).First(&extension).Error
	if err != nil {
		return nil, err
	}
	return &extension, nil
} 