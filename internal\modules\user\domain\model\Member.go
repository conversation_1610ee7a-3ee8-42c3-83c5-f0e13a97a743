/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: {current_date}
 * @LastEditors: C<PERSON>
 * @LastEditTime: {current_date}
 * @FilePath: internal/modules/user/domain/model/Member.go
 * @Description: Defines the data model for the frontend user (Member),
 *               corresponding to the 'members' database table.
 *
 * © {current_year} GACMS. All rights reserved.
 */
package model

import "time"

// Member represents a frontend user of the system.
type Member struct {
	ID           uint      `gorm:"primaryKey"`
	SiteID       uint      `gorm:"not null;uniqueIndex:idx_site_username"`
	Username     string    `gorm:"not null;uniqueIndex:idx_site_username"`
	Email        *string   // Pointer to allow NULL
	PasswordHash string    `gorm:"not null"`
	CreatedAt    time.Time
	UpdatedAt    time.Time

	// Relationships
	SocialIdentities []SocialIdentity `gorm:"foreignKey:MemberID"`
	Roles            []*UserRole      `gorm:"foreignKey:UserID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
} 