<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 角色编辑</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .permission-group {
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        
        .permission-group-header {
            padding: 12px 15px;
            background: rgba(0,0,0,0.2);
            border-bottom: 1px solid rgba(255,255,255,0.1);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .permission-group-body {
            padding: 15px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        
        .permission-group-body.expanded {
            max-height: 1000px;
        }
        
        .permission-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        
        .permission-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        
        .permission-item-label {
            flex: 1;
            margin-left: 10px;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 页面标题 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">编辑角色</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <a href="roles.html" class="flex items-center justify-center bg-gray-700 text-white px-5 py-3 rounded-lg font-medium transition-all hover:bg-gray-600">
                            <span class="relative flex items-center">
                                <i class="fas fa-arrow-left mr-2"></i>
                                返回角色列表
                            </span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 编辑表单 -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 左侧基本信息 -->
                <div class="lg:col-span-1">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold text-white mb-4">基本信息</h3>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2" for="roleName">角色名称</label>
                            <input type="text" id="roleName" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" value="高级编辑">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2" for="roleDesc">角色描述</label>
                            <textarea id="roleDesc" rows="3" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">拥有内容编辑、审核和部分系统设置权限的编辑角色</textarea>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2" for="roleType">角色类型</label>
                            <select id="roleType" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="custom">自定义角色</option>
                                <option value="system">系统角色</option>
                            </select>
                        </div>
                        
                        <div class="mb-4">
                            <label class="flex items-center text-gray-300 cursor-pointer">
                                <input type="checkbox" class="mr-2" checked>
                                <span>启用角色</span>
                            </label>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">上级角色</label>
                            <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">无</option>
                                <option value="1">超级管理员</option>
                                <option value="2" selected>编辑</option>
                                <option value="3">作者</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- 角色统计 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold text-white mb-4">角色统计</h3>
                        
                        <div class="flex items-center mb-3">
                            <div class="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-users text-blue-500"></i>
                            </div>
                            <div>
                                <div class="text-gray-400 text-sm">已分配用户</div>
                                <div class="text-white font-medium">8 人</div>
                            </div>
                        </div>
                        
                        <div class="flex items-center mb-3">
                            <div class="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-shield-alt text-green-500"></i>
                            </div>
                            <div>
                                <div class="text-gray-400 text-sm">已拥有权限</div>
                                <div class="text-white font-medium">32 项</div>
                            </div>
                        </div>
                        
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-purple-500/20 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-calendar-alt text-purple-500"></i>
                            </div>
                            <div>
                                <div class="text-gray-400 text-sm">创建时间</div>
                                <div class="text-white font-medium">2024-05-12</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 保存按钮 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <div class="flex flex-col gap-3">
                            <button class="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30">
                                <i class="fas fa-save mr-2"></i> 保存角色
                            </button>
                            <button class="w-full bg-gray-700 text-white px-5 py-3 rounded-lg font-medium transition-all hover:bg-gray-600">
                                <i class="fas fa-times mr-2"></i> 取消
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧权限设置 -->
                <div class="lg:col-span-2">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold text-white mb-4">权限设置</h3>
                        
                        <!-- 权限搜索 -->
                        <div class="mb-6">
                            <div class="relative">
                                <input type="text" placeholder="搜索权限..." 
                                       class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent pl-10">
                                <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                    <i class="fas fa-search"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex justify-between items-center mb-4">
                            <div>
                                <label class="flex items-center text-gray-300 cursor-pointer">
                                    <input type="checkbox" class="mr-2" id="checkAllPermissions">
                                    <span>全选</span>
                                </label>
                            </div>
                            <div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-gray-400">共 85 项权限</span>
                                    <button class="text-blue-500 hover:text-blue-400">
                                        <i class="fas fa-expand-arrows-alt mr-1"></i> 展开全部
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 权限模块 -->
                        <div class="space-y-4">
                            <!-- 内容管理模块 -->
                            <div class="permission-group">
                                <div class="permission-group-header">
                                    <div class="flex items-center">
                                        <input type="checkbox" class="mr-3" checked>
                                        <span class="text-white">内容管理</span>
                                    </div>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="permission-group-body expanded">
                                    <div class="permission-item">
                                        <input type="checkbox" checked>
                                        <div class="permission-item-label">
                                            <div class="text-white">查看文章</div>
                                            <div class="text-gray-400 text-xs">允许查看所有文章</div>
                                        </div>
                                    </div>
                                    <div class="permission-item">
                                        <input type="checkbox" checked>
                                        <div class="permission-item-label">
                                            <div class="text-white">创建文章</div>
                                            <div class="text-gray-400 text-xs">允许创建新文章</div>
                                        </div>
                                    </div>
                                    <div class="permission-item">
                                        <input type="checkbox" checked>
                                        <div class="permission-item-label">
                                            <div class="text-white">编辑文章</div>
                                            <div class="text-gray-400 text-xs">允许编辑所有文章</div>
                                        </div>
                                    </div>
                                    <div class="permission-item">
                                        <input type="checkbox" checked>
                                        <div class="permission-item-label">
                                            <div class="text-white">删除文章</div>
                                            <div class="text-gray-400 text-xs">允许删除所有文章</div>
                                        </div>
                                    </div>
                                    <div class="permission-item">
                                        <input type="checkbox" checked>
                                        <div class="permission-item-label">
                                            <div class="text-white">管理评论</div>
                                            <div class="text-gray-400 text-xs">允许审核、回复和删除评论</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 用户管理模块 -->
                            <div class="permission-group">
                                <div class="permission-group-header">
                                    <div class="flex items-center">
                                        <input type="checkbox" class="mr-3">
                                        <span class="text-white">用户管理</span>
                                    </div>
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                                <div class="permission-group-body">
                                    <div class="permission-item">
                                        <input type="checkbox">
                                        <div class="permission-item-label">
                                            <div class="text-white">查看用户</div>
                                            <div class="text-gray-400 text-xs">允许查看所有用户信息</div>
                                        </div>
                                    </div>
                                    <div class="permission-item">
                                        <input type="checkbox">
                                        <div class="permission-item-label">
                                            <div class="text-white">创建用户</div>
                                            <div class="text-gray-400 text-xs">允许创建新用户</div>
                                        </div>
                                    </div>
                                    <div class="permission-item">
                                        <input type="checkbox">
                                        <div class="permission-item-label">
                                            <div class="text-white">编辑用户</div>
                                            <div class="text-gray-400 text-xs">允许编辑用户信息</div>
                                        </div>
                                    </div>
                                    <div class="permission-item">
                                        <input type="checkbox">
                                        <div class="permission-item-label">
                                            <div class="text-white">删除用户</div>
                                            <div class="text-gray-400 text-xs">允许删除用户账号</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 系统设置模块 -->
                            <div class="permission-group">
                                <div class="permission-group-header">
                                    <div class="flex items-center">
                                        <input type="checkbox" class="mr-3" checked>
                                        <span class="text-white">系统设置</span>
                                    </div>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="permission-group-body expanded">
                                    <div class="permission-item">
                                        <input type="checkbox" checked>
                                        <div class="permission-item-label">
                                            <div class="text-white">网站设置</div>
                                            <div class="text-gray-400 text-xs">允许修改网站基本配置</div>
                                        </div>
                                    </div>
                                    <div class="permission-item">
                                        <input type="checkbox" checked>
                                        <div class="permission-item-label">
                                            <div class="text-white">系统优化</div>
                                            <div class="text-gray-400 text-xs">允许执行系统优化操作</div>
                                        </div>
                                    </div>
                                    <div class="permission-item">
                                        <input type="checkbox">
                                        <div class="permission-item-label">
                                            <div class="text-white">数据备份</div>
                                            <div class="text-gray-400 text-xs">允许进行数据备份和恢复</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>
    
    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            
            // 加载顶部导航栏
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 权限组展开/折叠
            const permissionGroupHeaders = document.querySelectorAll('.permission-group-header');
            
            permissionGroupHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    const group = this.closest('.permission-group');
                    const body = group.querySelector('.permission-group-body');
                    const icon = this.querySelector('i');
                    
                    body.classList.toggle('expanded');
                    
                    if(icon) {
                        if(body.classList.contains('expanded')) {
                            icon.classList.remove('fa-chevron-right');
                            icon.classList.add('fa-chevron-down');
                        } else {
                            icon.classList.remove('fa-chevron-down');
                            icon.classList.add('fa-chevron-right');
                        }
                    }
                });
            });
            
            // 权限组全选/取消全选
            const permissionGroupCheckboxes = document.querySelectorAll('.permission-group-header input[type="checkbox"]');
            
            permissionGroupCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const group = this.closest('.permission-group');
                    const itemCheckboxes = group.querySelectorAll('.permission-item input[type="checkbox"]');
                    
                    itemCheckboxes.forEach(itemCheckbox => {
                        itemCheckbox.checked = this.checked;
                    });
                });
            });
            
            // 全选/取消全选
            const checkAllPermissions = document.getElementById('checkAllPermissions');
            if(checkAllPermissions) {
                checkAllPermissions.addEventListener('change', function() {
                    const allCheckboxes = document.querySelectorAll('.permission-group input[type="checkbox"]');
                    allCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                });
            }
        });
    </script>
</body>
</html> 