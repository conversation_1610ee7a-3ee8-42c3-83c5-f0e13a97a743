/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/modules/extension/infrastructure/persistence/ExtensionGormRepository.go
 * @Description: GORM implementation of the ExtensionRepository.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package persistence

import (
	"gacms/internal/modules/extension/domain/model"
	"gorm.io/gorm"
)

type ExtensionGormRepository struct {
	db *gorm.DB
}

func NewExtensionGormRepository(db *gorm.DB) *ExtensionGormRepository {
	return &ExtensionGormRepository{db: db}
}

func (r *ExtensionGormRepository) Create(extension *model.Extension) error {
	return r.db.Create(extension).Error
}

func (r *ExtensionGormRepository) Update(extension *model.Extension) error {
	return r.db.Save(extension).Error
}

func (r *ExtensionGormRepository) Delete(id uint) error {
	return r.db.Delete(&model.Extension{}, id).Error
}

func (r *ExtensionGormRepository) FindByDirName(dirName string) (*model.Extension, error) {
	var extension model.Extension
	err := r.db.Where("directory_name = ?", dirName).First(&extension).Error
	return &extension, err
}

func (r *ExtensionGormRepository) FindByType(extType string) ([]*model.Extension, error) {
	var extensions []*model.Extension
	err := r.db.Where("type = ?", extType).Find(&extensions).Error
	return extensions, err
}

func (r *ExtensionGormRepository) GetAll() ([]*model.Extension, error) {
	var extensions []*model.Extension
	err := r.db.Find(&extensions).Error
	return extensions, err
} 