/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/ModuleRouteRegistration.go
 * @Description: 模块主动路由注册机制，支持模块通过公共库接口注册路由
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"fmt"
	"strings"

	"gacms/pkg/contract"
	"go.uber.org/zap"
)

// ModuleRouteRegistrar 模块路由注册器
// 提供模块主动注册路由的机制
type ModuleRouteRegistrar struct {
	router   contract.Router
	logger   *zap.Logger
	eventMgr contract.EventManager
}

// NewModuleRouteRegistrar 创建模块路由注册器
func NewModuleRouteRegistrar(router contract.Router, logger *zap.Logger, eventMgr contract.EventManager) *ModuleRouteRegistrar {
	registrar := &ModuleRouteRegistrar{
		router:   router,
		logger:   logger,
		eventMgr: eventMgr,
	}

	// 监听模块加载事件，自动注册模块路由
	registrar.setupEventListeners()

	return registrar
}

// RegisterModuleRoutes 注册模块的所有路由
func (r *ModuleRouteRegistrar) RegisterModuleRoutes(moduleName string, instance *ModuleInstance) error {
	r.logger.Info("Registering module routes",
		zap.String("module", moduleName),
		zap.Uint("site_id", instance.SiteID),
		zap.Int("routes_count", len(instance.Routes)),
	)

	for _, route := range instance.Routes {
		err := r.registerSingleRoute(moduleName, &route, instance)
		if err != nil {
			r.logger.Error("Failed to register route",
				zap.String("module", moduleName),
				zap.String("pattern", route.Pattern),
				zap.String("method", route.Method),
				zap.Error(err),
			)
			continue
		}
	}

	// 发布路由注册完成事件
	r.publishRouteRegistrationEvent(moduleName, instance)

	return nil
}

// registerSingleRoute 注册单个路由
func (r *ModuleRouteRegistrar) registerSingleRoute(moduleName string, route *RouteInfo, instance *ModuleInstance) error {
	// 获取控制器实例
	controller, exists := instance.GetController(route.Controller)
	if !exists {
		return fmt.Errorf("controller %s not found in module %s", route.Controller, moduleName)
	}

	// 创建路由处理器
	handler := r.createRouteHandler(controller, route.Action)

	// 根据路径前缀确定入口点
	entryPoint := r.determineEntryPoint(route.Pattern)

	// 注册路由
	return r.router.RegisterRoute(entryPoint, route.Method, route.Pattern, handler)
}

// createRouteHandler 创建路由处理器
func (r *ModuleRouteRegistrar) createRouteHandler(controller interface{}, action string) interface{} {
	// 这里可以创建一个包装器，用于调用控制器的指定方法
	// 暂时返回控制器本身，实际应用中需要更复杂的处理
	return controller
}

// determineEntryPoint 根据路径确定入口点
func (r *ModuleRouteRegistrar) determineEntryPoint(pattern string) contract.RouteTarget {
	// 根据路径前缀确定入口点
	if len(pattern) > 0 && pattern[0] == '/' {
		parts := strings.Split(pattern[1:], "/")
		if len(parts) > 0 {
			switch parts[0] {
			case "admin":
				return contract.AdminRoute
			case "api":
				return contract.ThirdPartyAPIRoute
			default:
				return contract.FrontendRoute
			}
		}
	}
	return contract.FrontendRoute
}

// setupEventListeners 设置事件监听器
func (r *ModuleRouteRegistrar) setupEventListeners() {
	// 监听模块加载事件
	r.eventMgr.Subscribe("module.loaded", r.onModuleLoaded)

	r.logger.Debug("Module route registrar event listeners setup")
}

// onModuleLoaded 处理模块加载事件
func (r *ModuleRouteRegistrar) onModuleLoaded(event contract.Event) {
	eventData := event.GetData()
	moduleName, ok := eventData["module_name"].(string)
	if !ok {
		return
	}

	// 这里需要获取模块实例并注册路由
	// 实际实现中需要与ModuleProxyFactory集成
	r.logger.Debug("Module loaded, registering routes",
		zap.String("module", moduleName),
	)

	// TODO: 获取模块实例并注册路由
	// instance := moduleFactory.GetModuleInstance(moduleName)
	// r.RegisterModuleRoutes(moduleName, instance)
}

// publishRouteRegistrationEvent 发布路由注册事件
func (r *ModuleRouteRegistrar) publishRouteRegistrationEvent(moduleName string, instance *ModuleInstance) {
	eventData := map[string]interface{}{
		"module_name":   moduleName,
		"site_id":       instance.SiteID,
		"routes_count":  len(instance.Routes),
		"is_global":     instance.IsGlobal,
	}

	ctx := context.Background()
	if instance.SiteID > 0 {
		// 如果是租户模块，添加租户上下文
		// ctx = database.WithSiteID(ctx, instance.SiteID)
	}

	event := r.eventMgr.CreateEvent(ctx, "module.routes.registered", eventData)
	if err := r.eventMgr.PublishEvent(event); err != nil {
		r.logger.Error("Failed to publish route registration event",
			zap.String("module", moduleName),
			zap.Error(err),
		)
	}
}

// GetRegisteredRoutes 获取已注册的路由信息
func (r *ModuleRouteRegistrar) GetRegisteredRoutes() map[contract.RouteTarget][]contract.RouteInfo {
	return r.router.GetAllRoutes()
}

// GetModuleRoutes 获取指定模块的路由
func (r *ModuleRouteRegistrar) GetModuleRoutes(moduleName string) []contract.RouteInfo {
	allRoutes := r.router.GetAllRoutes()
	var moduleRoutes []contract.RouteInfo

	for _, routes := range allRoutes {
		for _, route := range routes {
			if route.Module == moduleName {
				moduleRoutes = append(moduleRoutes, route)
			}
		}
	}

	return moduleRoutes
}

// UnregisterModuleRoutes 注销模块路由
func (r *ModuleRouteRegistrar) UnregisterModuleRoutes(moduleName string) error {
	// TODO: 实现路由注销逻辑
	r.logger.Info("Unregistering module routes",
		zap.String("module", moduleName),
	)

	// 发布路由注销事件
	eventData := map[string]interface{}{
		"module_name": moduleName,
	}

	event := r.eventMgr.CreateEvent(context.Background(), "module.routes.unregistered", eventData)
	return r.eventMgr.PublishEvent(event)
}

// ValidateRoutes 验证路由配置
func (r *ModuleRouteRegistrar) ValidateRoutes(routes []RouteInfo) []error {
	var errors []error

	for _, route := range routes {
		if route.Pattern == "" {
			errors = append(errors, fmt.Errorf("route pattern cannot be empty"))
		}
		if route.Method == "" {
			errors = append(errors, fmt.Errorf("route method cannot be empty"))
		}
		if route.Controller == "" {
			errors = append(errors, fmt.Errorf("route controller cannot be empty"))
		}
		if route.Action == "" {
			errors = append(errors, fmt.Errorf("route action cannot be empty"))
		}
	}

	return errors
}
