/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/category/domain/model/Category.go
 * @Description: Defines the Category domain model.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package model

import "gorm.io/gorm"

// Category represents a content category.
type Category struct {
	gorm.Model
	SiteID      uint   `gorm:"index"`
	ParentID    *uint  `gorm:"index"` // Pointer for nullable foreign key
	Name        string `gorm:"type:varchar(255);not null"`
	Slug        string `gorm:"type:varchar(255);uniqueIndex;not null"`
	Description string `gorm:"type:text"`
	SortOrder   int    `gorm:"default:0"`
} 