/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/topic/domain/model/Topic.go
 * @Description: Defines the Topic domain model.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package model

import "gorm.io/gorm"

// Topic represents a collection of posts.
type Topic struct {
	gorm.Model
	SiteID      uint   `gorm:"index"`
	Name        string `gorm:"type:varchar(255);not null"`
	Slug        string `gorm:"type:varchar(255);uniqueIndex;not null"`
	Description string `gorm:"type:text"`
	CoverImage  string `gorm:"type:varchar(512)"`
	Status      int    `gorm:"default:1"` // 1: published, 0: draft
} 