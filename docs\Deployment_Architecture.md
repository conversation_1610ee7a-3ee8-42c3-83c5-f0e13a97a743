<!--
Author: <PERSON><PERSON>eh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
-->
# 亘安网站内容管理系统 (GACMS) - 部署架构文档 (v2)

## 目录
// ... existing code ...
  - [4.5. 搜索服务 (Search Service)](#45-搜索服务-search-service)
- [5. 部署流程与工具](#5-部署流程与工具)
  - [5.1. CI/CD 流程设计](#51-cicd-流程设计)
    - [5.1.1. 源码到镜像 (Source-to-Image)](#511-源码到镜像-sourcetoimage)
    - [5.1.2. 模块化构建策略](#512-模块化构建策略)
  - [5.2. 版本控制策略](#52-版本控制策略)
  - [5.3. 部署工具链](#53-部署工具链)
// ... existing code ...
  - [5.4. 回滚策略](#54-回滚策略)
- [6. 监控、告警与日志管理](#6-监控告警与日志管理)
// ... existing code ...
  - [4.5. 搜索服务 (Search Service)](#45-搜索服务-search-service)
- [5. 部署流程与工具](#5-部署流程与工具)
  - [5.1. CI/CD 流程设计](#51-cicd-流程设计)
  - [5.2. 版本控制策略](#52-版本控制策略)
  - [5.3. 部署工具链](#53-部署工具链)
// ... existing code ...
  - [4.5. 搜索服务 (Search Service)](#45-搜索服务-search-service)

---

## 5. 部署流程与工具

本章定义了将GACMS代码从版本控制系统安全、高效、可靠地部署到各环境的流程和所使用的工具。

### 5.1. CI/CD 流程设计

我们采用基于Git的持续集成与持续部署（CI/CD）流程，以实现开发、测试和发布的自动化。

- **触发**: 当代码被推送到特定分支（如 `develop`, `main`）或创建Pull Request时，CI/CD流程自动触发。
- **CI (持续集成) 阶段**:
  1.  **代码检出**: 从Git仓库拉取最新代码。
  2.  **静态代码分析**: 运行`golangci-lint` (后端) 和 `ESLint`/`Stylelint` (前端) 进行代码质量和风格检查。
  3.  **单元测试**: 运行所有单元测试，并检查代码覆盖率。
  4.  **依赖安装与构建**:
      -   **后端**: 使用Go Modules下载依赖，并根据`cmd/gacms/Main.go`中的模块注册表，编译生成**包含所有指定模块**的单一Go二进制可执行文件。
      -   **前端**: 使用npm/yarn安装依赖，并使用Vite/Webpack构建出静态资源文件。
  5.  **容器化**: 将后端二进制文件和前端静态资源打包成一个轻量级的Docker镜像。
  6.  **推送镜像**: 将构建好的Docker镜像推送到容器镜像仓库（如 Docker Hub, AWS ECR, GCR）。
- **CD (持续部署) 阶段**:
  1.  **部署到测试环境**: 当代码合并到`develop`分支后，自动将最新的Docker镜像部署到测试环境。
  2.  **自动化测试**: 在测试环境运行API测试、端到端测试等自动化集成测试。
  3.  **部署到预生产环境**: 当准备发布时，将经过测试的镜像手动或自动部署到预生产环境，进行最终验证。
  4.  **部署到生产环境**: 在所有测试和验证通过后，通过手动审批或自动化流程，将镜像部署到生产环境（可采用蓝绿部署或金丝雀发布策略）。

#### 5.1.1. 源码到镜像 (Source-to-Image)

整个构建过程的核心是将源代码转换为一个可部署的、自包含的Docker镜像。这个镜像将包含：
-   编译后的Go后端二进制文件。
-   构建后的前端静态文件（HTML, CSS, JS）。
-   应用的配置文件模板。
-   启动应用的必要脚本。

#### 5.1.2. 模块化构建策略

GACMS的平台化架构允许我们通过编译时组合不同的模块，来构建出功能不同的产品版本。

- **核心机制**: `cmd/gacms/Main.go` 文件是应用入口，同时也是**模块注册中心**。CI/CD流水线可以根据不同的构建目标（如"专业版"、"商业版"），动态地修改或选择不同的`Main.go`文件版本来进行编译。
- **核心机制**: `cmd/gacms/Main.go` 文件是整个GACMS平台的**心脏和启动器**。它定义了当前部署实例**需要加载哪些模块**。CI/CD流水线通过管理这个文件的内容（或使用构建标签选择不同的main文件），来最终决定编译出的二进制文件包含哪些功能。
- **实现方式**:
    -   可以使用Go的构建标签（Build Tags）。为不同版本的产品（如`community`, `professional`, `enterprise`）提供不同的`main_xx.go`文件，每个文件导入和注册不同的模块组合。在编译时通过 `-tags` 参数选择性地编译。
    -   也可以通过脚本在CI/CD流程中，根据目标版本动态生成`Main.go`文件，再进行编译。
- **优势**: 这种方式使得我们可以用一套核心代码库，通过不同的模块组合，灵活地构建和交付满足不同客户需求的GACMS产品，而无需维护多个功能分支。
- **优势**: 这种方式是"万物皆模块"理念在部署层面的体现。它使得我们可以用一套核心代码库，通过不同的模块组合，灵活地构建和交付满足不同客户需求的GACMS产品，而无需维护多个功能分支。

### 5.2. 版本控制策略

- **Git Flow**: 采用简化的Git Flow模型。
  - `main`: 生产分支，只接受来自发布分支的合并。
  - `develop`: 开发主分支，集成所有已完成的功能。
  - `feat/*`: 功能开发分支，从`develop`创建。
  - `fix/*`: Bug修复分支，从`develop`或`main`创建。
  - `release/*`: 发布分支，从`develop`创建，用于准备发布。

### 5.3. 部署工具链

- **版本控制**: Git / GitHub
- **CI/CD**: GitHub Actions
- **容器化**: Docker, Docker Compose
- **容器编排**: Kubernetes (K8s) (用于专业版和商业版)
- **配置管理**: Helm (用于K8s部署), Go-Viper, 环境变量
- **基础设施即代码 (IaC)**: Terraform (可选，用于复杂云环境的自动化管理)

### 5.4. 回滚策略

- **自动化回滚**: 如果部署到测试或预生产环境后，自动化测试失败，部署应自动回滚到上一个稳定版本。
- **手动回滚**: 在生产环境中，如果新版本出现严重问题，应能通过CI/CD工具或容器编排平台快速回滚到上一个版本的Docker镜像。

---

## 6. 监控、告警与日志管理
// ... existing code ...
  - [7.5. 身份与访问管理 (IAM)](#75-身份与访问管理-iam)
- [8. 可伸缩性与高可用性](#8-可伸缩性与高可用性)
- [9. 灾难恢复与备份策略](#9-灾难恢复与备份策略)
  - [9.1. 恢复目标 (RPO/RTO)](#91-恢复目标-rporto)
  - [9.2. 数据备份策略](#92-数据备份策略)
    - [9.2.1. 数据库备份](#921-数据库备份)
    - [9.2.2. 文件存储备份](#922-文件存储备份)
    - [9.2.3. 配置与模块化部署备份](#923-配置与模块化部署备份)
  - [9.3. 灾难恢复计划](#93-灾难恢复计划)
    - [9.3.1. 恢复流程](#931-恢复流程)
// ... existing code ...
  - [9.2.1. 数据库备份](#921-数据库备份)
    - [9.2.2. 文件存储备份](#922-文件存储备份)
    - [9.2.3. 配置数据备份](#923-配置数据备份)
  - [9.3. 灾难恢复计划](#93-灾难恢复计划)
// ... existing code ...
    - **备份内容**: 核心数据库 `gacms_core` 以及所有已启用模块的业务数据库（如果分离部署）。
    - **备份策略**:
        - **专业版/商业版**: 每日全量备份 + 事务日志实时备份（Point-in-Time Recovery）。
        - **个人版 (SaaS)**: 每日全量备份。
    - **备份存储**: 备份文件应加密后存储在异地的对象存储中，并保留多个周期的备份。

#### 9.2.2. 文件存储备份

- **备份内容**: 用户上传的媒体文件、文档等。
- **备份策略**:
    - **专业版/商业版**: 使用对象存储的版本控制和跨区域复制功能，实现实时备份和高可用。
    - **个人版 (SaaS)**: 定期将对象存储桶同步到异地备份存储桶。

#### 9.2.3. 配置与模块化部署备份

- **备份内容**:
    -   **环境变量**和**配置文件** (`config.yaml`)。
    -   用于构建特定版本应用的**`Main.go`文件**或**构建脚本**。
- **备份策略**:
    -   配置文件应通过版本控制系统（Git）进行管理，敏感信息通过安全的Secrets Manager备份。
    -   `Main.go`或相关构建脚本是应用功能的关键定义，必须随代码一同纳入Git严格管理和备份。这确保了即使在灾难情况下，我们也能精确地重建出包含特定模块组合的应用版本。

### 9.3. 灾难恢复计划

#### 9.3.1. 恢复流程
// ... existing code ...
- [附录C: 关键配置参数示例](#附录c-关键配置参数示例)
- [附录D: 不同产品版本的部署差异考量](#附录d-不同产品版本的部署差异考量)
- [附录E: 模块化部署注意事项](#附录e-模块化部署注意事项)
- [版本历史](#版本历史)

---
// ... existing code ...
- **许可证管理:** 部署架构需要考虑与许可证验证机制的集成，确保功能与授权版本匹配。

---

## 附录E: 模块化部署注意事项

GACMS的平台化架构提供了极大的灵活性，但在部署时也需要特别注意以下几点，以确保系统的稳定和可维护性。

- **三层配置体系**:
  - 部署时必须提供清晰的三层配置。`ADR-004` 定义了配置的加载和覆盖顺序：**环境配置 > 站点配置 > 全局配置**。
  - **全局配置 (`configs/global/`)**: 包含系统所有模块的默认基础配置，随代码库版本化管理。
  - **站点配置 (`configs/sites/`)**: 生产环境中，此目录应作为持久化存储卷挂载，以便于动态增删站点配置而不需重新部署应用。
  - **环境配置 (`configs/env/` 或 环境变量)**: 用于覆盖特定环境的配置（如数据库密码、Redis地址）。推荐**优先使用环境变量**来注入敏感信息和环境相关的配置，这符合云原生应用的十二要素（The Twelve-Factor App）原则。

- **数据库迁移**:
  - 部署流程必须能够**依次**执行数据库迁移脚本：首先执行核心系统的迁移，然后按预定顺序执行所有已启用模块的迁移。
  - 推荐使用`golang-migrate`等工具，为每个模块维护独立的迁移文件目录，并在主部署脚本中依次调用它们。

- **资源命名空间**:
  - 模块应避免创建与核心系统或其他模块冲突的资源，例如：
    -   **数据库表**: 模块创建的数据表应使用模块名作为前缀，如 `forum_posts`, `mall_products`。
    -   **缓存键**: 模块使用的Redis键也应添加模块名作为前缀，如 `forum:post:1`, `mall:product:1`。

- **依赖管理**:
  - 模块开发者应尽量减少其`go.mod`中的非必要依赖，以避免与GACMS核心或其他模块产生版本冲突。
  - 在将新模块集成到主构建流程之前，需要进行依赖兼容性检查。

遵循以上原则，可以确保即使在集成了大量第三方模块的情况下，GACMS的部署和运维依然清晰、可控。

---

## 1. 文档引言

## 1. 文档引言
// ... existing code ... 

## 3. 部署架构视图

### 3.1. 逻辑架构

GACMS采用完全前后端分离的逻辑架构。

- **前端 (Web/Admin)**: 使用React构建的单页面应用(SPA)，负责所有用户界面和交互。
- **后端 (Go)**: 基于Gin框架构建的无状态API服务，负责业务逻辑、数据处理和持久化。它被设计为一个**微核心平台**，通过加载不同的模块来提供完整的功能。
- **数据存储**: 使用MySQL进行结构化数据存储，使用Redis进行缓存和会话管理。

### 3.2. 物理部署图 (Kubernetes示例)

下图展示了在Kubernetes环境中部署GACMS（专业版/商业版）的典型物理架构。

```mermaid
graph TD
    subgraph "互联网用户"
        direction LR
        User(用户浏览器)
    end

    subgraph "云平台 / 数据中心"
        direction LR
        DNS(DNS / CDN)
        LB(负载均衡器 / Ingress)

        subgraph "Kubernetes 集群"
            direction TB
            
            subgraph "GACMS 核心服务"
                direction LR
                GacmsApp("
                    **GACMS 主应用 (Go Binary)**
                    ---
                    微核心 (Module Manager)
                    ---
                    - 加载模块A (e.g., User)
                    - 加载模块B (e.g., Post)
                    - 加载模块C (e.g., Forum)
                ")
                GacmsApp --- pod1("Pod 1 / Replica")
                GacmsApp --- pod2("Pod 2 / Replica")
                GacmsApp --- pod3("Pod 3 / Replica")
            end
            
            subgraph "依赖服务"
                direction LR
                MySQL(MySQL 数据库)
                Redis(Redis 缓存)
                Search(搜索引擎 e.g. OpenSearch)
            end

            subgraph "运维工具"
                direction LR
                Prometheus(监控)
                Grafana(可视化)
                Fluentd(日志)
            end
        end
    end

    User -- HTTPS --> DNS
    DNS --> LB
    LB -- Route --> pod1
    LB -- Route --> pod2
    LB -- Route --> pod3
    
    pod1 -- CRUD --> MySQL
    pod2 -- CRUD --> MySQL
    pod3 -- CRUD --> MySQL
    
    pod1 -- Cache --> Redis
    pod2 -- Cache --> Redis
    pod3 -- Cache --> Redis
    
    pod1 -- Search --> Search
    
    pod1 -- Logs --> Fluentd
    pod2 -- Logs --> Fluentd
    pod3 -- Logs --> Fluentd
    
    Prometheus -- Scrape --> pod1
    Prometheus -- Scrape --> pod2
    Prometheus -- Scrape --> pod3
    
    Grafana -- Query --> Prometheus
```

**组件说明**:
- **GACMS 主应用**: 这是系统的**唯一入口点**，是一个编译后的Go二进制文件。其内部的**模块管理器(Module Manager)** 在启动时会加载所有已配置的模块（如用户模块、内容模块、以及任何第三方模块），并将它们的API路由注册到主路由器。这种设计使得整个应用高度内聚和可扩展。部署的核心就是部署这个主应用容器。
- **数据库 (MySQL)**: 负责持久化存储所有模块的数据。
- **缓存 (Redis)**: 用于热点数据缓存、会话管理等，以提升性能。
- **搜索引擎 (Search Service)**: 提供全文检索能力，作为一个独立的服务部署。

---

## 4. 组件与服务详解

## 4. 组件与服务详解
// ... existing code ... 