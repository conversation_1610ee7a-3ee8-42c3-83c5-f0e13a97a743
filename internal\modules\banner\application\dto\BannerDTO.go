/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/banner/application/dto/BannerDTO.go
 * @Description: Defines the DTO for the banner module.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package dto

import "time"

// BannerCreateDTO is used for creating a new banner.
type BannerCreateDTO struct {
	SiteID      uint      `json:"site_id" binding:"required"`
	GroupSlug   string    `json:"group_slug" binding:"required"`
	Title       string    `json:"title" binding:"required"`
	Description string    `json:"description"`
	ImageUrl    string    `json:"image_url" binding:"required"`
	LinkUrl     string    `json:"link_url"`
	SortOrder   int       `json:"sort_order"`
	IsPublished bool      `json:"is_published"`
	PublishedAt *time.Time `json:"published_at"`
	ExpiresAt   *time.Time `json:"expires_at"`
}

// BannerUpdateDTO is used for updating an existing banner.
type BannerUpdateDTO struct {
	GroupSlug   string    `json:"group_slug"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	ImageUrl    string    `json:"image_url"`
	LinkUrl     string    `json:"link_url"`
	SortOrder   int       `json:"sort_order"`
	IsPublished *bool     `json:"is_published"`
	PublishedAt *time.Time `json:"published_at"`
	ExpiresAt   *time.Time `json:"expires_at"`
} 