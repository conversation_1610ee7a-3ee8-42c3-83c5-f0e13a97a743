/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/modules/user/domain/model/User.go
 * @Description: 用户领域模型
 * 
 * © 2025 GACMS. All rights reserved.
 */

package model

import (
	"time"
)

// UserStatus 用户状态
type UserStatus int

const (
	// UserStatusPending 待激活
	UserStatusPending UserStatus = 0

	// UserStatusActive 正常
	UserStatusActive UserStatus = 1

	// UserStatusDisabled 禁用
	UserStatusDisabled UserStatus = 2

	// UserStatusDeleted 已删除
	UserStatusDeleted UserStatus = 3
)

// User 用户模型
type User struct {
	ID          string     `json:"id"`
	Username    string     `json:"username"`
	Email       string     `json:"email"`
	Password    string     `json:"-"` // 不序列化密码
	Nickname    string     `json:"nickname"`
	Avatar      string     `json:"avatar"`
	Bio         string     `json:"bio"`
	Status      UserStatus `json:"status"`
	Roles       []string   `json:"roles"`
	LastLoginAt time.Time  `json:"lastLoginAt"`
	CreatedAt   time.Time  `json:"createdAt"`
	UpdatedAt   time.Time  `json:"updatedAt"`
	DeletedAt   *time.Time `json:"deletedAt,omitempty"`
	SiteID      string     `json:"siteId"`
}

// IsActive 检查用户是否处于活跃状态
func (u *User) IsActive() bool {
	return u.Status == UserStatusActive
}

// IsDisabled 检查用户是否被禁用
func (u *User) IsDisabled() bool {
	return u.Status == UserStatusDisabled
}

// IsDeleted 检查用户是否被删除
func (u *User) IsDeleted() bool {
	return u.Status == UserStatusDeleted || u.DeletedAt != nil
}

// HasRole 检查用户是否拥有指定角色
func (u *User) HasRole(role string) bool {
	for _, r := range u.Roles {
		if r == role {
			return true
		}
	}
	return false
}

// AddRole 添加角色
func (u *User) AddRole(role string) {
	if !u.HasRole(role) {
		u.Roles = append(u.Roles, role)
	}
}

// RemoveRole 移除角色
func (u *User) RemoveRole(role string) {
	for i, r := range u.Roles {
		if r == role {
			u.Roles = append(u.Roles[:i], u.Roles[i+1:]...)
			break
		}
	}
}

// Activate 激活用户
func (u *User) Activate() {
	u.Status = UserStatusActive
	u.UpdatedAt = time.Now()
}

// Disable 禁用用户
func (u *User) Disable() {
	u.Status = UserStatusDisabled
	u.UpdatedAt = time.Now()
}

// Delete 删除用户
func (u *User) Delete() {
	u.Status = UserStatusDeleted
	now := time.Now()
	u.DeletedAt = &now
	u.UpdatedAt = now
}

// UpdateProfile 更新用户资料
func (u *User) UpdateProfile(nickname, avatar, bio string) {
	if nickname != "" {
		u.Nickname = nickname
	}
	if avatar != "" {
		u.Avatar = avatar
	}
	if bio != "" {
		u.Bio = bio
	}
	u.UpdatedAt = time.Now()
}

// UpdateEmail 更新用户邮箱
func (u *User) UpdateEmail(email string) {
	if email != "" && email != u.Email {
		u.Email = email
		u.UpdatedAt = time.Now()
	}
}

// UpdatePassword 更新用户密码
func (u *User) UpdatePassword(password string) {
	if password != "" {
		u.Password = password // 实际应用中应该先哈希处理
		u.UpdatedAt = time.Now()
	}
} 