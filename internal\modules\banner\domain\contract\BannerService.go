/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/banner/domain/contract/BannerService.go
 * @Description: Banner service contract
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"gacms/internal/modules/banner/domain/model"

	"github.com/gin-gonic/gin"
)

type IBannerService interface {
	// Banner Position Management
	CreateBannerPosition(ctx *gin.Context, position *model.BannerPosition) (*model.BannerPosition, error)
	UpdateBannerPosition(ctx *gin.Context, position *model.BannerPosition) error
	DeleteBannerPosition(ctx *gin.Context, id uint) error
	GetBannerPosition(ctx *gin.Context, id uint) (*model.BannerPosition, error)
	GetAllBannerPositions(ctx *gin.Context) ([]*model.BannerPosition, error)

	// Banner Management
	CreateBanner(ctx *gin.Context, banner *model.Banner) (*model.Banner, error)
	UpdateBanner(ctx *gin.Context, banner *model.Banner) error
	DeleteBanner(ctx *gin.Context, id uint) error
	GetBanner(ctx *gin.Context, id uint) (*model.Banner, error)
	GetBannersByPositionID(ctx *gin.Context, positionID uint) ([]*model.Banner, error)
} 