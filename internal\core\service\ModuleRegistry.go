/*
 * @Author: <PERSON><PERSON>eh <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/ModuleRegistry.go
 * @Description: 模块注册表，统一管理模块配置和工厂函数
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"fmt"
	"sync"

	"go.uber.org/zap"
)

// ModuleRegistry 模块注册表
// 统一管理所有模块的配置和工厂函数
type ModuleRegistry struct {
	logger *zap.Logger

	// 模块配置
	configs map[string]*ModuleConfig

	// 并发控制
	mu sync.RWMutex
}

// NewModuleRegistry 创建模块注册表
func NewModuleRegistry(logger *zap.Logger) *ModuleRegistry {
	registry := &ModuleRegistry{
		logger:  logger,
		configs: make(map[string]*ModuleConfig),
	}

	// 注册所有模块配置
	registry.registerAllModules()

	return registry
}

// RegisterModule 注册模块配置
func (r *ModuleRegistry) RegisterModule(config *ModuleConfig) error {
	if err := config.Validate(); err != nil {
		return fmt.Errorf("invalid module config: %w", err)
	}

	r.mu.Lock()
	defer r.mu.Unlock()

	r.configs[config.Name] = config

	r.logger.Debug("Module registered",
		zap.String("name", config.Name),
		zap.String("version", config.Version),
		zap.Bool("is_global", config.IsGlobal),
		zap.Bool("supports_tenant", config.SupportsTenant),
	)

	return nil
}

// GetModuleConfig 获取模块配置
func (r *ModuleRegistry) GetModuleConfig(moduleName string) (*ModuleConfig, bool) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	config, exists := r.configs[moduleName]
	return config, exists
}

// ListModules 列出所有注册的模块
func (r *ModuleRegistry) ListModules() []string {
	r.mu.RLock()
	defer r.mu.RUnlock()

	modules := make([]string, 0, len(r.configs))
	for name := range r.configs {
		modules = append(modules, name)
	}

	return modules
}

// GetModulesByType 按类型获取模块
func (r *ModuleRegistry) GetModulesByType(isGlobal bool) []*ModuleConfig {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var modules []*ModuleConfig
	for _, config := range r.configs {
		if config.IsGlobal == isGlobal {
			modules = append(modules, config)
		}
	}

	return modules
}

// GetModulesByModuleType 按模块类型获取模块
func (r *ModuleRegistry) GetModulesByModuleType(moduleType ModuleType) []*ModuleConfig {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var modules []*ModuleConfig
	for _, config := range r.configs {
		if config.Type == moduleType {
			modules = append(modules, config)
		}
	}

	return modules
}

// GetCoreModules 获取核心模块
func (r *ModuleRegistry) GetCoreModules() []*ModuleConfig {
	return r.GetModulesByModuleType(ModuleTypeCore)
}

// GetOptionalModules 获取可选模块
func (r *ModuleRegistry) GetOptionalModules() []*ModuleConfig {
	return r.GetModulesByModuleType(ModuleTypeOptional)
}

// GetVendorsModules 获取第三方供应商模块
func (r *ModuleRegistry) GetVendorsModules() []*ModuleConfig {
	return r.GetModulesByModuleType(ModuleTypeVendors)
}

// IsCoreModule 检查是否为核心模块
func (r *ModuleRegistry) IsCoreModule(moduleName string) bool {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if config, exists := r.configs[moduleName]; exists {
		return config.IsCore()
	}
	return false
}

// IsOptionalModule 检查是否为可选模块
func (r *ModuleRegistry) IsOptionalModule(moduleName string) bool {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if config, exists := r.configs[moduleName]; exists {
		return config.IsOptional()
	}
	return false
}

// CanDisableModule 检查模块是否可以禁用
func (r *ModuleRegistry) CanDisableModule(moduleName string) bool {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if config, exists := r.configs[moduleName]; exists {
		return config.CanDisable()
	}
	return false
}

// registerAllModules 注册所有模块配置
func (r *ModuleRegistry) registerAllModules() {
	r.logger.Info("Registering all module configurations")

	// 注册核心模块
	r.registerUserModule()
	r.registerSiteModule()
	r.registerActionLogModule()
	r.registerAdminModule()
	r.registerContentModule() // 统一的内容管理模块

	// 注册可选模块
	r.registerThemeModule()

	r.logger.Info("All module configurations registered",
		zap.Int("total_modules", len(r.configs)),
	)
}

// registerUserModule 注册用户模块配置
func (r *ModuleRegistry) registerUserModule() {
	config := &ModuleConfig{
		Name:           "user",
		Version:        "1.0.0",
		Description:    "User management module with authentication and authorization",
		Author:         "GACMS Team",
		Dependencies:   []string{"database", "logger", "event"},
		Type:           ModuleTypeCore, // 核心模块
		LoadPriority:   10,             // 高优先级
		IsGlobal:       false,
		SupportsTenant: true,
		Path:           "internal/modules/user",
		ConfigPath:     "internal/modules/user/config",

		// 全局工厂
		GlobalFactory: func(deps *GlobalDependencies) (*ModuleInstance, error) {
			return createGlobalUserModule(deps)
		},

		// 租户工厂
		TenantFactory: func(deps *TenantDependencies) (*ModuleInstance, error) {
			return createTenantUserModule(deps)
		},
	}

	if err := r.RegisterModule(config); err != nil {
		r.logger.Error("Failed to register user module", zap.Error(err))
	}
}

// createGlobalUserModule 创建全局用户模块实例
func createGlobalUserModule(deps *GlobalDependencies) (*ModuleInstance, error) {
	deps.Logger.Debug("Creating global user module instance")

	instance := &ModuleInstance{
		Name:        "user",
		Version:     "1.0.0",
		SiteID:      0,
		IsGlobal:    true,
		Services:    make(map[string]interface{}),
		Controllers: make(map[string]interface{}),
		Routes:      make([]RouteInfo, 0),
	}

	// 全局用户模块可能只包含一些共享的工具服务
	// 具体的用户管理功能在租户模块中实现

	deps.Logger.Debug("Global user module instance created")
	return instance, nil
}

// createTenantUserModule 创建租户特定用户模块实例
func createTenantUserModule(deps *TenantDependencies) (*ModuleInstance, error) {
	deps.Logger.Debug("Creating tenant user module instance",
		zap.Uint("site_id", deps.SiteID),
	)

	instance := &ModuleInstance{
		Name:        "user",
		Version:     "1.0.0",
		SiteID:      deps.SiteID,
		IsGlobal:    false,
		Services:    make(map[string]interface{}),
		Controllers: make(map[string]interface{}),
		Routes:      make([]RouteInfo, 0),
	}

	// TODO: 创建用户模块的具体服务和控制器
	// 这里需要重构现有的用户模块代码以适应新的工厂模式
	
	// 暂时创建基本的服务结构
	err := createUserModuleServices(instance, deps)
	if err != nil {
		return nil, fmt.Errorf("failed to create user module services: %w", err)
	}

	// 创建控制器
	err = createUserModuleControllers(instance, deps)
	if err != nil {
		return nil, fmt.Errorf("failed to create user module controllers: %w", err)
	}

	// 注册路由
	createUserModuleRoutes(instance)

	deps.Logger.Debug("Tenant user module instance created",
		zap.Uint("site_id", deps.SiteID),
		zap.Int("services", len(instance.Services)),
		zap.Int("controllers", len(instance.Controllers)),
		zap.Int("routes", len(instance.Routes)),
	)

	return instance, nil
}

// createUserModuleServices 创建用户模块服务（真实实现）
func createUserModuleServices(instance *ModuleInstance, deps *TenantDependencies) error {
	deps.Logger.Debug("Creating real user module services",
		zap.Uint("site_id", deps.SiteID),
	)

	// 1. 创建用户仓储服务（真实实现）
	userRepo, err := createRealUserRepository(deps)
	if err != nil {
		return fmt.Errorf("failed to create user repository: %w", err)
	}
	instance.AddService("UserRepository", userRepo)

	// 2. 创建管理员仓储服务
	adminRepo, err := createRealAdminRepository(deps)
	if err != nil {
		return fmt.Errorf("failed to create admin repository: %w", err)
	}
	instance.AddService("AdminRepository", adminRepo)

	// 3. 创建角色仓储服务
	roleRepo, err := createRealRoleRepository(deps)
	if err != nil {
		return fmt.Errorf("failed to create role repository: %w", err)
	}
	instance.AddService("RoleRepository", roleRepo)

	// 4. 创建权限仓储服务
	permissionRepo, err := createRealPermissionRepository(deps)
	if err != nil {
		return fmt.Errorf("failed to create permission repository: %w", err)
	}
	instance.AddService("PermissionRepository", permissionRepo)

	// 5. 创建用户业务服务（真实实现）
	userService, err := createRealUserService(userRepo, deps)
	if err != nil {
		return fmt.Errorf("failed to create user service: %w", err)
	}
	instance.AddService("UserService", userService)

	// 6. 创建管理员服务
	adminService, err := createRealAdminService(adminRepo, roleRepo, deps)
	if err != nil {
		return fmt.Errorf("failed to create admin service: %w", err)
	}
	instance.AddService("AdminService", adminService)

	// 7. 创建权限服务
	permissionService, err := createRealPermissionService(permissionRepo, deps)
	if err != nil {
		return fmt.Errorf("failed to create permission service: %w", err)
	}
	instance.AddService("PermissionService", permissionService)

	deps.Logger.Debug("Real user module services created successfully",
		zap.Uint("site_id", deps.SiteID),
		zap.Int("services_count", len(instance.Services)),
	)

	return nil
}

// createUserModuleControllers 创建用户模块控制器（真实实现）
func createUserModuleControllers(instance *ModuleInstance, deps *TenantDependencies) error {
	deps.Logger.Debug("Creating real user module controllers",
		zap.Uint("site_id", deps.SiteID),
	)

	// 1. 获取服务实例
	userService, exists := instance.GetService("UserService")
	if !exists {
		return fmt.Errorf("UserService not found")
	}

	adminService, exists := instance.GetService("AdminService")
	if !exists {
		return fmt.Errorf("AdminService not found")
	}

	permissionService, exists := instance.GetService("PermissionService")
	if !exists {
		return fmt.Errorf("PermissionService not found")
	}

	// 2. 创建用户控制器（支持三入口）
	userController, err := createRealUserController(userService, adminService, permissionService, deps)
	if err != nil {
		return fmt.Errorf("failed to create user controller: %w", err)
	}
	instance.AddController("UserController", userController)

	// 3. 创建管理员认证控制器
	adminAuthController, err := createRealAdminAuthController(adminService, deps)
	if err != nil {
		return fmt.Errorf("failed to create admin auth controller: %w", err)
	}
	instance.AddController("AdminAuthController", adminAuthController)

	// 4. 创建会员认证控制器
	memberAuthController, err := createRealMemberAuthController(userService, deps)
	if err != nil {
		return fmt.Errorf("failed to create member auth controller: %w", err)
	}
	instance.AddController("MemberAuthController", memberAuthController)

	// 5. 创建权限控制器
	permissionController, err := createRealPermissionController(permissionService, deps)
	if err != nil {
		return fmt.Errorf("failed to create permission controller: %w", err)
	}
	instance.AddController("PermissionController", permissionController)

	deps.Logger.Debug("Real user module controllers created successfully",
		zap.Uint("site_id", deps.SiteID),
		zap.Int("controllers_count", len(instance.Controllers)),
	)

	return nil
}

// createUserModuleRoutes 创建用户模块路由（支持三入口）
func createUserModuleRoutes(instance *ModuleInstance) {
	// 公共入口用户路由
	publicUserRoutes := []RouteInfo{
		{
			Pattern:    "/user",
			Method:     "GET",
			Controller: "UserController",
			Action:     "Index",
			Middleware: []string{},
		},
		{
			Pattern:    "/user/{id}",
			Method:     "GET",
			Controller: "UserController",
			Action:     "Show",
			Middleware: []string{},
		},
		{
			Pattern:    "/user",
			Method:     "POST",
			Controller: "UserController",
			Action:     "Store",
			Middleware: []string{"csrf"},
		},
	}

	// 管理员入口用户路由
	adminUserRoutes := []RouteInfo{
		{
			Pattern:    "/admin/user",
			Method:     "GET",
			Controller: "UserController",
			Action:     "AdminIndex",
			Middleware: []string{"admin_auth"},
		},
		{
			Pattern:    "/admin/user/{id}",
			Method:     "GET",
			Controller: "UserController",
			Action:     "Show",
			Middleware: []string{"admin_auth"},
		},
		{
			Pattern:    "/admin/user",
			Method:     "POST",
			Controller: "UserController",
			Action:     "AdminStore",
			Middleware: []string{"admin_auth", "csrf"},
		},
		{
			Pattern:    "/admin/user/{id}",
			Method:     "PUT",
			Controller: "UserController",
			Action:     "AdminUpdate",
			Middleware: []string{"admin_auth", "csrf"},
		},
		{
			Pattern:    "/admin/user/{id}",
			Method:     "DELETE",
			Controller: "UserController",
			Action:     "AdminDelete",
			Middleware: []string{"admin_auth", "csrf"},
		},
	}

	// API入口用户路由
	apiUserRoutes := []RouteInfo{
		{
			Pattern:    "/api/user",
			Method:     "GET",
			Controller: "UserController",
			Action:     "ApiIndex",
			Middleware: []string{"api_auth"},
		},
		{
			Pattern:    "/api/user/{id}",
			Method:     "GET",
			Controller: "UserController",
			Action:     "Show",
			Middleware: []string{"api_auth"},
		},
		{
			Pattern:    "/api/user",
			Method:     "POST",
			Controller: "UserController",
			Action:     "ApiStore",
			Middleware: []string{"api_auth"},
		},
		{
			Pattern:    "/api/user/{id}",
			Method:     "PUT",
			Controller: "UserController",
			Action:     "ApiUpdate",
			Middleware: []string{"api_auth"},
		},
		{
			Pattern:    "/api/user/{id}",
			Method:     "DELETE",
			Controller: "UserController",
			Action:     "ApiDelete",
			Middleware: []string{"api_auth"},
		},
	}

	// 认证路由（多入口）
	authRoutes := []RouteInfo{
		// 公共认证
		{
			Pattern:    "/auth/login",
			Method:     "POST",
			Controller: "MemberAuthController",
			Action:     "Login",
			Middleware: []string{"csrf"},
		},
		{
			Pattern:    "/auth/logout",
			Method:     "POST",
			Controller: "MemberAuthController",
			Action:     "Logout",
			Middleware: []string{"member_auth"},
		},
		// 管理员认证
		{
			Pattern:    "/admin/auth/login",
			Method:     "POST",
			Controller: "AdminAuthController",
			Action:     "Login",
			Middleware: []string{"csrf"},
		},
		{
			Pattern:    "/admin/auth/logout",
			Method:     "POST",
			Controller: "AdminAuthController",
			Action:     "Logout",
			Middleware: []string{"admin_auth"},
		},
		// API认证
		{
			Pattern:    "/api/auth/token",
			Method:     "POST",
			Controller: "AdminAuthController",
			Action:     "GetToken",
			Middleware: []string{},
		},
	}

	// 权限路由
	permissionRoutes := []RouteInfo{
		{
			Pattern:    "/admin/permission",
			Method:     "GET",
			Controller: "PermissionController",
			Action:     "Index",
			Middleware: []string{"admin_auth"},
		},
		{
			Pattern:    "/api/permission/check",
			Method:     "GET",
			Controller: "PermissionController",
			Action:     "Check",
			Middleware: []string{"api_auth"},
		},
	}

	// 添加所有路由
	allRoutes := append(publicUserRoutes, adminUserRoutes...)
	allRoutes = append(allRoutes, apiUserRoutes...)
	allRoutes = append(allRoutes, authRoutes...)
	allRoutes = append(allRoutes, permissionRoutes...)

	for _, route := range allRoutes {
		instance.AddRoute(route)
	}
}

// createUserRepository 创建用户仓储服务
func createUserRepository(deps *TenantDependencies) (interface{}, error) {
	// 这里需要导入用户模块的仓储实现
	// 暂时创建一个简化的仓储接口

	// TODO: 实际实现应该是：
	// return persistence.NewUserRepository(deps.DB, deps.Logger)

	// 暂时返回一个模拟的仓储
	return &MockUserRepository{
		siteID: deps.SiteID,
		db:     deps.DB,
		logger: deps.Logger,
	}, nil
}

// createUserService 创建用户业务服务
func createUserService(userRepo interface{}, deps *TenantDependencies) (interface{}, error) {
	// TODO: 实际实现应该是：
	// return service.NewUserService(userRepo, deps.EventManager, deps.Logger)

	// 暂时返回一个模拟的服务
	return &MockUserService{
		siteID:      deps.SiteID,
		repository:  userRepo,
		eventMgr:    deps.EventManager,
		logger:      deps.Logger,
	}, nil
}

// createAuthService 创建认证服务
func createAuthService(userRepo interface{}, deps *TenantDependencies) (interface{}, error) {
	// TODO: 实际实现应该是：
	// return service.NewAuthService(userRepo, deps.EventManager, deps.Logger)

	return &MockAuthService{
		siteID:     deps.SiteID,
		repository: userRepo,
		logger:     deps.Logger,
	}, nil
}

// createRealUserRepository 创建真实的用户仓储
func createRealUserRepository(deps *TenantDependencies) (interface{}, error) {
	// TODO: 这里需要导入真实的用户仓储实现
	// 暂时返回模拟实现，后续替换为真实实现
	return &MockUserRepository{
		siteID: deps.SiteID,
		db:     deps.DB,
		logger: deps.Logger,
	}, nil
}

// createRealAdminRepository 创建真实的管理员仓储
func createRealAdminRepository(deps *TenantDependencies) (interface{}, error) {
	// TODO: 导入真实的管理员仓储
	// return repoImpl.NewGormAdminRepository(deps.DB, deps.Logger)
	return &MockUserRepository{
		siteID: deps.SiteID,
		db:     deps.DB,
		logger: deps.Logger,
	}, nil
}

// createRealRoleRepository 创建真实的角色仓储
func createRealRoleRepository(deps *TenantDependencies) (interface{}, error) {
	// TODO: 导入真实的角色仓储
	// return repoImpl.NewGormRoleRepository(deps.DB, deps.Logger)
	return &MockUserRepository{
		siteID: deps.SiteID,
		db:     deps.DB,
		logger: deps.Logger,
	}, nil
}

// createRealPermissionRepository 创建真实的权限仓储
func createRealPermissionRepository(deps *TenantDependencies) (interface{}, error) {
	// TODO: 导入真实的权限仓储
	// return repoImpl.NewGormPermissionRepository(deps.DB, deps.Logger)
	return &MockUserRepository{
		siteID: deps.SiteID,
		db:     deps.DB,
		logger: deps.Logger,
	}, nil
}

// createRealUserService 创建真实的用户服务
func createRealUserService(userRepo interface{}, deps *TenantDependencies) (interface{}, error) {
	// TODO: 这里需要导入真实的用户服务实现
	// 暂时返回模拟实现，后续替换为真实实现
	return &MockUserService{
		siteID:      deps.SiteID,
		repository:  userRepo,
		eventMgr:    deps.EventManager,
		logger:      deps.Logger,
	}, nil
}

// createRealAdminService 创建真实的管理员服务
func createRealAdminService(adminRepo, roleRepo interface{}, deps *TenantDependencies) (interface{}, error) {
	// TODO: 导入真实的管理员服务
	// return service.NewAdminService(adminRepo, roleRepo, deps.EventManager, deps.Logger)
	return &MockUserService{
		siteID:      deps.SiteID,
		repository:  adminRepo,
		eventMgr:    deps.EventManager,
		logger:      deps.Logger,
	}, nil
}

// createRealPermissionService 创建真实的权限服务
func createRealPermissionService(permissionRepo interface{}, deps *TenantDependencies) (interface{}, error) {
	// TODO: 导入真实的权限服务
	// return service.NewPermissionService(permissionRepo, deps.EventManager, deps.Logger)
	return &MockPermissionService{
		siteID: deps.SiteID,
		db:     deps.DB,
		logger: deps.Logger,
	}, nil
}

// createUserController 创建用户控制器
func createUserController(userService, authService, permissionService interface{}, deps *TenantDependencies) (interface{}, error) {
	// TODO: 实际实现应该是：
	// return controller.NewUserController(userService, authService, permissionService)

	return &MockUserController{
		siteID:            deps.SiteID,
		userService:       userService,
		authService:       authService,
		permissionService: permissionService,
		logger:            deps.Logger,
	}, nil
}

// createAuthController 创建认证控制器
func createAuthController(authService interface{}, deps *TenantDependencies) (interface{}, error) {
	// TODO: 实际实现应该是：
	// return controller.NewAuthController(authService)

	return &MockAuthController{
		siteID:      deps.SiteID,
		authService: authService,
		logger:      deps.Logger,
	}, nil
}

// createRealUserController 创建真实的用户控制器（支持三入口）
func createRealUserController(userService, adminService, permissionService interface{}, deps *TenantDependencies) (interface{}, error) {
	// TODO: 这里需要创建支持三入口的真实用户控制器
	// 暂时返回增强的模拟控制器
	return &EnhancedUserController{
		siteID:            deps.SiteID,
		userService:       userService,
		adminService:      adminService,
		permissionService: permissionService,
		logger:            deps.Logger,
	}, nil
}

// createRealAdminAuthController 创建真实的管理员认证控制器
func createRealAdminAuthController(adminService interface{}, deps *TenantDependencies) (interface{}, error) {
	// TODO: 导入真实的管理员认证控制器
	// return controller.NewAdminAuthController(adminService)
	return &MockAuthController{
		siteID:      deps.SiteID,
		authService: adminService,
		logger:      deps.Logger,
	}, nil
}

// createRealMemberAuthController 创建真实的会员认证控制器
func createRealMemberAuthController(userService interface{}, deps *TenantDependencies) (interface{}, error) {
	// TODO: 导入真实的会员认证控制器
	// return controller.NewMemberAuthController(userService)
	return &MockAuthController{
		siteID:      deps.SiteID,
		authService: userService,
		logger:      deps.Logger,
	}, nil
}

// createRealPermissionController 创建真实的权限控制器
func createRealPermissionController(permissionService interface{}, deps *TenantDependencies) (interface{}, error) {
	// TODO: 导入真实的权限控制器
	// return controller.NewPermissionController(permissionService)
	return &MockPermissionController{
		siteID:            deps.SiteID,
		permissionService: permissionService,
		logger:            deps.Logger,
	}, nil
}

// GetStats 获取注册表统计信息
func (r *ModuleRegistry) GetStats() map[string]interface{} {
	r.mu.RLock()
	defer r.mu.RUnlock()

	globalModules := 0
	tenantModules := 0
	coreModules := 0
	optionalModules := 0
	thirdPartyModules := 0

	for _, config := range r.configs {
		if config.IsGlobal {
			globalModules++
		}
		if config.SupportsTenant {
			tenantModules++
		}

		switch config.Type {
		case ModuleTypeCore:
			coreModules++
		case ModuleTypeOptional:
			optionalModules++
		case ModuleTypeVendors:
			thirdPartyModules++
		}
	}

	return map[string]interface{}{
		"total_modules":       len(r.configs),
		"global_modules":      globalModules,
		"tenant_modules":      tenantModules,
		"core_modules":        coreModules,
		"optional_modules":    optionalModules,
		"vendors_modules": thirdPartyModules,
	}
}

// registerSiteModule 注册站点模块配置
func (r *ModuleRegistry) registerSiteModule() {
	config := &ModuleConfig{
		Name:           "site",
		Version:        "1.0.0",
		Description:    "Multi-site management with domain binding and URL rewriting",
		Author:         "GACMS Team",
		Dependencies:   []string{"database", "logger", "event"},
		Type:           ModuleTypeCore, // 核心模块
		LoadPriority:   20,             // 中等优先级
		IsGlobal:       true,           // 站点管理是全局的
		SupportsTenant: false,
		Path:           "internal/modules/site",
		ConfigPath:     "internal/modules/site/config",

		// 全局工厂
		GlobalFactory: func(deps *GlobalDependencies) (*ModuleInstance, error) {
			return createGlobalSiteModule(deps)
		},
	}

	if err := r.RegisterModule(config); err != nil {
		r.logger.Error("Failed to register site module", zap.Error(err))
	}
}

// registerActionLogModule 注册操作日志模块配置
func (r *ModuleRegistry) registerActionLogModule() {
	config := &ModuleConfig{
		Name:           "actionlog",
		Version:        "1.0.0",
		Description:    "System operation audit and logging",
		Author:         "GACMS Team",
		Dependencies:   []string{"database", "logger", "event"},
		Type:           ModuleTypeCore, // 核心模块
		LoadPriority:   30,             // 中等优先级
		IsGlobal:       true,           // 日志是全局的
		SupportsTenant: false,
		Path:           "internal/modules/actionlog",
		ConfigPath:     "internal/modules/actionlog/config",

		// 全局工厂
		GlobalFactory: func(deps *GlobalDependencies) (*ModuleInstance, error) {
			return createGlobalActionLogModule(deps)
		},
	}

	if err := r.RegisterModule(config); err != nil {
		r.logger.Error("Failed to register actionlog module", zap.Error(err))
	}
}

// registerAdminModule 注册后台管理模块配置
func (r *ModuleRegistry) registerAdminModule() {
	config := &ModuleConfig{
		Name:           "admin",
		Version:        "1.0.0",
		Description:    "System administration interface and dashboard",
		Author:         "GACMS Team",
		Dependencies:   []string{"user", "site"},
		Type:           ModuleTypeCore, // 核心模块
		LoadPriority:   40,             // 较低优先级（依赖其他模块）
		IsGlobal:       true,           // 后台管理是全局的
		SupportsTenant: false,
		Path:           "internal/modules/admin",
		ConfigPath:     "internal/modules/admin/config",

		// 全局工厂
		GlobalFactory: func(deps *GlobalDependencies) (*ModuleInstance, error) {
			return createGlobalAdminModule(deps)
		},
	}

	if err := r.RegisterModule(config); err != nil {
		r.logger.Error("Failed to register admin module", zap.Error(err))
	}
}

// registerContentModule 注册统一内容管理模块配置
// 注意：这是合并后的内容模块，包含以下子模块（保持目录相对独立）：
// - posts: 文章管理 (internal/modules/content/posts)
// - pages: 页面管理 (internal/modules/content/pages)
// - categories: 分类管理 (internal/modules/content/categories)
// - tags: 标签管理 (internal/modules/content/tags)
// - menu: 菜单管理 (internal/modules/content/menu)
// - banner: 横幅管理 (internal/modules/content/banner)
// - media: 媒体管理 (internal/modules/content/media)
func (r *ModuleRegistry) registerContentModule() {
	config := &ModuleConfig{
		Name:           "content",
		Version:        "1.0.0",
		Description:    "Unified content management system with posts, pages, categories, tags, menu, banner, and media",
		Author:         "GACMS Team",
		Dependencies:   []string{"user", "site"},
		Type:           ModuleTypeCore, // 核心模块
		LoadPriority:   50,             // 较低优先级（依赖其他模块）
		IsGlobal:       false,          // 内容是租户特定的
		SupportsTenant: true,
		Path:           "internal/modules/content",
		ConfigPath:     "internal/modules/content/config",

		// 全局工厂（共享的内容类型定义、资源类型等）
		GlobalFactory: func(deps *GlobalDependencies) (*ModuleInstance, error) {
			return createGlobalContentModule(deps)
		},

		// 租户工厂（具体的内容实例、资源实例）
		TenantFactory: func(deps *TenantDependencies) (*ModuleInstance, error) {
			return createTenantContentModule(deps)
		},
	}

	if err := r.RegisterModule(config); err != nil {
		r.logger.Error("Failed to register content module", zap.Error(err))
	}
}



// registerThemeModule 注册主题模块配置（可选模块）
func (r *ModuleRegistry) registerThemeModule() {
	config := &ModuleConfig{
		Name:           "theme",
		Version:        "1.0.0",
		Description:    "Website theme and template management",
		Author:         "GACMS Team",
		Dependencies:   []string{"content"},
		Type:           ModuleTypeOptional, // 可选模块
		LoadPriority:   110,                // 低优先级
		IsGlobal:       false,              // 主题是租户特定的
		SupportsTenant: true,
		Path:           "internal/modules/theme",
		ConfigPath:     "internal/modules/theme/config",

		// 租户工厂
		TenantFactory: func(deps *TenantDependencies) (*ModuleInstance, error) {
			return createTenantThemeModule(deps)
		},
	}

	if err := r.RegisterModule(config); err != nil {
		r.logger.Error("Failed to register theme module", zap.Error(err))
	}
}
