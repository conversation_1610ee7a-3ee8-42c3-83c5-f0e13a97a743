/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-12
 * @LastEditors: C<PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/category/domain/contract/CategoryRepository.go
 * @Description: Defines the repository interface for categories.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package contract

import (
	"context"
	"gacms/internal/modules/category/domain/model"
)

// CategoryRepository defines the interface for category data operations.
type CategoryRepository interface {
	Create(ctx context.Context, category *model.Category) error
	Update(ctx context.Context, category *model.Category) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*model.Category, error)
	GetAll(ctx context.Context) ([]*model.Category, error)
	// In a full implementation, you'd also have List, Update, Delete, etc.
} 