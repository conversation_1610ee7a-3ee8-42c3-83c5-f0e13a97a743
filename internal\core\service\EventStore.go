/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-14
 * @LastEditors: C<PERSON>eh
 * @LastEditTime: 2025-06-14
 * @FilePath: internal/core/service/EventStore.go
 * @Description: 事件存储器实现
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"errors"
	"sync"
	"time"

	"gacms/pkg/contract"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// EventRecord 表示存储的事件记录
type EventRecord struct {
	ID            string
	EventName     string
	SerializedData []byte
	CreatedAt     time.Time
	ProcessedAt   *time.Time
	Status        string
}

// DefaultEventStore 是 contract.EventStore 接口的默认实现
type DefaultEventStore struct {
	serializer contract.EventSerializer
	logger     *zap.Logger
	mu         sync.RWMutex
	records    map[string]EventRecord // 内存存储，实际应用中应该使用数据库
}

// DefaultEventStoreParams 定义了创建 DefaultEventStore 所需的参数
type DefaultEventStoreParams struct {
	fx.In

	Serializer contract.EventSerializer
	Logger     *zap.Logger
}

// NewDefaultEventStore 创建一个新的 DefaultEventStore 实例
func NewDefaultEventStore(params DefaultEventStoreParams) contract.EventStore {
	return &DefaultEventStore{
		serializer: params.Serializer,
		logger:     params.Logger,
		records:    make(map[string]EventRecord),
	}
}

// SaveEvent 将事件保存到存储库
func (s *DefaultEventStore) SaveEvent(ctx context.Context, event contract.Event) (string, error) {
	if event == nil {
		return "", errors.New("event cannot be nil")
	}

	// 序列化事件
	data, err := s.serializer.Serialize(event)
	if err != nil {
		return "", err
	}

	// 创建事件记录
	record := EventRecord{
		ID:            event.ID(),
		EventName:     string(event.Name()),
		SerializedData: data,
		CreatedAt:     time.Now(),
		Status:        "pending",
	}

	// 存储事件记录
	s.mu.Lock()
	s.records[event.ID()] = record
	s.mu.Unlock()

	s.logger.Debug("Stored event",
		zap.String("id", event.ID()),
		zap.String("name", string(event.Name())),
	)

	return event.ID(), nil
}

// MarkAsProcessed 将事件标记为已处理
func (s *DefaultEventStore) MarkAsProcessed(ctx context.Context, eventID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	record, exists := s.records[eventID]
	if !exists {
		return errors.New("event not found")
	}

	now := time.Now()
	record.ProcessedAt = &now
	record.Status = "processed"
	s.records[eventID] = record

	s.logger.Debug("Marked event as processed",
		zap.String("id", eventID),
	)

	return nil
}

// GetEventByID 通过ID获取单个事件
func (s *DefaultEventStore) GetEventByID(ctx context.Context, eventID string) (contract.Event, error) {
	s.mu.RLock()
	record, exists := s.records[eventID]
	s.mu.RUnlock()

	if !exists {
		return nil, errors.New("event not found")
	}

	// 反序列化事件
	event, err := s.serializer.Deserialize(record.SerializedData, contract.EventName(record.EventName))
	if err != nil {
		return nil, err
	}

	return event, nil
}

// GetEventsByName 获取指定名称的所有事件
func (s *DefaultEventStore) GetEventsByName(ctx context.Context, eventName contract.EventName) ([]contract.Event, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	var events []contract.Event
	for _, record := range s.records {
		if record.EventName == string(eventName) {
			event, err := s.serializer.Deserialize(record.SerializedData, eventName)
			if err != nil {
				s.logger.Error("Failed to deserialize event",
					zap.String("id", record.ID),
					zap.Error(err),
				)
				continue
			}
			events = append(events, event)
		}
	}

	return events, nil
}

// GetEventsByTimeRange 获取指定时间范围内的所有事件
func (s *DefaultEventStore) GetEventsByTimeRange(ctx context.Context, start, end time.Time) ([]contract.Event, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	var events []contract.Event
	for _, record := range s.records {
		if (record.CreatedAt.After(start) || record.CreatedAt.Equal(start)) &&
		   (record.CreatedAt.Before(end) || record.CreatedAt.Equal(end)) {
			event, err := s.serializer.Deserialize(record.SerializedData, contract.EventName(record.EventName))
			if err != nil {
				s.logger.Error("Failed to deserialize event",
					zap.String("id", record.ID),
					zap.Error(err),
				)
				continue
			}
			events = append(events, event)
		}
	}

	return events, nil
}

// GetPendingEvents 获取所有待处理的事件
func (s *DefaultEventStore) GetPendingEvents(ctx context.Context) ([]contract.Event, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	var events []contract.Event
	for _, record := range s.records {
		if record.Status == "pending" {
			event, err := s.serializer.Deserialize(record.SerializedData, contract.EventName(record.EventName))
			if err != nil {
				s.logger.Error("Failed to deserialize event",
					zap.String("id", record.ID),
					zap.Error(err),
				)
				continue
			}
			events = append(events, event)
		}
	}

	return events, nil
}

// SaveEvents 批量保存多个事件
func (s *DefaultEventStore) SaveEvents(ctx context.Context, events []contract.Event) error {
	if len(events) == 0 {
		return nil
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	for _, event := range events {
		if event == nil {
			continue
		}

		// 序列化事件
		data, err := s.serializer.Serialize(event)
		if err != nil {
			return err
		}

		// 创建事件记录
		record := EventRecord{
			ID:            event.ID(),
			EventName:     string(event.Name()),
			SerializedData: data,
			CreatedAt:     time.Now(),
			Status:        "pending",
		}

		s.records[event.ID()] = record
	}

	s.logger.Debug("Batch saved events", zap.Int("count", len(events)))
	return nil
}

// GetEventsByAggregate 获取指定聚合的所有事件
func (s *DefaultEventStore) GetEventsByAggregate(ctx context.Context, aggregateType string, aggregateID string) ([]contract.Event, error) {
	// 注意：当前实现不支持聚合概念，返回空列表
	// 在实际应用中，需要在EventRecord中添加AggregateType和AggregateID字段
	s.logger.Warn("GetEventsByAggregate not fully implemented - aggregate support needed")
	return []contract.Event{}, nil
}

// GetEventsByType 获取指定类型的所有事件
func (s *DefaultEventStore) GetEventsByType(ctx context.Context, eventType contract.EventName, fromTime, toTime time.Time) ([]contract.Event, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	var events []contract.Event
	for _, record := range s.records {
		if record.EventName == string(eventType) {
			// 检查时间范围
			if !fromTime.IsZero() && record.CreatedAt.Before(fromTime) {
				continue
			}
			if !toTime.IsZero() && record.CreatedAt.After(toTime) {
				continue
			}

			event, err := s.serializer.Deserialize(record.SerializedData, eventType)
			if err != nil {
				s.logger.Error("Failed to deserialize event",
					zap.String("id", record.ID),
					zap.Error(err),
				)
				continue
			}
			events = append(events, event)
		}
	}

	return events, nil
}

// GetAllEvents 获取所有事件
func (s *DefaultEventStore) GetAllEvents(ctx context.Context, fromTime, toTime time.Time, offset, limit int) ([]contract.Event, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	var allRecords []EventRecord
	for _, record := range s.records {
		// 检查时间范围
		if !fromTime.IsZero() && record.CreatedAt.Before(fromTime) {
			continue
		}
		if !toTime.IsZero() && record.CreatedAt.After(toTime) {
			continue
		}
		allRecords = append(allRecords, record)
	}

	// 应用分页
	start := offset
	if start < 0 {
		start = 0
	}
	if start >= len(allRecords) {
		return []contract.Event{}, nil
	}

	end := start + limit
	if limit <= 0 || end > len(allRecords) {
		end = len(allRecords)
	}

	var events []contract.Event
	for i := start; i < end; i++ {
		record := allRecords[i]
		event, err := s.serializer.Deserialize(record.SerializedData, contract.EventName(record.EventName))
		if err != nil {
			s.logger.Error("Failed to deserialize event",
				zap.String("id", record.ID),
				zap.Error(err),
			)
			continue
		}
		events = append(events, event)
	}

	return events, nil
}

// CountEvents 计算事件总数
func (s *DefaultEventStore) CountEvents(ctx context.Context, eventType contract.EventName, fromTime, toTime time.Time) (int, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	count := 0
	for _, record := range s.records {
		// 检查事件类型
		if eventType != "" && record.EventName != string(eventType) {
			continue
		}

		// 检查时间范围
		if !fromTime.IsZero() && record.CreatedAt.Before(fromTime) {
			continue
		}
		if !toTime.IsZero() && record.CreatedAt.After(toTime) {
			continue
		}

		count++
	}

	return count, nil
}

// DeleteEvent 删除指定ID的事件
func (s *DefaultEventStore) DeleteEvent(ctx context.Context, eventID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if _, exists := s.records[eventID]; !exists {
		return errors.New("event not found")
	}

	delete(s.records, eventID)
	s.logger.Debug("Deleted event",
		zap.String("id", eventID),
	)

	return nil
}

// Clear 清除所有事件记录
func (s *DefaultEventStore) Clear(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.records = make(map[string]EventRecord)
	s.logger.Debug("Cleared all event records")

	return nil
} 