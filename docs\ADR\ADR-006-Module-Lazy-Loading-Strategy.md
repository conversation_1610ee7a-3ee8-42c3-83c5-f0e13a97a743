<!--
Author: Cion Nieh
EMAIL: <EMAIL>
Copyright (c) 2025 GACMS. All rights reserved.
-->
# ADR-006: 模块运行时懒加载策略 (Module Runtime Lazy-Loading Strategy)

## 状态
已接受

## 上下文
随着系统模块数量的增加，我们原有的DI（依赖注入）模型遇到了瓶颈。原模型在应用启动时，会一次性地构建所有"已启用"模块的依赖图，并将它们全部加载到内存中。这种"启动时加载"（Eager Loading）的模式导致了几个日益严重的问题：

1.  **启动性能下降**: 每增加一个模块，应用的启动时间都会延长。
2.  **资源消耗过高**: 所有模块，无论是否被当前请求所使用，都会在启动时占用内存资源。
3.  **非真正的"可插拔"**: 模块的启用和禁用虽然可以通过配置文件控制，但其加载行为仍然是全局性的，不够灵活。

为了构建一个更高效、更具扩展性的"微核心"架构，我们需要一种机制，使得模块可以在**运行时（Runtime）**，当其功能**第一次被请求**时，才被真正地加载和初始化。

## 决策
我们决定采用一种混合了**服务定位器（Service Locator）**和**单例模块子容器（Singleton per-module Sub-container）**思想的懒加载策略。

其核心逻辑如下：

1.  **主 DI 容器轻量化**: `uber-go/fx` 构建的主 DI 容器在启动时，只负责加载全局共享的基础设施服务（如数据库连接、日志记录器、配置管理器等）。它**不再**加载任何具体的业务模块。

2.  **引入 `ModuleRegistry` 服务**: 主容器提供一个核心的单例服务：`ModuleRegistry`。
    *   该服务在启动时会扫描 `internal/modules` 目录，读取所有模块的 `module.json` 元信息和它们的 `fx.Option` 定义，但仅将其保存在内部的一个注册表中，并**不**执行它们。
    *   `ModuleRegistry` 自身会从主容器中注入所有全局共享服务。

3.  **实现运行时懒加载**:
    *   `ModuleRegistry` 提供一个 `GetController(moduleName string)` 方法。
    *   当应用（例如，路由层）第一次请求某个模块的控制器时，会调用此方法。
    *   该方法会检查内部的控制器实例缓存。如果未命中，它将执行以下懒加载流程：
        a.  为该模块创建一个**一次性的、独立的 `fx` 子容器**。
        b.  将 `ModuleRegistry` 持有的全局共享服务（数据库、日志等）通过 `fx.Supply` 提供给这个子容器。
        c.  将该模块自己的 `fx.Option` 提供给子容器。
        d.  使用 `fx.Populate` 让子容器构建出该模块的控制器实例。
        e.  子容器在完成填充后即被丢弃，其生命周期非常短暂。
        f.  将新创建的控制器实例存入缓存，以便后续请求复用。
    *   返回控制器实例。

## 备选方案

### 方案一：原生 `fx` 模式 (被否决)
- **描述**: 依赖 `fx` 自身的机制。
- **原因**: 经过调研，`fx` 的核心设计是在启动时构建静态依赖图，不支持运行时的动态模块注入。此路不通。

### 方案二：纯服务定位器 (被否决)
- **描述**: `ModuleRegistry` 在需要时手动实例化控制器，并手动注入其依赖。
- **原因**: 这相当于我们自己重新实现了一个 DI 容器，完全抛弃了 `fx` 在模块内部进行依赖管理的能力。这会导致大量的模板代码和维护困难，过于复杂。

### 方案三：请求级子容器 (被否决)
- **描述**: 为每一个 HTTP 请求都创建一个 `fx` 子容器来获取控制器。
- **原因**: 性能开销过大，为每个请求都进行一次依赖注入解析是不可接受的。

## 后果

### 积极
- **显著提升启动性能**: 应用启动时几乎是瞬时的，因为它不再需要构建和加载任何业务模块。
- **降低内存占用**: 只有被实际使用到的模块才会被加载到内存中。
- **真·模块化**: 实现了真正意义上的模块"可插拔"，为未来实现模块的在线安装、卸载、热更新等高级功能奠定了坚实的架构基础。
- **保留了 `fx` 的优势**: 每个模块内部的依赖关系仍然由 `fx` 自动管理，开发者无需关心模块内的依赖注入细节，开发体验良好。

### 消极
- **增加了架构复杂性**: 引入了子容器和两级依赖（全局依赖和模块内依赖）的概念，需要团队成员理解这种新模式。
- **首次请求延迟**: 对某个模块的第一次请求，会因为需要即时创建子容器而产生一个可感知的、一次性的延迟。这需要通过监控来量化，并在必要时进行优化（如预热）。
- **控制器类型映射**: 当前 `ModuleRegistry` 的实现需要一个 `switch` 语句来手动映射模块名和控制器类型。这在未来可以通过更高级的反射或注册机制来优化。

## 实施注意事项
- **全局依赖管理**: 必须谨慎地区分哪些是全局共享的依赖（应在主容器中提供），哪些是模块私有的依赖（应在模块自身的 `fx.Option` 中提供）。
- **路由层改造**: HTTP 路由注册时，不能再直接注入控制器实例，而是注入 `ModuleRegistry`。路由处理器将是一个闭包，在闭包内调用 `registry.GetController()` 来实现懒加载。
- **循环依赖风险**: 虽然模块间是懒加载的，但在创建子容器时，如果模块A的控制器依赖模块B的服务，而模块B的服务又反过来需要模块A的某个组件，仍然可能在子容器构建时出现问题。这需要通过清晰的接口和事件驱动来避免。 