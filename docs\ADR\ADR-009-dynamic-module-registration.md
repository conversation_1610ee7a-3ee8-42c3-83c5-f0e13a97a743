# ADR-009: Dynamic Module Registration via Service Recipes

## Status
Proposed

## Context
The current dependency injection (DI) system, configured in `internal/core/di/Container.go`, hardcodes all application modules. Every time a new module is created or removed, developers must manually edit the `moduleProviders` variable in this central file. This creates tight coupling between the application core and its modules, making the system rigid and difficult to extend, especially for third-party modules which cannot be integrated without modifying the core codebase. This approach violates the Open/Closed Principle and hinders our goal of a truly modular architecture.

Analysis of `internal/core/service/ModuleManager.go` revealed an existing, more advanced, but currently unused mechanism for module registration. The `ModuleManager` is designed to receive `ModuleRecipe` objects via an `fx` group named `"module_recipes"`, which allows for automatic discovery of modules provided to the container. However, the current DI setup does not supply these recipes correctly.

## Decision
We will refactor the DI system to adopt a dynamic, recipe-based module registration approach. This leverages the existing `ModuleManager`'s capabilities and `go.uber.org/fx`'s group feature.

1.  **Standardize Module Recipes**: Every module must expose a `Recipe` variable of type `service.ModuleRecipe` in its root `module.go` file. This recipe contains the module's name and its `fx.Option` providers.

2.  **Create a Central Recipe Provider**: We will create a new file, `internal/modules/providers.go`. This file will be the single source of truth for providing all module recipes to the `fx` container. It will contain a single `fx.Option` variable, `AllModuleRecipes`, which provides each module's `Recipe` annotated with `fx.ResultTags(\`group:"module_recipes"\`)`.

3.  **Decouple the Core Container**: The main DI container in `internal/core/di/Container.go` will be modified to use `modules.AllModuleRecipes` instead of the hardcoded `moduleProviders` list.

4.  **Deprecate Old Mechanisms**: The hardcoded list in `internal/modules/registry.go` will be removed as it becomes redundant.

This change moves the responsibility of module registration from the core container to a dedicated provider, effectively decoupling them.

## Alternatives Considered
### Option 1: Status Quo
- **Description**: Continue manually adding modules to `di/Container.go`.
- **Pros**: No immediate effort required.
- **Cons**: High technical debt, poor scalability, violates architectural principles, and makes third-party integration nearly impossible.
- **Why Not**: It directly contradicts our goals for a flexible and extensible platform.

### Option 2: Build-Time Code Generation
- **Description**: Use a tool like `go:generate` to scan the `internal/modules` directory and automatically generate the `internal/modules/providers.go` file.
- **Pros**: Fully automatic discovery. No manual list needs to be maintained.
- **Cons**: Adds complexity to the build process, can feel "magical" and obscure, and introduces a dependency on code generation tools.
- **Why Not**: While this is a powerful solution, we are opting for the simpler Central Recipe Provider first to achieve immediate architectural improvement with lower implementation complexity. This can be considered as a future enhancement.

## Consequences

### Positive
- **Improved Extensibility**: Adding a new module only requires adding one line to `internal/modules/providers.go`, without touching the core DI logic.
- **Decoupling**: The application core becomes completely agnostic of the specific modules being used.
- **Third-Party Friendly**: Paves the way for a system where third-party modules can be easily integrated.
- **Cleanliness**: Centralizes module registration logic into a single, dedicated file.

### Negative
- A single file, `internal/modules/providers.go`, still needs to be manually updated when modules are added or removed. However, this is a significant improvement over the current state.

### Neutral
- This refactoring primarily affects the application's startup and DI wiring; runtime behavior remains unchanged.

## Implementation Notes
- **File to Create**: `internal/modules/providers.go`
- **Files to Modify**: `internal/core/di/Container.go`
- **File to Delete/Refactor**: `internal/modules/registry.go`
- All modules must be checked to ensure they export a `Recipe` variable. If not, they must be updated.

## Review Date
One month after implementation to evaluate the new workflow and decide on potentially moving to a code generation approach. 