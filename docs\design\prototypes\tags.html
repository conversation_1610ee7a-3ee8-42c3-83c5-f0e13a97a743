<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 标签管理</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        /* 标签样式 */
        .tag {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        
        .tag.selected {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border-color: #0056b3;
        }
        
        /* 表格行样式 */
        .table-row {
            transition: all 0.3s ease;
        }
        
        .table-row:hover {
            background-color: rgba(50, 50, 50, 0.3);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">      
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">标签管理</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="createTagBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-plus text-white"></i>
                                </span>
                                新建标签
                            </span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 统计概览 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- 标签总数 -->
                <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-tags text-blue-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">标签总数</div>
                            <div class="text-xl font-semibold text-white">56</div>
                            <div class="text-xs text-gray-400 mt-1">今日 +2 个</div>
                        </div>
                    </div>
                </div>

                <!-- 最多引用 -->
                <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-star text-green-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">最多引用</div>
                            <div class="text-xl font-semibold text-white">技术分享</div>
                            <div class="text-xs text-gray-400 mt-1">共 23 篇文章</div>
                        </div>
                    </div>
                </div>

                <!-- 最新标签 -->
                <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-clock text-purple-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">最新标签</div>
                            <div class="text-xl font-semibold text-white">前端框架</div>
                            <div class="text-xs text-gray-400 mt-1">今日 12:30 创建</div>
                        </div>
                    </div>
                </div>

                <!-- 未使用标签 -->
                <div class="bg-gray-800/20 p-4 rounded-xl border border-gray-700">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center mr-4">
                            <i class="fas fa-exclamation-triangle text-yellow-500 text-xl"></i>
                        </div>
                        <div>
                            <div class="text-sm text-gray-400">未使用标签</div>
                            <div class="text-xl font-semibold text-white">8</div>
                            <div class="text-xs text-gray-400 mt-1">
                                <span class="text-yellow-400">需要整理</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 标签管理主内容 -->
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- 左侧标签列表 -->
                <div class="lg:col-span-1">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold text-white mb-4">标签列表</h3>
                        
                        <!-- 搜索框 -->
                        <div class="mb-6">
                            <div class="relative">
                                <input type="text" placeholder="搜索标签..." 
                                       class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent pl-10">
                                <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                    <i class="fas fa-search"></i>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 标签云 -->
                        <div class="flex flex-wrap gap-2 mb-6">
                            <span class="tag px-3 py-1 rounded-full bg-gray-700/80 border border-gray-600 text-sm selected">技术分享 (23)</span>
                            <span class="tag px-3 py-1 rounded-full bg-gray-700/80 border border-gray-600 text-sm">产品更新 (15)</span>
                            <span class="tag px-3 py-1 rounded-full bg-gray-700/80 border border-gray-600 text-sm">行业动态 (12)</span>
                            <span class="tag px-3 py-1 rounded-full bg-gray-700/80 border border-gray-600 text-sm">教程 (8)</span>
                            <span class="tag px-3 py-1 rounded-full bg-gray-700/80 border border-gray-600 text-sm">设计 (6)</span>
                            <span class="tag px-3 py-1 rounded-full bg-gray-700/80 border border-gray-600 text-sm">开发 (5)</span>
                            <span class="tag px-3 py-1 rounded-full bg-gray-700/80 border border-gray-600 text-sm">新闻 (4)</span>
                            <span class="tag px-3 py-1 rounded-full bg-gray-700/80 border border-gray-600 text-sm">AI (3)</span>
                            <span class="tag px-3 py-1 rounded-full bg-gray-700/80 border border-gray-600 text-sm">云计算 (2)</span>
                            <span class="tag px-3 py-1 rounded-full bg-gray-700/80 border border-gray-600 text-sm">前端框架 (0)</span>
                        </div>
                        
                        <!-- 标签详情表单 -->
                        <form class="bg-gray-800/20 border border-gray-700 rounded-xl p-4">
                            <h4 class="text-white font-medium mb-4">编辑标签</h4>
                            
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2 text-sm">标签名称</label>
                                <input type="text" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" value="技术分享">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2 text-sm">标签别名</label>
                                <input type="text" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" value="tech-share">
                                <div class="text-xs text-gray-500 mt-1">用于URL，仅支持字母、数字、连字符和下划线</div>
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-gray-300 mb-2 text-sm">描述</label>
                                <textarea class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" rows="3">技术相关的分享文章和教程</textarea>
                            </div>
                            
                            <div class="flex flex-wrap gap-3 mt-6">
                                <button type="submit" class="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30">
                                    <i class="fas fa-save mr-2"></i> 保存
                                </button>
                                <button type="button" class="flex-1 bg-red-600/30 text-red-400 border border-red-500/30 px-4 py-2 rounded-lg font-medium transition-all hover:bg-red-600/40">
                                    <i class="fas fa-trash-alt mr-2"></i> 删除
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- 右侧关联文章 -->
                <div class="lg:col-span-3">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <div class="flex flex-wrap justify-between items-center mb-6">
                            <h3 class="text-lg font-semibold text-white">关联文章</h3>
                            
                            <!-- 操作按钮 -->
                            <div class="flex flex-wrap gap-3 mt-2 sm:mt-0">
                                <select class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-gray-300 focus:ring-2 focus:ring-blue-500">
                                    <option value="newest">最新发布</option>
                                    <option value="oldest">最早发布</option>
                                    <option value="views">浏览量</option>
                                </select>
                                <button class="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:shadow-lg hover:shadow-blue-500/30">
                                    <i class="fas fa-link mr-2"></i> 批量关联
                                </button>
                            </div>
                        </div>
                        
                        <!-- 文章表格 -->
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b border-gray-700">
                                        <th class="text-left py-3 px-4 text-gray-300 font-medium">#</th>
                                        <th class="text-left py-3 px-4 text-gray-300 font-medium">文章标题</th>
                                        <th class="text-left py-3 px-4 text-gray-300 font-medium">所属栏目</th>
                                        <th class="text-left py-3 px-4 text-gray-300 font-medium">发布时间</th>
                                        <th class="text-left py-3 px-4 text-gray-300 font-medium">浏览量</th>
                                        <th class="text-left py-3 px-4 text-gray-300 font-medium">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 文章项 1 -->
                                    <tr class="table-row border-b border-gray-700 hover:bg-gray-800/20">
                                        <td class="py-4 px-4 text-white">1</td>
                                        <td class="py-4 px-4">
                                            <div class="flex items-center">
                                                <img src="./assets/images/article-thumb-1.jpg" alt="文章缩略图" class="w-10 h-10 rounded object-cover mr-3">
                                                <span class="text-white font-medium">Go语言高性能编程实践指南</span>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4 text-gray-300">技术教程</td>
                                        <td class="py-4 px-4 text-gray-300">2025-06-15</td>
                                        <td class="py-4 px-4 text-gray-300">3,254</td>
                                        <td class="py-4 px-4">
                                            <div class="flex space-x-2">
                                                <button class="text-gray-400 hover:text-blue-500" title="查看">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="text-gray-400 hover:text-yellow-500" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-gray-400 hover:text-red-500" title="移除">
                                                    <i class="fas fa-unlink"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    
                                    <!-- 文章项 2 -->
                                    <tr class="table-row border-b border-gray-700 hover:bg-gray-800/20">
                                        <td class="py-4 px-4 text-white">2</td>
                                        <td class="py-4 px-4">
                                            <div class="flex items-center">
                                                <img src="./assets/images/article-thumb-2.jpg" alt="文章缩略图" class="w-10 h-10 rounded object-cover mr-3">
                                                <span class="text-white font-medium">React性能优化最佳实践</span>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4 text-gray-300">前端开发</td>
                                        <td class="py-4 px-4 text-gray-300">2025-06-14</td>
                                        <td class="py-4 px-4 text-gray-300">2,873</td>
                                        <td class="py-4 px-4">
                                            <div class="flex space-x-2">
                                                <button class="text-gray-400 hover:text-blue-500" title="查看">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="text-gray-400 hover:text-yellow-500" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-gray-400 hover:text-red-500" title="移除">
                                                    <i class="fas fa-unlink"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    
                                    <!-- 文章项 3 -->
                                    <tr class="table-row border-b border-gray-700 hover:bg-gray-800/20">
                                        <td class="py-4 px-4 text-white">3</td>
                                        <td class="py-4 px-4">
                                            <div class="flex items-center">
                                                <img src="./assets/images/article-thumb-3.jpg" alt="文章缩略图" class="w-10 h-10 rounded object-cover mr-3">
                                                <span class="text-white font-medium">Docker容器化部署实战</span>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4 text-gray-300">运维部署</td>
                                        <td class="py-4 px-4 text-gray-300">2025-06-13</td>
                                        <td class="py-4 px-4 text-gray-300">1,932</td>
                                        <td class="py-4 px-4">
                                            <div class="flex space-x-2">
                                                <button class="text-gray-400 hover:text-blue-500" title="查看">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="text-gray-400 hover:text-yellow-500" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-gray-400 hover:text-red-500" title="移除">
                                                    <i class="fas fa-unlink"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    
                                    <!-- 文章项 4 -->
                                    <tr class="table-row border-b border-gray-700 hover:bg-gray-800/20">
                                        <td class="py-4 px-4 text-white">4</td>
                                        <td class="py-4 px-4">
                                            <div class="flex items-center">
                                                <img src="./assets/images/article-thumb-4.jpg" alt="文章缩略图" class="w-10 h-10 rounded object-cover mr-3">
                                                <span class="text-white font-medium">Python数据分析入门指南</span>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4 text-gray-300">数据科学</td>
                                        <td class="py-4 px-4 text-gray-300">2025-06-12</td>
                                        <td class="py-4 px-4 text-gray-300">1,648</td>
                                        <td class="py-4 px-4">
                                            <div class="flex space-x-2">
                                                <button class="text-gray-400 hover:text-blue-500" title="查看">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="text-gray-400 hover:text-yellow-500" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-gray-400 hover:text-red-500" title="移除">
                                                    <i class="fas fa-unlink"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    
                                    <!-- 文章项 5 -->
                                    <tr class="table-row hover:bg-gray-800/20">
                                        <td class="py-4 px-4 text-white">5</td>
                                        <td class="py-4 px-4">
                                            <div class="flex items-center">
                                                <img src="./assets/images/article-thumb-5.jpg" alt="文章缩略图" class="w-10 h-10 rounded object-cover mr-3">
                                                <span class="text-white font-medium">Web安全最佳实践</span>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4 text-gray-300">安全</td>
                                        <td class="py-4 px-4 text-gray-300">2025-06-10</td>
                                        <td class="py-4 px-4 text-gray-300">2,106</td>
                                        <td class="py-4 px-4">
                                            <div class="flex space-x-2">
                                                <button class="text-gray-400 hover:text-blue-500" title="查看">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="text-gray-400 hover:text-yellow-500" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-gray-400 hover:text-red-500" title="移除">
                                                    <i class="fas fa-unlink"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <div class="flex justify-between items-center mt-6">
                            <div class="text-sm text-gray-400">
                                显示 1 至 5，共 23 个文章
                            </div>
                            <div class="flex space-x-2">
                                <button disabled class="bg-gray-800 text-gray-500 px-4 py-2 rounded-lg">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <button class="bg-blue-500 text-white px-4 py-2 rounded-lg">1</button>
                                <button class="bg-gray-800 text-white hover:bg-gray-700 px-4 py-2 rounded-lg">2</button>
                                <button class="bg-gray-800 text-white hover:bg-gray-700 px-4 py-2 rounded-lg">3</button>
                                <button class="bg-gray-800 text-white hover:bg-gray-700 px-4 py-2 rounded-lg">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏
            if(document.getElementById('sidebar')) {
                loadHTML('sidebar', 'sidebar_menu.html');
            }
            
            // 加载顶部导航栏
            if(document.getElementById('topNavbar')) {
                loadHTML('topNavbar', 'top_navbar.html');
            }
            
            // 标签点击选中效果
            const tagElements = document.querySelectorAll('.tag');
            tagElements.forEach(tag => {
                tag.addEventListener('click', function() {
                    // 取消其他标签的选中状态
                    tagElements.forEach(t => t.classList.remove('selected'));
                    // 选中当前标签
                    this.classList.add('selected');
                });
            });
        });
    </script>
</body>
</html> 