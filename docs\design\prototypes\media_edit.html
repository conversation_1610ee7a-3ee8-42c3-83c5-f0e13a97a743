<!--
© 2025 GACMS. All rights reserved.
Author: <PERSON><PERSON> <<EMAIL>>

-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GACMS - 媒体编辑</title>
    <link rel="icon" href="./assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://s4.zstatic.net/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/gacms-admin.css">
    <link rel="stylesheet" href="./assets/css/sidebar-menu.css">
    <link rel="stylesheet" href="./assets/css/top-navbar.css">
    <style>
        /* 保留必要的自定义样式 */
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #007bff, #00c6ff);
            border-radius: 3px;
        }
        
        .media-preview {
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            aspect-ratio: 16/9;
            background-color: #2D3748;
            background-position: center;
            background-repeat: no-repeat;
            background-size: contain;
        }
        
        .media-preview.image {
            background-size: cover;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#007bff',
                            600: '#0069d9',
                        },
                        secondary: {
                            500: '#00c6ff',
                        },
                        dark: {
                            100: '#e0e0e0',
                            200: '#a0a0a0',
                            300: '#9CA3AF',
                            400: '#6B7280',
                            500: '#4B5563',
                            600: '#374151',
                            700: '#2D3748',
                            800: '#1F2937',
                            900: '#1A1A1A',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="flex h-screen text-gray-200">
    <!-- 侧边栏 -->
    <aside id="sidebar" class="sidebar w-52 h-screen load-template fixed left-0 top-0 z-10"></aside>

    <main class="flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out md:ml-52">
        <header class="header sticky top-0 z-30">
            <!-- 顶部导航栏 -->
            <div id="topNavbar" class="w-full sticky top-0 left-0 right-0"></div>
        </header>
        <div class="px-[35px] flex-[1_0_auto] overflow-y-auto pt-4">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm mb-6 text-gray-400">
                <a href="dashboard.html" class="hover:text-white">首页</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="content.html" class="hover:text-white">内容管理</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <a href="medias.html" class="hover:text-white">媒体库</a>
                <i class="fas fa-chevron-right text-xs mx-2"></i>
                <span class="text-white">编辑媒体</span>
            </div>

            <!-- 顶部操作区域 -->
            <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                <div class="flex flex-wrap justify-between items-center">
                    <h2 class="text-xl font-bold text-white relative pl-3 section-title">编辑媒体文件</h2>
                    <div class="flex space-x-3 mt-4 sm:mt-0">
                        <button id="saveBtn" class="flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-save text-white"></i>
                                </span>
                                保存
                            </span>
                        </button>
                        <a href="medias.html" class="flex items-center justify-center bg-gradient-to-r from-gray-600 to-gray-700 text-white px-5 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-gray-600/30 relative overflow-hidden">
                            <span class="relative flex items-center">
                                <span class="mr-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/15">
                                    <i class="fas fa-times text-white"></i>
                                </span>
                                取消
                            </span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 媒体编辑内容 -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                <!-- 左侧：媒体预览 -->
                <div class="lg:col-span-2">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">媒体预览</h3>
                        
                        <div class="media-preview image mb-4" style="background-image: url('https://images.unsplash.com/photo-1644982654072-0b42e6636821?ixlib=rb-1.2.1&ixid=MnwxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80');"></div>
                        
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div>
                                <p class="text-gray-400 mb-1">文件类型</p>
                                <p class="text-white font-medium">图像 (JPEG)</p>
                            </div>
                            <div>
                                <p class="text-gray-400 mb-1">文件大小</p>
                                <p class="text-white font-medium">1.4 MB</p>
                            </div>
                            <div>
                                <p class="text-gray-400 mb-1">尺寸</p>
                                <p class="text-white font-medium">1920 × 1080 像素</p>
                            </div>
                            <div>
                                <p class="text-gray-400 mb-1">上传日期</p>
                                <p class="text-white font-medium">2025-03-14 10:35</p>
                            </div>
                        </div>
                        
                        <!-- 复制URL工具栏 -->
                        <div class="mt-6 space-y-3">
                            <h4 class="text-sm font-semibold text-white">复制链接</h4>
                            <div class="grid grid-cols-1 gap-3">
                                <div>
                                    <label class="text-gray-400 text-sm block mb-1">原始URL</label>
                                    <div class="flex">
                                        <input type="text" readonly value="/uploads/2025/03/mountain-landscape-1920x1080.jpg" class="flex-1 bg-gray-700 border border-r-0 border-gray-600 rounded-l-lg px-4 py-3 text-white">
                                        <button class="copy-btn px-4 bg-gray-600 border border-l-0 border-gray-600 rounded-r-lg hover:bg-gray-500 transition-colors">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                                <div>
                                    <label class="text-gray-400 text-sm block mb-1">完整URL</label>
                                    <div class="flex">
                                        <input type="text" readonly value="https://www.example.com/uploads/2025/03/mountain-landscape-1920x1080.jpg" class="flex-1 bg-gray-700 border border-r-0 border-gray-600 rounded-l-lg px-4 py-3 text-white">
                                        <button class="copy-btn px-4 bg-gray-600 border border-l-0 border-gray-600 rounded-r-lg hover:bg-gray-500 transition-colors">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                                <div>
                                    <label class="text-gray-400 text-sm block mb-1">HTML代码</label>
                                    <div class="flex">
                                        <input type="text" readonly value='<img src="/uploads/2025/03/mountain-landscape-1920x1080.jpg" alt="山景图片" width="1920" height="1080">' class="flex-1 bg-gray-700 border border-r-0 border-gray-600 rounded-l-lg px-4 py-3 text-white">
                                        <button class="copy-btn px-4 bg-gray-600 border border-l-0 border-gray-600 rounded-r-lg hover:bg-gray-500 transition-colors">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                                <div>
                                    <label class="text-gray-400 text-sm block mb-1">Markdown代码</label>
                                    <div class="flex">
                                        <input type="text" readonly value='![山景图片](/uploads/2025/03/mountain-landscape-1920x1080.jpg)' class="flex-1 bg-gray-700 border border-r-0 border-gray-600 rounded-l-lg px-4 py-3 text-white">
                                        <button class="copy-btn px-4 bg-gray-600 border border-l-0 border-gray-600 rounded-r-lg hover:bg-gray-500 transition-colors">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 替换媒体文件 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">替换文件</h3>
                        
                        <p class="text-gray-400 text-sm mb-4">您可以上传新文件来替换当前媒体，而不改变其URL。</p>
                        
                        <div class="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center">
                            <div class="flex flex-col items-center justify-center">
                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-3"></i>
                                <p class="text-gray-300 mb-2">拖放文件到此处，或</p>
                                <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">选择文件</button>
                                <p class="text-gray-400 text-sm mt-2">支持JPG, PNG, GIF, SVG, PDF等格式，最大50MB</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：基本信息编辑 -->
                <div class="lg:col-span-1">
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">基本信息</h3>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">文件标题</label>
                            <input type="text" value="山景图片" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">替代文本 (Alt)</label>
                            <input type="text" value="山景图片" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <p class="text-gray-400 text-sm mt-1">描述图片内容，用于SEO和无障碍功能</p>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">说明</label>
                            <textarea rows="3" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">壮丽山景照片，拍摄于2025年春季。高山日落景观，云雾缭绕。</textarea>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">标签</label>
                            <input type="text" value="山, 景观, 自然, 日落" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <p class="text-gray-400 text-sm mt-1">多个标签用逗号分隔</p>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">作者</label>
                            <input type="text" value="John Smith" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">来源</label>
                            <input type="text" value="Unsplash" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>
                    
                    <!-- 权限设置 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 text-white">权限设置</h3>
                        
                        <div class="mb-4">
                            <label class="block text-gray-300 mb-2">可见性</label>
                            <select class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="public" selected>公开</option>
                                <option value="private">私有</option>
                                <option value="password">密码保护</option>
                            </select>
                        </div>
                        
                        <div class="mb-4">
                            <label class="flex items-center">
                                <input type="checkbox" checked class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-900 w-5 h-5">
                                <span class="ml-2 text-gray-300">允许下载</span>
                            </label>
                        </div>
                        
                        <div class="mb-4">
                            <label class="flex items-center">
                                <input type="checkbox" checked class="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-900 w-5 h-5">
                                <span class="ml-2 text-gray-300">允许在搜索中显示</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 危险操作区 -->
                    <div class="bg-gray-800/10 border border-gray-700 rounded-xl p-6">
                        <h3 class="text-lg font-semibold mb-4 text-white text-red-400">危险操作</h3>
                        
                        <button class="flex items-center justify-center bg-red-500/20 hover:bg-red-500/30 text-red-400 w-full py-3 rounded-lg transition-colors">
                            <i class="fas fa-trash-alt mr-2"></i> 删除此媒体
                        </button>
                        <p class="text-gray-400 text-sm mt-2">删除后将无法恢复，引用此媒体的内容将失效</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面底部版权信息 -->
        <footer class="page-footer mt-4 py-6 text-center text-sm text-gray-500">
            © 2025 GACMS 后台管理系统 - 版本 v1.0.0
        </footer>
    </main>

    <script src="./assets/js/gacms-admin.js"></script>
    <script src="./assets/js/sidebar-menu.js"></script>
    <script src="./assets/js/top-navbar.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 复制按钮功能
            const copyButtons = document.querySelectorAll('.copy-btn');
            
            copyButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const inputElement = button.parentElement.querySelector('input');
                    inputElement.select();
                    document.execCommand('copy');
                    
                    // 显示复制成功提示
                    const originalIcon = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-check"></i>';
                    button.classList.add('bg-green-600');
                    
                    setTimeout(() => {
                        button.innerHTML = originalIcon;
                        button.classList.remove('bg-green-600');
                    }, 1500);
                });
            });
            
            // 表单保存功能
            document.getElementById('saveBtn').addEventListener('click', function() {
                alert('媒体信息保存成功！');
                // 在实际场景中，这里会有AJAX请求保存表单数据
            });
        });
    </script>
</body>
</html> 