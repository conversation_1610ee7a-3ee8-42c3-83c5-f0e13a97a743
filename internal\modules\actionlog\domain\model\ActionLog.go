/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-10
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-10
 * @FilePath: internal/modules/actionlog/domain/model/ActionLog.go
 * @Description: Defines the data model for an action log.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package model

import (
	"gorm.io/gorm"
)

// ActionLog records an action performed by an admin user.
type ActionLog struct {
	gorm.Model
	UserID       uint   `gorm:"index"`
	Username     string `gorm:"type:varchar(50)"`
	SiteID       uint   `gorm:"index"`
	IPAddress    string `gorm:"type:varchar(45)"`
	Method       string `gorm:"type:varchar(10)"` // e.g., POST, GET
	Path         string `gorm:"type:varchar(255)"`
	Description  string `gorm:"type:varchar(512)"` // e.g., "Created post 'New Post'"
	Status       int    // HTTP Status Code
	ResponseTime int64  // in milliseconds
} 