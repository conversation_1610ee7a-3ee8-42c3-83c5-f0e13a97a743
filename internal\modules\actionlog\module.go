/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-06-12
 * @FilePath: internal/modules/actionlog/module.go
 * @Description: Defines and provides the actionlog module's components and metadata
 *               for dependency injection and discovery by the core system.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package actionlog

import (
	"gacms/internal/modules/actionlog/application/service"
	"gacms/internal/modules/actionlog/domain/contract"
	"gacms/internal/modules/actionlog/infrastructure/persistence"
	"gacms/internal/modules/actionlog/port/controller"
	"gacms/internal/modules/actionlog/application/observer"

	"gacms/internal/port/http/router"
	pkgContract "gacms/pkg/contract"
	"go.uber.org/fx"
)

const ModuleName = "actionlog"

// Module is the standard fx dependency injection module for the actionlog package.
var Module = fx.Options(
	fx.Provide(
		// Persistence
		fx.Annotate(
			persistence.NewActionLogGormRepository,
			fx.As(new(contract.ActionLogRepository)),
		),

		// Application Services
		service.NewActionLogService,

		// Event Observers (通用观察者，处理自身的通用业务事件)
		observer.NewActionLogObserver,      // 操作日志记录观察者
		observer.NewActionLogQueryObserver, // 操作日志查询观察者
	),

	// Event Observer Registration (声明式配置，fx自动发现)
	fx.Invoke(func(
		manager pkgContract.EventManager,
		logObserver *observer.ActionLogObserver,
		queryObserver *observer.ActionLogQueryObserver,
	) {
		manager.RegisterHandler(logObserver)
		manager.RegisterHandler(queryObserver)
	}),

	// HTTP Controller
	fx.Provide(
		controller.NewActionLogController,
	),

	// Routable Registration
	fx.Provide(
		fx.Annotate(
			func(c *controller.ActionLogController) pkgContract.RoutableRegistration {
				return pkgContract.RoutableRegistration{
					Target:   pkgContract.AdminRoute,
					Routable: c,
				}
			},
			fx.ResultTags(`group:"routables"`),
		),
	),
)
