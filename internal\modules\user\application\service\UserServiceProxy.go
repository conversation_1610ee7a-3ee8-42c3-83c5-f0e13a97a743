/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-13
 * @LastEditors: <PERSON><PERSON>eh
 * @LastEditTime: 2025-06-13
 * @FilePath: internal/modules/user/application/service/UserServiceProxy.go
 * @Description: Implements a proxy for the UserService to enable lazy loading.
 * 
 * © 2025 GACMS. All rights reserved.
 */

package service

import (
	"context"
	"sync"
	"gacms/internal/modules/user/domain/contract"
	"gacms/internal/modules/user/domain/model"
	pkgContract "gacms/pkg/contract"
)

// UserServiceProxy is a proxy that lazy-loads the real UserService.
// It implements the contract.UserService interface.
type UserServiceProxy struct {
	userRepo    contract.UserRepository
	dispatcher  pkgContract.EventManager
	realService contract.UserService
	once        sync.Once
}

// NewUserServiceProxy creates a new proxy for the user service.
// This constructor is cheap to call and is intended to be registered with the DI container.
func NewUserServiceProxy(userRepo contract.UserRepository, dispatcher pkgContract.EventManager) contract.UserService {
	return &UserServiceProxy{
		userRepo:   userRepo,
		dispatcher: dispatcher,
	}
}

// getRealService ensures the real user service is instantiated, but only once.
func (p *UserServiceProxy) getRealService() contract.UserService {
	p.once.Do(func() {
		p.realService = NewUserService(p.userRepo, p.dispatcher)
	})
	return p.realService
}

// RegisterNewMember forwards the call to the real service, initializing it if necessary.
func (p *UserServiceProxy) RegisterNewMember(ctx context.Context, email, password, nickname string) (*model.Member, error) {
	return p.getRealService().RegisterNewMember(ctx, email, password, nickname)
}

// VerifyPassword forwards the call to the real service, initializing it if necessary.
func (p *UserServiceProxy) VerifyPassword(ctx context.Context, userID uint, password string) (bool, error) {
	return p.getRealService().VerifyPassword(ctx, userID, password)
} 