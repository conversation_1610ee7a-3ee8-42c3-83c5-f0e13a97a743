/*
 * @Author: <PERSON><PERSON> <<EMAIL>>
 * @Date: 2025-06-11
 * @LastEditors: C<PERSON> Nieh
 * @LastEditTime: 2025-06-11
 * @FilePath: internal/port/http/response/Response.go
 * @Description: Defines a standardized JSON response structure for the API.
 * 
 * © 2025 GACMS. All rights reserved.
 */
package response

import (
	"net/http"
	"github.com/gin-gonic/gin"
)

// Response defines the standard JSON response format.
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// Success sends a standard success response (200 OK).
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    0,
		Message: "Success",
		Data:    data,
	})
}

// Fail sends a standard error response with a given HTTP status and message.
func Fail(c *gin.Context, httpStatus int, message string) {
	c.JSON(httpStatus, Response{
		Code:    -1, // Generic error code
		Message: message,
		Data:    nil,
	})
}

// FailWithCode sends a standard error response with a custom error code.
func FailWithCode(c *gin.Context, httpStatus int, code int, message string) {
	c.JSON(httpStatus, Response{
		Code:    code,
		Message: message,
		Data:    nil,
	})
} 